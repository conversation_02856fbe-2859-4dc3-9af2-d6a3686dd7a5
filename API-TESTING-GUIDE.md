# 家乡话猜猜猜 - API接口测试指南

**版本**: v1.0  
**更新时间**: 2025-08-02

## 📋 概述

本指南提供了完整的API接口测试方案，包括单元测试、集成测试、性能测试和端到端测试。确保前后端API接口的可靠性和性能。

## 🛠 测试环境设置

### 1. 环境依赖

```bash
# 安装测试依赖
npm install --save-dev jest supertest axios

# 安装API测试工具
npm install --save-dev newman postman-to-k6
```

### 2. 测试配置

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  collectCoverageFrom: [
    'serverless/**/*.js',
    '!serverless/**/*.test.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## 🧪 核心API测试用例

### 1. 认证接口测试

```javascript
// tests/api/auth.test.js
const request = require('supertest');
const app = require('../helpers/test-app');

describe('认证API测试', () => {
  describe('POST /v1/auth/wechat/login', () => {
    test('应该成功处理微信登录', async () => {
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: 'valid_test_code'
        })
        .expect(200);

      expect(response.body).toMatchObject({
        code: 0,
        message: 'success',
        data: {
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
          expiresIn: expect.any(Number),
          tokenType: 'Bearer',
          user: {
            id: expect.any(Number),
            nickname: expect.any(String),
            avatarUrl: expect.any(String),
            openid: expect.any(String)
          },
          isNewUser: expect.any(Boolean)
        }
      });
    });

    test('应该拒绝无效的微信code', async () => {
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: 'invalid_code'
        })
        .expect(400);

      expect(response.body.code).toBe('INVALID_WECHAT_CODE');
    });

    test('应该拒绝缺少code参数的请求', async () => {
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({})
        .expect(400);

      expect(response.body.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /v1/auth/refresh', () => {
    let refreshToken;

    beforeEach(async () => {
      // 先获取有效的refreshToken
      const loginResponse = await request(app)
        .post('/v1/auth/wechat/login')
        .send({ code: 'valid_test_code' });
      
      refreshToken = loginResponse.body.data.refreshToken;
    });

    test('应该成功刷新访问令牌', async () => {
      const response = await request(app)
        .post('/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.data).toMatchObject({
        accessToken: expect.any(String),
        refreshToken: expect.any(String),
        expiresIn: expect.any(Number),
        tokenType: 'Bearer'
      });
    });

    test('应该拒绝无效的刷新令牌', async () => {
      const response = await request(app)
        .post('/v1/auth/refresh')
        .send({ refreshToken: 'invalid_token' })
        .expect(400);

      expect(response.body.code).toBe('INVALID_REFRESH_TOKEN');
    });
  });

  describe('GET /v1/auth/me', () => {
    let accessToken;

    beforeEach(async () => {
      const loginResponse = await request(app)
        .post('/v1/auth/wechat/login')
        .send({ code: 'valid_test_code' });
      
      accessToken = loginResponse.body.data.accessToken;
    });

    test('应该返回当前用户信息', async () => {
      const response = await request(app)
        .get('/v1/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.data.user).toMatchObject({
        id: expect.any(Number),
        nickname: expect.any(String),
        avatarUrl: expect.any(String),
        openid: expect.any(String)
      });
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/v1/auth/me')
        .expect(401);

      expect(response.body.code).toBe('INVALID_TOKEN');
    });
  });
});
```

### 2. 游戏接口测试

```javascript
// tests/api/game.test.js
const request = require('supertest');
const app = require('../helpers/test-app');
const { createTestUser, getAuthHeaders } = require('../helpers/auth-helper');

describe('游戏API测试', () => {
  let authHeaders;
  let testUser;

  beforeEach(async () => {
    testUser = await createTestUser();
    authHeaders = await getAuthHeaders(testUser);
  });

  describe('GET /v1/questions', () => {
    test('应该返回指定数量的题目', async () => {
      const response = await request(app)
        .get('/v1/questions?count=5&difficulty=1&random=true')
        .expect(200);

      expect(response.body.data).toMatchObject({
        questions: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(Number),
            category: expect.any(String),
            region: expect.any(String),
            questionText: expect.any(String),
            audioUrl: expect.any(String),
            difficultyLevel: expect.any(Number),
            answerOptions: expect.any(Array)
          })
        ]),
        total: expect.any(Number),
        params: {
          count: 5,
          difficulty: 1,
          random: true
        }
      });

      expect(response.body.data.questions.length).toBeLessThanOrEqual(5);
    });

    test('应该支持按类别筛选题目', async () => {
      const response = await request(app)
        .get('/v1/questions?category=cantonese&count=3')
        .expect(200);

      response.body.data.questions.forEach(question => {
        expect(question.category).toBe('cantonese');
      });
    });

    test('应该验证参数范围', async () => {
      const response = await request(app)
        .get('/v1/questions?count=100') // 超出限制
        .expect(200);

      // 应该被限制为最大值50
      expect(response.body.data.questions.length).toBeLessThanOrEqual(50);
    });
  });

  describe('POST /v1/game-sessions', () => {
    test('应该成功创建游戏会话', async () => {
      const response = await request(app)
        .post('/v1/game-sessions')
        .set(authHeaders)
        .send({
          category: 'cantonese',
          difficulty: 1,
          questionCount: 10,
          gameMode: 'standard'
        })
        .expect(200);

      expect(response.body.data).toMatchObject({
        gameSession: {
          sessionId: expect.any(String),
          userId: testUser.id,
          category: 'cantonese',
          difficulty: 1,
          questionCount: 10,
          gameMode: 'standard',
          status: 1,
          startTime: expect.any(String)
        },
        firstQuestion: expect.objectContaining({
          id: expect.any(Number),
          questionText: expect.any(String),
          audioUrl: expect.any(String)
        })
      });
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .post('/v1/game-sessions')
        .send({
          category: 'cantonese',
          difficulty: 1,
          questionCount: 10
        })
        .expect(401);

      expect(response.body.code).toBe('INVALID_TOKEN');
    });

    test('应该验证请求参数', async () => {
      const response = await request(app)
        .post('/v1/game-sessions')
        .set(authHeaders)
        .send({
          difficulty: 6, // 超出范围
          questionCount: 100 // 超出范围
        })
        .expect(400);

      expect(response.body.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /v1/game-sessions/{sessionId}/submit', () => {
    let gameSession;

    beforeEach(async () => {
      const response = await request(app)
        .post('/v1/game-sessions')
        .set(authHeaders)
        .send({
          category: 'cantonese',
          difficulty: 1,
          questionCount: 5,
          gameMode: 'standard'
        });
      
      gameSession = response.body.data.gameSession;
    });

    test('应该成功提交答案', async () => {
      const response = await request(app)
        .post(`/v1/game-sessions/${gameSession.sessionId}/submit`)
        .set(authHeaders)
        .send({
          questionId: 1,
          userAnswer: '选项A',
          answerTime: 5000,
          hintUsed: false
        })
        .expect(200);

      expect(response.body.data).toMatchObject({
        result: {
          isCorrect: expect.any(Boolean),
          correctAnswer: expect.any(String),
          explanation: expect.any(String),
          scoreEarned: expect.any(Number),
          streakCount: expect.any(Number)
        },
        gameSession: expect.objectContaining({
          sessionId: gameSession.sessionId,
          totalScore: expect.any(Number)
        }),
        isGameCompleted: expect.any(Boolean)
      });
    });

    test('应该拒绝无效的会话ID', async () => {
      const response = await request(app)
        .post('/v1/game-sessions/invalid_session/submit')
        .set(authHeaders)
        .send({
          questionId: 1,
          userAnswer: '选项A',
          answerTime: 5000
        })
        .expect(404);

      expect(response.body.code).toBe('SESSION_NOT_FOUND');
    });
  });
});
```

### 3. 围观功能测试

```javascript
// tests/api/spectator.test.js
const request = require('supertest');
const app = require('../helpers/test-app');
const { createTestUser, getAuthHeaders } = require('../helpers/auth-helper');

describe('围观功能API测试', () => {
  let authHeaders;
  let testUser;
  let gameSession;

  beforeEach(async () => {
    testUser = await createTestUser();
    authHeaders = await getAuthHeaders(testUser);
    
    // 创建游戏会话用于围观
    const gameResponse = await request(app)
      .post('/v1/game-sessions')
      .set(authHeaders)
      .send({
        category: 'cantonese',
        difficulty: 1,
        questionCount: 5
      });
    
    gameSession = gameResponse.body.data.gameSession;
  });

  describe('POST /v1/spectator/rooms', () => {
    test('应该成功创建围观房间', async () => {
      const response = await request(app)
        .post('/v1/spectator/rooms')
        .set(authHeaders)
        .send({
          gameSessionId: gameSession.sessionId,
          settings: {
            maxSpectators: 100,
            allowComments: true
          }
        })
        .expect(201);

      expect(response.body.data).toMatchObject({
        id: expect.any(String),
        gameSessionId: gameSession.sessionId,
        creatorId: testUser.id,
        status: 'active',
        settings: expect.any(Object)
      });
    });

    test('应该拒绝无效的游戏会话ID', async () => {
      const response = await request(app)
        .post('/v1/spectator/rooms')
        .set(authHeaders)
        .send({
          gameSessionId: 'invalid_session_id'
        })
        .expect(400);

      expect(response.body.error).toContain('Game session');
    });
  });

  describe('GET /v1/spectator/rooms', () => {
    let spectatorRoom;

    beforeEach(async () => {
      const response = await request(app)
        .post('/v1/spectator/rooms')
        .set(authHeaders)
        .send({
          gameSessionId: gameSession.sessionId
        });
      
      spectatorRoom = response.body.data;
    });

    test('应该返回围观房间列表', async () => {
      const response = await request(app)
        .get('/v1/spectator/rooms?page=1&limit=10&status=active')
        .expect(200);

      expect(response.body.data).toMatchObject({
        rooms: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            gameSessionId: expect.any(String),
            status: 'active',
            spectatorCount: expect.any(Number)
          })
        ]),
        pagination: {
          page: 1,
          limit: 10,
          total: expect.any(Number),
          totalPages: expect.any(Number)
        }
      });
    });

    test('应该支持按状态筛选', async () => {
      const response = await request(app)
        .get('/v1/spectator/rooms?status=active')
        .expect(200);

      response.body.data.rooms.forEach(room => {
        expect(room.status).toBe('active');
      });
    });
  });

  describe('POST /v1/spectator/rooms/{roomId}/join', () => {
    let spectatorRoom;
    let spectatorUser;
    let spectatorAuthHeaders;

    beforeEach(async () => {
      // 创建围观房间
      const roomResponse = await request(app)
        .post('/v1/spectator/rooms')
        .set(authHeaders)
        .send({
          gameSessionId: gameSession.sessionId
        });
      
      spectatorRoom = roomResponse.body.data;

      // 创建围观者用户
      spectatorUser = await createTestUser();
      spectatorAuthHeaders = await getAuthHeaders(spectatorUser);
    });

    test('应该成功加入围观房间', async () => {
      const response = await request(app)
        .post(`/v1/spectator/rooms/${spectatorRoom.id}/join`)
        .set(spectatorAuthHeaders)
        .expect(200);

      expect(response.body.data).toMatchObject({
        roomId: spectatorRoom.id,
        userId: spectatorUser.id,
        spectatorCount: expect.any(Number)
      });
    });

    test('应该拒绝重复加入', async () => {
      // 第一次加入
      await request(app)
        .post(`/v1/spectator/rooms/${spectatorRoom.id}/join`)
        .set(spectatorAuthHeaders)
        .expect(200);

      // 第二次加入应该失败
      const response = await request(app)
        .post(`/v1/spectator/rooms/${spectatorRoom.id}/join`)
        .set(spectatorAuthHeaders)
        .expect(409);

      expect(response.body.error).toContain('already joined');
    });
  });
});
```

## 🚀 性能测试

### 1. API负载测试

```javascript
// tests/performance/load.test.js
const axios = require('axios');

describe('API性能测试', () => {
  const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
  let authToken;

  beforeAll(async () => {
    // 获取测试用的auth token
    const loginResponse = await axios.post(`${BASE_URL}/v1/auth/wechat/login`, {
      code: 'valid_test_code'
    });
    authToken = loginResponse.data.data.accessToken;
  });

  test('GET /v1/questions 响应时间应该 < 200ms', async () => {
    const startTime = Date.now();
    
    const response = await axios.get(`${BASE_URL}/v1/questions?count=10`, {
      timeout: 5000
    });
    
    const duration = Date.now() - startTime;
    
    expect(response.status).toBe(200);
    expect(duration).toBeLessThan(200);
    console.log(`题目获取耗时: ${duration}ms`);
  });

  test('POST /v1/game-sessions 并发创建测试', async () => {
    const concurrency = 10;
    const promises = [];

    for (let i = 0; i < concurrency; i++) {
      const promise = axios.post(`${BASE_URL}/v1/game-sessions`, {
        category: 'cantonese',
        difficulty: 1,
        questionCount: 5
      }, {
        headers: { Authorization: `Bearer ${authToken}` },
        timeout: 5000
      });
      promises.push(promise);
    }

    const startTime = Date.now();
    const results = await Promise.all(promises);
    const duration = Date.now() - startTime;

    results.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data.data.gameSession).toBeDefined();
    });

    const avgDuration = duration / concurrency;
    expect(avgDuration).toBeLessThan(500);
    console.log(`并发游戏会话创建平均耗时: ${avgDuration}ms`);
  });

  test('API吞吐量测试', async () => {
    const requestCount = 100;
    const requests = [];

    for (let i = 0; i < requestCount; i++) {
      requests.push(
        axios.get(`${BASE_URL}/v1/questions?count=5&random=true`, {
          timeout: 10000
        })
      );
    }

    const startTime = Date.now();
    const results = await Promise.allSettled(requests);
    const duration = Date.now() - startTime;

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const throughput = (successful / duration) * 1000; // requests per second

    expect(successful).toBeGreaterThan(requestCount * 0.95); // 95%成功率
    expect(throughput).toBeGreaterThan(10); // 至少10 RPS

    console.log(`吞吐量测试结果:`);
    console.log(`- 总请求数: ${requestCount}`);
    console.log(`- 成功请求数: ${successful}`);
    console.log(`- 总耗时: ${duration}ms`);
    console.log(`- 吞吐量: ${throughput.toFixed(2)} RPS`);
  });
});
```

### 2. 内存和资源使用监控

```javascript
// tests/performance/memory.test.js
const pidusage = require('pidusage');

describe('资源使用监控', () => {
  test('API服务器内存使用应该稳定', async () => {
    const pid = process.pid;
    const measurements = [];

    // 进行10次API调用，监控内存使用
    for (let i = 0; i < 10; i++) {
      await axios.get(`${BASE_URL}/v1/questions?count=10`);
      
      const stats = await pidusage(pid);
      measurements.push({
        memory: stats.memory,
        cpu: stats.cpu,
        timestamp: Date.now()
      });

      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 检查内存增长趋势
    const memoryValues = measurements.map(m => m.memory);
    const memoryGrowth = memoryValues[memoryValues.length - 1] - memoryValues[0];
    const memoryGrowthRate = memoryGrowth / memoryValues[0];

    expect(memoryGrowthRate).toBeLessThan(0.1); // 内存增长不超过10%

    console.log('内存使用统计:');
    console.log(`- 初始内存: ${(memoryValues[0] / 1024 / 1024).toFixed(2)} MB`);
    console.log(`- 最终内存: ${(memoryValues[memoryValues.length - 1] / 1024 / 1024).toFixed(2)} MB`);
    console.log(`- 内存增长率: ${(memoryGrowthRate * 100).toFixed(2)}%`);
  });
});
```

## 🔄 端到端测试

### 1. 完整游戏流程测试

```javascript
// tests/e2e/game-flow.test.js
const axios = require('axios');

describe('端到端游戏流程测试', () => {
  const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
  let authToken;
  let gameSession;

  test('完整游戏流程', async () => {
    console.log('🎮 开始端到端游戏流程测试');

    // 步骤1: 用户登录
    console.log('📱 步骤1: 用户登录');
    const loginResponse = await axios.post(`${BASE_URL}/v1/auth/wechat/login`, {
      code: 'valid_test_code'
    });

    expect(loginResponse.status).toBe(200);
    authToken = loginResponse.data.data.accessToken;
    const user = loginResponse.data.data.user;
    console.log(`✅ 登录成功: ${user.nickname}`);

    // 步骤2: 获取题目列表
    console.log('📚 步骤2: 获取题目列表');
    const questionsResponse = await axios.get(`${BASE_URL}/v1/questions?count=5&difficulty=1`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    expect(questionsResponse.status).toBe(200);
    const questions = questionsResponse.data.data.questions;
    expect(questions.length).toBeGreaterThan(0);
    console.log(`✅ 获取到 ${questions.length} 道题目`);

    // 步骤3: 创建游戏会话
    console.log('🎲 步骤3: 创建游戏会话');
    const sessionResponse = await axios.post(`${BASE_URL}/v1/game-sessions`, {
      category: 'cantonese',
      difficulty: 1,
      questionCount: 5,
      gameMode: 'standard'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    expect(sessionResponse.status).toBe(200);
    gameSession = sessionResponse.data.data.gameSession;
    console.log(`✅ 游戏会话创建成功: ${gameSession.sessionId}`);

    // 步骤4: 模拟答题过程
    console.log('✏️ 步骤4: 开始答题');
    let totalScore = 0;
    let correctAnswers = 0;

    for (let i = 0; i < 5; i++) {
      const question = questions[i];
      const isCorrect = Math.random() > 0.3; // 70%正确率
      const userAnswer = isCorrect ? question.answerOptions[0] : question.answerOptions[1];
      const answerTime = Math.floor(Math.random() * 10000) + 2000; // 2-12秒

      const submitResponse = await axios.post(
        `${BASE_URL}/v1/game-sessions/${gameSession.sessionId}/submit`,
        {
          questionId: question.id,
          userAnswer: userAnswer,
          answerTime: answerTime,
          hintUsed: false
        },
        {
          headers: { Authorization: `Bearer ${authToken}` }
        }
      );

      expect(submitResponse.status).toBe(200);
      const result = submitResponse.data.data.result;
      
      if (result.isCorrect) {
        correctAnswers++;
        totalScore += result.scoreEarned;
      }

      console.log(`  第${i + 1}题: ${result.isCorrect ? '正确' : '错误'} (得分: ${result.scoreEarned})`);
    }

    // 步骤5: 获取游戏结果
    console.log('📊 步骤5: 获取游戏结果');
    const resultsResponse = await axios.get(
      `${BASE_URL}/v1/game-sessions/${gameSession.sessionId}/results`,
      {
        headers: { Authorization: `Bearer ${authToken}` }
      }
    );

    expect(resultsResponse.status).toBe(200);
    const gameResults = resultsResponse.data.data;
    
    console.log(`✅ 游戏完成!`);
    console.log(`   总分: ${gameResults.gameSession.totalScore}`);
    console.log(`   正确题数: ${correctAnswers}/5`);
    console.log(`   正确率: ${(correctAnswers / 5 * 100).toFixed(1)}%`);

    // 步骤6: 检查排行榜
    console.log('🏆 步骤6: 检查排行榜');
    const leaderboardResponse = await axios.get(
      `${BASE_URL}/v1/leaderboard/global?type=total_score&page=1&size=10`
    );

    expect(leaderboardResponse.status).toBe(200);
    const leaderboard = leaderboardResponse.data.data.leaderboard;
    console.log(`✅ 排行榜获取成功，共 ${leaderboard.length} 条记录`);

    console.log('🎉 端到端测试完成!');
  }, 30000); // 30秒超时
});
```

## 📊 测试报告生成

### 1. 测试结果收集器

```javascript
// tests/helpers/test-reporter.js
class TestReporter {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      },
      categories: {},
      performance: {},
      errors: []
    };
  }

  recordTest(category, testName, status, duration, error = null) {
    this.results.summary.total++;
    this.results.summary[status]++;

    if (!this.results.categories[category]) {
      this.results.categories[category] = {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        tests: []
      };
    }

    const categoryResult = this.results.categories[category];
    categoryResult.total++;
    categoryResult[status]++;
    categoryResult.tests.push({
      name: testName,
      status,
      duration,
      error
    });

    if (error) {
      this.results.errors.push({
        category,
        test: testName,
        error: error.message,
        stack: error.stack
      });
    }
  }

  recordPerformance(endpoint, metrics) {
    this.results.performance[endpoint] = {
      ...metrics,
      timestamp: new Date().toISOString()
    };
  }

  generateReport() {
    const successRate = (this.results.summary.passed / this.results.summary.total) * 100;
    
    const report = {
      ...this.results,
      summary: {
        ...this.results.summary,
        successRate: successRate.toFixed(2) + '%'
      }
    };

    return report;
  }

  saveReport(filename = 'test-report.json') {
    const fs = require('fs');
    const report = this.generateReport();
    
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`📊 测试报告已保存至: ${filename}`);
    
    return report;
  }

  printSummary() {
    const report = this.generateReport();
    
    console.log('\n📊 测试结果汇总');
    console.log('='.repeat(50));
    console.log(`总测试数: ${report.summary.total}`);
    console.log(`通过: ${report.summary.passed} ✅`);
    console.log(`失败: ${report.summary.failed} ❌`);
    console.log(`跳过: ${report.summary.skipped} ⏭️`);
    console.log(`成功率: ${report.summary.successRate}`);

    if (report.errors.length > 0) {
      console.log('\n❌ 失败详情:');
      report.errors.forEach((error, index) => {
        console.log(`${index + 1}. [${error.category}] ${error.test}: ${error.error}`);
      });
    }

    console.log('\n📈 性能指标:');
    Object.entries(report.performance).forEach(([endpoint, metrics]) => {
      console.log(`${endpoint}:`);
      console.log(`  平均响应时间: ${metrics.avgDuration}ms`);
      console.log(`  最大响应时间: ${metrics.maxDuration}ms`);
      console.log(`  成功率: ${metrics.successRate}%`);
    });
  }
}

module.exports = TestReporter;
```

### 2. 自动化测试运行脚本

```javascript
// scripts/run-api-tests.js
#!/usr/bin/env node

const { spawn } = require('child_process');
const TestReporter = require('../tests/helpers/test-reporter');

async function runTests() {
  const reporter = new TestReporter();
  
  console.log('🚀 开始API测试套件');
  console.log('='.repeat(50));

  try {
    // 运行不同类型的测试
    const testSuites = [
      { name: '单元测试', command: 'npm', args: ['run', 'test:unit'] },
      { name: '集成测试', command: 'npm', args: ['run', 'test:integration'] },
      { name: '性能测试', command: 'npm', args: ['run', 'test:performance'] },
      { name: '端到端测试', command: 'npm', args: ['run', 'test:e2e'] }
    ];

    for (const suite of testSuites) {
      console.log(`\n🧪 运行${suite.name}...`);
      
      const startTime = Date.now();
      try {
        await runCommand(suite.command, suite.args);
        const duration = Date.now() - startTime;
        
        reporter.recordTest(suite.name, 'suite', 'passed', duration);
        console.log(`✅ ${suite.name}完成 (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - startTime;
        
        reporter.recordTest(suite.name, 'suite', 'failed', duration, error);
        console.log(`❌ ${suite.name}失败: ${error.message}`);
      }
    }

    // 生成报告
    reporter.printSummary();
    reporter.saveReport(`test-reports/api-test-report-${Date.now()}.json`);

  } catch (error) {
    console.error('测试运行失败:', error);
    process.exit(1);
  }
}

function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { stdio: 'inherit' });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', reject);
  });
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
```

## 🎯 持续集成配置

### 1. GitHub Actions配置

```yaml
# .github/workflows/api-tests.yml
name: API Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  api-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: dialect_game_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Setup test environment
      run: |
        cp .env.example .env.test
        npm run db:migrate:test
        npm run db:seed:test

    - name: Run API tests
      run: |
        npm run test:unit
        npm run test:integration
        npm run test:performance
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 3306
        DB_USER: root
        DB_PASSWORD: testpassword
        DB_NAME: dialect_game_test
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: test-reports/

    - name: Update test coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

## 📝 总结

这份API测试指南提供了完整的测试策略，包括：

### 测试覆盖范围
- ✅ **单元测试**: 验证单个API接口功能
- ✅ **集成测试**: 验证接口间的协作
- ✅ **性能测试**: 验证响应时间和吞吐量
- ✅ **端到端测试**: 验证完整业务流程
- ✅ **错误处理测试**: 验证各种异常情况

### 自动化程度
- 🤖 **自动化测试套件**: 完整的自动化测试流程
- 📊 **测试报告生成**: 详细的测试结果报告
- 🔄 **持续集成**: CI/CD流水线集成
- 📈 **性能监控**: 实时性能指标收集

### 质量保证
- 🛡️ **高覆盖率**: 目标80%以上代码覆盖率
- ⚡ **性能基准**: 响应时间<200ms，吞吐量>10RPS
- 🔒 **安全测试**: 认证、授权、输入验证
- 💪 **稳定性**: 并发测试和压力测试

通过这套完整的测试体系，可以确保API接口的可靠性、性能和安全性，为前端开发团队提供稳定的服务支撑。