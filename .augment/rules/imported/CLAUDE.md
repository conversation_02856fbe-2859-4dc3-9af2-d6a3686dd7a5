---
type: "always_apply"
---

# CLAUDE.md

# 我们用中文交流

## 本项目的设计方案是 oh-my-game.md

## 所有 agent 的工作文件按照 structure.md 中的目录结构组织

## 每天任务完成后更新 daily-report.md daily-issue.md daily-plan.md 文件

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## 🎯 Project Overview

**家乡话猜猜猜** - A viral WeChat mini-game where users guess dialect regions from audio clips. 
- **Frontend**: Cocos Creator 3.8.x TypeScript
- **Backend**: Node.js Serverless on Tencent Cloud
- **Architecture**: Cost-optimized ($300/month) for 10K DAU
- **Core**: Audio-driven viral social gaming

## 🏗️ Architecture & Tech Stack

### Frontend - WeChat Mini-Game
```
frontend/
├── cocos-project/          # Cocos Creator 3.8.x
│   ├── assets/scripts/     # TypeScript MVC architecture
│   │   ├── managers/       # GameManager, AudioManager, UIManager
│   │   ├── components/     # Interactive UI components
│   │   └── utils/          # WeChatAPI integration
│   └── wechat-config/      # Mini-game specific configs
```

### Backend - Serverless Lambda
```
backend/
├── serverless.yml          # Tencent Cloud SCF configuration
├── serverless/             # Lambda functions for API services
│   ├── auth/              # WeChat OAuth login
│   ├── game/              # Question management + scoring
│   ├── content/           # Audio file serving (COS CDN)
│   └── analytics/         # User behavior tracking
├── database/              # MySQL schemas & migrations
```

### Content Pipeline
```
content/
├── audio/                 # Optimized audio assets organized by dialect
├── scripts/               # Recording & quality guidelines
├── database/              # JSON question data + cultural context
└── production/            # Batch processing workflows
```

## 🚀 Development Commands

### Frontend (Cocos Creator)
```bash
# Build for WeChat
open cocos-project/ in Cocos Creator → Build → WeChat mini-game

# Local development
cd frontend && npm run dev         # Start dev server
npm run build-wechat              # Build for WeChat platform
```

### Backend Development
```bash
cd backend
npm install                       # Install dependencies
npm run dev                       # Local serverless development (port 3001)
npm run test                      # Jest test suite
npm run deploy:dev               # Deploy to dev environment
npm run lint                      # ESLint code quality
npm run migrate                   # Database migrations
```

### Audio Content Processing
```bash
cd tools/audio-processing/
python batch-converter.py         # Convert audio formats
python quality-checker.py         # Validate audio quality
```

## 🧪 Testing Strategy

### Frontend Testing
```bash
npm run test:unit                 # Component tests
npm run test:e2e                  # Cocos Creator gameplay tests
npm run test:performance          # FPS & memory monitoring
```

### Backend Testing
```bash
npm run test                      # Unit tests for all Lambda functions
npm run test:integration          # API integration tests
npm run test:load                 # 10K DAU load testing
```

## 📊 Smart Audio Strategy

**Three-tier optimization**:
1. **Local Cache**: 3-50MB per user, Web Audio API
2. **COS CDN**: Global distribution, 70% compression
3. **Predictive Load**: AI-driven preloading based on user patterns

## 🔧 Development Workflow

### Git Branches
- `main` - Production
- `develop` - Integration
- `feature/` - Individual features
- `hotfix/` - Critical fixes

### Quality Gates
- All tests must pass → `npm run test`
- Lint clean → `npm run lint`
- Performance benchmarks → `npm run perf`
- Security scan → `npm run security`

## 📁 Agent Workspaces

Specialized directories for agent task delegation:
- `docs/product/` - Product manager specs
- `docs/design/` - UI/UX designs and assets  
- `docs/architecture/` - System design documents
- `content/` - Audio content creation pipeline
- `docs/testing/` - QA test plans and reports

## 🎯 Performance Targets

**Operational constraints**:
- 99.9% uptime (8.7h downtime/year)
- <200ms API response
- <500KB initial bundle
- Cost: <$300/month at 10K DAU

## 🔄 Deployment

```bash
npm run deploy:prod              # Production deployment
npm run deploy:staging           # Staging deployment  
npm run rollback:v1.2.3          # Rapid rollback capability
```