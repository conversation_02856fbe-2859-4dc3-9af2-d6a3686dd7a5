# 音频制作工具链和质量检测流程

## 🛠️ 制作工具链

### 主要工具
1. **录制工具**: Audacity（免费开源）
2. **格式转换**: FFmpeg（命令行工具）
3. **质量检测**: SoX（音频分析）
4. **批量处理**: 自定义Python脚本

### 工具安装指南

#### macOS安装
```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装必要工具
brew install audacity
brew install ffmpeg
brew install sox
brew install python3
```

#### Windows安装
```bash
# 下载安装包
- Audacity: https://www.audacityteam.org/download/
- FFmpeg: https://ffmpeg.org/download.html
- SoX: http://sox.sourceforge.net/
```

## 🎙️ 录制标准流程

### 步骤1: 环境准备
```bash
# 检查录制环境噪音水平
sox -d -n trim 0 10 stat
# 目标: RMS amplitude < 0.01 (相当于 -40dB)
```

### 步骤2: 录制设置
**Audacity设置**:
- 采样率: 44100 Hz
- 质量: 32-bit float
- 声道: 立体声（录制时），单声道（导出时）
- 音量: 保持在-12dB到-6dB之间

### 步骤3: 录制技巧
1. **距离控制**: 距离麦克风15-20cm
2. **发音标准**: 清晰、自然、地道
3. **多次录制**: 每个词汇录制3-5遍
4. **即时检查**: 录制后立即回放检查

## 🔧 后期处理流程

### 自动化处理脚本
创建批量处理脚本 `process_audio.py`:

```python
#!/usr/bin/env python3
import os
import subprocess
import json
from pathlib import Path

def process_audio_file(input_file, output_file):
    """
    处理单个音频文件
    """
    # 1. 降噪处理
    subprocess.run([
        'sox', input_file, 'temp_denoised.wav',
        'noisered', 'noise_profile.prof', '0.21'
    ])
    
    # 2. 音量标准化
    subprocess.run([
        'sox', 'temp_denoised.wav', 'temp_normalized.wav',
        'loudness', '-16'
    ])
    
    # 3. 转换为MP3格式
    subprocess.run([
        'ffmpeg', '-i', 'temp_normalized.wav',
        '-codec:a', 'libmp3lame',
        '-b:a', '128k',
        '-ac', '1',  # 单声道
        '-ar', '44100',
        output_file
    ])
    
    # 清理临时文件
    os.remove('temp_denoised.wav')
    os.remove('temp_normalized.wav')

def validate_audio_specs(file_path):
    """
    验证音频技术规格
    """
    result = subprocess.run([
        'ffprobe', '-v', 'quiet', '-print_format', 'json',
        '-show_format', '-show_streams', file_path
    ], capture_output=True, text=True)
    
    data = json.loads(result.stdout)
    audio_stream = data['streams'][0]
    
    # 检查规格
    specs = {
        'format': data['format']['format_name'],
        'codec': audio_stream['codec_name'],
        'sample_rate': int(audio_stream['sample_rate']),
        'bit_rate': int(audio_stream.get('bit_rate', 0)),
        'channels': int(audio_stream['channels']),
        'duration': float(audio_stream['duration']),
        'file_size': int(data['format']['size'])
    }
    
    return specs

# 使用示例
if __name__ == "__main__":
    input_dir = "raw_recordings/"
    output_dir = "processed_audio/"
    
    for audio_file in Path(input_dir).glob("*.wav"):
        output_file = Path(output_dir) / f"{audio_file.stem}.mp3"
        process_audio_file(str(audio_file), str(output_file))
        
        # 验证输出规格
        specs = validate_audio_specs(str(output_file))
        print(f"处理完成: {output_file.name}")
        print(f"规格: {specs}")
```

## 📊 质量检测流程

### 技术规格检测

#### 1. 基础规格验证
```bash
# 检查音频基本信息
ffprobe -v quiet -print_format json -show_format -show_streams audio.mp3

# 预期输出规格:
# - format: mp3
# - sample_rate: 44100
# - bit_rate: 128000
# - channels: 1
# - duration: 2-6秒
# - file_size: <80KB
```

#### 2. 音频质量分析
```bash
# 分析音频质量
sox audio.mp3 -n stat

# 检查项目:
# RMS amplitude: 应该在 0.1-0.3 范围内
# Maximum amplitude: 应该 < 1.0 (防止削波)
# Length: 2-6秒范围内
```

#### 3. 噪音水平检测
```bash
# 检测背景噪音
sox audio.mp3 -n trim 0 0.5 stat 2>&1 | grep "RMS amplitude"

# 目标: RMS amplitude < 0.01 (-40dB)
```

### 内容质量检测

#### 1. 发音准确性检查
- **专家审核**: 每个方言区安排本地专家
- **一致性检查**: 同一录音者完成同方言内容
- **清晰度评估**: 语音清晰度≥90%

#### 2. 文化适宜性审核
- **积极正面**: 避免消极或争议内容
- **文化准确**: 确保文化背景正确
- **时代性**: 使用现代常用表达

## 🎯 质量标准

### 必须达标项（Pass/Fail）
- [x] **音频格式**: MP3格式
- [x] **采样率**: 44.1kHz
- [x] **比特率**: 128kbps
- [x] **声道**: 单声道
- [x] **文件大小**: <80KB
- [x] **时长**: 2-6秒
- [x] **命名规范**: 符合标准

### 质量评分项（1-5分）
- **发音准确性**: ≥4.5分（90%准确率）
- **音频清晰度**: ≥4.0分（80%清晰度）
- **地道程度**: ≥4.0分（本地化程度）
- **自然度**: ≥4.0分（不做作、自然）

## 🔄 工作流程图

```
原始录音 → 环境噪音检测 → 多次录制 → 选择最佳版本
    ↓
降噪处理 → 音量标准化 → 格式转换 → 文件重命名
    ↓
技术规格验证 → 内容质量审核 → 专家评估 → 最终验收
    ↓
集成测试 → 用户体验测试 → 发布准备 → 正式部署
```

## 📁 文件组织结构

```
content/audio/
├── raw_recordings/          # 原始录音文件
├── processed/               # 处理后的音频文件
├── quality_reports/         # 质量检测报告
├── tools/                   # 处理工具和脚本
│   ├── process_audio.py     # 批量处理脚本
│   ├── validate_specs.py    # 规格验证脚本
│   └── noise_profile.prof   # 降噪配置文件
└── final/                   # 最终音频文件
    ├── cantonese/
    ├── sichuan/
    ├── shanghai/
    ├── northeast/
    └── minnan/
```

## ⚡ 效率优化

### 批量处理命令
```bash
# 批量转换格式
for file in raw_recordings/*.wav; do
    ffmpeg -i "$file" -codec:a libmp3lame -b:a 128k -ac 1 -ar 44100 "processed/${file%.wav}.mp3"
done

# 批量验证规格
for file in processed/*.mp3; do
    echo "检查文件: $file"
    ffprobe -v quiet -print_format json -show_format "$file" | jq '.format.size, .format.duration'
done
```

### 质量检测自动化
```bash
# 创建质量检测脚本
#!/bin/bash
AUDIO_DIR="processed"
REPORT_FILE="quality_report_$(date +%Y%m%d).txt"

echo "音频质量检测报告 - $(date)" > $REPORT_FILE
echo "================================" >> $REPORT_FILE

for file in $AUDIO_DIR/*.mp3; do
    echo "文件: $(basename $file)" >> $REPORT_FILE
    
    # 检查文件大小
    size=$(stat -f%z "$file")
    echo "大小: ${size} bytes" >> $REPORT_FILE
    
    # 检查时长
    duration=$(ffprobe -v quiet -show_entries format=duration -of csv=p=0 "$file")
    echo "时长: ${duration} 秒" >> $REPORT_FILE
    
    # 检查规格
    ffprobe -v quiet -print_format json -show_streams "$file" | jq '.streams[0] | {codec_name, sample_rate, bit_rate, channels}' >> $REPORT_FILE
    
    echo "---" >> $REPORT_FILE
done
```

这个完整的制作工具链确保了音频内容的高质量和标准化制作流程。