# 粤语第一阶段录制计划

**录制日期**: 2025-07-31  
**负责人**: Audio-Content-Agent  
**录制目标**: 第一批5个粤语词汇

## 📝 录制清单

### 第一批词汇 (日常用语 - 简单级别)

| 序号 | 中文 | 粤语拼音 | 录制要点 | 文件名 |
|------|------|----------|----------|---------|
| 1 | 你好 | nei5 hou2 | 标准问候语，语调自然友好 | cantonese_daily_easy_001.mp3 |
| 2 | 谢谢 | do1 ze6 | 感谢用语，语速适中 | cantonese_daily_easy_002.mp3 |
| 3 | 不客气 | m4 haak3 hei3 | 回应用语，语调谦逊 | cantonese_daily_easy_003.mp3 |
| 4 | 再见 | zoi3 gin3 | 告别用语，语调平和 | cantonese_daily_easy_004.mp3 |
| 5 | 早晨 | zou2 san4 | 早上问候，语调轻松 | cantonese_daily_easy_005.mp3 |

## 🎤 录制标准

### 技术规格
- **格式**: 录制WAV，转换MP3
- **时长**: 2-4秒
- **采样**: 44.1kHz/16bit
- **环境**: 安静室内，背景噪音<-40dB

### 发音要求
- **准确性**: 标准广州粤语发音
- **清晰度**: 每个音节清楚
- **自然性**: 日常使用的语调
- **一致性**: 同一录制者风格统一

## 🔧 工具使用流程

### 1. 录制阶段
```bash
# 使用录音设备录制WAV文件
# 保存到: /content/audio/raw/cantonese/
```

### 2. 转换阶段
```bash
cd tools/audio-processing/
python3 batch-converter.py
```

### 3. 质检阶段
```bash
python3 quality-checker.py
```

## 📊 质量标准

### 必达指标
- ✅ 技术规格100%符合标准
- ✅ 发音准确性≥95%
- ✅ 音频清晰度≥90%
- ✅ 文件命名规范100%正确

### 验收流程
1. 自动化技术检测
2. 发音准确性审核
3. 文化适宜性确认
4. 最终质量评估

## 🎯 成功标准

**录制完成标准**:
- 5个音频文件全部录制完成
- 技术规格100%达标
- 发音质量通过专家审核
- 文件正确命名和分类

**预期时间**: 2小时录制 + 1小时后期处理

---

**项目路径**: `/Users/<USER>/Public/GitHub/hometown-dialect-game/`  
**工具目录**: `/tools/audio-processing/`  
**输出目录**: `/content/audio/cantonese/daily/easy/`