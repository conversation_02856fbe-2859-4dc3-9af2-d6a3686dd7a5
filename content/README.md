# 音频内容管理中心

## 目录说明

本目录包含"家乡话猜猜猜"项目的所有音频内容相关文件，统一管理音频制作的各个环节。

### 目录结构

```
audio-content/
├── README.md                    # 本说明文件
├── databases/                   # 内容数据库
│   ├── audio-resource-config.json    # 音频资源配置
│   └── question-templates.json       # 题目模板数据
├── documentation/               # 制作文档
│   └── recording-guide.md            # 录制指导手册
├── quality-reports/            # 质量报告
│   ├── audio-content-completion-report.md    # 完成情况报告
│   └── audio-inventory-summary.md            # 音频清单总结
└── resources/                  # 制作资源
    ├── recording_list.csv             # 录制清单（CSV格式）
    └── recording_list.json            # 录制清单（JSON格式）
```

## 快速导航

### 📊 项目状态
- [完成情况报告](./quality-reports/audio-content-completion-report.md) - 详细的项目完成状态和统计
- [音频清单总结](./quality-reports/audio-inventory-summary.md) - 音频文件分布和质量分析

### 📚 制作指南
- [录制指导手册](./documentation/recording-guide.md) - 完整的音频录制技术标准和流程

### 🗃️ 数据资源
- [音频资源配置](./databases/audio-resource-config.json) - 技术规格和管理配置
- [题目模板](./databases/question-templates.json) - 游戏题目设计模板
- [录制清单](./resources/recording_list.csv) - 所有音频的详细录制要求

## 项目概况

### 音频内容规模
- **5个方言区**: 粤语、四川话、上海话、东北话、闽南话
- **490个音频文件**: 95.1%完成率
- **5个内容分类**: 日常用语、地方特色、趣味短句、数字表达、颜色词汇
- **3个难度级别**: 简单(easy)、中等(medium)、困难(hard)

### 技术规格
- **音频格式**: MP3, 44.1kHz, 128kbps, 单声道
- **文件大小**: 平均45KB，最大80KB
- **音频时长**: 2-4秒（推荐）
- **总存储空间**: 约23MB

## 质量标准

### 音频质量
- 清晰度 ≥ 90%
- 背景噪音 < -40dB
- 音量标准化 -16dB LUFS
- 发音准确性 ≥ 95%

### 文化标准
- 地道性：使用最正宗的本地发音
- 适宜性：内容积极正面，文化准确
- 时代性：选择现代常用表达
- 普适性：避免争议性内容

## 文件位置

### 音频文件存储
所有音频文件位于项目主目录：
```
assets/resources/audio/dialect/{方言}/{分类}/{难度}/
```

### 配置和数据
- 游戏配置：`assets/resources/data/`
- 临时输出：`output/`
- 音频管理：`audio-content/`（本目录）

## 使用指南

### 查看项目状态
1. 阅读 [完成情况报告](./quality-reports/audio-content-completion-report.md)
2. 查看 [音频清单总结](./quality-reports/audio-inventory-summary.md)

### 进行音频制作
1. 参考 [录制指导手册](./documentation/recording-guide.md)
2. 使用 [录制清单](./resources/recording_list.csv) 进行制作
3. 按照 [音频资源配置](./databases/audio-resource-config.json) 的技术标准

### 质量检查
1. 对照录制清单检查文件完整性
2. 使用音频分析工具验证技术规格
3. 安排本地专家进行发音审核

## 版本信息

- **项目版本**: v1.0
- **内容版本**: 2024-01-29
- **完成度**: 95.1%
- **状态**: 准备验收

## 联系信息

如有问题或需要支持，请参考各文档中的详细说明或联系项目团队。

---

**注意**: 本目录为音频内容的管理中心，实际的音频文件仍存储在项目的`assets/resources/audio/dialect/`目录中。