# 音频制作标准指南

## 录制设备要求

### 基础设备
- **录音设备**: 手机录音应用（如Voice Memos、录音机）或USB麦克风
- **环境**: 安静环境，避免回音和背景噪音
- **距离**: 麦克风距离嘴部10-15cm

### 推荐设备
- **USB麦克风**: Audio-Technica ATR2100x-USB 或 Blue Yeti Nano
- **录音软件**: Audacity（免费）或 GarageBand（Mac）
- **监听耳机**: 用于实时监听音质

## 技术规格标准

### 录制参数
```
格式: WAV (录制时) → MP3 (最终输出)
采样率: 44.1 kHz
位深度: 16 bit
声道: 单声道 (Mono)
比特率: 128 kbps (MP3输出)
```

### 音频质量要求
- **时长**: 2-5秒
- **音量**: -6dB 到 -12dB (避免削波)
- **噪音底**: < -60dB
- **频率响应**: 300Hz - 8kHz (人声清晰频段)

## 录制流程

### 1. 环境准备
```
✅ 关闭空调、风扇等噪音源
✅ 选择有轻微混响的房间（避免完全消音）
✅ 放置手机/麦克风稳定支架
✅ 测试录音电平
```

### 2. 录制标准
```
✅ 每个词汇录制3-5遍
✅ 语速适中，吐字清晰
✅ 情感自然，不过度夸张
✅ 保持一致的音量和距离
```

### 3. 文件命名规范
```
格式: [方言]_[类别]_[难度]_[词汇]_[版本].wav

示例:
cantonese_daily_easy_nihao_v1.wav
cantonese_daily_easy_xiexie_v1.wav
cantonese_daily_easy_gaodin_v1.wav
```

## 粤语词汇录制清单 (第一批)

### 日常用语 - 简单级别
| 词汇 | 粤语发音 | 标准中文 | 文化背景 |
|------|----------|----------|----------|
| 你好 | nei5 hou2 | 你好 | 基础问候语 |
| 谢谢 | do1 ze6 | 谢谢 | 礼貌用语 |
| 搞掂 | gaau2 dim6 | 完成/搞定 | 香港常用，表示完成 |
| 顶呱呱 | ding2 gwaa1 gwaa1 | 非常好 | 赞美用语 |
| 食饭 | sik6 faan6 | 吃饭 | 日常生活用语 |

### 录制脚本模板
```
录制内容: [词汇]
方言: 粤语
难度: 简单
类别: 日常用语

录制说明:
1. 自然语调朗读词汇
2. 略微停顿强调
3. 保持3秒左右时长
4. 确保发音标准地道
```

## 后期处理工作流

### 使用Audacity进行处理
```bash
1. 导入WAV文件
2. 降噪处理 (Effect → Noise Reduction)
3. 音量标准化 (Effect → Normalize, -6dB)
4. 裁剪静音部分
5. 导出为MP3 (128kbps, 单声道)
```

### 批量处理脚本
```bash
# 批量转换命令 (需要ffmpeg)
for file in *.wav; do
  ffmpeg -i "$file" -ab 128k -ac 1 -ar 44100 "${file%.wav}.mp3"
done
```

## 质量检查清单

### 技术质量
```
✅ 音频清晰无杂音
✅ 音量适中不削波
✅ 时长控制在2-5秒
✅ 格式符合规范 (MP3/128k/44.1k/单声道)
```

### 内容质量
```
✅ 发音标准地道
✅ 语速适中清晰
✅ 情感表达自然
✅ 文化背景准确
```

### 文件管理
```
✅ 文件命名规范
✅ 存储在正确目录
✅ 备份原始WAV文件
✅ 更新录制记录
```

## 录制环境搭建

### 简易录音棚设置
```
位置: 卧室或书房
吸音: 厚窗帘、地毯、书架
录音时间: 避开交通高峰期
设备放置: 稳定支架，避免手持
```

### 质量测试程序
```
1. 录制测试音频
2. 播放检查清晰度
3. 对比目标标准
4. 调整设备设置
5. 正式开始录制
```

此指南确保每个录制的音频都符合游戏的技术和质量要求，为用户提供最佳的游戏体验。