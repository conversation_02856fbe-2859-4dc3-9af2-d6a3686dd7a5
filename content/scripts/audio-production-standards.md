# 音频内容制作标准 - 家乡话猜猜猜游戏

## 技术规格要求

### 音频格式规范
- **容器格式**: MP3
- **编码**: MPEG-1 Audio Layer III
- **采样率**: 44.1 kHz
- **比特率**: 128 kbps (CBR 恒定比特率)
- **声道**: 单声道 (Mono)
- **时长**: 2-6秒 (推荐3-4秒)

### 文件大小控制
- **目标大小**: 30-50KB 每个文件
- **最大限制**: 80KB
- **压缩要求**: 音质清晰前提下最小化文件大小
- **批量大小**: 每个方言区域总计 < 5MB

### 音频质量标准

#### 1. 技术质量指标
```
噪音控制: < -40dB (背景噪音)
频率响应: 80Hz - 15kHz (人声优化)
动态范围: 6-12dB 
响度标准: -16 LUFS (流媒体标准)
削峰控制: 不超过 -1dBFS
```

#### 2. 录音环境要求
- **录音室**: 专业隔音环境或家庭隔音房间
- **麦克风**: 心形指向电容麦克风，距离15-20cm
- **监听**: 封闭式耳机实时监听
- **前置放大器**: 音频接口增益控制在-12dB到-6dB

#### 3. 后期制作标准
```bash
# 音频处理流程
1. 降噪处理 (Noise Reduction)
2. 均衡调整 (EQ: 高通80Hz, 中频增强)
3. 压缩限制 (Compressor: Ratio 3:1, Attack 10ms)
4. 响度标准化 (-16 LUFS)
5. 削峰限制 (Limiter: -1dBFS)
6. 格式转换 (WAV → MP3 128kbps)
```

## 发音质量要求

### 准确性标准
- **本土化**: 必须是当地母语者录制
- **纯正度**: 方言发音纯正，无普通话口音影响
- **一致性**: 同一方言区域由同一录音者完成
- **清晰度**: 发音清晰，音节完整

### 语速控制
- **正常语速**: 接近日常对话速度
- **停顿适当**: 词汇间有自然停顿
- **重音明确**: 方言特色重音突出
- **语调自然**: 符合方言语调特点

### 录音内容规范
```
录音格式: [方言词汇] + 0.3秒停顿
示例: "你好阿" (粤语) → nei5 hou2 aa3
音量一致: 所有录音保持相同音量级别
语气统一: 平和、清晰、自然的语气
```

## 文化质量标准

### 内容适宜性
- **积极正面**: 选择积极向上的词汇和表达
- **文化准确**: 准确反映当地文化背景
- **地域特色**: 突出方言独特性和趣味性
- **避免争议**: 不使用可能引起争议的词汇

### 教育价值
- **学习性**: 有助于了解方言文化
- **趣味性**: 增加游戏的娱乐性
- **代表性**: 选择最具代表性的方言词汇
- **实用性**: 日常生活中常用表达

## 录音人员要求

### 资质标准
- **母语者**: 在当地生活10年以上
- **年龄范围**: 25-45岁 (声音成熟稳定)
- **教育背景**: 高中以上学历
- **表达能力**: 普通话流利，能解释方言含义

### 专业能力
- **发音标准**: 方言发音纯正地道
- **声音特质**: 声音清晰，音色适中
- **录音经验**: 有配音或录音经验优先
- **文化理解**: 深度了解当地文化背景

## 质量审核流程

### 三级审核制度

#### 一级审核 (录音师自检)
```
✓ 技术指标检查 (音量、清晰度、噪音)
✓ 发音准确性确认
✓ 文件格式和命名检查
✓ 时长控制验证
```

#### 二级审核 (本地专家评审)
```
✓ 方言纯正度评估 (≥95%准确率)
✓ 文化适宜性审核
✓ 教育价值评估
✓ 与标准对比验证
```

#### 三级审核 (技术质检)
```
✓ 音频技术规格全面检测
✓ 兼容性测试 (iOS/Android/小程序)
✓ 性能影响评估
✓ 批量质量统计分析
```

### 不合格处理
- **重录标准**: 技术或发音问题严重
- **修复优先**: 可通过后期处理解决的问题
- **替换方案**: 准备备选词汇和录音者

## 文件命名和组织

### 命名规范
```
格式: {dialect}_{category}_{difficulty}_{sequence}.mp3
示例: cantonese_daily_easy_001.mp3

组成说明:
- dialect: 方言标识 (cantonese/sichuan/shanghai/northeast/minnan)
- category: 内容分类 (daily/local_terms/numbers/colors/humor)
- difficulty: 难度等级 (easy/medium/hard)
- sequence: 序号 (001-999)
```

### 目录结构
```
content/audio/
├── cantonese/
│   ├── daily/easy/        # 日常用语-简单
│   ├── daily/medium/      # 日常用语-中等
│   ├── local_terms/medium/ # 地方词汇-中等
│   ├── local_terms/hard/  # 地方词汇-困难
│   ├── numbers/easy/      # 数字-简单
│   ├── colors/medium/     # 颜色-中等
│   └── humor/medium/      # 幽默-中等
└── [其他方言同样结构]
```

## 成本控制和效率

### 预算分配
```
录音人员费用: 5人×500元/天×5天 = 12,500元
审核专家费用: 5人×800元/方言 = 4,000元
后期制作费用: 1人×1000元/天×2天 = 2,000元
设备租赁费用: 2,000元
其他费用: 4,500元
总计: 25,000元 (约48.5元/个音频)
```

### 效率优化
- **批量录制**: 同方言内容集中录制
- **模板化**: 标准化录音流程和后期处理
- **并行作业**: 录制和后期制作并行进行
- **质量预检**: 录制过程中实时质量控制

## 设备和软件要求

### 录音设备
```
麦克风: Audio-Technica AT2020 (电容麦)
音频接口: Focusrite Scarlett 2i2 (USB接口)
监听耳机: Sony MDR-7506 (封闭式)
录音环境: 吸音泡沫 + 麦克风隔离罩
```

### 软件工具
```
录音软件: Audacity (免费) / Adobe Audition (专业)
后期处理: Audacity + 插件包
格式转换: FFmpeg (批量处理)
质量检测: Spek (频谱分析)
```

### 批量处理脚本
```bash
#!/bin/bash
# 音频批量处理脚本示例
for file in *.wav; do
    # 降噪 → 标准化 → 转MP3
    ffmpeg -i "$file" -af "highpass=f=80,compand,loudnorm=I=-16:LRA=7:tp=-1" -b:a 128k "${file%.wav}.mp3"
done
```

## 持续改进机制

### 数据收集
- **用户反馈**: 收集游戏中音频质量反馈
- **技术监控**: 自动化音频质量监控
- **使用统计**: 各音频使用频次和正确率统计
- **性能指标**: 加载速度和播放稳定性

### 优化策略
- **A/B测试**: 不同音频版本效果对比
- **迭代更新**: 根据反馈持续优化
- **扩展计划**: 新方言和内容扩展策略
- **技术升级**: 音频技术和工具升级

---

**制作周期**: 总计14天
**质量目标**: 发音准确率≥95%，音频清晰度≥90%，文化适宜性100%
**技术目标**: 兼容性100%，文件大小控制在目标范围内
**用户体验**: 加载速度<500ms，播放稳定性100%