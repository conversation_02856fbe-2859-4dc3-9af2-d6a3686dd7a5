# 围观功能UI组件设计规范

## 🎯 组件设计原则

### 围观体验核心
1. **沉浸感**: 让用户感受到真实的围观氛围
2. **参与感**: 通过弹幕和预测增强互动体验
3. **实时性**: 所有状态变化都要有即时反馈
4. **社交性**: 突出围观者之间的互动关系

## 🏠 围观房间组件

### WatchRoomCard - 围观房间卡片
```typescript
interface WatchRoomCardProps {
  roomId: string;
  playerInfo: {
    nickname: string;
    avatar: string;
    level: number;
    region: string;
  };
  gameStatus: 'waiting' | 'playing' | 'finished';
  viewerCount: number;
  currentQuestion?: {
    dialect: string;
    difficulty: 'easy' | 'medium' | 'hard';
  };
  isLive: boolean;
}

// 视觉状态
- 等待中: 柔和呼吸动画，黄色状态点
- 进行中: 绿色脉冲动画，实时数据更新
- 已结束: 静态显示，灰色调处理
```

### WatchRoomHeader - 房间顶部信息栏
```typescript
interface WatchRoomHeaderProps {
  roomInfo: {
    roomId: string;
    playerName: string;
    gameProgress: string; // "第3题/共10题"
    timeRemaining: number; // 剩余时间(秒)
  };
  viewerCount: number;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  onExit: () => void;
}

// 布局结构
- 左侧: 玩家信息 + 游戏进度
- 中间: 围观人数 + 连接状态
- 右侧: 退出按钮
```

## 💬 弹幕系统组件

### BarrageContainer - 弹幕容器
```typescript
interface BarrageContainerProps {
  messages: BarrageMessage[];
  maxHeight: number;
  autoScroll: boolean;
  onPause: () => void;
  onResume: () => void;
  filterOptions: {
    showPredictions: boolean;
    showSystemMessages: boolean;
    showVipMessages: boolean;
  };
}

interface BarrageMessage {
  id: string;
  userId: string;
  nickname: string;
  avatar: string;
  content: string;
  type: 'normal' | 'prediction' | 'system' | 'vip';
  timestamp: number;
  userLevel: number;
}
```

### BarrageInput - 弹幕输入框
```typescript
interface BarrageInputProps {
  placeholder: string;
  maxLength: number;
  onSend: (message: string) => void;
  disabled: boolean;
  rateLimitInfo: {
    remaining: number;
    resetTime: number;
  };
  quickReplies: string[]; // 快捷回复选项
}

// 功能特性
- 实时字数统计
- 发送频率限制提示
- 快捷表情和回复
- 敏感词实时检测
```

### BarrageMessage - 单条弹幕消息
```typescript
interface BarrageMessageProps {
  message: BarrageMessage;
  showAvatar: boolean;
  showTimestamp: boolean;
  onUserClick: (userId: string) => void;
  onMessageLike: (messageId: string) => void;
}

// 消息类型样式
- 普通消息: 白色背景，常规字体
- 预测消息: 蓝色背景，加粗显示
- 系统消息: 黄色背景，居中显示
- VIP消息: 金色边框，特殊图标
```

## 🎯 预测游戏组件

### PredictionPanel - 预测面板
```typescript
interface PredictionPanelProps {
  question: {
    id: string;
    options: string[];
    timeLimit: number; // 预测时间限制(秒)
  };
  predictions: {
    [option: string]: {
      count: number;
      percentage: number;
    };
  };
  userPrediction?: string;
  onPredict: (option: string) => void;
  disabled: boolean;
}

// 状态管理
- 预测开放: 显示选项，允许选择
- 预测关闭: 显示统计，禁用选择
- 结果揭晓: 高亮正确答案，显示用户结果
```

### PredictionOption - 预测选项
```typescript
interface PredictionOptionProps {
  option: string;
  count: number;
  percentage: number;
  isSelected: boolean;
  isCorrect?: boolean;
  isRevealed: boolean;
  onClick: () => void;
  disabled: boolean;
}

// 视觉状态
- 未选择: 白色背景，灰色边框
- 已选择: 红色边框，轻微阴影
- 正确答案: 绿色背景，成功图标
- 错误选择: 红色背景，错误图标
```

### PredictionResult - 预测结果
```typescript
interface PredictionResultProps {
  userPrediction: string;
  correctAnswer: string;
  earnedPoints: number;
  rank: number;
  totalParticipants: number;
  streakCount: number;
}

// 结果展示
- 预测正确: 绿色庆祝动画 + 积分奖励
- 预测错误: 红色失望动画 + 正确答案提示
- 排名显示: 当前排名 / 总参与人数
- 连击记录: 连续预测正确次数
```

## 👥 围观者列表组件

### ViewerList - 围观者列表
```typescript
interface ViewerListProps {
  viewers: ViewerInfo[];
  maxDisplay: number;
  showOnlineStatus: boolean;
  onViewerClick: (userId: string) => void;
}

interface ViewerInfo {
  userId: string;
  nickname: string;
  avatar: string;
  level: number;
  isVip: boolean;
  isOnline: boolean;
  joinTime: number;
}

// 显示策略
- 最多显示8个头像
- 超出部分显示"+N"
- VIP用户优先显示
- 按加入时间排序
```

### ViewerAvatar - 围观者头像
```typescript
interface ViewerAvatarProps {
  viewer: ViewerInfo;
  size: 'small' | 'medium' | 'large';
  showLevel: boolean;
  showOnlineStatus: boolean;
  onClick: () => void;
}

// 头像装饰
- VIP用户: 金色边框
- 高等级用户: 彩色边框
- 在线状态: 绿色小圆点
- 离线状态: 灰色处理
```

## 📊 实时统计组件

### GameStats - 游戏统计
```typescript
interface GameStatsProps {
  stats: {
    currentQuestion: number;
    totalQuestions: number;
    correctAnswers: number;
    accuracy: number;
    timeElapsed: number;
    averageResponseTime: number;
  };
  showDetailed: boolean;
}

// 统计展示
- 进度条: 当前题目进度
- 准确率: 圆形进度图
- 用时统计: 数字显示 + 趋势图标
```

### ViewerStats - 围观统计
```typescript
interface ViewerStatsProps {
  stats: {
    totalViewers: number;
    peakViewers: number;
    averageViewTime: number;
    messageCount: number;
    predictionParticipation: number;
  };
  realTimeUpdates: boolean;
}

// 实时更新
- 围观人数: 实时变化动画
- 消息统计: 滚动数字效果
- 参与度: 百分比进度条
```

## 🎮 交互控制组件

### WatchControls - 围观控制栏
```typescript
interface WatchControlsProps {
  controls: {
    canSendBarrage: boolean;
    canPredict: boolean;
    canLike: boolean;
    canShare: boolean;
  };
  onBarrageToggle: () => void;
  onPredictionToggle: () => void;
  onLike: () => void;
  onShare: () => void;
}

// 控制按钮
- 弹幕开关: 控制弹幕显示/隐藏
- 预测参与: 开启/关闭预测功能
- 点赞按钮: 为玩家点赞
- 分享按钮: 分享围观房间
```

### NetworkIndicator - 网络状态指示器
```typescript
interface NetworkIndicatorProps {
  quality: 'excellent' | 'good' | 'poor' | 'disconnected';
  latency: number;
  onRetry: () => void;
}

// 状态显示
- 优秀: 3格信号，绿色
- 良好: 2格信号，黄色  
- 较差: 1格信号，红色
- 断线: 0格信号，灰色 + 重连按钮
```

这套组件设计确保了围观功能的完整性和一致性，同时提供了丰富的交互体验和实时反馈。
