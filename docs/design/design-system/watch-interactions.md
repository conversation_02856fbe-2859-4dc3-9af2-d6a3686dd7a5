# 围观功能交互设计规范

## 🎯 交互设计原则

### 核心交互理念
1. **直觉操作**: 3秒内理解所有交互逻辑
2. **即时反馈**: 每个操作都有明确的视觉反馈
3. **社交感知**: 突出围观的社交属性和群体感
4. **沉浸体验**: 让用户专注于游戏内容而非界面操作

## 🏠 围观房间发现与进入

### 房间发现流程
```
主页 → 围观大厅 → 房间列表 → 房间详情 → 进入围观
  ↓       ↓        ↓        ↓        ↓
 推荐   筛选条件   房间卡片   预览信息   连接建立
```

### 房间卡片交互
- **点击卡片**: 显示房间详情浮层
- **长按卡片**: 显示快捷操作菜单(收藏、分享、举报)
- **滑动卡片**: 左滑显示更多操作，右滑快速进入
- **状态指示**: 实时显示房间状态和围观人数变化

### 进入房间交互
```typescript
// 进入房间交互流程
interface RoomEntryFlow {
  // 1. 点击进入
  onRoomEnter: () => {
    showLoadingIndicator();
    establishConnection();
    preloadRoomData();
  };
  
  // 2. 连接建立
  onConnectionEstablished: () => {
    hideLoadingIndicator();
    showRoomInterface();
    playEnterAnimation();
    sendJoinMessage();
  };
  
  // 3. 连接失败
  onConnectionFailed: () => {
    showErrorDialog();
    offerRetryOptions();
  };
}
```

## 💬 弹幕系统交互

### 弹幕发送交互
```typescript
// 弹幕输入交互
interface BarrageInputInteraction {
  // 输入状态管理
  onInputFocus: () => {
    expandInputArea();
    showQuickReplies();
    showEmojiPanel();
  };
  
  onInputBlur: () => {
    collapseInputArea();
    hideQuickReplies();
  };
  
  // 发送交互
  onSend: (message: string) => {
    validateMessage();
    showSendingIndicator();
    submitMessage();
    showSuccessFeedback();
    clearInput();
  };
  
  // 频率限制处理
  onRateLimitHit: () => {
    showRateLimitWarning();
    disableInputTemporarily();
    showCountdownTimer();
  };
}
```

### 弹幕浏览交互
- **自动滚动**: 新消息自动滚动到底部
- **手动暂停**: 触摸弹幕区域暂停自动滚动
- **消息点击**: 点击消息显示用户信息卡片
- **消息长按**: 显示操作菜单(回复、举报、屏蔽)

### 弹幕过滤交互
```typescript
// 弹幕过滤控制
interface BarrageFilterControls {
  filters: {
    showPredictions: boolean;
    showSystemMessages: boolean;
    showVipOnly: boolean;
    hideBlockedUsers: boolean;
  };
  
  onFilterToggle: (filterType: string) => {
    updateFilterState();
    refreshBarrageDisplay();
    saveUserPreference();
  };
}
```

## 🎯 预测游戏交互

### 预测参与流程
```
题目开始 → 预测开放 → 选择答案 → 提交预测 → 等待结果 → 查看结果
    ↓         ↓         ↓         ↓         ↓         ↓
  倒计时    选项显示   确认选择   提交反馈   实时统计   积分奖励
```

### 预测选择交互
```typescript
// 预测选择交互
interface PredictionInteraction {
  // 选项选择
  onOptionSelect: (option: string) => {
    highlightSelection();
    showConfirmButton();
    updatePredictionStats();
  };
  
  // 预测提交
  onPredictionSubmit: () => {
    showSubmitAnimation();
    lockSelection();
    showWaitingState();
    updateUserStats();
  };
  
  // 结果揭晓
  onResultReveal: (correct: boolean, points: number) => {
    if (correct) {
      showSuccessAnimation();
      displayPointsEarned();
      updateStreak();
    } else {
      showFailureAnimation();
      showCorrectAnswer();
      resetStreak();
    }
  };
}
```

### 预测统计查看
- **实时更新**: 预测分布实时变化，带动画效果
- **详细统计**: 点击统计区域显示详细数据
- **历史记录**: 长按显示个人预测历史

## 👥 社交互动交互

### 围观者互动
```typescript
// 围观者社交互动
interface ViewerSocialInteraction {
  // 用户信息查看
  onViewerClick: (userId: string) => {
    showUserProfileCard();
    displayUserStats();
    showInteractionOptions();
  };
  
  // 社交操作
  onFollowUser: () => {
    sendFollowRequest();
    updateFollowStatus();
    showFollowFeedback();
  };
  
  onPrivateMessage: () => {
    openChatWindow();
    initializePrivateChat();
  };
}
```

### 点赞和礼物系统
```typescript
// 点赞礼物交互
interface LikeGiftInteraction {
  // 点赞操作
  onLike: () => {
    showLikeAnimation();
    incrementLikeCount();
    sendLikeMessage();
    showFloatingHeart();
  };
  
  // 礼物发送
  onGiftSend: (giftType: string) => {
    showGiftAnimation();
    deductUserCoins();
    broadcastGiftMessage();
    showGiftEffect();
  };
}
```

## 📱 手势和触控交互

### 基础手势
- **单击**: 选择、确认操作
- **双击**: 快速点赞
- **长按**: 显示上下文菜单
- **滑动**: 切换页面、滚动内容
- **捏合**: 缩放内容(如果支持)

### 围观专用手势
```typescript
// 围观界面手势
interface WatchGestures {
  // 快速操作手势
  onDoubleTab: () => {
    sendQuickLike();
    showHeartAnimation();
  };
  
  onSwipeUp: () => {
    showBarrageInput();
    focusInputField();
  };
  
  onSwipeDown: () => {
    hideBarrageInput();
    showFullGameView();
  };
  
  onSwipeLeft: () => {
    showPredictionPanel();
  };
  
  onSwipeRight: () => {
    showViewerList();
  };
}
```

## 🔄 状态反馈和动画

### 连接状态反馈
```typescript
// 连接状态视觉反馈
interface ConnectionFeedback {
  states: {
    connecting: {
      indicator: 'pulsing_dot',
      message: '正在连接...',
      animation: 'spin'
    },
    connected: {
      indicator: 'green_dot',
      message: '连接正常',
      animation: 'pulse'
    },
    unstable: {
      indicator: 'yellow_dot',
      message: '连接不稳定',
      animation: 'blink'
    },
    disconnected: {
      indicator: 'red_dot',
      message: '连接断开',
      animation: 'static'
    }
  };
}
```

### 操作反馈动画
```css
/* 成功操作反馈 */
.success-feedback {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% { 
    background-color: rgba(82,196,26,0.3);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(82,196,26,0.6);
    transform: scale(1.02);
  }
  100% { 
    background-color: transparent;
    transform: scale(1);
  }
}

/* 错误操作反馈 */
.error-feedback {
  animation: errorShake 0.5s ease-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 加载状态动画 */
.loading-state {
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}
```

## ⚡ 性能优化交互

### 智能加载策略
```typescript
// 性能优化交互策略
interface PerformanceOptimization {
  // 懒加载策略
  lazyLoading: {
    viewerAvatars: 'viewport_based',
    barrageHistory: 'scroll_based',
    predictionHistory: 'on_demand'
  };
  
  // 降级策略
  degradationStrategy: {
    lowNetwork: {
      reduceAnimations: true,
      lowerImageQuality: true,
      increasePollingInterval: true
    },
    lowBattery: {
      disableNonEssentialAnimations: true,
      reduceUpdateFrequency: true
    }
  };
}
```

### 用户体验优化
- **预加载**: 预测用户下一步操作，提前加载相关数据
- **缓存策略**: 智能缓存常用数据，减少网络请求
- **降级处理**: 网络不佳时自动降低功能复杂度
- **离线支持**: 连接断开时显示缓存内容，支持基本浏览

这套交互设计规范确保了围观功能的易用性和流畅性，同时保持了良好的性能表现。
