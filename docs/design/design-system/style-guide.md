# 《家乡话猜猜猜》样式指南

## 🎯 设计原则

### 核心理念
1. **文化传承**: 融入中国传统文化元素
2. **情感温度**: 营造家乡归属感和温暖氛围
3. **现代简约**: 符合年轻人审美的扁平化设计
4. **直觉交互**: 3秒内理解所有操作逻辑

### 视觉风格
- **文化感**: 水墨纹理、云纹装饰、印章风格
- **亲和力**: 温暖色彩、圆润边角、友好图标
- **现代感**: 扁平化设计、简洁布局、清晰层次
- **地域特色**: 34省份差异化视觉识别

## 📝 字体规范

### 中文字体
- **主标题**: PingFang SC Medium, 24px-32px
- **次标题**: PingFang SC Regular, 18px-22px
- **正文**: PingFang SC Regular, 14px-16px
- **说明文字**: PingFang SC Light, 12px-14px

### 英文字体
- **主要**: SF Pro Display, 系统字体
- **代码**: SF Mono, 等宽字体

### 字体层级
```
H1: 32px/1.2 - 主页标题
H2: 24px/1.3 - 页面标题  
H3: 20px/1.4 - 区块标题
H4: 18px/1.4 - 小标题
Body: 16px/1.5 - 正文
Caption: 14px/1.4 - 说明文字
Small: 12px/1.3 - 辅助文字
```

## 🔲 布局规范

### 网格系统
- **基础网格**: 8px 基础单位
- **边距**: 16px, 24px, 32px
- **间距**: 8px, 16px, 24px, 32px
- **圆角**: 4px, 8px, 12px, 16px

### 布局结构
```
移动端布局 (375px 基准):
- 页面边距: 16px
- 卡片内边距: 16px
- 组件间距: 16px
- 按钮高度: 44px
- 列表项高度: 56px
```

### 响应式断点
- **小屏**: < 375px
- **标准**: 375px - 414px  
- **大屏**: > 414px

## 🎨 组件规范

### 按钮样式
```css
主要按钮:
- 背景: 中国红 #C8102E
- 文字: 白色 #FFFFFF
- 圆角: 8px
- 高度: 44px
- 阴影: 0 2px 8px rgba(200,16,46,0.2)

次要按钮:
- 背景: 透明
- 边框: 1px solid #C8102E  
- 文字: 中国红 #C8102E
- 圆角: 8px
- 高度: 44px
```

### 卡片样式
```css
标准卡片:
- 背景: 白色 #FFFFFF
- 圆角: 12px
- 阴影: 0 2px 12px rgba(0,0,0,0.08)
- 内边距: 16px
- 边框: none
```

### 输入框样式
```css
文本输入框:
- 背景: 白色 #FFFFFF
- 边框: 1px solid #D9D9D9
- 圆角: 8px
- 高度: 44px
- 内边距: 12px 16px
- 聚焦边框: #C8102E
```

## 🎵 动效规范

### 动画时长
- **快速**: 200ms (hover, 点击反馈)
- **标准**: 300ms (页面切换, 弹窗)
- **慢速**: 500ms (复杂动画, 加载)

### 缓动函数
- **标准**: cubic-bezier(0.4, 0.0, 0.2, 1)
- **减速**: cubic-bezier(0.0, 0.0, 0.2, 1)
- **加速**: cubic-bezier(0.4, 0.0, 1, 1)
- **弹性**: cubic-bezier(0.68, -0.55, 0.265, 1.55)

### 常用动效
```css
/* 点击反馈 */
.tap-feedback {
  transform: scale(0.95);
  transition: transform 0.2s ease;
}

/* 悬浮效果 */
.hover-lift {
  transform: translateY(-2px);
  box_shadow: 0 4px 16px rgba(0,0,0,0.12);
  transition: all 0.3s ease;
}

/* 加载动画 */
.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
```

## 📱 移动端适配

### 安全区域
- **顶部**: 状态栏高度 + 20px
- **底部**: Home Indicator + 34px
- **侧边**: 16px 最小边距

### 触摸目标
- **最小尺寸**: 44x44px
- **推荐尺寸**: 48x48px
- **间距**: 8px 最小间距

### 字体缩放
```css
/* 支持系统字体缩放 */
body {
  font-size: calc(16px + 0.5vw);
  line-height: 1.5;
}

@media (max-width: 375px) {
  body { font-size: 14px; }
}

@media (min-width: 414px) {
  body { font-size: 16px; }
}
```

## 🌟 特色元素

### 传统文化装饰
- **云纹图案**: 页面四角装饰
- **水墨纹理**: 背景叠加效果
- **印章样式**: 成就徽章设计
- **书法字体**: 特殊场景标题

### 省份特色元素
每个省份包含:
- **标志建筑**: 简化线描风格
- **传统纹样**: 地域特色图案
- **色彩主题**: 专属品牌色彩
- **文化符号**: 代表性文化元素

### 音频可视化
- **音波动画**: 播放时的同心圆扩散
- **频谱显示**: 音频播放可视化
- **进度指示**: 音频播放进度条
- **音量控制**: 可视化音量调节

## 🎭 围观功能专用设计规范

### 围观界面布局
```css
围观主界面结构:
- 顶部状态栏: 房间信息、围观人数、退出按钮
- 游戏区域: 占屏幕60%，显示玩家游戏状态
- 弹幕区域: 占屏幕25%，实时滚动弹幕
- 交互区域: 占屏幕15%，预测、点赞、输入等操作
```

### 围观房间卡片
```css
房间卡片样式:
- 尺寸: 343x120px (移动端)
- 背景: 白色渐变到浅灰 #FFFFFF → #F8F9FA
- 圆角: 16px
- 阴影: 0 4px 16px rgba(200,16,46,0.1)
- 内边距: 16px
- 边框: 2px solid transparent (活跃时显示中国红)

房间状态指示:
- 进行中: 绿色脉冲动画 #52C41A
- 等待中: 黄色呼吸动画 #FAAD14
- 已结束: 灰色静态 #999999
```

### 弹幕系统UI
```css
弹幕容器:
- 背景: 半透明黑色 rgba(0,0,0,0.3)
- 圆角: 12px
- 内边距: 12px
- 最大高度: 200px
- 滚动: 自动滚动，支持手动暂停

弹幕消息样式:
- 背景: 半透明白色 rgba(255,255,255,0.9)
- 圆角: 18px
- 内边距: 8px 12px
- 字体: PingFang SC Regular 14px
- 行高: 1.4
- 间距: 4px垂直间距

弹幕类型样式:
- 普通弹幕: 白色背景
- 预测弹幕: 蓝色背景 rgba(24,144,255,0.1)
- 系统消息: 黄色背景 rgba(250,173,20,0.1)
- VIP弹幕: 金色边框 #FFD700
```

### 预测游戏界面
```css
预测选项卡片:
- 尺寸: 160x80px
- 背景: 白色 #FFFFFF
- 圆角: 12px
- 边框: 2px solid #D9D9D9
- 选中状态: 边框变为中国红 #C8102E
- 阴影: 0 2px 8px rgba(0,0,0,0.1)

预测统计显示:
- 进度条高度: 8px
- 圆角: 4px
- 背景: #F5F5F5
- 填充色: 渐变 #C8102E → #F4A259
- 动画: 0.5s ease-in-out

预测结果展示:
- 正确预测: 绿色背景 rgba(82,196,26,0.1)
- 错误预测: 红色背景 rgba(255,77,79,0.1)
- 积分显示: 金色文字 #FFD700
```

### 围观者列表
```css
围观者头像:
- 尺寸: 32x32px
- 圆角: 16px (圆形)
- 边框: 2px solid #FFFFFF
- 阴影: 0 2px 4px rgba(0,0,0,0.1)
- 重叠排列: -8px间距

围观人数显示:
- 背景: 半透明黑色 rgba(0,0,0,0.6)
- 圆角: 12px
- 内边距: 4px 8px
- 文字: 白色 #FFFFFF
- 字体: PingFang SC Medium 12px
- 图标: 眼睛图标 + 数字
```

### 实时状态指示器
```css
连接状态指示:
- 在线: 绿色圆点 #52C41A + 脉冲动画
- 连接中: 黄色圆点 #FAAD14 + 旋转动画
- 离线: 红色圆点 #FF4D4F + 静态

网络质量指示:
- 优秀: 3格信号，绿色 #52C41A
- 良好: 2格信号，黄色 #FAAD14
- 较差: 1格信号，红色 #FF4D4F
```

### 围观功能动效
```css
/* 房间进入动画 */
.room-enter {
  animation: slideInUp 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 弹幕滚动动画 */
.barrage-scroll {
  animation: scrollUp 0.3s ease-out;
}

@keyframes scrollUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 预测选择动画 */
.prediction-select {
  animation: pulseSelect 0.2s ease-in-out;
}

@keyframes pulseSelect {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 围观人数变化动画 */
.viewer-count-change {
  animation: countUp 0.5s ease-out;
}

@keyframes countUp {
  0% {
    transform: scale(0.8);
    color: #52C41A;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}
```

### 围观功能交互反馈
```css
/* 点赞动画 */
.like-animation {
  animation: likeFloat 1s ease-out forwards;
}

@keyframes likeFloat {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px) scale(1.5);
    opacity: 0;
  }
}

/* 弹幕发送成功反馈 */
.barrage-sent {
  animation: sentFeedback 0.3s ease-out;
}

@keyframes sentFeedback {
  0% { background-color: rgba(82,196,26,0.3); }
  100% { background-color: transparent; }
}

/* 预测提交反馈 */
.prediction-submitted {
  animation: submitPulse 0.6s ease-out;
}

@keyframes submitPulse {
  0%, 100% {
    border-color: #C8102E;
    box-shadow: 0 0 0 0 rgba(200,16,46,0.4);
  }
  50% {
    border-color: #C8102E;
    box-shadow: 0 0 0 8px rgba(200,16,46,0);
  }
}
```

这套围观功能设计规范确保了与主游戏界面的一致性，同时突出了围观的社交娱乐特性，营造了"看热闹"的氛围感。