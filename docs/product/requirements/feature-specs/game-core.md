# 游戏核心功能详细规格

## 📋 文档信息

- **功能模块**: 游戏核心玩法
- **文档版本**: v1.0
- **创建日期**: 2024-07-30
- **负责人**: product-manager-agent
- **开发优先级**: P0 (核心功能)

---

## 🎯 功能概述

游戏核心功能是整个产品的核心价值载体，包括经典问答模式、挑战模式和UGC模式三个主要玩法，为用户提供不同层次的方言学习和娱乐体验。

---

## 🎮 经典模式 (Classic Mode)

### 功能定位
标准的方言音频问答游戏，适合所有用户群体的基础娱乐和学习需求。

### 详细规格

#### 模式选择界面
**界面元素**:
- 方言地区选择器 (12个主要方言区域)
- 难度等级选择 (简单/中等/困难)  
- 个人最佳成绩显示
- 开始游戏按钮

**交互逻辑**:
```
用户进入 → 显示地区选择 → 选择方言区域 → 选择难度 → 显示历史成绩 → 开始游戏
```

**技术要求**:
- 界面响应时间 <100ms
- 地区选择支持搜索和拼音输入
- 历史成绩从缓存或数据库加载
- 支持上次选择记忆功能

#### 方言区域配置
**支持地区列表**:
```yaml
官话方言区:
  - 北京官话: {id: 1, name: "北京话", difficulty_range: [1,10]}
  - 东北官话: {id: 2, name: "东北话", difficulty_range: [1,8]}
  - 胶辽官话: {id: 3, name: "山东话", difficulty_range: [2,9]}
  - 中原官话: {id: 4, name: "河南话", difficulty_range: [2,8]}

吴方言区:
  - 上海话: {id: 5, name: "上海话", difficulty_range: [3,10]}
  - 苏州话: {id: 6, name: "苏州话", difficulty_range: [4,10]}
  - 杭州话: {id: 7, name: "杭州话", difficulty_range: [3,9]}

粤方言区:
  - 广州话: {id: 8, name: "广州话", difficulty_range: [2,10]}
  - 深圳话: {id: 9, name: "深圳话", difficulty_range: [2,8]}

其他方言区:
  - 闽南话: {id: 10, name: "闽南话", difficulty_range: [4,10]}
  - 客家话: {id: 11, name: "客家话", difficulty_range: [3,9]}
  - 湘语: {id: 12, name: "湖南话", difficulty_range: [3,8]}
```

#### 游戏主界面
**界面布局**:
```
┌─────────────────────────────────────┐
│  返回 [X]    计时器 [01:23]   暂停 [||] │
├─────────────────────────────────────┤
│            当前题目: 5/10            │
├─────────────────────────────────────┤
│        🔊 音频播放器                 │
│     ▶️ 播放  ⏸️ 暂停  🔄 重播        │
├─────────────────────────────────────┤
│  题目文字显示区域 (可选)             │
├─────────────────────────────────────┤
│  选项A: XXXXXXXXXXXX                │
│  选项B: XXXXXXXXXXXX                │  
│  选项C: XXXXXXXXXXXX                │
│  选项D: XXXXXXXXXXXX                │
├─────────────────────────────────────┤
│   当前得分: 180  连答: 3题正确       │
└─────────────────────────────────────┘
```

**交互逻辑**:
1. 自动播放音频 (3秒后可重播)
2. 用户选择答案
3. 立即显示正确/错误反馈
4. 显示解析和积分变化
5. 2秒后自动进入下一题

#### 音频处理规格
**音频格式标准**:
- 格式: MP3/AAC双格式支持
- 采样率: 16kHz (标准质量)
- 码率: 64kbps (平衡质量与大小)
- 时长: 3-10秒
- 文件大小: <200KB

**音频加载策略**:
```typescript
class AudioLoadingStrategy {
    // 预加载策略
    preloadAudio() {
        // 1. 预加载前3题音频 (优先级1)
        // 2. 后台加载第4-6题 (优先级2)
        // 3. 智能预测后续题目 (优先级3)
    }
    
    // 缓存策略  
    cacheManagement() {
        // LRU缓存: 最多缓存50个音频文件
        // 内存限制: 最大10MB音频缓存
        // 过期策略: 30分钟未使用自动清理
    }
}
```

#### 积分计算规则
**基础分数**:
```
简单难度: 10分/题
中等难度: 20分/题  
困难难度: 30分/题
```

**加分机制**:
```javascript
function calculateScore(difficulty, answerTime, isCorrect, consecutiveCount) {
    if (!isCorrect) return 0;
    
    let baseScore = difficulty === 'easy' ? 10 : 
                   difficulty === 'medium' ? 20 : 30;
    
    // 时间加成 (越快答对加分越多)
    let timeBonus = Math.max(0, (10 - answerTime) * difficulty_multiplier);
    
    // 连答加成
    let consecutiveBonus = consecutiveCount >= 5 ? 50 : 0;
    
    return baseScore + timeBonus + consecutiveBonus;
}
```

**特殊奖励**:
- 连续5题正确: +50分
- 单轮全对 (10题): +100分
- 快速答题 (<3秒): +5-15分额外加成
- 首次完成某难度: +20分

#### 游戏结束界面
**结果展示**:
```
┌─────────────────────────────────────┐
│            🎉 游戏结束               │
├─────────────────────────────────────┤
│        您的得分: 245分               │
│        正确率: 8/10 (80%)           │
│        用时: 3分25秒                 │
│        超越: 68%的玩家               │
├─────────────────────────────────────┤
│    🏆 获得成就: 方言新手             │
├─────────────────────────────────────┤
│  [再来一局]  [分享成绩]  [返回首页]   │
└─────────────────────────────────────┘
```

**分享功能集成**:
- 自动生成个性化分享卡片
- 包含得分、正确率、方言类型
- 一键分享到微信好友/群/朋友圈
- 分享内容包含邀请链接

---

## ⚡ 挑战模式 (Challenge Mode)

### 功能定位
限时快速答题模式，考验用户的方言水平和反应速度，增加竞技性和刺激感。

### 详细规格

#### 挑战规则设计
**基础规则**:
- 游戏时长: 60秒倒计时
- 题目数量: 无限制 (能答多少算多少)
- 难度递增: 每10题提升一个难度等级
- 答错惩罚: 扣除5秒时间
- 连答奖励: 连续5题正确 +10秒

**难度递增曲线**:
```
题目 1-10:   简单难度 (3分/题)
题目 11-20:  中等难度 (5分/题)  
题目 21-30:  困难难度 (8分/题)
题目 31+:    专家难度 (12分/题)
```

#### 实时排行榜
**排行榜类型**:
- 实时榜: 当前正在进行的挑战排名
- 日榜: 当日最高分排行
- 周榜: 本周最高分排行
- 总榜: 历史最高分排行

**排行榜数据结构**:
```json
{
  "rank": 1,
  "user_id": "user123",
  "nickname": "方言达人",
  "avatar": "https://avatar.url",
  "score": 456,
  "correct_count": 38,
  "region": "上海",
  "timestamp": "2024-07-30T10:00:00Z"
}
```

#### 多人同时挑战
**功能设计**:
- 支持最多100人同时挑战
- 实时显示前10名排名
- 挑战结束后显示最终排行
- 支持好友间的挑战邀请

**技术实现**:
```typescript
class ChallengeSession {
    sessionId: string;
    participants: User[];
    startTime: Date;
    duration: number = 60; // 秒
    
    // 实时排名更新
    updateRanking(userId: string, newScore: number) {
        // WebSocket推送排名变化
        // 只推送前10名变化以节省带宽
    }
    
    // 挑战结束处理
    endChallenge() {
        // 计算最终排名
        // 发送结果通知
        // 更新用户历史记录
    }
}
```

---

## 🎨 UGC模式 (User Generated Content)

### 功能定位
用户贡献内容模式，让用户参与内容创作，构建社区驱动的内容生态。

### 详细规格

#### 内容创作工具
**音频录制功能**:
```
录制界面布局:
┌─────────────────────────────────────┐
│  [X 取消]    录制方言音频    [✓ 完成] │
├─────────────────────────────────────┤
│        🎤 点击开始录制               │
│         录制时长: 00:00              │
├─────────────────────────────────────┤
│  录制质量指示器: ●●●●○ (良好)        │
├─────────────────────────────────────┤
│  [▶️ 试听]  [🔄 重录]  [⏹️ 停止]    │
├─────────────────────────────────────┤
│  添加题目文字:                       │
│  [________________]                  │
│                                     │
│  选择方言地区: [下拉选择]             │
│  选择难度等级: [●简单 ○中等 ○困难]    │
└─────────────────────────────────────┘
```

**录制技术规格**:
- 支持格式: WAV录制 → MP3转换
- 采样率: 16kHz
- 最大录制时长: 15秒
- 最小录制时长: 2秒
- 实时音量检测和提示
- 自动降噪处理

#### 题目创建系统
**题目类型支持**:
1. **音频理解题**: 听音频选择正确含义
2. **发音对比题**: 区分相似发音的不同词汇
3. **文化背景题**: 与方言相关的文化知识
4. **语境应用题**: 在特定情境下使用方言

**题目创建流程**:
```
录制音频 → 添加题目文字 → 设置选项 → 添加解析 → 选择分类 → 提交审核
```

**题目数据结构**:
```json
{
  "id": "ugc_question_001",
  "creator_id": "user123",
  "audio_url": "https://cdn.example.com/audio/ugc/001.mp3",
  "question_text": "这句话是什么意思？",
  "options": [
    {"id": "A", "text": "你好吗", "is_correct": true},
    {"id": "B", "text": "再见", "is_correct": false},
    {"id": "C", "text": "谢谢", "is_correct": false},
    {"id": "D", "text": "不客气", "is_correct": false}
  ],
  "explanation": "这是典型的上海话问候语",
  "dialect_region": "shanghai",
  "difficulty": "medium",
  "cultural_note": "上海人常用的日常问候",
  "status": "pending_review",
  "created_at": "2024-07-30T10:00:00Z"
}
```

#### 三级审核机制

##### L1: AI自动审核
**审核内容**:
- 音频质量检测 (清晰度、噪音、时长)
- 内容安全检测 (敏感词、违规内容)
- 基础格式验证 (文件格式、大小)

**技术实现**:
```python
class AIContentReview:
    def audio_quality_check(self, audio_file):
        # 音频质量分析
        quality_score = analyze_audio_quality(audio_file)
        noise_level = detect_noise_level(audio_file)
        
        return {
            "quality_score": quality_score,  # 0-100
            "noise_level": noise_level,      # 0-100
            "is_clear": quality_score > 70 and noise_level < 30
        }
    
    def content_safety_check(self, text_content):
        # 内容安全检测
        sensitive_words = detect_sensitive_words(text_content)
        inappropriate_content = detect_inappropriate_content(text_content)
        
        return {
            "is_safe": len(sensitive_words) == 0 and not inappropriate_content,
            "issues": sensitive_words + inappropriate_content
        }
```

##### L2: 专家人工审核
**审核标准**:
- 方言准确性 (专业性检查)
- 文化适宜性 (文化背景正确性)
- 教育价值 (是否有学习价值)
- 创意独特性 (避免重复内容)

**审核流程**:
```
AI审核通过 → 分配专家 → 专家评估 → 给出意见 → 通过/拒绝/修改建议
```

**专家审核界面**:
```
审核内容显示区域:
- 音频播放和波形显示
- 题目文字和选项
- 创作者信息和历史记录

审核工具:
- 方言准确性评分 (1-5星)
- 内容质量评分 (1-5星)  
- 审核意见文本框
- 快速标签选择
- 通过/拒绝/修改按钮
```

##### L3: 社区众包审核
**机制设计**:
- 已发布内容的持续监督
- 用户举报和评分系统
- 众包审核员志愿者体系
- 基于声誉的审核权重

**众包审核激励**:
- 审核积分奖励
- 社区贡献认证
- 月度优秀审核员奖励
- 审核准确率排行榜

---

## 📊 数据埋点与分析

### 关键指标追踪

#### 游戏行为数据
```javascript
// 游戏开始
track('game_start', {
    mode: 'classic|challenge|ugc',
    dialect_region: 'shanghai',
    difficulty: 'medium',
    user_level: 5
});

// 答题行为
track('question_answered', {
    question_id: 'q001',
    is_correct: true,
    answer_time: 4.2,
    chosen_option: 'A',
    difficulty: 'medium'
});

// 游戏结束
track('game_end', {
    total_score: 245,
    correct_rate: 0.8,
    total_time: 205,
    questions_count: 10
});
```

#### 社交分享数据
```javascript
// 分享行为
track('share_initiated', {
    share_type: 'wechat_friend|wechat_moment|wechat_group',
    content_type: 'game_result|achievement|invitation',
    game_score: 245
});

// 分享转化
track('share_conversion', {
    share_id: 'share_123',
    new_user_registered: true,
    conversion_time: 3600 // 秒
});
```

#### UGC行为数据
```javascript
// 内容创作
track('ugc_creation_start', {
    content_type: 'audio_question|story|tutorial'
});

track('ugc_submission', {
    content_id: 'ugc_001',
    creation_time: 1200, // 秒
    audio_duration: 8.5,
    dialect_region: 'shanghai'
});

// 内容审核
track('ugc_review_result', {
    content_id: 'ugc_001',
    review_stage: 'ai|expert|community',
    result: 'approved|rejected|needs_modification',
    review_time: 3600
});
```

### 性能监控指标
```javascript
// 页面性能
track('page_performance', {
    page_name: 'game_main',
    load_time: 2.1,
    first_contentful_paint: 1.2,
    largest_contentful_paint: 1.8
});

// 音频性能  
track('audio_performance', {
    audio_id: 'audio_001',
    load_time: 1.5,
    play_success: true,
    quality: 'high|medium|low'
});

// API性能
track('api_performance', {
    endpoint: '/api/game/submit',
    response_time: 245,
    status_code: 200,
    payload_size: 1024
});
```

---

## 🔧 技术实现要求

### 前端技术栈
- **游戏引擎**: Cocos Creator 3.8.x
- **编程语言**: TypeScript
- **状态管理**: 自定义Store模式
- **音频处理**: Web Audio API + WeChat Audio API
- **网络请求**: axios + 重试机制
- **本地存储**: WeChat Storage API + IndexedDB

### 后端API设计

#### 游戏相关API
```typescript
// 开始游戏
POST /api/game/start
{
  "mode": "classic|challenge|ugc",
  "dialect_region": "shanghai", 
  "difficulty": "medium"
}

// 提交答题
POST /api/game/submit
{
  "session_id": "game_session_123",
  "question_id": "q001",
  "answer": "A",
  "answer_time": 4.2
}

// 结束游戏
POST /api/game/end  
{
  "session_id": "game_session_123",
  "final_score": 245,
  "total_time": 205
}
```

#### UGC相关API
```typescript
// 上传音频
POST /api/ugc/upload-audio
FormData: {
  "audio_file": File,
  "dialect_region": "shanghai",
  "duration": 8.5
}

// 创建题目
POST /api/ugc/create-question
{
  "audio_url": "https://cdn.example.com/audio/ugc/001.mp3",
  "question_text": "这句话是什么意思？",
  "options": [...],
  "explanation": "解析内容",
  "dialect_region": "shanghai",
  "difficulty": "medium"
}
```

### 缓存策略
```typescript
class GameCacheManager {
    // 音频缓存
    audioCacheStrategy = {
        maxSize: 10 * 1024 * 1024, // 10MB
        maxCount: 50,
        ttl: 30 * 60 * 1000, // 30分钟
        strategy: 'LRU'
    };
    
    // 题目缓存
    questionCacheStrategy = {
        maxSize: 5 * 1024 * 1024, // 5MB
        maxCount: 200,
        ttl: 60 * 60 * 1000, // 1小时
        strategy: 'LFU'
    };
    
    // 用户数据缓存
    userDataCacheStrategy = {
        maxSize: 1 * 1024 * 1024, // 1MB
        ttl: 24 * 60 * 60 * 1000, // 24小时
        strategy: 'TTL'
    };
}
```

---

## ✅ 验收标准

### 功能验收标准
1. **游戏流程完整性**: 用户可以完整完成一局游戏
2. **音频播放稳定性**: 音频加载成功率 >99%
3. **计分准确性**: 积分计算100%准确
4. **分享功能可用性**: 分享成功率 >95%
5. **UGC功能完整性**: 用户可以完整创建并提交内容

### 性能验收标准
1. **加载性能**: 游戏启动时间 <3秒
2. **响应性能**: 界面操作响应时间 <100ms
3. **音频性能**: 音频加载时间 <2秒
4. **内存使用**: 峰值内存使用 <100MB
5. **网络容错**: 弱网络环境下可用性 >90%

### 质量验收标准
1. **兼容性**: 支持微信7.0+版本
2. **稳定性**: 崩溃率 <0.1%
3. **安全性**: 无数据泄露风险
4. **用户体验**: SUS可用性评分 >80分
5. **内容质量**: UGC内容通过率 >85%

---

**文档结束**

> 本文档详细定义了游戏核心功能的所有技术规格和业务逻辑，为开发团队提供了完整的实现指南。所有设计均与技术架构的成本约束和性能要求保持一致。