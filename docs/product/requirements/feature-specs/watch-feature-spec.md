# 围观功能详细规格说明

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2024-07-31
- **负责人**: product-manager-agent
- **状态**: 技术架构对接版
- **优先级**: P0 (核心功能)

---

## 🎯 功能概述

### 产品定位
围观功能是《家乡话猜猜猜》的革命性社交创新，将传统的"看热闹"需求转化为积极的互动娱乐体验，通过实时围观、弹幕互动、预测游戏等功能，打造全新的社交游戏模式。

### 核心价值
1. **满足看热闹心理**: 用户天然的围观欲望得到满足
2. **增强社交粘性**: 围观用户与玩家、围观者之间的多重社交关系
3. **提升用户留存**: 围观用户7日留存率预计比普通用户高30%+
4. **创造变现机会**: 围观增值服务贡献15%收入
5. **降低获客成本**: 围观分享提升K-Factor到1.8+

### 技术架构支撑
基于已完成的混合通信策略技术架构：
- **WebSocket + SSE + 智能轮询**: 保证实时性和成本控制
- **智能房间合并算法**: 相似房间自动合并，降低连接成本
- **分层消息同步**: 关键数据实时，非关键数据延迟
- **高并发支持**: 单房间支持6000+并发围观用户

---

## 🏗️ 功能架构设计

### 系统架构图

```
围观功能系统架构
├── 前端层 (Frontend)
│   ├── 围观发现组件 (WatchDiscovery)
│   ├── 围观界面组件 (WatchRoom)
│   ├── 弹幕系统组件 (BarrageSystem)
│   ├── 预测游戏组件 (PredictionGame)
│   └── 互动控制组件 (InteractionControl)
│
├── 业务逻辑层 (Business Logic)
│   ├── 围观房间管理 (WatchRoomManager)
│   ├── 实时通信协调 (RealtimeCommunication)
│   ├── 预测算法引擎 (PredictionEngine)
│   ├── 积分奖励系统 (RewardSystem)
│   └── 内容审核过滤 (ContentFilter)
│
├── 数据层 (Data Layer)
│   ├── 围观状态缓存 (Redis)
│   ├── 用户关系数据 (MySQL)
│   ├── 围观行为分析 (Analytics)
│   └── 弹幕内容存储 (MongoDB)
│
└── 基础设施层 (Infrastructure)
    ├── WebSocket服务集群
    ├── SSE推送服务
    ├── 负载均衡器
    └── CDN内容分发
```

### 核心流程设计

#### 1. 围观发现流程
```
用户进入应用 → 检测好友游戏状态 → 显示围观入口 → 用户点击围观 → 进入围观房间
```

#### 2. 围观参与流程
```
进入围观房间 → 建立实时连接 → 同步游戏状态 → 参与互动 (弹幕/预测/点赞) → 获得积分奖励
```

#### 3. 围观退出流程
```
用户主动退出 → 保存围观数据 → 断开连接 → 更新房间状态 → 结算积分奖励
```

---

## 🎮 核心功能规格

### 1. 围观发现系统

#### 1.1 围观入口展示
**功能描述**: 在多个位置展示围观机会，吸引用户参与

**展示位置**:
- **首页动态流**: "小明正在挑战四川话，已连对8题！[围观]"
- **排行榜页面**: "3位好友正在游戏中 [点击围观]"
- **好友列表**: 好友头像显示"游戏中"状态
- **热门推荐**: "精彩对局推荐 - 高手PK进行中"

**技术实现**:
```typescript
interface WatchOpportunity {
  playerId: string;
  playerName: string;
  playerLevel: number;
  currentStreak: number;
  dialectType: string;
  watcherCount: number;
  isHotMatch: boolean;
  gameProgress: number; // 0-100%
}

class WatchDiscoveryService {
  async getWatchOpportunities(userId: string): Promise<WatchOpportunity[]> {
    // 获取好友正在游戏的状态
    // 获取热门围观推荐
    // 按优先级排序返回
  }
}
```

#### 1.2 智能推荐算法
**推荐权重公式**:
```
推荐分数 = 好友关系权重(40%) + 玩家水平权重(25%) + 游戏精彩度(20%) + 围观热度(15%)
```

**具体指标**:
- **好友关系**: 直接好友(1.0) > 共同好友(0.7) > 同地域(0.5) > 陌生人(0.3)
- **玩家水平**: 排行榜前100(1.0) > 前1000(0.8) > 前10000(0.6) > 其他(0.4)
- **游戏精彩度**: 连击数*0.1 + 正确率*0.5 + 用时加成*0.4
- **围观热度**: 当前围观人数/100 (最大值1.0)

### 2. 围观房间系统

#### 2.1 房间状态管理
**房间生命周期**:
```
创建房间 → 等待玩家 → 游戏进行中 → 游戏结束 → 房间销毁
```

**房间数据结构**:
```typescript
interface WatchRoom {
  roomId: string;
  playerId: string;
  playerInfo: PlayerInfo;
  gameState: GameState;
  watchers: WatcherInfo[];
  barrages: BarrageMessage[];
  predictions: PredictionData[];
  createTime: number;
  lastUpdateTime: number;
}

interface WatcherInfo {
  userId: string;
  userName: string;
  avatar: string;
  joinTime: number;
  isVIP: boolean;
  totalLikes: number;
  totalPredictions: number;
}
```

#### 2.2 智能房间合并
**合并策略**:
- **相似度计算**: 方言类型(50%) + 玩家水平(30%) + 围观偏好(20%)
- **合并阈值**: 相似度>0.8 且两房间围观人数<1000
- **合并过程**: 
  1. 识别可合并房间对
  2. 选择主房间(围观人数更多)
  3. 迁移次房间用户到主房间
  4. 合并弹幕和预测数据
  5. 释放次房间资源

**技术实现**:
```typescript
class RoomMergeService {
  async findMergeablePairs(): Promise<RoomPair[]> {
    // 计算所有房间的相似度矩阵
    // 找出相似度高且符合合并条件的房间对
  }
  
  async mergeRooms(primaryRoom: string, secondaryRoom: string): Promise<void> {
    // 执行房间合并逻辑
    // 通知所有用户房间变更
    // 更新连接状态
  }
}
```

### 3. 实时通信系统

#### 3.1 混合通信策略
**通信选择算法**:
```typescript
class CommunicationSelector {
  selectCommunicationMethod(userInfo: UserInfo, roomInfo: RoomInfo): CommunicationType {
    if (userInfo.networkQuality === 'high' && roomInfo.interactionLevel === 'high') {
      return 'WebSocket';
    }
    if (userInfo.deviceType === 'mobile' && userInfo.batteryLevel < 20) {
      return 'Polling';
    }
    return 'SSE';
  }
}
```

**消息分层设计**:
- **P0 - 关键消息**: 游戏状态变更、答题结果 (WebSocket实时推送)
- **P1 - 重要消息**: 弹幕消息、预测结果 (SSE推送，延迟<1秒)
- **P2 - 一般消息**: 围观人数变化、点赞统计 (智能轮询，延迟<3秒)

#### 3.2 连接管理
**连接池管理**:
```typescript
class ConnectionManager {
  private connections: Map<string, Connection> = new Map();
  private maxConnectionsPerRoom: number = 6000;
  
  async addWatcher(roomId: string, userId: string): Promise<Connection> {
    if (this.getConnectionCount(roomId) >= this.maxConnectionsPerRoom) {
      // 触发房间分流或合并逻辑
      await this.handleRoomOverflow(roomId);
    }
    
    const connection = await this.createConnection(roomId, userId);
    this.connections.set(`${roomId}:${userId}`, connection);
    return connection;
  }
}
```

### 4. 弹幕互动系统

#### 4.1 弹幕发送与显示
**弹幕类型**:
- **文字弹幕**: 普通文字评论 (最多30字符)
- **表情弹幕**: 快捷表情包 (预设50种表情)
- **预设快评**: 快捷评论 ("666", "笑死了", "这个我会")
- **VIP弹幕**: 付费用户专属样式弹幕

**弹幕数据结构**:
```typescript
interface BarrageMessage {
  messageId: string;
  userId: string;
  userName: string;
  content: string;
  type: 'text' | 'emoji' | 'preset' | 'vip';
  timestamp: number;
  likes: number;
  isVisible: boolean;
}
```

#### 4.2 弹幕过滤与审核
**三级过滤机制**:
1. **客户端预过滤**: 敏感词库基础过滤，实时反馈
2. **服务端AI过滤**: 语义理解，情感分析，风险评估
3. **人工审核**: 争议内容人工复审，用户举报处理

**技术实现**:
```typescript
class BarrageFilter {
  async filterMessage(message: BarrageMessage): Promise<FilterResult> {
    // Level 1: 敏感词过滤
    const sensitiveWordResult = await this.checkSensitiveWords(message.content);
    
    // Level 2: AI内容理解
    const aiResult = await this.aiContentAnalysis(message.content);
    
    // Level 3: 用户行为分析
    const behaviorResult = await this.analyzUserBehavior(message.userId);
    
    return this.combineResults([sensitiveWordResult, aiResult, behaviorResult]);
  }
}
```

### 5. 预测游戏系统

#### 5.1 预测机制设计
**预测流程**:
1. **音频播放**: 围观者同步听到题目
2. **预测窗口**: 音频结束后10秒内可预测
3. **选项分布**: 实时显示各选项预测人数比例
4. **结果揭晓**: 玩家答题后立即显示预测结果
5. **积分结算**: 预测正确者获得积分奖励

**预测数据结构**:
```typescript
interface PredictionData {
  questionId: string;
  predictions: {
    optionA: number;
    optionB: number; 
    optionC: number;
    optionD: number;
  };
  userPredictions: Map<string, string>; // userId -> selectedOption
  correctAnswer?: string;
  isSettled: boolean;
}
```

#### 5.2 积分奖励算法
**基础积分规则**:
- **预测正确**: 10积分 (基础奖励)
- **连续预测**: 连续N次正确，积分 = 10 * (1 + N * 0.1)
- **少数派奖励**: 预测正确且选择人数<20%，额外+5积分
- **快速预测**: 在3秒内完成预测，额外+2积分

**技术实现**:
```typescript
class PredictionRewardCalculator {
  calculateReward(userId: string, prediction: UserPrediction, result: PredictionResult): number {
    let reward = 0;
    
    if (prediction.selectedOption === result.correctAnswer) {
      reward += 10; // 基础奖励
      
      // 连击奖励
      const streak = this.getUserPredictionStreak(userId);
      reward += Math.floor(streak * 0.1 * 10);
      
      // 少数派奖励
      const optionRatio = result.getOptionRatio(prediction.selectedOption);
      if (optionRatio < 0.2) {
        reward += 5;
      }
      
      // 快速预测奖励
      if (prediction.responseTime < 3000) {
        reward += 2;
      }
    }
    
    return reward;
  }
}
```

### 6. 点赞助威系统

#### 6.1 点赞机制
**点赞类型**:
- **普通点赞**: 基础点赞，成本1积分
- **超级点赞**: 特效点赞，成本5积分，效果更显著
- **连击点赞**: 连续点赞有特殊动画效果

**点赞效果**:
- **玩家积分加成**: 每100个点赞增加5%积分加成，最多20%
- **围观者奖励**: 点赞者获得0.5积分/次
- **视觉反馈**: 点赞有专门的动画效果和音效

#### 6.2 助威系统
**助威数据统计**:
```typescript
interface CheerData {
  playerId: string;
  totalLikes: number;
  likesByTime: Map<number, number>; // timestamp -> likes count
  topSupporters: WatcherInfo[];
  cheerEffect: number; // 0.0-0.2 (积分加成比例)
}
```

---

## 📊 性能要求与约束

### 技术性能指标

#### 延迟要求
- **围观房间进入**: <3秒完成连接建立
- **弹幕消息延迟**: <1秒到达其他围观者
- **预测结果同步**: <500ms完成结果分发
- **点赞反馈延迟**: <200ms显示视觉反馈

#### 并发支持
- **单房间并发**: 支持6000+同时围观用户
- **系统总并发**: 支持10万+同时围观用户
- **消息吞吐量**: 10万条/秒弹幕处理能力
- **数据同步频率**: 关键数据100ms同步，一般数据3秒同步

#### 成本控制
- **围观功能成本增加**: 10万DAU增加$50-80/月服务器成本
- **带宽优化**: 通过智能合并减少30%连接数
- **存储优化**: 弹幕数据7天清理，预测数据30天归档

### 兼容性要求

#### 设备兼容
- **iOS**: 支持iOS 12+，iPhone 6s+
- **Android**: 支持Android 8+，2GB RAM+
- **微信版本**: 微信7.0+小游戏环境

#### 网络适配
- **4G网络**: 正常围观体验，弹幕延迟<2秒
- **3G网络**: 基础围观功能，弹幕延迟<5秒
- **WiFi**: 完整围观体验，所有功能正常

---

## 🔒 安全与合规

### 内容安全
1. **敏感词过滤**: 基于最新敏感词库实时过滤
2. **AI内容审核**: 语义理解识别违规内容
3. **用户举报**: 快速举报处理机制
4. **违规处罚**: 分级处罚制度 (警告→限制→封禁)

### 数据安全
1. **传输加密**: 所有实时通信使用WSS/HTTPS
2. **数据脱敏**: 日志中脱敏用户敏感信息
3. **访问控制**: 房间权限验证，防止未授权访问
4. **数据备份**: 关键围观数据异地备份

### 隐私保护
1. **匿名围观**: 支持匿名模式围观
2. **数据清理**: 围观行为数据定期清理
3. **权限控制**: 用户可设置围观权限级别
4. **信息保护**: 不泄露用户敏感位置信息

---

## 🎯 成功指标与验收标准

### 核心指标
- **围观转化率**: ≥25% (看到围观入口→实际围观)
- **围观时长**: ≥8分钟 (单次围观平均时长)
- **围观互动率**: ≥60% (围观用户中的弹幕/预测参与率)
- **围观留存提升**: +30% (围观用户7日留存vs普通用户)

### 技术指标
- **系统稳定性**: 99.9%可用性，故障恢复时间<5分钟
- **性能达标**: 满足所有延迟和并发要求
- **成本控制**: 围观功能成本增幅控制在预算范围内
- **安全合规**: 100%内容过关，零重大安全事件

### 验收标准
1. **功能完整性**: 所有规格定义的功能正常工作
2. **用户体验**: UI/UX符合设计规范，操作流畅直观
3. **性能达标**: 满足所有性能指标要求
4. **安全合规**: 通过安全审计和合规检查
5. **数据准确**: 积分计算、统计数据100%准确

---

## 🚀 实施计划

### 开发阶段划分

#### Phase 1: 基础围观功能 (2周)
- 围观发现与房间进入
- 基础围观界面
- 简单弹幕系统
- 基础数据统计

#### Phase 2: 互动功能完善 (2周)  
- 预测游戏系统
- 点赞助威功能
- 积分奖励机制
- 围观分享功能

#### Phase 3: 高级功能 (1周)
- 智能房间合并
- VIP围观特权
- 数据分析后台
- 性能优化

#### Phase 4: 测试与优化 (1周)
- 全功能测试
- 性能压力测试
- 安全测试
- 用户体验优化

### 关键里程碑
- **Week 2**: 基础围观功能可用，支持100+并发
- **Week 4**: 完整围观体验，支持1000+并发  
- **Week 5**: 高级功能完善，支持6000+并发
- **Week 6**: 生产环境部署，正式上线

---

**文档结束**

> 本规格说明基于已完成的围观功能技术架构，提供了完整的产品功能定义和技术实现指导。所有设计都经过成本效益分析，确保在预算约束下实现最大产品价值。