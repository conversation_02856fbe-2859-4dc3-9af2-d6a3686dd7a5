# 预测游戏系统详细规格说明

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2024-07-31
- **负责人**: product-manager-agent
- **状态**: 技术架构对接版
- **优先级**: P1 (重要功能)

---

## 🎯 系统概述

### 功能定位
预测游戏系统是围观功能的核心互动机制，让围观用户能够预测玩家的答题选择，通过群体智慧、竞技乐趣和积分奖励，将被动围观转化为主动参与的游戏体验。

### 核心价值
1. **参与感增强**: 围观者从被动观看转为主动参与
2. **智力挑战**: 提供围观用户独特的预测挑战乐趣
3. **社交互动**: 围观者之间形成预测竞争和讨论
4. **群体智慧**: 利用集体预测提供学习价值
5. **留存提升**: 预测游戏增强围观用户粘性和留存

### 设计理念
- **实时性**: 预测过程与玩家游戏完全同步
- **公平性**: 所有围观用户享有平等的预测机会
- **智能化**: 基于历史数据提供预测辅助和趋势分析
- **激励性**: 通过积分奖励和成就系统激发参与热情

---

## 🏗️ 系统架构

### 技术架构设计

```
预测游戏系统架构
├── 前端展示层 (Presentation Layer)  
│   ├── 预测界面组件 (PredictionUI)
│   ├── 统计展示组件 (StatsDisplay)
│   ├── 结果动画组件 (ResultAnimation)
│   └── 排行榜组件 (LeaderboardUI)
│
├── 业务逻辑层 (Business Logic Layer)
│   ├── 预测管理器 (PredictionManager)
│   ├── 奖励计算引擎 (RewardCalculator)
│   ├── 统计分析引擎 (AnalyticsEngine)
│   ├── 排行榜服务 (LeaderboardService)
│   └── 作弊检测服务 (AntiCheatService)
│
├── 数据服务层 (Data Service Layer)
│   ├── 预测数据管理 (PredictionDataManager)
│   ├── 用户行为追踪 (UserBehaviorTracker)
│   ├── 实时统计缓存 (RealtimeStatsCache)
│   └── 历史数据归档 (HistoricalDataArchiver)
│
├── 数据存储层 (Data Storage Layer)
│   ├── 实时预测缓存 (Redis Cluster)
│   ├── 预测历史数据 (MongoDB)
│   ├── 用户统计数据 (MySQL)
│   └── 分析结果缓存 (Redis)
│
└── 基础设施层 (Infrastructure Layer)
    ├── 实时消息推送 (WebSocket/SSE)
    ├── 分布式锁服务 (Redis Lock)
    ├── 定时任务调度 (Cron Jobs)
    └── 监控告警系统 (Monitoring)
```

### 数据流架构

#### 预测提交流程
```
围观用户选择 → 客户端验证 → 提交到服务器 → 权限验证 → 数据存储 → 实时统计更新 → 推送给其他用户
```

#### 结果结算流程
```
玩家答题完成 → 获取正确答案 → 批量结算预测 → 计算积分奖励 → 更新用户数据 → 推送结算结果
```

---

## 🎮 核心功能设计

### 1. 预测参与机制

#### 1.1 预测时间窗口
**时间设计**:
- **预测开始**: 题目音频播放完毕后立即开始
- **预测时长**: 10秒倒计时窗口
- **预测截止**: 玩家答题前1秒截止预测
- **特殊情况**: 玩家快速答题时自动截止预测

**技术实现**:
```typescript
interface PredictionWindow {
  questionId: string;
  startTime: number;      // 预测开始时间戳
  endTime: number;        // 预测结束时间戳
  duration: number;       // 预测窗口时长 (默认10秒)
  isActive: boolean;      // 当前是否可预测
  participantCount: number; // 参与预测人数
}

class PredictionWindowManager {
  async startPredictionWindow(questionId: string, audioLength: number): Promise<PredictionWindow> {
    const now = Date.now();
    const window: PredictionWindow = {
      questionId,
      startTime: now + audioLength * 1000, // 音频播放完毕后开始
      endTime: now + audioLength * 1000 + 10000, // 10秒预测时间
      duration: 10000,
      isActive: true,
      participantCount: 0
    };
    
    // 设置自动关闭定时器
    setTimeout(() => this.closePredictionWindow(questionId), 10000);
    
    return window;
  }
}
```

#### 1.2 预测选项展示
**界面设计**:
- **选项显示**: 与玩家看到的4个选项完全一致
- **实时统计**: 显示每个选项的预测人数和百分比
- **视觉反馈**: 用户选择后高亮显示，禁止重复选择
- **倒计时**: 清晰的倒计时显示，增强紧迫感

**数据结构**:
```typescript
interface PredictionOptions {
  questionId: string;
  options: {
    A: { text: string; count: number; percentage: number };
    B: { text: string; count: number; percentage: number };
    C: { text: string; count: number; percentage: number };
    D: { text: string; count: number; percentage: number };
  };
  totalPredictions: number;
  userSelection?: 'A' | 'B' | 'C' | 'D';
  isLocked: boolean; // 用户是否已选择
}
```

### 2. 群体智慧展示

#### 2.1 实时统计更新
**统计算法**:
```typescript
class PredictionStatsManager {
  private stats: Map<string, PredictionStats> = new Map();
  
  async updateStats(questionId: string, option: string): Promise<PredictionStats> {
    const current = this.stats.get(questionId) || this.createEmptyStats(questionId);
    
    // 更新选项计数
    current.options[option].count++;
    current.totalCount++;
    
    // 重新计算百分比
    this.recalculatePercentages(current);
    
    // 保存统计数据
    this.stats.set(questionId, current);
    
    // 推送给所有围观者
    await this.broadcastStats(questionId, current);
    
    return current;
  }
  
  private recalculatePercentages(stats: PredictionStats): void {
    for (const option of ['A', 'B', 'C', 'D']) {
      stats.options[option].percentage = stats.totalCount > 0 
        ? (stats.options[option].count / stats.totalCount) * 100
        : 0;
    }
  }
}
```

#### 2.2 趋势分析展示
**趋势指标**:
- **热门选项**: 当前预测人数最多的选项
- **冷门选项**: 预测人数最少但可能正确的选项
- **变化趋势**: 各选项预测比例的变化方向
- **群体信心**: 基于预测分布计算的群体信心指数

**信心指数计算**:
```typescript
class ConfidenceCalculator {
  calculateGroupConfidence(stats: PredictionStats): number {
    const percentages = Object.values(stats.options).map(opt => opt.percentage);
    const maxPercentage = Math.max(...percentages);
    
    // 计算分布的集中度
    const entropy = this.calculateEntropy(percentages);
    const maxEntropy = Math.log2(4); // 4选项的最大熵
    
    // 信心指数 = (最高选项比例 * 0.6) + ((1 - 归一化熵) * 0.4)
    const confidence = (maxPercentage / 100 * 0.6) + ((1 - entropy / maxEntropy) * 0.4);
    
    return Math.round(confidence * 100);
  }
  
  private calculateEntropy(percentages: number[]): number {
    return percentages.reduce((entropy, p) => {
      if (p > 0) {
        const prob = p / 100;
        entropy -= prob * Math.log2(prob);
      }
      return entropy;
    }, 0);
  }
}
```

### 3. 预测结算系统

#### 3.1 答案揭晓机制
**结算触发**:
- **玩家答题**: 玩家选择答案后立即触发结算
- **超时结算**: 玩家答题超时后按错误答案结算
- **特殊情况**: 游戏中断时的结算处理

**结算流程**:
```typescript
class PredictionSettlementService {
  async settlePrediction(questionId: string, correctAnswer: string): Promise<SettlementResult> {
    // 1. 获取所有预测数据
    const predictions = await this.getPredictions(questionId);
    
    // 2. 批量计算奖励
    const rewards = await this.calculateRewards(predictions, correctAnswer);
    
    // 3. 更新用户积分
    await this.updateUserPoints(rewards);
    
    // 4. 更新统计数据
    await this.updateUserStats(predictions, correctAnswer);
    
    // 5. 推送结果给用户
    await this.broadcastResults(questionId, correctAnswer, rewards);
    
    return {
      questionId,
      correctAnswer,
      totalPredictions: predictions.length,
      correctPredictions: rewards.filter(r => r.isCorrect).length,
      totalRewardsDistributed: rewards.reduce((sum, r) => sum + r.points, 0)
    };
  }
}
```

#### 3.2 积分奖励计算
**奖励规则**:
```typescript
interface RewardRules {
  // 基础奖励
  correctPrediction: 10;           // 预测正确基础积分
  
  // 连击奖励  
  streakMultiplier: 0.1;          // 连击系数 (每连击1次 +10%)
  maxStreakBonus: 2.0;            // 最大连击倍数
  
  // 少数派奖励
  minorityThreshold: 0.2;         // 少数派阈值 (20%)
  minorityBonus: 5;               // 少数派追加积分
  
  // 速度奖励
  fastPredictionThreshold: 3000;  // 快速预测阈值 (3秒)
  speedBonus: 2;                  // 速度奖励积分
  
  // VIP奖励
  vipMultiplier: 1.5;             // VIP用户奖励倍数
}

class RewardCalculator {
  calculateReward(prediction: UserPrediction, context: PredictionContext): RewardResult {
    if (prediction.selectedOption !== context.correctAnswer) {
      return { points: 0, isCorrect: false, bonuses: [] };
    }
    
    let totalPoints = this.rules.correctPrediction;
    const bonuses: string[] = [];
    
    // 连击奖励
    const streakBonus = Math.min(
      prediction.userStreak * this.rules.streakMultiplier,
      this.rules.maxStreakBonus
    );
    totalPoints = Math.floor(totalPoints * (1 + streakBonus));
    if (streakBonus > 0) {
      bonuses.push(`连击奖励 x${(1 + streakBonus).toFixed(1)}`);
    }
    
    // 少数派奖励
    const optionPercentage = context.optionStats[prediction.selectedOption].percentage / 100;
    if (optionPercentage < this.rules.minorityThreshold) {
      totalPoints += this.rules.minorityBonus;
      bonuses.push(`少数派奖励 +${this.rules.minorityBonus}`);
    }
    
    // 速度奖励
    if (prediction.responseTime < this.rules.fastPredictionThreshold) {
      totalPoints += this.rules.speedBonus;
      bonuses.push(`快速预测 +${this.rules.speedBonus}`);
    }
    
    // VIP奖励
    if (prediction.isVIP) {
      totalPoints = Math.floor(totalPoints * this.rules.vipMultiplier);
      bonuses.push(`VIP奖励 x${this.rules.vipMultiplier}`);
    }
    
    return {
      points: totalPoints,
      isCorrect: true,
      bonuses: bonuses
    };
  }
}
```

### 4. 预测辅助功能

#### 4.1 智能提示系统
**提示类型**:
- **历史准确率**: 显示当前用户对该方言类型的历史预测准确率
- **选项热度**: 基于历史数据显示各选项的被选择概率
- **难度提示**: 根据题目难度调整预测策略建议
- **玩家倾向**: 基于被围观玩家的答题习惯给出倾向性提示

**VIP预测辅助**:
```typescript
class PredictionAssistant {
  async generateHints(questionId: string, userId: string): Promise<PredictionHints> {
    const user = await this.getUserProfile(userId);
    const question = await this.getQuestionData(questionId);
    const playerHistory = await this.getPlayerHistory(question.playerId);
    
    return {
      // 用户历史准确率
      userAccuracy: await this.calculateUserAccuracy(userId, question.dialectType),
      
      // 选项推荐
      recommendedOption: await this.analyzeHistoricalPatterns(question),
      
      // 玩家倾向分析
      playerTendency: await this.analyzePlayerBehavior(playerHistory, question),
      
      // 群体智慧参考
      crowdWisdom: await this.getCurrentPredictionTrend(questionId),
      
      // 信心评级
      confidenceLevel: await this.calculateHintConfidence(question, user)
    };
  }
}
```

#### 4.2 预测道具系统
**道具类型**:
```typescript
interface PredictionTools {
  // 基础道具
  hintsCard: {
    name: '提示卡';
    effect: '显示详细的预测分析和建议';
    cost: 50; // 积分成本
    cooldown: 300; // 冷却时间(秒)
  };
  
  // 高级道具  
  crystalBall: {
    name: '水晶球';
    effect: '查看过去5题的预测准确率趋势';
    cost: 100;
    cooldown: 600;
  };
  
  // 社交道具
  crowdConsult: {
    name: '群众咨询';
    effect: '查看好友和高手玩家的预测选择';
    cost: 80;
    cooldown: 450;
  };
  
  // VIP专属道具
  expertAnalysis: {
    name: '专家分析';
    effect: 'AI分析提供最优预测建议';
    cost: 200;
    cooldown: 900;
    requiresVIP: true;
  };
}
```

---

## 📊 数据统计与分析

### 1. 用户预测行为追踪

#### 1.1 个人预测档案
**数据收集**:
```typescript
interface UserPredictionProfile {
  userId: string;
  
  // 基础统计
  totalPredictions: number;         // 总预测次数
  correctPredictions: number;       // 正确预测次数
  overallAccuracy: number;          // 总体准确率
  
  // 方言分类准确率
  dialectAccuracy: Map<string, {
    total: number;
    correct: number;
    accuracy: number;
  }>;
  
  // 选项偏好分析
  optionPreference: {
    A: number; B: number; C: number; D: number;
  };
  
  // 时间行为分析
  averageResponseTime: number;      // 平均响应时间
  peakPerformanceTime: number[];    // 表现最佳时间段
  
  // 连击记录
  currentStreak: number;            // 当前连击
  longestStreak: number;           // 最长连击
  streakHistory: number[];         // 连击历史
  
  // 社交数据
  followedPredictors: string[];    // 关注的预测高手
  predictionInfluence: number;     // 预测影响力指数
}
```

#### 1.2 群体行为分析
**群体智慧指标**:
```typescript
interface CrowdWisdomMetrics {
  // 整体表现
  totalParticipants: number;        // 参与总人数
  averageAccuracy: number;          // 群体平均准确率
  wisdomIndex: number;             // 群体智慧指数
  
  // 分布分析
  accuracyDistribution: {           // 准确率分布
    '90-100%': number;
    '70-89%': number;
    '50-69%': number;
    '<50%': number;
  };
  
  // 预测模式
  consensusRate: number;           // 共识率 (多数选择正确率)
  contrarian: number;              // 逆向预测成功率
  
  // 难题识别
  difficultQuestions: {            // 难题标识
    questionId: string;
    accuracy: number;
    entropy: number; // 预测分散度
  }[];
}
```

### 2. 实时数据处理

#### 2.1 流式数据处理
```typescript
class PredictionStreamProcessor {
  private eventStream: EventEmitter = new EventEmitter();
  
  async processPredictionEvent(event: PredictionEvent): Promise<void> {
    // 实时更新统计
    await this.updateRealtimeStats(event);
    
    // 触发相关计算
    this.eventStream.emit('prediction-received', event);
    
    // 检查是否需要触发特殊逻辑
    await this.checkSpecialConditions(event);
  }
  
  private async updateRealtimeStats(event: PredictionEvent): Promise<void> {
    const key = `prediction:${event.questionId}`;
    
    // 更新Redis中的实时统计
    await this.redis.pipeline()
      .hincrby(key, `option_${event.selectedOption}`, 1)
      .hincrby(key, 'total_count', 1)
      .expire(key, 3600) // 1小时过期
      .exec();
  }
}
```

#### 2.2 统计数据缓存策略
**缓存层设计**:
- **L1 - 内存缓存**: 当前题目的实时统计 (TTL: 题目结束)
- **L2 - Redis缓存**: 用户预测历史 (TTL: 24小时)  
- **L3 - 数据库**: 完整的预测记录 (永久存储)

```typescript
class PredictionCacheManager {
  // L1: 内存缓存
  private memoryCache: Map<string, PredictionStats> = new Map();
  
  async getStats(questionId: string): Promise<PredictionStats> {
    // 尝试从内存获取
    let stats = this.memoryCache.get(questionId);
    if (stats) return stats;
    
    // 尝试从Redis获取
    stats = await this.getFromRedis(questionId);
    if (stats) {
      this.memoryCache.set(questionId, stats);
      return stats;
    }
    
    // 从数据库重建
    stats = await this.rebuildFromDatabase(questionId);
    await this.setToRedis(questionId, stats);
    this.memoryCache.set(questionId, stats);
    
    return stats;
  }
}
```

---

## 🏆 排行榜与竞技系统

### 1. 多维度排行榜

#### 1.1 全局排行榜
**排行榜类型**:
```typescript
interface LeaderboardTypes {
  // 综合排行
  overall: {
    metric: '总积分 + 准确率加权';
    timeframe: '全时间';
    updateFrequency: '实时';
  };
  
  // 准确率排行
  accuracy: {
    metric: '预测准确率';
    minPredictions: 100; // 最少预测次数
    timeframe: '近30天';
    updateFrequency: '每小时';
  };
  
  // 连击排行
  streak: {
    metric: '最长连击记录';
    currentOnly: false; // 包含历史记录
    timeframe: '全时间';
    updateFrequency: '实时';
  };
  
  // 专项排行
  dialect: {
    metric: '各方言类型专项准确率';
    groupBy: 'dialectType';
    minPredictions: 50;
    timeframe: '近30天';
  };
}
```

#### 1.2 排行榜计算算法
```typescript
class LeaderboardCalculator {
  calculateOverallScore(user: UserPredictionProfile): number {
    const baseScore = user.totalPredictions * 10; // 基础参与分
    const accuracyBonus = user.overallAccuracy * user.totalPredictions * 20; // 准确率奖励
    const streakBonus = user.longestStreak * 50; // 连击奖励
    const recentPerformance = this.calculateRecentPerformance(user); // 近期表现
    
    return Math.round(
      baseScore * 0.2 +           // 参与度权重 20%
      accuracyBonus * 0.5 +       // 准确率权重 50%
      streakBonus * 0.2 +         // 连击权重 20%
      recentPerformance * 0.1     // 近期表现权重 10%
    );
  }
  
  private calculateRecentPerformance(user: UserPredictionProfile): number {
    // 计算近7天的表现加权分数
    const recentPredictions = this.getRecentPredictions(user.userId, 7);
    let weightedScore = 0;
    let totalWeight = 0;
    
    recentPredictions.forEach((prediction, index) => {
      const dayWeight = 7 - index; // 越近权重越高
      const score = prediction.isCorrect ? 100 : 0;
      weightedScore += score * dayWeight;
      totalWeight += dayWeight;
    });
    
    return totalWeight > 0 ? weightedScore / totalWeight : 0;
  }
}
```

### 2. 成就系统

#### 2.1 预测成就定义
```typescript
interface PredictionAchievements {
  // 基础成就
  firstBlood: {
    name: '初出茅庐';
    description: '完成首次预测';
    condition: { type: 'total_predictions', value: 1 };
    reward: { points: 10, badge: 'rookie' };
  };
  
  centurion: {
    name: '百发百中';
    description: '100次预测准确率达到80%以上';
    condition: { type: 'accuracy_with_min', accuracy: 0.8, minPredictions: 100 };
    reward: { points: 500, badge: 'sharpshooter', title: '神算子' };
  };
  
  // 连击成就
  streakMaster: {
    name: '连击大师';
    description: '达到20连击';
    condition: { type: 'streak', value: 20 };
    reward: { points: 200, badge: 'streak_master' };
  };
  
  // 专项成就
  dialectExpert: {
    name: '方言专家';
    description: '某种方言类型预测准确率90%以上(最少50次)';
    condition: { type: 'dialect_expertise', accuracy: 0.9, minPredictions: 50 };
    reward: { points: 300, badge: 'dialect_expert', title: '方言达人' };
  };
  
  // 社交成就
  crowdInfluencer: {
    name: '预测导师';
    description: '你的预测选择影响了1000+其他用户';
    condition: { type: 'influence_count', value: 1000 };
    reward: { points: 800, badge: 'influencer', title: '预测导师' };
  };
}
```

#### 2.2 成就检测系统
```typescript
class AchievementDetector {
  async checkAchievements(userId: string, event: PredictionEvent): Promise<Achievement[]> {
    const userProfile = await this.getUserProfile(userId);
    const unlockedAchievements: Achievement[] = [];
    
    for (const [achievementId, achievement] of Object.entries(this.achievements)) {
      // 检查用户是否已获得该成就
      if (await this.hasAchievement(userId, achievementId)) continue;
      
      // 检查是否满足解锁条件
      if (await this.checkCondition(userProfile, achievement.condition)) {
        await this.unlockAchievement(userId, achievementId);
        unlockedAchievements.push(achievement);
      }
    }
    
    return unlockedAchievements;
  }
  
  private async checkCondition(profile: UserPredictionProfile, condition: AchievementCondition): Promise<boolean> {
    switch (condition.type) {
      case 'total_predictions':
        return profile.totalPredictions >= condition.value;
        
      case 'accuracy_with_min':
        return profile.totalPredictions >= condition.minPredictions && 
               profile.overallAccuracy >= condition.accuracy;
               
      case 'streak':
        return profile.longestStreak >= condition.value;
        
      case 'dialect_expertise':
        return Array.from(profile.dialectAccuracy.values())
          .some(stat => stat.total >= condition.minPredictions && 
                       stat.accuracy >= condition.accuracy);
        
      default:
        return false;
    }
  }
}
```

---

## 🔒 反作弊与安全机制

### 1. 作弊检测系统

#### 1.1 行为模式分析
**异常行为检测**:
```typescript
class AntiCheatDetector {
  async analyzeUserBehavior(userId: string, prediction: PredictionEvent): Promise<CheatRiskAssessment> {
    const riskFactors: RiskFactor[] = [];
    
    // 1. 响应时间异常检测
    const avgResponseTime = await this.getAverageResponseTime(userId);
    if (prediction.responseTime < 500 || prediction.responseTime > avgResponseTime * 3) {
      riskFactors.push({
        type: 'unusual_response_time',
        severity: 'medium',
        description: `响应时间异常: ${prediction.responseTime}ms`
      });
    }
    
    // 2. 准确率异常检测
    const recentAccuracy = await this.getRecentAccuracy(userId, 20);
    if (recentAccuracy > 0.95 && prediction.isCorrect) {
      riskFactors.push({
        type: 'suspiciously_high_accuracy',
        severity: 'high', 
        description: `近期准确率过高: ${recentAccuracy * 100}%`
      });
    }
    
    // 3. 预测模式检测
    const patternRisk = await this.detectPredictionPatterns(userId);
    if (patternRisk.isRisky) {
      riskFactors.push({
        type: 'suspicious_pattern',
        severity: patternRisk.severity,
        description: patternRisk.description
      });
    }
    
    return {
      userId,
      riskLevel: this.calculateOverallRisk(riskFactors),
      riskFactors,
      recommendedAction: this.determineAction(riskFactors)
    };
  }
}
```

#### 1.2 多维度验证
**验证策略**:
- **设备指纹**: 检测同一设备多账号操作
- **网络分析**: 识别异常网络行为和代理使用
- **时间模式**: 分析预测时间分布的异常模式
- **社交关系**: 检测协同作弊的用户群体

### 2. 安全防护措施

#### 2.1 数据安全
```typescript
class PredictionSecurityManager {
  // 预测数据加密存储
  async encryptPredictionData(data: PredictionData): Promise<string> {
    const key = await this.getEncryptionKey();
    return this.cryptoService.encrypt(JSON.stringify(data), key);
  }
  
  // 防止预测数据泄露
  async validatePredictionIntegrity(prediction: UserPrediction): Promise<boolean> {
    // 验证时间戳
    const now = Date.now();
    if (Math.abs(now - prediction.timestamp) > 30000) { // 30秒容差
      return false;
    }
    
    // 验证用户权限
    if (!await this.hasValidPredictionPermission(prediction.userId)) {
      return false;
    }
    
    // 验证预测窗口
    const window = await this.getPredictionWindow(prediction.questionId);
    if (!window.isActive || now > window.endTime) {
      return false;
    }
    
    return true;
  }
}
```

#### 2.2 访问控制
**权限管理**:
- **预测权限**: 只有围观用户可以参与预测
- **频率限制**: 每个用户每题只能预测一次
- **时间窗口**: 严格控制预测时间窗口
- **黑名单机制**: 对违规用户进行预测权限限制

---

## 📈 性能优化与监控

### 1. 性能优化策略

#### 1.1 数据库优化
```typescript
// 预测数据索引设计
interface PredictionIndexes {
  // 复合索引
  user_question: ['userId', 'questionId'];           // 用户预测查询
  question_time: ['questionId', 'timestamp'];        // 题目时间查询
  user_accuracy: ['userId', 'isCorrect', 'timestamp']; // 用户准确率计算
  
  // 单字段索引
  timestamp: ['timestamp'];                          // 时间范围查询
  streak: ['userId', 'streakCount'];                // 连击排行
}

class PredictionQueryOptimizer {
  // 批量查询优化
  async batchGetUserAccuracy(userIds: string[]): Promise<Map<string, number>> {
    const query = `
      SELECT userId, 
             COUNT(*) as total,
             SUM(CASE WHEN isCorrect THEN 1 ELSE 0 END) as correct
      FROM predictions 
      WHERE userId IN (${userIds.map(() => '?').join(',')})
        AND timestamp > ?
      GROUP BY userId
    `;
    
    const results = await this.db.query(query, [...userIds, Date.now() - 30 * 24 * 60 * 60 * 1000]);
    
    return new Map(results.map(row => [
      row.userId, 
      row.total > 0 ? row.correct / row.total : 0
    ]));
  }
}
```

#### 1.2 缓存策略优化
```typescript
class PredictionCacheOptimizer {
  private cacheHierarchy = {
    L1: new Map<string, any>(),      // 内存缓存
    L2: 'redis',                     // Redis缓存
    L3: 'database'                   // 数据库
  };
  
  async optimizeCache(): Promise<void> {
    // 预热热点数据
    await this.preloadHotData();
    
    // 清理过期缓存
    await this.cleanupExpiredCache();
    
    // 优化缓存命中率
    await this.optimizeCacheHitRate();
  }
  
  private async preloadHotData(): Promise<void> {
    // 预加载活跃用户的预测历史
    const activeUsers = await this.getActiveUsers(100);
    for (const userId of activeUsers) {
      await this.getUserPredictionHistory(userId);
    }
    
    // 预加载当前活跃题目的统计数据
    const activeQuestions = await this.getActiveQuestions();
    for (const questionId of activeQuestions) {
      await this.getPredictionStats(questionId);
    }
  }
}
```

### 2. 监控与告警

#### 2.1 关键指标监控
```typescript
interface PredictionSystemMetrics {
  // 性能指标
  predictionLatency: number;        // 预测处理延迟
  settlementLatency: number;        // 结算处理延迟
  cacheHitRate: number;            // 缓存命中率
  databaseConnections: number;      // 数据库连接数
  
  // 业务指标
  activePredictors: number;         // 活跃预测用户数
  predictionsPerMinute: number;     // 每分钟预测数
  accuracyRate: number;            // 系统整体准确率
  rewardDistribution: number;       // 积分发放总量
  
  // 质量指标
  cheatDetectionRate: number;       // 作弊检测率
  falsePositiveRate: number;        // 误报率
  systemErrorRate: number;          // 系统错误率
}
```

#### 2.2 告警策略
```typescript
class PredictionAlertManager {
  private alertThresholds = {
    predictionLatency: 2000,         // 预测延迟超过2秒
    cacheHitRate: 0.8,              // 缓存命中率低于80%
    errorRate: 0.05,                // 错误率超过5%
    cheatDetectionSpike: 0.1,       // 作弊检测率超过10%
  };
  
  async checkAlerts(metrics: PredictionSystemMetrics): Promise<Alert[]> {
    const alerts: Alert[] = [];
    
    if (metrics.predictionLatency > this.alertThresholds.predictionLatency) {
      alerts.push({
        level: 'warning',
        type: 'performance',
        message: `预测系统延迟过高: ${metrics.predictionLatency}ms`,
        suggestions: ['检查数据库连接', '优化查询语句', '增加缓存层']
      });
    }
    
    if (metrics.cheatDetectionRate > this.alertThresholds.cheatDetectionSpike) {
      alerts.push({
        level: 'critical',
        type: 'security', 
        message: `作弊检测率异常升高: ${metrics.cheatDetectionRate * 100}%`,
        suggestions: ['检查作弊检测算法', '人工审核异常用户', '加强安全措施']
      });
    }
    
    return alerts;
  }
}
```

---

## 🎯 验收标准与测试计划

### 1. 功能验收标准

#### 1.1 核心功能验收
- [ ] **预测参与**: 围观用户可以在10秒窗口内正常预测
- [ ] **实时统计**: 预测统计实时更新，延迟<1秒
- [ ] **结算准确**: 积分奖励计算100%准确，无遗漏
- [ ] **排行榜**: 各类排行榜数据准确，更新及时

#### 1.2 性能验收标准
- [ ] **并发支持**: 单题支持1000+用户同时预测
- [ ] **响应延迟**: 预测提交响应<500ms
- [ ] **统计更新**: 实时统计更新延迟<1秒
- [ ] **结算速度**: 批量结算完成时间<3秒

#### 1.3 安全验收标准
- [ ] **作弊检测**: 95%+作弊行为检出率
- [ ] **数据安全**: 预测数据传输和存储加密
- [ ] **访问控制**: 严格的预测权限验证
- [ ] **防重复**: 100%防止重复预测

### 2. 测试计划

#### 2.1 单元测试
```typescript
describe('PredictionSystem', () => {
  test('计算积分奖励准确性', async () => {
    const calculator = new RewardCalculator();
    const prediction = createMockPrediction({ isCorrect: true, streak: 5 });
    const reward = calculator.calculateReward(prediction, mockContext);
    
    expect(reward.points).toBe(15); // 10 + 50% streak bonus
    expect(reward.bonuses).toContain('连击奖励 x1.5');
  });
  
  test('预测窗口时间控制', async () => {
    const windowManager = new PredictionWindowManager();
    const window = await windowManager.startPredictionWindow('q1', 3000);
    
    expect(window.duration).toBe(10000);
    expect(window.isActive).toBe(true);
    
    // 模拟时间过去
    jest.advanceTimersByTime(11000);
    expect(await windowManager.isWindowActive('q1')).toBe(false);
  });
});
```

#### 2.2 集成测试
```typescript
describe('PredictionIntegration', () => {
  test('完整预测流程', async () => {
    // 1. 开始预测窗口
    const window = await predictionService.startPrediction('q1');
    expect(window.isActive).toBe(true);
    
    // 2. 用户提交预测
    const result = await predictionService.submitPrediction('user1', 'q1', 'A');
    expect(result.success).toBe(true);
    
    // 3. 统计实时更新
    const stats = await predictionService.getStats('q1');
    expect(stats.options.A.count).toBe(1);
    
    // 4. 结算处理
    const settlement = await predictionService.settlePrediction('q1', 'A');
    expect(settlement.totalRewardsDistributed).toBeGreaterThan(0);
  });
});
```

#### 2.3 压力测试
```typescript
describe('PredictionLoadTest', () => {
  test('高并发预测提交', async () => {
    const concurrentUsers = 1000;
    const predictions = [];
    
    // 并发提交预测
    for (let i = 0; i < concurrentUsers; i++) {
      predictions.push(
        predictionService.submitPrediction(`user${i}`, 'q1', 'A')
      );
    }
    
    const results = await Promise.allSettled(predictions);
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    
    expect(successCount).toBeGreaterThan(concurrentUsers * 0.95); // 95%成功率
  });
});
```

---

## 🚀 部署与运维

### 1. 部署策略

#### 1.1 蓝绿部署
```yaml
# deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prediction-service
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: prediction-service
  template:
    metadata:
      labels:
        app: prediction-service
    spec:
      containers:
      - name: prediction-service
        image: prediction-service:latest
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

#### 1.2 数据库迁移
```typescript
class PredictionDatabaseMigration {
  async migrateToV2(): Promise<void> {
    await this.db.transaction(async (trx) => {
      // 1. 创建新表结构
      await this.createNewTables(trx);
      
      // 2. 迁移现有数据
      await this.migrateExistingData(trx);
      
      // 3. 更新索引
      await this.updateIndexes(trx);
      
      // 4. 验证数据完整性
      await this.validateMigration(trx);
    });
  }
}
```

### 2. 运维监控

#### 2.1 健康检查
```typescript
class PredictionHealthCheck {
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkExternalAPI(),
      this.checkSystemResources()
    ]);
    
    return {
      status: checks.every(c => c.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: ['database', 'redis', 'external_api', 'resources'][index],
        status: check.status,
        details: check.status === 'rejected' ? check.reason : 'OK'
      })),
      timestamp: Date.now()
    };
  }
}
```

#### 2.2 故障恢复
```typescript
class PredictionDisasterRecovery {
  async recoverFromFailure(failureType: FailureType): Promise<void> {
    switch (failureType) {
      case 'database_failure':
        await this.switchToSecondaryDatabase();
        await this.syncDataWhenPrimaryRecovers();
        break;
        
      case 'redis_failure':
        await this.fallbackToInMemoryCache();
        await this.rebuildCacheWhenRedisRecovers();
        break;
        
      case 'service_overload':
        await this.enableRateLimiting();
        await this.scaleUpInstances();
        break;
    }
  }
}
```

---

**文档结束**

> 本预测游戏系统规格说明书提供了完整的产品功能定义和技术实现方案，确保围观功能中预测游戏的高质量交付。所有设计都考虑了用户体验、系统性能、安全性和可维护性等关键要素。