# 弹幕系统详细规格说明

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2024-07-31
- **负责人**: product-manager-agent
- **状态**: 技术架构对接版
- **优先级**: P1 (重要功能)

---

## 🎯 系统概述

### 功能定位
弹幕系统是围观功能的核心互动组件，为围观用户提供实时评论交流平台，通过智能过滤、个性化展示、社交奖励等机制，打造健康活跃的围观社区氛围。

### 核心价值
1. **实时互动**: 围观者与围观者之间的即时交流
2. **情感表达**: 通过弹幕表达观点、情感和态度
3. **社区氛围**: 营造热烈的围观氛围，增强参与感
4. **用户粘性**: 优质弹幕互动提升用户留存和活跃度
5. **变现潜力**: 弹幕特效和VIP功能创造收入机会

### 设计原则
- **实时性**: 弹幕延迟<1秒，保证实时互动体验
- **智能化**: AI过滤确保内容健康，智能推荐优质弹幕
- **个性化**: 支持多样化弹幕样式和个性化设置
- **社交化**: 通过点赞、回复等机制促进用户间互动

---

## 🏗️ 系统架构

### 技术架构图

```
弹幕系统架构
├── 客户端层 (Client Layer)
│   ├── 弹幕输入组件 (BarrageInput)
│   ├── 弹幕显示组件 (BarrageDisplay) 
│   ├── 弹幕动画引擎 (AnimationEngine)
│   └── 弹幕设置面板 (SettingsPanel)
│
├── 网关层 (Gateway Layer)
│   ├── WebSocket连接管理
│   ├── 消息路由分发
│   ├── 连接负载均衡
│   └── 心跳检测机制
│
├── 业务逻辑层 (Business Layer)
│   ├── 弹幕内容管理 (ContentManager)
│   ├── 实时过滤引擎 (FilterEngine)
│   ├── 弹幕分发服务 (DistributionService)
│   ├── 用户权限控制 (PermissionControl)  
│   └── 统计分析服务 (AnalyticsService)
│
├── 数据层 (Data Layer)
│   ├── 弹幕实时缓存 (Redis Cluster)
│   ├── 弹幕持久存储 (MongoDB)
│   ├── 用户状态缓存 (Redis)
│   └── 敏感词库 (MySQL)
│
└── 基础设施层 (Infrastructure)
    ├── 消息队列 (RabbitMQ)
    ├── 分布式锁 (Redis Lock)
    ├── 监控告警 (Prometheus)
    └── 日志收集 (ELK Stack)
```

### 数据流设计

#### 弹幕发送流程
```
用户输入 → 客户端预检查 → 发送到服务器 → 内容过滤 → 权限验证 → 存储 → 分发给其他用户 → 客户端渲染
```

#### 弹幕接收流程  
```
服务器推送 → 客户端接收 → 消息验证 → 弹幕队列 → 动画渲染 → 屏幕显示
```

---

## 💬 弹幕类型与格式

### 1. 弹幕类型分类

#### 1.1 文字弹幕
**基础规格**:
- **字符限制**: 1-30个字符 (中文按2字符计算)
- **发送频率**: 普通用户3秒/条，VIP用户1秒/条
- **样式设置**: 字体、颜色、大小、透明度可调
- **特殊效果**: 滚动、静止、顶部、底部四种显示模式

**数据结构**:
```typescript
interface TextBarrage {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: number;
  style: {
    color: string;
    fontSize: number;
    fontFamily: string;
    opacity: number;
  };
  position: 'scroll' | 'top' | 'bottom' | 'static';
  isVIP: boolean;
  likes: number;
}
```

#### 1.2 表情弹幕
**表情库设计**:
- **基础表情**: 50种通用表情 (开心、惊讶、疑问等)
- **方言特色表情**: 各地方言特色表情包
- **季节表情**: 根据节日和季节更新的限时表情
- **VIP专属表情**: 付费用户专享的精美表情

**技术实现**:
```typescript
interface EmojiBarrage {
  id: string;
  userId: string;
  emojiId: string;
  timestamp: number;
  size: 'small' | 'medium' | 'large';
  animation: 'bounce' | 'rotate' | 'pulse' | 'none';
  isSpecial: boolean; // VIP或限时表情
}
```

#### 1.3 预设快评
**快评内容库**:
- **通用快评**: "666", "哈哈哈", "牛啊", "这个我会"
- **方言快评**: "巴适得很", "老铁没毛病", "雷死我了"
- **情境快评**: "太难了", "简单题", "高手如云", "学到了"
- **互动快评**: "同感", "赞同", "不同意", "+1"

#### 1.4 VIP特效弹幕
**特效类型**:
- **彩虹弹幕**: 彩色渐变文字效果
- **闪光弹幕**: 文字闪烁发光效果  
- **粗体弹幕**: 加粗显示，更显著
- **边框弹幕**: 带彩色边框的弹幕
- **动画弹幕**: 文字带动画效果

### 2. 弹幕样式系统

#### 2.1 样式配置
```typescript
interface BarrageStyle {
  // 文字样式
  fontFamily: string;    // 字体
  fontSize: number;      // 字号 (12-24px)
  fontWeight: 'normal' | 'bold';
  color: string;         // 文字颜色
  backgroundColor?: string; // 背景颜色
  
  // 边框样式
  borderWidth?: number;
  borderColor?: string;
  borderRadius?: number;
  
  // 动画效果
  animation?: {
    type: 'fade' | 'slide' | 'bounce' | 'pulse';
    duration: number;
    delay: number;
  };
  
  // 显示位置
  position: {
    type: 'scroll' | 'top' | 'bottom' | 'static';
    lane?: number; // 弹幕轨道
  };
}
```

#### 2.2 弹幕轨道管理
**轨道分配策略**:
- **滚动弹幕**: 自动分配到空闲轨道，避免重叠
- **顶部弹幕**: 固定在顶部3条轨道显示
- **底部弹幕**: 固定在底部3条轨道显示
- **VIP弹幕**: 优先占用最佳显示轨道

**技术实现**:
```typescript
class BarrageTrackManager {
  private tracks: Map<number, BarrageTrack> = new Map();
  private maxTracks: number = 10;
  
  assignTrack(barrage: Barrage): number {
    // 为弹幕分配最佳显示轨道
    for (let i = 0; i < this.maxTracks; i++) {
      const track = this.tracks.get(i);
      if (!track || track.canAcceptBarrage(barrage)) {
        return i;
      }
    }
    return 0; // 默认轨道
  }
}
```

---

## 🛡️ 内容过滤与审核

### 1. 三级过滤体系

#### 1.1 客户端预过滤 (Level 1)
**实时过滤功能**:
- **敏感词检测**: 基于本地敏感词库即时提示
- **长度检查**: 实时显示字符数量，超长自动截取
- **格式验证**: 检查特殊字符、表情符号合法性
- **频率控制**: 客户端限制发送频率，防止刷屏

**技术实现**:
```typescript
class ClientSideFilter {
  private sensitiveWords: Set<string> = new Set();
  
  validateInput(content: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      filteredContent: content
    };
    
    // 敏感词检测
    for (const word of this.sensitiveWords) {
      if (content.includes(word)) {
        result.errors.push(`包含敏感词: ${word}`);
        result.filteredContent = content.replace(word, '*'.repeat(word.length));
      }
    }
    
    // 长度检查
    if (content.length > 30) {
      result.errors.push('内容过长');
      result.isValid = false;
    }
    
    return result;
  }
}
```

#### 1.2 服务端AI过滤 (Level 2)
**AI模型集成**:
- **文本分类**: 识别广告、辱骂、政治敏感等内容
- **情感分析**: 检测负面情绪和恶意评论
- **语义理解**: 识别隐晦表达和变体敏感词
- **上下文分析**: 结合聊天历史判断内容意图

**过滤策略**:
```typescript
interface AIFilterResult {
  risk_level: 'safe' | 'warning' | 'danger';
  confidence: number; // 0-1置信度
  categories: string[]; // 违规类别
  suggestions: string[]; // 修改建议
}

class AIContentFilter {
  async analyzeContent(content: string, context: ChatContext): Promise<AIFilterResult> {
    // 调用AI模型进行内容分析
    const result = await this.aiModel.analyze({
      text: content,
      context: context,
      user_history: await this.getUserHistory(context.userId)
    });
    
    return {
      risk_level: result.risk_level,
      confidence: result.confidence,
      categories: result.detected_categories,
      suggestions: result.modification_suggestions
    };
  }
}
```

#### 1.3 人工审核 (Level 3)
**审核触发条件**:
- AI识别为高风险内容 (confidence > 0.8)
- 用户举报次数 > 3次
- 包含争议性关键词
- 新用户首次发言

**审核流程**:
```typescript
class HumanModerationService {
  async submitForReview(barrage: Barrage, reason: string): Promise<void> {
    const moderationTask: ModerationTask = {
      id: generateId(),
      barrageId: barrage.id,
      content: barrage.content,
      userId: barrage.userId,
      reason: reason,
      priority: this.calculatePriority(barrage, reason),
      createTime: Date.now(),
      status: 'pending'
    };
    
    await this.moderationQueue.push(moderationTask);
    await this.notifyModerators(moderationTask);
  }
}
```

### 2. 违规处罚机制

#### 2.1 处罚等级
**分级处罚制度**:
- **轻微违规**: 删除弹幕，口头警告
- **一般违规**: 限制发言10分钟，积分扣除
- **严重违规**: 限制发言24小时，围观功能限制
- **恶意违规**: 永久封禁围观功能

#### 2.2 用户信誉系统
```typescript
interface UserReputation {
  userId: string;
  score: number; // 0-100信誉分
  level: 'excellent' | 'good' | 'normal' | 'poor' | 'banned';
  violations: ViolationRecord[];
  lastUpdateTime: number;
}

class ReputationManager {
  updateReputation(userId: string, violation: ViolationRecord): void {
    const reputation = this.getUserReputation(userId);
    reputation.score -= this.calculatePenalty(violation);
    reputation.violations.push(violation);
    
    // 根据信誉分调整用户权限
    this.adjustUserPermissions(userId, reputation);
  }
}
```

---

## 🎨 弹幕显示与动画

### 1. 渲染引擎

#### 1.1 Canvas渲染系统
**渲染架构**:
```typescript
class BarrageRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private animationFrame: number;
  private barrages: ActiveBarrage[] = [];
  
  render(): void {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    for (const barrage of this.barrages) {
      this.renderBarrage(barrage);
      this.updateBarragePosition(barrage);
    }
    
    // 清理已完成的弹幕
    this.barrages = this.barrages.filter(b => !b.isComplete);
    
    this.animationFrame = requestAnimationFrame(() => this.render());
  }
  
  private renderBarrage(barrage: ActiveBarrage): void {
    this.ctx.save();
    
    // 设置样式
    this.ctx.font = `${barrage.style.fontSize}px ${barrage.style.fontFamily}`;
    this.ctx.fillStyle = barrage.style.color;
    this.ctx.globalAlpha = barrage.style.opacity;
    
    // 绘制弹幕
    this.ctx.fillText(barrage.content, barrage.x, barrage.y);
    
    this.ctx.restore();
  }
}
```

#### 1.2 性能优化
**优化策略**:
- **对象池**: 复用弹幕对象，减少GC压力
- **视窗裁剪**: 只渲染可见区域的弹幕
- **帧率控制**: 根据设备性能动态调整帧率
- **批量渲染**: 合并相同样式的弹幕批量渲染

### 2. 动画效果

#### 2.1 动画类型
**滚动动画**:
```typescript
class ScrollAnimation {
  update(barrage: ActiveBarrage, deltaTime: number): void {
    barrage.x -= barrage.speed * deltaTime;
    
    if (barrage.x + barrage.width < 0) {
      barrage.isComplete = true;
    }
  }
}
```

**淡入淡出动画**:
```typescript
class FadeAnimation {
  update(barrage: ActiveBarrage, deltaTime: number): void {
    const progress = (Date.now() - barrage.startTime) / barrage.duration;
    
    if (progress < 0.2) {
      // 淡入阶段
      barrage.style.opacity = progress / 0.2;
    } else if (progress > 0.8) {
      // 淡出阶段
      barrage.style.opacity = (1 - progress) / 0.2;
    } else {
      // 完全显示阶段
      barrage.style.opacity = 1;
    }
    
    if (progress >= 1) {
      barrage.isComplete = true;
    }
  }
}
```

#### 2.2 碰撞检测
**轨道碰撞检测**:
```typescript
class CollisionDetector {
  checkCollision(newBarrage: ActiveBarrage, existingBarrages: ActiveBarrage[]): boolean {
    for (const existing of existingBarrages) {
      if (existing.track === newBarrage.track) {
        // 检查水平重叠
        const gap = 20; // 弹幕间距
        if (existing.x + existing.width + gap > newBarrage.x) {
          return true;
        }
      }
    }
    return false;
  }
}
```

---

## 📊 数据统计与分析

### 1. 实时统计

#### 1.1 弹幕统计指标
```typescript
interface BarrageMetrics {
  // 基础指标
  totalCount: number;        // 弹幕总数
  activeUsers: number;       // 活跃发言用户数
  averageLength: number;     // 平均弹幕长度
  
  // 质量指标
  likeRate: number;         // 点赞率
  reportRate: number;       // 举报率
  filteredRate: number;     // 过滤率
  
  // 时间分布
  peakTime: number;         // 弹幕高峰时间
  averageInterval: number;  // 平均发送间隔
  
  // 内容分析
  emotionScore: number;     // 情感分数 (-1到1)
  topKeywords: string[];    // 热词统计
  languageDistribution: Map<string, number>; // 语言分布
}
```

#### 1.2 用户行为分析
```typescript
interface UserBarrageBehavior {
  userId: string;
  
  // 发送行为
  totalSent: number;        // 发送总数
  averagePerSession: number; // 单次会话平均发送数
  favoriteTime: number[];   // 偏好发送时间段
  
  // 互动行为
  likesGiven: number;       // 给出点赞数
  likesReceived: number;    // 收到点赞数
  repliesCount: number;     // 回复数量
  
  // 质量指标
  approvalRate: number;     // 通过审核率
  popularityScore: number;  // 受欢迎度分数
  influenceIndex: number;   // 影响力指数
}
```

### 2. 数据存储策略

#### 2.1 分层存储
**存储架构**:
- **热数据 (Redis)**: 最近1小时的弹幕，支持实时查询
- **温数据 (MongoDB)**: 最近7天的弹幕，支持历史回放
- **冷数据 (对象存储)**: 7天以上的弹幕，压缩存储用于分析

#### 2.2 数据清理策略
```typescript
class DataCleanupService {
  async cleanupBarrages(): Promise<void> {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const oneWeek = 7 * 24 * 60 * 60 * 1000;
    
    // 移动热数据到温数据
    const hotCutoff = now - oneHour;
    await this.moveHotToWarm(hotCutoff);
    
    // 归档温数据到冷存储
    const warmCutoff = now - oneWeek;
    await this.archiveWarmToCold(warmCutoff);
    
    // 删除过期数据
    const deleteCutoff = now - 30 * 24 * 60 * 60 * 1000; // 30天
    await this.deleteExpiredData(deleteCutoff);
  }
}
```

---

## 🎁 社交功能与奖励

### 1. 弹幕互动功能

#### 1.1 点赞系统
**点赞机制**:
- **点赞权限**: 所有围观用户都可以给弹幕点赞
- **点赞限制**: 每个用户对同一条弹幕只能点赞一次
- **点赞奖励**: 被点赞的弹幕作者获得1积分/赞
- **点赞效果**: 高赞弹幕在弹幕流中更突出显示

#### 1.2 回复系统
**回复功能**:
- **@回复**: 支持@用户名回复特定弹幕
- **线程回复**: 形成弹幕对话线程
- **回复通知**: 被回复用户收到通知提醒
- **回复统计**: 统计用户回复活跃度

### 2. 积分奖励机制

#### 2.1 积分获取规则
```typescript
interface BarrageRewardRules {
  // 基础奖励
  sendBarrage: 1;           // 发送弹幕
  receiveFirstLike: 2;      // 首次被点赞
  receiveAdditionalLike: 1; // 后续点赞
  
  // 质量奖励
  popularBarrage: 5;        // 获得10+点赞
  featuredBarrage: 10;      // 被官方精选
  
  // 互动奖励
  replyToOthers: 1;         // 回复他人
  startConversation: 3;     // 引发讨论
  
  // 连续活动奖励
  consecutiveDays: 5;       // 连续活跃天数奖励
}
```

#### 2.2 特殊成就系统
```typescript
interface BarrageAchievement {
  id: string;
  name: string;
  description: string;
  condition: AchievementCondition;
  reward: {
    points: number;
    badge?: string;
    title?: string;
  };
}

const barrageAchievements: BarrageAchievement[] = [
  {
    id: 'barrage_master',
    name: '弹幕达人',
    description: '累计发送1000条弹幕',
    condition: { type: 'total_sent', value: 1000 },
    reward: { points: 100, badge: '弹幕达人', title: '话痨' }
  },
  {
    id: 'like_magnet',
    name: '点赞磁铁', 
    description: '单条弹幕获得100+点赞',
    condition: { type: 'single_barrage_likes', value: 100 },
    reward: { points: 50, badge: '受欢迎' }
  }
];
```

---

## 🔧 技术实现细节

### 1. 实时通信

#### 1.1 WebSocket连接管理
```typescript
class BarrageWebSocketManager {
  private connections: Map<string, WebSocket> = new Map();
  private rooms: Map<string, Set<string>> = new Map();
  
  joinRoom(userId: string, roomId: string, ws: WebSocket): void {
    this.connections.set(userId, ws);
    
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set());
    }
    this.rooms.get(roomId)!.add(userId);
    
    ws.on('message', (data) => this.handleMessage(userId, roomId, data));
    ws.on('close', () => this.leaveRoom(userId, roomId));
  }
  
  broadcast(roomId: string, message: BarrageMessage): void {
    const users = this.rooms.get(roomId);
    if (!users) return;
    
    for (const userId of users) {
      const ws = this.connections.get(userId);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    }
  }
}
```

#### 1.2 消息队列处理
```typescript
class BarrageMessageQueue {
  private queue: BarrageMessage[] = [];
  private processing: boolean = false;
  
  async enqueue(message: BarrageMessage): Promise<void> {
    this.queue.push(message);
    
    if (!this.processing) {
      this.processing = true;
      await this.processQueue();
      this.processing = false;
    }
  }
  
  private async processQueue(): Promise<void> {
    while (this.queue.length > 0) {
      const message = this.queue.shift()!;
      
      try {
        // 内容过滤
        const filterResult = await this.filterService.filter(message);
        if (!filterResult.passed) continue;
        
        // 存储消息
        await this.storage.save(message);
        
        // 分发消息
        await this.distribute(message);
        
      } catch (error) {
        console.error('处理弹幕消息失败:', error);
      }
    }
  }
}
```

### 2. 性能优化

#### 2.1 弹幕流控制
```typescript
class BarrageFlowController {
  private maxBarragesPerSecond: number = 50;
  private currentSecondCount: number = 0;
  private lastSecond: number = 0;
  
  shouldAcceptBarrage(): boolean {
    const currentSecond = Math.floor(Date.now() / 1000);
    
    if (currentSecond !== this.lastSecond) {
      this.currentSecondCount = 0;
      this.lastSecond = currentSecond;
    }
    
    if (this.currentSecondCount >= this.maxBarragesPerSecond) {
      return false;
    }
    
    this.currentSecondCount++;
    return true;
  }
}
```

#### 2.2 内存优化
```typescript
class BarrageMemoryManager {
  private maxMemoryBarrages: number = 1000;
  private barragePool: Barrage[] = [];
  
  getBarrageFromPool(): Barrage {
    if (this.barragePool.length > 0) {
      return this.barragePool.pop()!;
    }
    return new Barrage();
  }
  
  returnBarrageToPool(barrage: Barrage): void {
    barrage.reset();
    
    if (this.barragePool.length < this.maxMemoryBarrages) {
      this.barragePool.push(barrage);
    }
  }
}
```

---

## 📈 监控与运维

### 1. 系统监控

#### 1.1 关键指标监控
```typescript
interface BarrageSystemMetrics {
  // 性能指标
  messageLatency: number;      // 消息延迟 (ms)
  throughput: number;          // 吞吐量 (msg/s)
  connectionCount: number;     // 连接数
  memoryUsage: number;         // 内存使用 (MB)
  
  // 业务指标
  activeUsers: number;         // 活跃用户数
  messagesPerMinute: number;   // 每分钟消息数
  filterRate: number;          // 过滤率 (%)
  errorRate: number;           // 错误率 (%)
}
```

#### 1.2 告警策略
```typescript
class BarrageAlertManager {
  private thresholds = {
    messageLatency: 2000,      // 消息延迟超过2秒
    errorRate: 0.05,           // 错误率超过5%
    filterRate: 0.3,           // 过滤率超过30%
    memoryUsage: 1000,         // 内存使用超过1GB
  };
  
  checkAlerts(metrics: BarrageSystemMetrics): Alert[] {
    const alerts: Alert[] = [];
    
    if (metrics.messageLatency > this.thresholds.messageLatency) {
      alerts.push({
        level: 'warning',
        message: `弹幕延迟过高: ${metrics.messageLatency}ms`
      });
    }
    
    if (metrics.errorRate > this.thresholds.errorRate) {
      alerts.push({
        level: 'error',
        message: `系统错误率过高: ${metrics.errorRate * 100}%`
      });
    }
    
    return alerts;
  }
}
```

### 2. 故障处理

#### 2.1 降级策略
- **弹幕功能降级**: 在系统压力过大时，临时禁用特效弹幕
- **连接数限制**: 单房间连接数超过阈值时，拒绝新连接
- **消息限流**: 高峰期限制用户发送频率
- **缓存降级**: Redis不可用时，使用本地缓存

#### 2.2 故障恢复
```typescript
class BarrageRecoveryService {
  async recover(): Promise<void> {
    try {
      // 重建连接
      await this.reconnectUsers();
      
      // 恢复消息队列
      await this.restoreMessageQueue();
      
      // 同步数据状态
      await this.syncDataState();
      
      console.log('弹幕系统恢复完成');
    } catch (error) {
      console.error('弹幕系统恢复失败:', error);
      throw error;
    }
  }
}
```

---

## 🎯 验收标准与测试

### 1. 功能测试

#### 1.1 基础功能测试
- [ ] 弹幕发送功能正常，延迟<1秒
- [ ] 弹幕显示正常，动画流畅
- [ ] 内容过滤准确，敏感词100%拦截
- [ ] 积分奖励正确，计算无误差

#### 1.2 并发测试
- [ ] 单房间支持1000+并发弹幕
- [ ] 系统总体支持10000+并发连接
- [ ] 高并发下消息不丢失
- [ ] 内存使用稳定，无泄漏

#### 1.3 异常测试
- [ ] 网络断开自动重连
- [ ] 服务器重启数据不丢失
- [ ] 恶意用户攻击防护有效
- [ ] 系统过载正常降级

### 2. 性能标准

- **消息延迟**: 平均<1秒，95%分位<2秒
- **系统吞吐**: 支持10万条/分钟弹幕处理
- **内存使用**: 单实例<1GB内存占用
- **错误率**: <1%系统错误率
- **可用性**: 99.9%服务可用性

---

**文档结束**

> 本弹幕系统规格说明书详细定义了围观功能中弹幕系统的完整技术实现方案，涵盖了从用户交互到后端处理的所有环节，确保系统的可用性、扩展性和用户体验。