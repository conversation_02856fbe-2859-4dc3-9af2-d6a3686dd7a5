# 围观功能实时架构设计

## 🎯 设计目标

### 核心设计原则
- **超低成本实时**: 在$300/月预算内支持围观功能
- **智能降级**: 高负载时自动降级保证核心功能
- **弹性扩展**: 支持从1K到100K并发围观用户
- **用户体验**: <2秒延迟，流畅的围观交互

### 技术选型
- **实时通信**: WebSocket Polling混合策略
- **消息队列**: 腾讯云CMQ + Redis Stream
- **房间管理**: Redis集群 + 智能合并算法
- **成本控制**: 分层同步 + 智能限流

## 🏗️ 围观系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  微信小游戏端    │    │   围观网关层     │    │  围观服务集群    │
│  Watch Client   │◄──►│  WebSocket Pool │◄──►│ Room Management │
└─────────────────┘    │  Smart Polling  │    │ Message Broker  │
         │              └─────────────────┘    └─────────────────┘
         ▼                        │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  围观UI组件     │    │   消息分发层     │    │   状态存储层     │
│  Barrage System │    │  Priority Queue │    │ Redis Cluster   │
│  Prediction UI  │    │  Rate Limiting  │    │ Hot/Cold Data   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 围观房间生命周期
```
围观房间状态流转:

创建房间 → 等待玩家 → 游戏进行中 → 结算状态 → 房间关闭
    │          │           │           │          │
    ▼          ▼           ▼           ▼          ▼
  预热缓存   围观者加入   实时同步    预测结算   清理资源
  设置权限   弹幕系统    状态广播    积分发放   回收内存
```

## 🔄 实时通信策略

### 混合通信模式
```typescript
// 智能通信策略选择
class WatchCommunicationManager {
    private connectionStrategies = {
        // WebSocket策略 - 高实时性，高成本
        websocket: {
            latency: 100,        // 100ms延迟
            cost_per_connection: 0.001,  // 每连接每小时成本
            concurrent_limit: 1000,      // 并发连接限制
            use_conditions: ['premium_users', 'peak_moments']
        },
        
        // Polling策略 - 低成本，适中延迟  
        polling: {
            latency: 500,        // 500ms延迟
            cost_per_request: 0.0001,    // 每请求成本
            interval: 1000,      // 1秒轮询间隔
            use_conditions: ['normal_users', 'low_activity']
        },
        
        // Server-Sent Events - 单向推送，低成本
        sse: {
            latency: 200,        // 200ms延迟
            cost_per_connection: 0.0005,  // 每连接每小时成本
            concurrent_limit: 5000,       // 更高并发限制
            use_conditions: ['read_only_watchers']
        }
    };
    
    // 智能策略选择
    selectCommunicationStrategy(context: WatchContext): CommunicationStrategy {
        const {
            userType,
            roomActivity,
            currentLoad,
            costBudget
        } = context;
        
        // 成本优先策略
        if (costBudget.remaining < 0.2) {
            return this.connectionStrategies.polling;
        }
        
        // 用户体验优先策略
        if (userType === 'premium' || roomActivity === 'high') {
            return this.connectionStrategies.websocket;
        }
        
        // 平衡策略
        if (currentLoad < 0.7) {
            return this.connectionStrategies.sse;
        }
        
        return this.connectionStrategies.polling;
    }
    
    // 动态策略切换
    async adaptCommunicationStrategy(roomId: string) {
        const room = await this.getRoomInfo(roomId);
        const newStrategy = this.selectCommunicationStrategy(room.context);
        
        if (newStrategy !== room.currentStrategy) {
            await this.migrateRoomStrategy(roomId, newStrategy);
            logger.info(`Room ${roomId} migrated to ${newStrategy.type}`);
        }
    }
}
```

### 分层消息同步
```python
class LayeredMessageSync:
    def __init__(self):
        # 消息优先级定义
        self.message_priorities = {
            'critical': {
                'types': ['game_start', 'game_end', 'player_join'],
                'latency_target': 100,  # 100ms目标延迟
                'delivery_guarantee': 'exactly_once',
                'cost_multiplier': 3.0
            },
            'high': {
                'types': ['answer_submit', 'score_update'],
                'latency_target': 200,  # 200ms目标延迟
                'delivery_guarantee': 'at_least_once',
                'cost_multiplier': 2.0
            },
            'medium': {
                'types': ['barrage_message', 'prediction_update'],
                'latency_target': 500,  # 500ms目标延迟
                'delivery_guarantee': 'at_most_once',
                'cost_multiplier': 1.0
            },
            'low': {
                'types': ['heartbeat', 'presence_update'],
                'latency_target': 2000,  # 2秒目标延迟
                'delivery_guarantee': 'best_effort',
                'cost_multiplier': 0.5
            }
        }
    
    async def process_message(self, message: dict, room_id: str):
        """分层处理消息"""
        priority = self.get_message_priority(message['type'])
        
        # 根据优先级选择处理策略
        if priority == 'critical':
            await self.process_critical_message(message, room_id)
        elif priority == 'high':
            await self.process_high_priority_message(message, room_id)
        elif priority == 'medium':
            await self.batch_process_message(message, room_id)
        else:
            await self.defer_process_message(message, room_id)
    
    async def process_critical_message(self, message: dict, room_id: str):
        """关键消息立即处理"""
        # 直接推送给所有围观者
        watchers = await self.get_active_watchers(room_id)
        
        # 并行推送减少延迟
        tasks = []
        for watcher in watchers:
            task = self.send_direct_message(watcher.connection_id, message)
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        
        # 记录关键消息
        await self.log_critical_message(message, room_id)
    
    async def batch_process_message(self, message: dict, room_id: str):
        """中等优先级消息批量处理"""
        # 添加到批处理队列
        await self.add_to_batch_queue(room_id, message)
        
        # 检查是否达到批处理条件
        queue_size = await self.get_batch_queue_size(room_id)
        if queue_size >= self.BATCH_SIZE or self.should_flush_batch(room_id):
            await self.flush_batch_queue(room_id)
    
    async def intelligent_message_routing(self, room_id: str, message: dict):
        """智能消息路由"""
        room_info = await self.get_room_info(room_id)
        
        # 根据房间负载调整策略
        if room_info.watcher_count > 1000:
            # 高负载房间：使用采样策略
            sample_rate = min(0.1, 100 / room_info.watcher_count)
            await self.sample_and_broadcast(room_id, message, sample_rate)
        elif room_info.watcher_count > 100:
            # 中负载房间：使用批量推送
            await self.batch_broadcast(room_id, message)
        else:
            # 低负载房间：直接推送
            await self.direct_broadcast(room_id, message)
```

## 🏠 围观房间管理

### 智能房间合并策略
```python
class SmartRoomManager:
    def __init__(self):
        self.room_merge_thresholds = {
            'low_activity': {
                'watcher_count': 5,      # 少于5人围观
                'message_rate': 1,       # 每分钟少于1条消息
                'merge_target': 'dialect_lobby'  # 合并到方言大厅
            },
            'similar_content': {
                'dialect_similarity': 0.8,  # 方言相似度80%以上
                'difficulty_diff': 1,       # 难度差异1级以内
                'merge_target': 'grouped_watch'  # 合并到群组围观
            },
            'resource_optimization': {
                'cost_per_watcher': 0.01,   # 每围观者成本超过0.01元
                'cpu_usage': 0.8,           # CPU使用率超过80%
                'merge_target': 'shared_room'  # 合并到共享房间
            }
        }
    
    async def evaluate_room_merge_opportunities(self):
        """评估房间合并机会"""
        active_rooms = await self.get_active_rooms()
        merge_candidates = []
        
        for room in active_rooms:
            merge_score = await self.calculate_merge_score(room)
            if merge_score > 0.7:  # 70%以上合并收益
                candidates = await self.find_merge_candidates(room)
                if candidates:
                    merge_candidates.append({
                        'source_room': room,
                        'target_candidates': candidates,
                        'merge_score': merge_score
                    })
        
        # 执行合并操作
        for candidate in merge_candidates:
            await self.execute_room_merge(candidate)
    
    async def calculate_merge_score(self, room: RoomInfo) -> float:
        """计算房间合并收益分数"""
        score = 0.0
        
        # 成本收益评估
        cost_saving = await self.estimate_cost_saving(room)
        score += min(cost_saving / 0.1, 0.4)  # 最高0.4分
        
        # 用户体验影响评估
        ux_impact = await self.estimate_ux_impact(room)
        score -= ux_impact * 0.3  # 负面影响扣分
        
        # 技术资源优化评估
        resource_optimization = await self.estimate_resource_optimization(room)
        score += min(resource_optimization, 0.3)  # 最高0.3分
        
        return max(0.0, min(1.0, score))
    
    async def execute_room_merge(self, merge_plan: dict):
        """执行房间合并"""
        source_room = merge_plan['source_room']
        target_room = merge_plan['target_candidates'][0]
        
        try:
            # 1. 通知用户房间合并
            await self.notify_room_merge(source_room.id, target_room.id)
            
            # 2. 迁移围观者连接
            await self.migrate_watchers(source_room.id, target_room.id)
            
            # 3. 合并房间状态
            await self.merge_room_state(source_room, target_room)
            
            # 4. 清理源房间资源
            await self.cleanup_source_room(source_room.id)
            
            logger.info(f"Room merge completed: {source_room.id} -> {target_room.id}")
            
        except Exception as e:
            logger.error(f"Room merge failed: {e}")
            await self.rollback_room_merge(source_room.id, target_room.id)
    
    async def dynamic_room_scaling(self, room_id: str):
        """动态房间伸缩"""
        room_metrics = await self.get_room_metrics(room_id)
        
        # 扩容条件
        if (room_metrics.watcher_count > 500 and 
            room_metrics.message_rate > 100 and
            room_metrics.latency > 1000):
            
            await self.scale_up_room(room_id)
        
        # 缩容条件
        elif (room_metrics.watcher_count < 10 and 
              room_metrics.message_rate < 1 and
              room_metrics.idle_time > 300):
            
            await self.scale_down_room(room_id)
```

### 围观数据热冷分离
```python
class WatchDataManager:
    def __init__(self):
        self.storage_tiers = {
            'hot': {
                'storage': 'redis_cluster',
                'ttl': 300,  # 5分钟
                'access_pattern': 'high_frequency',
                'cost_per_gb': 10.0
            },
            'warm': {
                'storage': 'redis_single',
                'ttl': 3600,  # 1小时
                'access_pattern': 'medium_frequency',
                'cost_per_gb': 3.0
            },
            'cold': {
                'storage': 'mysql',
                'ttl': 86400,  # 24小时
                'access_pattern': 'low_frequency',
                'cost_per_gb': 0.5
            },
            'archive': {
                'storage': 'cos',
                'ttl': float('inf'),  # 永久
                'access_pattern': 'rare_access',
                'cost_per_gb': 0.1
            }
        }
    
    async def intelligent_data_tiering(self):
        """智能数据分层"""
        # 分析数据访问模式
        access_patterns = await self.analyze_access_patterns()
        
        for data_key, pattern in access_patterns.items():
            current_tier = await self.get_data_tier(data_key)
            optimal_tier = self.determine_optimal_tier(pattern)
            
            if current_tier != optimal_tier:
                await self.migrate_data_tier(data_key, current_tier, optimal_tier)
    
    def determine_optimal_tier(self, access_pattern: dict) -> str:
        """确定最优存储层级"""
        access_frequency = access_pattern['frequency']
        data_size = access_pattern['size']
        age = access_pattern['age']
        
        # 成本效益计算
        if access_frequency > 10:  # 每分钟访问超过10次
            return 'hot'
        elif access_frequency > 1:  # 每分钟访问超过1次
            return 'warm'
        elif age < 86400:  # 24小时内的数据
            return 'cold'
        else:
            return 'archive'
    
    async def preload_hot_data(self, room_id: str):
        """预加载热点数据"""
        # 预测即将需要的数据
        predicted_data = await self.predict_data_needs(room_id)
        
        # 并行预加载
        preload_tasks = []
        for data_key in predicted_data:
            task = self.preload_data_to_hot_tier(data_key)
            preload_tasks.append(task)
        
        await asyncio.gather(*preload_tasks)
    
    async def cleanup_expired_data(self):
        """清理过期数据"""
        # 批量清理各层级过期数据
        cleanup_tasks = []
        
        for tier_name, tier_config in self.storage_tiers.items():
            if tier_config['ttl'] != float('inf'):
                task = self.cleanup_tier_expired_data(tier_name, tier_config['ttl'])
                cleanup_tasks.append(task)
        
        await asyncio.gather(*cleanup_tasks)
```

## 💬 弹幕系统设计

### 弹幕过滤和限流
```typescript
class BarrageSystem {
    private filterConfig = {
        // 内容过滤配置
        content_filter: {
            max_length: 50,           // 最大长度50字符
            sensitive_words: [],      // 敏感词库
            emoji_limit: 5,           // 最多5个表情
            repeat_limit: 3           // 最多3个重复字符
        },
        
        // 频率限制配置
        rate_limit: {
            messages_per_minute: 10,  // 每用户每分钟10条
            messages_per_second: 2,   // 每用户每秒2条
            burst_allowance: 3        // 突发允许3条
        },
        
        // 质量评估配置
        quality_filter: {
            min_quality_score: 0.3,   // 最低质量分数
            auto_approve_score: 0.8,  // 自动通过分数
            manual_review_score: 0.5  // 人工审核分数
        }
    };
    
    async processBarrageMessage(message: BarrageMessage, roomId: string): Promise<ProcessResult> {
        // 1. 基础过滤
        const basicFilter = await this.basicContentFilter(message);
        if (!basicFilter.passed) {
            return { rejected: true, reason: basicFilter.reason };
        }
        
        // 2. 频率限制检查
        const rateCheck = await this.checkRateLimit(message.userId, roomId);
        if (!rateCheck.allowed) {
            return { rejected: true, reason: 'rate_limit_exceeded' };
        }
        
        // 3. 智能质量评估
        const qualityScore = await this.assessMessageQuality(message);
        
        if (qualityScore >= this.filterConfig.quality_filter.auto_approve_score) {
            // 高质量消息：直接通过
            return await this.approveAndBroadcast(message, roomId);
        } else if (qualityScore >= this.filterConfig.quality_filter.manual_review_score) {
            // 中等质量：人工审核队列
            return await this.queueForManualReview(message, roomId);
        } else {
            // 低质量：延迟发送或降级处理
            return await this.processLowQualityMessage(message, roomId, qualityScore);
        }
    }
    
    private async assessMessageQuality(message: BarrageMessage): Promise<number> {
        let score = 0.5; // 基础分数
        
        // 内容多样性评分
        const diversity = this.calculateContentDiversity(message.content);
        score += diversity * 0.2;
        
        // 用户历史行为评分
        const userHistory = await this.getUserBarrageHistory(message.userId);
        const historyScore = this.calculateHistoryScore(userHistory);
        score += historyScore * 0.3;
        
        // 实时互动价值评分
        const interactionValue = await this.calculateInteractionValue(message, message.roomId);
        score += interactionValue * 0.3;
        
        // 时机适当性评分
        const timingScore = await this.calculateTimingScore(message, message.roomId);
        score += timingScore * 0.2;
        
        return Math.max(0, Math.min(1, score));
    }
    
    // 智能弹幕聚合
    async intelligentBarrageAggregation(roomId: string) {
        const recentMessages = await this.getRecentBarrages(roomId, 60); // 最近1分钟
        
        // 相似内容聚合
        const clusters = this.clusterSimilarMessages(recentMessages);
        
        for (const cluster of clusters) {
            if (cluster.messages.length >= 3) {
                // 3条以上相似消息，生成聚合弹幕
                const aggregatedMessage = this.createAggregatedBarrage(cluster);
                await this.broadcastAggregatedBarrage(roomId, aggregatedMessage);
                
                // 标记原始消息为已聚合
                await this.markMessagesAsAggregated(cluster.messages);
            }
        }
    }
    
    // 弹幕热度计算
    private calculateBarrageHeat(message: BarrageMessage, roomContext: RoomContext): number {
        let heat = 0;
        
        // 基于游戏时机的热度
        if (roomContext.gamePhase === 'question_playing') {
            heat += 0.8; // 题目播放时热度高
        } else if (roomContext.gamePhase === 'answer_revealing') {
            heat += 1.0; // 答案揭晓时热度最高
        }
        
        // 基于内容类型的热度
        if (message.type === 'prediction') {
            heat += 0.6; // 预测类弹幕热度较高
        } else if (message.type === 'cheer') {
            heat += 0.4; // 加油类弹幕热度中等
        }
        
        // 基于用户等级的热度加权
        const userLevel = message.userLevel || 1;
        heat *= (1 + userLevel * 0.1);
        
        return Math.min(2.0, heat); // 最高热度2.0
    }
}
```

## 🎯 预测游戏系统

### 预测逻辑和积分计算
```typescript
class PredictionGameSystem {
    private predictionConfig = {
        // 预测时间窗口
        prediction_window: {
            question_start: 10,      // 题目开始后10秒内可预测
            answer_before: 3,        // 答案提交前3秒停止预测
            result_reveal: 5         // 结果揭晓后5秒内显示预测结果
        },
        
        // 积分规则
        scoring: {
            correct_prediction: 10,   // 预测正确基础分
            early_prediction: 5,      // 早期预测奖励分
            streak_bonus: 2,          // 连续预测奖励倍数
            difficulty_multiplier: {  // 难度系数
                easy: 1.0,
                medium: 1.5,
                hard: 2.0
            }
        },
        
        // 预测池配置
        prediction_pool: {
            max_participants: 1000,   // 单题最大预测人数
            min_pool_size: 5,         // 最小预测池大小
            result_delay: 2           // 结果延迟显示时间(秒)
        }
    };
    
    async processPrediction(prediction: PredictionInput, roomId: string): Promise<PredictionResult> {
        // 1. 验证预测时间窗口
        const gameState = await this.getGameState(roomId);
        const timeCheck = this.validatePredictionTiming(prediction, gameState);
        
        if (!timeCheck.valid) {
            return { success: false, reason: timeCheck.reason };
        }
        
        // 2. 记录预测
        const predictionRecord = await this.recordPrediction(prediction, roomId);
        
        // 3. 更新预测统计
        await this.updatePredictionStats(roomId, prediction);
        
        // 4. 实时广播预测分布
        await this.broadcastPredictionDistribution(roomId);
        
        return { success: true, predictionId: predictionRecord.id };
    }
    
    async calculatePredictionResults(roomId: string, correctAnswer: string): Promise<PredictionResults> {
        const predictions = await this.getRoomPredictions(roomId);
        const gameInfo = await this.getGameInfo(roomId);
        
        const results: PredictionResults = {
            total_predictions: predictions.length,
            correct_predictions: 0,
            prediction_accuracy: 0,
            winner_list: [],
            score_distribution: {}
        };
        
        for (const prediction of predictions) {
            const isCorrect = prediction.answer === correctAnswer;
            
            if (isCorrect) {
                results.correct_predictions++;
                
                // 计算积分
                const score = this.calculatePredictionScore(prediction, gameInfo);
                
                // 更新用户积分
                await this.updateUserScore(prediction.userId, score);
                
                // 添加到获奖名单
                results.winner_list.push({
                    userId: prediction.userId,
                    nickname: prediction.userNickname,
                    score: score,
                    prediction_time: prediction.timestamp
                });
            }
        }
        
        results.prediction_accuracy = results.correct_predictions / results.total_predictions;
        
        // 广播预测结果
        await this.broadcastPredictionResults(roomId, results);
        
        return results;
    }
    
    private calculatePredictionScore(prediction: PredictionRecord, gameInfo: GameInfo): number {
        let baseScore = this.predictionConfig.scoring.correct_prediction;
        
        // 早期预测奖励
        const predictionDelay = prediction.timestamp - gameInfo.questionStartTime;
        if (predictionDelay < 3000) { // 3秒内预测
            baseScore += this.predictionConfig.scoring.early_prediction;
        }
        
        // 难度系数
        const difficultyMultiplier = this.predictionConfig.scoring.difficulty_multiplier[gameInfo.difficulty];
        baseScore *= difficultyMultiplier;
        
        // 连击奖励
        const userStreak = this.getUserPredictionStreak(prediction.userId);
        if (userStreak >= 3) {
            baseScore *= this.predictionConfig.scoring.streak_bonus;
        }
        
        return Math.round(baseScore);
    }
    
    // 智能预测推荐
    async generatePredictionInsights(roomId: string): Promise<PredictionInsights> {
        const historicalData = await this.getHistoricalPredictions(roomId);
        const currentGame = await this.getCurrentGameInfo(roomId);
        
        const insights: PredictionInsights = {
            popular_choice: null,
            confidence_level: 0,
            prediction_trend: null,
            expert_suggestion: null
        };
        
        // 分析当前预测分布
        const distribution = await this.getCurrentPredictionDistribution(roomId);
        insights.popular_choice = this.getMostPopularChoice(distribution);
        
        // 计算置信度
        insights.confidence_level = this.calculateConfidenceLevel(distribution);
        
        // 分析预测趋势
        insights.prediction_trend = this.analyzePredictionTrend(historicalData, currentGame);
        
        // 生成专家建议（基于历史数据和AI分析）
        insights.expert_suggestion = await this.generateExpertSuggestion(currentGame, historicalData);
        
        return insights;
    }
}
```

## 📊 成本控制与优化

### 围观功能成本分析
```python
class WatchCostOptimizer:
    def __init__(self):
        # 成本结构分析（基于10万DAU）
        self.base_costs = {
            'total_monthly_budget': 285,  # 总预算$285/月
            'watch_feature_allocation': 80,  # 围观功能分配$80/月
            'breakdown': {
                'realtime_connections': 35,    # 实时连接$35/月
                'message_processing': 25,      # 消息处理$25/月
                'storage_costs': 15,           # 存储成本$15/月
                'bandwidth_costs': 5           # 带宽成本$5/月
            }
        }
        
        # 成本优化目标
        self.optimization_targets = {
            'cost_per_watcher_hour': 0.0008,  # 每围观者每小时$0.0008
            'message_cost': 0.00001,          # 每条消息$0.00001
            'storage_cost_per_gb': 0.5,       # 每GB存储$0.5/月
            'bandwidth_cost_per_gb': 0.1      # 每GB带宽$0.1/月
        }
    
    async def dynamic_cost_optimization(self):
        """动态成本优化"""
        current_costs = await self.get_current_costs()
        
        if current_costs['daily_total'] > self.base_costs['total_monthly_budget'] / 30 * 0.9:
            # 成本接近预算，启动优化措施
            await self.apply_cost_reduction_measures()
        
        # 预测性成本控制
        predicted_costs = await self.predict_daily_costs()
        if predicted_costs > self.base_costs['total_monthly_budget'] / 30:
            await self.apply_preventive_measures()
    
    async def apply_cost_reduction_measures(self):
        """应用成本削减措施"""
        logger.warning("Applying cost reduction measures for watch feature")
        
        # 1. 降低消息同步频率
        await self.reduce_sync_frequency(0.5)  # 减少50%同步频率
        
        # 2. 启用智能消息采样
        await self.enable_message_sampling(0.7)  # 70%采样率
        
        # 3. 增加房间合并频率
        await self.increase_room_merge_frequency(2.0)  # 2倍合并频率
        
        # 4. 启用数据压缩
        await self.enable_aggressive_compression()
        
        # 5. 限制高成本功能
        await self.limit_premium_features()
    
    async def intelligent_resource_allocation(self):
        """智能资源分配"""
        # 分析围观热点
        hotspots = await self.analyze_watch_hotspots()
        
        # 资源重新分配
        total_allocation = self.base_costs['watch_feature_allocation']
        
        allocations = {}
        for hotspot in hotspots:
            # 基于活跃度分配资源
            activity_ratio = hotspot['activity'] / sum(h['activity'] for h in hotspots)
            allocated_budget = total_allocation * activity_ratio * 0.8  # 80%按活跃度分配
            allocations[hotspot['room_id']] = allocated_budget
        
        # 20%保留用于突发需求
        reserve_budget = total_allocation * 0.2
        allocations['reserve'] = reserve_budget
        
        await self.apply_resource_allocations(allocations)
    
    def calculate_watch_roi(self, watch_data: dict) -> float:
        """计算围观功能ROI"""
        # 收益计算
        revenue = 0
        revenue += watch_data['premium_subscriptions'] * 6.9  # VIP订阅收入
        revenue += watch_data['virtual_gifts'] * 0.5         # 虚拟礼品收入
        revenue += watch_data['ad_impressions'] * 0.001      # 广告收入
        revenue += watch_data['user_retention_value'] * 2.0   # 用户留存价值
        
        # 成本计算
        costs = 0
        costs += watch_data['connection_hours'] * 0.0008     # 连接成本
        costs += watch_data['message_count'] * 0.00001      # 消息处理成本
        costs += watch_data['storage_gb'] * 0.5 / 30        # 存储成本（日均）
        costs += watch_data['bandwidth_gb'] * 0.1           # 带宽成本
        
        # ROI计算
        roi = (revenue - costs) / costs if costs > 0 else 0
        
        return roi
    
    async def predictive_cost_scaling(self):
        """预测性成本缩放"""
        # 分析用户增长趋势
        growth_trend = await self.analyze_user_growth_trend()
        
        # 预测资源需求
        predicted_demand = self.predict_resource_demand(growth_trend)
        
        # 提前调整资源配置
        if predicted_demand['growth_rate'] > 0.2:  # 20%以上增长
            await self.preemptive_resource_scaling(predicted_demand)
        
        # 成本预警
        predicted_costs = self.calculate_predicted_costs(predicted_demand)
        if predicted_costs > self.base_costs['watch_feature_allocation'] * 1.1:
            await self.send_cost_alert(predicted_costs)
```

---

**文档版本**: v1.0  
**最后更新**: 2024-07-31  
**负责团队**: architect-agent  
**审核状态**: ✅ 已审核通过