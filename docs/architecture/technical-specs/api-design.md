# RESTful API设计与认证授权体系

## 1. API设计规范

### 1.1 总体设计原则
- **RESTful风格**: 资源驱动的URL设计
- **版本控制**: API版本通过URL路径控制
- **统一响应**: 一致的响应格式和错误处理
- **幂等性**: GET、PUT、DELETE操作保证幂等
- **状态码规范**: 合理使用HTTP状态码

### 1.2 API基础规范

#### URL设计规范
```
基础格式: https://api.dialectgame.com/v1/{resource}[/{id}][?query]

资源命名规则:
- 使用名词复数形式
- 使用小写字母和连字符
- 避免动词，通过HTTP方法表达操作

示例:
✅ GET /v1/users/123
✅ POST /v1/game-sessions
✅ PUT /v1/users/123/profile
❌ GET /v1/getUser/123
❌ POST /v1/createGameSession
```

#### HTTP方法规范
```javascript
const HTTP_METHODS = {
  GET: '获取资源（幂等）',
  POST: '创建资源（非幂等）',
  PUT: '更新整个资源（幂等）',
  PATCH: '部分更新资源（非幂等）',
  DELETE: '删除资源（幂等）'
};

// 方法使用示例
// 获取用户信息
GET /v1/users/123

// 创建游戏会话
POST /v1/game-sessions

// 更新用户资料
PUT /v1/users/123/profile

// 更新用户积分
PATCH /v1/users/123/score

// 删除游戏记录
DELETE /v1/game-records/456
```

#### 统一响应格式
```javascript
// 成功响应格式
{
  "code": 0,
  "message": "success",
  "data": {
    // 业务数据
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_1642248600_abc123"
}

// 错误响应格式
{
  "code": "INVALID_PARAMS",
  "message": "参数验证失败",
  "details": {
    "field": "userId",
    "reason": "用户ID不能为空"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_1642248600_def456"
}

// 分页响应格式
{
  "code": 0,
  "message": "success",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_1642248600_ghi789"
}
```

## 2. API路由设计

### 2.1 用户相关API

#### 用户认证
```javascript
// 微信登录
POST /v1/auth/wechat/login
{
  "code": "微信授权码",
  "encryptedData": "加密用户数据（可选）",
  "iv": "初始化向量（可选）"
}

// 刷新Token
POST /v1/auth/refresh
{
  "refreshToken": "刷新令牌"
}

// 退出登录
POST /v1/auth/logout
Authorization: Bearer {accessToken}
```

#### 用户信息管理
```javascript
// 获取用户信息
GET /v1/users/{userId}
Authorization: Bearer {accessToken}

// 更新用户资料
PUT /v1/users/{userId}/profile
Authorization: Bearer {accessToken}
{
  "nickname": "用户昵称",
  "avatar": "头像URL",
  "province": "省份",
  "city": "城市"
}

// 获取用户游戏统计
GET /v1/users/{userId}/stats?category={dialectCategory}
Authorization: Bearer {accessToken}

// 获取用户游戏历史
GET /v1/users/{userId}/game-history?page=1&size=20&category={category}
Authorization: Bearer {accessToken}
```

### 2.2 游戏相关API

#### 游戏会话管理
```javascript
// 创建游戏会话
POST /v1/game-sessions
Authorization: Bearer {accessToken}
{
  "category": "方言类别",
  "difficulty": 1,
  "questionCount": 10,
  "gameMode": "standard"
}

// 获取游戏会话信息
GET /v1/game-sessions/{sessionId}
Authorization: Bearer {accessToken}

// 结束游戏会话
PUT /v1/game-sessions/{sessionId}/finish
Authorization: Bearer {accessToken}
{
  "finalScore": 850,
  "correctCount": 8,
  "totalTime": 120
}
```

#### 题目相关
```javascript
// 获取题目
GET /v1/questions?category={category}&difficulty={level}&limit=10
Authorization: Bearer {accessToken}

// 获取题目详情
GET /v1/questions/{questionId}
Authorization: Bearer {accessToken}

// 提交答案
POST /v1/game-sessions/{sessionId}/answers
Authorization: Bearer {accessToken}
{
  "questionId": 123,
  "answer": "用户答案",
  "answerTime": 5.2,
  "hintUsed": false
}
```

### 2.3 排行榜API

```javascript
// 获取排行榜
GET /v1/leaderboards/{type}?period={period}&category={category}&page=1&size=100
// type: overall|weekly|monthly|friends
// period: current|2024-01|2024-W01

// 获取用户排名
GET /v1/users/{userId}/ranking?type=overall&category={category}
Authorization: Bearer {accessToken}

// 获取好友排行
GET /v1/users/{userId}/friends-ranking?category={category}
Authorization: Bearer {accessToken}
```

### 2.4 社交功能API

```javascript
// 分享游戏成绩
POST /v1/shares
Authorization: Bearer {accessToken}
{
  "type": "score",
  "content": {
    "score": 850,
    "category": "beijing",
    "accuracy": 0.85
  },
  "platform": "wechat"
}

// 邀请好友
POST /v1/invitations
Authorization: Bearer {accessToken}
{
  "inviteCode": "生成的邀请码",
  "message": "邀请消息"
}

// 接受邀请
POST /v1/invitations/{inviteCode}/accept
Authorization: Bearer {accessToken}
```

## 3. 认证授权体系

### 3.1 JWT Token设计

#### Token结构
```javascript
// JWT Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// JWT Payload
{
  "sub": "用户ID",
  "iat": 1642248600,        // 签发时间
  "exp": 1642335000,        // 过期时间（24小时）
  "aud": "dialect-game",    // 受众
  "iss": "api.dialectgame.com", // 签发者
  "scope": "user:read user:write game:play", // 权限范围
  "sessionId": "会话ID",
  "deviceId": "设备ID"
}

// JWT Signature
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

#### Token管理实现
```javascript
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class TokenManager {
  constructor() {
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET;
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET;
    this.accessTokenTTL = 24 * 60 * 60; // 24小时
    this.refreshTokenTTL = 30 * 24 * 60 * 60; // 30天
  }
  
  // 生成访问令牌
  generateAccessToken(user, sessionId = null) {
    const payload = {
      sub: user.id,
      aud: 'dialect-game',
      iss: 'api.dialectgame.com',
      scope: this.getUserScopes(user),
      sessionId: sessionId || this.generateSessionId(),
      deviceId: user.deviceId || null
    };
    
    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn: this.accessTokenTTL
    });
  }
  
  // 生成刷新令牌
  generateRefreshToken(userId) {
    const payload = {
      sub: userId,
      type: 'refresh',
      aud: 'dialect-game',
      iss: 'api.dialectgame.com'
    };
    
    return jwt.sign(payload, this.refreshTokenSecret, {
      expiresIn: this.refreshTokenTTL
    });
  }
  
  // 验证访问令牌
  verifyAccessToken(token) {
    try {
      return jwt.verify(token, this.accessTokenSecret);
    } catch (error) {
      throw new Error(`Invalid access token: ${error.message}`);
    }
  }
  
  // 验证刷新令牌
  verifyRefreshToken(token) {
    try {
      return jwt.verify(token, this.refreshTokenSecret);
    } catch (error) {
      throw new Error(`Invalid refresh token: ${error.message}`);
    }
  }
  
  // 获取用户权限范围
  getUserScopes(user) {
    const baseScopes = ['user:read', 'game:play'];
    
    if (user.role === 'admin') {
      baseScopes.push('admin:read', 'admin:write');
    }
    
    if (user.isPremium) {
      baseScopes.push('premium:features');
    }
    
    return baseScopes.join(' ');
  }
  
  // 生成会话ID
  generateSessionId() {
    return crypto.randomBytes(16).toString('hex');
  }
}

// 全局Token管理器
const tokenManager = new TokenManager();
```

### 3.2 认证中间件

#### 认证中间件实现
```javascript
// 认证中间件
const authMiddleware = (requiredScopes = []) => {
  return async (event, context) => {
    try {
      // 提取Token
      const authHeader = event.headers.authorization || event.headers.Authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('Missing or invalid authorization header');
      }
      
      const token = authHeader.substring(7);
      
      // 验证Token
      const payload = tokenManager.verifyAccessToken(token);
      
      // 检查用户状态
      const user = await getUserById(payload.sub);
      if (!user || user.status !== 'active') {
        throw new Error('User not found or inactive');
      }
      
      // 检查权限
      if (requiredScopes.length > 0) {
        const userScopes = payload.scope.split(' ');
        const hasPermission = requiredScopes.some(scope => 
          userScopes.includes(scope)
        );
        
        if (!hasPermission) {
          throw new Error('Insufficient permissions');
        }
      }
      
      // 将用户信息添加到上下文
      event.user = user;
      event.tokenPayload = payload;
      
      return { authorized: true };
      
    } catch (error) {
      console.error('Authentication failed:', error);
      
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json',
          'WWW-Authenticate': 'Bearer realm="API"'
        },
        body: JSON.stringify({
          code: 'UNAUTHORIZED',
          message: error.message,
          timestamp: new Date().toISOString()
        })
      };
    }
  };
};

// 使用示例
const protectedHandler = async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult; // 返回认证失败响应
  }
  
  // 业务逻辑
  const userId = event.user.id;
  // ...
};
```

### 3.3 微信登录集成

#### 微信登录流程
```javascript
const axios = require('axios');

class WeChatAuthService {
  constructor() {
    this.appId = process.env.WECHAT_APP_ID;
    this.appSecret = process.env.WECHAT_APP_SECRET;
  }
  
  // 微信登录
  async login(code, encryptedData = null, iv = null) {
    try {
      // 1. 通过code获取session_key和openid
      const sessionData = await this.getSessionKey(code);
      
      // 2. 解密用户信息（如果提供了加密数据）
      let userInfo = null;
      if (encryptedData && iv) {
        userInfo = this.decryptUserInfo(
          sessionData.session_key, 
          encryptedData, 
          iv
        );
      }
      
      // 3. 查找或创建用户
      let user = await this.findUserByOpenId(sessionData.openid);
      if (!user) {
        user = await this.createUser({
          openid: sessionData.openid,
          unionid: sessionData.unionid,
          ...userInfo
        });
      } else if (userInfo) {
        // 更新用户信息
        user = await this.updateUser(user.id, userInfo);
      }
      
      // 4. 生成Token
      const accessToken = tokenManager.generateAccessToken(user);
      const refreshToken = tokenManager.generateRefreshToken(user.id);
      
      // 5. 保存刷新令牌
      await this.saveRefreshToken(user.id, refreshToken);
      
      return {
        accessToken,
        refreshToken,
        expiresIn: tokenManager.accessTokenTTL,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar_url,
          totalScore: user.total_score
        }
      };
      
    } catch (error) {
      console.error('WeChat login failed:', error);
      throw new Error('微信登录失败');
    }
  }
  
  // 获取微信session_key
  async getSessionKey(code) {
    const url = 'https://api.weixin.qq.com/sns/jscode2session';
    const params = {
      appid: this.appId,
      secret: this.appSecret,
      js_code: code,
      grant_type: 'authorization_code'
    };
    
    const response = await axios.get(url, { params });
    
    if (response.data.errcode) {
      throw new Error(`WeChat API error: ${response.data.errmsg}`);
    }
    
    return response.data;
  }
  
  // 解密用户信息
  decryptUserInfo(sessionKey, encryptedData, iv) {
    const crypto = require('crypto');
    
    const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
    const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
    const ivBuffer = Buffer.from(iv, 'base64');
    
    const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
    let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
    decrypted += decipher.final('utf8');
    
    return JSON.parse(decrypted);
  }
  
  // 查找用户
  async findUserByOpenId(openid) {
    const sql = 'SELECT * FROM users WHERE openid = ? AND status = 1';
    const results = await db.execute(sql, [openid]);
    return results[0] || null;
  }
  
  // 创建用户
  async createUser(userData) {
    const sql = `
      INSERT INTO users (openid, unionid, nickname, avatar_url, gender, province, city, country)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      userData.openid,
      userData.unionid || null,
      userData.nickName || '',
      userData.avatarUrl || null,
      userData.gender || 0,
      userData.province || null,
      userData.city || null,
      userData.country || null
    ];
    
    const result = await db.execute(sql, params);
    
    // 返回创建的用户
    return await this.findUserByOpenId(userData.openid);
  }
}

// 全局微信认证服务
const weChatAuthService = new WeChatAuthService();
```

## 4. 限流和防刷策略

### 4.1 限流算法实现

#### 令牌桶算法
```javascript
class TokenBucket {
  constructor(capacity, refillRate, redis) {
    this.capacity = capacity;        // 桶容量
    this.refillRate = refillRate;    // 令牌补充速率（个/秒）
    this.redis = redis;
  }
  
  async consume(key, tokens = 1) {
    const now = Date.now();
    const bucketKey = `bucket:${key}`;
    
    // Lua脚本实现原子操作
    const script = `
      local bucket_key = KEYS[1]
      local capacity = tonumber(ARGV[1])
      local refill_rate = tonumber(ARGV[2])
      local tokens_requested = tonumber(ARGV[3])
      local now = tonumber(ARGV[4])
      
      -- 获取当前状态
      local bucket_data = redis.call('HMGET', bucket_key, 'tokens', 'last_refill')
      local current_tokens = tonumber(bucket_data[1]) or capacity
      local last_refill = tonumber(bucket_data[2]) or now
      
      -- 计算补充的令牌数
      local time_passed = (now - last_refill) / 1000
      local tokens_to_add = math.floor(time_passed * refill_rate)
      current_tokens = math.min(capacity, current_tokens + tokens_to_add)
      
      -- 检查是否有足够令牌
      if current_tokens >= tokens_requested then
        current_tokens = current_tokens - tokens_requested
        
        -- 更新状态
        redis.call('HMSET', bucket_key, 
          'tokens', current_tokens, 
          'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600)
        
        return {1, current_tokens}
      else
        -- 更新最后补充时间但不消费令牌
        redis.call('HMSET', bucket_key, 
          'tokens', current_tokens, 
          'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600)
        
        return {0, current_tokens}
      end
    `;
    
    const result = await this.redis.eval(
      script, 
      1, 
      bucketKey, 
      this.capacity, 
      this.refillRate, 
      tokens, 
      now
    );
    
    return {
      allowed: result[0] === 1,
      remainingTokens: result[1]
    };
  }
}
```

#### 滑动窗口算法
```javascript
class SlidingWindowRateLimit {
  constructor(windowSize, maxRequests, redis) {
    this.windowSize = windowSize * 1000; // 转换为毫秒
    this.maxRequests = maxRequests;
    this.redis = redis;
  }
  
  async checkLimit(key) {
    const now = Date.now();
    const windowStart = now - this.windowSize;
    const limitKey = `sliding:${key}`;
    
    const script = `
      local key = KEYS[1]
      local window_start = tonumber(ARGV[1])
      local now = tonumber(ARGV[2])
      local max_requests = tonumber(ARGV[3])
      
      -- 清理过期记录
      redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
      
      -- 获取当前窗口内的请求数
      local current_requests = redis.call('ZCARD', key)
      
      if current_requests < max_requests then
        -- 添加当前请求
        redis.call('ZADD', key, now, now)
        redis.call('EXPIRE', key, math.ceil(ARGV[4] / 1000))
        return {1, max_requests - current_requests - 1}
      else
        return {0, 0}
      end
    `;
    
    const result = await this.redis.eval(
      script,
      1,
      limitKey,
      windowStart,
      now,
      this.maxRequests,
      this.windowSize
    );
    
    return {
      allowed: result[0] === 1,
      remaining: result[1]
    };
  }
}
```

### 4.2 限流中间件
```javascript
// 限流配置
const RATE_LIMIT_CONFIG = {
  // 全局限流
  global: { capacity: 1000, refillRate: 10 }, // 1000请求/桶，10个/秒补充
  
  // 用户级限流
  user: { capacity: 100, refillRate: 2 },     // 100请求/桶，2个/秒补充
  
  // API级限流
  api: {
    '/v1/auth/login': { capacity: 10, refillRate: 0.1 },     // 登录：10次/桶，6次/分钟
    '/v1/questions': { capacity: 100, refillRate: 5 },       // 题目：100次/桶，5次/秒
    '/v1/game-sessions': { capacity: 20, refillRate: 0.5 },  // 游戏：20次/桶，30次/分钟
    '/v1/leaderboards': { capacity: 50, refillRate: 1 }      // 排行榜：50次/桶，1次/秒
  },
  
  // IP级限流
  ip: { windowSize: 60, maxRequests: 300 } // 滑动窗口：60秒内最多300请求
};

// 限流中间件
const rateLimitMiddleware = (limitType = 'user') => {
  return async (event, context) => {
    try {
      const redis = getRedisCluster();
      let limitKey;
      let limiter;
      
      // 根据限流类型生成key
      switch (limitType) {
        case 'global':
          limitKey = 'global';
          limiter = new TokenBucket(
            RATE_LIMIT_CONFIG.global.capacity,
            RATE_LIMIT_CONFIG.global.refillRate,
            redis
          );
          break;
          
        case 'user':
          if (!event.user) {
            throw new Error('User authentication required for user-level rate limiting');
          }
          limitKey = `user:${event.user.id}`;
          limiter = new TokenBucket(
            RATE_LIMIT_CONFIG.user.capacity,
            RATE_LIMIT_CONFIG.user.refillRate,
            redis
          );
          break;
          
        case 'api':
          const apiPath = event.path;
          const apiConfig = RATE_LIMIT_CONFIG.api[apiPath];
          if (!apiConfig) {
            return { allowed: true }; // 没有配置限流则允许
          }
          limitKey = `api:${apiPath}:${event.user?.id || event.sourceIP}`;
          limiter = new TokenBucket(
            apiConfig.capacity,
            apiConfig.refillRate,
            redis
          );
          break;
          
        case 'ip':
          limitKey = `ip:${event.sourceIP}`;
          limiter = new SlidingWindowRateLimit(
            RATE_LIMIT_CONFIG.ip.windowSize,
            RATE_LIMIT_CONFIG.ip.maxRequests,
            redis
          );
          break;
          
        default:
          return { allowed: true };
      }
      
      // 检查限流
      const result = await limiter.consume ? 
        await limiter.consume(limitKey) : 
        await limiter.checkLimit(limitKey);
      
      if (!result.allowed) {
        return {
          statusCode: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Limit': RATE_LIMIT_CONFIG[limitType].capacity || RATE_LIMIT_CONFIG[limitType].maxRequests,
            'X-RateLimit-Remaining': result.remainingTokens || result.remaining,
            'Retry-After': '60'
          },
          body: JSON.stringify({
            code: 'RATE_LIMIT_EXCEEDED',
            message: '请求频率过高，请稍后再试',
            timestamp: new Date().toISOString()
          })
        };
      }
      
      // 添加限流信息到响应头
      event.rateLimitInfo = {
        remaining: result.remainingTokens || result.remaining,
        limit: RATE_LIMIT_CONFIG[limitType].capacity || RATE_LIMIT_CONFIG[limitType].maxRequests
      };
      
      return { allowed: true };
      
    } catch (error) {
      console.error('Rate limiting error:', error);
      return { allowed: true }; // 限流失败时允许请求通过
    }
  };
};
```

### 4.3 防刷机制
```javascript
// 防刷检测服务
class AntiFraudService {
  constructor() {
    this.redis = getRedisCluster();
  }
  
  // 设备指纹检测
  async checkDeviceFingerprint(deviceId, userId) {
    const key = `device:${deviceId}`;
    const existingUser = await this.redis.get(key);
    
    if (existingUser && existingUser !== userId.toString()) {
      // 同一设备多个用户，可能存在刷量行为
      await this.recordSuspiciousActivity('DEVICE_SHARING', {
        deviceId,
        users: [existingUser, userId]
      });
      
      return { suspicious: true, reason: 'DEVICE_SHARING' };
    }
    
    // 记录设备-用户关联
    await this.redis.setex(key, 24 * 60 * 60, userId); // 24小时过期
    
    return { suspicious: false };
  }
  
  // 游戏行为检测
  async checkGameBehavior(userId, gameSession) {
    const suspicious = [];
    
    // 1. 答题速度检测
    if (gameSession.avgAnswerTime < 1) {
      suspicious.push('ANSWER_TOO_FAST');
    }
    
    // 2. 准确率异常检测
    if (gameSession.accuracy > 0.95 && gameSession.avgAnswerTime < 3) {
      suspicious.push('ACCURACY_TOO_HIGH');
    }
    
    // 3. 游戏频率检测
    const todayGames = await this.getTodayGameCount(userId);
    if (todayGames > 100) {
      suspicious.push('TOO_MANY_GAMES');
    }
    
    if (suspicious.length > 0) {
      await this.recordSuspiciousActivity('GAME_BEHAVIOR', {
        userId,
        issues: suspicious,
        gameSession
      });
      
      return { suspicious: true, reasons: suspicious };
    }
    
    return { suspicious: false };
  }
  
  // 记录可疑活动
  async recordSuspiciousActivity(type, data) {
    const record = {
      type,
      data,
      timestamp: new Date().toISOString(),
      severity: this.getSeverityLevel(type)
    };
    
    const key = `suspicious:${Date.now()}:${Math.random()}`;
    await this.redis.setex(key, 7 * 24 * 60 * 60, JSON.stringify(record)); // 保存7天
    
    console.warn('Suspicious activity detected:', record);
  }
  
  getSeverityLevel(type) {
    const severityMap = {
      'DEVICE_SHARING': 'MEDIUM',
      'ANSWER_TOO_FAST': 'HIGH',
      'ACCURACY_TOO_HIGH': 'HIGH',
      'TOO_MANY_GAMES': 'MEDIUM'
    };
    
    return severityMap[type] || 'LOW';
  }
  
  async getTodayGameCount(userId) {
    const today = new Date().toISOString().split('T')[0];
    const key = `game_count:${userId}:${today}`;
    
    const count = await this.redis.get(key);
    return parseInt(count) || 0;
  }
}

// 全局防刷服务
const antiFraudService = new AntiFraudService();
```

## 5. 错误处理机制

### 5.1 统一错误码设计
```javascript
// 错误码定义
const ERROR_CODES = {
  // 通用错误 (1000-1099)
  INTERNAL_ERROR: { code: 1000, message: '服务器内部错误' },
  INVALID_PARAMS: { code: 1001, message: '参数验证失败' },
  RESOURCE_NOT_FOUND: { code: 1004, message: '资源不存在' },
  
  // 认证错误 (2000-2099)
  UNAUTHORIZED: { code: 2001, message: '未授权访问' },
  INVALID_TOKEN: { code: 2002, message: '无效的访问令牌' },
  TOKEN_EXPIRED: { code: 2003, message: '访问令牌已过期' },
  INSUFFICIENT_PERMISSIONS: { code: 2004, message: '权限不足' },
  
  // 业务错误 (3000-3099)
  USER_NOT_FOUND: { code: 3001, message: '用户不存在' },
  GAME_SESSION_EXPIRED: { code: 3002, message: '游戏会话已过期' },
  QUESTION_NOT_AVAILABLE: { code: 3003, message: '题目不可用' },
  INVALID_ANSWER: { code: 3004, message: '无效的答案格式' },
  
  // 限流错误 (4000-4099)
  RATE_LIMIT_EXCEEDED: { code: 4001, message: '请求频率过高' },
  DAILY_LIMIT_EXCEEDED: { code: 4002, message: '今日游戏次数已达上限' },
  
  // 第三方服务错误 (5000-5099)
  WECHAT_API_ERROR: { code: 5001, message: '微信接口调用失败' },
  PAYMENT_ERROR: { code: 5002, message: '支付处理失败' }
};

// 自定义错误类
class APIError extends Error {
  constructor(errorCode, details = null, statusCode = 400) {
    const errorInfo = ERROR_CODES[errorCode] || ERROR_CODES.INTERNAL_ERROR;
    super(errorInfo.message);
    
    this.name = 'APIError';
    this.code = errorCode;
    this.statusCode = statusCode;
    this.details = details;
  }
}

// 错误处理中间件
const errorHandler = (handler) => {
  return async (event, context) => {
    try {
      const result = await handler(event, context);
      
      // 成功响应
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'X-RateLimit-Limit': event.rateLimitInfo?.limit,
          'X-RateLimit-Remaining': event.rateLimitInfo?.remaining
        },
        body: JSON.stringify({
          code: 0,
          message: 'success',
          data: result,
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
      
    } catch (error) {
      console.error('API Error:', error);
      
      let statusCode = 500;
      let errorCode = 'INTERNAL_ERROR';
      let details = null;
      
      if (error instanceof APIError) {
        statusCode = error.statusCode;
        errorCode = error.code;
        details = error.details;
      } else if (error.name === 'ValidationError') {
        statusCode = 400;
        errorCode = 'INVALID_PARAMS';
        details = error.details;
      }
      
      const errorInfo = ERROR_CODES[errorCode] || ERROR_CODES.INTERNAL_ERROR;
      
      return {
        statusCode,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          code: errorCode,
          message: errorInfo.message,
          details,
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
    }
  };
};
```

### 5.2 参数验证
```javascript
const Ajv = require('ajv');
const addFormats = require('ajv-formats');

// 参数验证器
class RequestValidator {
  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);
  }
  
  // 验证请求参数
  validate(schema, data) {
    const validate = this.ajv.compile(schema);
    const valid = validate(data);
    
    if (!valid) {
      throw new APIError('INVALID_PARAMS', {
        errors: validate.errors.map(error => ({
          field: error.instancePath.slice(1) || error.params?.missingProperty,
          message: error.message,
          value: error.data
        }))
      });
    }
    
    return true;
  }
}

// 验证中间件
const validateRequest = (schema) => {
  const validator = new RequestValidator();
  
  return (event) => {
    let data = {};
    
    // 解析请求体
    if (event.body) {
      try {
        data = JSON.parse(event.body);
      } catch (error) {
        throw new APIError('INVALID_PARAMS', { message: '无效的JSON格式' });
      }
    }
    
    // 合并查询参数
    if (event.queryStringParameters) {
      data = { ...data, ...event.queryStringParameters };
    }
    
    // 合并路径参数
    if (event.pathParameters) {
      data = { ...data, ...event.pathParameters };
    }
    
    // 验证参数
    validator.validate(schema, data);
    
    return data;
  };
};

// 常用验证Schema
const VALIDATION_SCHEMAS = {
  // 用户登录
  wechatLogin: {
    type: 'object',
    required: ['code'],
    properties: {
      code: { type: 'string', minLength: 1 },
      encryptedData: { type: 'string' },
      iv: { type: 'string' }
    }
  },
  
  // 创建游戏会话
  createGameSession: {
    type: 'object',
    required: ['category', 'difficulty'],
    properties: {
      category: { type: 'string', enum: ['beijing', 'shanghai', 'guangdong', 'sichuan'] },
      difficulty: { type: 'integer', minimum: 1, maximum: 5 },
      questionCount: { type: 'integer', minimum: 5, maximum: 50, default: 10 },
      gameMode: { type: 'string', enum: ['standard', 'challenge', 'practice'], default: 'standard' }
    }
  },
  
  // 提交答案
  submitAnswer: {
    type: 'object',
    required: ['questionId', 'answer'],
    properties: {
      questionId: { type: 'integer', minimum: 1 },
      answer: { type: 'string', minLength: 1, maxLength: 200 },
      answerTime: { type: 'number', minimum: 0 },
      hintUsed: { type: 'boolean', default: false }
    }
  }
};
```

这个API设计提供了：

1. **完整的认证体系**: JWT + 微信登录集成
2. **合理的限流机制**: 多层限流 + 防刷检测
3. **统一的错误处理**: 标准化错误码和响应格式
4. **严格的参数验证**: Schema验证确保数据安全
5. **RESTful规范**: 标准的API设计模式

通过这些设计，确保API在高并发场景下的稳定性和安全性，同时提供良好的开发体验。