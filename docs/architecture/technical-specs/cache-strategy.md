# Redis多层缓存架构设计

## 1. 缓存架构总览

### 1.1 多层缓存架构图

```
┌─────────────────────────────────────────────────────────────┐
│                      微信小程序                              │
│                  (本地缓存 5分钟)                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  API网关                                    │
│             (边缘缓存 1分钟)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                SCF函数实例                                   │
│            (进程内缓存 30秒)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Redis缓存集群                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   L1缓存    │ │   L2缓存    │ │      持久化缓存         │ │
│  │ (热点数据)   │ │ (用户数据)   │ │   (配置/排行榜)         │ │
│  │   30秒      │ │   10分钟    │ │     24小时             │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 TencentDB MySQL                             │
│                  (数据源)                                   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 缓存层次设计

| 缓存层级 | 存储位置 | TTL | 用途 | 命中率目标 |
|---------|----------|-----|------|-----------|
| L0 | 小程序本地 | 5分钟 | 静态配置、用户信息 | 80% |
| L1 | API网关 | 1分钟 | 热点API响应 | 60% |
| L2 | SCF进程内 | 30秒 | 函数级缓存 | 40% |
| L3 | Redis热点 | 30秒-5分钟 | 高频数据 | 95% |
| L4 | Redis用户 | 10分钟-1小时 | 用户相关数据 | 90% |
| L5 | Redis持久 | 1小时-24小时 | 配置、排行榜 | 85% |

## 2. Redis集群配置

### 2.1 集群架构
```javascript
// Redis集群配置
const REDIS_CLUSTER_CONFIG = {
  // 主节点配置
  nodes: [
    { host: process.env.REDIS_HOST_1, port: 6379 },
    { host: process.env.REDIS_HOST_2, port: 6379 },
    { host: process.env.REDIS_HOST_3, port: 6379 }
  ],
  
  // 集群选项
  options: {
    // 连接配置
    password: process.env.REDIS_PASSWORD,
    connectTimeout: 10000,
    commandTimeout: 5000,
    
    // 重试配置
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    
    // 性能优化
    enableOfflineQueue: false,
    lazyConnect: true,
    keepAlive: true,
    
    // 集群特定配置
    enableReadyCheck: true,
    redisOptions: {
      family: 4,
      connectTimeout: 5000,
      commandTimeout: 5000
    }
  }
};

// 初始化Redis集群
const Redis = require('ioredis');
let redisCluster = null;

const getRedisCluster = () => {
  if (!redisCluster) {
    redisCluster = new Redis.Cluster(
      REDIS_CLUSTER_CONFIG.nodes,
      REDIS_CLUSTER_CONFIG.options
    );
    
    // 错误处理
    redisCluster.on('error', (err) => {
      console.error('Redis cluster error:', err);
    });
    
    redisCluster.on('ready', () => {
      console.log('Redis cluster ready');
    });
  }
  
  return redisCluster;
};
```

### 2.2 分片策略
```javascript
// 数据分片规则
const SHARD_RULES = {
  // 用户数据：按用户ID分片
  user: (userId) => `user:${userId % 16}`,
  
  // 游戏数据：按会话ID分片  
  game: (sessionId) => `game:${sessionId.slice(-2)}`,
  
  // 排行榜：按类型分片
  leaderboard: (type) => `rank:${type}`,
  
  // 题目缓存：按分类分片
  question: (category) => `quest:${category}`
};

// 分片路由器
class ShardRouter {
  static getShardKey(dataType, identifier) {
    const shardRule = SHARD_RULES[dataType];
    if (!shardRule) {
      throw new Error(`Unknown data type: ${dataType}`);
    }
    return shardRule(identifier);
  }
  
  static getUserShard(userId) {
    return this.getShardKey('user', userId);
  }
  
  static getGameShard(sessionId) {
    return this.getShardKey('game', sessionId);
  }
}
```

## 3. 缓存键设计规范

### 3.1 键命名规范
```javascript
// 缓存键设计规范
const CACHE_KEYS = {
  // 用户相关
  USER_INFO: 'u:info:{userId}',                    // 用户基础信息
  USER_STATS: 'u:stats:{userId}:{category}',       // 用户游戏统计
  USER_RANK: 'u:rank:{userId}:{type}',             // 用户排名信息
  USER_SESSION: 'u:session:{sessionId}',           // 用户会话
  
  // 游戏相关
  QUESTIONS_POOL: 'g:pool:{category}:{level}',     // 题目池
  QUESTION_DETAIL: 'g:quest:{questionId}',         // 题目详情
  GAME_SESSION: 'g:session:{sessionId}',           // 游戏会话
  GAME_RECORD: 'g:record:{userId}:{date}',         // 游戏记录
  
  // 排行榜
  LEADERBOARD: 'r:board:{type}:{period}',          // 排行榜
  RANK_CACHE: 'r:cache:{type}:{period}',           // 排名缓存
  
  // 系统配置
  SYSTEM_CONFIG: 's:config:{key}',                 // 系统配置
  RATE_LIMIT: 's:limit:{userId}:{api}',            // 限流计数
  
  // 统计数据
  STATS_DAILY: 'st:daily:{date}',                  // 日统计
  STATS_HOURLY: 'st:hourly:{hour}',                // 小时统计
};

// 键生成器
class CacheKeyGenerator {
  static userInfo(userId) {
    return CACHE_KEYS.USER_INFO.replace('{userId}', userId);
  }
  
  static userStats(userId, category) {
    return CACHE_KEYS.USER_STATS
      .replace('{userId}', userId)
      .replace('{category}', category);
  }
  
  static questionsPool(category, level) {
    return CACHE_KEYS.QUESTIONS_POOL
      .replace('{category}', category)
      .replace('{level}', level);
  }
  
  static leaderboard(type, period) {
    return CACHE_KEYS.LEADERBOARD
      .replace('{type}', type)
      .replace('{period}', period);
  }
}
```

### 3.2 TTL策略配置
```javascript
// TTL配置策略
const TTL_CONFIG = {
  // 用户数据TTL
  USER_INFO: 10 * 60,           // 10分钟
  USER_STATS: 5 * 60,           // 5分钟
  USER_SESSION: 30 * 60,        // 30分钟
  
  // 游戏数据TTL
  QUESTIONS_POOL: 1 * 60,       // 1分钟 (热点数据)
  QUESTION_DETAIL: 30 * 60,     // 30分钟
  GAME_SESSION: 20 * 60,        // 20分钟
  
  // 排行榜TTL
  LEADERBOARD_REALTIME: 30,     // 30秒 (实时排行)
  LEADERBOARD_DAILY: 5 * 60,    // 5分钟 (日排行)
  LEADERBOARD_WEEKLY: 30 * 60,  // 30分钟 (周排行)
  
  // 系统配置TTL
  SYSTEM_CONFIG: 24 * 60 * 60,  // 24小时
  RATE_LIMIT: 60,               // 1分钟
  
  // 统计数据TTL
  STATS_REALTIME: 10,           // 10秒
  STATS_DAILY: 24 * 60 * 60,    // 24小时
};

// TTL管理器
class TTLManager {
  static getTTL(dataType) {
    return TTL_CONFIG[dataType] || 300; // 默认5分钟
  }
  
  static getRandomTTL(baseTTL, variance = 0.1) {
    // 添加随机性避免缓存雪崩
    const randomFactor = 1 + (Math.random() - 0.5) * variance * 2;
    return Math.floor(baseTTL * randomFactor);
  }
}
```

## 4. 缓存操作封装

### 4.1 统一缓存接口
```javascript
// 缓存操作封装类
class CacheManager {
  constructor() {
    this.redis = getRedisCluster();
    this.localCache = new Map(); // 进程内缓存
    this.localCacheTTL = new Map(); // 本地缓存TTL
  }
  
  // 多级缓存获取
  async get(key, options = {}) {
    const { useLocal = true, fallback = null } = options;
    
    try {
      // 1. 尝试本地缓存
      if (useLocal && this.isLocalCacheValid(key)) {
        return this.localCache.get(key);
      }
      
      // 2. 尝试Redis缓存
      const redisValue = await this.redis.get(key);
      if (redisValue !== null) {
        const parsedValue = JSON.parse(redisValue);
        
        // 更新本地缓存
        if (useLocal) {
          this.setLocalCache(key, parsedValue, 30); // 30秒本地缓存
        }
        
        return parsedValue;
      }
      
      // 3. 返回fallback
      return fallback;
      
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return fallback;
    }
  }
  
  // 设置缓存
  async set(key, value, ttl = 300) {
    try {
      const serializedValue = JSON.stringify(value);
      
      // 1. 设置Redis缓存
      if (ttl > 0) {
        await this.redis.setex(key, ttl, serializedValue);
      } else {
        await this.redis.set(key, serializedValue);
      }
      
      // 2. 更新本地缓存
      this.setLocalCache(key, value, Math.min(ttl, 30));
      
      return true;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }
  
  // 删除缓存
  async del(key) {
    try {
      // 1. 删除Redis缓存
      await this.redis.del(key);
      
      // 2. 删除本地缓存
      this.localCache.delete(key);
      this.localCacheTTL.delete(key);
      
      return true;
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }
  
  // 批量操作
  async mget(keys) {
    try {
      const pipeline = this.redis.pipeline();
      keys.forEach(key => pipeline.get(key));
      
      const results = await pipeline.exec();
      return results.map(([err, result]) => {
        if (err || result === null) return null;
        try {
          return JSON.parse(result);
        } catch {
          return result;
        }
      });
    } catch (error) {
      console.error('Cache mget error:', error);
      return new Array(keys.length).fill(null);
    }
  }
  
  // 本地缓存管理
  setLocalCache(key, value, ttl) {
    this.localCache.set(key, value);
    this.localCacheTTL.set(key, Date.now() + ttl * 1000);
  }
  
  isLocalCacheValid(key) {
    if (!this.localCache.has(key)) return false;
    
    const expireTime = this.localCacheTTL.get(key);
    if (Date.now() > expireTime) {
      this.localCache.delete(key);
      this.localCacheTTL.delete(key);
      return false;
    }
    
    return true;
  }
}

// 全局缓存管理器实例
let cacheManager = null;
const getCacheManager = () => {
  if (!cacheManager) {
    cacheManager = new CacheManager();
  }
  return cacheManager;
};
```

### 4.2 业务缓存封装
```javascript
// 用户数据缓存
class UserCacheService {
  constructor() {
    this.cache = getCacheManager();
  }
  
  // 获取用户信息
  async getUserInfo(userId) {
    const key = CacheKeyGenerator.userInfo(userId);
    const cached = await this.cache.get(key);
    
    if (cached) {
      return cached;
    }
    
    // 从数据库获取
    const userInfo = await this.fetchUserFromDB(userId);
    if (userInfo) {
      await this.cache.set(key, userInfo, TTL_CONFIG.USER_INFO);
    }
    
    return userInfo;
  }
  
  // 更新用户信息
  async updateUserInfo(userId, userInfo) {
    const key = CacheKeyGenerator.userInfo(userId);
    
    // 1. 更新数据库
    await this.updateUserInDB(userId, userInfo);
    
    // 2. 更新缓存
    await this.cache.set(key, userInfo, TTL_CONFIG.USER_INFO);
    
    // 3. 清除相关缓存
    await this.invalidateUserRelatedCache(userId);
  }
  
  // 获取用户游戏统计
  async getUserStats(userId, category) {
    const key = CacheKeyGenerator.userStats(userId, category);
    const cached = await this.cache.get(key);
    
    if (cached) {
      return cached;
    }
    
    const stats = await this.fetchUserStatsFromDB(userId, category);
    if (stats) {
      await this.cache.set(key, stats, TTL_CONFIG.USER_STATS);
    }
    
    return stats;
  }
  
  // 清除用户相关缓存
  async invalidateUserRelatedCache(userId) {
    const patterns = [
      CacheKeyGenerator.userInfo(userId),
      `u:stats:${userId}:*`,
      `u:rank:${userId}:*`,
      `g:record:${userId}:*`
    ];
    
    for (const pattern of patterns) {
      if (pattern.includes('*')) {
        // 使用SCAN命令删除匹配的键
        await this.deleteByPattern(pattern);
      } else {
        await this.cache.del(pattern);
      }
    }
  }
  
  async deleteByPattern(pattern) {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

// 游戏数据缓存
class GameCacheService {
  constructor() {
    this.cache = getCacheManager();
  }
  
  // 获取题目池
  async getQuestionsPool(category, level, limit = 20) {
    const key = CacheKeyGenerator.questionsPool(category, level);
    const cached = await this.cache.get(key);
    
    if (cached && cached.length >= limit) {
      return this.shuffleArray(cached).slice(0, limit);
    }
    
    // 从数据库获取
    const questions = await this.fetchQuestionsFromDB(category, level, limit * 2);
    if (questions.length > 0) {
      await this.cache.set(key, questions, TTL_CONFIG.QUESTIONS_POOL);
      return questions.slice(0, limit);
    }
    
    return [];
  }
  
  // 预热题目缓存
  async warmupQuestionCache() {
    const categories = ['beijing', 'shanghai', 'guangdong', 'sichuan'];
    const levels = [1, 2, 3, 4, 5];
    
    const warmupTasks = [];
    for (const category of categories) {
      for (const level of levels) {
        warmupTasks.push(
          this.getQuestionsPool(category, level, 50)
        );
      }
    }
    
    await Promise.all(warmupTasks);
    console.log('Question cache warmed up');
  }
  
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// 排行榜缓存
class LeaderboardCacheService {
  constructor() {
    this.cache = getCacheManager();
  }
  
  // 获取排行榜
  async getLeaderboard(type, period, limit = 100) {
    const key = CacheKeyGenerator.leaderboard(type, period);
    const cached = await this.cache.get(key);
    
    if (cached) {
      return cached.slice(0, limit);
    }
    
    // 从数据库计算排行榜
    const leaderboard = await this.calculateLeaderboard(type, period, limit);
    if (leaderboard.length > 0) {
      const ttl = this.getLeaderboardTTL(type);
      await this.cache.set(key, leaderboard, ttl);
    }
    
    return leaderboard;
  }
  
  // 更新排行榜缓存
  async updateLeaderboardCache(type, period) {
    const key = CacheKeyGenerator.leaderboard(type, period);
    const leaderboard = await this.calculateLeaderboard(type, period, 1000);
    
    const ttl = this.getLeaderboardTTL(type);
    await this.cache.set(key, leaderboard, ttl);
    
    return leaderboard;
  }
  
  getLeaderboardTTL(type) {
    switch (type) {
      case 'realtime': return TTL_CONFIG.LEADERBOARD_REALTIME;
      case 'daily': return TTL_CONFIG.LEADERBOARD_DAILY;
      case 'weekly': return TTL_CONFIG.LEADERBOARD_WEEKLY;
      default: return TTL_CONFIG.LEADERBOARD_DAILY;
    }
  }
}
```

## 5. 缓存更新策略

### 5.1 缓存更新模式
```javascript
// 缓存更新策略枚举
const CACHE_UPDATE_STRATEGY = {
  WRITE_THROUGH: 'write_through',     // 写透模式
  WRITE_BEHIND: 'write_behind',       // 写回模式
  CACHE_ASIDE: 'cache_aside',         // 旁路缓存模式
  REFRESH_AHEAD: 'refresh_ahead'      // 预刷新模式
};

// 缓存更新管理器
class CacheUpdateManager {
  constructor() {
    this.cache = getCacheManager();
    this.updateQueue = [];
    this.isProcessing = false;
  }
  
  // 写透模式 - 同时更新缓存和数据库
  async writeThrough(key, value, ttl, updateDBFunc) {
    try {
      // 1. 更新数据库
      await updateDBFunc(value);
      
      // 2. 更新缓存
      await this.cache.set(key, value, ttl);
      
      return true;
    } catch (error) {
      console.error('Write-through update failed:', error);
      // 回滚数据库操作（如果需要）
      throw error;
    }
  }
  
  // 写回模式 - 先更新缓存，异步更新数据库
  async writeBehind(key, value, ttl, updateDBFunc) {
    try {
      // 1. 立即更新缓存
      await this.cache.set(key, value, ttl);
      
      // 2. 异步更新数据库
      this.updateQueue.push({
        key,
        value,
        updateDBFunc,
        timestamp: Date.now()
      });
      
      // 启动批量处理
      this.processUpdateQueue();
      
      return true;
    } catch (error) {
      console.error('Write-behind update failed:', error);
      throw error;
    }
  }
  
  // 旁路缓存模式 - 先更新数据库，再删除缓存
  async cacheAside(key, updateDBFunc) {
    try {
      // 1. 更新数据库
      const result = await updateDBFunc();
      
      // 2. 删除缓存，让下次读取时重新加载
      await this.cache.del(key);
      
      return result;
    } catch (error) {
      console.error('Cache-aside update failed:', error);
      throw error;
    }
  }
  
  // 预刷新模式 - 在过期前刷新缓存
  async refreshAhead(key, loadDataFunc, ttl, refreshThreshold = 0.8) {
    const cached = await this.cache.get(key);
    
    if (cached) {
      // 检查是否需要预刷新
      const keyTTL = await this.redis.ttl(key);
      if (keyTTL > 0 && keyTTL < ttl * refreshThreshold) {
        // 异步预刷新
        this.asyncRefresh(key, loadDataFunc, ttl);
      }
      return cached;
    }
    
    // 缓存不存在，同步加载
    const data = await loadDataFunc();
    if (data) {
      await this.cache.set(key, data, ttl);
    }
    
    return data;
  }
  
  // 异步刷新缓存
  async asyncRefresh(key, loadDataFunc, ttl) {
    try {
      const data = await loadDataFunc();
      if (data) {
        await this.cache.set(key, data, ttl);
      }
    } catch (error) {
      console.error(`Async refresh failed for key ${key}:`, error);
    }
  }
  
  // 处理更新队列
  async processUpdateQueue() {
    if (this.isProcessing || this.updateQueue.length === 0) {
      return;
    }
    
    this.isProcessing = true;
    
    try {
      const batch = this.updateQueue.splice(0, 100); // 批量处理
      
      const updatePromises = batch.map(async (item) => {
        try {
          await item.updateDBFunc(item.value);
        } catch (error) {
          console.error(`DB update failed for key ${item.key}:`, error);
          // 可以选择重新加入队列或记录错误
        }
      });
      
      await Promise.all(updatePromises);
      
    } finally {
      this.isProcessing = false;
      
      // 继续处理剩余队列
      if (this.updateQueue.length > 0) {
        setTimeout(() => this.processUpdateQueue(), 100);
      }
    }
  }
}
```

### 5.2 缓存一致性保证
```javascript
// 分布式锁实现
class DistributedLock {
  constructor(redis) {
    this.redis = redis;
  }
  
  async acquire(lockKey, ttl = 10000, retryDelay = 100, maxRetries = 100) {
    const lockValue = `${Date.now()}-${Math.random()}`;
    
    for (let i = 0; i < maxRetries; i++) {
      const result = await this.redis.set(
        lockKey, 
        lockValue, 
        'PX', ttl, 
        'NX'
      );
      
      if (result === 'OK') {
        return { lockKey, lockValue, ttl };
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
    
    throw new Error(`Failed to acquire lock: ${lockKey}`);
  }
  
  async release(lockKey, lockValue) {
    const script = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;
    
    return await this.redis.eval(script, 1, lockKey, lockValue);
  }
}

// 使用分布式锁确保缓存一致性
class ConsistentCacheManager {
  constructor() {
    this.cache = getCacheManager();
    this.lock = new DistributedLock(getRedisCluster());
  }
  
  async updateWithLock(key, updateFunc) {
    const lockKey = `lock:${key}`;
    let lockInfo = null;
    
    try {
      // 获取分布式锁
      lockInfo = await this.lock.acquire(lockKey, 5000);
      
      // 执行更新操作
      const result = await updateFunc();
      
      return result;
      
    } finally {
      // 释放锁
      if (lockInfo) {
        await this.lock.release(lockInfo.lockKey, lockInfo.lockValue);
      }
    }
  }
}
```

## 6. 缓存监控和优化

### 6.1 缓存监控指标
```javascript
// 缓存性能监控
class CacheMonitor {
  constructor() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalResponseTime: 0,
      requestCount: 0
    };
    
    this.startTime = Date.now();
  }
  
  // 记录缓存命中
  recordHit(responseTime = 0) {
    this.metrics.hits++;
    this.recordResponseTime(responseTime);
  }
  
  // 记录缓存未命中
  recordMiss(responseTime = 0) {
    this.metrics.misses++;
    this.recordResponseTime(responseTime);
  }
  
  // 记录响应时间
  recordResponseTime(responseTime) {
    this.metrics.totalResponseTime += responseTime;
    this.metrics.requestCount++;
  }
  
  // 记录错误
  recordError() {
    this.metrics.errors++;
  }
  
  // 获取缓存统计
  getStats() {
    const totalRequests = this.metrics.hits + this.metrics.misses;
    const hitRate = totalRequests > 0 ? this.metrics.hits / totalRequests : 0;
    const avgResponseTime = this.metrics.requestCount > 0 
      ? this.metrics.totalResponseTime / this.metrics.requestCount 
      : 0;
    
    return {
      hitRate: (hitRate * 100).toFixed(2) + '%',
      totalRequests,
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      errors: this.metrics.errors,
      avgResponseTime: avgResponseTime.toFixed(2) + 'ms',
      uptime: Date.now() - this.startTime
    };
  }
  
  // 重置统计
  reset() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalResponseTime: 0,
      requestCount: 0
    };
    this.startTime = Date.now();
  }
}

// 全局监控实例
const cacheMonitor = new CacheMonitor();

// 监控装饰器
const withMonitoring = (cacheMethod) => {
  return async function(...args) {
    const startTime = Date.now();
    
    try {
      const result = await cacheMethod.apply(this, args);
      const responseTime = Date.now() - startTime;
      
      if (result !== null && result !== undefined) {
        cacheMonitor.recordHit(responseTime);
      } else {
        cacheMonitor.recordMiss(responseTime);
      }
      
      return result;
    } catch (error) {
      cacheMonitor.recordError();
      throw error;
    }
  };
};
```

### 6.2 缓存优化策略
```javascript
// 缓存优化建议生成器
class CacheOptimizer {
  constructor(monitor) {
    this.monitor = monitor;
  }
  
  analyzePerformance() {
    const stats = this.monitor.getStats();
    const suggestions = [];
    
    // 命中率分析
    const hitRate = parseFloat(stats.hitRate);
    if (hitRate < 80) {
      suggestions.push({
        type: 'HIT_RATE',
        message: `缓存命中率过低: ${stats.hitRate}，建议检查缓存键设计和TTL配置`,
        priority: 'HIGH'
      });
    }
    
    // 响应时间分析
    const avgResponseTime = parseFloat(stats.avgResponseTime);
    if (avgResponseTime > 50) {
      suggestions.push({
        type: 'RESPONSE_TIME',
        message: `平均响应时间过长: ${stats.avgResponseTime}，建议优化网络连接或增加本地缓存`,
        priority: 'MEDIUM'
      });
    }
    
    // 错误率分析
    if (stats.errors > stats.totalRequests * 0.01) {
      suggestions.push({
        type: 'ERROR_RATE',
        message: `错误率过高: ${(stats.errors / stats.totalRequests * 100).toFixed(2)}%，建议检查Redis连接稳定性`,
        priority: 'HIGH'
      });
    }
    
    return {
      stats,
      suggestions,
      timestamp: new Date().toISOString()
    };
  }
  
  generateOptimizationPlan() {
    const analysis = this.analyzePerformance();
    
    return {
      currentPerformance: analysis.stats,
      issues: analysis.suggestions,
      recommendations: [
        '启用本地缓存以减少Redis访问',
        '优化缓存键设计，提高数据局部性',
        '实施缓存预热策略，提高命中率',
        '调整TTL策略，平衡数据新鲜度和性能',
        '使用Redis Pipeline减少网络往返',
        '实施缓存分层，提高整体性能'
      ]
    };
  }
}
```

此Redis多层缓存架构设计提供了：

1. **高性能**: 多层缓存策略，目标命中率90%+
2. **高可用**: 集群部署，故障自动切换
3. **低成本**: 智能TTL管理，减少存储成本
4. **可监控**: 完善的监控体系，实时性能分析
5. **易扩展**: 分片设计，支持水平扩展

通过合理的缓存层次设计和更新策略，确保在10万DAU的场景下，API响应时间控制在100ms以内，缓存成本控制在月度预算15美元以内。