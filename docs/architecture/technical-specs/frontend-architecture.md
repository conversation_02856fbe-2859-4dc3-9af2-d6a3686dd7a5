# 前端架构设计

## 🎮 技术栈选型

### 核心框架
- **Cocos Creator 3.8.x**: 游戏引擎，原生支持微信小游戏
- **TypeScript**: 类型安全，提升开发效率和代码质量
- **微信小游戏API**: 原生集成微信生态功能

### 架构优势
- **性能优化**: 原生渲染，60FPS稳定帧率
- **包体优化**: 首包<4MB，总包<20MB
- **跨平台**: 一套代码支持微信小游戏、Web、原生App
- **生态集成**: 深度集成微信支付、分享、用户体系

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────┐
│                    微信小游戏容器                      │
├─────────────────────────────────────────────────────┤
│  展示层 (Presentation Layer)                         │
│  ├── 场景管理 (Scene Manager)                        │
│  ├── UI管理器 (UI Manager)                          │
│  └── 输入控制器 (Input Controller)                   │
├─────────────────────────────────────────────────────┤
│  业务层 (Business Layer)                            │
│  ├── 游戏管理器 (Game Manager)                       │
│  ├── 音频管理器 (Audio Manager)                      │
│  ├── 社交管理器 (Social Manager)                     │
│  ├── 学习管理器 (Learning Manager)                   │
│  └── 🆕 围观管理器 (Watch Manager)                    │
├─────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                             │
│  ├── 网络管理器 (Network Manager)                    │
│  ├── 数据存储 (Data Storage)                        │
│  ├── 缓存管理 (Cache Manager)                       │
│  └── 微信API (WeChat API)                          │
├─────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure Layer)                    │
│  ├── 事件系统 (Event System)                        │
│  ├── 资源管理 (Resource Manager)                     │
│  ├── 工具类 (Utilities)                            │
│  └── 常量定义 (Constants)                           │
└─────────────────────────────────────────────────────┘
```

## 📁 模块设计

### 1. 游戏管理器 (GameManager)
```typescript
class GameManager extends cc.Component {
    // 游戏状态管理
    private gameState: GameState = GameState.MENU;
    private currentQuestion: QuestionData;
    private userScore: number = 0;
    private streakCount: number = 0;
    
    // 游戏配置
    private gameConfig: GameConfig;
    private difficultyLevel: DifficultyLevel = DifficultyLevel.NORMAL;
    
    // 核心方法
    startGame(mode: GameMode): void {}
    loadQuestion(): Promise<QuestionData> {}
    submitAnswer(answer: string): GameResult {}
    calculateScore(isCorrect: boolean, responseTime: number): number {}
    endGame(): void {}
    
    // 状态管理
    private updateGameState(newState: GameState): void {}
    private saveGameProgress(): void {}
    private loadGameProgress(): void {}
}
```

### 2. 音频管理器 (AudioManager)
```typescript
class AudioManager extends cc.Component {
    // 音频缓存池
    private audioCache: Map<string, cc.AudioClip> = new Map();
    private loadingQueue: string[] = [];
    
    // 播放控制
    private currentAudioSource: cc.AudioSource;
    private backgroundMusic: cc.AudioSource;
    
    // 智能预加载策略
    async preloadAudio(audioId: string): Promise<void> {
        if (this.audioCache.has(audioId)) return;
        
        // 分级加载：高优先级音频优先加载
        const priority = this.getAudioPriority(audioId);
        this.loadingQueue.sort((a, b) => 
            this.getAudioPriority(b) - this.getAudioPriority(a)
        );
    }
    
    // 自适应音质
    private selectAudioQuality(): AudioQuality {
        const networkType = wx.getNetworkType();
        const devicePerformance = this.getDevicePerformance();
        
        if (networkType === '2g' || devicePerformance === 'low') {
            return AudioQuality.LOW;
        } else if (networkType === '3g' || devicePerformance === 'medium') {
            return AudioQuality.MEDIUM;
        }
        return AudioQuality.HIGH;
    }
    
    // 音频压缩和优化
    playAudio(audioId: string, options?: AudioOptions): Promise<void> {}
    stopAudio(): void {}
    pauseAudio(): void {}
    resumeAudio(): void {}
    setVolume(volume: number): void {}
}
```

### 3. 社交管理器 (SocialManager)
```typescript
class SocialManager extends cc.Component {
    // 微信分享功能
    async shareToWeChat(shareData: ShareData): Promise<boolean> {
        const shareInfo = {
            title: shareData.title,
            imageUrl: shareData.imageUrl,
            query: `inviteCode=${shareData.inviteCode}&score=${shareData.score}`,
            success: (res) => {
                this.trackShareSuccess(shareData);
                this.rewardUser('share_success');
            },
            fail: (err) => {
                this.trackShareFailure(shareData, err);
            }
        };
        
        return new Promise((resolve) => {
            wx.shareAppMessage(shareInfo);
        });
    }
    
    // 好友挑战
    async challengeFriend(friendId: string, questionSet: string): Promise<void> {
        const challengeData = {
            challengerId: this.getCurrentUserId(),
            targetId: friendId,
            questionSet: questionSet,
            timestamp: Date.now(),
            expireTime: Date.now() + 24 * 60 * 60 * 1000 // 24小时有效
        };
        
        await NetworkManager.getInstance().sendChallenge(challengeData);
    }
    
    // 排行榜
    async getLeaderboard(type: LeaderboardType): Promise<LeaderboardData[]> {}
    async submitScore(score: number, metadata?: any): Promise<void> {}
    
    // 邀请码系统
    generateInviteCode(): string {}
    processInviteCode(code: string): Promise<InviteResult> {}
}
```

### 4. 🆕 围观管理器 (WatchManager)
```typescript
class WatchManager extends cc.Component {
    // 围观连接管理
    private watchConnections: Map<string, WatchConnection> = new Map();
    private currentRoomId: string = null;
    private isWatching: boolean = false;
    
    // 围观状态
    private watcherCount: number = 0;
    private roomActivity: RoomActivity = RoomActivity.LOW;
    private connectionStrategy: ConnectionStrategy = ConnectionStrategy.POLLING;
    
    // 弹幕系统
    private barrageQueue: BarrageMessage[] = [];
    private barrageDisplayLimit: number = 10;
    private barrageSpeed: number = 60; // 像素/秒
    
    // 预测游戏
    private predictionState: PredictionState = PredictionState.CLOSED;
    private userPrediction: string = null;
    private predictionResults: PredictionResults = null;
    
    // 单例模式
    private static instance: WatchManager;
    static getInstance(): WatchManager {
        if (!WatchManager.instance) {
            WatchManager.instance = new WatchManager();
        }
        return WatchManager.instance;
    }
    
    // 核心围观方法
    async startWatching(roomId: string, options?: WatchOptions): Promise<boolean> {
        try {
            // 1. 检查围观权限
            const permission = await this.checkWatchPermission(roomId);
            if (!permission.allowed) {
                this.showMessage(permission.reason);
                return false;
            }
            
            // 2. 建立围观连接
            const connection = await this.establishWatchConnection(roomId, options);
            if (!connection) {
                throw new Error('Failed to establish watch connection');
            }
            
            // 3. 初始化围观界面
            await this.initializeWatchUI(roomId);
            
            // 4. 开始接收实时数据
            this.startReceivingRealtimeData(connection);
            
            // 5. 更新状态
            this.currentRoomId = roomId;
            this.isWatching = true;
            
            // 发送围观事件
            EventManager.emit('watch_started', { roomId: roomId });
            
            return true;
            
        } catch (error) {
            console.error('Failed to start watching:', error);
            this.showError('围观连接失败，请稍后重试');
            return false;
        }
    }
    
    async stopWatching(): Promise<void> {
        if (!this.isWatching) return;
        
        try {
            // 1. 关闭连接
            const connection = this.watchConnections.get(this.currentRoomId);
            if (connection) {
                await connection.close();
                this.watchConnections.delete(this.currentRoomId);
            }
            
            // 2. 清理界面
            await this.cleanupWatchUI();
            
            // 3. 重置状态
            this.resetWatchState();
            
            // 发送围观结束事件
            EventManager.emit('watch_ended', { roomId: this.currentRoomId });
            
        } catch (error) {
            console.error('Error stopping watch:', error);
        }
    }
    
    // 智能连接策略选择
    private async selectConnectionStrategy(roomId: string): Promise<ConnectionStrategy> {
        const roomInfo = await NetworkManager.getInstance().getRoomInfo(roomId);
        const networkInfo = this.getNetworkInfo();
        const deviceInfo = this.getDeviceInfo();
        
        // 成本优先策略
        if (roomInfo.watcherCount > 100) {
            return ConnectionStrategy.POLLING; // 高并发时使用轮询
        }
        
        // 体验优先策略
        if (networkInfo.type === '4g' || networkInfo.type === 'wifi') {
            if (roomInfo.activity === 'high') {
                return ConnectionStrategy.WEBSOCKET; // 高活跃度时使用WebSocket
            } else {
                return ConnectionStrategy.SSE; // 中等活跃度使用SSE
            }
        }
        
        // 兼容性策略
        return ConnectionStrategy.POLLING; // 默认使用轮询
    }
    
    // 建立围观连接
    private async establishWatchConnection(roomId: string, options?: WatchOptions): Promise<WatchConnection> {
        const strategy = await this.selectConnectionStrategy(roomId);
        
        let connection: WatchConnection;
        
        switch (strategy) {
            case ConnectionStrategy.WEBSOCKET:
                connection = new WebSocketWatchConnection(roomId, options);
                break;
            case ConnectionStrategy.SSE:
                connection = new SSEWatchConnection(roomId, options);
                break;
            case ConnectionStrategy.POLLING:
            default:
                connection = new PollingWatchConnection(roomId, options);
                break;
        }
        
        // 设置连接事件监听
        this.setupConnectionEvents(connection);
        
        // 建立连接
        await connection.connect();
        
        // 缓存连接
        this.watchConnections.set(roomId, connection);
        
        return connection;
    }
    
    // 弹幕系统管理
    async sendBarrage(message: string): Promise<boolean> {
        if (!this.isWatching) return false;
        
        try {
            // 1. 本地验证
            const validation = this.validateBarrageMessage(message);
            if (!validation.valid) {
                this.showMessage(validation.reason);
                return false;
            }
            
            // 2. 发送到服务器
            const connection = this.watchConnections.get(this.currentRoomId);
            const result = await connection.sendBarrage({
                content: message,
                type: 'text',
                timestamp: Date.now()
            });
            
            if (result.success) {
                // 3. 本地预显示（乐观更新）
                this.addLocalBarrage(message, true);
                return true;
            } else {
                this.showMessage(result.reason || '弹幕发送失败');
                return false;
            }
            
        } catch (error) {
            console.error('Failed to send barrage:', error);
            this.showMessage('弹幕发送失败，请稍后重试');
            return false;
        }
    }
    
    // 预测游戏管理
    async submitPrediction(answer: string): Promise<boolean> {
        if (!this.isWatching || this.predictionState !== PredictionState.OPEN) {
            return false;
        }
        
        try {
            const connection = this.watchConnections.get(this.currentRoomId);
            const result = await connection.submitPrediction({
                answer: answer,
                timestamp: Date.now(),
                confidence: this.calculatePredictionConfidence(answer)
            });
            
            if (result.success) {
                this.userPrediction = answer;
                this.updatePredictionUI();
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('Failed to submit prediction:', error);
            return false;
        }
    }
    
    // 实时数据处理
    private startReceivingRealtimeData(connection: WatchConnection): void {
        connection.on('game_state_update', (data: GameStateUpdate) => {
            this.handleGameStateUpdate(data);
        });
        
        connection.on('barrage_message', (message: BarrageMessage) => {
            this.handleBarrageMessage(message);
        });
        
        connection.on('prediction_update', (update: PredictionUpdate) => {
            this.handlePredictionUpdate(update);
        });
        
        connection.on('watcher_count_update', (count: number) => {
            this.updateWatcherCount(count);
        });
        
        connection.on('room_activity_change', (activity: RoomActivity) => {
            this.handleRoomActivityChange(activity);
        });
    }
    
    // 智能弹幕显示
    private handleBarrageMessage(message: BarrageMessage): void {
        // 质量过滤
        if (message.quality_score < 0.3) {
            return; // 过滤低质量弹幕
        }
        
        // 添加到显示队列
        this.barrageQueue.push(message);
        
        // 控制显示数量
        if (this.barrageQueue.length > this.barrageDisplayLimit) {
            this.barrageQueue.shift();
        }
        
        // 更新弹幕显示
        this.updateBarrageDisplay();
    }
    
    // 自适应性能优化
    private adaptPerformanceSettings(): void {
        const fps = this.getCurrentFPS();
        const memoryUsage = this.getMemoryUsage();
        
        if (fps < 30 || memoryUsage > 80) {
            // 性能不足，降级设置
            this.barrageDisplayLimit = Math.max(5, this.barrageDisplayLimit - 2);
            this.barrageSpeed = Math.max(30, this.barrageSpeed - 10);
            
            // 切换到更轻量的连接策略
            if (this.connectionStrategy === ConnectionStrategy.WEBSOCKET) {
                this.switchConnectionStrategy(ConnectionStrategy.POLLING);
            }
        } else if (fps > 50 && memoryUsage < 60) {
            // 性能充足，提升设置
            this.barrageDisplayLimit = Math.min(15, this.barrageDisplayLimit + 1);
            this.barrageSpeed = Math.min(80, this.barrageSpeed + 5);
        }
    }
    
    // 围观数据缓存
    private cacheWatchData(roomId: string, data: any): void {
        const cacheKey = `watch_data_${roomId}`;
        const cacheData = {
            data: data,
            timestamp: Date.now(),
            ttl: 300000 // 5分钟TTL
        };
        
        DataStorage.getInstance().setCache(cacheKey, cacheData);
    }
    
    private getCachedWatchData(roomId: string): any {
        const cacheKey = `watch_data_${roomId}`;
        const cached = DataStorage.getInstance().getCache(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
            return cached.data;
        }
        
        return null;
    }
}

// 围观连接抽象基类
abstract class WatchConnection {
    protected roomId: string;
    protected options: WatchOptions;
    protected eventEmitter: EventEmitter;
    protected isConnected: boolean = false;
    
    constructor(roomId: string, options?: WatchOptions) {
        this.roomId = roomId;
        this.options = options || {};
        this.eventEmitter = new EventEmitter();
    }
    
    abstract connect(): Promise<void>;
    abstract close(): Promise<void>;
    abstract sendBarrage(message: BarrageMessage): Promise<SendResult>;
    abstract submitPrediction(prediction: PredictionInput): Promise<SendResult>;
    
    on(event: string, callback: Function): void {
        this.eventEmitter.on(event, callback);
    }
    
    protected emit(event: string, data: any): void {
        this.eventEmitter.emit(event, data);
    }
}

// WebSocket连接实现
class WebSocketWatchConnection extends WatchConnection {
    private ws: WebSocket;
    private heartbeatInterval: number;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    
    async connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            const wsUrl = `wss://api.hometowndialect.com/watch/${this.roomId}`;
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                resolve();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                this.isConnected = false;
                this.stopHeartbeat();
                this.attemptReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                reject(error);
            };
        });
    }
    
    async close(): Promise<void> {
        if (this.ws) {
            this.ws.close();
            this.stopHeartbeat();
        }
    }
    
    async sendBarrage(message: BarrageMessage): Promise<SendResult> {
        if (!this.isConnected) {
            return { success: false, reason: 'Connection not established' };
        }
        
        const payload = {
            type: 'barrage',
            data: message
        };
        
        this.ws.send(JSON.stringify(payload));
        return { success: true };
    }
    
    async submitPrediction(prediction: PredictionInput): Promise<SendResult> {
        if (!this.isConnected) {
            return { success: false, reason: 'Connection not established' };
        }
        
        const payload = {
            type: 'prediction',
            data: prediction
        };
        
        this.ws.send(JSON.stringify(payload));
        return { success: true };
    }
    
    private handleMessage(message: any): void {
        switch (message.type) {
            case 'game_state_update':
                this.emit('game_state_update', message.data);
                break;
            case 'barrage_message':
                this.emit('barrage_message', message.data);
                break;
            case 'prediction_update':
                this.emit('prediction_update', message.data);
                break;
            case 'watcher_count':
                this.emit('watcher_count_update', message.data.count);
                break;
        }
    }
    
    private startHeartbeat(): void {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // 30秒心跳
    }
    
    private stopHeartbeat(): void {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
    }
    
    private attemptReconnect(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.pow(2, this.reconnectAttempts) * 1000; // 指数退避
            
            setTimeout(() => {
                console.log(`Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
                this.connect().catch(console.error);
            }, delay);
        }
    }
}

// 轮询连接实现
class PollingWatchConnection extends WatchConnection {
    private pollingInterval: number;
    private pollingFrequency: number = 1000; // 1秒轮询
    private lastMessageId: string = '';
    
    async connect(): Promise<void> {
        this.isConnected = true;
        this.startPolling();
    }
    
    async close(): Promise<void> {
        this.isConnected = false;
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
    }
    
    async sendBarrage(message: BarrageMessage): Promise<SendResult> {
        try {
            const response = await NetworkManager.getInstance().request({
                url: `/watch/${this.roomId}/barrage`,
                method: 'POST',
                data: message
            });
            
            return { success: response.code === 0 };
        } catch (error) {
            return { success: false, reason: error.message };
        }
    }
    
    async submitPrediction(prediction: PredictionInput): Promise<SendResult> {
        try {
            const response = await NetworkManager.getInstance().request({
                url: `/watch/${this.roomId}/prediction`,
                method: 'POST',
                data: prediction
            });
            
            return { success: response.code === 0 };
        } catch (error) {
            return { success: false, reason: error.message };
        }
    }
    
    private startPolling(): void {
        this.pollingInterval = setInterval(async () => {
            try {
                const response = await NetworkManager.getInstance().request({
                    url: `/watch/${this.roomId}/updates`,
                    method: 'GET',
                    params: { lastMessageId: this.lastMessageId }
                });
                
                if (response.code === 0 && response.data.updates) {
                    this.processUpdates(response.data.updates);
                }
            } catch (error) {
                console.error('Polling error:', error);
            }
        }, this.pollingFrequency);
    }
    
    private processUpdates(updates: any[]): void {
        for (const update of updates) {
            this.lastMessageId = update.id;
            
            switch (update.type) {
                case 'game_state':
                    this.emit('game_state_update', update.data);
                    break;
                case 'barrage':
                    this.emit('barrage_message', update.data);
                    break;
                case 'prediction':
                    this.emit('prediction_update', update.data);
                    break;
                case 'watcher_count':
                    this.emit('watcher_count_update', update.data.count);
                    break;
            }
        }
    }
}
```

### 5. 网络管理器 (NetworkManager)
```typescript
class NetworkManager extends cc.Component {
    private baseURL: string;
    private authToken: string;
    private requestQueue: RequestQueue = new RequestQueue();
    
    // 单例模式
    private static instance: NetworkManager;
    static getInstance(): NetworkManager {}
    
    // HTTP请求封装
    async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
        // 请求去重
        const requestKey = this.generateRequestKey(config);
        if (this.requestQueue.has(requestKey)) {
            return this.requestQueue.get(requestKey);
        }
        
        // 添加认证头
        config.headers = {
            ...config.headers,
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
        };
        
        // 请求重试机制
        return this.retryRequest(config, 3);
    }
    
    // 专用API方法
    async login(code: string): Promise<LoginResponse> {}
    async getQuestions(params: QuestionParams): Promise<QuestionResponse> {}
    async submitGameResult(result: GameResult): Promise<void> {}
    async uploadAudio(audioBlob: Blob): Promise<UploadResponse> {}
    
    // 网络状态监控
    private setupNetworkMonitoring(): void {
        wx.onNetworkStatusChange((res) => {
            if (!res.isConnected) {
                this.handleOfflineMode();
            } else {
                this.handleOnlineMode();
            }
        });
    }
}
```

## 🎨 UI架构设计

### UI组件体系
```typescript
// 基础UI组件
abstract class BaseUIComponent extends cc.Component {
    protected isVisible: boolean = false;
    protected animationDuration: number = 0.3;
    
    show(animated: boolean = true): Promise<void> {}
    hide(animated: boolean = true): Promise<void> {}
    setInteractable(enabled: boolean): void {}
}

// 游戏界面组件
class GameUIComponent extends BaseUIComponent {
    @property(cc.Label)
    questionLabel: cc.Label = null;
    
    @property([cc.Button])
    answerButtons: cc.Button[] = [];
    
    @property(cc.ProgressBar)
    timeProgress: cc.ProgressBar = null;
    
    @property(cc.Label)
    scoreLabel: cc.Label = null;
    
    // 答题界面逻辑
    displayQuestion(question: QuestionData): void {
        this.questionLabel.string = question.text;
        this.setupAnswerButtons(question.options);
        this.startTimer(question.timeLimit);
    }
    
    private setupAnswerButtons(options: AnswerOption[]): void {
        options.forEach((option, index) => {
            const button = this.answerButtons[index];
            button.getComponentInChildren(cc.Label).string = option.text;
            button.node.on('click', () => this.onAnswerSelected(option), this);
        });
    }
}

// 🆕 围观界面组件
class WatchUIComponent extends BaseUIComponent {
    @property(cc.Node)
    playerInfoPanel: cc.Node = null;
    
    @property(cc.Label)
    playerNameLabel: cc.Label = null;
    
    @property(cc.Label)
    playerLevelLabel: cc.Label = null;
    
    @property(cc.Label)
    watcherCountLabel: cc.Label = null;
    
    @property(cc.Node)
    barrageContainer: cc.Node = null;
    
    @property(cc.EditBox)
    barrageInput: cc.EditBox = null;
    
    @property(cc.Button)
    sendBarrageButton: cc.Button = null;
    
    @property(cc.Node)
    predictionPanel: cc.Node = null;
    
    @property([cc.Button])
    predictionButtons: cc.Button[] = [];
    
    @property(cc.Label)
    predictionResultLabel: cc.Label = null;
    
    @property(cc.Node)
    gameStateDisplay: cc.Node = null;
    
    // 围观界面初始化
    async initializeWatchUI(roomInfo: RoomInfo): Promise<void> {
        // 1. 设置玩家信息
        this.setupPlayerInfo(roomInfo.playerInfo);
        
        // 2. 初始化弹幕系统
        this.initializeBarrageSystem();
        
        // 3. 设置预测面板
        this.setupPredictionPanel(roomInfo.gameInfo);
        
        // 4. 绑定事件监听
        this.bindWatchEvents();
        
        // 5. 开启UI动画
        await this.playEnterAnimation();
    }
    
    private setupPlayerInfo(playerInfo: PlayerInfo): void {
        this.playerNameLabel.string = playerInfo.nickname;
        this.playerLevelLabel.string = `LV${playerInfo.level}`;
        
        // 设置玩家头像
        this.loadPlayerAvatar(playerInfo.avatarUrl);
        
        // 设置玩家状态指示器
        this.updatePlayerStatus(playerInfo.status);
    }
    
    private initializeBarrageSystem(): void {
        // 设置弹幕输入框
        this.barrageInput.placeholder = "发送弹幕...";
        this.barrageInput.maxLength = 50;
        
        // 绑定发送按钮事件
        this.sendBarrageButton.node.on('click', this.onSendBarrage, this);
        
        // 设置弹幕容器
        this.setupBarrageContainer();
        
        // 启动弹幕动画系统
        this.startBarrageAnimation();
    }
    
    private setupPredictionPanel(gameInfo: GameInfo): void {
        if (!gameInfo || !gameInfo.allowPrediction) {
            this.predictionPanel.active = false;
            return;
        }
        
        // 根据题目选项设置预测按钮
        gameInfo.options.forEach((option, index) => {
            if (index < this.predictionButtons.length) {
                const button = this.predictionButtons[index];
                button.getComponentInChildren(cc.Label).string = option.text;
                button.node.on('click', () => this.onPredictionSelected(option.value), this);
                button.node.active = true;
            }
        });
        
        // 隐藏多余的按钮
        for (let i = gameInfo.options.length; i < this.predictionButtons.length; i++) {
            this.predictionButtons[i].node.active = false;
        }
    }
    
    // 弹幕显示管理
    displayBarrage(message: BarrageMessage): void {
        const barrageNode = this.createBarrageNode(message);
        this.barrageContainer.addChild(barrageNode);
        
        // 启动弹幕动画
        this.animateBarrage(barrageNode);
        
        // 管理弹幕数量
        this.manageBarrageCount();
    }
    
    private createBarrageNode(message: BarrageMessage): cc.Node {
        const node = new cc.Node('barrage');
        const label = node.addComponent(cc.Label);
        
        // 设置弹幕文本
        label.string = `${message.userNickname}: ${message.content}`;
        label.fontSize = 24;
        label.overflow = cc.Label.Overflow.NONE;
        
        // 根据用户等级设置颜色
        const color = this.getBarrageColor(message.userLevel);
        label.color = color;
        
        // 添加背景
        this.addBarrageBackground(node);
        
        return node;
    }
    
    private animateBarrage(barrageNode: cc.Node): void {
        const containerWidth = this.barrageContainer.width;
        const nodeWidth = barrageNode.width;
        
        // 设置初始位置（右侧屏幕外）
        barrageNode.x = containerWidth + nodeWidth / 2;
        barrageNode.y = this.getRandomBarrageY();
        
        // 创建移动动画
        const moveAction = cc.moveTo(
            this.calculateBarrageDuration(nodeWidth + containerWidth),
            -nodeWidth / 2,
            barrageNode.y
        );
        
        // 动画完成后销毁节点
        const destroyAction = cc.callFunc(() => {
            barrageNode.destroy();
        });
        
        barrageNode.runAction(cc.sequence(moveAction, destroyAction));
    }
    
    // 预测游戏管理
    private onPredictionSelected(answer: string): void {
        // 禁用所有预测按钮
        this.predictionButtons.forEach(button => {
            button.interactable = false;
        });
        
        // 高亮选择的预测
        const selectedButton = this.predictionButtons.find(
            button => button.getComponentInChildren(cc.Label).string === answer
        );
        if (selectedButton) {
            this.highlightPredictionButton(selectedButton);
        }
        
        // 提交预测
        WatchManager.getInstance().submitPrediction(answer);
        
        // 显示预测状态
        this.showPredictionStatus('已提交预测');
    }
    
    updatePredictionResults(results: PredictionResults): void {
        if (!results) return;
        
        // 显示预测结果
        const isCorrect = results.userResult?.correct || false;
        const resultText = isCorrect ? 
            `预测正确！获得 ${results.userResult.score} 积分` : 
            '预测错误，下次加油！';
        
        this.predictionResultLabel.string = resultText;
        this.predictionResultLabel.node.active = true;
        
        // 播放结果动画
        this.playPredictionResultAnimation(isCorrect);
        
        // 更新预测统计显示
        this.updatePredictionStats(results);
    }
    
    // 围观状态更新
    updateWatcherCount(count: number): void {
        this.watcherCountLabel.string = `${count}人围观`;
        
        // 根据围观人数调整UI显示
        if (count > 100) {
            this.enableHighConcurrencyMode();
        } else {
            this.disableHighConcurrencyMode();
        }
    }
    
    updateGameState(gameState: GameStateUpdate): void {
        switch (gameState.phase) {
            case 'question_playing':
                this.onQuestionPlaying(gameState);
                break;
            case 'answer_thinking':
                this.onAnswerThinking(gameState);
                break;
            case 'answer_revealing':
                this.onAnswerRevealing(gameState);
                break;
            case 'game_ended':
                this.onGameEnded(gameState);
                break;
        }
    }
    
    private onQuestionPlaying(gameState: GameStateUpdate): void {
        // 开启预测
        this.enablePrediction();
        
        // 重置UI状态
        this.resetPredictionPanel();
        
        // 显示题目信息
        this.displayQuestionInfo(gameState.questionInfo);
    }
    
    private onAnswerRevealing(gameState: GameStateUpdate): void {
        // 关闭预测
        this.disablePrediction();
        
        // 显示正确答案
        this.highlightCorrectAnswer(gameState.correctAnswer);
        
        // 等待预测结果
        this.showPredictionWaiting();
    }
    
    // 性能优化
    private enableHighConcurrencyMode(): void {
        // 减少弹幕显示数量
        this.barrageDisplayLimit = 5;
        
        // 降低动画帧率
        this.barrageAnimationFPS = 30;
        
        // 启用弹幕合并
        this.enableBarrageMerging = true;
    }
    
    private optimizeForLowPerformance(): void {
        // 禁用部分动画效果
        this.disableNonEssentialAnimations();
        
        // 减少弹幕特效
        this.simplifyBarrageEffects();
        
        // 降低UI更新频率
        this.reduceUIUpdateFrequency();
    }
    
    // 事件处理
    private bindWatchEvents(): void {
        // 网络事件
        EventManager.on('watch_connection_lost', this.onConnectionLost, this);
        EventManager.on('watch_connection_restored', this.onConnectionRestored, this);
        
        // 游戏事件
        EventManager.on('game_state_changed', this.updateGameState, this);
        EventManager.on('prediction_results', this.updatePredictionResults, this);
        
        // UI事件
        EventManager.on('barrage_received', this.displayBarrage, this);
        EventManager.on('watcher_count_changed', this.updateWatcherCount, this);
    }
    
    private onSendBarrage(): void {
        const message = this.barrageInput.string.trim();
        if (!message) return;
        
        // 发送弹幕
        WatchManager.getInstance().sendBarrage(message).then(success => {
            if (success) {
                this.barrageInput.string = '';
                this.showSendSuccess();
            } else {
                this.showSendError();
            }
        });
    }
    
    // 界面动画
    private async playEnterAnimation(): Promise<void> {
        // 玩家信息面板从上方滑入
        const playerPanelAction = cc.sequence(
            cc.moveTo(0.3, cc.v2(0, 0)).easing(cc.easeBackOut())
        );
        
        // 弹幕容器从左侧滑入
        const barrageAction = cc.sequence(
            cc.moveTo(0.4, cc.v2(0, 0)).easing(cc.easeBackOut())
        );
        
        // 预测面板从下方滑入
        const predictionAction = cc.sequence(
            cc.moveTo(0.5, cc.v2(0, 0)).easing(cc.easeBackOut())
        );
        
        // 并行播放动画
        return new Promise(resolve => {
            let completed = 0;
            const onComplete = () => {
                completed++;
                if (completed === 3) {
                    resolve();
                }
            };
            
            this.playerInfoPanel.runAction(cc.sequence(playerPanelAction, cc.callFunc(onComplete)));
            this.barrageContainer.runAction(cc.sequence(barrageAction, cc.callFunc(onComplete)));
            this.predictionPanel.runAction(cc.sequence(predictionAction, cc.callFunc(onComplete)));
        });
    }
}
```

### 场景管理
```typescript
enum SceneType {
    LOADING = 'LoadingScene',
    MAIN_MENU = 'MainScene',
    GAME = 'GameScene',
    RESULT = 'ResultScene',
    SETTINGS = 'SettingsScene',
    LEADERBOARD = 'LeaderboardScene',
    🆕 WATCH = 'WatchScene'        // 围观场景
}

class SceneManager extends cc.Component {
    private currentScene: SceneType;
    private sceneStack: SceneType[] = [];
    
    async loadScene(sceneType: SceneType, showLoading: boolean = true): Promise<void> {
        if (showLoading) {
            await this.showLoadingScreen();
        }
        
        // 预加载场景资源
        await this.preloadSceneAssets(sceneType);
        
        // 场景切换动画
        await this.transitionToScene(sceneType);
        
        this.currentScene = sceneType;
    }
    
    private async transitionToScene(sceneType: SceneType): Promise<void> {
        // 淡出当前场景
        await this.fadeOutCurrentScene();
        
        // 加载新场景
        await cc.director.loadScene(sceneType);
        
        // 淡入新场景
        await this.fadeInNewScene();
    }
}
```

## 📱 性能优化策略

### 1. 渲染优化
```typescript
class RenderOptimizer {
    // 对象池管理
    private objectPools: Map<string, cc.NodePool> = new Map();
    
    // 批处理渲染
    private batchRenderer: BatchRenderer = new BatchRenderer();
    
    // 动态合批
    setupDynamicBatching(): void {
        // 相同材质的UI元素自动合批
        cc.dynamicAtlasManager.enabled = true;
        cc.dynamicAtlasManager.maxFrameSize = 512;
    }
    
    // 减少绘制调用
    optimizeDrawCalls(): void {
        // 使用图集纹理
        // 合并静态UI元素
        // 减少透明层级
    }
}
```

### 2. 内存优化
```typescript
class MemoryManager {
    // 纹理压缩
    private compressTextures(): void {
        // iOS: PVRTC格式
        // Android: ETC2格式
        // 自动选择最优压缩格式
    }
    
    // 音频优化
    private optimizeAudio(): void {
        // 背景音乐: OGG/MP3格式
        // 音效: WAV格式（短音频）
        // 动态加载和释放
    }
    
    // 内存监控
    private monitorMemoryUsage(): void {
        const memInfo = cc.sys.garbageCollect();
        if (memInfo.usedSize > this.memoryThreshold) {
            this.triggerGarbageCollection();
        }
    }
}
```

### 3. 加载优化
```typescript
class LoadingOptimizer {
    // 分包加载
    async loadSubpackage(name: string): Promise<void> {
        return new Promise((resolve, reject) => {
            wx.loadSubpackage({
                name: name,
                success: resolve,
                fail: reject
            });
        });
    }
    
    // 资源预加载
    private async preloadCriticalAssets(): Promise<void> {
        const criticalAssets = [
            'textures/ui/main_ui',
            'audio/bgm/main_theme',
            'prefabs/game_ui'
        ];
        
        await Promise.all(
            criticalAssets.map(asset => this.loadAsset(asset))
        );
    }
    
    // 渐进式加载
    private setupProgressiveLoading(): void {
        // 首屏必需资源优先加载
        // 后续资源按需加载
        // 预测用户行为，提前加载
    }
}
```

## 🔧 开发工具和流程

### 1. 开发环境配置
```json
{
  "cocosCreator": "3.8.x",
  "nodejs": ">=16.0.0",
  "typescript": "^4.8.0",
  "eslint": "^8.0.0",
  "prettier": "^2.8.0"
}
```

### 2. 代码规范
```typescript
// TSLint配置
{
  "extends": ["@typescript-eslint/recommended"],
  "rules": {
    "prefer-const": "error",
    "no-var": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn"
  }
}
```

### 3. 构建流程
```yaml
# GitHub Actions CI/CD
name: Frontend Build
on:
  push:
    branches: [main, develop]
    
jobs:
  build:
    steps:
      - name: Checkout code
      - name: Setup Node.js
      - name: Install dependencies
      - name: Run tests
      - name: Build project
      - name: Deploy to staging
```

## 📊 监控和分析

### 1. 性能监控
```typescript
class PerformanceMonitor {
    // FPS监控
    private monitorFPS(): void {
        cc.director.on(cc.Director.EVENT_AFTER_DRAW, () => {
            const fps = cc.game.getFrameRate();
            if (fps < 30) {
                this.reportPerformanceIssue('low_fps', { fps });
            }
        });
    }
    
    // 内存监控
    private monitorMemory(): void {
        setInterval(() => {
            const memInfo = cc.sys.garbageCollect();
            this.reportMetric('memory_usage', memInfo.usedSize);
        }, 5000);
    }
    
    // 加载时间监控
    private trackLoadingTime(sceneName: string, startTime: number): void {
        const endTime = Date.now();
        const loadTime = endTime - startTime;
        this.reportMetric('scene_load_time', loadTime, { scene: sceneName });
    }
}
```

### 2. 用户行为分析
```typescript
class AnalyticsManager {
    // 游戏事件追踪
    trackGameEvent(eventName: string, parameters: any): void {
        const eventData = {
            event: eventName,
            parameters: parameters,
            timestamp: Date.now(),
            userId: this.getCurrentUserId(),
            sessionId: this.getSessionId()
        };
        
        this.sendToAnalytics(eventData);
    }
    
    // 关键指标
    trackKPI(kpiName: string, value: number): void {
        const kpiData = {
            name: kpiName,
            value: value,
            timestamp: Date.now()
        };
        
        this.sendKPIData(kpiData);
    }
}
```

## 🔐 安全考量

### 1. 代码保护
- **代码混淆**: TypeScript编译后进行代码混淆
- **资源加密**: 关键资源文件加密存储
- **反调试**: 添加反调试检测机制

### 2. 数据安全
- **本地存储加密**: 敏感数据加密存储
- **通信加密**: 所有API调用使用HTTPS
- **输入验证**: 严格验证用户输入数据

### 3. 防作弊机制
```typescript
class AntiCheatSystem {
    // 时间验证
    private validateAnswerTime(startTime: number, endTime: number): boolean {
        const responseTime = endTime - startTime;
        return responseTime >= this.minResponseTime && responseTime <= this.maxResponseTime;
    }
    
    // 行为模式检测
    private detectSuspiciousBehavior(userActions: UserAction[]): boolean {
        // 检测异常的答题模式
        // 检测连续正确率异常
        // 检测响应时间异常
    }
    
    // 设备指纹
    private generateDeviceFingerprint(): string {
        const deviceInfo = wx.getSystemInfoSync();
        return this.hashDeviceInfo(deviceInfo);
    }
}
```

---

**文档版本**: v1.0  
**最后更新**: 2024-07-30  
**负责团队**: architect-agent  
**审核状态**: ✅ 已审核通过