# 数据库架构设计方案

## 1. 总体设计原则

### 1.1 设计目标
- **性能优先**: 查询响应时间 < 100ms
- **成本控制**: TencentDB Serverless按量计费优化
- **数据一致性**: 游戏核心数据强一致性
- **扩展性**: 支持千万级用户数据

### 1.2 技术选型
- **数据库**: TencentDB Serverless MySQL 8.0
- **字符集**: utf8mb4 (支持emoji)
- **引擎**: InnoDB
- **时区**: Asia/Shanghai

## 2. 数据库表结构设计

### 2.1 用户相关表

#### 用户基础信息表 (users)
```sql
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(100) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint unsigned DEFAULT 0 COMMENT '性别: 0未知 1男 2女',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言',
  `total_score` int unsigned DEFAULT 0 COMMENT '总积分',
  `total_games` int unsigned DEFAULT 0 COMMENT '总游戏次数',
  `win_games` int unsigned DEFAULT 0 COMMENT '胜利次数',
  `max_streak` int unsigned DEFAULT 0 COMMENT '最大连胜数',
  `current_level` int unsigned DEFAULT 1 COMMENT '当前等级',
  `status` tinyint unsigned DEFAULT 1 COMMENT '状态: 0禁用 1正常',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_total_score` (`total_score` DESC),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_score` (`status`, `total_score` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';
```

#### 用户游戏统计表 (user_game_stats)
```sql
CREATE TABLE `user_game_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `dialect_category` varchar(50) NOT NULL COMMENT '方言类别',
  `total_questions` int unsigned DEFAULT 0 COMMENT '总题目数',
  `correct_answers` int unsigned DEFAULT 0 COMMENT '正确答案数',
  `accuracy_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '准确率',
  `best_time` int unsigned DEFAULT 0 COMMENT '最佳用时(秒)',
  `avg_time` decimal(8,4) DEFAULT 0.0000 COMMENT '平均用时(秒)',
  `last_played_at` timestamp NULL DEFAULT NULL COMMENT '最后游戏时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_dialect` (`user_id`, `dialect_category`),
  KEY `idx_accuracy` (`accuracy_rate` DESC),
  KEY `idx_last_played` (`last_played_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户游戏统计表';
```

### 2.2 游戏内容表

#### 方言题目表 (dialect_questions)
```sql
CREATE TABLE `dialect_questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `category` varchar(50) NOT NULL COMMENT '方言类别',
  `region` varchar(100) NOT NULL COMMENT '地区',
  `question_text` text NOT NULL COMMENT '题目文本',
  `question_type` tinyint unsigned DEFAULT 1 COMMENT '题目类型: 1听音辨字 2选择题 3填空题',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频文件URL',
  `audio_duration` int unsigned DEFAULT 0 COMMENT '音频时长(秒)',
  `difficulty_level` tinyint unsigned DEFAULT 1 COMMENT '难度等级: 1-5',
  `standard_answer` varchar(200) NOT NULL COMMENT '标准答案',
  `answer_options` json DEFAULT NULL COMMENT '选择题选项',
  `explanation` text DEFAULT NULL COMMENT '题目解释',
  `usage_count` int unsigned DEFAULT 0 COMMENT '使用次数',
  `correct_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '正确率',
  `avg_answer_time` decimal(8,4) DEFAULT 0.0000 COMMENT '平均答题时间',
  `status` tinyint unsigned DEFAULT 1 COMMENT '状态: 0下线 1上线',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_region` (`category`, `region`),
  KEY `idx_difficulty` (`difficulty_level`),
  KEY `idx_status_difficulty` (`status`, `difficulty_level`),
  KEY `idx_correct_rate` (`correct_rate` DESC),
  KEY `idx_usage_count` (`usage_count` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='方言题目表';
```

#### 游戏记录表 (game_records)
```sql
CREATE TABLE `game_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `game_session_id` varchar(64) NOT NULL COMMENT '游戏会话ID',
  `question_id` bigint unsigned NOT NULL COMMENT '题目ID',
  `user_answer` varchar(200) DEFAULT NULL COMMENT '用户答案',
  `is_correct` tinyint unsigned DEFAULT 0 COMMENT '是否正确: 0错误 1正确',
  `answer_time` int unsigned DEFAULT 0 COMMENT '答题用时(秒)',
  `score_earned` int unsigned DEFAULT 0 COMMENT '获得积分',
  `streak_count` int unsigned DEFAULT 0 COMMENT '连击数',
  `hint_used` tinyint unsigned DEFAULT 0 COMMENT '是否使用提示: 0否 1是',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_game_session` (`game_session_id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_created` (`user_id`, `created_at` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏记录表';
```

### 2.3 排行榜表

#### 排行榜表 (leaderboards)
```sql
CREATE TABLE `leaderboards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '排行榜类型: overall总榜 weekly周榜 monthly月榜',
  `category` varchar(50) DEFAULT NULL COMMENT '方言类别(可选)',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `score` int unsigned NOT NULL COMMENT '积分',
  `rank` int unsigned NOT NULL COMMENT '排名',
  `extra_data` json DEFAULT NULL COMMENT '额外数据',
  `period` varchar(20) NOT NULL COMMENT '周期标识: 2024-01, 2024-W01',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_period_user` (`type`, `period`, `user_id`),
  KEY `idx_type_period_rank` (`type`, `period`, `rank`),
  KEY `idx_type_category_rank` (`type`, `category`, `rank`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜表';
```

### 2.4 社交功能表

#### 用户关系表 (user_relationships)
```sql
CREATE TABLE `user_relationships` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `friend_id` bigint unsigned NOT NULL COMMENT '好友ID',
  `relationship_type` tinyint unsigned DEFAULT 1 COMMENT '关系类型: 1好友 2黑名单',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_friend` (`user_id`, `friend_id`),
  KEY `idx_friend_id` (`friend_id`),
  KEY `idx_type` (`relationship_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关系表';
```

#### 分享记录表 (share_records)
```sql
CREATE TABLE `share_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '分享用户ID',
  `share_type` varchar(50) NOT NULL COMMENT '分享类型: score成绩 invite邀请 achievement成就',
  `share_content` json NOT NULL COMMENT '分享内容',
  `platform` varchar(20) DEFAULT 'wechat' COMMENT '分享平台',
  `click_count` int unsigned DEFAULT 0 COMMENT '点击次数',
  `conversion_count` int unsigned DEFAULT 0 COMMENT '转化次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';
```

## 3. 索引优化策略

### 3.1 核心查询索引

#### 高频查询索引
```sql
-- 用户登录查询 (openid)
CREATE INDEX idx_users_openid ON users(openid);

-- 排行榜查询 (按积分排序)
CREATE INDEX idx_users_score_status ON users(status, total_score DESC);

-- 题目获取查询 (按类别和难度)
CREATE INDEX idx_questions_category_difficulty ON dialect_questions(status, category, difficulty_level);

-- 游戏记录查询 (按用户和时间)
CREATE INDEX idx_game_records_user_time ON game_records(user_id, created_at DESC);

-- 排行榜查询 (按类型、周期、排名)
CREATE INDEX idx_leaderboard_type_period_rank ON leaderboards(type, period, rank);
```

#### 复合索引设计原则
```sql
-- 遵循最左前缀原则
-- 选择性高的字段放前面
-- 常用查询条件组合

-- 示例：用户游戏统计查询
CREATE INDEX idx_user_stats_composite ON user_game_stats(
  user_id,               -- 精确匹配
  dialect_category,      -- 分组条件  
  accuracy_rate DESC     -- 排序字段
);
```

### 3.2 分区策略

#### 按时间分区 - 游戏记录表
```sql
-- 按月分区，自动清理历史数据
ALTER TABLE game_records PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202401 VALUES LESS THAN (202402),
  PARTITION p202402 VALUES LESS THAN (202403),
  PARTITION p202403 VALUES LESS THAN (202404),
  -- 自动添加新分区的脚本
  PARTITION pmax VALUES LESS THAN MAXVALUE
);

-- 分区维护脚本
DELIMITER $$
CREATE EVENT auto_add_partition
ON SCHEDULE EVERY 1 MONTH
STARTS '2024-01-01 00:00:00'
DO
BEGIN
  SET @sql = CONCAT('ALTER TABLE game_records ADD PARTITION (PARTITION p', 
                   DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 MONTH), '%Y%m'),
                   ' VALUES LESS THAN (', 
                   YEAR(DATE_ADD(NOW(), INTERVAL 1 MONTH)) * 100 + MONTH(DATE_ADD(NOW(), INTERVAL 1 MONTH)),
                   '))');
  PREPARE stmt FROM @sql;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;
```

#### 按用户ID分表策略 (预留)
```sql
-- 当用户量达到百万级时启用
-- 按用户ID取模分表，支持水平扩展

-- 用户表分表示例 (按1000万分8张表)
CREATE TABLE users_0 LIKE users;
CREATE TABLE users_1 LIKE users;
-- ... 其他分表

-- 分表路由函数
DELIMITER $$
CREATE FUNCTION get_user_table_suffix(user_id BIGINT) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
  RETURN user_id % 8;
END$$
DELIMITER ;
```

## 4. 查询优化策略

### 4.1 常用查询优化

#### 排行榜查询优化
```sql
-- 原始查询 (慢)
SELECT u.nickname, u.avatar_url, u.total_score 
FROM users u 
WHERE u.status = 1 
ORDER BY u.total_score DESC 
LIMIT 100;

-- 优化后查询 (快)
-- 1. 使用覆盖索引
CREATE INDEX idx_users_leaderboard ON users(status, total_score DESC, nickname, avatar_url);

-- 2. 分页优化 (避免OFFSET)
SELECT u.nickname, u.avatar_url, u.total_score 
FROM users u 
WHERE u.status = 1 AND u.total_score < ? -- 上一页最小分数
ORDER BY u.total_score DESC 
LIMIT 20;
```

#### 用户游戏历史查询
```sql
-- 优化前
SELECT * FROM game_records WHERE user_id = ? ORDER BY created_at DESC LIMIT 20;

-- 优化后：只查询必要字段，使用覆盖索引
CREATE INDEX idx_game_records_user_summary ON game_records(
  user_id, created_at DESC, question_id, is_correct, score_earned
);

SELECT question_id, is_correct, score_earned, created_at 
FROM game_records 
WHERE user_id = ? 
ORDER BY created_at DESC 
LIMIT 20;
```

### 4.2 聚合查询优化

#### 实时统计查询
```sql
-- 用户统计信息更新 (触发器方式)
DELIMITER $$
CREATE TRIGGER update_user_stats 
AFTER INSERT ON game_records
FOR EACH ROW
BEGIN
  -- 更新用户总积分和游戏次数
  UPDATE users SET 
    total_score = total_score + NEW.score_earned,
    total_games = total_games + 1,
    win_games = CASE WHEN NEW.is_correct = 1 THEN win_games + 1 ELSE win_games END
  WHERE id = NEW.user_id;
  
  -- 更新分类统计
  INSERT INTO user_game_stats (user_id, dialect_category, total_questions, correct_answers)
  VALUES (NEW.user_id, 
          (SELECT category FROM dialect_questions WHERE id = NEW.question_id),
          1, 
          NEW.is_correct)
  ON DUPLICATE KEY UPDATE
    total_questions = total_questions + 1,
    correct_answers = correct_answers + NEW.is_correct,
    accuracy_rate = correct_answers / total_questions;
END$$
DELIMITER ;
```

## 5. 数据库连接配置

### 5.1 TencentDB Serverless配置
```javascript
// 数据库连接配置
const DB_CONFIG = {
  // 基础配置
  host: process.env.DB_HOST,
  port: 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  
  // Serverless优化配置
  connectionLimit: 10,        // 连接池大小
  acquireTimeout: 60000,      // 获取连接超时
  timeout: 60000,            // 查询超时
  reconnect: true,           // 自动重连
  
  // 性能优化
  charset: 'utf8mb4',
  timezone: '+08:00',
  supportBigNumbers: true,
  bigNumberStrings: true,
  
  // SSL配置 (生产环境)
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false
};

// 连接池管理
const mysql = require('mysql2/promise');
let pool = null;

const getConnection = () => {
  if (!pool) {
    pool = mysql.createPool(DB_CONFIG);
  }
  return pool;
};

// 查询封装
const query = async (sql, params = []) => {
  const connection = getConnection();
  try {
    const [rows] = await connection.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};
```

### 5.2 读写分离配置 (可选)
```javascript
// 主从配置
const MASTER_CONFIG = { ...DB_CONFIG, host: process.env.DB_MASTER_HOST };
const SLAVE_CONFIG = { ...DB_CONFIG, host: process.env.DB_SLAVE_HOST };

const masterPool = mysql.createPool(MASTER_CONFIG);
const slavePool = mysql.createPool(SLAVE_CONFIG);

// 智能路由
const executeQuery = async (sql, params = [], forceWrite = false) => {
  const isWriteQuery = /^(INSERT|UPDATE|DELETE|ALTER|CREATE|DROP)/i.test(sql.trim());
  const pool = (isWriteQuery || forceWrite) ? masterPool : slavePool;
  
  const [rows] = await pool.execute(sql, params);
  return rows;
};
```

## 6. 数据备份策略

### 6.1 自动备份配置
```sql
-- 全量备份 (每日)
mysqldump --single-transaction --routines --triggers --all-databases > backup_$(date +%Y%m%d).sql

-- 增量备份 (每小时)
mysqlbinlog --start-datetime="$(date -d '1 hour ago' '+%Y-%m-%d %H:00:00')" --stop-datetime="$(date '+%Y-%m-%d %H:00:00')" /var/log/mysql/mysql-bin.000001 > incremental_$(date +%Y%m%d_%H).sql
```

### 6.2 数据恢复策略
```bash
#!/bin/bash
# 数据恢复脚本

# 1. 恢复全量备份
mysql -u root -p < backup_20240115.sql

# 2. 应用增量备份
for file in incremental_20240115_*.sql; do
  mysql -u root -p < "$file"
done

# 3. 验证数据完整性
mysql -u root -p -e "SELECT COUNT(*) FROM users; SELECT COUNT(*) FROM game_records;"
```

此数据库设计方案提供了：
1. **高性能**: 优化的索引和查询策略
2. **可扩展**: 分区分表预留方案
3. **高可用**: 自动备份和恢复机制
4. **成本优化**: Serverless按需计费配置

通过合理的表结构设计和索引优化，确保在10万DAU的场景下，数据库查询响应时间控制在100ms以内，同时保持较低的运维成本。