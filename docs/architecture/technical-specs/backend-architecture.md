# 后端架构设计

## 🎯 设计目标

### 核心原则
- **Serverless优先**: 零固定成本，按需付费
- **微服务架构**: 功能模块独立部署，松耦合设计
- **自动扩缩容**: 根据流量自动调整资源
- **成本优化**: 10万DAU月成本控制在$285以内

### 技术选型
- **计算平台**: 腾讯云Serverless云函数(SCF)
- **API网关**: 腾讯云API网关
- **数据库**: TencentDB Serverless MySQL + Redis
- **存储**: 腾讯云对象存储COS
- **监控**: 腾讯云监控 + 自定义APM

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小游戏     │    │    API网关       │    │   云函数集群     │
│   Cocos App     │◄──►│  Rate Limiting  │◄──►│  Auto Scaling   │
└─────────────────┘    │  Authentication │    │  Load Balance   │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN全球加速    │    │   消息队列       │    │   数据存储层     │
│  Static Assets  │    │  Event Driven   │    │  MySQL+Redis    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 服务拆分架构
```
微服务模块划分:

用户服务 (User Service)
├── 用户认证与授权
├── 用户信息管理
├── 积分与等级系统
└── 学习进度追踪

内容服务 (Content Service)
├── 题库管理
├── UGC内容审核
├── 内容推荐引擎
└── 方言区域管理

游戏服务 (Game Service)
├── 游戏会话管理
├── 实时排行榜
├── 匹配与PK系统
└── 成就系统

社交服务 (Social Service)
├── 好友系统
├── 分享与邀请
├── 社区互动
└── 消息通知

数据分析服务 (Analytics Service)
├── 用户行为分析
├── 游戏数据统计
├── 业务指标计算
└── 实时监控告警

🆕 围观服务 (Watch Service)
├── 围观房间管理
├── 实时消息推送
├── 弹幕系统
├── 预测游戏引擎
└── 围观数据统计
```

## ⚡ Serverless架构设计

### 1. 云函数架构
```python
# 函数配置优化
FUNCTION_CONFIGS = {
    'user-auth': {
        'memory': 128,  # 轻量级认证
        'timeout': 3,
        'concurrency': 100
    },
    'game-logic': {
        'memory': 256,  # 中等计算量
        'timeout': 10,
        'concurrency': 50
    },
    'content-process': {
        'memory': 512,  # 音频处理
        'timeout': 30,
        'concurrency': 20
    },
    'analytics-batch': {
        'memory': 1024,  # 批量数据处理
        'timeout': 60,
        'concurrency': 10
    }
}

# 函数代码结构
class BaseHandler:
    def __init__(self):
        self.db_pool = self.get_db_connection_pool()
        self.redis_client = self.get_redis_client()
        self.logger = self.setup_logging()
    
    def handler(self, event, context):
        try:
            # 请求预处理
            request_data = self.parse_request(event)
            
            # 参数验证
            self.validate_request(request_data)
            
            # 业务逻辑处理
            result = self.process_business_logic(request_data)
            
            # 响应格式化
            return self.format_response(result)
            
        except Exception as e:
            self.logger.error(f"Function error: {str(e)}")
            return self.error_response(e)
    
    def get_db_connection_pool(self):
        # 连接池优化，复用连接
        return ConnectionPool(
            host=os.environ['DB_HOST'],
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD'],
            database=os.environ['DB_NAME'],
            max_connections=5,  # SCF函数连接数限制
            connection_timeout=5
        )
```

### 2. API网关配置
```yaml
# API网关路由配置
api_gateway:
  base_url: https://api.hometowndialect.com
  rate_limiting:
    default: 100/minute/user
    premium: 1000/minute/user
  
  routes:
    # 用户认证
    - path: /auth/login
      method: POST
      function: user-auth-login
      rate_limit: 10/minute/ip
      
    - path: /auth/refresh
      method: POST
      function: user-auth-refresh
      rate_limit: 60/minute/user
    
    # 游戏接口
    - path: /game/question
      method: GET
      function: game-get-question
      auth_required: true
      
    - path: /game/submit
      method: POST
      function: game-submit-answer
      auth_required: true
      rate_limit: 30/minute/user
    
    # 内容管理
    - path: /content/upload
      method: POST
      function: content-upload-audio
      auth_required: true
      rate_limit: 10/minute/user
      max_body_size: 10MB
```

### 3. 自动扩缩容策略
```python
# 扩容策略配置
SCALING_CONFIG = {
    'triggers': {
        'cpu_threshold': 70,     # CPU使用率阈值
        'memory_threshold': 80,   # 内存使用率阈值
        'response_time': 1000,    # 响应时间阈值(ms)
        'error_rate': 5          # 错误率阈值(%)
    },
    'scaling_policy': {
        'min_instances': 0,      # 最小实例数(可缩容到0)
        'max_instances': 1000,   # 最大实例数
        'scale_up_step': 2,      # 扩容步长
        'scale_down_step': 1,    # 缩容步长
        'cooldown_period': 300   # 冷却时间(秒)
    }
}
```

## 🗄️ 数据库架构设计

### 1. MySQL数据库设计
```sql
-- 用户表设计
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(64) UNIQUE NOT NULL COMMENT '微信OpenID',
    unionid VARCHAR(64) COMMENT '微信UnionID',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    level INT DEFAULT 1 COMMENT '用户等级',
    total_score BIGINT DEFAULT 0 COMMENT '总积分',
    current_streak INT DEFAULT 0 COMMENT '当前连击数',
    max_streak INT DEFAULT 0 COMMENT '最大连击数',
    region VARCHAR(50) COMMENT '所在地区',
    preferred_dialect VARCHAR(50) COMMENT '偏好方言',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_level_score (level, total_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 题目表设计
CREATE TABLE questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    dialect VARCHAR(50) NOT NULL COMMENT '方言类型',
    difficulty ENUM('easy', 'medium', 'hard') NOT NULL COMMENT '难度等级',
    question_text VARCHAR(500) NOT NULL COMMENT '题目文本',
    audio_url VARCHAR(255) NOT NULL COMMENT '音频URL',
    audio_duration INT NOT NULL COMMENT '音频时长(秒)',
    correct_answer VARCHAR(100) NOT NULL COMMENT '正确答案',
    options JSON COMMENT '选项(JSON格式)',
    cultural_note TEXT COMMENT '文化背景说明',
    contributor_id BIGINT COMMENT '贡献者ID',
    status ENUM('draft', 'reviewing', 'approved', 'rejected') DEFAULT 'draft',
    play_count BIGINT DEFAULT 0 COMMENT '播放次数',
    correct_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '正确率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_dialect_difficulty (dialect, difficulty),
    INDEX idx_status (status),
    INDEX idx_contributor (contributor_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目表';

-- 游戏记录表设计
CREATE TABLE game_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    game_mode ENUM('classic', 'learning', 'challenge', 'pk') NOT NULL,
    dialect VARCHAR(50) NOT NULL COMMENT '方言类型',
    total_questions INT NOT NULL COMMENT '总题数',
    correct_answers INT NOT NULL COMMENT '正确题数',
    total_score INT NOT NULL COMMENT '总得分',
    duration INT NOT NULL COMMENT '游戏时长(秒)',
    avg_response_time DECIMAL(8,2) COMMENT '平均响应时间(秒)',
    max_streak INT DEFAULT 0 COMMENT '最大连击数',
    questions_data JSON COMMENT '题目详情(JSON)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_mode (user_id, game_mode),
    INDEX idx_dialect_score (dialect, total_score),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏会话记录表';

-- 排行榜表设计(Redis + MySQL)
CREATE TABLE leaderboards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    leaderboard_type ENUM('daily', 'weekly', 'monthly', 'all_time') NOT NULL,
    dialect VARCHAR(50) NOT NULL,
    score BIGINT NOT NULL,
    rank_position INT NOT NULL,
    calculation_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_type_dialect_date (user_id, leaderboard_type, dialect, calculation_date),
    INDEX idx_type_dialect_rank (leaderboard_type, dialect, rank_position),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜表';
```

### 2. Redis缓存设计
```python
# Redis缓存策略
class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=os.environ['REDIS_HOST'],
            port=6379,
            decode_responses=True,
            connection_pool=redis.ConnectionPool(max_connections=10)
        )
    
    # 缓存键设计规范
    CACHE_KEYS = {
        'user_info': 'user:{user_id}',
        'user_session': 'session:{user_id}',
        'leaderboard': 'leaderboard:{type}:{dialect}',
        'question_pool': 'questions:{dialect}:{difficulty}',
        'game_config': 'config:game',
        'daily_stats': 'stats:daily:{date}',
        'hot_questions': 'hot:questions:{dialect}'
    }
    
    # TTL策略
    CACHE_TTL = {
        'user_info': 3600,      # 用户信息 1小时
        'user_session': 1800,   # 用户会话 30分钟
        'leaderboard': 300,     # 排行榜 5分钟
        'question_pool': 7200,  # 题目池 2小时
        'game_config': 86400,   # 游戏配置 24小时
        'daily_stats': 86400,   # 日统计 24小时
        'hot_questions': 3600   # 热门题目 1小时
    }
    
    async def get_user_info(self, user_id: int) -> dict:
        cache_key = self.CACHE_KEYS['user_info'].format(user_id=user_id)
        cached_data = await self.redis_client.get(cache_key)
        
        if cached_data:
            return json.loads(cached_data)
        
        # 缓存未命中，从数据库获取
        user_data = await self.db.get_user_by_id(user_id)
        if user_data:
            await self.redis_client.setex(
                cache_key, 
                self.CACHE_TTL['user_info'], 
                json.dumps(user_data)
            )
        return user_data
    
    async def update_leaderboard(self, leaderboard_type: str, dialect: str, scores: list):
        cache_key = self.CACHE_KEYS['leaderboard'].format(
            type=leaderboard_type, 
            dialect=dialect
        )
        
        # 使用Redis Sorted Set存储排行榜
        pipe = self.redis_client.pipeline()
        pipe.delete(cache_key)
        for user_id, score in scores:
            pipe.zadd(cache_key, {user_id: score})
        pipe.expire(cache_key, self.CACHE_TTL['leaderboard'])
        await pipe.execute()
```

## 📡 API设计规范

### 1. RESTful API设计
```python
# API响应格式标准化
class APIResponse:
    def __init__(self):
        self.success_template = {
            "code": 0,
            "message": "success",
            "data": None,
            "timestamp": None
        }
        
        self.error_template = {
            "code": None,
            "message": None,
            "error_detail": None,
            "timestamp": None
        }
    
    def success(self, data=None, message="success"):
        response = self.success_template.copy()
        response["data"] = data
        response["message"] = message
        response["timestamp"] = int(time.time())
        return response
    
    def error(self, code, message, detail=None):
        response = self.error_template.copy()
        response["code"] = code
        response["message"] = message
        response["error_detail"] = detail
        response["timestamp"] = int(time.time())
        return response

# 错误码定义
ERROR_CODES = {
    # 用户相关 1xxx
    1001: "用户不存在",
    1002: "用户认证失败",
    1003: "用户权限不足",
    
    # 游戏相关 2xxx
    2001: "题目不存在",
    2002: "游戏会话已过期",
    2003: "答案提交超时",
    
    # 内容相关 3xxx
    3001: "音频格式不支持",
    3002: "内容审核失败",
    3003: "内容重复提交",
    
    # 系统相关 5xxx
    5001: "数据库连接错误",
    5002: "缓存服务异常",
    5003: "第三方服务异常"
}
```

### 2. 核心API实现
```python
# 用户认证API
@app.route('/auth/login', methods=['POST'])
async def user_login(event, context):
    """微信用户登录认证"""
    try:
        request_data = json.loads(event['body'])
        wx_code = request_data.get('code')
        
        # 调用微信API获取openid
        wx_response = await wechat_api.code2session(wx_code)
        openid = wx_response['openid']
        
        # 查询或创建用户
        user = await user_service.get_or_create_user(openid, wx_response)
        
        # 生成JWT Token
        token = jwt_manager.generate_token(user['id'])
        
        return APIResponse().success({
            'token': token,
            'user_info': user,
            'expires_in': 7200
        })
        
    except Exception as e:
        return APIResponse().error(1002, "用户认证失败", str(e))

# 获取题目API
@app.route('/game/question', methods=['GET'])
@auth_required
async def get_question(event, context):
    """获取游戏题目"""
    try:
        user_id = event['user_id']
        dialect = event['queryStringParameters'].get('dialect', 'random')
        difficulty = event['queryStringParameters'].get('difficulty', 'medium')
        
        # 智能题目推荐
        question = await question_service.get_recommended_question(
            user_id=user_id,
            dialect=dialect,
            difficulty=difficulty
        )
        
        # 记录题目播放
        await analytics_service.track_question_play(question['id'], user_id)
        
        return APIResponse().success(question)
        
    except Exception as e:
        return APIResponse().error(2001, "题目获取失败", str(e))

# 提交答案API
@app.route('/game/submit', methods=['POST'])
@auth_required
@rate_limit(30, 60)  # 每分钟最多30次
async def submit_answer(event, context):
    """提交游戏答案"""
    try:
        user_id = event['user_id']
        request_data = json.loads(event['body'])
        
        question_id = request_data.get('question_id')
        user_answer = request_data.get('answer')
        response_time = request_data.get('response_time')
        
        # 验证答案
        result = await game_service.validate_answer(
            question_id=question_id,
            user_answer=user_answer,
            response_time=response_time,
            user_id=user_id
        )
        
        # 更新用户积分和统计
        await user_service.update_user_stats(user_id, result)
        
        # 更新排行榜
        await leaderboard_service.update_user_rank(user_id, result['score'])
        
        return APIResponse().success(result)
        
    except Exception as e:
        return APIResponse().error(2003, "答案提交失败", str(e))

# 🆕 围观服务API
@app.route('/watch/room/join', methods=['POST'])
@auth_required
async def join_watch_room(event, context):
    """加入围观房间"""
    try:
        user_id = event['user_id']
        request_data = json.loads(event['body'])
        
        room_id = request_data.get('room_id')
        watch_options = request_data.get('options', {})
        
        # 检查围观权限
        permission = await watch_service.check_watch_permission(user_id, room_id)
        if not permission['allowed']:
            return APIResponse().error(4001, permission['reason'])
        
        # 加入房间
        room_info = await watch_service.join_room(user_id, room_id, watch_options)
        
        # 记录围观行为
        await analytics_service.track_watch_join(user_id, room_id)
        
        return APIResponse().success(room_info)
        
    except Exception as e:
        return APIResponse().error(4001, "加入围观房间失败", str(e))

@app.route('/watch/room/leave', methods=['POST'])
@auth_required
async def leave_watch_room(event, context):
    """离开围观房间"""
    try:
        user_id = event['user_id']
        request_data = json.loads(event['body'])
        
        room_id = request_data.get('room_id')
        
        # 离开房间
        await watch_service.leave_room(user_id, room_id)
        
        # 记录围观行为
        await analytics_service.track_watch_leave(user_id, room_id)
        
        return APIResponse().success()
        
    except Exception as e:
        return APIResponse().error(4002, "离开围观房间失败", str(e))

@app.route('/watch/barrage/send', methods=['POST'])
@auth_required
@rate_limit(10, 60)  # 每分钟最多10条弹幕
async def send_barrage(event, context):
    """发送弹幕"""
    try:
        user_id = event['user_id']
        request_data = json.loads(event['body'])
        
        room_id = request_data.get('room_id')
        content = request_data.get('content')
        
        # 弹幕内容验证
        validation = await barrage_service.validate_content(content, user_id)
        if not validation['valid']:
            return APIResponse().error(4003, validation['reason'])
        
        # 发送弹幕
        barrage_id = await barrage_service.send_barrage(
            user_id=user_id,
            room_id=room_id,
            content=content
        )
        
        return APIResponse().success({'barrage_id': barrage_id})
        
    except Exception as e:
        return APIResponse().error(4003, "发送弹幕失败", str(e))

@app.route('/watch/prediction/submit', methods=['POST'])
@auth_required
async def submit_prediction(event, context):
    """提交预测"""
    try:
        user_id = event['user_id']
        request_data = json.loads(event['body'])
        
        room_id = request_data.get('room_id')
        answer = request_data.get('answer')
        confidence = request_data.get('confidence', 0.5)
        
        # 提交预测
        prediction_id = await prediction_service.submit_prediction(
            user_id=user_id,
            room_id=room_id,
            answer=answer,
            confidence=confidence
        )
        
        return APIResponse().success({'prediction_id': prediction_id})
        
    except Exception as e:
        return APIResponse().error(4004, "提交预测失败", str(e))

@app.route('/watch/updates', methods=['GET'])
@auth_required
async def get_watch_updates(event, context):
    """获取围观更新（用于轮询）"""
    try:
        user_id = event['user_id']
        room_id = event['queryStringParameters'].get('room_id')
        last_update_id = event['queryStringParameters'].get('last_update_id', '')
        
        # 获取更新
        updates = await watch_service.get_updates_since(
            room_id=room_id,
            last_update_id=last_update_id,
            user_id=user_id
        )
        
        return APIResponse().success({'updates': updates})
        
    except Exception as e:
        return APIResponse().error(4005, "获取围观更新失败", str(e))
```

## 🏠 围观服务架构实现

### 1. 围观房间管理服务
```python
class WatchRoomService:
    def __init__(self):
        self.redis_client = redis.Redis(host=os.environ['REDIS_HOST'])
        self.room_configs = {
            'max_watchers_per_room': 1000,
            'room_idle_timeout': 300,  # 5分钟无活动自动关闭
            'merge_threshold': 5,      # 少于5人围观考虑合并
            'message_buffer_size': 100
        }
    
    async def join_room(self, user_id: int, room_id: str, options: dict) -> dict:
        """用户加入围观房间"""
        
        # 1. 检查房间是否存在
        room_exists = await self.check_room_exists(room_id)
        if not room_exists:
            # 创建新房间
            await self.create_room(room_id)
        
        # 2. 检查房间容量
        current_watchers = await self.get_room_watcher_count(room_id)
        if current_watchers >= self.room_configs['max_watchers_per_room']:
            # 尝试房间分流
            alternative_room = await self.find_alternative_room(room_id)
            if alternative_room:
                room_id = alternative_room
            else:
                raise Exception("房间已满，无法加入")
        
        # 3. 加入房间
        await self.add_watcher_to_room(user_id, room_id, options)
        
        # 4. 获取房间信息
        room_info = await self.get_room_info(room_id)
        
        # 5. 广播用户加入事件
        await self.broadcast_watcher_join(room_id, user_id)
        
        return room_info
    
    async def leave_room(self, user_id: int, room_id: str):
        """用户离开围观房间"""
        
        # 1. 从房间移除用户
        await self.remove_watcher_from_room(user_id, room_id)
        
        # 2. 广播用户离开事件
        await self.broadcast_watcher_leave(room_id, user_id)
        
        # 3. 检查房间是否需要清理
        watcher_count = await self.get_room_watcher_count(room_id)
        if watcher_count == 0:
            # 启动房间清理定时器
            await self.schedule_room_cleanup(room_id)
    
    async def intelligent_room_merge(self):
        """智能房间合并"""
        
        # 获取所有活跃房间
        active_rooms = await self.get_active_rooms()
        
        # 分析合并机会
        merge_candidates = []
        
        for room in active_rooms:
            if room['watcher_count'] < self.room_configs['merge_threshold']:
                # 寻找合并目标
                target_room = await self.find_merge_target(room)
                if target_room:
                    merge_candidates.append({
                        'source': room['id'],
                        'target': target_room['id'],
                        'benefit_score': self.calculate_merge_benefit(room, target_room)
                    })
        
        # 执行高收益合并
        for candidate in sorted(merge_candidates, key=lambda x: x['benefit_score'], reverse=True):
            if candidate['benefit_score'] > 0.7:
                await self.execute_room_merge(candidate['source'], candidate['target'])
    
    async def broadcast_to_room(self, room_id: str, message: dict, exclude_users: list = None):
        """向房间广播消息"""
        
        # 获取房间所有观众
        watchers = await self.get_room_watchers(room_id)
        
        # 过滤排除用户
        if exclude_users:
            watchers = [w for w in watchers if w['user_id'] not in exclude_users]
        
        # 根据连接策略发送消息
        websocket_users = []
        polling_users = []
        
        for watcher in watchers:
            if watcher['connection_type'] == 'websocket':
                websocket_users.append(watcher)
            else:
                polling_users.append(watcher)
        
        # 并行发送
        tasks = []
        if websocket_users:
            tasks.append(self.broadcast_websocket(websocket_users, message))
        if polling_users:
            tasks.append(self.queue_polling_message(room_id, message))
        
        await asyncio.gather(*tasks)

class BarrageService:
    def __init__(self):
        self.redis_client = redis.Redis(host=os.environ['REDIS_HOST'])
        self.content_filter = ContentFilter()
        self.rate_limiter = RateLimiter()
        
        self.barrage_config = {
            'max_length': 50,
            'rate_limit': 10,  # 每分钟10条
            'quality_threshold': 0.3,
            'auto_approve_threshold': 0.8
        }
    
    async def validate_content(self, content: str, user_id: int) -> dict:
        """验证弹幕内容"""
        
        # 1. 基础验证
        if not content or len(content.strip()) == 0:
            return {'valid': False, 'reason': '弹幕内容不能为空'}
        
        if len(content) > self.barrage_config['max_length']:
            return {'valid': False, 'reason': f'弹幕长度不能超过{self.barrage_config["max_length"]}字符'}
        
        # 2. 频率限制
        rate_check = await self.rate_limiter.check_rate_limit(
            f"barrage_user_{user_id}",
            self.barrage_config['rate_limit'],
            60  # 60秒窗口
        )
        
        if not rate_check['allowed']:
            return {'valid': False, 'reason': '发送太频繁，请稍后再试'}
        
        # 3. 内容过滤
        filter_result = await self.content_filter.filter_content(content)
        if not filter_result['passed']:
            return {'valid': False, 'reason': filter_result['reason']}
        
        # 4. 质量评估
        quality_score = await self.assess_barrage_quality(content, user_id)
        if quality_score < self.barrage_config['quality_threshold']:
            return {'valid': False, 'reason': '弹幕质量过低'}
        
        return {
            'valid': True,
            'quality_score': quality_score,
            'auto_approve': quality_score >= self.barrage_config['auto_approve_threshold']
        }
    
    async def send_barrage(self, user_id: int, room_id: str, content: str) -> str:
        """发送弹幕"""
        
        # 创建弹幕对象
        barrage_id = self.generate_barrage_id()
        
        barrage = {
            'id': barrage_id,
            'user_id': user_id,
            'room_id': room_id,
            'content': content,
            'timestamp': time.time(),
            'quality_score': await self.assess_barrage_quality(content, user_id)
        }
        
        # 获取用户信息
        user_info = await user_service.get_user_info(user_id)
        barrage['user_nickname'] = user_info['nickname']
        barrage['user_level'] = user_info['level']
        
        # 保存弹幕
        await self.save_barrage(barrage)
        
        # 广播弹幕
        await watch_room_service.broadcast_to_room(room_id, {
            'type': 'barrage_message',
            'data': barrage
        }, exclude_users=[user_id])
        
        # 更新统计
        await self.update_barrage_stats(room_id, user_id)
        
        return barrage_id
    
    async def assess_barrage_quality(self, content: str, user_id: int) -> float:
        """评估弹幕质量"""
        
        quality_score = 0.5  # 基础分数
        
        # 1. 内容多样性
        diversity_score = self.calculate_content_diversity(content)
        quality_score += diversity_score * 0.2
        
        # 2. 用户历史表现
        user_history = await self.get_user_barrage_history(user_id)
        history_score = self.calculate_user_history_score(user_history)
        quality_score += history_score * 0.3
        
        # 3. 时机适当性
        timing_score = await self.calculate_timing_appropriateness(user_id)
        quality_score += timing_score * 0.3
        
        # 4. 互动价值
        interaction_score = self.calculate_interaction_value(content)
        quality_score += interaction_score * 0.2
        
        return max(0.0, min(1.0, quality_score))

class PredictionService:
    def __init__(self):
        self.redis_client = redis.Redis(host=os.environ['REDIS_HOST'])
        self.prediction_config = {
            'max_predictions_per_question': 1000,
            'prediction_window': 10,  # 10秒预测窗口
            'base_score': 10,
            'early_bonus': 5,
            'streak_multiplier': 2.0
        }
    
    async def submit_prediction(self, user_id: int, room_id: str, 
                               answer: str, confidence: float) -> str:
        """提交预测"""
        
        # 1. 检查预测窗口
        game_state = await game_service.get_room_game_state(room_id)
        if not self.is_prediction_window_open(game_state):
            raise Exception("当前不在预测时间窗口内")
        
        # 2. 检查重复预测
        existing_prediction = await self.get_user_prediction(user_id, room_id, game_state['question_id'])
        if existing_prediction:
            raise Exception("已经提交过预测")
        
        # 3. 创建预测记录
        prediction_id = self.generate_prediction_id()
        
        prediction = {
            'id': prediction_id,
            'user_id': user_id,
            'room_id': room_id,
            'question_id': game_state['question_id'],
            'answer': answer,
            'confidence': confidence,
            'timestamp': time.time()
        }
        
        # 4. 保存预测
        await self.save_prediction(prediction)
        
        # 5. 更新预测统计
        await self.update_prediction_stats(room_id, answer)
        
        # 6. 广播预测分布更新
        await self.broadcast_prediction_distribution(room_id)
        
        return prediction_id
    
    async def calculate_prediction_results(self, room_id: str, correct_answer: str) -> dict:
        """计算预测结果"""
        
        # 获取所有预测
        predictions = await self.get_room_predictions(room_id)
        
        results = {
            'total_predictions': len(predictions),
            'correct_predictions': 0,
            'accuracy_rate': 0.0,
            'winner_list': [],
            'score_distribution': {}
        }
        
        correct_predictions = []
        
        for prediction in predictions:
            if prediction['answer'] == correct_answer:
                correct_predictions.append(prediction)
                results['correct_predictions'] += 1
        
        # 计算准确率
        if results['total_predictions'] > 0:
            results['accuracy_rate'] = results['correct_predictions'] / results['total_predictions']
        
        # 计算积分和排名
        for prediction in correct_predictions:
            score = await self.calculate_prediction_score(prediction, room_id)
            
            # 更新用户积分
            await user_service.add_user_score(prediction['user_id'], score)
            
            # 添加到获奖名单
            user_info = await user_service.get_user_info(prediction['user_id'])
            results['winner_list'].append({
                'user_id': prediction['user_id'],
                'nickname': user_info['nickname'],
                'score': score,
                'prediction_time': prediction['timestamp']
            })
        
        # 按积分排序
        results['winner_list'].sort(key=lambda x: x['score'], reverse=True)
        
        # 广播结果
        await watch_room_service.broadcast_to_room(room_id, {
            'type': 'prediction_results',
            'data': results
        })
        
        return results
    
    async def calculate_prediction_score(self, prediction: dict, room_id: str) -> int:
        """计算预测积分"""
        
        base_score = self.prediction_config['base_score']
        
        # 早期预测奖励
        game_start_time = await self.get_question_start_time(room_id, prediction['question_id'])
        prediction_delay = prediction['timestamp'] - game_start_time
        
        if prediction_delay < 3:  # 3秒内预测
            base_score += self.prediction_config['early_bonus']
        
        # 连击奖励
        user_streak = await self.get_user_prediction_streak(prediction['user_id'])
        if user_streak >= 3:
            base_score = int(base_score * self.prediction_config['streak_multiplier'])
        
        # 置信度加权
        confidence_bonus = int(base_score * prediction['confidence'] * 0.2)
        base_score += confidence_bonus
        
        return base_score
```

## 🔧 中间件和工具

### 1. 认证中间件
```python
import jwt
from functools import wraps

def auth_required(func):
    @wraps(func)
    async def wrapper(event, context):
        try:
            # 从请求头获取token
            auth_header = event.get('headers', {}).get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return APIResponse().error(1002, "认证token缺失")
            
            token = auth_header[7:]  # 移除'Bearer '前缀
            
            # 验证JWT token
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            user_id = payload.get('user_id')
            
            # 检查用户是否存在
            user = await user_service.get_user_by_id(user_id)
            if not user:
                return APIResponse().error(1001, "用户不存在")
            
            # 将用户信息添加到event中
            event['user_id'] = user_id
            event['user_info'] = user
            
            return await func(event, context)
            
        except jwt.ExpiredSignatureError:
            return APIResponse().error(1002, "token已过期")
        except jwt.InvalidTokenError:
            return APIResponse().error(1002, "无效token")
        except Exception as e:
            return APIResponse().error(5003, "认证服务异常", str(e))
    
    return wrapper
```

### 2. 限流中间件
```python
import asyncio
from collections import defaultdict
import time

class RateLimiter:
    def __init__(self):
        self.requests = defaultdict(list)
        self.lock = asyncio.Lock()
    
    async def is_allowed(self, key: str, limit: int, window: int) -> bool:
        async with self.lock:
            now = time.time()
            window_start = now - window
            
            # 清除过期请求记录
            self.requests[key] = [
                req_time for req_time in self.requests[key] 
                if req_time > window_start
            ]
            
            # 检查是否超过限制
            if len(self.requests[key]) >= limit:
                return False
            
            # 记录当前请求
            self.requests[key].append(now)
            return True

rate_limiter = RateLimiter()

def rate_limit(limit: int, window: int):
    def decorator(func):
        @wraps(func)
        async def wrapper(event, context):
            # 获取限流key（用户ID或IP）
            user_id = event.get('user_id')
            client_ip = event.get('requestContext', {}).get('sourceIp')
            rate_key = f"user:{user_id}" if user_id else f"ip:{client_ip}"
            
            # 检查限流
            if not await rate_limiter.is_allowed(rate_key, limit, window):
                return APIResponse().error(4001, "请求过于频繁，请稍后再试")
            
            return await func(event, context)
        return wrapper
    return decorator
```

### 3. 数据库连接池
```python
import aiomysql
import asyncio

class DatabaseManager:
    def __init__(self):
        self.pool = None
        self.redis_client = None
    
    async def init_connections(self):
        # MySQL连接池
        self.pool = await aiomysql.create_pool(
            host=os.environ['DB_HOST'],
            port=3306,
            user=os.environ['DB_USER'],
            password=os.environ['DB_PASSWORD'],
            db=os.environ['DB_NAME'],
            charset='utf8mb4',
            minsize=1,
            maxsize=5,  # SCF环境限制连接数
            autocommit=True
        )
        
        # Redis连接
        import aioredis
        self.redis_client = await aioredis.from_url(
            f"redis://{os.environ['REDIS_HOST']}:6379",
            encoding="utf-8",
            decode_responses=True,
            max_connections=10
        )
    
    async def execute_query(self, sql: str, params=None):
        async with self.pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                return await cursor.fetchall()
    
    async def execute_update(self, sql: str, params=None):
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                result = await cursor.execute(sql, params)
                return result

# 全局数据库管理器
db_manager = DatabaseManager()
```

## 📊 监控和日志

### 1. APM监控
```python
import time
import json
from functools import wraps

class APMMonitor:
    def __init__(self):
        self.metrics = defaultdict(list)
    
    def track_performance(self, func_name: str, duration: float, success: bool):
        metric = {
            'function': func_name,
            'duration': duration,
            'success': success,
            'timestamp': time.time()
        }
        self.metrics[func_name].append(metric)
        
        # 异步发送到监控系统
        asyncio.create_task(self.send_metrics(metric))
    
    async def send_metrics(self, metric):
        # 发送到腾讯云监控或其他APM系统
        pass

def monitor_performance(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        error = None
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            error = str(e)
            raise
        finally:
            duration = time.time() - start_time
            apm_monitor.track_performance(func.__name__, duration, success)
            
            # 记录慢查询
            if duration > 1.0:  # 超过1秒的请求
                logger.warning(f"Slow function: {func.__name__}, duration: {duration:.2f}s")
    
    return wrapper

apm_monitor = APMMonitor()
```

### 2. 结构化日志
```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    def __init__(self):
        self.logger = logging.getLogger('hometown_dialect')
        self.logger.setLevel(logging.INFO)
        
        # 配置日志格式
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log(self, level: str, message: str, **kwargs):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'message': message,
            'function_name': kwargs.get('function_name'),
            'user_id': kwargs.get('user_id'),
            'request_id': kwargs.get('request_id'),
            'duration': kwargs.get('duration'),
            'error': kwargs.get('error'),
            'extra': {k: v for k, v in kwargs.items() 
                     if k not in ['function_name', 'user_id', 'request_id', 'duration', 'error']}
        }
        
        self.logger.info(json.dumps(log_entry, ensure_ascii=False))
    
    def info(self, message: str, **kwargs):
        self.log('INFO', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self.log('ERROR', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self.log('WARNING', message, **kwargs)

logger = StructuredLogger()
```

## 🚀 部署和运维

### 1. 基础设施即代码(IaC)
```yaml
# serverless.yml - Serverless Framework配置
service: hometown-dialect-backend

provider:
  name: tencent
  runtime: Python3.8
  region: ap-beijing
  memorySize: 256
  timeout: 30
  environment:
    DB_HOST: ${env:DB_HOST}
    DB_USER: ${env:DB_USER}
    DB_PASSWORD: ${env:DB_PASSWORD}
    REDIS_HOST: ${env:REDIS_HOST}
    JWT_SECRET: ${env:JWT_SECRET}

functions:
  # 用户认证函数
  auth-login:
    handler: handlers.user.login
    memorySize: 128
    timeout: 10
    events:
      - apigw:
          path: /auth/login
          method: POST
          cors: true
  
  # 游戏相关函数
  game-question:
    handler: handlers.game.get_question
    memorySize: 256
    timeout: 15
    events:
      - apigw:
          path: /game/question
          method: GET
          cors: true
  
  # 内容处理函数
  content-upload:
    handler: handlers.content.upload_audio
    memorySize: 512
    timeout: 60
    events:
      - apigw:
          path: /content/upload
          method: POST
          cors: true

plugins:
  - serverless-tencent-scf
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
```

### 2. CI/CD流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy Backend

on:
  push:
    branches: [main, develop]
    paths: ['backend/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.8'
      
      - name: Install dependencies
        run: |
          cd backend
          pip install -r requirements.txt
          pip install pytest pytest-asyncio
      
      - name: Run tests
        run: |
          cd backend
          pytest tests/ -v --cov=handlers
      
      - name: Code quality check
        run: |
          cd backend
          flake8 handlers/
          black --check handlers/

  deploy-staging:
    needs: test
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '16'
      
      - name: Install Serverless Framework
        run: npm install -g serverless
      
      - name: Deploy to staging
        run: |
          cd backend
          serverless deploy --stage staging
        env:
          TENCENT_SECRET_ID: ${{ secrets.TENCENT_SECRET_ID }}
          TENCENT_SECRET_KEY: ${{ secrets.TENCENT_SECRET_KEY }}

  deploy-production:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          cd backend
          serverless deploy --stage production
```

---

**文档版本**: v1.0  
**最后更新**: 2024-07-30  
**负责团队**: architect-agent  
**审核状态**: ✅ 已审核通过