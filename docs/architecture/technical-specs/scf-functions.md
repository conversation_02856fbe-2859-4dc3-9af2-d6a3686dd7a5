# 腾讯云SCF函数设计方案

## 1. 函数拆分策略

### 1.1 微服务函数架构

```
backend/
├── functions/
│   ├── user-service/           # 用户服务函数
│   │   ├── auth/              # 认证相关
│   │   ├── profile/           # 用户资料
│   │   └── analytics/         # 用户行为分析
│   ├── game-service/          # 游戏服务函数
│   │   ├── question/          # 题目管理
│   │   ├── answer/            # 答题处理
│   │   └── ranking/           # 排行榜
│   ├── social-service/        # 社交服务函数
│   │   ├── share/             # 分享功能
│   │   ├── invite/            # 邀请好友
│   │   └── community/         # 社区互动
│   └── admin-service/         # 管理后台函数
│       ├── content/           # 内容管理
│       ├── moderation/        # 内容审核
│       └── statistics/        # 数据统计
```

### 1.2 函数职责划分

#### 用户服务函数（user-service）
```javascript
// 函数配置
const USER_SERVICE_CONFIG = {
  runtime: 'Nodejs18.15',
  memorySize: 512,    // MB
  timeout: 30,        // 秒
  concurrency: 1000,  // 并发数
  deadLetterQueue: true
};

// 主要功能
- auth: 微信登录、JWT Token管理
- profile: 用户信息CRUD、游戏历史
- analytics: 用户行为埋点、数据收集
```

#### 游戏服务函数（game-service）
```javascript
// 函数配置
const GAME_SERVICE_CONFIG = {
  runtime: 'Nodejs18.15',
  memorySize: 256,    // MB - 游戏逻辑轻量
  timeout: 15,        // 秒 - 快速响应
  concurrency: 2000,  // 高并发支持
  deadLetterQueue: true
};

// 主要功能
- question: 题目获取、难度算法、A/B测试
- answer: 答案验证、积分计算、连击统计
- ranking: 实时排行榜、历史排名、好友排行
```

#### 社交服务函数（social-service）
```javascript
// 函数配置
const SOCIAL_SERVICE_CONFIG = {
  runtime: 'Nodejs18.15',
  memorySize: 128,    // MB - 最小配置
  timeout: 10,        // 秒
  concurrency: 500,   // 中等并发
  deadLetterQueue: false  // 非关键功能
};

// 主要功能
- share: 生成分享卡片、统计分享数据
- invite: 邀请链接生成、奖励发放
- community: 评论系统、点赞互动
```

## 2. 冷启动优化方案

### 2.1 预热机制
```javascript
// 预热调度器配置
const WARMUP_CONFIG = {
  schedule: '0 */5 * * * *',  // 每5分钟预热一次
  functions: [
    'user-service-auth',
    'game-service-question',
    'game-service-answer'
  ],
  concurrency: 5  // 预热并发数
};

// 预热函数实现
exports.warmupHandler = async (event, context) => {
  if (event.source === 'timer') {
    console.log('Warmup execution');
    return { statusCode: 200, body: 'Warmed up' };
  }
  
  // 正常业务逻辑
  return await normalHandler(event, context);
};
```

### 2.2 容器复用优化
```javascript
// 全局变量优化 - 连接池复用
let mysqlPool;
let redisClient;

exports.handler = async (event, context) => {
  // 初始化连接池（仅首次冷启动）
  if (!mysqlPool) {
    mysqlPool = mysql.createPool({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true
    });
  }
  
  if (!redisClient) {
    redisClient = redis.createClient({
      host: process.env.REDIS_HOST,
      port: process.env.REDIS_PORT,
      password: process.env.REDIS_PASS,
      lazyConnect: true,
      maxRetriesPerRequest: 3
    });
  }
  
  // 业务逻辑处理
  return await processRequest(event);
};
```

### 2.3 代码包优化
```json
// package.json - 最小依赖
{
  "dependencies": {
    "mysql2": "^3.6.0",        // 数据库连接
    "ioredis": "^5.3.2",       // Redis客户端
    "jsonwebtoken": "^9.0.2",  // JWT认证
    "ajv": "^8.12.0"           // 参数验证
  },
  "devDependencies": {
    "webpack": "^5.88.0"       // 代码打包优化
  }
}
```

## 3. 连接池管理

### 3.1 MySQL连接池配置
```javascript
// 数据库连接池配置
const DB_POOL_CONFIG = {
  // 连接池大小配置
  connectionLimit: 10,        // 最大连接数
  acquireTimeout: 60000,      // 获取连接超时时间
  timeout: 60000,            // 查询超时时间
  
  // 重连配置
  reconnect: true,
  reconnectDelay: 2000,
  
  // 性能优化
  removeNodeErrorCount: 10,
  restoreNodeTimeout: 50000,
  
  // SQL配置
  sql_mode: "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO",
  charset: 'utf8mb4',
  
  // 时区配置
  timezone: '+08:00'
};

// 连接池管理器
class DatabaseManager {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }
  
  async getConnection() {
    if (!this.pool) {
      this.pool = mysql.createPool(DB_POOL_CONFIG);
    }
    
    return new Promise((resolve, reject) => {
      this.pool.getConnection((err, connection) => {
        if (err) {
          console.error('Database connection error:', err);
          reject(err);
        } else {
          resolve(connection);
        }
      });
    });
  }
  
  async execute(sql, params) {
    const connection = await this.getConnection();
    try {
      return await new Promise((resolve, reject) => {
        connection.execute(sql, params, (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });
    } finally {
      connection.release();
    }
  }
}
```

### 3.2 Redis连接池配置
```javascript
// Redis连接池配置
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  password: process.env.REDIS_PASS,
  
  // 连接池配置
  family: 4,
  keepAlive: true,
  
  // 重连配置
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  
  // 集群配置（可选）
  enableOfflineQueue: false,
  lazyConnect: true
};

// Redis管理器
class RedisManager {
  constructor() {
    this.client = null;
  }
  
  getClient() {
    if (!this.client) {
      this.client = new Redis(REDIS_CONFIG);
      
      this.client.on('error', (err) => {
        console.error('Redis connection error:', err);
      });
      
      this.client.on('ready', () => {
        console.log('Redis connected');
      });
    }
    
    return this.client;
  }
  
  async get(key) {
    const client = this.getClient();
    return await client.get(key);
  }
  
  async set(key, value, ttl = 3600) {
    const client = this.getClient();
    return await client.setex(key, ttl, value);
  }
  
  async del(key) {
    const client = this.getClient();
    return await client.del(key);
  }
}
```

## 4. 内存和超时配置

### 4.1 函数资源配置矩阵

| 函数类型 | 内存(MB) | 超时(s) | 并发数 | 使用场景 |
|---------|----------|---------|--------|----------|
| 用户认证 | 512 | 30 | 1000 | JWT处理、数据库查询 |
| 题目获取 | 256 | 15 | 2000 | 轻量查询、缓存读取 |
| 答题处理 | 256 | 15 | 2000 | 逻辑计算、积分更新 |
| 排行榜 | 512 | 20 | 500 | 复杂聚合查询 |
| 分享功能 | 128 | 10 | 500 | 简单数据处理 |
| 数据分析 | 1024 | 60 | 100 | 大数据处理、报表生成 |
| 内容审核 | 256 | 30 | 200 | AI接口调用 |

### 4.2 动态资源调整策略
```javascript
// 环境变量配置
const FUNCTION_CONFIG = {
  development: {
    memorySize: 128,
    timeout: 30,
    concurrency: 100
  },
  test: {
    memorySize: 256,
    timeout: 30,
    concurrency: 500
  },
  production: {
    memorySize: 512,
    timeout: 15,
    concurrency: 1000
  }
};

// 运行时配置获取
const getConfig = () => {
  const env = process.env.ENVIRONMENT || 'development';
  return FUNCTION_CONFIG[env];
};
```

### 4.3 性能监控指标
```javascript
// 性能监控埋点
const performanceMonitor = {
  startTime: Date.now(),
  
  // 数据库查询监控
  dbQueryTime: 0,
  dbQueryCount: 0,
  
  // 缓存命中监控
  cacheHitCount: 0,
  cacheMissCount: 0,
  
  // 内存使用监控
  memoryUsage: process.memoryUsage(),
  
  // 记录指标
  recordMetric(name, value, unit = 'ms') {
    console.log(`MONITORING ${name}: ${value}${unit}`);
  },
  
  // 生成报告
  generateReport() {
    const endTime = Date.now();
    const totalTime = endTime - this.startTime;
    
    return {
      totalExecutionTime: totalTime,
      databaseQueries: {
        count: this.dbQueryCount,
        avgTime: this.dbQueryTime / Math.max(this.dbQueryCount, 1)
      },
      cachePerformance: {
        hitRate: this.cacheHitCount / (this.cacheHitCount + this.cacheMissCount),
        totalRequests: this.cacheHitCount + this.cacheMissCount
      },
      memoryUsage: this.memoryUsage
    };
  }
};
```

## 5. 错误处理和重试机制

### 5.1 统一错误处理
```javascript
// 错误处理中间件
const errorHandler = (handler) => {
  return async (event, context) => {
    try {
      const result = await handler(event, context);
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          code: 0,
          message: 'success',
          data: result
        })
      };
    } catch (error) {
      console.error('Function execution error:', error);
      
      // 错误分类处理
      let statusCode = 500;
      let errorCode = 'INTERNAL_ERROR';
      
      if (error.name === 'ValidationError') {
        statusCode = 400;
        errorCode = 'INVALID_PARAMS';
      } else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        errorCode = 'UNAUTHORIZED';
      }
      
      return {
        statusCode,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          code: errorCode,
          message: error.message,
          timestamp: new Date().toISOString()
        })
      };
    }
  };
};
```

### 5.2 重试机制
```javascript
// 重试装饰器
const withRetry = (fn, maxRetries = 3, delay = 1000) => {
  return async (...args) => {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn(...args);
      } catch (error) {
        lastError = error;
        
        // 不可重试的错误直接抛出
        if (error.code === 'INVALID_PARAMS' || error.statusCode === 400) {
          throw error;
        }
        
        if (i < maxRetries) {
          console.log(`Retry ${i + 1}/${maxRetries} after ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
      }
    }
    
    throw lastError;
  };
};

// 使用示例
const dbQuery = withRetry(async (sql, params) => {
  return await db.execute(sql, params);
}, 3, 1000);
```

此SCF函数设计方案确保了：
1. **高性能**: 合理的资源配置和连接池管理
2. **低成本**: 按需计费和资源优化
3. **高可用**: 错误处理和重试机制
4. **可扩展**: 微服务架构和无状态设计

通过这些优化策略，可以在保证用户体验的前提下，将函数执行成本控制在预算范围内。