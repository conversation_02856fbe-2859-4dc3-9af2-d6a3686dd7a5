# 安全需求规范

## 🎯 安全目标

### 核心安全原则
- **数据保护**: 用户隐私和敏感数据全面保护
- **访问控制**: 多层次权限管理和身份验证
- **系统安全**: 防范各类网络攻击和恶意行为
- **合规要求**: 符合相关法律法规和行业标準

### 安全标准
- **等保三级**: 符合国家信息安全等级保护三级要求
- **ISO 27001**: 遵循国际信息安全管理标准
- **GDPR**: 符合欧盟通用数据保护条例
- **网络安全法**: 符合中华人民共和国网络安全法

## 🔐 身份认证与授权

### 1. 用户身份认证

#### 微信认证集成
```python
class WeChatAuthenticator:
    def __init__(self):
        self.app_id = os.environ['WECHAT_APP_ID']
        self.app_secret = os.environ['WECHAT_APP_SECRET']
        self.token_expiry = 7200  # 2小时
        self.refresh_token_expiry = 86400 * 30  # 30天
    
    async def authenticate_user(self, wx_code: str) -> AuthResult:
        """微信用户认证"""
        try:
            # 调用微信API获取用户信息
            wx_response = await self.call_wechat_api(wx_code)
            
            if not wx_response.get('openid'):
                raise AuthenticationError("Invalid WeChat authorization code")
            
            # 验证用户信息
            user_info = await self.validate_user_info(wx_response)
            
            # 生成访问令牌
            access_token = await self.generate_access_token(user_info)
            refresh_token = await self.generate_refresh_token(user_info)
            
            # 记录登录日志
            await self.log_authentication_event(user_info, 'login_success')
            
            return AuthResult(
                success=True,
                access_token=access_token,
                refresh_token=refresh_token,
                user_info=user_info,
                expires_in=self.token_expiry
            )
            
        except Exception as e:
            await self.log_authentication_event(None, 'login_failure', str(e))
            raise AuthenticationError(f"Authentication failed: {str(e)}")
    
    async def validate_token(self, token: str) -> TokenValidationResult:
        """令牌验证"""
        try:
            # 解析JWT令牌
            payload = jwt.decode(
                token, 
                self.jwt_secret, 
                algorithms=['HS256'],
                options={'verify_exp': True}
            )
            
            user_id = payload.get('user_id')
            if not user_id:
                raise TokenValidationError("Invalid token payload")
            
            # 检查用户状态
            user = await self.get_user_by_id(user_id)
            if not user or user.status != 'active':
                raise TokenValidationError("User account is inactive")
            
            # 检查令牌黑名单
            if await self.is_token_blacklisted(token):
                raise TokenValidationError("Token has been revoked")
            
            return TokenValidationResult(
                valid=True,
                user_id=user_id,
                permissions=await self.get_user_permissions(user_id)
            )
            
        except jwt.ExpiredSignatureError:
            raise TokenValidationError("Token has expired")
        except jwt.InvalidTokenError:
            raise TokenValidationError("Invalid token format")
    
    async def generate_access_token(self, user_info: dict) -> str:
        """生成访问令牌"""
        payload = {
            'user_id': user_info['id'],
            'openid': user_info['openid'],
            'iat': int(time.time()),
            'exp': int(time.time()) + self.token_expiry,
            'jti': str(uuid.uuid4())  # 令牌唯一标识
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
    
    async def refresh_access_token(self, refresh_token: str) -> str:
        """刷新访问令牌"""
        # 验证刷新令牌
        refresh_payload = await self.validate_refresh_token(refresh_token)
        
        # 生成新的访问令牌
        user_info = await self.get_user_by_id(refresh_payload['user_id'])
        new_access_token = await self.generate_access_token(user_info)
        
        # 记录令牌刷新事件
        await self.log_token_refresh(user_info['id'])
        
        return new_access_token
```

#### 多因素认证(MFA)
```python
class MFAManager:
    def __init__(self):
        self.totp_issuer = "HomeTownDialect"
        self.backup_codes_count = 8
        self.rate_limiter = RateLimiter()
    
    async def enable_mfa(self, user_id: int) -> MFASetupResult:
        """启用多因素认证"""
        
        # 生成TOTP密钥
        secret_key = pyotp.random_base32()
        
        # 生成备用码
        backup_codes = self.generate_backup_codes()
        
        # 生成二维码
        totp_uri = pyotp.totp.TOTP(secret_key).provisioning_uri(
            name=f"user_{user_id}",
            issuer_name=self.totp_issuer
        )
        
        qr_code = self.generate_qr_code(totp_uri)
        
        # 临时存储密钥（等待用户验证）
        await self.store_pending_mfa_setup(user_id, secret_key, backup_codes)
        
        return MFASetupResult(
            secret_key=secret_key,
            qr_code=qr_code,
            backup_codes=backup_codes
        )
    
    async def verify_mfa_setup(self, user_id: int, totp_code: str) -> bool:
        """验证MFA设置"""
        
        # 限制验证频率
        if not await self.rate_limiter.is_allowed(f"mfa_verify_{user_id}", 5, 300):
            raise RateLimitError("Too many verification attempts")
        
        # 获取待验证的密钥
        pending_setup = await self.get_pending_mfa_setup(user_id)
        if not pending_setup:
            raise MFAError("No pending MFA setup found")
        
        # 验证TOTP码
        totp = pyotp.TOTP(pending_setup['secret_key'])
        if not totp.verify(totp_code, valid_window=1):
            await self.log_mfa_event(user_id, 'setup_verification_failed')
            return False
        
        # 正式启用MFA
        await self.activate_mfa(user_id, pending_setup)
        await self.cleanup_pending_mfa_setup(user_id)
        
        await self.log_mfa_event(user_id, 'mfa_enabled')
        return True
    
    async def verify_mfa_code(self, user_id: int, code: str) -> bool:
        """验证MFA代码"""
        
        # 限制验证频率
        if not await self.rate_limiter.is_allowed(f"mfa_auth_{user_id}", 10, 300):
            raise RateLimitError("Too many MFA attempts")
        
        user_mfa = await self.get_user_mfa_settings(user_id)
        if not user_mfa or not user_mfa['enabled']:
            raise MFAError("MFA not enabled for user")
        
        # 首先尝试TOTP验证
        totp = pyotp.TOTP(user_mfa['secret_key'])
        if totp.verify(code, valid_window=1):
            await self.log_mfa_event(user_id, 'totp_verified')
            return True
        
        # 尝试备用码验证
        if await self.verify_backup_code(user_id, code):
            await self.log_mfa_event(user_id, 'backup_code_used')
            return True
        
        await self.log_mfa_event(user_id, 'mfa_verification_failed')
        return False
```

### 2. 权限管理系统

#### RBAC权限模型
```python
class RBACManager:
    def __init__(self):
        self.permission_cache = TTLCache(maxsize=1000, ttl=3600)
        
    # 角色定义
    ROLES = {
        'user': {
            'name': '普通用户',
            'permissions': [
                'game:play',
                'game:submit_score',
                'social:share',
                'social:challenge_friend',
                'profile:view_own',
                'profile:edit_own'
            ]
        },
        'vip_user': {
            'name': 'VIP用户',
            'permissions': [
                'game:play',
                'game:submit_score',
                'game:unlimited_lives',
                'game:skip_question',
                'social:share',
                'social:challenge_friend',
                'content:submit',
                'profile:view_own',
                'profile:edit_own',
                'leaderboard:view_all'
            ]
        },
        'contributor': {
            'name': '内容贡献者',
            'permissions': [
                'game:play',
                'game:submit_score',
                'social:share',
                'social:challenge_friend',
                'content:submit',
                'content:edit_own',
                'content:view_stats',
                'profile:view_own',
                'profile:edit_own'
            ]
        },
        'moderator': {
            'name': '内容审核员',
            'permissions': [
                'game:play',
                'content:review',
                'content:approve',
                'content:reject',
                'user:view_profile',
                'user:moderate',
                'report:handle'
            ]
        },
        'admin': {
            'name': '管理员',
            'permissions': [
                'admin:*',  # 所有管理权限
                'user:*',   # 所有用户操作权限
                'content:*', # 所有内容操作权限
                'system:*'  # 所有系统操作权限
            ]
        }
    }
    
    async def check_permission(self, user_id: int, permission: str) -> bool:
        """检查用户权限"""
        
        # 检查缓存
        cache_key = f"user_perm_{user_id}_{permission}"
        if cache_key in self.permission_cache:
            return self.permission_cache[cache_key]
        
        # 获取用户角色
        user_roles = await self.get_user_roles(user_id)
        
        # 检查权限
        has_permission = False
        for role in user_roles:
            if self.role_has_permission(role, permission):
                has_permission = True
                break
        
        # 缓存结果
        self.permission_cache[cache_key] = has_permission
        
        return has_permission
    
    def role_has_permission(self, role: str, permission: str) -> bool:
        """检查角色是否拥有指定权限"""
        
        if role not in self.ROLES:
            return False
        
        role_permissions = self.ROLES[role]['permissions']
        
        # 检查通配符权限
        for role_perm in role_permissions:
            if role_perm.endswith('*'):
                # 通配符匹配
                prefix = role_perm[:-1]
                if permission.startswith(prefix):
                    return True
            elif role_perm == permission:
                # 精确匹配
                return True
        
        return False
    
    async def assign_role(self, user_id: int, role: str, assigned_by: int) -> bool:
        """分配角色"""
        
        if role not in self.ROLES:
            raise ValueError(f"Invalid role: {role}")
        
        # 检查分配者权限
        if not await self.check_permission(assigned_by, 'user:assign_role'):
            raise PermissionError("Insufficient permissions to assign roles")
        
        # 分配角色
        await self.db.execute("""
            INSERT INTO user_roles (user_id, role, assigned_by, assigned_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
                assigned_by = VALUES(assigned_by),
                assigned_at = VALUES(assigned_at)
        """, (user_id, role, assigned_by))
        
        # 清除权限缓存
        await self.clear_user_permission_cache(user_id)
        
        # 记录操作日志
        await self.log_role_assignment(user_id, role, assigned_by)
        
        return True
    
    async def get_user_effective_permissions(self, user_id: int) -> list:
        """获取用户有效权限列表"""
        
        user_roles = await self.get_user_roles(user_id)
        permissions = set()
        
        for role in user_roles:
            if role in self.ROLES:
                permissions.update(self.ROLES[role]['permissions'])
        
        return list(permissions)
```

## 🛡️ 数据安全保护

### 1. 数据加密策略

#### 敏感数据加密
```python
class DataEncryption:
    def __init__(self):
        self.encryption_key = self.load_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # 不同类型数据的加密策略
        self.encryption_policies = {
            'pii': {  # 个人身份信息
                'algorithm': 'AES-256-GCM',
                'key_rotation_days': 90,
                'fields': ['phone', 'email', 'real_name', 'id_card']
            },
            'sensitive': {  # 敏感业务数据
                'algorithm': 'AES-256-CBC',
                'key_rotation_days': 180,
                'fields': ['payment_info', 'location', 'device_id']
            },
            'internal': {  # 内部数据
                'algorithm': 'AES-128-CBC',
                'key_rotation_days': 365,
                'fields': ['session_data', 'preferences']
            }
        }
    
    def encrypt_field(self, field_name: str, value: str) -> str:
        """加密字段数据"""
        if not value:
            return value
        
        # 确定加密策略
        policy = self.get_field_encryption_policy(field_name)
        if not policy:
            return value  # 不需要加密的字段
        
        try:
            # 加密数据
            encrypted_data = self.cipher_suite.encrypt(value.encode('utf-8'))
            
            # 添加元数据
            return self.format_encrypted_data(encrypted_data, policy)
            
        except Exception as e:
            logger.error(f"Failed to encrypt field {field_name}: {e}")
            raise EncryptionError(f"Encryption failed for field {field_name}")
    
    def decrypt_field(self, field_name: str, encrypted_value: str) -> str:
        """解密字段数据"""
        if not encrypted_value or not self.is_encrypted_data(encrypted_value):
            return encrypted_value
        
        try:
            # 解析加密数据
            encrypted_data, metadata = self.parse_encrypted_data(encrypted_value)
            
            # 解密
            decrypted_bytes = self.cipher_suite.decrypt(encrypted_data)
            return decrypted_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to decrypt field {field_name}: {e}")
            raise DecryptionError(f"Decryption failed for field {field_name}")
    
    def get_field_encryption_policy(self, field_name: str) -> dict:
        """获取字段加密策略"""
        for policy_name, policy in self.encryption_policies.items():
            if field_name in policy['fields']:
                return policy
        return None
    
    async def rotate_encryption_keys(self):
        """密钥轮换"""
        for policy_name, policy in self.encryption_policies.items():
            last_rotation = await self.get_last_key_rotation(policy_name)
            
            if self.should_rotate_key(last_rotation, policy['key_rotation_days']):
                await self.perform_key_rotation(policy_name, policy)
    
    async def perform_key_rotation(self, policy_name: str, policy: dict):
        """执行密钥轮换"""
        logger.info(f"Starting key rotation for policy {policy_name}")
        
        # 生成新密钥
        new_key = Fernet.generate_key()
        
        # 获取所有使用此策略的加密数据
        encrypted_fields = await self.get_encrypted_fields_by_policy(policy_name)
        
        # 重新加密数据
        for field_info in encrypted_fields:
            await self.reencrypt_field_data(field_info, new_key)
        
        # 更新密钥
        await self.update_encryption_key(policy_name, new_key)
        
        # 记录轮换事件
        await self.log_key_rotation_event(policy_name)
        
        logger.info(f"Key rotation completed for policy {policy_name}")
```

#### 传输加密
```python
class TransportSecurity:
    def __init__(self):
        self.tls_config = {
            'min_version': ssl.TLSVersion.TLSv1_2,
            'ciphers': [
                'ECDHE-RSA-AES256-GCM-SHA384',
                'ECDHE-RSA-AES128-GCM-SHA256',
                'ECDHE-RSA-AES256-SHA384',
                'ECDHE-RSA-AES128-SHA256'
            ],
            'cert_file': '/path/to/server.crt',
            'key_file': '/path/to/server.key'
        }
    
    def create_secure_context(self) -> ssl.SSLContext:
        """创建安全SSL上下文"""
        context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        
        # 设置最小TLS版本
        context.minimum_version = self.tls_config['min_version']
        
        # 设置密码套件
        context.set_ciphers(':'.join(self.tls_config['ciphers']))
        
        # 加载证书
        context.load_cert_chain(
            self.tls_config['cert_file'],
            self.tls_config['key_file']
        )
        
        # 安全选项
        context.options |= ssl.OP_NO_SSLv2
        context.options |= ssl.OP_NO_SSLv3
        context.options |= ssl.OP_NO_TLSv1
        context.options |= ssl.OP_NO_TLSv1_1
        context.options |= ssl.OP_SINGLE_DH_USE
        context.options |= ssl.OP_SINGLE_ECDH_USE
        
        return context
    
    async def validate_certificate(self, hostname: str, cert: bytes) -> bool:
        """验证证书有效性"""
        try:
            # 解析证书
            certificate = x509.load_pem_x509_certificate(cert, default_backend())
            
            # 检查有效期
            now = datetime.utcnow()
            if now < certificate.not_valid_before or now > certificate.not_valid_after:
                return False
            
            # 检查主机名
            if not self.verify_hostname(certificate, hostname):
                return False
            
            # 检查证书链
            if not await self.verify_certificate_chain(certificate):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Certificate validation failed: {e}")
            return False
    
    def setup_certificate_pinning(self, pinned_certs: list):
        """设置证书固定"""
        self.pinned_certificate_hashes = set()
        
        for cert_data in pinned_certs:
            # 计算证书指纹
            cert_hash = hashlib.sha256(cert_data).hexdigest()
            self.pinned_certificate_hashes.add(cert_hash)
    
    def verify_pinned_certificate(self, cert_data: bytes) -> bool:
        """验证固定证书"""
        cert_hash = hashlib.sha256(cert_data).hexdigest()
        return cert_hash in self.pinned_certificate_hashes
```

### 2. 数据脱敏与匿名化

#### 数据脱敏策略
```python
class DataMasking:
    def __init__(self):
        self.masking_rules = {
            'phone': self.mask_phone,
            'email': self.mask_email,
            'id_card': self.mask_id_card,
            'name': self.mask_name,
            'address': self.mask_address
        }
    
    def mask_sensitive_data(self, data: dict, context: str = 'log') -> dict:
        """脱敏敏感数据"""
        masked_data = data.copy()
        
        for field, value in data.items():
            if field in self.masking_rules and value:
                masked_data[field] = self.masking_rules[field](value, context)
        
        return masked_data
    
    def mask_phone(self, phone: str, context: str) -> str:
        """手机号脱敏"""
        if len(phone) != 11:
            return phone
        
        if context == 'log':
            return phone[:3] + '****' + phone[-4:]
        elif context == 'display':
            return phone[:3] + '****' + phone[-2:]
        else:
            return '***********'
    
    def mask_email(self, email: str, context: str) -> str:
        """邮箱脱敏"""
        if '@' not in email:
            return email
        
        username, domain = email.split('@', 1)
        
        if context == 'log':
            masked_username = username[:2] + '*' * (len(username) - 2)
        else:
            masked_username = username[0] + '*' * (len(username) - 1)
        
        return f"{masked_username}@{domain}"
    
    def mask_id_card(self, id_card: str, context: str) -> str:
        """身份证脱敏"""
        if len(id_card) != 18:
            return id_card
        
        if context == 'log':
            return id_card[:6] + '********' + id_card[-4:]
        else:
            return id_card[:4] + '**********' + id_card[-2:]
    
    async def anonymize_dataset(self, dataset: list, keep_fields: list = None) -> list:
        """数据集匿名化"""
        keep_fields = keep_fields or []
        anonymized_data = []
        
        # 生成匿名化映射
        id_mapping = {}
        
        for record in dataset:
            anonymized_record = {}
            
            for field, value in record.items():
                if field in keep_fields:
                    anonymized_record[field] = value
                elif field == 'user_id':
                    # 用户ID匿名化
                    if value not in id_mapping:
                        id_mapping[value] = f"user_{len(id_mapping) + 1}"
                    anonymized_record[field] = id_mapping[value]
                elif field in self.masking_rules:
                    anonymized_record[field] = self.masking_rules[field](value, 'anonymize')
                else:
                    anonymized_record[field] = value
            
            anonymized_data.append(anonymized_record)
        
        return anonymized_data
```

## 🔍 安全监控与审计

### 1. 安全事件监控

#### 实时安全监控
```python
class SecurityMonitor:
    def __init__(self):
        self.threat_patterns = self.load_threat_patterns()
        self.security_rules = self.load_security_rules()
        self.alert_manager = AlertManager()
        
        # 威胁等级定义
        self.threat_levels = {
            'critical': {'score': 90, 'response_time': 300},    # 5分钟
            'high': {'score': 70, 'response_time': 1800},       # 30分钟
            'medium': {'score': 50, 'response_time': 7200},     # 2小时
            'low': {'score': 30, 'response_time': 86400}        # 24小时
        }
    
    async def monitor_security_events(self, event: SecurityEvent):
        """监控安全事件"""
        
        # 基础安全检查
        threats = await self.detect_threats(event)
        
        # 行为分析
        behavioral_anomalies = await self.analyze_behavior(event)
        
        # 威胁评分
        threat_score = self.calculate_threat_score(threats, behavioral_anomalies)
        
        # 触发响应
        if threat_score >= self.threat_levels['critical']['score']:
            await self.handle_critical_threat(event, threats)
        elif threat_score >= self.threat_levels['high']['score']:
            await self.handle_high_threat(event, threats)
        elif threat_score >= self.threat_levels['medium']['score']:
            await self.handle_medium_threat(event, threats)
        
        # 记录安全事件
        await self.log_security_event(event, threats, threat_score)
    
    async def detect_threats(self, event: SecurityEvent) -> list:
        """威胁检测"""
        detected_threats = []
        
        # SQL注入检测
        if self.detect_sql_injection(event):
            detected_threats.append({
                'type': 'sql_injection',
                'severity': 'high',
                'pattern': 'SQL injection pattern detected',
                'evidence': event.payload
            })
        
        # XSS攻击检测
        if self.detect_xss_attack(event):
            detected_threats.append({
                'type': 'xss_attack',
                'severity': 'medium',
                'pattern': 'XSS payload detected',
                'evidence': event.payload
            })
        
        # 暴力破解检测
        if await self.detect_brute_force(event):
            detected_threats.append({
                'type': 'brute_force',
                'severity': 'high',
                'pattern': 'Multiple failed login attempts',
                'evidence': {'ip': event.source_ip, 'attempts': event.attempt_count}
            })
        
        # 异常访问模式检测
        if await self.detect_anomalous_access(event):
            detected_threats.append({
                'type': 'anomalous_access',
                'severity': 'medium',
                'pattern': 'Unusual access pattern',
                'evidence': event.access_pattern
            })
        
        return detected_threats
    
    def detect_sql_injection(self, event: SecurityEvent) -> bool:
        """SQL注入检测"""
        sql_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)",
            r"(\b(UNION|OR|AND)\b.*\b(SELECT|INSERT|UPDATE|DELETE)\b)",
            r"('+.*--)",
            r"(\bOR\b.*=.*\bOR\b)",
            r"(\b1=1\b|\b1=2\b)"
        ]
        
        payload = event.payload.upper() if event.payload else ""
        
        for pattern in sql_patterns:
            if re.search(pattern, payload, re.IGNORECASE):
                return True
        
        return False
    
    def detect_xss_attack(self, event: SecurityEvent) -> bool:
        """XSS攻击检测"""
        xss_patterns = [
            r"<script[^>]*>.*</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"document\.cookie",
            r"alert\s*\(",
            r"eval\s*\("
        ]
        
        payload = event.payload if event.payload else ""
        
        for pattern in xss_patterns:
            if re.search(pattern, payload, re.IGNORECASE):
                return True
        
        return False
    
    async def detect_brute_force(self, event: SecurityEvent) -> bool:
        """暴力破解检测"""
        if event.event_type != 'login_attempt':
            return False
        
        # 获取最近的登录尝试记录
        recent_attempts = await self.get_recent_login_attempts(
            event.source_ip, 
            minutes=15
        )
        
        failed_attempts = [a for a in recent_attempts if not a.success]
        
        # 15分钟内超过5次失败登录认为是暴力破解
        return len(failed_attempts) > 5
    
    async def handle_critical_threat(self, event: SecurityEvent, threats: list):
        """处理严重威胁"""
        logger.critical(f"Critical threat detected: {threats}")
        
        # 立即阻止来源IP
        await self.block_ip_address(event.source_ip, duration=86400)  # 24小时
        
        # 发送紧急告警
        await self.alert_manager.send_critical_alert(
            title="严重安全威胁检测",
            message=f"检测到严重安全威胁，已自动阻止源IP: {event.source_ip}",
            threats=threats
        )
        
        # 触发安全响应流程
        await self.trigger_incident_response(event, threats)
    
    async def analyze_behavior(self, event: SecurityEvent) -> list:
        """行为分析"""
        anomalies = []
        
        # 时间异常检测
        if await self.detect_time_anomaly(event):
            anomalies.append({
                'type': 'time_anomaly',
                'description': 'Access at unusual time',
                'severity': 'low'
            })
        
        # 地理位置异常检测
        if await self.detect_location_anomaly(event):
            anomalies.append({
                'type': 'location_anomaly',
                'description': 'Access from unusual location',
                'severity': 'medium'
            })
        
        # 设备异常检测
        if await self.detect_device_anomaly(event):
            anomalies.append({
                'type': 'device_anomaly',
                'description': 'Access from new/unusual device',
                'severity': 'low'
            })
        
        return anomalies
```

### 2. 审计日志系统

#### 安全审计日志
```python
class SecurityAuditLogger:
    def __init__(self):
        self.audit_categories = {
            'authentication': ['login', 'logout', 'token_refresh', 'mfa'],
            'authorization': ['permission_check', 'role_assignment', 'access_denied'],
            'data_access': ['data_read', 'data_write', 'data_delete', 'sensitive_access'],
            'security_events': ['threat_detected', 'ip_blocked', 'security_rule_triggered'],
            'system_events': ['config_change', 'key_rotation', 'backup_restore']
        }
    
    async def log_security_event(self, event_type: str, user_id: int = None, 
                                details: dict = None, severity: str = 'info'):
        """记录安全事件"""
        
        audit_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'user_id': user_id,
            'session_id': self.get_current_session_id(),
            'source_ip': self.get_client_ip(),
            'user_agent': self.get_user_agent(),
            'severity': severity,
            'details': details or {},
            'trace_id': self.generate_trace_id()
        }
        
        # 根据事件类型确定存储策略
        category = self.get_event_category(event_type)
        
        # 存储到审计日志
        await self.store_audit_record(audit_record, category)
        
        # 如果是高级别安全事件，同时发送到SIEM
        if severity in ['warning', 'error', 'critical']:
            await self.send_to_siem(audit_record)
    
    async def log_data_access(self, operation: str, table: str, record_id: str = None,
                             user_id: int = None, sensitive_fields: list = None):
        """记录数据访问"""
        
        details = {
            'operation': operation,
            'table': table,
            'record_id': record_id,
            'sensitive_fields': sensitive_fields or [],
            'query_hash': self.generate_query_hash()
        }
        
        await self.log_security_event(
            event_type='data_access',
            user_id=user_id,
            details=details,
            severity='info' if not sensitive_fields else 'warning'
        )
    
    async def log_authentication_event(self, event_type: str, user_id: int = None,
                                     success: bool = True, failure_reason: str = None):
        """记录认证事件"""
        
        details = {
            'success': success,
            'failure_reason': failure_reason,
            'auth_method': self.get_auth_method(),
            'device_fingerprint': self.get_device_fingerprint()
        }
        
        severity = 'info' if success else 'warning'
        if event_type == 'login' and not success:
            # 多次登录失败升级严重度
            recent_failures = await self.get_recent_login_failures(user_id)
            if len(recent_failures) > 3:
                severity = 'error'
        
        await self.log_security_event(
            event_type=f"auth_{event_type}",
            user_id=user_id,
            details=details,
            severity=severity
        )
    
    async def generate_compliance_report(self, report_type: str, 
                                       start_date: datetime, end_date: datetime) -> dict:
        """生成合规报告"""
        
        # 获取指定时间范围的审计记录
        audit_records = await self.get_audit_records(start_date, end_date)
        
        report_generators = {
            'access_report': self.generate_access_report,
            'security_incident_report': self.generate_security_incident_report,
            'data_protection_report': self.generate_data_protection_report,
            'authentication_report': self.generate_authentication_report
        }
        
        generator = report_generators.get(report_type)
        if not generator:
            raise ValueError(f"Unknown report type: {report_type}")
        
        return await generator(audit_records, start_date, end_date)
    
    async def generate_security_incident_report(self, audit_records: list, 
                                              start_date: datetime, end_date: datetime) -> dict:
        """生成安全事件报告"""
        
        # 筛选安全事件
        security_events = [r for r in audit_records 
                          if r['event_type'].startswith('security_') or 
                             r['severity'] in ['error', 'critical']]
        
        # 按类型分组统计
        event_stats = {}
        for event in security_events:
            event_type = event['event_type']
            if event_type not in event_stats:
                event_stats[event_type] = {
                    'count': 0,
                    'severity_breakdown': {},
                    'affected_users': set(),
                    'source_ips': set()
                }
            
            stats = event_stats[event_type]
            stats['count'] += 1
            stats['severity_breakdown'][event['severity']] = \
                stats['severity_breakdown'].get(event['severity'], 0) + 1
            
            if event['user_id']:
                stats['affected_users'].add(event['user_id'])
            if event['source_ip']:
                stats['source_ips'].add(event['source_ip'])
        
        # 转换set为count
        for stats in event_stats.values():
            stats['affected_users'] = len(stats['affected_users'])
            stats['source_ips'] = len(stats['source_ips'])
        
        return {
            'report_type': 'security_incident_report',
            'period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'summary': {
                'total_security_events': len(security_events),
                'critical_events': len([e for e in security_events if e['severity'] == 'critical']),
                'unique_threat_types': len(event_stats)
            },
            'event_statistics': event_stats,
            'generated_at': datetime.utcnow().isoformat()
        }
```

---

**文档版本**: v1.0  
**最后更新**: 2024-07-30  
**负责团队**: architect-agent  
**审核状态**: ✅ 已审核通过