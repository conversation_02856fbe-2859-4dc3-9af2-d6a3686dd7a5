# 性能需求规范

## 🎯 性能目标

### 核心性能指标
- **首屏加载**: <3秒完成游戏可玩状态
- **API响应**: <500ms平均响应时间  
- **音频加载**: <2秒完成预加载
- **系统可用性**: 99.9%正常运行时间
- **并发处理**: 支持10K+同时在线用户

### 用户体验标准
- **交互响应**: 点击响应<100ms
- **场景切换**: 场景转换<1秒
- **网络适应**: 2G网络下可正常游戏
- **设备兼容**: 支持低端设备流畅运行

## 📱 前端性能需求

### 1. 加载性能指标

#### 首屏加载性能
```
关键指标:
- 白屏时间(FP): <1.5秒
- 首次内容绘制(FCP): <2秒  
- 首次有意义绘制(FMP): <2.5秒
- 首次可交互时间(TTI): <3秒

网络条件分级:
- WiFi/4G: <2秒可玩
- 3G网络: <3秒可玩
- 2G网络: <5秒可玩(降级模式)
```

#### 资源加载优化
```
资源大小限制:
- 首包大小: <4MB(微信小游戏限制)
- 总包大小: <20MB
- 单个音频: <500KB
- 单张图片: <200KB
- 字体文件: <1MB

加载策略:
- 核心资源: 启动时预加载
- 次要资源: 按需懒加载
- 音频资源: 渐进式加载
- 图片资源: WebP格式优先
```

### 2. 运行时性能指标

#### 帧率性能
```
目标帧率:
- 高端设备: 60FPS稳定
- 中端设备: 30FPS稳定  
- 低端设备: 20FPS可接受

帧率监控:
- 连续3秒低于目标帧率触发告警
- 自动降级渲染质量保证流畅性
- 实时监控并上报性能数据
```

#### 内存使用
```
内存限制:
- iOS设备: <150MB峰值内存
- Android设备: <200MB峰值内存
- 微信环境: <100MB安全阈值

内存管理:
- 对象池技术减少GC压力
- 纹理压缩节省显存
- 及时释放不用资源
- 内存泄漏实时检测
```

### 3. 网络性能需求

#### 数据传输优化
```
传输大小:
- API请求: <10KB平均
- 音频下载: 分片传输,每片<100KB
- 图片资源: 多尺寸适配
- 配置数据: 增量更新

网络适配:
- 弱网环境自动降级
- 断网情况离线模式
- 网络切换无缝重连
- 请求失败自动重试(指数退避)
```

## ⚙️ 后端性能需求

### 1. API响应性能

#### 响应时间要求
```
核心接口性能标准:
- 用户登录: <200ms (95分位)
- 获取题目: <300ms (95分位)
- 提交答案: <400ms (95分位)
- 排行榜查询: <500ms (95分位)
- 音频上传: <2秒 (95分位)

性能分级:
- P0接口(登录/题目): <300ms
- P1接口(游戏逻辑): <500ms  
- P2接口(统计/分析): <1秒
- P3接口(管理后台): <2秒
```

#### 并发处理能力
```
并发指标:
- 单函数并发: 1000个实例
- API网关QPS: 10,000请求/秒
- 数据库连接: 单函数最多5个连接
- 缓存命中率: >80%

负载测试标准:
- 正常负载: 1000 QPS稳定运行
- 峰值负载: 5000 QPS短时间处理
- 压力测试: 10000 QPS不宕机
- 恢复能力: 故障后5分钟内恢复
```

### 2. 数据库性能

#### 查询性能标准
```
查询响应时间:
- 主键查询: <10ms
- 索引查询: <50ms
- 复杂查询: <200ms
- 聚合统计: <500ms

慢查询监控:
- >100ms查询记录日志
- >1秒查询触发告警
- 每日慢查询分析报告
- 自动SQL优化建议
```

#### 数据库连接管理
```
连接池配置:
- 最小连接数: 5
- 最大连接数: 100
- 连接超时: 30秒
- 空闲超时: 300秒

连接复用策略:
- 函数级连接池
- 跨请求连接复用
- 智能连接负载均衡
- 连接泄漏自动检测
```

### 3. 缓存性能需求

#### Redis缓存指标
```
缓存性能:
- 缓存命中率: >85%
- 缓存响应时间: <5ms
- 内存使用率: <80%
- 键空间命中率: >90%

缓存策略:
- 热点数据预热
- LRU淘汰策略
- 分布式缓存一致性
- 缓存雪崩防护
```

## 📊 性能监控和告警

### 1. 实时监控指标

#### 前端监控
```typescript
class PerformanceMonitor {
    // 性能指标收集
    private metrics = {
        fps: 0,
        memory: 0,
        loadTime: 0,
        apiResponseTime: 0,
        errorRate: 0
    };
    
    // FPS监控
    monitorFPS() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                this.metrics.fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
                
                // FPS过低告警
                if (this.metrics.fps < 20) {
                    this.reportPerformanceIssue('low_fps', this.metrics.fps);
                }
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }
    
    // 内存监控
    monitorMemory() {
        if (performance.memory) {
            const memInfo = performance.memory;
            this.metrics.memory = memInfo.usedJSHeapSize / 1048576; // MB
            
            // 内存使用过高告警
            if (this.metrics.memory > 100) {
                this.reportPerformanceIssue('high_memory', this.metrics.memory);
            }
        }
    }
    
    // API响应时间监控
    monitorAPIResponse(apiName: string, responseTime: number) {
        this.metrics.apiResponseTime = responseTime;
        
        // 响应时间过长告警
        if (responseTime > 1000) {
            this.reportPerformanceIssue('slow_api', {
                api: apiName,
                responseTime: responseTime
            });
        }
    }
    
    // 上报性能问题
    private reportPerformanceIssue(type: string, data: any) {
        const report = {
            type: type,
            data: data,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        // 发送到监控系统
        this.sendToMonitoring(report);
    }
}
```

#### 后端监控
```python
import time
import psutil
import asyncio
from typing import Dict, Any

class BackendPerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'response_times': [],
            'error_count': 0,
            'request_count': 0,
            'cpu_usage': 0,
            'memory_usage': 0,
            'db_connection_count': 0
        }
        
        # 启动监控任务
        asyncio.create_task(self.monitor_system_resources())
    
    async def monitor_api_performance(self, func):
        """API性能监控装饰器"""
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                self.metrics['request_count'] += 1
                return result
                
            except Exception as e:
                self.metrics['error_count'] += 1
                raise
                
            finally:
                response_time = (time.time() - start_time) * 1000
                self.metrics['response_times'].append(response_time)
                
                # 保持最近1000次请求的数据
                if len(self.metrics['response_times']) > 1000:
                    self.metrics['response_times'] = self.metrics['response_times'][-1000:]
                
                # 性能告警检查
                await self.check_performance_alerts(response_time)
        
        return wrapper
    
    async def monitor_system_resources(self):
        """系统资源监控"""
        while True:
            try:
                # CPU使用率
                self.metrics['cpu_usage'] = psutil.cpu_percent(interval=1)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.metrics['memory_usage'] = memory.percent
                
                # 检查资源告警
                if self.metrics['cpu_usage'] > 80:
                    await self.send_alert('high_cpu', self.metrics['cpu_usage'])
                
                if self.metrics['memory_usage'] > 85:
                    await self.send_alert('high_memory', self.metrics['memory_usage'])
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
    
    async def check_performance_alerts(self, response_time: float):
        """性能告警检查"""
        # 响应时间告警
        if response_time > 1000:
            await self.send_alert('slow_response', response_time)
        
        # 错误率告警
        if self.metrics['request_count'] > 100:
            error_rate = self.metrics['error_count'] / self.metrics['request_count']
            if error_rate > 0.05:  # 5%错误率
                await self.send_alert('high_error_rate', error_rate)
        
        # 平均响应时间告警
        if len(self.metrics['response_times']) >= 100:
            avg_response_time = sum(self.metrics['response_times'][-100:]) / 100
            if avg_response_time > 500:
                await self.send_alert('avg_slow_response', avg_response_time)
    
    async def send_alert(self, alert_type: str, value: float):
        """发送告警通知"""
        alert_data = {
            'type': alert_type,
            'value': value,
            'timestamp': time.time(),
            'service': 'hometown-dialect-backend'
        }
        
        # 发送到告警系统(如钉钉、邮件等)
        await self.send_to_alert_system(alert_data)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        response_times = self.metrics['response_times']
        
        if not response_times:
            return {}
        
        return {
            'avg_response_time': sum(response_times) / len(response_times),
            'p95_response_time': sorted(response_times)[int(len(response_times) * 0.95)],
            'p99_response_time': sorted(response_times)[int(len(response_times) * 0.99)],
            'request_count': self.metrics['request_count'],
            'error_count': self.metrics['error_count'],
            'error_rate': self.metrics['error_count'] / max(self.metrics['request_count'], 1),
            'cpu_usage': self.metrics['cpu_usage'],
            'memory_usage': self.metrics['memory_usage']
        }
```

### 2. 告警策略

#### 告警级别定义
```yaml
告警级别:
  P0-紧急 (5分钟内处理):
    - 系统不可用 (可用性<95%)
    - API响应时间>2秒持续5分钟
    - 错误率>10%持续3分钟
    - 数据库连接失败
    
  P1-重要 (30分钟内处理):
    - API响应时间>1秒持续10分钟
    - 错误率>5%持续10分钟
    - CPU使用率>90%持续5分钟
    - 内存使用率>90%持续5分钟
    
  P2-一般 (2小时内处理):
    - API响应时间>500ms持续30分钟
    - 缓存命中率<70%持续30分钟
    - 磁盘使用率>80%
    - 慢查询数量异常增长
```

#### 告警通知渠道
```python
class AlertManager:
    def __init__(self):
        self.channels = {
            'dingtalk': DingtalkNotifier(),
            'email': EmailNotifier(),
            'sms': SMSNotifier(),
            'wechat': WechatNotifier()
        }
    
    async def send_alert(self, alert: Alert):
        """根据告警级别选择通知渠道"""
        if alert.level == 'P0':
            # 紧急告警：所有渠道通知
            await asyncio.gather(
                self.channels['dingtalk'].send(alert),
                self.channels['sms'].send(alert),
                self.channels['wechat'].send(alert)
            )
        elif alert.level == 'P1':
            # 重要告警：钉钉+邮件
            await asyncio.gather(
                self.channels['dingtalk'].send(alert),
                self.channels['email'].send(alert)
            )
        else:
            # 一般告警：钉钉通知
            await self.channels['dingtalk'].send(alert)
```

## 🔧 性能优化策略

### 1. 自动性能优化

#### 自适应降级策略
```typescript
class AdaptivePerformanceManager {
    private performanceLevel: 'high' | 'medium' | 'low' = 'high';
    private fpsHistory: number[] = [];
    
    // 性能等级自动调整
    adjustPerformanceLevel() {
        const avgFPS = this.getAverageFPS();
        const memoryUsage = this.getMemoryUsage();
        
        if (avgFPS < 20 || memoryUsage > 100) {
            this.performanceLevel = 'low';
            this.applyLowPerformanceSettings();
        } else if (avgFPS < 45 || memoryUsage > 80) {
            this.performanceLevel = 'medium';
            this.applyMediumPerformanceSettings();
        } else {
            this.performanceLevel = 'high';
            this.applyHighPerformanceSettings();
        }
    }
    
    // 低性能模式设置
    private applyLowPerformanceSettings() {
        // 降低音频质量
        AudioManager.getInstance().setQuality('low');
        
        // 减少粒子效果
        EffectManager.getInstance().setQuality('low');
        
        // 降低渲染分辨率
        this.setRenderScale(0.8);
        
        // 简化UI动画
        UIManager.getInstance().disableAnimations();
    }
    
    // 中性能模式设置
    private applyMediumPerformanceSettings() {
        AudioManager.getInstance().setQuality('medium');
        EffectManager.getInstance().setQuality('medium');
        this.setRenderScale(0.9);
        UIManager.getInstance().enableSimpleAnimations();
    }
    
    // 高性能模式设置
    private applyHighPerformanceSettings() {
        AudioManager.getInstance().setQuality('high');
        EffectManager.getInstance().setQuality('high');
        this.setRenderScale(1.0);
        UIManager.getInstance().enableAllAnimations();
    }
}
```

#### 智能缓存预热
```python
class IntelligentCacheWarmer:
    def __init__(self):
        self.user_behavior_predictor = UserBehaviorPredictor()
        self.cache_manager = CacheManager()
    
    async def predictive_cache_warmup(self, user_id: int):
        """基于用户行为预测的缓存预热"""
        
        # 分析用户历史行为
        user_patterns = await self.user_behavior_predictor.analyze_patterns(user_id)
        
        # 预测用户可能访问的内容
        predicted_content = self.predict_next_content(user_patterns)
        
        # 预热相关缓存
        for content in predicted_content:
            await self.cache_manager.warmup_content(content)
    
    def predict_next_content(self, patterns: dict) -> list:
        """预测用户下一步可能访问的内容"""
        predictions = []
        
        # 基于时间模式预测
        if patterns.get('preferred_time') == 'evening':
            predictions.extend(self.get_evening_popular_content())
        
        # 基于方言偏好预测
        preferred_dialect = patterns.get('preferred_dialect')
        if preferred_dialect:
            predictions.extend(
                self.get_dialect_related_content(preferred_dialect)
            )
        
        # 基于难度进展预测
        current_level = patterns.get('current_level', 1)
        predictions.extend(
            self.get_level_appropriate_content(current_level)
        )
        
        return predictions[:10]  # 限制预热内容数量
```

### 2. 资源优化策略

#### 音频资源优化
```python
class AudioOptimizer:
    def __init__(self):
        self.compression_configs = {
            'high': {'bitrate': 128, 'sample_rate': 44100},
            'medium': {'bitrate': 96, 'sample_rate': 22050},
            'low': {'bitrate': 64, 'sample_rate': 16000}
        }
    
    async def optimize_audio_for_device(self, audio_url: str, device_info: dict) -> str:
        """根据设备能力优化音频"""
        
        # 根据网络状况选择质量
        network_type = device_info.get('network_type')
        if network_type in ['2g', 'slow-2g']:
            quality = 'low'
        elif network_type == '3g':
            quality = 'medium'
        else:
            quality = 'high'
        
        # 生成优化后的音频URL
        optimized_url = await self.generate_optimized_audio(audio_url, quality)
        
        return optimized_url
    
    async def batch_optimize_audio(self, audio_list: list):
        """批量音频优化"""
        tasks = []
        for audio in audio_list:
            for quality in ['high', 'medium', 'low']:
                task = self.optimize_single_audio(audio, quality)
                tasks.append(task)
        
        await asyncio.gather(*tasks)
```

### 3. 成本性能平衡

#### 动态资源分配
```python
class DynamicResourceAllocator:
    def __init__(self):
        self.current_load = 0
        self.cost_budget = 285  # 月预算$285
        self.cost_tracker = CostTracker()
    
    async def allocate_resources_based_on_load(self):
        """根据负载动态分配资源"""
        
        current_cost = await self.cost_tracker.get_monthly_cost()
        remaining_budget = self.cost_budget - current_cost
        days_remaining = self.get_days_remaining_in_month()
        
        # 计算每日预算
        daily_budget = remaining_budget / max(days_remaining, 1)
        
        # 根据预算调整资源配置
        if daily_budget < 5:  # 预算紧张
            await self.apply_cost_saving_mode()
        elif daily_budget > 15:  # 预算充足
            await self.apply_performance_mode()
        else:  # 预算适中
            await self.apply_balanced_mode()
    
    async def apply_cost_saving_mode(self):
        """成本节约模式"""
        # 降低函数内存配置
        await self.update_function_memory('auth-login', 128)
        await self.update_function_memory('game-logic', 256)
        
        # 增加缓存TTL
        await self.cache_manager.extend_ttl_globally(2.0)
        
        # 启用更激进的压缩
        await self.enable_aggressive_compression()
    
    async def apply_performance_mode(self):
        """性能优先模式"""
        # 提高函数内存配置
        await self.update_function_memory('auth-login', 256)
        await self.update_function_memory('game-logic', 512)
        
        # 缩短缓存TTL提高数据新鲜度
        await self.cache_manager.reduce_ttl_globally(0.5)
        
        # 启用预热机制
        await self.enable_cache_warmup()
```

## 📈 性能测试策略

### 1. 压力测试计划

#### 负载测试场景
```yaml
测试场景配置:
  正常负载测试:
    用户数: 1000
    持续时间: 30分钟
    操作: 登录->游戏->分享循环
    成功率要求: >99%
    
  峰值负载测试:
    用户数: 5000
    持续时间: 10分钟
    操作: 高频游戏操作
    成功率要求: >95%
    
  压力测试:
    用户数: 10000
    持续时间: 5分钟
    操作: 混合操作场景
    系统要求: 不宕机，有限降级
    
  稳定性测试:
    用户数: 500
    持续时间: 24小时
    操作: 随机用户行为模拟
    内存泄漏检测: 无明显增长
```

#### 自动化测试脚本
```python
import asyncio
import aiohttp
import time
import random
from dataclasses import dataclass
from typing import List

@dataclass
class TestResult:
    response_time: float
    status_code: int
    success: bool
    error_message: str = None

class PerformanceTestRunner:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = None
        self.results: List[TestResult] = []
    
    async def run_load_test(self, concurrent_users: int, duration_minutes: int):
        """运行负载测试"""
        
        async with aiohttp.ClientSession() as session:
            self.session = session
            
            # 创建并发用户任务
            tasks = []
            for i in range(concurrent_users):
                task = asyncio.create_task(
                    self.simulate_user_behavior(f"user_{i}", duration_minutes)
                )
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
            
            # 生成测试报告
            return self.generate_test_report()
    
    async def simulate_user_behavior(self, user_id: str, duration_minutes: int):
        """模拟用户行为"""
        end_time = time.time() + (duration_minutes * 60)
        
        while time.time() < end_time:
            try:
                # 登录
                await self.test_login(user_id)
                
                # 游戏会话
                for _ in range(random.randint(1, 5)):
                    await self.test_get_question()
                    await asyncio.sleep(random.uniform(2, 8))  # 模拟答题时间
                    await self.test_submit_answer()
                
                # 查看排行榜
                if random.random() < 0.3:
                    await self.test_leaderboard()
                
                # 分享操作
                if random.random() < 0.2:
                    await self.test_share()
                
                await asyncio.sleep(random.uniform(5, 15))  # 用户间隔
                
            except Exception as e:
                self.results.append(TestResult(0, 0, False, str(e)))
    
    async def test_api_endpoint(self, method: str, endpoint: str, data: dict = None) -> TestResult:
        """测试API端点"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                async with self.session.get(url) as response:
                    response_time = time.time() - start_time
                    await response.text()  # 确保完全接收响应
                    
                    return TestResult(
                        response_time=response_time * 1000,  # 转换为毫秒
                        status_code=response.status,
                        success=response.status == 200
                    )
            
            elif method.upper() == 'POST':
                async with self.session.post(url, json=data) as response:
                    response_time = time.time() - start_time
                    await response.text()
                    
                    return TestResult(
                        response_time=response_time * 1000,
                        status_code=response.status,
                        success=response.status in [200, 201]
                    )
        
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                response_time=response_time * 1000,
                status_code=0,
                success=False,
                error_message=str(e)
            )
    
    def generate_test_report(self) -> dict:
        """生成测试报告"""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r.success]
        response_times = [r.response_time for r in successful_results]
        
        return {
            'total_requests': len(self.results),
            'successful_requests': len(successful_results),
            'success_rate': len(successful_results) / len(self.results) * 100,
            'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
            'p95_response_time': sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0,
            'p99_response_time': sorted(response_times)[int(len(response_times) * 0.99)] if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'error_count': len(self.results) - len(successful_results),
            'errors': [r.error_message for r in self.results if not r.success and r.error_message]
        }

# 测试执行
async def main():
    tester = PerformanceTestRunner('https://api.hometowndialect.com')
    
    # 执行负载测试
    report = await tester.run_load_test(
        concurrent_users=1000,
        duration_minutes=30
    )
    
    print("Performance Test Report:")
    print(f"Success Rate: {report['success_rate']:.2f}%")
    print(f"Average Response Time: {report['avg_response_time']:.2f}ms")
    print(f"P95 Response Time: {report['p95_response_time']:.2f}ms")
    print(f"P99 Response Time: {report['p99_response_time']:.2f}ms")

if __name__ == "__main__":
    asyncio.run(main())
```

### 2. 性能回归测试

#### 持续集成中的性能测试
```yaml
# .github/workflows/performance-test.yml
name: Performance Test

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  performance-test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'
      
      - name: Install dependencies
        run: |
          pip install aiohttp asyncio pytest-asyncio
      
      - name: Deploy to test environment
        run: |
          # 部署到测试环境
          ./deploy-test.sh
      
      - name: Run performance tests
        run: |
          python performance_test.py --env=test --users=100 --duration=5
      
      - name: Performance regression check
        run: |
          python check_performance_regression.py
      
      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('performance_report.json', 'utf8');
            const data = JSON.parse(report);
            
            const comment = `
            ## Performance Test Results
            
            - **Success Rate**: ${data.success_rate.toFixed(2)}%
            - **Average Response Time**: ${data.avg_response_time.toFixed(2)}ms
            - **P95 Response Time**: ${data.p95_response_time.toFixed(2)}ms
            - **P99 Response Time**: ${data.p99_response_time.toFixed(2)}ms
            
            ${data.regression_detected ? '⚠️ Performance regression detected!' : '✅ No performance regression'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
```

---

**文档版本**: v1.0  
**最后更新**: 2024-07-30  
**负责团队**: architect-agent  
**审核状态**: ✅ 已审核通过