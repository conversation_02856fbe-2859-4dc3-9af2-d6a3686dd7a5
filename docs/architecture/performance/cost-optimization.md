# 成本优化策略与预算分析

## 1. 总体成本分析

### 1.1 成本构成分析

基于10万DAU的使用场景，详细成本分析如下：

| 服务组件 | 月用量估算 | 单价 | 月成本($) | 年成本($) | 占比 | 优化潜力 |
|---------|-----------|------|----------|----------|------|----------|
| **计算资源** |
| SCF函数调用 | 5000万次 | $0.000133/万次 | $66.5 | $798 | 24% | 高 |
| SCF执行时长 | 10万GB-s | $0.0000167/GB-s | $16.7 | $200 | 6% | 中 |
| **网络资源** |
| API网关 | 5000万次 | $0.06/万次 | $300→$60* | $720 | 22% | 高 |
| CDN流量 | 1TB | $0.18/GB | $180→$36* | $432 | 13% | 高 |
| **存储资源** |
| TencentDB Serverless | 2核4GB | $0.000056/核·秒 | $95 | $1140 | 34% | 中 |
| Redis缓存 | 1GB内存版 | $0.05/GB/天 | $15 | $180 | 5% | 低 |
| COS对象存储 | 100GB | $0.024/GB | $2.4 | $29 | 1% | 低 |
| **监控运维** |
| 日志服务 | 100GB | $0.13/GB | $13 | $156 | 5% | 中 |
| 监控告警 | 基础版 | $5/月 | $5 | $60 | 2% | 低 |
| **总计** | - | - | **$276** | **$3312** | **100%** | - |

*通过缓存和CDN优化可降低80%成本

### 1.2 成本优化目标

```
当前成本: $276/月
优化目标: $220/月 (节省20%)
关键指标:
- 函数执行成本: 从$83.2降至$65 (节省22%)
- 网络成本: 从$240降至$96 (节省60%)
- 存储成本: 从$112.4降至$100 (节省11%)
```

## 2. 核心优化策略

### 2.1 计算资源优化

#### 函数执行时间优化
```javascript
// 1. 连接池复用优化
class OptimizedConnectionManager {
  constructor() {
    this.pools = new Map();
    this.warmupScheduler = new WarmupScheduler();
  }
  
  // 复用数据库连接池
  getDBPool(config) {
    const key = `${config.host}:${config.database}`;
    if (!this.pools.has(key)) {
      this.pools.set(key, mysql.createPool({
        ...config,
        connectionLimit: 5,           // 降低连接数
        acquireTimeout: 30000,        // 减少等待时间
        timeout: 30000,
        idleTimeout: 300000,          // 5分钟空闲超时
        reconnect: true
      }));
    }
    return this.pools.get(key);
  }
  
  // 复用Redis连接
  getRedisClient(config) {
    const key = `${config.host}:${config.port}`;
    if (!this.pools.has(key)) {
      this.pools.set(key, new Redis({
        ...config,
        lazyConnect: true,
        maxRetriesPerRequest: 2,      // 减少重试次数
        retryDelayOnFailover: 50,     // 减少重试延迟
        enableOfflineQueue: false,    // 禁用离线队列
        connectTimeout: 5000,         // 减少连接超时
        commandTimeout: 3000          // 减少命令超时
      }));
    }
    return this.pools.get(key);
  }
}

// 2. 代码包大小优化
const webpack = require('webpack');

const optimizedWebpackConfig = {
  mode: 'production',
  target: 'node',
  externals: ['aws-sdk'], // 排除云函数环境自带的SDK
  
  optimization: {
    minimize: true,
    sideEffects: false,
    usedExports: true,
    
    // 代码分割
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        }
      }
    }
  },
  
  plugins: [
    // 删除未使用的代码
    new webpack.optimize.ModuleConcatenationPlugin(),
    
    // 环境变量优化
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production')
    })
  ],
  
  resolve: {
    alias: {
      // 使用轻量级替代品
      'moment': 'dayjs',
      'lodash': 'lodash-es'
    }
  }
};

// 3. 预热策略优化
class WarmupScheduler {
  constructor() {
    this.warmupConfig = {
      // 核心函数每5分钟预热一次
      high_priority: {
        functions: ['user-auth', 'game-question', 'game-answer'],
        interval: '0 */5 * * * *',
        concurrency: 3
      },
      
      // 次要函数每15分钟预热一次
      normal_priority: {
        functions: ['user-profile', 'leaderboard', 'share'],
        interval: '0 */15 * * * *',
        concurrency: 2
      },
      
      // 低频函数每小时预热一次
      low_priority: {
        functions: ['admin-stats', 'data-export'],
        interval: '0 0 * * * *',
        concurrency: 1
      }
    };
  }
  
  // 智能预热调度
  async scheduleWarmup() {
    const currentHour = new Date().getHours();
    const isBusinessHour = currentHour >= 8 && currentHour <= 22;
    
    // 业务时间内增加预热频率
    if (isBusinessHour) {
      await this.warmupFunctions('high_priority');
      if (currentHour % 2 === 0) {
        await this.warmupFunctions('normal_priority');
      }
    } else {
      // 非业务时间减少预热频率
      if (currentHour % 4 === 0) {
        await this.warmupFunctions('high_priority');
      }
    }
  }
}
```

#### 内存配置优化策略
```javascript
// 动态内存配置
const OPTIMIZED_MEMORY_CONFIG = {
  // 轻量级函数 - 128MB
  lightweight: {
    functions: ['health-check', 'simple-query'],
    memory: 128,
    expectedDuration: 100,  // 100ms
    costPerExecution: 0.0000002
  },
  
  // 标准函数 - 256MB (最佳性价比)
  standard: {
    functions: ['user-auth', 'question-fetch', 'answer-submit'],
    memory: 256,
    expectedDuration: 300,  // 300ms
    costPerExecution: 0.0000008
  },
  
  // 计算密集型 - 512MB
  compute_intensive: {
    functions: ['leaderboard-calc', 'data-analysis'],
    memory: 512,
    expectedDuration: 800,  // 800ms
    costPerExecution: 0.0000033
  },
  
  // 内存密集型 - 1024MB
  memory_intensive: {
    functions: ['batch-processing', 'report-generation'],
    memory: 1024,
    expectedDuration: 2000, // 2s
    costPerExecution: 0.0000133
  }
};

// 内存使用优化技巧
class MemoryOptimizer {
  // 1. 对象池管理
  constructor() {
    this.objectPools = {
      responses: [],
      requests: [],
      connections: []
    };
  }
  
  getResponse() {
    return this.objectPools.responses.pop() || { data: null, error: null };
  }
  
  releaseResponse(response) {
    response.data = null;
    response.error = null;
    this.objectPools.responses.push(response);
  }
  
  // 2. 流式处理大数据
  async processLargeDataset(dataset) {
    const BATCH_SIZE = 100;
    const results = [];
    
    for (let i = 0; i < dataset.length; i += BATCH_SIZE) {
      const batch = dataset.slice(i, i + BATCH_SIZE);
      const batchResults = await this.processBatch(batch);
      results.push(...batchResults);
      
      // 释放批次内存
      batch.length = 0;
      
      // 每处理1000条记录后强制垃圾回收
      if (i % 1000 === 0 && global.gc) {
        global.gc();
      }
    }
    
    return results;
  }
  
  // 3. 内存监控
  monitorMemoryUsage() {
    const usage = process.memoryUsage();
    const maxMemory = parseInt(process.env.AWS_LAMBDA_FUNCTION_MEMORY_SIZE) * 1024 * 1024;
    const usagePercent = (usage.heapUsed / maxMemory) * 100;
    
    if (usagePercent > 80) {
      console.warn(`High memory usage: ${usagePercent.toFixed(2)}%`);
      
      // 触发垃圾回收
      if (global.gc) {
        global.gc();
      }
    }
    
    return { usage, usagePercent, maxMemory };
  }
}
```

### 2.2 网络成本优化

#### CDN和缓存策略
```javascript
// CDN配置优化
const CDN_OPTIMIZATION_CONFIG = {
  // 静态资源CDN配置
  static_resources: {
    path: '/static/*',
    cacheControl: 'public, max-age=31536000', // 1年
    compress: true,
    minify: true,
    
    // 智能压缩配置
    compression: {
      gzip: true,
      brotli: true,
      level: 6 // 平衡压缩率和CPU成本
    }
  },
  
  // API响应CDN配置
  api_responses: {
    path: '/v1/questions/*',
    cacheControl: 'public, max-age=300', // 5分钟
    varyHeaders: ['Authorization', 'Accept-Language'],
    
    // 仅缓存GET请求
    cacheableHttpMethods: ['GET', 'HEAD']
  },
  
  // 音频文件CDN配置
  audio_files: {
    path: '/audio/*',
    cacheControl: 'public, max-age=86400', // 24小时
    compress: false, // 音频文件已压缩
    
    # 地域分布优化
    regions: ['ap-beijing', 'ap-shanghai', 'ap-guangzhou']
  }
};

// CDN成本计算器
class CDNCostCalculator {
  constructor() {
    this.pricing = {
      china: 0.18,     // $0.18/GB 中国大陆
      asia: 0.12,      // $0.12/GB 亚太地区
      global: 0.085    // $0.085/GB 全球
    };
  }
  
  calculateMonthlyCost(trafficGB, region = 'china') {
    const basePrice = this.pricing[region] * trafficGB;
    
    // 批量折扣
    let discount = 0;
    if (trafficGB > 1000) discount = 0.2;      // >1TB: 20%折扣
    else if (trafficGB > 500) discount = 0.15; // >500GB: 15%折扣
    else if (trafficGB > 100) discount = 0.1;  // >100GB: 10%折扣
    
    return {
      basePrice,
      discount: basePrice * discount,
      finalPrice: basePrice * (1 - discount),
      savings: basePrice * discount
    };
  }
}

// 边缘缓存实现
class EdgeCache {
  constructor() {
    this.cache = new Map();
    this.hitCount = 0;
    this.missCount = 0;
  }
  
  // 智能缓存策略
  shouldCache(request) {
    const { method, path, headers } = request;
    
    // 只缓存GET请求
    if (method !== 'GET') return false;
    
    // 不缓存个人化内容
    if (path.includes('/users/') && path.includes('/profile')) return false;
    
    // 缓存题目数据
    if (path.startsWith('/v1/questions')) return true;
    
    // 缓存排行榜（短时间)
    if (path.startsWith('/v1/leaderboards')) return true;
    
    return false;
  }
  
  generateCacheKey(request) {
    const { path, queryString, headers } = request;
    const relevantHeaders = ['Accept-Language', 'X-Game-Version'];
    
    const headerString = relevantHeaders
      .map(h => `${h}:${headers[h] || ''}`)
      .join('|');
    
    return `${path}?${queryString || ''}#${headerString}`;
  }
  
  getCacheStats() {
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;
    
    return {
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: hitRate.toFixed(2) + '%',
      cacheSize: this.cache.size
    };
  }
}
```

#### API网关成本优化
```javascript
// API网关成本优化策略
class APIGatewayOptimizer {
  constructor() {
    this.batchProcessor = new BatchProcessor();
    this.compressionHandler = new CompressionHandler();
  }
  
  // 请求批处理
  async batchAPIRequests(requests) {
    const BATCH_SIZE = 10;
    const batches = [];
    
    for (let i = 0; i < requests.length; i += BATCH_SIZE) {
      batches.push(requests.slice(i, i + BATCH_SIZE));
    }
    
    const results = await Promise.all(
      batches.map(batch => this.processBatch(batch))
    );
    
    return results.flat();
  }
  
  // 响应压缩
  compressResponse(response, acceptEncoding) {
    if (!acceptEncoding) return response;
    
    const compressionType = this.selectCompressionType(acceptEncoding);
    if (!compressionType) return response;
    
    const compressed = this.compressionHandler.compress(
      JSON.stringify(response), 
      compressionType
    );
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Encoding': compressionType,
        'Content-Length': compressed.length
      },
      body: compressed,
      isBase64Encoded: true
    };
  }
  
  // 选择最优压缩算法
  selectCompressionType(acceptEncoding) {
    if (acceptEncoding.includes('br')) return 'br';      // Brotli: 最高压缩率
    if (acceptEncoding.includes('gzip')) return 'gzip';  // Gzip: 通用支持
    if (acceptEncoding.includes('deflate')) return 'deflate'; // Deflate: 备选
    return null;
  }
  
  // 请求合并优化
  async optimizeRequestMerging(userId, requestTypes) {
    // 将多个请求合并为单个请求
    const mergedRequest = {
      userId,
      requests: requestTypes,
      timestamp: Date.now()
    };
    
    const response = await this.processMergedRequest(mergedRequest);
    
    // 拆分响应给各个请求方
    return this.splitMergedResponse(response, requestTypes);
  }
}
```

### 2.3 数据库成本优化

#### TencentDB Serverless优化
```javascript
// 数据库连接优化
class ServerlessDBOptimizer {
  constructor() {
    this.connectionPool = null;
    this.queryCache = new Map();
    this.preparedStatements = new Map();
  }
  
  // 连接池配置优化
  createOptimizedPool() {
    return mysql.createPool({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
      
      // 连接池优化配置
      connectionLimit: 3,          // 减少连接数降低成本
      acquireTimeout: 20000,       // 20秒获取超时
      timeout: 15000,              // 15秒查询超时
      idleTimeout: 180000,         // 3分钟空闲超时
      
      // Serverless优化
      reconnect: true,
      reconnectDelay: 2000,
      removeNodeErrorCount: 5,
      restoreNodeTimeout: 30000,
      
      // 查询优化
      supportBigNumbers: true,
      bigNumberStrings: false,      // 节省内存
      dateStrings: false,           // 节省转换成本
      debug: false,                 // 生产环境关闭调试
      
      // SSL配置
      ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false
      } : false
    });
  }
  
  // 查询优化
  async optimizedQuery(sql, params = []) {
    const queryKey = this.generateQueryKey(sql, params);
    
    // 1. 检查查询缓存
    if (this.queryCache.has(queryKey)) {
      const cached = this.queryCache.get(queryKey);
      if (Date.now() - cached.timestamp < 30000) { // 30秒缓存
        return cached.result;
      }
    }
    
    // 2. 使用预编译语句
    let statement = this.preparedStatements.get(sql);
    if (!statement) {
      statement = await this.connectionPool.prepare(sql);
      this.preparedStatements.set(sql, statement);
    }
    
    // 3. 执行查询
    const [rows] = await statement.execute(params);
    
    // 4. 缓存结果（仅读查询）
    if (sql.trim().toUpperCase().startsWith('SELECT')) {
      this.queryCache.set(queryKey, {
        result: rows,
        timestamp: Date.now()
      });
      
      // 限制缓存大小
      if (this.queryCache.size > 100) {
        const firstKey = this.queryCache.keys().next().value;
        this.queryCache.delete(firstKey);
      }
    }
    
    return rows;
  }
  
  // 批量查询优化
  async batchQuery(queries) {
    const connection = await this.connectionPool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const results = [];
      for (const { sql, params } of queries) {
        const [rows] = await connection.execute(sql, params);
        results.push(rows);
      }
      
      await connection.commit();
      return results;
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  generateQueryKey(sql, params) {
    return `${sql}:${JSON.stringify(params)}`;
  }
}

// 数据分层存储策略
class DataTieringStrategy {
  constructor() {
    this.tiers = {
      hot: {
        retention: 7,        // 7天热数据
        storage: 'redis',
        cost: 0.05           // $/GB/day
      },
      warm: {
        retention: 30,       // 30天温数据
        storage: 'mysql',
        cost: 0.024          // $/GB/month
      },
      cold: {
        retention: 365,      // 1年冷数据
        storage: 'cos',
        cost: 0.018          // $/GB/month
      }
    };
  }
  
  // 数据分层迁移
  async migrateDataTiers() {
    const now = new Date();
    
    // 迁移到温存储 (7天前的数据)
    const warmCutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    await this.migrateToWarmStorage(warmCutoff);
    
    // 迁移到冷存储 (30天前的数据)
    const coldCutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    await this.migrateToColdStorage(coldCutoff);
    
    // 删除过期数据 (1年前的数据)
    const deleteCutoff = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
    await this.deleteExpiredData(deleteCutoff);
  }
  
  // 成本计算
  calculateStorageCost(dataSize) {
    const hotData = Math.min(dataSize, 10); // 假设10GB热数据
    const warmData = Math.min(dataSize - hotData, 50); // 假设50GB温数据
    const coldData = Math.max(0, dataSize - hotData - warmData);
    
    const costs = {
      hot: hotData * this.tiers.hot.cost * 30,    // 月成本
      warm: warmData * this.tiers.warm.cost,
      cold: coldData * this.tiers.cold.cost,
    };
    
    costs.total = costs.hot + costs.warm + costs.cold;
    
    return costs;
  }
}
```

### 2.4 监控成本优化

#### 日志和监控成本控制
```javascript
// 智能日志管理
class CostOptimizedLogger {
  constructor() {
    this.logLevels = {
      ERROR: 1,
      WARN: 2,
      INFO: 3,
      DEBUG: 4
    };
    
    this.currentLevel = process.env.LOG_LEVEL || 'INFO';
    this.batchSize = 50;
    this.logBuffer = [];
    this.samplingRates = {
      ERROR: 1.0,    // 100% 错误日志
      WARN: 1.0,     // 100% 警告日志
      INFO: 0.1,     // 10% 信息日志
      DEBUG: 0.01    // 1% 调试日志
    };
  }
  
  // 采样日志记录
  log(level, message, extra = {}) {
    if (!this.shouldLog(level)) return;
    
    // 采样控制
    if (Math.random() > this.samplingRates[level]) return;
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      requestId: extra.requestId,
      userId: extra.userId,
      function: process.env.AWS_LAMBDA_FUNCTION_NAME,
      memory: process.memoryUsage().heapUsed
    };
    
    // 批量写入
    this.logBuffer.push(logEntry);
    if (this.logBuffer.length >= this.batchSize) {
      this.flush();
    }
  }
  
  // 结构化日志压缩
  compressLogEntry(entry) {
    // 移除重复字段
    const compressed = {
      t: entry.timestamp,
      l: entry.level.charAt(0), // E/W/I/D
      m: entry.message,
      r: entry.requestId?.slice(-8), // 只保留后8位
      u: entry.userId,
      f: entry.function?.slice(-10), // 只保留后10位
      mem: Math.round(entry.memory / 1024 / 1024) // MB
    };
    
    return compressed;
  }
  
  // 批量写入
  async flush() {
    if (this.logBuffer.length === 0) return;
    
    const compressedLogs = this.logBuffer.map(entry => 
      this.compressLogEntry(entry)
    );
    
    // 压缩并写入
    const compressed = JSON.stringify(compressedLogs);
    console.log(compressed);
    
    this.logBuffer = [];
  }
  
  shouldLog(level) {
    return this.logLevels[level] <= this.logLevels[this.currentLevel];
  }
}

// 监控指标优化
class OptimizedMetrics {
  constructor() {
    this.metrics = new Map();
    this.flushInterval = 60000; // 1分钟批量上报
    this.metricBuffer = [];
  }
  
  // 聚合指标收集
  increment(metricName, value = 1, tags = {}) {
    const key = this.generateMetricKey(metricName, tags);
    const current = this.metrics.get(key) || 0;
    this.metrics.set(key, current + value);
  }
  
  gauge(metricName, value, tags = {}) {
    const key = this.generateMetricKey(metricName, tags);
    this.metrics.set(key, value);
  }
  
  // 批量上报指标
  async flushMetrics() {
    if (this.metrics.size === 0) return;
    
    const batch = [];
    for (const [key, value] of this.metrics.entries()) {
      const [name, tags] = this.parseMetricKey(key);
      batch.push({ name, value, tags, timestamp: Date.now() });
    }
    
    // 压缩上报
    const compressed = this.compressMetrics(batch);
    await this.uploadMetrics(compressed);
    
    this.metrics.clear();
  }
  
  // 指标压缩
  compressMetrics(metrics) {
    // 按指标名称分组
    const grouped = {};
    for (const metric of metrics) {
      if (!grouped[metric.name]) {
        grouped[metric.name] = [];
      }
      grouped[metric.name].push({
        v: metric.value,
        t: metric.tags,
        ts: metric.timestamp
      });
    }
    
    return grouped;
  }
  
  generateMetricKey(name, tags) {
    const tagString = Object.entries(tags)
      .sort()
      .map(([k, v]) => `${k}:${v}`)
      .join(',');
    return `${name}#${tagString}`;
  }
}
```

## 3. 成本监控和报警

### 3.1 实时成本监控
```javascript
// 成本追踪器
class CostTracker {
  constructor() {
    this.dailyBudget = 10;  // $10/day
    this.monthlyBudget = 276; // $276/month
    this.costBreakdown = {};
    this.redis = getRedisCluster();
  }
  
  // 记录函数执行成本
  async recordFunctionCost(functionName, duration, memorySize) {
    const cost = this.calculateFunctionCost(duration, memorySize);
    const today = new Date().toISOString().split('T')[0];
    const key = `cost:${today}:${functionName}`;
    
    await this.redis.incrbyfloat(key, cost);
    await this.redis.expire(key, 86400 * 7); // 保存7天
    
    // 检查预算告警
    await this.checkBudgetAlert(today);
  }
  
  // 计算函数执行成本
  calculateFunctionCost(duration, memorySize) {
    const gbSeconds = (memorySize / 1024) * (duration / 1000);
    const executionCost = gbSeconds * 0.0000167; // SCF定价
    const requestCost = 0.000000133; // 请求定价
    
    return executionCost + requestCost;
  }
  
  // 获取每日成本报告
  async getDailyCostReport(date) {
    const pattern = `cost:${date}:*`;
    const keys = await this.redis.keys(pattern);
    
    const costs = {};
    let totalCost = 0;
    
    for (const key of keys) {
      const functionName = key.split(':')[2];
      const cost = parseFloat(await this.redis.get(key)) || 0;
      costs[functionName] = cost;
      totalCost += cost;
    }
    
    return {
      date,
      totalCost: totalCost.toFixed(6),
      breakdown: costs,
      budgetUsage: ((totalCost / this.dailyBudget) * 100).toFixed(2) + '%'
    };
  }
  
  // 预算告警检查
  async checkBudgetAlert(date) {
    const report = await this.getDailyCostReport(date);
    const usagePercent = parseFloat(report.budgetUsage);
    
    if (usagePercent > 80) {
      await this.sendBudgetAlert({
        level: usagePercent > 95 ? 'CRITICAL' : 'WARNING',
        date,
        usage: report.budgetUsage,
        totalCost: report.totalCost,
        breakdown: report.breakdown
      });
    }
  }
  
  // 发送预算告警
  async sendBudgetAlert(alert) {
    const message = {
      title: `成本预算告警 - ${alert.level}`,
      content: `
        日期: ${alert.date}
        预算使用率: ${alert.usage}
        总成本: $${alert.totalCost}
        
        成本分解:
        ${Object.entries(alert.breakdown)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5)
          .map(([func, cost]) => `- ${func}: $${cost.toFixed(6)}`)
          .join('\n')}
      `,
      timestamp: new Date().toISOString()
    };
    
    // 发送到企业微信/钉钉等
    await this.sendAlert(message);
  }
}

// 成本优化建议生成器
class CostOptimizationAdvisor {
  constructor() {
    this.costTracker = new CostTracker();
  }
  
  // 生成优化建议
  async generateOptimizationAdvice() {
    const weeklyReport = await this.getWeeklyCostAnalysis();
    const suggestions = [];
    
    // 分析高成本函数
    const topCostFunctions = Object.entries(weeklyReport.functionCosts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
    
    for (const [functionName, cost] of topCostFunctions) {
      const analysis = await this.analyzeFunctionCost(functionName, cost);
      suggestions.push(...analysis.suggestions);
    }
    
    // 分析成本趋势
    const trendAnalysis = this.analyzeCostTrend(weeklyReport.dailyCosts);
    suggestions.push(...trendAnalysis.suggestions);
    
    // 生成优化计划
    const optimizationPlan = this.generateOptimizationPlan(suggestions);
    
    return {
      currentWeeklyCost: weeklyReport.totalCost,
      projectedMonthlyCost: weeklyReport.totalCost * 4.33,
      topCostFunctions,
      suggestions,
      optimizationPlan,
      potentialSavings: optimizationPlan.totalSavings
    };
  }
  
  // 分析单个函数成本
  async analyzeFunctionCost(functionName, weeklyCost) {
    const suggestions = [];
    const avgDailyCost = weeklyCost / 7;
    
    // 高成本函数优化建议
    if (avgDailyCost > 2) { // $2/day
      suggestions.push({
        type: 'HIGH_COST_FUNCTION',
        function: functionName,
        issue: '函数日均成本过高',
        recommendation: '考虑优化内存配置或执行时间',
        potentialSaving: avgDailyCost * 0.3 * 30, // 30%节省
        priority: 'HIGH'
      });
    }
    
    // 内存配置优化
    const memoryAnalysis = await this.analyzeMemoryUsage(functionName);
    if (memoryAnalysis.overProvisioned) {
      suggestions.push({
        type: 'MEMORY_OPTIMIZATION',
        function: functionName,
        issue: '内存配置过高',
        recommendation: `建议从${memoryAnalysis.current}MB降至${memoryAnalysis.recommended}MB`,
        potentialSaving: memoryAnalysis.savings * 30,
        priority: 'MEDIUM'
      });
    }
    
    return { suggestions };
  }
  
  // 生成优化计划
  generateOptimizationPlan(suggestions) {
    const plan = {
      immediate: [],  // 立即执行
      shortTerm: [],  // 1周内
      longTerm: [],   // 1月内
      totalSavings: 0
    };
    
    for (const suggestion of suggestions) {
      const timeframe = this.getOptimizationTimeframe(suggestion);
      plan[timeframe].push(suggestion);
      plan.totalSavings += suggestion.potentialSaving || 0;
    }
    
    return plan;
  }
  
  getOptimizationTimeframe(suggestion) {
    if (suggestion.priority === 'HIGH') return 'immediate';
    if (suggestion.type === 'MEMORY_OPTIMIZATION') return 'shortTerm';
    return 'longTerm';
  }
}
```

## 4. 成本优化清单

### 4.1 立即执行优化（0-1天）
```markdown
## 高优先级优化项
- [ ] 启用函数预热机制，减少冷启动成本
- [ ] 配置API网关缓存，减少后端调用
- [ ] 启用CDN压缩，减少流量成本
- [ ] 调整日志级别，减少日志存储成本
- [ ] 启用Redis缓存，减少数据库查询

## 配置调整
- [ ] 函数内存配置优化（256MB为最佳性价比点）
- [ ] 数据库连接池大小调整（3-5个连接）
- [ ] 缓存TTL策略优化（热点数据30秒-5分钟）
```

### 4.2 短期优化（1-7天）
```markdown
## 代码优化
- [ ] 实施连接池复用策略
- [ ] 优化查询语句，减少数据库执行时间
- [ ] 实施批量处理，减少函数调用次数
- [ ] 启用响应压缩，减少网络传输

## 架构调整
- [ ] 实施边缘缓存策略
- [ ] 配置数据分层存储
- [ ] 优化静态资源CDN配置
- [ ] 实施限流机制，防止异常调用
```

### 4.3 长期优化（1-4周）
```markdown
## 深度优化
- [ ] 实施数据库读写分离
- [ ] 配置自动扩缩容策略
- [ ] 实施智能预热调度
- [ ] 建立成本监控和告警体系

## 成本管控
- [ ] 建立成本预算控制机制
- [ ] 实施资源使用率监控
- [ ] 定期成本优化review
- [ ] 建立成本优化流程
```

### 4.4 预期成本节省效果

| 优化项目 | 当前成本 | 优化后成本 | 节省金额 | 节省比例 |
|---------|----------|------------|----------|----------|
| 函数执行优化 | $83.2 | $65.0 | $18.2 | 22% |
| 网络成本优化 | $240.0 | $96.0 | $144.0 | 60% |
| 存储成本优化 | $112.4 | $100.0 | $12.4 | 11% |
| 监控成本优化 | $18.0 | $14.0 | $4.0 | 22% |
| **总计** | **$276.0** | **$220.0** | **$56.0** | **20%** |

通过这些优化策略，预计可以将月度成本从$276降低至$220，节省20%的成本，同时保持系统性能和用户体验。

关键成功因素：
1. **持续监控**: 实时跟踪成本变化
2. **数据驱动**: 基于实际使用数据优化
3. **渐进式优化**: 分阶段实施，降低风险
4. **性能平衡**: 在成本和性能之间找到最佳平衡点