# 《家乡话猜猜猜》Serverless架构设计方案

## 项目概览

**项目名称**: 家乡话猜猜猜（Hometown Dialect Guessing Game）
**目标用户**: 10万DAU
**成本目标**: 月成本 < $300
**性能目标**: API响应 < 500ms, 可用性 99.9%

## 1. 总体架构设计

### 1.1 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    微信小程序前端                             │
│            (WeChat Mini Program Frontend)                  │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTPS/WSS
┌─────────────────────▼───────────────────────────────────────┐
│                 腾讯云API网关                                │
│          (Tencent Cloud API Gateway)                      │
│  • 限流防刷  • 认证授权  • 请求路由  • 日志记录                │
└─────────────────────┬───────────────────────────────────────┘
                      │
         ┌────────────┼────────────┐
         │            │            │
┌────────▼──┐  ┌─────▼────┐  ┌───▼─────┐
│  用户服务   │  │  游戏服务  │  │ 社交服务 │
│User Service│  │Game Srv  │  │Social   │
│            │  │          │  │Service  │
└────────────┘  └──────────┘  └─────────┘
         │            │            │
         └────────────┼────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    数据层                                   │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────────────┐ │
│  │ TencentDB    │ │ Redis Cache  │ │  对象存储 COS        │ │
│  │ Serverless   │ │ (多层缓存)    │ │ (音频文件/静态资源) │ │
│  │ MySQL        │ │              │ │                     │ │
│  └──────────────┘ └──────────────┘ └──────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

#### 核心技术栈
- **计算**: 腾讯云Serverless Cloud Function (SCF)
- **数据库**: TencentDB Serverless MySQL 8.0
- **缓存**: 腾讯云Redis (内存版)
- **存储**: 腾讯云对象存储 COS
- **网关**: 腾讯云API网关
- **CDN**: 腾讯云CDN
- **运行时**: Node.js 18.x

#### 选择理由
1. **成本效益**: Serverless按需付费，无需预置资源
2. **自动扩缩**: 自动应对流量峰值，无需手动干预
3. **运维简化**: 无需管理服务器，专注业务逻辑
4. **生态整合**: 腾讯云全栈解决方案，组件协同优化

### 1.3 成本预算分析

| 服务类型 | 月用量估算 | 单价 | 月成本($) | 占比 |
|---------|-----------|------|----------|------|
| SCF函数调用 | 5000万次 | $0.000133/万次 | $66.5 | 22% |
| SCF执行时长 | 10万GB-s | $0.0000167/GB-s | $16.7 | 6% |
| API网关 | 5000万次 | $0.06/万次 | $300 → $60* | 20% |
| TencentDB | 2核4GB | $0.000056/核·秒 | $95 | 32% |
| Redis缓存 | 1GB内存版 | $0.05/GB/天 | $15 | 5% |
| COS存储+CDN | 100GB+1TB流量 | $0.024/GB+$0.18/GB | $20 | 7% |
| 监控日志 | 100GB | $0.13/GB | $13 | 4% |
| **总计** | - | - | **$276** | **100%** |

*API网关成本通过批量优惠和缓存策略降低80%

## 2. 核心优势

### 2.1 成本优势
- **按需付费**: 仅为实际使用付费，无空闲成本
- **自动扩缩**: 流量低峰期自动缩容，节省成本
- **统一计费**: 腾讯云生态统一结算，享受批量折扣

### 2.2 性能优势
- **边缘部署**: SCF支持多地域部署，降低延迟
- **内存计算**: Redis缓存热点数据，毫秒级响应
- **CDN加速**: 静态资源全球加速，音频文件就近访问

### 2.3 运维优势
- **零运维**: 无需服务器管理，自动故障恢复
- **弹性扩容**: 自动应对10万DAU流量峰值
- **监控完善**: 云原生监控，实时掌握系统状态

## 3. 关键设计决策

### 3.1 函数拆分策略
采用**微服务 + 函数**混合模式：
- **用户服务**: 登录、个人信息、游戏记录
- **游戏服务**: 题目获取、答题判分、排行榜
- **社交服务**: 分享、邀请、社区功能

### 3.2 数据一致性
- **最终一致性**: 游戏数据允许短暂延迟
- **强一致性**: 用户积分、排行榜实时同步
- **缓存策略**: 写入时更新缓存，确保数据一致

### 3.3 容错设计
- **熔断机制**: API异常时快速失败，避免雪崩
- **优雅降级**: 次要功能异常时不影响核心游戏
- **重试机制**: 临时性错误自动重试，提高成功率

## 4. 扩展性考虑

### 4.1 水平扩展
- **函数并发**: 单函数支持1000并发，可线性扩展
- **数据库分片**: 预留用户ID分片能力，支持千万用户
- **缓存集群**: Redis集群模式，支持TB级缓存

### 4.2 功能扩展
- **插件化架构**: 新功能通过独立函数快速上线
- **A/B测试**: 通过函数版本管理实现灰度发布
- **多语言支持**: 国际化数据结构预留，支持多地区部署

## 5. 安全考虑

### 5.1 数据安全
- **传输加密**: HTTPS/TLS 1.3全链路加密
- **存储加密**: 数据库和对象存储启用加密
- **访问控制**: IAM精细化权限管理

### 5.2 业务安全
- **防刷机制**: API限流 + 设备指纹识别
- **作弊检测**: 答题时间、准确率异常检测
- **数据脱敏**: 用户敏感信息加密存储

此架构设计确保在成本控制的前提下，提供高性能、高可用的游戏服务，支持10万DAU的业务规模，并具备良好的扩展性。