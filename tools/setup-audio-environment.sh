#!/bin/bash

# 音频制作环境设置脚本
# 自动安装所需依赖和工具

echo "🎵 设置音频制作环境..."
echo "================================"

# 检测操作系统
OS=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    OS="mac"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

echo "🖥️ 检测到操作系统: $OS"

# 检查Python环境
echo "🐍 检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✅ Python已安装: $PYTHON_VERSION"
else
    echo "❌ 未找到Python3，请先安装Python"
    exit 1
fi

# 安装FFmpeg
echo "🔧 检查FFmpeg..."
if command -v ffmpeg &> /dev/null; then
    FFMPEG_VERSION=$(ffmpeg -version | head -n1)
    echo "✅ FFmpeg已安装: ${FFMPEG_VERSION:0:50}..."
else
    echo "📦 正在安装FFmpeg..."
    
    if [[ "$OS" == "mac" ]]; then
        # Mac使用Homebrew
        if command -v brew &> /dev/null; then
            brew install ffmpeg
        else
            echo "❌ 未找到Homebrew，请先安装: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
    elif [[ "$OS" == "linux" ]]; then
        # Linux使用apt
        sudo apt update && sudo apt install -y ffmpeg
    fi
    
    # 再次检查
    if command -v ffmpeg &> /dev/null; then
        echo "✅ FFmpeg安装成功"
    else
        echo "❌ FFmpeg安装失败"
        exit 1
    fi
fi

# 创建必要目录
echo "📁 创建目录结构..."
PROJECT_ROOT="/Users/<USER>/Public/GitHub/hometown-dialect-game"
mkdir -p "$PROJECT_ROOT/content/audio/raw"
mkdir -p "$PROJECT_ROOT/content/audio/backup"
mkdir -p "$PROJECT_ROOT/content/production"
echo "✅ 目录结构创建完成"

# 测试处理工具
echo "🧪 测试音频处理工具..."
cd "$PROJECT_ROOT/tools/audio-processing/"

echo "测试批量转换工具..."
python3 batch-converter.py --help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 批量转换工具正常"
else
    echo "❌ 批量转换工具测试失败"
fi

echo "测试质量检查工具..."
python3 quality-checker.py --help > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 质量检查工具正常"
else
    echo "❌ 质量检查工具测试失败"
fi

# 创建示例录制配置
echo "📝 创建录制配置..."
cat > "$PROJECT_ROOT/content/scripts/quick-start.md" << 'EOF'
# 快速开始录制

## 1. 使用手机录制
1. 打开手机录音应用
2. 设置为最高质量 (如果可选)
3. 在安静环境中录制
4. 每个词汇录制2-3遍

## 2. 文件传输
将录制的音频文件传输到电脑:
```
/content/audio/raw/
```

## 3. 批量处理
```bash
cd tools/audio-processing/
python3 batch-converter.py
python3 quality-checker.py
```

## 4. 检查结果
查看质量报告:
```
/content/production/audio-quality-report.json
```
EOF

echo "✅ 录制配置创建完成"

# 环境验证
echo ""
echo "🎯 环境验证结果:"
echo "================================"
echo "✅ Python: $(python3 --version)"
echo "✅ FFmpeg: 已安装"
echo "✅ 目录结构: 已创建"
echo "✅ 处理工具: 已准备"
echo ""
echo "🚀 环境设置完成！"
echo "现在可以开始录制音频文件了。"
echo ""
echo "下一步:"
echo "1. 使用录音设备录制WAV文件"
echo "2. 将文件放到 content/audio/raw/ 目录"
echo "3. 运行处理工具进行转换和质量检查"