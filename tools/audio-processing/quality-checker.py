#!/usr/bin/env python3
"""
音频质量检查工具
验证音频文件是否符合游戏技术规格要求
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime
import statistics

# 质量标准配置
QUALITY_STANDARDS = {
    'format': 'mp3',
    'min_duration': 2.0,    # 最短2秒
    'max_duration': 5.0,    # 最长5秒
    'sample_rate': 44100,   # 采样率
    'channels': 1,          # 单声道
    'min_bitrate': 120,     # 最低比特率 kbps
    'max_bitrate': 140,     # 最高比特率 kbps
    'max_file_size': 100 * 1024,  # 最大文件大小 100KB
    'noise_floor_threshold': -60,  # 噪音底 dB
}

class AudioQualityChecker:
    def __init__(self):
        self.checked_files = []
        self.passed_files = []
        self.failed_files = []
        
    def check_dependencies(self):
        """检查依赖程序"""
        tools = ['ffprobe', 'ffmpeg']
        missing = []
        
        for tool in tools:
            try:
                subprocess.run([tool, '-version'], 
                             capture_output=True, check=True)
                print(f"✅ {tool} 已安装")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing.append(tool)
                print(f"❌ {tool} 未找到")
        
        if missing:
            print(f"请安装缺失工具: {', '.join(missing)}")
            return False
        return True
    
    def get_audio_info(self, file_path):
        """获取音频文件详细信息"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                str(file_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return json.loads(result.stdout)
        except Exception as e:
            print(f"❌ 获取音频信息失败: {file_path.name} - {e}")
        return None
    
    def analyze_audio_levels(self, file_path):
        """分析音频电平"""
        try:
            cmd = [
                'ffmpeg',
                '-i', str(file_path),
                '-af', 'volumedetect',
                '-f', 'null',
                '-'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 解析音量信息
            lines = result.stderr.split('\n')
            volume_info = {}
            
            for line in lines:
                if 'mean_volume:' in line:
                    volume_info['mean_volume'] = float(line.split(':')[1].strip().replace(' dB', ''))
                elif 'max_volume:' in line:
                    volume_info['max_volume'] = float(line.split(':')[1].strip().replace(' dB', ''))
                elif 'histogram' in line and 'dB' in line:
                    # 简单的噪音底估算
                    volume_info['noise_floor'] = volume_info.get('mean_volume', -20) - 20
            
            return volume_info
            
        except Exception as e:
            print(f"❌ 音频电平分析失败: {file_path.name} - {e}")
        return {}
    
    def check_file_quality(self, file_path):
        """检查单个文件质量"""
        print(f"🔍 检查文件: {file_path.name}")
        
        # 基础信息
        file_size = file_path.stat().st_size
        audio_info = self.get_audio_info(file_path)
        
        if not audio_info:
            return False
        
        # 提取关键信息
        format_info = audio_info.get('format', {})
        stream_info = audio_info.get('streams', [{}])[0]
        
        duration = float(format_info.get('duration', 0))
        bitrate = int(format_info.get('bit_rate', 0)) // 1000  # 转换为 kbps
        sample_rate = int(stream_info.get('sample_rate', 0))
        channels = int(stream_info.get('channels', 0))
        
        # 音频电平分析
        volume_info = self.analyze_audio_levels(file_path)
        
        # 质量检查结果
        checks = {
            'file_path': str(file_path),
            'file_size_bytes': file_size,
            'file_size_kb': file_size / 1024,
            'duration': duration,
            'bitrate_kbps': bitrate,
            'sample_rate': sample_rate,
            'channels': channels,
            'volume_info': volume_info,
            'checks': {}
        }
        
        # 执行各项检查
        passed = True
        
        # 1. 文件格式检查
        format_name = format_info.get('format_name', '').lower()
        format_check = QUALITY_STANDARDS['format'] in format_name
        checks['checks']['format'] = {
            'passed': format_check,
            'expected': QUALITY_STANDARDS['format'],
            'actual': format_name,
            'message': '✅ 格式正确' if format_check else f"❌ 格式错误: 期望 {QUALITY_STANDARDS['format']}, 实际 {format_name}"
        }
        passed = passed and format_check
        
        # 2. 时长检查
        duration_check = QUALITY_STANDARDS['min_duration'] <= duration <= QUALITY_STANDARDS['max_duration']
        checks['checks']['duration'] = {
            'passed': duration_check,
            'expected': f"{QUALITY_STANDARDS['min_duration']}-{QUALITY_STANDARDS['max_duration']}s",
            'actual': f"{duration:.1f}s",
            'message': '✅ 时长合适' if duration_check else f"❌ 时长不符: {duration:.1f}s (应在 {QUALITY_STANDARDS['min_duration']}-{QUALITY_STANDARDS['max_duration']}s)"
        }
        passed = passed and duration_check
        
        # 3. 采样率检查
        sample_rate_check = sample_rate == QUALITY_STANDARDS['sample_rate']
        checks['checks']['sample_rate'] = {
            'passed': sample_rate_check,
            'expected': QUALITY_STANDARDS['sample_rate'],
            'actual': sample_rate,
            'message': '✅ 采样率正确' if sample_rate_check else f"❌ 采样率错误: {sample_rate} (应为 {QUALITY_STANDARDS['sample_rate']})"
        }
        passed = passed and sample_rate_check
        
        # 4. 声道数检查
        channels_check = channels == QUALITY_STANDARDS['channels']
        checks['checks']['channels'] = {
            'passed': channels_check,
            'expected': QUALITY_STANDARDS['channels'],
            'actual': channels,
            'message': '✅ 声道正确' if channels_check else f"❌ 声道错误: {channels} (应为 {QUALITY_STANDARDS['channels']})"
        }
        passed = passed and channels_check
        
        # 5. 比特率检查
        bitrate_check = QUALITY_STANDARDS['min_bitrate'] <= bitrate <= QUALITY_STANDARDS['max_bitrate']
        checks['checks']['bitrate'] = {
            'passed': bitrate_check,
            'expected': f"{QUALITY_STANDARDS['min_bitrate']}-{QUALITY_STANDARDS['max_bitrate']} kbps",
            'actual': f"{bitrate} kbps",
            'message': '✅ 比特率合适' if bitrate_check else f"❌ 比特率不符: {bitrate} kbps (应在 {QUALITY_STANDARDS['min_bitrate']}-{QUALITY_STANDARDS['max_bitrate']} kbps)"
        }
        passed = passed and bitrate_check
        
        # 6. 文件大小检查
        size_check = file_size <= QUALITY_STANDARDS['max_file_size']
        checks['checks']['file_size'] = {
            'passed': size_check,
            'expected': f"<= {QUALITY_STANDARDS['max_file_size']/1024:.0f} KB",
            'actual': f"{file_size/1024:.1f} KB",
            'message': '✅ 文件大小合适' if size_check else f"❌ 文件太大: {file_size/1024:.1f} KB (应 <= {QUALITY_STANDARDS['max_file_size']/1024:.0f} KB)"
        }
        passed = passed and size_check
        
        # 7. 音量检查 (如果有音量信息)
        if volume_info:
            max_volume = volume_info.get('max_volume', 0)
            volume_check = max_volume <= -1  # 避免削波
            checks['checks']['volume'] = {
                'passed': volume_check,
                'expected': '<= -1 dB',
                'actual': f"{max_volume:.1f} dB",
                'message': '✅ 音量合适' if volume_check else f"⚠️ 音量可能过大: {max_volume:.1f} dB (建议 <= -1 dB)"
            }
            # 音量检查作为警告，不影响总体通过状态
        
        checks['overall_passed'] = passed
        self.checked_files.append(checks)
        
        if passed:
            self.passed_files.append(file_path)
            print(f"✅ 质量检查通过: {file_path.name}")
        else:
            self.failed_files.append(file_path)
            print(f"❌ 质量检查失败: {file_path.name}")
            
            # 显示失败的检查项
            for check_name, check_result in checks['checks'].items():
                if not check_result['passed']:
                    print(f"   {check_result['message']}")
        
        return passed
    
    def check_directory(self, directory):
        """批量检查目录中的音频文件"""
        directory_path = Path(directory)
        
        if not directory_path.exists():
            print(f"❌ 目录不存在: {directory}")
            return False
        
        # 查找音频文件
        audio_files = []
        audio_extensions = ['.mp3', '.wav', '.m4a', '.aac']
        
        for ext in audio_extensions:
            audio_files.extend(directory_path.rglob(f"*{ext}"))
        
        if not audio_files:
            print(f"❌ 在 {directory} 中未找到音频文件")
            return False
        
        print(f"📁 找到 {len(audio_files)} 个音频文件")
        print("-" * 50)
        
        # 检查每个文件
        for audio_file in audio_files:
            self.check_file_quality(audio_file)
            print()  # 空行分隔
        
        return True
    
    def generate_quality_report(self):
        """生成质量检查报告"""
        total_files = len(self.checked_files)
        passed_count = len(self.passed_files)
        failed_count = len(self.failed_files)
        pass_rate = passed_count / total_files * 100 if total_files > 0 else 0
        
        # 统计分析
        durations = [f['duration'] for f in self.checked_files]
        file_sizes = [f['file_size_kb'] for f in self.checked_files]
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_files': total_files,
                'passed_files': passed_count,
                'failed_files': failed_count,
                'pass_rate': f"{pass_rate:.1f}%"
            },
            'statistics': {
                'average_duration': f"{statistics.mean(durations):.1f}s" if durations else "N/A",
                'average_file_size': f"{statistics.mean(file_sizes):.1f} KB" if file_sizes else "N/A",
                'min_duration': f"{min(durations):.1f}s" if durations else "N/A",
                'max_duration': f"{max(durations):.1f}s" if durations else "N/A"
            },
            'quality_standards': QUALITY_STANDARDS,
            'detailed_results': self.checked_files
        }
        
        # 保存报告
        report_path = Path('../../content/production/audio-quality-report.json')
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("=" * 60)
        print("📊 音频质量检查报告")
        print("=" * 60)
        print(f"总文件数: {total_files}")
        print(f"通过检查: {passed_count}")
        print(f"未通过检查: {failed_count}")
        print(f"通过率: {pass_rate:.1f}%")
        
        if durations:
            print(f"平均时长: {statistics.mean(durations):.1f}s")
            print(f"平均文件大小: {statistics.mean(file_sizes):.1f} KB")
        
        print(f"详细报告已保存到: {report_path}")
        
        if self.failed_files:
            print(f"\n❌ 未通过检查的文件 ({failed_count}):")
            for failed_file in self.failed_files:
                print(f"  - {failed_file.name}")

def main():
    """主函数"""
    print("🔍 音频质量检查工具")
    print("-" * 30)
    
    checker = AudioQualityChecker()
    
    # 检查依赖
    if not checker.check_dependencies():
        sys.exit(1)
    
    # 处理命令行参数
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        # 使用默认目录
        directory = '../../content/audio/'
        print(f"使用默认目录: {directory}")
    
    # 开始检查
    print(f"\n🚀 开始质量检查...")
    success = checker.check_directory(directory)
    
    if success:
        checker.generate_quality_report()
        print("✅ 质量检查完成!")
        
        # 返回适当的退出码
        if checker.failed_files:
            print("⚠️ 有文件未通过质量检查")
            sys.exit(1)
    else:
        print("❌ 质量检查失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()