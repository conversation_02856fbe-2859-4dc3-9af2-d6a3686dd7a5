#!/usr/bin/env python3
"""
音频批处理转换工具
自动处理录制的WAV文件，转换为游戏所需的MP3格式
"""

import os
import sys
import subprocess
from pathlib import Path
import json
from datetime import datetime

# 配置参数
AUDIO_CONFIG = {
    'input_format': 'wav',
    'output_format': 'mp3',
    'bitrate': '128k',
    'channels': 1,  # 单声道
    'sample_rate': 44100,
    'volume_normalize': '-6dB'
}

# 目录配置
DIRECTORIES = {
    'input': '../../content/audio/raw/',
    'output': '../../content/audio/',
    'backup': '../../content/audio/backup/'
}

class AudioProcessor:
    def __init__(self):
        self.processed_files = []
        self.failed_files = []
        
    def check_dependencies(self):
        """检查依赖程序是否安装"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            print("✅ FFmpeg 已安装")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ 错误: 未找到 FFmpeg")
            print("请安装 FFmpeg: brew install ffmpeg (Mac) 或 apt install ffmpeg (Ubuntu)")
            return False
    
    def process_file(self, input_path, output_path):
        """处理单个音频文件"""
        try:
            # FFmpeg 命令构建
            cmd = [
                'ffmpeg',
                '-i', str(input_path),
                '-ab', AUDIO_CONFIG['bitrate'],
                '-ac', str(AUDIO_CONFIG['channels']),
                '-ar', str(AUDIO_CONFIG['sample_rate']),
                '-filter:a', f"volume={AUDIO_CONFIG['volume_normalize']}",
                '-y',  # 覆盖输出文件
                str(output_path)
            ]
            
            # 执行转换
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                file_size = os.path.getsize(output_path)
                duration = self.get_audio_duration(output_path)
                
                self.processed_files.append({
                    'input': str(input_path),
                    'output': str(output_path),
                    'size_bytes': file_size,
                    'duration_seconds': duration,
                    'timestamp': datetime.now().isoformat()
                })
                
                print(f"✅ 处理完成: {input_path.name} → {output_path.name}")
                print(f"   大小: {file_size/1024:.1f} KB, 时长: {duration:.1f}s")
                return True
            else:
                self.failed_files.append({
                    'file': str(input_path),
                    'error': result.stderr
                })
                print(f"❌ 处理失败: {input_path.name}")
                print(f"   错误: {result.stderr}")
                return False
                
        except Exception as e:
            self.failed_files.append({
                'file': str(input_path),
                'error': str(e)
            })
            print(f"❌ 异常错误: {input_path.name} - {e}")
            return False
    
    def get_audio_duration(self, file_path):
        """获取音频时长"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                str(file_path)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return float(data['format']['duration'])
        except:
            pass
        return 0.0
    
    def process_directory(self, input_dir, output_dir):
        """批量处理目录中的音频文件"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            print(f"❌ 输入目录不存在: {input_path}")
            return False
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有WAV文件
        wav_files = list(input_path.rglob(f"*.{AUDIO_CONFIG['input_format']}"))
        
        if not wav_files:
            print(f"❌ 在 {input_path} 中未找到 {AUDIO_CONFIG['input_format'].upper()} 文件")
            return False
        
        print(f"📁 找到 {len(wav_files)} 个文件待处理")
        
        # 处理每个文件
        for wav_file in wav_files:
            # 构建输出文件路径，保持目录结构
            relative_path = wav_file.relative_to(input_path)
            output_file = output_path / relative_path.with_suffix(f'.{AUDIO_CONFIG["output_format"]}')
            
            # 创建输出目录
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 处理文件
            self.process_file(wav_file, output_file)
        
        return True
    
    def generate_report(self):
        """生成处理报告"""
        total_files = len(self.processed_files) + len(self.failed_files)
        success_rate = len(self.processed_files) / total_files * 100 if total_files > 0 else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_files': total_files,
            'processed_successfully': len(self.processed_files),
            'failed_files': len(self.failed_files),
            'success_rate': f"{success_rate:.1f}%",
            'processed_files': self.processed_files,
            'failed_files': self.failed_files,
            'config': AUDIO_CONFIG
        }
        
        # 保存报告
        report_path = Path('../../content/production/audio-processing-report.json')
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n" + "="*50)
        print("📊 处理报告")
        print("="*50)
        print(f"总文件数: {total_files}")
        print(f"成功处理: {len(self.processed_files)}")
        print(f"处理失败: {len(self.failed_files)}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"报告已保存到: {report_path}")
        
        if self.failed_files:
            print("\n❌ 失败文件:")
            for failed in self.failed_files:
                print(f"  - {failed['file']}")

def main():
    """主函数"""
    print("🎵 音频批处理转换工具")
    print("-" * 30)
    
    processor = AudioProcessor()
    
    # 检查依赖
    if not processor.check_dependencies():
        sys.exit(1)
    
    # 处理命令行参数
    if len(sys.argv) > 2:
        input_dir = sys.argv[1]
        output_dir = sys.argv[2]
    else:
        # 使用默认目录
        input_dir = DIRECTORIES['input']
        output_dir = DIRECTORIES['output']
        
        print(f"使用默认目录:")
        print(f"  输入: {input_dir}")
        print(f"  输出: {output_dir}")
    
    # 开始处理
    print(f"\n🚀 开始批量处理...")
    success = processor.process_directory(input_dir, output_dir)
    
    if success:
        processor.generate_report()
        print("✅ 批量处理完成!")
    else:
        print("❌ 批量处理失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()