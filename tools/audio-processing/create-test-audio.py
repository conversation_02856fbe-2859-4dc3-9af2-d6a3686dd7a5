#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频文件生成器
为"家乡话猜猜猜"游戏创建模拟测试音频文件

功能：
1. 生成符合规格的测试音频文件（TTS或静音）
2. 批量创建所需的目录结构和文件
3. 验证音频格式和大小符合要求
4. 为真实录音制作提供框架
"""

import os
import sys
import json
import subprocess
from pathlib import Path
import argparse

# 音频配置
AUDIO_CONFIG = {
    "sample_rate": 44100,
    "bit_rate": "128k", 
    "channels": 1,
    "duration": 3.0,  # 秒
    "format": "mp3",
    "target_size_kb": 40
}

# 方言配置
DIALECTS = {
    "cantonese": "粤语",
    "sichuan": "四川话", 
    "shanghai": "上海话",
    "northeast": "东北话",
    "minnan": "闽南话"
}

# 分类和难度配置
CATEGORIES = {
    "daily": {"easy": 20, "medium": 10, "hard": 0},
    "local_terms": {"easy": 8, "medium": 15, "hard": 7},
    "numbers": {"easy": 10, "medium": 0, "hard": 0},
    "colors": {"easy": 0, "medium": 8, "hard": 0},
    "humor": {"easy": 0, "medium": 5, "hard": 10}
}

def ensure_directory(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)
    
def create_silent_audio(output_path, duration=3.0):
    """创建静音测试音频文件"""
    try:
        cmd = [
            "ffmpeg", "-y",
            "-f", "lavfi",
            "-i", f"anullsrc=channel_layout=mono:sample_rate={AUDIO_CONFIG['sample_rate']}",
            "-t", str(duration),
            "-b:a", AUDIO_CONFIG['bit_rate'],
            "-ac", str(AUDIO_CONFIG['channels']),
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return True
        else:
            print(f"FFmpeg错误: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("错误: 未找到FFmpeg，将创建占位文件")
        return create_placeholder_file(output_path)
    except Exception as e:
        print(f"创建音频失败: {e}")
        return create_placeholder_file(output_path)

def create_placeholder_file(output_path):
    """创建占位文件"""
    try:
        with open(output_path, 'wb') as f:
            # 创建基本的MP3文件头，避免播放器报错
            mp3_header = bytes([
                0xFF, 0xFB, 0x90, 0x00,  # MP3同步字和头信息
                0x00, 0x00, 0x00, 0x00,  # 填充数据
                0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00
            ])
            f.write(mp3_header)
            # 添加更多数据达到合理大小
            f.write(b'\\x00' * (1024 * 30))  # 30KB占位数据
        return True
    except Exception as e:
        print(f"创建占位文件失败: {e}")
        return False

def generate_test_audio_files(base_path, dialect, create_audio=True):
    """为指定方言生成测试音频文件"""
    generated_files = []
    
    for category, difficulties in CATEGORIES.items():
        for difficulty, count in difficulties.items():
            if count == 0:
                continue
                
            # 创建目录
            dir_path = os.path.join(base_path, dialect, category, difficulty)
            ensure_directory(dir_path)
            
            # 生成音频文件
            for i in range(1, count + 1):
                filename = f"{dialect}_{category}_{difficulty}_{i:03d}.mp3"
                file_path = os.path.join(dir_path, filename)
                
                if create_audio:
                    success = create_silent_audio(file_path, AUDIO_CONFIG['duration'])
                    if success:
                        generated_files.append(file_path)
                        print(f"✓ 创建: {filename}")
                    else:
                        print(f"✗ 失败: {filename}")
                else:
                    generated_files.append(file_path)
                    print(f"→ 规划: {filename}")
    
    return generated_files

def validate_audio_file(file_path):
    """验证音频文件规格"""
    if not os.path.exists(file_path):
        return False, "文件不存在"
    
    file_size = os.path.getsize(file_path) / 1024  # KB
    if file_size > 80:
        return False, f"文件过大: {file_size:.1f}KB"
    
    try:
        # 使用ffprobe检查音频信息
        cmd = [
            "ffprobe", "-v", "quiet", "-print_format", "json",
            "-show_format", "-show_streams", file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            return True, f"基本有效 ({file_size:.1f}KB)"  # 占位文件
        
        info = json.loads(result.stdout)
        
        # 检查音频流
        audio_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'audio']
        if not audio_streams:
            return False, "无音频流"
        
        stream = audio_streams[0]
        duration = float(stream.get('duration', 0))
        sample_rate = int(stream.get('sample_rate', 0))
        channels = int(stream.get('channels', 0))
        
        issues = []
        if abs(duration - AUDIO_CONFIG['duration']) > 0.5:
            issues.append(f"时长{duration:.1f}s")
        if sample_rate != AUDIO_CONFIG['sample_rate']:
            issues.append(f"采样率{sample_rate}Hz")
        if channels != AUDIO_CONFIG['channels']:
            issues.append(f"声道{channels}")
        
        if issues:
            return True, f"有效但需调整: {', '.join(issues)} ({file_size:.1f}KB)"
        else:
            return True, f"符合规格 ({file_size:.1f}KB)"
            
    except Exception as e:
        return True, f"检查失败但文件存在 ({file_size:.1f}KB)"

def create_audio_manifest(base_path, generated_files):
    """创建音频清单文件"""
    manifest = {
        "version": "1.0.0",
        "generated_at": "2025-08-02",
        "total_files": len(generated_files),
        "audio_config": AUDIO_CONFIG,
        "files": []
    }
    
    for file_path in generated_files:
        rel_path = os.path.relpath(file_path, base_path)
        is_valid, status = validate_audio_file(file_path)
        
        file_info = {
            "path": rel_path.replace("\\\\", "/"),
            "filename": os.path.basename(file_path),
            "size_kb": os.path.getsize(file_path) / 1024 if os.path.exists(file_path) else 0,
            "status": status,
            "valid": is_valid
        }
        manifest["files"].append(file_info)
    
    # 统计信息
    stats = {"by_dialect": {}, "by_category": {}, "by_difficulty": {}}
    for file_info in manifest["files"]:
        parts = file_info["filename"].split("_")
        if len(parts) >= 4:
            dialect, category, difficulty = parts[0], parts[1], parts[2]
            
            stats["by_dialect"][dialect] = stats["by_dialect"].get(dialect, 0) + 1
            stats["by_category"][category] = stats["by_category"].get(category, 0) + 1
            stats["by_difficulty"][difficulty] = stats["by_difficulty"].get(difficulty, 0) + 1
    
    manifest["statistics"] = stats
    
    # 保存清单
    manifest_path = os.path.join(base_path, "audio_manifest.json")
    with open(manifest_path, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, ensure_ascii=False, indent=2)
    
    print(f"\\n✓ 音频清单已保存: {manifest_path}")
    return manifest_path

def main():
    parser = argparse.ArgumentParser(description="生成测试音频文件")
    parser.add_argument("--base-path", default="../content/audio", help="音频文件基础路径")
    parser.add_argument("--dialect", choices=list(DIALECTS.keys()) + ["all"], default="all", help="生成指定方言")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要创建的文件，不实际创建")
    parser.add_argument("--validate-only", action="store_true", help="仅验证现有文件")
    
    args = parser.parse_args()
    
    base_path = os.path.abspath(args.base_path)
    print(f"音频文件基础路径: {base_path}")
    
    if args.validate_only:
        # 仅验证现有文件
        print("\\n=== 验证现有音频文件 ===")
        existing_files = []
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.endswith('.mp3'):
                    existing_files.append(os.path.join(root, file))
        
        if not existing_files:
            print("未找到音频文件")
            return
            
        for file_path in existing_files:
            is_valid, status = validate_audio_file(file_path)
            rel_path = os.path.relpath(file_path, base_path)
            print(f"{'✓' if is_valid else '✗'} {rel_path}: {status}")
        
        return
    
    # 生成文件
    create_audio = not args.dry_run
    dialects_to_process = [args.dialect] if args.dialect != "all" else list(DIALECTS.keys())
    
    all_generated_files = []
    
    for dialect in dialects_to_process:
        print(f"\\n=== 处理方言: {DIALECTS[dialect]} ({dialect}) ===")
        generated_files = generate_test_audio_files(base_path, dialect, create_audio)
        all_generated_files.extend(generated_files)
        print(f"完成 {len(generated_files)} 个文件")
    
    if create_audio and all_generated_files:
        # 创建清单和验证
        create_audio_manifest(base_path, all_generated_files)
        
        print(f"\\n=== 生成总结 ===")
        print(f"总计生成: {len(all_generated_files)} 个音频文件")
        print(f"涵盖方言: {len(dialects_to_process)} 个")
        print(f"存储位置: {base_path}")
    elif not create_audio:
        print(f"\\n=== 规划总结 (DRY RUN) ===")
        print(f"计划生成: {len(all_generated_files)} 个音频文件")
        print(f"涵盖方言: {len(dialects_to_process)} 个")
        print(f"目标位置: {base_path}")
    
    print("\\n=== 后续步骤 ===")
    print("1. 使用此脚本创建测试框架")
    print("2. 联系本地录音师录制真实音频")
    print("3. 使用 quality-checker.py 验证音频质量")
    print("4. 更新题库数据库")
    print("5. 在游戏中测试音频播放")

if __name__ == "__main__":
    main()