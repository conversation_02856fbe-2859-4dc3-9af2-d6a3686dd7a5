/**
 * 微信小游戏主入口文件
 * 家乡话猜猜猜小游戏
 */

// 引入适配器
require('./adapter');

// 引入 Cocos Creator 引擎
require('cocos2d-js-min.js');

// 游戏初始化
const initGame = () => {
    // 设置游戏配置
    const gameConfig = {
        RESOURCES_ONLY: false,
        loadFirstScene: true,
        groupList: [],
        collisionMatrix: []
    };
    
    // 初始化引擎
    cc.game.run(gameConfig, () => {
        console.log('[Game] 游戏引擎初始化完成');
        
        // 监听游戏显示/隐藏
        wx.onShow((res) => {
            console.log('[Game] 游戏显示', res);
            cc.game.resume();
        });
        
        wx.onHide(() => {
            console.log('[Game] 游戏隐藏');
            cc.game.pause();
        });
        
        // 监听内存警告
        wx.onMemoryWarning((res) => {
            console.warn('[Game] 内存警告', res);
            // 触发垃圾回收
            if (cc.sys.garbageCollect) {
                cc.sys.garbageCollect();
            }
        });
        
        // 监听网络状态变化
        wx.onNetworkStatusChange((res) => {
            console.log('[Game] 网络状态变化', res);
            // 通知游戏管理器网络状态变化
            if (cc.systemEvent) {
                cc.systemEvent.emit('network_status_change', res);
            }
        });
        
        // 监听音频中断
        wx.onAudioInterruptionBegin(() => {
            console.log('[Game] 音频中断开始');
            // 暂停游戏音频
            if (cc.audioEngine) {
                cc.audioEngine.pauseAll();
            }
        });
        
        wx.onAudioInterruptionEnd(() => {
            console.log('[Game] 音频中断结束');
            // 恢复游戏音频
            if (cc.audioEngine) {
                cc.audioEngine.resumeAll();
            }
        });
    });
};

// 监听微信小游戏生命周期
wx.onLaunch((options) => {
    console.log('[Game] 小游戏启动', options);
    
    // 获取设备信息
    const systemInfo = wx.getSystemInfoSync();
    console.log('[Game] 设备信息:', systemInfo);
    
    // 初始化游戏
    initGame();
});

wx.onError((error) => {
    console.error('[Game] 小游戏错误:', error);
});

// 设置分享信息
wx.onShareAppMessage(() => {
    return {
        title: '家乡话猜猜猜 - 听方言，猜家乡！',
        imageUrl: 'assets/images/share-image.jpg',
        query: 'from_share=true'
    };
});

// 性能监控
if (wx.getPerformance) {
    const performance = wx.getPerformance();
    
    // 监控内存使用
    setInterval(() => {
        const memoryInfo = performance.getMemoryInfo();
        if (memoryInfo.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
            console.warn('[Game] 内存使用过高:', memoryInfo);
        }
    }, 30000); // 每30秒检查一次
}

// 导出全局对象供游戏使用
global.wx = wx;
global.GameGlobal = {
    isWeChatGame: true,
    deviceInfo: wx.getSystemInfoSync(),
    version: '1.0.0'
};

console.log('[Game] 微信小游戏环境初始化完成');