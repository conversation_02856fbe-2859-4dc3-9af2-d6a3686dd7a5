{"deviceOrientation": "portrait", "showStatusBar": false, "networkTimeout": {"request": 10000, "connectSocket": 10000, "uploadFile": 10000, "downloadFile": 10000}, "subpackages": [{"name": "audio-assets", "root": "assets/audio/"}], "preloadRule": {"pages/index": {"network": "all", "packages": ["audio-assets"]}}, "navigateToMiniProgramAppIdList": [], "permission": {"scope.userInfo": {"desc": "用于个性化游戏体验"}}, "plugins": {}, "workers": "workers", "requiredBackgroundModes": [], "resizable": false}