{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020", "DOM"], "moduleResolution": "node", "strict": true, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "declaration": false, "sourceMap": false, "baseUrl": "./", "paths": {"cc": ["./cocos-js"], "cc/*": ["./cocos-js/*"]}, "typeRoots": ["./node_modules/@types", "./@types"]}, "include": ["assets/scripts/**/*"], "exclude": ["build/**/*", "library/**/*", "local/**/*", "temp/**/*", "node_modules/**/*"]}