import { _decorator, Component, Node, Button, Sprite, Label, tween, Vec3, Color } from 'cc';
import { GameManager } from '../managers/GameManager';
import { EventManager } from '../managers/EventManager';
import { UI_CONFIG, GAME_RULES } from '../constants/GameConstants';
import { UIAnimationHelper } from '../utils/UIAnimationHelper';

const { ccclass, property } = _decorator;

/**
 * 音频播放按钮组件
 * 负责音频播放控制和视觉反馈
 */
@ccclass('AudioButton')
export class AudioButton extends Component {
    @property(Button)
    public button: Button = null;
    
    @property(Sprite)
    public iconSprite: Sprite = null;
    
    @property(Label)
    public countLabel: Label = null;
    
    @property(Node)
    public rippleContainer: Node = null;
    
    @property([Node])
    public rippleNodes: Node[] = [];
    
    // 状态管理
    private _isPlaying: boolean = false;
    private _playCount: number = 0;
    private _maxPlays: number = GAME_RULES.MAX_AUDIO_PLAYS;
    
    // 管理器引用
    private _gameManager: GameManager = null;
    private _eventManager: EventManager = null;
    
    // 动画控制
    private _rippleAnimations: any[] = [];
    private _iconAnimation: any = null;
    
    protected onLoad(): void {
        this.initializeComponents();
        this.registerEventListeners();
    }
    
    protected onDestroy(): void {
        this.unregisterEventListeners();
        this.stopAllAnimations();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 获取管理器引用
        this._gameManager = GameManager.instance;
        this._eventManager = EventManager.instance;
        
        // 初始化按钮
        if (!this.button) {
            this.button = this.node.getComponent(Button);
            if (!this.button) {
                this.button = this.node.addComponent(Button);
            }
        }
        
        // 设置按钮点击事件
        this.button.node.on(Button.EventType.CLICK, this.onButtonClick, this);
        
        // 初始化图标
        if (!this.iconSprite) {
            this.iconSprite = this.node.getComponentInChildren(Sprite);
        }
        
        // 初始化计数标签
        if (!this.countLabel) {
            const countNode = this.node.getChildByName('CountLabel');
            if (countNode) {
                this.countLabel = countNode.getComponent(Label);
            }
        }
        
        // 初始化同心圆容器
        if (!this.rippleContainer) {
            this.rippleContainer = this.node.getChildByName('RippleContainer');
        }
        
        // 创建同心圆节点
        this.createRippleNodes();
        
        // 设置初始状态
        this.reset();
        
        console.log('[AudioButton] 音频按钮初始化完成');
    }
    
    /**
     * 创建同心圆节点
     */
    private createRippleNodes(): void {
        if (!this.rippleContainer) return;
        
        // 清理现有节点
        this.rippleContainer.removeAllChildren();
        this.rippleNodes = [];
        
        // 创建同心圆节点
        for (let i = 0; i < UI_CONFIG.AUDIO_BUTTON.RIPPLE_COUNT; i++) {
            const rippleNode = new Node(`Ripple_${i}`);
            const sprite = rippleNode.addComponent(Sprite);
            
            // 设置同心圆属性
            rippleNode.setParent(this.rippleContainer);
            rippleNode.setScale(0.5, 0.5, 1);
            
            // 设置透明度和颜色
            sprite.color = new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY);
            sprite.color.a = 100; // 半透明
            
            this.rippleNodes.push(rippleNode);
        }
        
        // 隐藏同心圆
        this.rippleContainer.active = false;
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        if (!this._eventManager) return;
        
        // 监听音频播放事件
        this._eventManager.on('audio_play_started', this.onAudioPlayStarted, this);
        this._eventManager.on('audio_play_complete', this.onAudioPlayComplete, this);
        this._eventManager.on('audio_play_error', this.onAudioPlayError, this);
        
        // 监听游戏状态事件
        this._eventManager.on('question_started', this.onQuestionStarted, this);
    }
    
    /**
     * 注销事件监听器
     */
    private unregisterEventListeners(): void {
        if (!this._eventManager) return;
        
        this._eventManager.targetOff(this);
    }
    
    /**
     * 重置按钮状态
     */
    public reset(): void {
        this._isPlaying = false;
        this._playCount = 0;
        
        this.updateButtonState();
        this.updateCountDisplay();
        this.stopAllAnimations();
        
        console.log('[AudioButton] 按钮状态已重置');
    }
    
    /**
     * 更新按钮状态
     */
    private updateButtonState(): void {
        if (!this.button) return;
        
        // 检查是否可以播放
        const canPlay = this._playCount < this._maxPlays && !this._isPlaying;
        
        // 设置按钮交互状态
        this.button.interactable = canPlay;
        
        // 更新按钮视觉状态
        if (this.iconSprite) {
            if (this._isPlaying) {
                // 播放状态 - 暂停图标
                this.iconSprite.color = new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY);
            } else if (canPlay) {
                // 可播放状态 - 播放图标
                this.iconSprite.color = new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY);
            } else {
                // 不可播放状态 - 灰色
                this.iconSprite.color = new Color(128, 128, 128, 255);
            }
        }
        
        // 更新按钮缩放
        const targetScale = canPlay ? 1.0 : 0.9;
        if (this.node.scale.x !== targetScale) {
            tween(this.node)
                .to(0.2, { scale: new Vec3(targetScale, targetScale, 1) })
                .start();
        }
    }
    
    /**
     * 更新计数显示
     */
    private updateCountDisplay(): void {
        if (!this.countLabel) return;
        
        const remaining = this._maxPlays - this._playCount;
        this.countLabel.string = `${remaining}`;
        
        // 设置计数标签颜色
        if (remaining > 1) {
            this.countLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.TEXT_PRIMARY);
        } else if (remaining === 1) {
            this.countLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY);
        } else {
            this.countLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
        }
    }
    
    /**
     * 开始播放动画
     */
    private startPlayingAnimation(): void {
        if (!this.rippleContainer || this.rippleNodes.length === 0) return;
        
        // 显示同心圆容器
        this.rippleContainer.active = true;
        
        // 停止之前的动画
        this.stopRippleAnimations();
        
        // 启动同心圆扩散动画
        this.rippleNodes.forEach((rippleNode, index) => {
            const delay = index * 200; // 每个圆的延迟时间
            
            const animation = tween(rippleNode)
                .delay(delay / 1000)
                .repeatForever(
                    tween(rippleNode)
                        .to(0, { scale: new Vec3(0.5, 0.5, 1) })
                        .to(UI_CONFIG.AUDIO_BUTTON.ANIMATION_DURATION / 1000, { 
                            scale: new Vec3(2.0, 2.0, 1) 
                        })
                        .parallel(
                            tween(rippleNode.getComponent(Sprite)!)
                                .to(UI_CONFIG.AUDIO_BUTTON.ANIMATION_DURATION / 1000, { 
                                    color: new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY).clone().multiplyScalar(1).setA(0)
                                })
                        )
                        .delay(300 / 1000) // 间隔时间
                        .call(() => {
                            // 重置同心圆状态
                            rippleNode.setScale(0.5, 0.5, 1);
                            const sprite = rippleNode.getComponent(Sprite)!;
                            sprite.color = new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY);
                            sprite.color.a = 100;
                        })
                )
                .start();
                
            this._rippleAnimations.push(animation);
        });
        
        // 按钮图标旋转动画
        if (this.iconSprite) {
            this._iconAnimation = tween(this.iconSprite.node)
                .repeatForever(
                    tween(this.iconSprite.node)
                        .by(2, { angle: 360 })
                )
                .start();
        }
        
        console.log('[AudioButton] 播放动画已启动');
    }
    
    /**
     * 停止播放动画
     */
    private stopPlayingAnimation(): void {
        // 隐藏同心圆
        if (this.rippleContainer) {
            this.rippleContainer.active = false;
        }
        
        // 停止所有动画
        this.stopRippleAnimations();
        
        // 重置图标旋转
        if (this._iconAnimation) {
            this._iconAnimation.stop();
            this._iconAnimation = null;
            
            if (this.iconSprite) {
                this.iconSprite.node.angle = 0;
            }
        }
        
        console.log('[AudioButton] 播放动画已停止');
    }
    
    /**
     * 停止同心圆动画
     */
    private stopRippleAnimations(): void {
        this._rippleAnimations.forEach(animation => {
            if (animation) {
                animation.stop();
            }
        });
        this._rippleAnimations = [];
    }
    
    /**
     * 停止所有动画
     */
    private stopAllAnimations(): void {
        this.stopPlayingAnimation();
    }
    
    /**
     * 播放按钮点击动画（使用动画池优化）
     */
    private playClickAnimation(): void {
        if (!this.node) return;

        UIAnimationHelper.clickFeedback(this.node, () => {
            console.log('[AudioButton] 点击动画完成');
        });
    }
    
    // ========== 公共接口 ==========
    
    /**
     * 播放开始回调
     */
    public onPlayStarted(): void {
        this._isPlaying = true;
        this._playCount++;
        
        this.updateButtonState();
        this.updateCountDisplay();
        this.startPlayingAnimation();
        
        console.log(`[AudioButton] 播放开始，第 ${this._playCount} 次`);
    }
    
    /**
     * 播放完成回调
     */
    public onPlayComplete(): void {
        this._isPlaying = false;
        
        this.updateButtonState();
        this.stopPlayingAnimation();
        
        console.log('[AudioButton] 播放完成');
    }
    
    /**
     * 播放错误回调
     */
    public onPlayError(): void {
        this._isPlaying = false;
        
        this.updateButtonState();
        this.stopPlayingAnimation();
        
        // 播放错误动画
        this.playErrorAnimation();
        
        console.log('[AudioButton] 播放错误');
    }
    
    /**
     * 播放错误动画（使用动画池优化）
     */
    private playErrorAnimation(): void {
        if (!this.node) return;

        // 使用动画助手播放错误反馈动画
        UIAnimationHelper.errorFeedback(this.node, () => {
            console.log('[AudioButton] 错误动画完成');
        });

        // 红色闪烁效果
        if (this.iconSprite) {
            const originalColor = this.iconSprite.color.clone();
            const errorColor = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);

            tween(this.iconSprite)
                .to(0.2, { color: errorColor })
                .to(0.2, { color: originalColor })
                .to(0.2, { color: errorColor })
                .to(0.2, { color: originalColor })
                .start();
        }
    }
    
    // ========== 事件处理器 ==========
    
    /**
     * 按钮点击事件处理
     */
    private onButtonClick(): void {
        if (!this._gameManager || this._isPlaying || this._playCount >= this._maxPlays) {
            return;
        }
        
        console.log('[AudioButton] 按钮被点击');
        
        // 播放点击动画
        this.playClickAnimation();
        
        // 请求播放音频
        this._gameManager.playCurrentQuestionAudio();
    }
    
    /**
     * 音频播放开始事件处理
     */
    private onAudioPlayStarted(event: any): void {
        // 由外部调用 onPlayStarted 方法
    }
    
    /**
     * 音频播放完成事件处理
     */
    private onAudioPlayComplete(event: any): void {
        // 由外部调用 onPlayComplete 方法
    }
    
    /**
     * 音频播放错误事件处理
     */
    private onAudioPlayError(event: any): void {
        // 由外部调用 onPlayError 方法
    }
    
    /**
     * 题目开始事件处理
     */
    private onQuestionStarted(event: any): void {
        // 重置按钮状态（由外部调用 reset 方法）
    }
    
    // ========== 属性访问器 ==========
    
    /**
     * 是否正在播放
     */
    public get isPlaying(): boolean {
        return this._isPlaying;
    }
    
    /**
     * 播放次数
     */
    public get playCount(): number {
        return this._playCount;
    }
    
    /**
     * 是否可以播放
     */
    public get canPlay(): boolean {
        return this._playCount < this._maxPlays && !this._isPlaying;
    }
}