import { _decorator, Component, Button, Label, Node } from 'cc';
import { ILanguageInfo, SupportedLanguage } from '../data/GameData';

const { ccclass, property } = _decorator;

/**
 * 语言选项组件
 */
@ccclass('LanguageItem')
export class LanguageItem extends Component {
    @property({
        type: Button,
        tooltip: '选择按钮'
    })
    public selectButton: Button = null;
    
    @property({
        type: Label,
        tooltip: '语言名称标签'
    })
    public nameLabel: Label = null;
    
    @property({
        type: Label,
        tooltip: '原生名称标签'
    })
    public nativeNameLabel: Label = null;
    
    @property({
        type: Label,
        tooltip: '完成度标签'
    })
    public completenessLabel: Label = null;
    
    @property({
        type: Node,
        tooltip: '选中状态指示器'
    })
    public selectedIndicator: Node = null;
    
    @property({
        type: Node,
        tooltip: '旗帜图标'
    })
    public flagIcon: Node = null;
    
    // 语言信息
    private _languageInfo: ILanguageInfo = null;
    private _isSelected: boolean = false;
    
    // 选择回调
    private _selectionCallback: (language: SupportedLanguage) => void = null;
    
    /**
     * 初始化语言选项
     */
    public initialize(
        languageInfo: ILanguageInfo, 
        showNativeName: boolean = true, 
        showCompleteness: boolean = true
    ): void {
        this._languageInfo = languageInfo;
        
        // 设置语言名称
        if (this.nameLabel) {
            this.nameLabel.string = languageInfo.name;
        }
        
        // 设置原生名称
        if (this.nativeNameLabel) {
            this.nativeNameLabel.string = languageInfo.nativeName;
            this.nativeNameLabel.node.active = showNativeName;
        }
        
        // 设置完成度
        if (this.completenessLabel) {
            this.completenessLabel.string = `${languageInfo.completeness}%`;
            this.completenessLabel.node.active = showCompleteness;
        }
        
        // 设置旗帜图标
        if (this.flagIcon) {
            // 这里可以设置旗帜图标
            // 由于Cocos Creator中设置emoji比较复杂，这里暂时跳过
        }
        
        // 设置按钮事件
        if (this.selectButton) {
            this.selectButton.node.on(Button.EventType.CLICK, this.onSelectClicked, this);
        }
        
        // 设置是否支持
        const buttonComponent = this.node.getComponent(Button);
        if (buttonComponent) {
            buttonComponent.interactable = languageInfo.isSupported;
        }
        
        this.updateVisualState();
    }
    
    /**
     * 设置选中状态
     */
    public setSelected(selected: boolean): void {
        if (this._isSelected !== selected) {
            this._isSelected = selected;
            this.updateVisualState();
        }
    }
    
    /**
     * 获取是否选中
     */
    public isSelected(): boolean {
        return this._isSelected;
    }
    
    /**
     * 获取语言代码
     */
    public getLanguageCode(): SupportedLanguage {
        return this._languageInfo ? this._languageInfo.code : null;
    }
    
    /**
     * 获取语言信息
     */
    public getLanguageInfo(): ILanguageInfo {
        return this._languageInfo;
    }
    
    /**
     * 设置选择回调
     */
    public setSelectionCallback(callback: (language: SupportedLanguage) => void): void {
        this._selectionCallback = callback;
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 更新视觉状态
     */
    private updateVisualState(): void {
        // 更新选中指示器
        if (this.selectedIndicator) {
            this.selectedIndicator.active = this._isSelected;
        }
        
        // 更新按钮状态
        if (this.selectButton) {
            // 这里可以更新按钮的视觉状态
        }
        
        // 更新不支持的语言的视觉效果
        if (this._languageInfo && !this._languageInfo.isSupported) {
            // 设置半透明效果
            this.node.opacity = 128;
        } else {
            this.node.opacity = 255;
        }
    }
    
    /**
     * 选择按钮点击事件
     */
    private onSelectClicked(): void {
        if (!this._languageInfo || !this._languageInfo.isSupported) {
            console.warn('[LanguageItem] 语言不支持或信息无效');
            return;
        }
        
        if (this._selectionCallback) {
            this._selectionCallback(this._languageInfo.code);
        }
    }
}
