import { _decorator, Component, Node, Label, tween, Vec3 } from 'cc';
import { AnswerOption } from './AnswerOption';
import { AudioButton } from './AudioButton';
import { ScoreDisplay } from './ScoreDisplay';
import { IQuestionData } from '../data/GameData';
import { UI_CONFIG } from '../constants/GameConstants';
import { UIAnimationHelper } from '../utils/UIAnimationHelper';
import { BaseComponent } from '../utils/BaseComponent';

const { ccclass, property } = _decorator;

/**
 * 题目组件
 * 整合音频播放、答题选项、分数显示等功能
 */
@ccclass('QuestionComponent')
export class QuestionComponent extends BaseComponent {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    public questionContainer: Node = null;
    
    @property(Label)
    public questionIndexLabel: Label = null;
    
    @property(Label)
    public dialectHintLabel: Label = null;
    
    @property(AudioButton)
    public audioButton: AudioButton = null;
    
    @property(ScoreDisplay)
    public scoreDisplay: ScoreDisplay = null;
    
    @property([AnswerOption])
    public answerOptions: AnswerOption[] = [];
    
    @property(Node)
    public optionsContainer: Node = null;
    
    @property(Node)
    public feedbackContainer: Node = null;
    
    @property(Label)
    public feedbackLabel: Label = null;
    
    @property(Node)
    public correctAnswerNode: Node = null;
    
    @property(Label)
    public correctAnswerLabel: Label = null;
    
    // ==================== 私有属性 ====================
    
    // 题目数据
    private _currentQuestion: IQuestionData = null;
    private _questionIndex: number = 0;
    private _totalQuestions: number = 0;
    
    // 答题状态
    private _isAnswering: boolean = false;
    private _selectedAnswerIndex: number = -1;
    private _correctAnswerIndex: number = -1;
    
    // 回调函数
    private _onAnswerSelectedCallback: ((answerIndex: number) => void) | null = null;
    private _onAudioPlayRequestCallback: (() => void) | null = null;
    
    // 动画状态
    private _isShowingResult: boolean = false;
    
    // ==================== 生命周期 ====================
    
    protected onLoad(): void {
        this.initializeComponents();
        this.setupEventListeners();
    }
    
    protected onDestroy(): void {
        this.cleanup();
    }
    
    // ==================== 初始化 ====================
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找组件
        this.autoFindComponents();
        
        // 初始化答题选项
        this.initializeAnswerOptions();
        
        // 初始化音频按钮
        this.initializeAudioButton();
        
        // 初始化分数显示
        this.initializeScoreDisplay();
        
        // 设置初始状态
        this.setInitialState();
        
        console.log('[QuestionComponent] 题目组件初始化完成');
    }
    
    /**
     * 自动查找组件
     */
    private autoFindComponents(): void {
        if (!this.questionContainer) {
            this.questionContainer = this.node.getChildByName('QuestionContainer');
        }
        
        if (!this.questionIndexLabel) {
            const indexNode = this.node.getChildByName('QuestionIndex');
            if (indexNode) {
                this.questionIndexLabel = indexNode.getComponent(Label);
            }
        }
        
        if (!this.dialectHintLabel) {
            const hintNode = this.node.getChildByName('DialectHint');
            if (hintNode) {
                this.dialectHintLabel = hintNode.getComponent(Label);
            }
        }
        
        if (!this.audioButton) {
            const audioNode = this.node.getChildByName('AudioButton');
            if (audioNode) {
                this.audioButton = audioNode.getComponent(AudioButton);
            }
        }
        
        if (!this.scoreDisplay) {
            const scoreNode = this.node.getChildByName('ScoreDisplay');
            if (scoreNode) {
                this.scoreDisplay = scoreNode.getComponent(ScoreDisplay);
            }
        }
        
        if (!this.optionsContainer) {
            this.optionsContainer = this.node.getChildByName('OptionsContainer');
        }
        
        if (!this.feedbackContainer) {
            this.feedbackContainer = this.node.getChildByName('FeedbackContainer');
        }
        
        if (!this.feedbackLabel) {
            const feedbackNode = this.feedbackContainer?.getChildByName('FeedbackLabel');
            if (feedbackNode) {
                this.feedbackLabel = feedbackNode.getComponent(Label);
            }
        }
        
        if (!this.correctAnswerNode) {
            this.correctAnswerNode = this.feedbackContainer?.getChildByName('CorrectAnswer');
        }
        
        if (!this.correctAnswerLabel) {
            const correctNode = this.correctAnswerNode?.getChildByName('CorrectAnswerLabel');
            if (correctNode) {
                this.correctAnswerLabel = correctNode.getComponent(Label);
            }
        }
    }
    
    /**
     * 初始化答题选项
     */
    private initializeAnswerOptions(): void {
        // 如果没有预设答题选项，自动查找
        if (this.answerOptions.length === 0 && this.optionsContainer) {
            const optionNodes = this.optionsContainer.children;
            for (let i = 0; i < optionNodes.length; i++) {
                const optionComponent = optionNodes[i].getComponent(AnswerOption);
                if (optionComponent) {
                    this.answerOptions.push(optionComponent);
                }
            }
        }
        
        // 确保有4个答题选项
        while (this.answerOptions.length < 4) {
            const optionNode = new Node(`Option_${this.answerOptions.length}`);
            const optionComponent = optionNode.addComponent(AnswerOption);
            
            if (this.optionsContainer) {
                optionNode.setParent(this.optionsContainer);
            } else {
                optionNode.setParent(this.node);
            }
            
            this.answerOptions.push(optionComponent);
        }
        
        console.log(`[QuestionComponent] 初始化了 ${this.answerOptions.length} 个答题选项`);
    }
    
    /**
     * 初始化音频按钮
     */
    private initializeAudioButton(): void {
        if (!this.audioButton) {
            // 创建音频按钮
            const audioNode = new Node('AudioButton');
            this.audioButton = audioNode.addComponent(AudioButton);
            audioNode.setParent(this.questionContainer || this.node);
        }
    }
    
    /**
     * 初始化分数显示
     */
    private initializeScoreDisplay(): void {
        if (!this.scoreDisplay) {
            // 创建分数显示
            const scoreNode = new Node('ScoreDisplay');
            this.scoreDisplay = scoreNode.addComponent(ScoreDisplay);
            scoreNode.setParent(this.questionContainer || this.node);
        }
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 监听答题选项点击
        this.answerOptions.forEach((option, index) => {
            if (option) {
                // 答题选项会通过回调函数通知选择
            }
        });
    }
    
    /**
     * 设置初始状态
     */
    private setInitialState(): void {
        // 隐藏反馈容器
        if (this.feedbackContainer) {
            this.feedbackContainer.active = false;
        }
        
        // 隐藏正确答案显示
        if (this.correctAnswerNode) {
            this.correctAnswerNode.active = false;
        }
        
        // 重置状态
        this._isAnswering = false;
        this._selectedAnswerIndex = -1;
        this._correctAnswerIndex = -1;
        this._isShowingResult = false;
    }
    
    // ==================== 公共接口 ====================
    
    /**
     * 设置题目数据
     */
    public setQuestion(question: IQuestionData, questionIndex: number, totalQuestions: number): void {
        this._currentQuestion = question;
        this._questionIndex = questionIndex;
        this._totalQuestions = totalQuestions;
        this._correctAnswerIndex = question.correctAnswer;
        
        // 更新题目索引显示
        this.updateQuestionIndex();
        
        // 更新方言提示
        this.updateDialectHint();
        
        // 设置答题选项
        this.setupAnswerOptions();
        
        // 重置状态
        this.resetQuestionState();
        
        // 播放题目出现动画
        this.playQuestionAppearAnimation();
        
        console.log(`[QuestionComponent] 设置题目: ${question.id} (${questionIndex + 1}/${totalQuestions})`);
    }
    
    /**
     * 开始答题
     */
    public startAnswering(onAnswerSelected: (answerIndex: number) => void, onAudioPlayRequest?: () => void): void {
        this._onAnswerSelectedCallback = onAnswerSelected;
        this._onAudioPlayRequestCallback = onAudioPlayRequest;
        
        this._isAnswering = true;
        this._selectedAnswerIndex = -1;
        
        // 启用答题选项
        this.setAnswerOptionsInteractable(true);
        
        // 重置音频按钮
        if (this.audioButton) {
            this.audioButton.reset();
        }
        
        console.log('[QuestionComponent] 开始答题');
    }
    
    /**
     * 提交答案
     */
    public submitAnswer(selectedIndex: number): void {
        if (!this._isAnswering || this._selectedAnswerIndex !== -1) {
            return;
        }
        
        this._selectedAnswerIndex = selectedIndex;
        this._isAnswering = false;
        
        // 禁用答题选项
        this.setAnswerOptionsInteractable(false);
        
        // 高亮选中的答案
        this.highlightSelectedAnswer(selectedIndex);
        
        // 调用回调函数
        if (this._onAnswerSelectedCallback) {
            this._onAnswerSelectedCallback(selectedIndex);
        }
        
        console.log(`[QuestionComponent] 提交答案: ${selectedIndex}`);
    }
    
    /**
     * 显示答题结果
     */
    public showResult(isCorrect: boolean, correctAnswerIndex: number): void {
        this._isShowingResult = true;
        
        // 显示正确和错误状态
        this.showAnswerStates(isCorrect, correctAnswerIndex);
        
        // 显示反馈
        this.showFeedback(isCorrect, correctAnswerIndex);
        
        // 播放结果动画
        this.playResultAnimation(isCorrect);
        
        console.log(`[QuestionComponent] 显示结果: ${isCorrect ? '正确' : '错误'}`);
    }
    
    /**
     * 更新分数显示
     */
    public updateScore(score: number, combo: number): void {
        if (this.scoreDisplay) {
            this.scoreDisplay.updateScore(score, combo);
        }
    }
    
    /**
     * 重置题目状态
     */
    public reset(): void {
        // 重置题目数据
        this._currentQuestion = null;
        this._questionIndex = 0;
        this._totalQuestions = 0;
        this._correctAnswerIndex = -1;
        
        // 重置状态
        this.resetQuestionState();
        
        // 重置音频按钮
        if (this.audioButton) {
            this.audioButton.reset();
        }
        
        // 重置分数显示
        if (this.scoreDisplay) {
            this.scoreDisplay.reset();
        }
        
        // 重置答题选项
        this.resetAnswerOptions();
        
        console.log('[QuestionComponent] 题目组件已重置');
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 重置题目状态
     */
    private resetQuestionState(): void {
        this._isAnswering = false;
        this._selectedAnswerIndex = -1;
        this._isShowingResult = false;
        
        this.setInitialState();
    }
    
    /**
     * 更新题目索引显示
     */
    private updateQuestionIndex(): void {
        if (this.questionIndexLabel) {
            this.questionIndexLabel.string = `${this._questionIndex + 1}/${this._totalQuestions}`;
        }
    }
    
    /**
     * 更新方言提示
     */
    private updateDialectHint(): void {
        if (this.dialectHintLabel && this._currentQuestion) {
            // 根据题目类型显示不同的提示
            let hintText = '请听音频，选择对应的方言区域';
            
            if (this._currentQuestion.category) {
                switch (this._currentQuestion.category) {
                    case 'daily':
                        hintText = '日常用语 - 这是哪里的方言？';
                        break;
                    case 'numbers':
                        hintText = '数字读音 - 这是哪里的方言？';
                        break;
                    case 'colors':
                        hintText = '颜色词汇 - 这是哪里的方言？';
                        break;
                    case 'local_terms':
                        hintText = '地方词汇 - 这是哪里的方言？';
                        break;
                    case 'humor':
                        hintText = '幽默表达 - 这是哪里的方言？';
                        break;
                    default:
                        hintText = '请听音频，选择对应的方言区域';
                        break;
                }
            }
            
            this.dialectHintLabel.string = hintText;
        }
    }
    
    /**
     * 设置答题选项
     */
    private setupAnswerOptions(): void {
        if (!this._currentQuestion || this.answerOptions.length < 4) {
            return;
        }
        
        // 设置每个选项
        for (let i = 0; i < 4; i++) {
            const option = this.answerOptions[i];
            const optionText = this._currentQuestion.options[i] || '';
            
            if (option) {
                option.initialize(i, optionText, (index: number) => {
                    this.submitAnswer(index);
                });
            }
        }
    }
    
    /**
     * 重置答题选项
     */
    private resetAnswerOptions(): void {
        this.answerOptions.forEach((option, index) => {
            if (option) {
                option.initialize(index, '', null);
                option.setInteractable(true);
            }
        });
    }
    
    /**
     * 设置答题选项交互状态
     */
    private setAnswerOptionsInteractable(interactable: boolean): void {
        this.answerOptions.forEach(option => {
            if (option) {
                option.setInteractable(interactable);
            }
        });
    }
    
    /**
     * 高亮选中的答案
     */
    private highlightSelectedAnswer(selectedIndex: number): void {
        this.answerOptions.forEach((option, index) => {
            if (option) {
                if (index === selectedIndex) {
                    // 这里不显示正确/错误状态，等待结果显示
                } else {
                    option.setInteractable(false);
                }
            }
        });
    }
    
    /**
     * 显示答案状态
     */
    private showAnswerStates(isCorrect: boolean, correctAnswerIndex: number): void {
        this.answerOptions.forEach((option, index) => {
            if (option) {
                if (index === correctAnswerIndex) {
                    // 显示正确答案
                    option.showCorrect();
                } else if (index === this._selectedAnswerIndex && !isCorrect) {
                    // 显示错误答案
                    option.showWrong();
                } else {
                    // 显示普通状态
                    option.showNormal();
                }
            }
        });
    }
    
    /**
     * 显示反馈
     */
    private showFeedback(isCorrect: boolean, correctAnswerIndex: number): void {
        if (!this.feedbackContainer) return;
        
        this.feedbackContainer.active = true;
        
        // 设置反馈文本
        if (this.feedbackLabel) {
            if (isCorrect) {
                this.feedbackLabel.string = '回答正确！';
                this.feedbackLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            } else {
                this.feedbackLabel.string = '回答错误';
                this.feedbackLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
            }
        }
        
        // 显示正确答案（如果回答错误）
        if (!isCorrect && this.correctAnswerNode && this.correctAnswerLabel) {
            this.correctAnswerNode.active = true;
            
            const correctOptionText = this._currentQuestion.options[correctAnswerIndex];
            this.correctAnswerLabel.string = `正确答案：${correctOptionText}`;
        }
        
        // 播放反馈出现动画
        this.playFeedbackAnimation();
    }
    
    // ==================== 动画效果 ====================
    
    /**
     * 播放题目出现动画
     */
    private playQuestionAppearAnimation(): void {
        if (!this.questionContainer) return;
        
        // 题目容器淡入动画
        this.questionContainer.setScale(0.9, 0.9, 1);
        this.questionContainer.getComponent(UITransform).setOpacity(0);
        
        tween(this.questionContainer)
            .parallel(
                tween().to(UI_CONFIG.ANIMATIONS.FADE_DURATION / 1000, { scale: new Vec3(1, 1, 1) }),
                tween(this.questionContainer.getComponent(UITransform)).to(UI_CONFIG.ANIMATIONS.FADE_DURATION / 1000, { opacity: 255 })
            )
            .start();
        
        // 答题选项依次出现
        this.answerOptions.forEach((option, index) => {
            if (option && option.node) {
                option.node.setScale(0, 0, 1);
                
                tween(option.node)
                    .delay(index * 0.1 + 0.2)
                    .to(0.3, { scale: new Vec3(1, 1, 1) }, {
                        easing: 'backOut'
                    })
                    .start();
            }
        });
    }
    
    /**
     * 播放结果动画
     */
    private playResultAnimation(isCorrect: boolean): void {
        if (isCorrect) {
            this.playCorrectAnimation();
        } else {
            this.playWrongAnimation();
        }
    }
    
    /**
     * 播放正确动画
     */
    private playCorrectAnimation(): void {
        // 整体组件庆祝动画
        if (this.questionContainer) {
            UIAnimationHelper.successFeedback(this.questionContainer, () => {
                console.log('[QuestionComponent] 正确动画完成');
            });
        }
        
        // 分数显示特殊动画
        if (this.scoreDisplay) {
            // 由分数显示组件内部处理
        }
    }
    
    /**
     * 播放错误动画
     */
    private playWrongAnimation(): void {
        // 整体组件错误动画
        if (this.questionContainer) {
            UIAnimationHelper.errorFeedback(this.questionContainer, () => {
                console.log('[QuestionComponent] 错误动画完成');
            });
        }
    }
    
    /**
     * 播放反馈动画
     */
    private playFeedbackAnimation(): void {
        if (!this.feedbackContainer) return;
        
        this.feedbackContainer.setScale(0.8, 0.8, 1);
        
        tween(this.feedbackContainer)
            .to(0.3, { scale: new Vec3(1, 1, 1) }, {
                easing: 'backOut'
            })
            .start();
    }
    
    // ==================== 音频相关 ====================
    
    /**
     * 音频播放开始回调
     */
    public onAudioPlayStarted(): void {
        if (this.audioButton) {
            this.audioButton.onPlayStarted();
        }
    }
    
    /**
     * 音频播放完成回调
     */
    public onAudioPlayComplete(): void {
        if (this.audioButton) {
            this.audioButton.onPlayComplete();
        }
    }
    
    /**
     * 音频播放错误回调
     */
    public onAudioPlayError(): void {
        if (this.audioButton) {
            this.audioButton.onPlayError();
        }
    }
    
    // ==================== 属性访问器 ====================
    
    /**
     * 是否正在答题
     */
    public get isAnswering(): boolean {
        return this._isAnswering;
    }
    
    /**
     * 是否正在显示结果
     */
    public get isShowingResult(): boolean {
        return this._isShowingResult;
    }
    
    /**
     * 当前题目
     */
    public get currentQuestion(): IQuestionData | null {
        return this._currentQuestion;
    }
    
    /**
     * 选中的答案索引
     */
    public get selectedAnswerIndex(): number {
        return this._selectedAnswerIndex;
    }
    
    /**
     * 音频播放次数
     */
    public get audioPlayCount(): number {
        return this.audioButton ? this.audioButton.playCount : 0;
    }
    
    /**
     * 是否可以播放音频
     */
    public get canPlayAudio(): boolean {
        return this.audioButton ? this.audioButton.canPlay : false;
    }
    
    // ==================== 清理 ====================
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        // 停止所有动画
        this.node.stopAllActions();
        
        // 清理回调函数
        this._onAnswerSelectedCallback = null;
        this._onAudioPlayRequestCallback = null;
        
        // 执行BaseComponent清理
        this.onCleanup();
    }
}