import { _decorator, Component, Node, Label, tween, Vec3, Color } from 'cc';
import { UI_CONFIG } from '../constants/GameConstants';

const { ccclass, property } = _decorator;

/**
 * 分数显示组件
 * 负责显示当前分数、连击数和分数变化动画
 */
@ccclass('ScoreDisplay')
export class ScoreDisplay extends Component {
    @property(Label)
    public scoreLabel: Label = null;
    
    @property(Label)
    public comboLabel: Label = null;
    
    @property(Node)
    public comboContainer: Node = null;
    
    @property(Label)
    public scoreChangeLabel: Label = null;
    
    // 分数数据
    private _currentScore: number = 0;
    private _currentCombo: number = 0;
    private _previousScore: number = 0;
    
    // 动画状态
    private _isAnimating: boolean = false;
    
    protected onLoad(): void {
        this.initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 初始化分数标签
        if (!this.scoreLabel) {
            const scoreLabelNode = this.node.getChildByName('ScoreLabel');
            if (scoreLabelNode) {
                this.scoreLabel = scoreLabelNode.getComponent(Label);
            }
        }
        
        // 初始化连击标签
        if (!this.comboLabel) {
            const comboLabelNode = this.node.getChildByName('ComboLabel');
            if (comboLabelNode) {
                this.comboLabel = comboLabelNode.getComponent(Label);
            }
        }
        
        // 初始化连击容器
        if (!this.comboContainer) {
            this.comboContainer = this.node.getChildByName('ComboContainer');
        }
        
        // 初始化分数变化标签
        if (!this.scoreChangeLabel) {
            const changeNode = this.node.getChildByName('ScoreChange');
            if (changeNode) {
                this.scoreChangeLabel = changeNode.getComponent(Label);
            }
        }
        
        // 设置初始状态
        this.setInitialState();
        
        console.log('[ScoreDisplay] 分数显示组件初始化完成');
    }
    
    /**
     * 设置初始状态
     */
    private setInitialState(): void {
        // 设置分数标签
        if (this.scoreLabel) {
            this.scoreLabel.string = '0';
            this.scoreLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.TEXT_PRIMARY);
        }
        
        // 隐藏连击容器
        if (this.comboContainer) {
            this.comboContainer.active = false;
        }
        
        // 隐藏分数变化标签
        if (this.scoreChangeLabel) {
            this.scoreChangeLabel.node.active = false;
        }
        
        // 重置数据
        this._currentScore = 0;
        this._currentCombo = 0;
        this._previousScore = 0;
    }
    
    /**
     * 更新分数
     */
    public updateScore(newScore: number, newCombo: number): void {
        const scoreIncrease = newScore - this._currentScore;
        
        this._previousScore = this._currentScore;
        this._currentScore = newScore;
        this._currentCombo = newCombo;
        
        // 更新分数显示
        this.updateScoreDisplay();
        
        // 更新连击显示
        this.updateComboDisplay();
        
        // 显示分数变化
        if (scoreIncrease > 0) {
            this.showScoreIncrease(scoreIncrease);
        }
        
        // 播放更新动画
        this.playUpdateAnimation();
        
        console.log(`[ScoreDisplay] 分数更新: ${newScore} (增加${scoreIncrease}), 连击: ${newCombo}`);
    }
    
    /**
     * 更新分数显示
     */
    private updateScoreDisplay(): void {
        if (!this.scoreLabel) return;
        
        // 数字动画效果
        this.animateNumberChange(this.scoreLabel, this._previousScore, this._currentScore);
    }
    
    /**
     * 更新连击显示
     */
    private updateComboDisplay(): void {
        if (!this.comboContainer || !this.comboLabel) return;
        
        // 连击数大于1时显示
        if (this._currentCombo > 1) {
            this.comboContainer.active = true;
            this.comboLabel.string = `${this._currentCombo}x`;
            
            // 设置连击数颜色
            if (this._currentCombo >= 5) {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR); // 红色
            } else if (this._currentCombo >= 3) {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY); // 橙色
            } else {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS); // 绿色
            }
            
            // 播放连击动画
            this.playComboAnimation();
        } else {
            // 隐藏连击显示
            this.comboContainer.active = false;
        }
    }
    
    /**
     * 显示分数增加
     */
    private showScoreIncrease(increase: number): void {
        if (!this.scoreChangeLabel) return;
        
        // 设置增加文本
        this.scoreChangeLabel.string = `+${increase}`;
        this.scoreChangeLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
        
        // 显示标签
        this.scoreChangeLabel.node.active = true;
        this.scoreChangeLabel.node.setScale(0, 0, 1);
        this.scoreChangeLabel.node.opacity = 255;
        
        // 播放分数增加动画
        this.playScoreIncreaseAnimation();
    }
    
    /**
     * 播放分数增加动画
     */
    private playScoreIncreaseAnimation(): void {
        if (!this.scoreChangeLabel) return;
        
        const changeNode = this.scoreChangeLabel.node;
        
        // 缩放 + 上升 + 淡出动画
        tween(changeNode)
            .to(0.2, { 
                scale: new Vec3(1.2, 1.2, 1),
                position: changeNode.position.clone().add3f(0, 50, 0)
            }, {
                easing: 'backOut'
            })
            .to(0.3, { 
                scale: new Vec3(1.0, 1.0, 1),
                position: changeNode.position.clone().add3f(0, 100, 0)
            })
            .to(0.5, { 
                opacity: 0,
                position: changeNode.position.clone().add3f(0, 150, 0)
            })
            .call(() => {
                changeNode.active = false;
                // 重置位置
                changeNode.position = changeNode.position.clone().subtract3f(0, 150, 0);
            })
            .start();
    }
    
    /**
     * 播放连击动画
     */
    private playComboAnimation(): void {
        if (!this.comboContainer) return;
        
        // 连击容器弹跳动画
        tween(this.comboContainer)
            .to(0.1, { scale: new Vec3(1.3, 1.3, 1) })
            .to(0.2, { scale: new Vec3(1.0, 1.0, 1) }, {
                easing: 'bounceOut'
            })
            .start();
        
        // 连击标签颜色脉冲效果
        if (this.comboLabel) {
            const originalColor = this.comboLabel.color.clone();
            const brightColor = originalColor.clone();
            brightColor.r = Math.min(255, brightColor.r + 50);
            brightColor.g = Math.min(255, brightColor.g + 50);
            brightColor.b = Math.min(255, brightColor.b + 50);
            
            tween(this.comboLabel)
                .to(0.2, { color: brightColor })
                .to(0.3, { color: originalColor })
                .start();
        }
    }
    
    /**
     * 播放更新动画
     */
    private playUpdateAnimation(): void {
        if (this._isAnimating) return;
        
        this._isAnimating = true;
        
        // 分数标签缩放动画
        if (this.scoreLabel) {
            tween(this.scoreLabel.node)
                .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.2, { scale: new Vec3(1.0, 1.0, 1) }, {
                    easing: 'backOut'
                })
                .call(() => {
                    this._isAnimating = false;
                })
                .start();
        }
    }
    
    /**
     * 数字变化动画
     */
    private animateNumberChange(label: Label, fromValue: number, toValue: number, duration: number = 0.5): void {
        if (!label || fromValue === toValue) return;
        
        const startTime = Date.now();
        const totalDuration = duration * 1000; // 转换为毫秒
        
        const updateNumber = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / totalDuration, 1);
            
            // 使用缓动函数
            const easedProgress = this.easeOutQuart(progress);
            const currentValue = Math.floor(fromValue + (toValue - fromValue) * easedProgress);
            
            label.string = currentValue.toString();
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                label.string = toValue.toString(); // 确保最终值正确
            }
        };
        
        updateNumber();
    }
    
    /**
     * 缓动函数 - easeOutQuart
     */
    private easeOutQuart(t: number): number {
        return 1 - Math.pow(1 - t, 4);
    }
    
    /**
     * 重置分数显示
     */
    public reset(): void {
        this._currentScore = 0;
        this._currentCombo = 0;
        this._previousScore = 0;
        this._isAnimating = false;
        
        // 重置显示
        if (this.scoreLabel) {
            this.scoreLabel.string = '0';
        }
        
        if (this.comboContainer) {
            this.comboContainer.active = false;
        }
        
        if (this.scoreChangeLabel) {
            this.scoreChangeLabel.node.active = false;
        }
        
        console.log('[ScoreDisplay] 分数显示已重置');
    }
    
    /**
     * 播放最终分数动画
     */
    public playFinalScoreAnimation(): void {
        // 分数标签发光动画
        if (this.scoreLabel) {
            const originalColor = this.scoreLabel.color.clone();
            const goldColor = new Color(255, 215, 0, 255); // 金色
            
            tween(this.scoreLabel)
                .to(0.5, { color: goldColor })
                .to(0.5, { color: originalColor })
                .to(0.5, { color: goldColor })
                .to(0.5, { color: originalColor })
                .start();
                
            // 分数标签缩放动画
            tween(this.scoreLabel.node)
                .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.4, { scale: new Vec3(1.0, 1.0, 1) }, {
                    easing: 'bounceOut'
                })
                .start();
        }
        
        // 整体容器动画
        tween(this.node)
            .to(0.2, { scale: new Vec3(1.05, 1.05, 1) })
            .to(0.3, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
        
        console.log('[ScoreDisplay] 播放最终分数动画');
    }
    
    /**
     * 设置分数（无动画）
     */
    public setScore(score: number): void {
        this._currentScore = score;
        
        if (this.scoreLabel) {
            this.scoreLabel.string = score.toString();
        }
    }
    
    /**
     * 设置连击数（无动画）
     */
    public setCombo(combo: number): void {
        this._currentCombo = combo;
        
        if (combo > 1) {
            if (this.comboContainer) {
                this.comboContainer.active = true;
            }
            if (this.comboLabel) {
                this.comboLabel.string = `${combo}x`;
            }
        } else {
            if (this.comboContainer) {
                this.comboContainer.active = false;
            }
        }
    }
    
    // ========== 属性访问器 ==========
    
    /**
     * 获取当前分数
     */
    public get currentScore(): number {
        return this._currentScore;
    }
    
    /**
     * 获取当前连击数
     */
    public get currentCombo(): number {
        return this._currentCombo;
    }
    
    /**
     * 是否正在播放动画
     */
    public get isAnimating(): boolean {
        return this._isAnimating;
    }
}