import { _decorator, Component, Node, Label, Button, Sprite, tween, Vec3, Color } from 'cc';
import { GameManager } from '../managers/GameManager';
import { EventManager } from '../managers/EventManager';
import { IGameSession } from '../data/GameData';
import { UI_CONFIG } from '../constants/GameConstants';

const { ccclass, property } = _decorator;

/**
 * 结果页面UI控制器
 * 负责游戏结果的显示和交互
 */
@ccclass('ResultUI')
export class ResultUI extends Component {
    @property(Label)
    public titleLabel: Label = null;
    
    @property(Label)
    public scoreLabel: Label = null;
    
    @property(Label)
    public accuracyLabel: Label = null;
    
    @property(Label)
    public correctCountLabel: Label = null;
    
    @property(Label)
    public comboLabel: Label = null;
    
    @property(Label)
    public timeLabel: Label = null;
    
    @property(Node)
    public starsContainer: Node = null;
    
    @property(Node)
    public achievementContainer: Node = null;
    
    @property(Button)
    public playAgainButton: Button = null;
    
    @property(Button)
    public shareButton: Button = null;
    
    @property(Button)
    public homeButton: Button = null;
    
    @property(Node)
    public resultPanel: Node = null;
    
    // 游戏会话数据
    private _gameSession: IGameSession = null;
    
    // 管理器引用
    private _gameManager: GameManager = null;
    private _eventManager: EventManager = null;
    
    // 动画状态
    private _isAnimating: boolean = false;
    
    protected onLoad(): void {
        this.initializeComponents();
        this.registerEventListeners();
    }
    
    protected onDestroy(): void {
        this.unregisterEventListeners();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 获取管理器引用
        this._gameManager = GameManager.instance;
        this._eventManager = EventManager.instance;
        
        // 注册按钮事件
        if (this.playAgainButton) {
            this.playAgainButton.node.on(Button.EventType.CLICK, this.onPlayAgainClick, this);
        }
        
        if (this.shareButton) {
            this.shareButton.node.on(Button.EventType.CLICK, this.onShareClick, this);
        }
        
        if (this.homeButton) {
            this.homeButton.node.on(Button.EventType.CLICK, this.onHomeClick, this);
        }
        
        // 设置初始状态
        this.setInitialState();
        
        console.log('[ResultUI] 结果页面UI初始化完成');
    }
    
    /**
     * 设置初始状态
     */
    private setInitialState(): void {
        // 隐藏结果面板
        if (this.resultPanel) {
            this.resultPanel.active = false;
        }
        
        // 隐藏星星容器
        if (this.starsContainer) {
            this.starsContainer.active = false;
        }
        
        // 隐藏成就容器
        if (this.achievementContainer) {
            this.achievementContainer.active = false;
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        if (!this._eventManager) return;
        
        // 监听游戏结束事件
        this._eventManager.on('game_ended', this.onGameEnded, this);
    }
    
    /**
     * 注销事件监听器
     */
    private unregisterEventListeners(): void {
        if (!this._eventManager) return;
        
        this._eventManager.targetOff(this);
    }
    
    /**
     * 显示游戏结果
     */
    public showResult(gameSession: IGameSession): void {
        this._gameSession = gameSession;
        
        console.log('[ResultUI] 显示游戏结果:', gameSession);
        
        // 显示结果面板
        if (this.resultPanel) {
            this.resultPanel.active = true;
        }
        
        // 更新结果数据
        this.updateResultData();
        
        // 播放结果显示动画
        this.playResultAnimation();
    }
    
    /**
     * 更新结果数据
     */
    private updateResultData(): void {
        if (!this._gameSession) return;
        
        const { 
            totalScore, 
            correctCount, 
            questions, 
            comboCount,
            startTime,
            endTime 
        } = this._gameSession;
        
        const totalQuestions = questions.length;
        const accuracy = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0;
        const gameTime = endTime ? Math.floor((endTime - startTime) / 1000) : 0;
        
        // 更新标题
        if (this.titleLabel) {
            let title = '游戏结束';
            if (accuracy >= 90) {
                title = '完美表现！';
            } else if (accuracy >= 70) {
                title = '表现不错！';
            } else if (accuracy >= 50) {
                title = '继续努力！';
            } else {
                title = '再接再厉！';
            }
            this.titleLabel.string = title;
        }
        
        // 更新分数
        if (this.scoreLabel) {
            this.scoreLabel.string = totalScore.toString();
        }
        
        // 更新准确率
        if (this.accuracyLabel) {
            this.accuracyLabel.string = `${accuracy.toFixed(1)}%`;
            // 根据准确率设置颜色
            if (accuracy >= 80) {
                this.accuracyLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            } else if (accuracy >= 60) {
                this.accuracyLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY);
            } else {
                this.accuracyLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
            }
        }
        
        // 更新正确题数
        if (this.correctCountLabel) {
            this.correctCountLabel.string = `${correctCount}/${totalQuestions}`;
        }
        
        // 更新最高连击
        if (this.comboLabel) {
            this.comboLabel.string = `${comboCount}`;
            if (comboCount >= 5) {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
            } else if (comboCount >= 3) {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY);
            } else {
                this.comboLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            }
        }
        
        // 更新游戏时间
        if (this.timeLabel) {
            const minutes = Math.floor(gameTime / 60);
            const seconds = gameTime % 60;
            this.timeLabel.string = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 更新星级评价
        this.updateStarRating(accuracy);
        
        // 检查并显示成就
        this.checkAndShowAchievements();
    }
    
    /**
     * 更新星级评价
     */
    private updateStarRating(accuracy: number): void {
        if (!this.starsContainer) return;
        
        // 计算星级 (1-3星)
        let starCount = 1;
        if (accuracy >= 90) {
            starCount = 3;
        } else if (accuracy >= 70) {
            starCount = 2;
        }
        
        // 显示星星容器
        this.starsContainer.active = true;
        
        // 更新星星显示
        const stars = this.starsContainer.children;
        for (let i = 0; i < stars.length && i < 3; i++) {
            const star = stars[i];
            const sprite = star.getComponent(Sprite);
            
            if (i < starCount) {
                // 点亮星星
                if (sprite) {
                    sprite.color = new Color().fromHEX('#FFD700'); // 金色
                }
                
                // 播放星星点亮动画
                this.scheduleOnce(() => {
                    this.playStarAnimation(star, i);
                }, i * 0.3);
            } else {
                // 未点亮星星
                if (sprite) {
                    sprite.color = new Color(128, 128, 128, 255); // 灰色
                }
            }
        }
        
        console.log(`[ResultUI] 星级评价: ${starCount}星 (准确率: ${accuracy.toFixed(1)}%)`);
    }
    
    /**
     * 播放星星动画
     */
    private playStarAnimation(starNode: Node, index: number): void {
        // 初始状态
        starNode.setScale(0, 0, 1);
        starNode.opacity = 0;
        
        // 缩放 + 淡入动画
        tween(starNode)
            .to(0.3, { 
                scale: new Vec3(1.2, 1.2, 1),
                opacity: 255
            }, {
                easing: 'backOut'
            })
            .to(0.2, { 
                scale: new Vec3(1.0, 1.0, 1)
            })
            .start();
        
        // 旋转动画
        tween(starNode)
            .delay(0.2)
            .by(0.5, { angle: 360 })
            .start();
    }
    
    /**
     * 检查并显示成就
     */
    private checkAndShowAchievements(): void {
        if (!this._gameSession || !this.achievementContainer) return;
        
        const achievements: string[] = [];
        const { totalScore, correctCount, questions, comboCount } = this._gameSession;
        const accuracy = questions.length > 0 ? (correctCount / questions.length) * 100 : 0;
        
        // 检查各种成就
        if (accuracy === 100) {
            achievements.push('完美答题');
        }
        
        if (totalScore >= 300) {
            achievements.push('高分达人');
        }
        
        if (comboCount >= 5) {
            achievements.push('连击高手');
        }
        
        // 如果有成就，显示成就容器
        if (achievements.length > 0) {
            this.achievementContainer.active = true;
            this.showAchievements(achievements);
        }
    }
    
    /**
     * 显示成就
     */
    private showAchievements(achievements: string[]): void {
        console.log('[ResultUI] 获得成就:', achievements);
        
        // 这里可以创建成就UI元素并播放动画
        // 由于没有具体的成就UI预制体，这里只是演示逻辑
        
        // 播放成就获得动画
        if (this.achievementContainer) {
            this.achievementContainer.setScale(0, 0, 1);
            
            tween(this.achievementContainer)
                .delay(1.0) // 延迟1秒显示
                .to(0.4, { scale: new Vec3(1, 1, 1) }, {
                    easing: 'bounceOut'
                })
                .start();
        }
    }
    
    /**
     * 播放结果显示动画
     */
    private playResultAnimation(): void {
        if (this._isAnimating) return;
        
        this._isAnimating = true;
        
        // 结果面板淡入动画
        if (this.resultPanel) {
            this.resultPanel.opacity = 0;
            this.resultPanel.setScale(0.8, 0.8, 1);
            
            tween(this.resultPanel)
                .to(0.5, { 
                    opacity: 255,
                    scale: new Vec3(1, 1, 1)
                }, {
                    easing: 'backOut'
                })
                .start();
        }
        
        // 分数数字动画
        if (this.scoreLabel && this._gameSession) {
            this.animateScore(0, this._gameSession.totalScore, 1.0);
        }
        
        // 按钮依次出现动画
        const buttons = [this.playAgainButton, this.shareButton, this.homeButton];
        buttons.forEach((button, index) => {
            if (button) {
                button.node.opacity = 0;
                button.node.setScale(0.8, 0.8, 1);
                
                tween(button.node)
                    .delay(0.8 + index * 0.2)
                    .to(0.3, { 
                        opacity: 255,
                        scale: new Vec3(1, 1, 1)
                    }, {
                        easing: 'backOut'
                    })
                    .start();
            }
        });
        
        this.scheduleOnce(() => {
            this._isAnimating = false;
        }, 2.0);
    }
    
    /**
     * 分数动画
     */
    private animateScore(fromScore: number, toScore: number, duration: number): void {
        if (!this.scoreLabel) return;
        
        const startTime = Date.now();
        const totalDuration = duration * 1000;
        
        const updateScore = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / totalDuration, 1);
            
            const easedProgress = this.easeOutQuart(progress);
            const currentScore = Math.floor(fromScore + (toScore - fromScore) * easedProgress);
            
            this.scoreLabel.string = currentScore.toString();
            
            if (progress < 1) {
                requestAnimationFrame(updateScore);
            } else {
                this.scoreLabel.string = toScore.toString();
            }
        };
        
        updateScore();
    }
    
    /**
     * 缓动函数
     */
    private easeOutQuart(t: number): number {
        return 1 - Math.pow(1 - t, 4);
    }
    
    /**
     * 生成分享内容
     */
    private generateShareContent(): { title: string, imageUrl: string, query: string } {
        if (!this._gameSession) {
            return {
                title: '家乡话猜猜猜',
                imageUrl: '',
                query: ''
            };
        }
        
        const { totalScore, correctCount, questions } = this._gameSession;
        const accuracy = questions.length > 0 ? (correctCount / questions.length) * 100 : 0;
        
        let title = `我在家乡话猜猜猜中得了${totalScore}分！`;
        if (accuracy >= 90) {
            title += '简直是方言大师！';
        } else if (accuracy >= 70) {
            title += '表现不错哦！';
        }
        
        return {
            title: title,
            imageUrl: 'https://your-domain.com/share-image.jpg', // 分享图片URL
            query: `score=${totalScore}&accuracy=${accuracy.toFixed(1)}`
        };
    }
    
    // ========== 事件处理器 ==========
    
    /**
     * 游戏结束事件处理
     */
    private onGameEnded(event: any): void {
        const { session } = event;
        this.showResult(session);
    }
    
    /**
     * 再玩一次按钮点击
     */
    private onPlayAgainClick(): void {
        console.log('[ResultUI] 再玩一次按钮被点击');
        
        // 播放按钮点击动画
        this.playButtonClickAnimation(this.playAgainButton.node);
        
        // 延迟重新开始游戏
        this.scheduleOnce(() => {
            if (this._gameManager) {
                this._gameManager.restartGame();
            }
        }, 0.2);
    }
    
    /**
     * 分享按钮点击
     */
    private onShareClick(): void {
        console.log('[ResultUI] 分享按钮被点击');
        
        // 播放按钮点击动画
        this.playButtonClickAnimation(this.shareButton.node);
        
        // 生成分享内容
        const shareContent = this.generateShareContent();
        
        // 调用微信分享API
        if (typeof wx !== 'undefined' && wx.shareAppMessage) {
            wx.shareAppMessage({
                title: shareContent.title,
                imageUrl: shareContent.imageUrl,
                query: shareContent.query,
                success: () => {
                    console.log('[ResultUI] 分享成功');
                },
                fail: (error) => {
                    console.error('[ResultUI] 分享失败:', error);
                }
            });
        } else {
            console.log('[ResultUI] 非微信环境，无法分享');
        }
    }
    
    /**
     * 返回主页按钮点击
     */
    private onHomeClick(): void {
        console.log('[ResultUI] 返回主页按钮被点击');
        
        // 播放按钮点击动画
        this.playButtonClickAnimation(this.homeButton.node);
        
        // 延迟返回主页
        this.scheduleOnce(() => {
            if (this._gameManager) {
                this._gameManager.quitGame();
            }
        }, 0.2);
    }
    
    /**
     * 播放按钮点击动画
     */
    private playButtonClickAnimation(buttonNode: Node): void {
        if (!buttonNode) return;
        
        tween(buttonNode)
            .to(0.1, { scale: new Vec3(0.95, 0.95, 1) })
            .to(0.1, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
    }
    
    // ========== 公共接口 ==========
    
    /**
     * 隐藏结果页面
     */
    public hide(): void {
        if (this.resultPanel) {
            this.resultPanel.active = false;
        }
        
        this._gameSession = null;
        this._isAnimating = false;
        
        console.log('[ResultUI] 结果页面已隐藏');
    }
    
    /**
     * 获取当前游戏会话
     */
    public get gameSession(): IGameSession | null {
        return this._gameSession;
    }
}