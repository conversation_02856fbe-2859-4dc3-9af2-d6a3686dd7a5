import { _decorator, Component, Label, RichText } from 'cc';
import { LocalizationManager, SupportedLanguage, IInterpolationParams } from '../i18n/LocalizationManager';
import { UISafetyHelper } from '../utils/UISafetyHelper';

const { ccclass, property } = _decorator;

/**
 * 本地化标签组件
 * 自动根据当前语言显示对应的文本
 */
@ccclass('LocalizedLabel')
export class LocalizedLabel extends Component {
    @property({
        tooltip: '翻译键，用于查找对应的翻译文本'
    })
    public translationKey: string = '';
    
    @property({
        tooltip: '插值参数，用于动态替换文本中的占位符'
    })
    public interpolationParams: string = '{}';
    
    @property({
        tooltip: '复数数量，用于复数形式的翻译'
    })
    public pluralCount: number = -1;
    
    @property({
        tooltip: '是否自动更新文本'
    })
    public autoUpdate: boolean = true;
    
    @property({
        tooltip: '默认文本，当翻译不存在时显示'
    })
    public fallbackText: string = '';
    
    @property({
        tooltip: '是否使用富文本格式'
    })
    public useRichText: boolean = false;
    
    // 组件引用
    private _label: Label | null = null;
    private _richText: RichText | null = null;
    private _localizationManager: LocalizationManager | null = null;
    
    // 当前显示的文本
    private _currentText: string = '';
    private _currentLanguage: SupportedLanguage | null = null;
    
    protected onLoad(): void {
        try {
            this._localizationManager = LocalizationManager.getInstance();

            // 获取文本组件
            this._label = this.getComponent(Label);
            this._richText = this.getComponent(RichText);

            if (!this._label && !this._richText) {
                console.warn('[LocalizedLabel] 未找到Label或RichText组件');
                return;
            }

            // 检查Label组件的字体设置
            if (this._label && !this._label.font) {
                console.warn('[LocalizedLabel] Label组件缺少字体设置，使用默认字体');
                // 这里可以设置一个默认字体，避免渲染错误
            }

            // 注册语言变更监听
            if (this._localizationManager && this.autoUpdate) {
                this.registerLanguageChangeListener();
            }

            // 延迟初始化文本，确保所有组件都已准备好
            this.scheduleOnce(() => {
                this.updateText();
            }, 0.1);

        } catch (error) {
            console.error('[LocalizedLabel] 初始化失败:', error);
        }
    }
    
    protected onDestroy(): void {
        this.unregisterLanguageChangeListener();
    }
    
    /**
     * 设置翻译键
     */
    public setTranslationKey(key: string): void {
        if (this.translationKey !== key) {
            this.translationKey = key;
            this.updateText();
        }
    }
    
    /**
     * 设置插值参数
     */
    public setInterpolationParams(params: IInterpolationParams | string): void {
        const paramString = typeof params === 'string' ? params : JSON.stringify(params);
        if (this.interpolationParams !== paramString) {
            this.interpolationParams = paramString;
            this.updateText();
        }
    }
    
    /**
     * 设置复数数量
     */
    public setPluralCount(count: number): void {
        if (this.pluralCount !== count) {
            this.pluralCount = count;
            this.updateText();
        }
    }
    
    /**
     * 设置回退文本
     */
    public setFallbackText(text: string): void {
        if (this.fallbackText !== text) {
            this.fallbackText = text;
            this.updateText();
        }
    }
    
    /**
     * 手动更新文本
     */
    public updateText(): void {
        if (!this._localizationManager) {
            this.setDisplayText(this.fallbackText || this.translationKey);
            return;
        }
        
        try {
            // 解析插值参数
            let params: IInterpolationParams = {};
            if (this.interpolationParams && this.interpolationParams.trim() !== '{}') {
                try {
                    params = JSON.parse(this.interpolationParams);
                } catch (error) {
                    console.warn('[LocalizedLabel] 插值参数解析失败:', this.interpolationParams);
                }
            }
            
            // 获取翻译文本
            const count = this.pluralCount >= 0 ? this.pluralCount : undefined;
            let translatedText = this._localizationManager.getText(this.translationKey, params, count);
            
            // 如果翻译失败且有回退文本，使用回退文本
            if (translatedText === this.translationKey && this.fallbackText) {
                translatedText = this.fallbackText;
            }
            
            // 更新显示文本
            this.setDisplayText(translatedText);
            
            // 记录当前状态
            this._currentText = translatedText;
            this._currentLanguage = this._localizationManager.getCurrentLanguage();
            
        } catch (error) {
            console.error('[LocalizedLabel] 更新文本失败:', error);
            this.setDisplayText(this.fallbackText || this.translationKey);
        }
    }
    
    /**
     * 获取当前显示的文本
     */
    public getCurrentText(): string {
        return this._currentText;
    }
    
    /**
     * 检查是否需要更新
     */
    public needsUpdate(): boolean {
        if (!this._localizationManager) {
            return false;
        }
        
        return this._currentLanguage !== this._localizationManager.getCurrentLanguage();
    }
    
    /**
     * 强制刷新
     */
    public forceRefresh(): void {
        this._currentLanguage = null;
        this.updateText();
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 设置显示文本
     */
    private setDisplayText(text: string): void {
        try {
            if (this.useRichText && this._richText) {
                this._richText.string = text;
            } else if (this._label) {
                // 使用安全的文本设置方法
                const success = UISafetyHelper.safeSetLabelText(
                    this._label,
                    text,
                    this.fallbackText || text
                );

                if (!success) {
                    console.warn('[LocalizedLabel] 安全文本设置失败，尝试直接设置');
                    // 最后的尝试：直接设置
                    this._label.string = text;
                }
            }
        } catch (error) {
            console.error('[LocalizedLabel] 设置显示文本失败:', error);
            // 尝试设置fallback文本
            if (this.fallbackText && this._label) {
                try {
                    this._label.string = this.fallbackText;
                } catch (fallbackError) {
                    console.error('[LocalizedLabel] 设置fallback文本也失败:', fallbackError);
                }
            }
        }
    }
    
    /**
     * 注册语言变更监听
     */
    private registerLanguageChangeListener(): void {
        // 这里应该监听LocalizationManager的语言变更事件
        // 由于事件系统的实现，这里使用定时检查作为临时方案
        this.schedule(this.checkLanguageChange, 1.0);
    }
    
    /**
     * 取消语言变更监听
     */
    private unregisterLanguageChangeListener(): void {
        this.unschedule(this.checkLanguageChange);
    }
    
    /**
     * 检查语言变更
     */
    private checkLanguageChange(): void {
        if (this.needsUpdate()) {
            this.updateText();
        }
    }
}


