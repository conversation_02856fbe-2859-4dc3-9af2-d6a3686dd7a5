import { _decorator, Component, Node, Prefab, instantiate, Layout, ScrollView, Button, Label } from 'cc';
import { LocalizationManager, SupportedLanguage, ILanguageInfo } from '../i18n/LocalizationManager';
import { LocalizedLabel } from './LocalizedLabel';
import { LanguageItem } from './LanguageItem';

const { ccclass, property } = _decorator;

/**
 * 语言选择面板
 * 提供语言切换功能的UI界面
 */
@ccclass('LanguageSelectionPanel')
export class LanguageSelectionPanel extends Component {
    @property({
        type: Prefab,
        tooltip: '语言选项预制体'
    })
    public languageItemPrefab: Prefab = null;
    
    @property({
        type: Node,
        tooltip: '语言列表容器'
    })
    public languageListContainer: Node = null;
    
    @property({
        type: ScrollView,
        tooltip: '滚动视图组件'
    })
    public scrollView: ScrollView = null;
    
    @property({
        type: Button,
        tooltip: '确认按钮'
    })
    public confirmButton: Button = null;
    
    @property({
        type: Button,
        tooltip: '取消按钮'
    })
    public cancelButton: Button = null;
    
    @property({
        type: LocalizedLabel,
        tooltip: '标题标签'
    })
    public titleLabel: LocalizedLabel = null;
    
    @property({
        type: LocalizedLabel,
        tooltip: '描述标签'
    })
    public descriptionLabel: LocalizedLabel = null;
    
    @property({
        tooltip: '是否显示语言完成度'
    })
    public showCompleteness: boolean = true;
    
    @property({
        tooltip: '是否显示原生名称'
    })
    public showNativeName: boolean = true;
    
    @property({
        tooltip: '是否只显示支持的语言'
    })
    public onlySupportedLanguages: boolean = true;
    
    // 语言管理器
    private _localizationManager: LocalizationManager = null;
    
    // 当前选中的语言
    private _selectedLanguage: SupportedLanguage = null;
    private _originalLanguage: SupportedLanguage = null;
    
    // 语言选项列表
    private _languageItems: LanguageItem[] = [];
    
    // 回调函数
    private _onLanguageChanged: (language: SupportedLanguage) => void = null;
    private _onCancelled: () => void = null;
    
    protected onLoad(): void {
        this._localizationManager = LocalizationManager.getInstance();
        
        if (this._localizationManager) {
            this._originalLanguage = this._localizationManager.getCurrentLanguage();
            this._selectedLanguage = this._originalLanguage;
        }
        
        this.setupUI();
        this.setupEventListeners();
        this.loadLanguageList();
    }
    
    /**
     * 显示语言选择面板
     */
    public show(onLanguageChanged?: (language: SupportedLanguage) => void, onCancelled?: () => void): void {
        this._onLanguageChanged = onLanguageChanged;
        this._onCancelled = onCancelled;
        
        this.node.active = true;
        this.refreshLanguageList();
    }
    
    /**
     * 隐藏语言选择面板
     */
    public hide(): void {
        this.node.active = false;
    }
    
    /**
     * 设置选中的语言
     */
    public setSelectedLanguage(language: SupportedLanguage): void {
        if (this._selectedLanguage !== language) {
            this._selectedLanguage = language;
            this.updateLanguageSelection();
        }
    }
    
    /**
     * 获取选中的语言
     */
    public getSelectedLanguage(): SupportedLanguage {
        return this._selectedLanguage;
    }
    
    /**
     * 刷新语言列表
     */
    public refreshLanguageList(): void {
        this.clearLanguageList();
        this.loadLanguageList();
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 设置UI
     */
    private setupUI(): void {
        // 设置标题和描述
        if (this.titleLabel) {
            this.titleLabel.setTranslationKey('settings.language.title');
        }
        
        if (this.descriptionLabel) {
            this.descriptionLabel.setTranslationKey('settings.language.description');
        }
        
        // 设置按钮文本
        if (this.confirmButton) {
            const confirmLabel = this.confirmButton.getComponentInChildren(LocalizedLabel);
            if (confirmLabel) {
                confirmLabel.setTranslationKey('common.confirm');
            }
        }
        
        if (this.cancelButton) {
            const cancelLabel = this.cancelButton.getComponentInChildren(LocalizedLabel);
            if (cancelLabel) {
                cancelLabel.setTranslationKey('common.cancel');
            }
        }
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (this.confirmButton) {
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmClicked, this);
        }
        
        if (this.cancelButton) {
            this.cancelButton.node.on(Button.EventType.CLICK, this.onCancelClicked, this);
        }
    }
    
    /**
     * 加载语言列表
     */
    private loadLanguageList(): void {
        if (!this._localizationManager || !this.languageItemPrefab || !this.languageListContainer) {
            console.warn('[LanguageSelectionPanel] 缺少必要组件');
            return;
        }
        
        const languages = this._localizationManager.getSupportedLanguages();
        const filteredLanguages = this.onlySupportedLanguages 
            ? languages.filter(lang => lang.isSupported)
            : languages;
        
        // 按名称排序
        filteredLanguages.sort((a, b) => a.name.localeCompare(b.name));
        
        for (const languageInfo of filteredLanguages) {
            this.createLanguageItem(languageInfo);
        }
        
        // 更新布局
        const layout = this.languageListContainer.getComponent(Layout);
        if (layout) {
            layout.updateLayout();
        }
        
        // 更新选中状态
        this.updateLanguageSelection();
    }
    
    /**
     * 创建语言选项
     */
    private createLanguageItem(languageInfo: ILanguageInfo): void {
        const itemNode = instantiate(this.languageItemPrefab);
        if (!itemNode) {
            console.error('[LanguageSelectionPanel] 创建语言选项失败');
            return;
        }
        
        // 添加到容器
        this.languageListContainer.addChild(itemNode);
        
        // 创建语言选项组件
        const languageItem = itemNode.addComponent(LanguageItem);
        languageItem.initialize(languageInfo, this.showNativeName, this.showCompleteness);
        languageItem.setSelectionCallback((language) => {
            this.setSelectedLanguage(language);
        });
        
        this._languageItems.push(languageItem);
    }
    
    /**
     * 清空语言列表
     */
    private clearLanguageList(): void {
        this._languageItems = [];
        
        if (this.languageListContainer) {
            this.languageListContainer.removeAllChildren();
        }
    }
    
    /**
     * 更新语言选择状态
     */
    private updateLanguageSelection(): void {
        for (const item of this._languageItems) {
            item.setSelected(item.getLanguageCode() === this._selectedLanguage);
        }
    }
    
    /**
     * 确认按钮点击事件
     */
    private async onConfirmClicked(): Promise<void> {
        if (this._selectedLanguage && this._selectedLanguage !== this._originalLanguage) {
            // 切换语言
            const success = await this._localizationManager.setLanguage(this._selectedLanguage);
            
            if (success) {
                console.log(`[LanguageSelectionPanel] 语言已切换到: ${this._selectedLanguage}`);
                
                // 调用回调
                if (this._onLanguageChanged) {
                    this._onLanguageChanged(this._selectedLanguage);
                }
            } else {
                console.error('[LanguageSelectionPanel] 语言切换失败');
                // 这里可以显示错误提示
                return;
            }
        }
        
        this.hide();
    }
    
    /**
     * 取消按钮点击事件
     */
    private onCancelClicked(): void {
        // 恢复原始选择
        this._selectedLanguage = this._originalLanguage;
        
        // 调用回调
        if (this._onCancelled) {
            this._onCancelled();
        }
        
        this.hide();
    }
}


