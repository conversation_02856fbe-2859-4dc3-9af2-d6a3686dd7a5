import { _decorator, Component, Label } from 'cc';
import { LocalizedLabel } from './LocalizedLabel';
import { LocalizationManager } from '../i18n/LocalizationManager';

const { ccclass, property } = _decorator;

/**
 * 本地化按钮组件
 * 自动本地化按钮文本
 */
@ccclass('LocalizedButton')
export class LocalizedButton extends Component {
    @property({
        tooltip: '按钮文本的翻译键'
    })
    public buttonTextKey: string = '';
    
    @property({
        tooltip: '按钮提示文本的翻译键'
    })
    public tooltipTextKey: string = '';
    
    @property({
        tooltip: '是否自动查找子节点中的Label组件'
    })
    public autoFindLabel: boolean = true;
    
    // 组件引用
    private _localizedLabel: LocalizedLabel | null = null;
    private _localizationManager: LocalizationManager | null = null;
    
    protected onLoad(): void {
        this._localizationManager = LocalizationManager.getInstance();
        
        // 查找或创建LocalizedLabel组件
        if (this.autoFindLabel) {
            this.setupLocalizedLabel();
        }
        
        this.updateButtonText();
    }
    
    /**
     * 设置按钮文本键
     */
    public setButtonTextKey(key: string): void {
        if (this.buttonTextKey !== key) {
            this.buttonTextKey = key;
            this.updateButtonText();
        }
    }
    
    /**
     * 设置提示文本键
     */
    public setTooltipTextKey(key: string): void {
        if (this.tooltipTextKey !== key) {
            this.tooltipTextKey = key;
            this.updateTooltipText();
        }
    }
    
    /**
     * 更新按钮文本
     */
    public updateButtonText(): void {
        if (this._localizedLabel && this.buttonTextKey) {
            this._localizedLabel.setTranslationKey(this.buttonTextKey);
        }
    }
    
    /**
     * 更新提示文本
     */
    public updateTooltipText(): void {
        if (this.tooltipTextKey && this._localizationManager) {
            const tooltipText = this._localizationManager.getText(this.tooltipTextKey);
            // 这里可以设置按钮的提示文本
            console.log(`[LocalizedButton] 提示文本: ${tooltipText}`);
        }
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 设置本地化标签
     */
    private setupLocalizedLabel(): void {
        // 查找现有的LocalizedLabel组件
        this._localizedLabel = this.node.getComponentInChildren(LocalizedLabel);
        
        if (!this._localizedLabel) {
            // 查找Label组件并添加LocalizedLabel
            const label = this.node.getComponentInChildren(Label);
            if (label) {
                this._localizedLabel = label.node.addComponent(LocalizedLabel);
            }
        }
        
        if (this._localizedLabel) {
            this._localizedLabel.autoUpdate = true;
        }
    }
}
