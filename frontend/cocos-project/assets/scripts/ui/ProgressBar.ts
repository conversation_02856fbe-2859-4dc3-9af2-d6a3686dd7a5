import { _decorator, Component, Node, Sprite, Label, ProgressBar as CocosProgressBar, tween, Vec3, Color } from 'cc';
import { UI_CONFIG } from '../constants/GameConstants';

const { ccclass, property } = _decorator;

/**
 * 进度条组件
 * 负责显示游戏进度和倒计时
 */
@ccclass('ProgressBar')
export class ProgressBar extends Component {
    @property(CocosProgressBar)
    public gameProgressBar: CocosProgressBar = null;
    
    @property(CocosProgressBar)
    public timeProgressBar: CocosProgressBar = null;
    
    @property(Label)
    public progressLabel: Label = null;
    
    @property(Label)
    public timeLabel: Label = null;
    
    // 状态数据
    private _gameProgress: number = 0;
    private _timeProgress: number = 1.0;
    private _timeLeft: number = 30;
    private _maxTime: number = 30;
    
    protected onLoad(): void {
        this.initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 初始化游戏进度条
        if (!this.gameProgressBar) {
            const gameProgressNode = this.node.getChildByName('GameProgress');
            if (gameProgressNode) {
                this.gameProgressBar = gameProgressNode.getComponent(CocosProgressBar);
            }
        }
        
        // 初始化时间进度条
        if (!this.timeProgressBar) {
            const timeProgressNode = this.node.getChildByName('TimeProgress');
            if (timeProgressNode) {
                this.timeProgressBar = timeProgressNode.getComponent(CocosProgressBar);
            }
        }
        
        // 初始化标签
        if (!this.progressLabel) {
            const labelNode = this.node.getChildByName('ProgressLabel');
            if (labelNode) {
                this.progressLabel = labelNode.getComponent(Label);
            }
        }
        
        if (!this.timeLabel) {
            const timeNode = this.node.getChildByName('TimeLabel');
            if (timeNode) {
                this.timeLabel = timeNode.getComponent(Label);
            }
        }
        
        // 设置初始状态
        this.setInitialState();
        
        console.log('[ProgressBar] 进度条组件初始化完成');
    }
    
    /**
     * 设置初始状态
     */
    private setInitialState(): void {
        // 设置游戏进度条颜色
        if (this.gameProgressBar) {
            this.gameProgressBar.progress = 0;
            // 设置进度条颜色为主题色
            const barSprite = this.gameProgressBar.barSprite;
            if (barSprite) {
                barSprite.color = new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY);
            }
        }
        
        // 设置时间进度条颜色
        if (this.timeProgressBar) {
            this.timeProgressBar.progress = 1.0;
            // 设置进度条颜色为绿色
            const barSprite = this.timeProgressBar.barSprite;
            if (barSprite) {
                barSprite.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            }
        }
        
        // 更新标签
        this.updateProgressLabel();
        this.updateTimeLabel();
    }
    
    /**
     * 设置游戏进度
     */
    public setProgress(progress: number): void {
        const clampedProgress = Math.max(0, Math.min(1, progress));
        
        if (this._gameProgress === clampedProgress) return;
        
        this._gameProgress = clampedProgress;
        
        // 更新进度条
        if (this.gameProgressBar) {
            tween(this.gameProgressBar)
                .to(UI_CONFIG.ANIMATIONS.SLIDE_DURATION / 1000, {
                    progress: clampedProgress
                })
                .start();
        }
        
        // 更新标签
        this.updateProgressLabel();
        
        // 播放进度更新动画
        this.playProgressUpdateAnimation();
        
        console.log(`[ProgressBar] 游戏进度更新: ${(clampedProgress * 100).toFixed(1)}%`);
    }
    
    /**
     * 设置剩余时间
     */
    public setTimeLeft(timeLeft: number): void {
        this._timeLeft = Math.max(0, timeLeft);
        this._timeProgress = this._maxTime > 0 ? this._timeLeft / this._maxTime : 0;
        
        // 更新时间进度条
        if (this.timeProgressBar) {
            this.timeProgressBar.progress = this._timeProgress;
            
            // 根据剩余时间改变颜色
            const barSprite = this.timeProgressBar.barSprite;
            if (barSprite) {
                let color: Color;
                if (this._timeProgress > 0.5) {
                    // 绿色
                    color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
                } else if (this._timeProgress > 0.2) {
                    // 橙色
                    color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY);
                } else {
                    // 红色
                    color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
                }
                
                tween(barSprite)
                    .to(0.2, { color: color })
                    .start();
            }
        }
        
        // 更新时间标签
        this.updateTimeLabel();
        
        // 时间紧急提醒动画
        if (this._timeLeft <= 10 && this._timeLeft > 0) {
            this.playUrgentAnimation();
        }
        
        console.log(`[ProgressBar] 剩余时间: ${this._timeLeft}秒`);
    }
    
    /**
     * 设置最大时间
     */
    public setMaxTime(maxTime: number): void {
        this._maxTime = Math.max(1, maxTime);
        this._timeProgress = this._timeLeft / this._maxTime;
        
        // 更新时间进度条
        if (this.timeProgressBar) {
            this.timeProgressBar.progress = this._timeProgress;
        }
        
        console.log(`[ProgressBar] 最大时间设置: ${this._maxTime}秒`);
    }
    
    /**
     * 重置进度条
     */
    public reset(): void {
        this._gameProgress = 0;
        this._timeProgress = 1.0;
        this._timeLeft = this._maxTime;
        
        // 重置进度条
        if (this.gameProgressBar) {
            this.gameProgressBar.progress = 0;
        }
        
        if (this.timeProgressBar) {
            this.timeProgressBar.progress = 1.0;
            const barSprite = this.timeProgressBar.barSprite;
            if (barSprite) {
                barSprite.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            }
        }
        
        // 更新标签
        this.updateProgressLabel();
        this.updateTimeLabel();
        
        console.log('[ProgressBar] 进度条已重置');
    }
    
    /**
     * 更新游戏进度标签
     */
    private updateProgressLabel(): void {
        if (this.progressLabel) {
            const percentage = Math.round(this._gameProgress * 100);
            this.progressLabel.string = `${percentage}%`;
        }
    }
    
    /**
     * 更新时间标签
     */
    private updateTimeLabel(): void {
        if (this.timeLabel) {
            const minutes = Math.floor(this._timeLeft / 60);
            const seconds = this._timeLeft % 60;
            this.timeLabel.string = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // 设置标签颜色
            if (this._timeLeft <= 10) {
                this.timeLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
            } else if (this._timeLeft <= 30) {
                this.timeLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY);
            } else {
                this.timeLabel.color = new Color().fromHEX(UI_CONFIG.COLORS.TEXT_PRIMARY);
            }
        }
    }
    
    /**
     * 播放进度更新动画
     */
    private playProgressUpdateAnimation(): void {
        if (!this.gameProgressBar || !this.gameProgressBar.node) return;
        
        // 进度条缩放动画
        tween(this.gameProgressBar.node)
            .to(0.1, { scale: new Vec3(1.02, 1.05, 1) })
            .to(0.2, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
    }
    
    /**
     * 播放紧急时间动画
     */
    private playUrgentAnimation(): void {
        // 时间标签闪烁动画
        if (this.timeLabel) {
            tween(this.timeLabel.node)
                .to(0.3, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.3, { scale: new Vec3(1.0, 1.0, 1) })
                .start();
        }
        
        // 时间进度条震动
        if (this.timeProgressBar) {
            const originalPosition = this.timeProgressBar.node.position.clone();
            
            tween(this.timeProgressBar.node)
                .repeat(2,
                    tween(this.timeProgressBar.node)
                        .by(0.1, { position: new Vec3(2, 0, 0) })
                        .by(0.1, { position: new Vec3(-4, 0, 0) })
                        .by(0.1, { position: new Vec3(2, 0, 0) })
                )
                .call(() => {
                    this.timeProgressBar.node.position = originalPosition;
                })
                .start();
        }
    }
    
    /**
     * 播放完成动画
     */
    public playCompleteAnimation(): void {
        if (!this.gameProgressBar) return;
        
        // 进度条发光动画
        const barSprite = this.gameProgressBar.barSprite;
        if (barSprite) {
            const originalColor = barSprite.color.clone();
            const brightColor = originalColor.clone();
            brightColor.r = Math.min(255, brightColor.r + 50);
            brightColor.g = Math.min(255, brightColor.g + 50);
            brightColor.b = Math.min(255, brightColor.b + 50);
            
            tween(barSprite)
                .to(0.3, { color: brightColor })
                .to(0.3, { color: originalColor })
                .to(0.3, { color: brightColor })
                .to(0.3, { color: originalColor })
                .start();
        }
        
        // 整体缩放动画
        tween(this.node)
            .to(0.2, { scale: new Vec3(1.05, 1.05, 1) })
            .to(0.3, { scale: new Vec3(1.0, 1.0, 1) })
            .start();
        
        console.log('[ProgressBar] 播放完成动画');
    }
    
    // ========== 属性访问器 ==========
    
    /**
     * 获取游戏进度
     */
    public get gameProgress(): number {
        return this._gameProgress;
    }
    
    /**
     * 获取时间进度
     */
    public get timeProgress(): number {
        return this._timeProgress;
    }
    
    /**
     * 获取剩余时间
     */
    public get timeLeft(): number {
        return this._timeLeft;
    }
}