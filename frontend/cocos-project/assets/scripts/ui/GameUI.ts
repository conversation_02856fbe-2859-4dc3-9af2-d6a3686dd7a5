import { _decorator, Component, Node, Label, Button, Sprite, Prefab, instantiate, Layout, Color, tween, Vec3 } from 'cc';
import { GameManager } from '../managers/GameManager';
import { EventManager } from '../managers/EventManager';
import { IQuestionData } from '../data/GameData';
import { UI_CONFIG, GAME_RULES } from '../constants/GameConstants';
import { AudioButton } from './AudioButton';
import { AnswerOption } from './AnswerOption';
import { ProgressBar } from './ProgressBar';
import { ScoreDisplay } from './ScoreDisplay';
import { UIAnimationHelper } from '../utils/UIAnimationHelper';

const { ccclass, property } = _decorator;

/**
 * 游戏主界面UI控制器
 * 负责答题界面的显示和交互
 */
@ccclass('GameUI')
export class GameUI extends Component {
    @property(Label)
    public questionLabel: Label = null;
    
    @property(Label)
    public dialectLabel: Label = null;
    
    @property(Label)
    public questionIndexLabel: Label = null;
    
    @property(Node)
    public audioButtonContainer: Node = null;
    
    @property(Node)
    public optionsContainer: Node = null;
    
    @property(Node)
    public progressContainer: Node = null;
    
    @property(Node)
    public scoreContainer: Node = null;
    
    @property(Button)
    public pauseButton: Button = null;
    
    @property(Prefab)
    public answerOptionPrefab: Prefab = null;
    
    // UI组件引用
    private _audioButton: AudioButton = null;
    private _progressBar: ProgressBar = null;
    private _scoreDisplay: ScoreDisplay = null;
    
    // 答题选项
    private _answerOptions: AnswerOption[] = [];
    
    // 当前题目数据
    private _currentQuestion: IQuestionData = null;
    
    // 游戏管理器引用
    private _gameManager: GameManager = null;
    private _eventManager: EventManager = null;
    
    // 答题状态
    private _isAnswering: boolean = false;
    private _selectedAnswer: number = -1;
    
    // 计时器
    private _answerTimer: number = 0;
    private _timeLeft: number = GAME_RULES.ANSWER_TIME_LIMIT;
    
    protected onLoad(): void {
        this.initializeComponents();
        this.registerEventListeners();
    }
    
    protected onDestroy(): void {
        this.unregisterEventListeners();
        this.stopTimer();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 获取管理器引用
        this._gameManager = GameManager.instance;
        this._eventManager = EventManager.instance;
        
        // 初始化音频按钮
        if (this.audioButtonContainer) {
            this._audioButton = this.audioButtonContainer.getComponent(AudioButton);
            if (!this._audioButton) {
                this._audioButton = this.audioButtonContainer.addComponent(AudioButton);
            }
        }
        
        // 初始化进度条
        if (this.progressContainer) {
            this._progressBar = this.progressContainer.getComponent(ProgressBar);
            if (!this._progressBar) {
                this._progressBar = this.progressContainer.addComponent(ProgressBar);
            }
        }
        
        // 初始化分数显示
        if (this.scoreContainer) {
            this._scoreDisplay = this.scoreContainer.getComponent(ScoreDisplay);
            if (!this._scoreDisplay) {
                this._scoreDisplay = this.scoreContainer.addComponent(ScoreDisplay);
            }
        }
        
        // 设置暂停按钮
        if (this.pauseButton) {
            this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseButtonClick, this);
        }
        
        console.log('[GameUI] 组件初始化完成');
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        if (!this._eventManager) return;
        
        // 监听题目开始事件
        this._eventManager.on('question_started', this.onQuestionStarted, this);
        
        // 监听答题结果事件
        this._eventManager.on('answer_submitted', this.onAnswerSubmitted, this);
        
        // 监听音频播放事件
        this._eventManager.on('audio_play_started', this.onAudioPlayStarted, this);
        this._eventManager.on('audio_play_complete', this.onAudioPlayComplete, this);
        this._eventManager.on('audio_play_error', this.onAudioPlayError, this);
        
        // 监听游戏状态变化
        this._eventManager.on('game_state_changed', this.onGameStateChanged, this);
    }
    
    /**
     * 注销事件监听器
     */
    private unregisterEventListeners(): void {
        if (!this._eventManager) return;
        
        this._eventManager.targetOff(this);
    }
    
    /**
     * 显示题目
     */
    public showQuestion(question: IQuestionData, questionIndex: number, totalQuestions: number): void {
        this._currentQuestion = question;
        this._isAnswering = true;
        this._selectedAnswer = -1;
        
        // 更新题目文本
        if (this.questionLabel) {
            this.questionLabel.string = question.question;
        }
        
        // 更新方言信息
        if (this.dialectLabel) {
            this.dialectLabel.string = `${question.dialect} - ${question.region}`;
        }
        
        // 更新题目索引
        if (this.questionIndexLabel) {
            this.questionIndexLabel.string = `${questionIndex + 1}/${totalQuestions}`;
        }
        
        // 更新进度条
        if (this._progressBar) {
            this._progressBar.setProgress((questionIndex + 1) / totalQuestions);
        }
        
        // 创建答题选项
        this.createAnswerOptions(question.options);
        
        // 重置音频按钮
        if (this._audioButton) {
            this._audioButton.reset();
        }
        
        // 开始倒计时
        this.startTimer();
        
        // 播放显示动画
        this.playShowAnimation();
        
        console.log(`[GameUI] 显示题目: ${question.id}, 索引: ${questionIndex + 1}/${totalQuestions}`);
    }
    
    /**
     * 创建答题选项
     */
    private createAnswerOptions(options: string[]): void {
        // 清理旧的选项
        this.clearAnswerOptions();
        
        if (!this.answerOptionPrefab || !this.optionsContainer) {
            console.error('[GameUI] 答题选项预制体或容器未设置');
            return;
        }
        
        // 创建新的选项
        for (let i = 0; i < options.length; i++) {
            const optionNode = instantiate(this.answerOptionPrefab);
            const optionComponent = optionNode.getComponent(AnswerOption);
            
            if (optionComponent) {
                optionComponent.initialize(i, options[i], this.onAnswerOptionSelected.bind(this));
                this._answerOptions.push(optionComponent);
            }
            
            // 设置选项位置
            optionNode.setParent(this.optionsContainer);
            
            // 播放选项出现动画
            this.playOptionAnimation(optionNode, i);
        }
        
        // 更新布局
        const layout = this.optionsContainer.getComponent(Layout);
        if (layout) {
            layout.updateLayout();
        }
    }
    
    /**
     * 清理答题选项
     */
    private clearAnswerOptions(): void {
        this._answerOptions.forEach(option => {
            if (option && option.node) {
                option.node.destroy();
            }
        });
        this._answerOptions = [];
    }
    
    /**
     * 播放选项出现动画（使用动画池优化）
     */
    private playOptionAnimation(optionNode: Node, index: number): void {
        // 延迟播放动画
        const delay = index * UI_CONFIG.ANSWER_OPTIONS.ANIMATION_DELAY;

        // 使用动画助手播放组合动画
        this.scheduleOnce(() => {
            UIAnimationHelper.scaleAndFadeIn(
                optionNode,
                UI_CONFIG.ANIMATIONS.SCALE_DURATION,
                0,
                () => {
                    console.log(`[GameUI] 选项 ${index} 动画完成`);
                }
            );
        }, delay / 1000);
    }
    
    /**
     * 播放界面显示动画（使用动画池优化）
     */
    private playShowAnimation(): void {
        // 题目文本淡入动画
        if (this.questionLabel) {
            UIAnimationHelper.fadeIn(
                this.questionLabel.node,
                UI_CONFIG.ANIMATIONS.FADE_DURATION,
                0,
                () => {
                    console.log('[GameUI] 题目文本淡入完成');
                }
            );
        }

        // 音频按钮弹跳动画
        if (this._audioButton && this._audioButton.node) {
            UIAnimationHelper.bounce(
                this._audioButton.node,
                UI_CONFIG.ANIMATIONS.BOUNCE_DURATION,
                100, // 延迟100ms
                () => {
                    console.log('[GameUI] 音频按钮弹跳完成');
                }
            );
        }
    }
    
    /**
     * 开始计时器
     */
    private startTimer(): void {
        this.stopTimer();
        this._timeLeft = GAME_RULES.ANSWER_TIME_LIMIT;
        
        this._answerTimer = setInterval(() => {
            this._timeLeft--;
            
            // 更新进度条时间显示
            if (this._progressBar) {
                this._progressBar.setTimeLeft(this._timeLeft);
            }
            
            // 时间到
            if (this._timeLeft <= 0) {
                this.onTimeUp();
            }
        }, 1000);
    }
    
    /**
     * 停止计时器
     */
    private stopTimer(): void {
        if (this._answerTimer) {
            clearInterval(this._answerTimer);
            this._answerTimer = 0;
        }
    }
    
    /**
     * 更新分数显示
     */
    public updateScore(score: number, combo: number): void {
        if (this._scoreDisplay) {
            this._scoreDisplay.updateScore(score, combo);
        }
    }
    
    /**
     * 设置选项交互状态
     */
    private setOptionsInteractable(interactable: boolean): void {
        this._answerOptions.forEach(option => {
            if (option) {
                option.setInteractable(interactable);
            }
        });
    }
    
    /**
     * 显示答案反馈
     */
    private showAnswerFeedback(selectedIndex: number, correctIndex: number): void {
        this._answerOptions.forEach((option, index) => {
            if (option) {
                if (index === correctIndex) {
                    option.showCorrect();
                } else if (index === selectedIndex) {
                    option.showWrong();
                } else {
                    option.showNormal();
                }
            }
        });
    }
    
    // ========== 事件处理器 ==========
    
    /**
     * 答题选项被选择
     */
    private onAnswerOptionSelected(index: number): void {
        if (!this._isAnswering) return;
        
        console.log(`[GameUI] 选择答案: ${index}`);
        
        this._selectedAnswer = index;
        this._isAnswering = false;
        
        // 停止计时器
        this.stopTimer();
        
        // 禁用所有选项
        this.setOptionsInteractable(false);
        
        // 显示答案反馈
        this.showAnswerFeedback(index, this._currentQuestion.correctAnswer);
        
        // 发送答题事件
        this._eventManager?.emit('answer_selected', {
            selectedAnswer: index,
            correctAnswer: this._currentQuestion.correctAnswer,
            questionId: this._currentQuestion.id
        });
    }
    
    /**
     * 时间到处理
     */
    private onTimeUp(): void {
        if (!this._isAnswering) return;
        
        console.log('[GameUI] 答题时间到');
        
        this._isAnswering = false;
        this.stopTimer();
        this.setOptionsInteractable(false);
        
        // 发送超时事件
        this._eventManager?.emit('answer_timeout', {
            questionId: this._currentQuestion.id
        });
    }
    
    /**
     * 暂停按钮点击
     */
    private onPauseButtonClick(): void {
        this._eventManager?.emit('game_pause_request');
    }
    
    /**
     * 题目开始事件处理
     */
    private onQuestionStarted(event: any): void {
        const { question, questionIndex, totalQuestions } = event;
        this.showQuestion(question, questionIndex, totalQuestions);
    }
    
    /**
     * 答题结果事件处理
     */
    private onAnswerSubmitted(event: any): void {
        const { currentScore, currentCombo, isCorrect } = event;
        
        // 更新分数显示
        this.updateScore(currentScore, currentCombo);
        
        // 播放结果动画
        if (isCorrect) {
            this.playCorrectAnimation();
        } else {
            this.playWrongAnimation();
        }
    }
    
    /**
     * 音频播放开始事件处理
     */
    private onAudioPlayStarted(event: any): void {
        if (this._audioButton) {
            this._audioButton.onPlayStarted();
        }
    }
    
    /**
     * 音频播放完成事件处理
     */
    private onAudioPlayComplete(event: any): void {
        if (this._audioButton) {
            this._audioButton.onPlayComplete();
        }
    }
    
    /**
     * 音频播放错误事件处理
     */
    private onAudioPlayError(event: any): void {
        if (this._audioButton) {
            this._audioButton.onPlayError();
        }
    }
    
    /**
     * 游戏状态变化事件处理
     */
    private onGameStateChanged(event: any): void {
        // 根据游戏状态调整UI显示
        // TODO: 实现状态相关的UI调整
    }
    
    /**
     * 播放正确答案动画
     */
    private playCorrectAnimation(): void {
        // 绿色闪烁效果
        const overlay = this.node.getChildByName('CorrectOverlay');
        if (overlay) {
            overlay.active = true;
            tween(overlay)
                .to(0.2, { opacity: 200 })
                .to(0.3, { opacity: 0 })
                .call(() => {
                    overlay.active = false;
                })
                .start();
        }
    }
    
    /**
     * 播放错误答案动画
     */
    private playWrongAnimation(): void {
        // 红色闪烁效果
        const overlay = this.node.getChildByName('WrongOverlay');
        if (overlay) {
            overlay.active = true;
            tween(overlay)
                .to(0.2, { opacity: 200 })
                .to(0.3, { opacity: 0 })
                .call(() => {
                    overlay.active = false;
                })
                .start();
        }
    }
}