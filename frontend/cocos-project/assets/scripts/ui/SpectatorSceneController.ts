import { _decorator, Component, Node, Label, Button, EditBox, Slider, Layout, instantiate, Prefab } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { SpectatorRoomManager, ISpectatorRoom, ISpectator, UserRole } from '../spectator/SpectatorRoomManager';
import { DanmakuSystem } from '../spectator/DanmakuSystem';
import { PredictionGameSystem, PredictionState, IUserPredictionRecord } from '../spectator/PredictionGameSystem';

const { ccclass, property } = _decorator;

/**
 * 围观场景控制器
 * 负责围观界面的交互逻辑、动画效果和状态管理
 */
@ccclass('SpectatorSceneController')
export class SpectatorSceneController extends Component {
    // 玩家区域
    @property(Node)
    public playerArea: Node = null;
    
    @property(Label)
    public playerNameLabel: Label = null;
    
    @property(Label)
    public playerScoreLabel: Label = null;
    
    @property(Label)
    public gameProgressLabel: Label = null;
    
    // 题目区域
    @property(Node)
    public questionArea: Node = null;
    
    @property(Label)
    public questionTextLabel: Label = null;
    
    @property(Button)
    public audioButton: Button = null;
    
    @property(Node)
    public answerOptionsContainer: Node = null;
    
    // 预测区域
    @property(Node)
    public predictionArea: Node = null;
    
    @property(Label)
    public predictionTitleLabel: Label = null;
    
    @property(Node)
    public predictionOptionsContainer: Node = null;
    
    @property(Slider)
    public confidenceSlider: Slider = null;
    
    @property(Label)
    public confidenceLabel: Label = null;
    
    @property(Button)
    public submitPredictionButton: Button = null;
    
    // 弹幕区域
    @property(Node)
    public danmakuArea: Node = null;
    
    @property(Node)
    public danmakuContainer: Node = null;
    
    @property(EditBox)
    public danmakuInput: EditBox = null;
    
    @property(Button)
    public sendDanmakuButton: Button = null;
    
    // 侧边栏
    @property(Node)
    public sidePanel: Node = null;
    
    @property(Node)
    public spectatorListContainer: Node = null;
    
    @property(Node)
    public leaderboardContainer: Node = null;
    
    // 预制体
    @property(Prefab)
    public answerOptionPrefab: Prefab = null;
    
    @property(Prefab)
    public predictionOptionPrefab: Prefab = null;
    
    @property(Prefab)
    public spectatorItemPrefab: Prefab = null;
    
    @property(Prefab)
    public leaderboardItemPrefab: Prefab = null;
    
    // 当前状态
    private _currentRoom: ISpectatorRoom = null;
    private _selectedPredictionOption: number = -1;
    private _predictionConfidence: number = 50;
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    private _spectatorRoomManager: SpectatorRoomManager = null;
    private _danmakuSystem: DanmakuSystem = null;
    private _predictionGameSystem: PredictionGameSystem = null;
    
    protected onLoad(): void {
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        this._spectatorRoomManager = SpectatorRoomManager.getInstance();
        this._danmakuSystem = this.danmakuContainer?.getComponent(DanmakuSystem);
        this._predictionGameSystem = PredictionGameSystem.getInstance();
        
        this.initializeUI();
        this.registerEventListeners();
        this.setupButtonEvents();
        
        console.log('[SpectatorSceneController] 围观场景控制器初始化完成');
    }
    
    protected onDestroy(): void {
        this.unregisterEventListeners();
    }
    
    /**
     * 加入房间
     */
    public async joinRoom(roomId: string): Promise<void> {
        try {
            await this._spectatorRoomManager.joinRoom(roomId);
            console.log('[SpectatorSceneController] 成功加入房间:', roomId);
        } catch (error) {
            console.error('[SpectatorSceneController] 加入房间失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'SpectatorSceneController.joinRoom' });
        }
    }
    
    /**
     * 离开房间
     */
    public leaveRoom(): void {
        this._spectatorRoomManager.leaveRoom();
        console.log('[SpectatorSceneController] 已离开房间');
    }
    
    /**
     * 发送弹幕
     */
    public sendDanmaku(): void {
        const content = this.danmakuInput.string.trim();
        if (!content) {
            console.warn('[SpectatorSceneController] 弹幕内容为空');
            return;
        }
        
        this._spectatorRoomManager.sendDanmaku(content);
        this.danmakuInput.string = '';
        
        console.log('[SpectatorSceneController] 发送弹幕:', content);
    }
    
    /**
     * 提交预测
     */
    public submitPrediction(): void {
        if (this._selectedPredictionOption === -1) {
            console.warn('[SpectatorSceneController] 未选择预测选项');
            return;
        }
        
        if (!this._currentRoom?.currentQuestion) {
            console.warn('[SpectatorSceneController] 当前没有题目');
            return;
        }
        
        this._spectatorRoomManager.submitPrediction(
            this._currentRoom.currentQuestion.questionId,
            this._selectedPredictionOption,
            this._predictionConfidence
        );
        
        // 禁用预测按钮
        this.submitPredictionButton.interactable = false;
        
        console.log(`[SpectatorSceneController] 提交预测: 选项${this._selectedPredictionOption}, 置信度${this._predictionConfidence}%`);
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 初始化置信度滑块
        if (this.confidenceSlider) {
            this.confidenceSlider.progress = 0.5;
            this._predictionConfidence = 50;
            this.updateConfidenceLabel();
        }
        
        // 初始化预测区域状态
        this.updatePredictionAreaState(PredictionState.WAITING);
        
        // 隐藏题目区域
        if (this.questionArea) {
            this.questionArea.active = false;
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.on('spectator_room_updated', this.onRoomUpdated.bind(this));
            eventManager.on('spectator_question_started', this.onQuestionStarted.bind(this));
            eventManager.on('spectator_answer_submitted', this.onAnswerSubmitted.bind(this));
            eventManager.on('spectator_game_ended', this.onGameEnded.bind(this));
            eventManager.on('prediction_started', this.onPredictionStarted.bind(this));
            eventManager.on('prediction_closed', this.onPredictionClosed.bind(this));
            eventManager.on('answer_revealed', this.onAnswerRevealed.bind(this));
        }
    }
    
    /**
     * 取消注册事件监听器
     */
    private unregisterEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.off('spectator_room_updated', this.onRoomUpdated.bind(this));
            eventManager.off('spectator_question_started', this.onQuestionStarted.bind(this));
            eventManager.off('spectator_answer_submitted', this.onAnswerSubmitted.bind(this));
            eventManager.off('spectator_game_ended', this.onGameEnded.bind(this));
            eventManager.off('prediction_started', this.onPredictionStarted.bind(this));
            eventManager.off('prediction_closed', this.onPredictionClosed.bind(this));
            eventManager.off('answer_revealed', this.onAnswerRevealed.bind(this));
        }
    }
    
    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 发送弹幕按钮
        if (this.sendDanmakuButton) {
            this.sendDanmakuButton.node.on(Button.EventType.CLICK, this.sendDanmaku, this);
        }
        
        // 提交预测按钮
        if (this.submitPredictionButton) {
            this.submitPredictionButton.node.on(Button.EventType.CLICK, this.submitPrediction, this);
        }
        
        // 置信度滑块
        if (this.confidenceSlider) {
            this.confidenceSlider.node.on('slide', this.onConfidenceChanged, this);
        }
        
        // 弹幕输入框回车事件
        if (this.danmakuInput) {
            this.danmakuInput.node.on(EditBox.EventType.EDITING_RETURN, this.sendDanmaku, this);
        }
    }
    
    /**
     * 房间更新事件处理
     */
    private onRoomUpdated(room: ISpectatorRoom): void {
        this._currentRoom = room;
        this.updatePlayerInfo();
        this.updateSpectatorList();
    }
    
    /**
     * 题目开始事件处理
     */
    private onQuestionStarted(data: any): void {
        const { question } = data;
        this.showQuestion(question);
        this.createAnswerOptions(question.options);
    }
    
    /**
     * 答案提交事件处理
     */
    private onAnswerSubmitted(data: any): void {
        this.updatePlayerScore(data.totalScore);
    }
    
    /**
     * 游戏结束事件处理
     */
    private onGameEnded(data: any): void {
        this.showGameResult(data);
    }
    
    /**
     * 预测开始事件处理
     */
    private onPredictionStarted(data: any): void {
        this.updatePredictionAreaState(PredictionState.OPEN);
        this.createPredictionOptions(data.options);
        this.startPredictionTimer(data.duration);
    }
    
    /**
     * 预测关闭事件处理
     */
    private onPredictionClosed(data: any): void {
        this.updatePredictionAreaState(PredictionState.CLOSED);
    }
    
    /**
     * 答案公布事件处理
     */
    private onAnswerRevealed(data: any): void {
        this.updatePredictionAreaState(PredictionState.REVEALED);
        this.showPredictionResult(data);
        this.updateLeaderboard(data.leaderboard);
    }
    
    /**
     * 置信度变化事件处理
     */
    private onConfidenceChanged(slider: Slider): void {
        this._predictionConfidence = Math.round(slider.progress * 100);
        this.updateConfidenceLabel();
    }
    
    /**
     * 更新玩家信息
     */
    private updatePlayerInfo(): void {
        if (!this._currentRoom) return;
        
        const { playerInfo, gameProgress } = this._currentRoom;
        
        if (this.playerNameLabel) {
            this.playerNameLabel.string = playerInfo.nickname;
        }
        
        if (this.playerScoreLabel) {
            this.playerScoreLabel.string = `分数: ${playerInfo.score}`;
        }
        
        if (this.gameProgressLabel) {
            this.gameProgressLabel.string = `${gameProgress.currentQuestionIndex}/${gameProgress.totalQuestions}`;
        }
    }
    
    /**
     * 显示题目
     */
    private showQuestion(question: any): void {
        if (this.questionArea) {
            this.questionArea.active = true;
        }
        
        if (this.questionTextLabel) {
            this.questionTextLabel.string = question.questionText || '听音频，选择对应的方言地区';
        }
    }
    
    /**
     * 创建答案选项
     */
    private createAnswerOptions(options: string[]): void {
        if (!this.answerOptionsContainer || !this.answerOptionPrefab) return;
        
        // 清空现有选项
        this.answerOptionsContainer.removeAllChildren();
        
        // 创建新选项
        options.forEach((option, index) => {
            const optionNode = instantiate(this.answerOptionPrefab);
            const label = optionNode.getComponentInChildren(Label);
            if (label) {
                label.string = option;
            }
            
            optionNode.parent = this.answerOptionsContainer;
        });
    }
    
    /**
     * 创建预测选项
     */
    private createPredictionOptions(options: string[]): void {
        if (!this.predictionOptionsContainer || !this.predictionOptionPrefab) return;
        
        // 清空现有选项
        this.predictionOptionsContainer.removeAllChildren();
        
        // 重置选择状态
        this._selectedPredictionOption = -1;
        
        // 创建新选项
        options.forEach((option, index) => {
            const optionNode = instantiate(this.predictionOptionPrefab);
            const label = optionNode.getComponentInChildren(Label);
            const button = optionNode.getComponent(Button);
            
            if (label) {
                label.string = option;
            }
            
            if (button) {
                button.node.on(Button.EventType.CLICK, () => {
                    this.selectPredictionOption(index);
                });
            }
            
            optionNode.parent = this.predictionOptionsContainer;
        });
        
        // 启用提交按钮
        if (this.submitPredictionButton) {
            this.submitPredictionButton.interactable = true;
        }
    }
    
    /**
     * 选择预测选项
     */
    private selectPredictionOption(index: number): void {
        this._selectedPredictionOption = index;
        
        // 更新UI显示选中状态
        const options = this.predictionOptionsContainer.children;
        options.forEach((optionNode, i) => {
            const button = optionNode.getComponent(Button);
            if (button) {
                // 这里可以添加选中状态的视觉效果
                button.normalColor = i === index ? cc.Color.GREEN : cc.Color.WHITE;
            }
        });
        
        console.log('[SpectatorSceneController] 选择预测选项:', index);
    }
    
    /**
     * 更新预测区域状态
     */
    private updatePredictionAreaState(state: PredictionState): void {
        if (!this.predictionTitleLabel) return;
        
        switch (state) {
            case PredictionState.WAITING:
                this.predictionTitleLabel.string = '等待题目开始...';
                this.predictionArea.active = false;
                break;
                
            case PredictionState.OPEN:
                this.predictionTitleLabel.string = '快来预测答案吧！';
                this.predictionArea.active = true;
                break;
                
            case PredictionState.CLOSED:
                this.predictionTitleLabel.string = '预测已关闭，等待答案公布...';
                break;
                
            case PredictionState.REVEALED:
                this.predictionTitleLabel.string = '答案已公布！';
                break;
        }
    }
    
    /**
     * 更新置信度标签
     */
    private updateConfidenceLabel(): void {
        if (this.confidenceLabel) {
            this.confidenceLabel.string = `置信度: ${this._predictionConfidence}%`;
        }
    }
    
    /**
     * 开始预测计时器
     */
    private startPredictionTimer(duration: number): void {
        // 这里可以添加倒计时显示逻辑
        console.log(`[SpectatorSceneController] 预测时间: ${duration}ms`);
    }
    
    /**
     * 显示预测结果
     */
    private showPredictionResult(data: any): void {
        console.log('[SpectatorSceneController] 预测结果:', data);
        // 这里可以添加预测结果的显示逻辑
    }
    
    /**
     * 更新围观者列表
     */
    private updateSpectatorList(): void {
        if (!this.spectatorListContainer || !this.spectatorItemPrefab) return;
        
        const spectators = this._spectatorRoomManager.getSpectators();
        
        // 清空现有列表
        this.spectatorListContainer.removeAllChildren();
        
        // 创建围观者项
        spectators.forEach(spectator => {
            const itemNode = instantiate(this.spectatorItemPrefab);
            const label = itemNode.getComponentInChildren(Label);
            if (label) {
                label.string = spectator.nickname;
            }
            
            itemNode.parent = this.spectatorListContainer;
        });
    }
    
    /**
     * 更新排行榜
     */
    private updateLeaderboard(leaderboard: IUserPredictionRecord[]): void {
        if (!this.leaderboardContainer || !this.leaderboardItemPrefab) return;
        
        // 清空现有排行榜
        this.leaderboardContainer.removeAllChildren();
        
        // 创建排行榜项
        leaderboard.forEach(record => {
            const itemNode = instantiate(this.leaderboardItemPrefab);
            const label = itemNode.getComponentInChildren(Label);
            if (label) {
                label.string = `${record.rank}. ${record.nickname} - ${record.totalPoints}分`;
            }
            
            itemNode.parent = this.leaderboardContainer;
        });
    }
    
    /**
     * 更新玩家分数
     */
    private updatePlayerScore(score: number): void {
        if (this.playerScoreLabel) {
            this.playerScoreLabel.string = `分数: ${score}`;
        }
    }
    
    /**
     * 显示游戏结果
     */
    private showGameResult(data: any): void {
        console.log('[SpectatorSceneController] 游戏结束:', data);
        // 这里可以添加游戏结果的显示逻辑
    }
}
