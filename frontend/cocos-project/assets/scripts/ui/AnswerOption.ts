import { _decorator, Component, Node, Button, Label, Sprite, Color, tween, Vec3 } from 'cc';
import { UI_CONFIG } from '../constants/GameConstants';
import { UIAnimationHelper } from '../utils/UIAnimationHelper';

const { ccclass, property } = _decorator;

/**
 * 答题选项组件
 * 负责单个答题选项的显示和交互
 */
@ccclass('AnswerOption')
export class AnswerOption extends Component {
    @property(Button)
    public button: Button = null;
    
    @property(Label)
    public optionLabel: Label = null;
    
    @property(Label)
    public indexLabel: Label = null;
    
    @property(Sprite)
    public backgroundSprite: Sprite = null;
    
    @property(Node)
    public feedbackIcon: Node = null;
    
    // 选项数据
    private _index: number = -1;
    private _text: string = '';
    private _isSelected: boolean = false;
    private _isInteractable: boolean = true;
    
    // 回调函数
    private _onSelectedCallback: ((index: number) => void) | null = null;
    
    // 颜色配置
    private readonly COLORS = {
        NORMAL: new Color().fromHEX(UI_CONFIG.COLORS.BACKGROUND),
        HOVER: new Color().fromHEX(UI_CONFIG.COLORS.SECONDARY).multiplyScalar(0.1),
        SELECTED: new Color().fromHEX(UI_CONFIG.COLORS.PRIMARY).multiplyScalar(0.2),
        CORRECT: new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS).multiplyScalar(0.3),
        WRONG: new Color().fromHEX(UI_CONFIG.COLORS.ERROR).multiplyScalar(0.3),
        DISABLED: new Color(200, 200, 200, 255)
    };
    
    protected onLoad(): void {
        this.initializeComponents();
        this.registerEventListeners();
    }
    
    protected onDestroy(): void {
        this.unregisterEventListeners();
    }
    
    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 初始化按钮
        if (!this.button) {
            this.button = this.node.getComponent(Button);
            if (!this.button) {
                this.button = this.node.addComponent(Button);
            }
        }
        
        // 初始化标签
        if (!this.optionLabel) {
            const labelNode = this.node.getChildByName('OptionLabel');
            if (labelNode) {
                this.optionLabel = labelNode.getComponent(Label);
            }
        }
        
        if (!this.indexLabel) {
            const indexNode = this.node.getChildByName('IndexLabel');
            if (indexNode) {
                this.indexLabel = indexNode.getComponent(Label);
            }
        }
        
        // 初始化背景
        if (!this.backgroundSprite) {
            this.backgroundSprite = this.node.getComponent(Sprite);
            if (!this.backgroundSprite) {
                this.backgroundSprite = this.node.addComponent(Sprite);
            }
        }
        
        // 初始化反馈图标
        if (!this.feedbackIcon) {
            this.feedbackIcon = this.node.getChildByName('FeedbackIcon');
        }
        
        // 设置初始状态
        this.setInitialState();
        
        console.log('[AnswerOption] 答题选项组件初始化完成');
    }
    
    /**
     * 设置初始状态
     */
    private setInitialState(): void {
        // 设置背景颜色
        if (this.backgroundSprite) {
            this.backgroundSprite.color = this.COLORS.NORMAL;
        }
        
        // 隐藏反馈图标
        if (this.feedbackIcon) {
            this.feedbackIcon.active = false;
        }
        
        // 设置按钮状态
        if (this.button) {
            this.button.interactable = true;
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        if (this.button) {
            this.button.node.on(Button.EventType.CLICK, this.onButtonClick, this);
            this.button.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
            this.button.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.button.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        }
    }
    
    /**
     * 注销事件监听器
     */
    private unregisterEventListeners(): void {
        if (this.button) {
            this.button.node.off(Button.EventType.CLICK, this.onButtonClick, this);
            this.button.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
            this.button.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.button.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        }
    }
    
    /**
     * 初始化选项
     */
    public initialize(index: number, text: string, onSelectedCallback: (index: number) => void): void {
        this._index = index;
        this._text = text;
        this._onSelectedCallback = onSelectedCallback;
        
        // 设置索引标签
        if (this.indexLabel) {
            this.indexLabel.string = String.fromCharCode(65 + index); // A, B, C, D
        }
        
        // 设置选项文本
        if (this.optionLabel) {
            this.optionLabel.string = text;
        }
        
        // 重置状态
        this._isSelected = false;
        this._isInteractable = true;
        this.setInitialState();
        
        console.log(`[AnswerOption] 选项初始化: ${index} - ${text}`);
    }
    
    /**
     * 设置交互状态
     */
    public setInteractable(interactable: boolean): void {
        this._isInteractable = interactable;
        
        if (this.button) {
            this.button.interactable = interactable;
        }
        
        // 更新视觉状态
        this.updateVisualState();
    }
    
    /**
     * 显示正确状态
     */
    public showCorrect(): void {
        console.log(`[AnswerOption] 显示正确答案: ${this._index}`);
        
        // 设置背景颜色
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(UI_CONFIG.ANIMATIONS.FADE_DURATION / 1000, {
                    color: this.COLORS.CORRECT
                })
                .start();
        }
        
        // 显示正确图标
        this.showFeedbackIcon('correct');
        
        // 播放正确动画
        this.playCorrectAnimation();
    }
    
    /**
     * 显示错误状态
     */
    public showWrong(): void {
        console.log(`[AnswerOption] 显示错误答案: ${this._index}`);
        
        // 设置背景颜色
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(UI_CONFIG.ANIMATIONS.FADE_DURATION / 1000, {
                    color: this.COLORS.WRONG
                })
                .start();
        }
        
        // 显示错误图标
        this.showFeedbackIcon('wrong');
        
        // 播放错误动画
        this.playWrongAnimation();
    }
    
    /**
     * 显示普通状态
     */
    public showNormal(): void {
        // 设置背景颜色为禁用状态
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(UI_CONFIG.ANIMATIONS.FADE_DURATION / 1000, {
                    color: this.COLORS.DISABLED
                })
                .start();
        }
        
        // 隐藏反馈图标
        if (this.feedbackIcon) {
            this.feedbackIcon.active = false;
        }
    }
    
    /**
     * 显示反馈图标
     */
    private showFeedbackIcon(type: 'correct' | 'wrong'): void {
        if (!this.feedbackIcon) return;
        
        this.feedbackIcon.active = true;
        
        // 设置图标类型（这里需要根据实际的图标资源来设置）
        const sprite = this.feedbackIcon.getComponent(Sprite);
        if (sprite) {
            // TODO: 根据type设置不同的图标
            if (type === 'correct') {
                sprite.color = new Color().fromHEX(UI_CONFIG.COLORS.SUCCESS);
            } else {
                sprite.color = new Color().fromHEX(UI_CONFIG.COLORS.ERROR);
            }
        }
        
        // 播放图标出现动画
        this.feedbackIcon.setScale(0, 0, 1);
        tween(this.feedbackIcon)
            .to(UI_CONFIG.ANIMATIONS.SCALE_DURATION / 1000, {
                scale: new Vec3(1, 1, 1)
            }, {
                easing: 'backOut'
            })
            .start();
    }
    
    /**
     * 播放正确动画（使用动画池优化）
     */
    private playCorrectAnimation(): void {
        UIAnimationHelper.successFeedback(this.node, () => {
            console.log(`[AnswerOption] 正确动画完成: ${this._index}`);
        });
    }
    
    /**
     * 播放错误动画（使用动画池优化）
     */
    private playWrongAnimation(): void {
        UIAnimationHelper.errorFeedback(this.node, () => {
            console.log(`[AnswerOption] 错误动画完成: ${this._index}`);
        });
    }
    
    /**
     * 播放点击动画（使用动画池优化）
     */
    private playClickAnimation(): void {
        UIAnimationHelper.clickFeedback(this.node, () => {
            console.log(`[AnswerOption] 点击动画完成: ${this._index}`);
        });
    }
    
    /**
     * 播放悬停动画（使用动画池优化）
     */
    private playHoverAnimation(): void {
        if (!this._isInteractable || this._isSelected) return;

        // 背景颜色变化
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(0.2, { color: this.COLORS.HOVER })
                .start();
        }

        // 使用动画助手播放悬停反馈
        UIAnimationHelper.hoverFeedback(this.node, 1.02, () => {
            console.log(`[AnswerOption] 悬停动画完成: ${this._index}`);
        });
    }
    
    /**
     * 停止悬停动画（使用动画池优化）
     */
    private stopHoverAnimation(): void {
        if (!this._isInteractable || this._isSelected) return;

        // 背景颜色恢复
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(0.2, { color: this.COLORS.NORMAL })
                .start();
        }

        // 使用动画助手停止悬停反馈
        UIAnimationHelper.stopHoverFeedback(this.node, () => {
            console.log(`[AnswerOption] 停止悬停动画: ${this._index}`);
        });
    }
    
    /**
     * 更新视觉状态
     */
    private updateVisualState(): void {
        if (!this.backgroundSprite) return;
        
        let targetColor = this.COLORS.NORMAL;
        
        if (!this._isInteractable) {
            targetColor = this.COLORS.DISABLED;
        } else if (this._isSelected) {
            targetColor = this.COLORS.SELECTED;
        }
        
        this.backgroundSprite.color = targetColor;
    }
    
    // ========== 事件处理器 ==========
    
    /**
     * 按钮点击事件处理
     */
    private onButtonClick(): void {
        if (!this._isInteractable || this._isSelected) return;
        
        console.log(`[AnswerOption] 选项被点击: ${this._index}`);
        
        this._isSelected = true;
        
        // 播放点击动画
        this.playClickAnimation();
        
        // 更新背景颜色
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(0.2, { color: this.COLORS.SELECTED })
                .start();
        }
        
        // 调用回调函数
        if (this._onSelectedCallback) {
            this._onSelectedCallback(this._index);
        }
    }
    
    /**
     * 触摸开始事件处理
     */
    private onTouchStart(): void {
        if (!this._isInteractable) return;
        
        this.playHoverAnimation();
        
        // 触觉反馈（如果支持的话）
        if (typeof wx !== 'undefined' && wx.vibrateShort) {
            wx.vibrateShort({
                type: 'light'
            });
        }
    }
    
    /**
     * 触摸结束事件处理
     */
    private onTouchEnd(): void {
        if (!this._isInteractable) return;
        
        this.stopHoverAnimation();
    }
    
    /**
     * 触摸取消事件处理
     */
    private onTouchCancel(): void {
        if (!this._isInteractable) return;
        
        this.stopHoverAnimation();
    }
    
    // ========== 属性访问器 ==========
    
    /**
     * 获取选项索引
     */
    public get index(): number {
        return this._index;
    }
    
    /**
     * 获取选项文本
     */
    public get text(): string {
        return this._text;
    }
    
    /**
     * 是否已选择
     */
    public get isSelected(): boolean {
        return this._isSelected;
    }
    
    /**
     * 是否可交互
     */
    public get isInteractable(): boolean {
        return this._isInteractable;
    }
}