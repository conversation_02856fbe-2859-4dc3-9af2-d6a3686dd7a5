import { _decorator } from 'cc';
import { LocalizedLabel } from './LocalizedLabel';

const { ccclass, property } = _decorator;

/**
 * 本地化富文本组件
 * 专门用于富文本的本地化
 */
@ccclass('LocalizedRichText')
export class LocalizedRichText extends LocalizedLabel {
    @property({
        tooltip: '是否启用富文本标签解析'
    })
    public enableRichTextTags: boolean = true;
    
    @property({
        tooltip: '富文本样式配置'
    })
    public richTextStyles: string = '{}';
    
    protected onLoad(): void {
        this.useRichText = true;
        super.onLoad();
    }
    
    /**
     * 设置富文本样式
     */
    public setRichTextStyles(styles: any): void {
        const styleString = typeof styles === 'string' ? styles : JSON.stringify(styles);
        if (this.richTextStyles !== styleString) {
            this.richTextStyles = styleString;
            this.updateText();
        }
    }
    
    /**
     * 应用富文本样式
     */
    private applyRichTextStyles(text: string): string {
        if (!this.enableRichTextTags || !this.richTextStyles || this.richTextStyles.trim() === '{}') {
            return text;
        }
        
        try {
            const styles = JSON.parse(this.richTextStyles);
            
            // 应用样式替换
            let styledText = text;
            for (const [key, value] of Object.entries(styles)) {
                const regex = new RegExp(`\\{${key}\\}`, 'g');
                styledText = styledText.replace(regex, value as string);
            }
            
            return styledText;
            
        } catch (error) {
            console.warn('[LocalizedRichText] 富文本样式解析失败:', this.richTextStyles);
            return text;
        }
    }
    
    /**
     * 重写设置显示文本方法
     */
    private setDisplayText(text: string): void {
        if (this._richText) {
            const styledText = this.applyRichTextStyles(text);
            this._richText.string = styledText;
        } else if (this._label) {
            this._label.string = text;
        }
    }
}
