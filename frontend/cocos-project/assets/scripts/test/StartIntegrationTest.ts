import { _decorator, Component } from 'cc';
import { DEBUG_CONFIG } from '../constants/GameConstants';
import { TestRunner } from '../utils/TestRunner';

const { ccclass, property } = _decorator;

/**
 * 联调测试启动组件
 * 在游戏启动时自动进行联调测试
 */
@ccclass('StartIntegrationTest')
export class StartIntegrationTest extends Component {
    
    @property({
        tooltip: '是否在游戏启动时自动运行联调测试'
    })
    public autoRun: boolean = true;

    @property({
        tooltip: '测试启动延迟时间（秒）'
    })
    public startDelay: number = 3;

    protected onLoad(): void {
        if (!DEBUG_CONFIG.ENABLED) {
            console.log('[StartIntegrationTest] 调试模式未启用，跳过联调测试');
            return;
        }

        if (this.autoRun) {
            this.scheduleOnce(() => {
                this.runIntegrationTest();
            }, this.startDelay);
        }

        // 注册全局测试命令
        this.registerGlobalCommands();
    }

    /**
     * 运行联调测试
     */
    private async runIntegrationTest(): Promise<void> {
        try {
            const testRunner = TestRunner.getInstance();
            
            console.log('%c🚀 自动启动前后端联调测试', 'color: #00ff00; font-size: 14px; font-weight: bold;');
            
            // 运行完整的联调测试
            await testRunner.startIntegrationTest();
            
        } catch (error) {
            console.error('❌ 自动联调测试失败:', error);
        }
    }

    /**
     * 注册全局测试命令
     */
    private registerGlobalCommands(): void {
        if (typeof window === 'undefined') {
            return;
        }

        const testRunner = TestRunner.getInstance();

        // 暴露便捷的测试命令
        (window as any).runTest = {
            // 运行完整测试
            full: () => testRunner.startIntegrationTest(),
            
            // 运行特定测试
            network: () => testRunner.runSpecificTest('network'),
            login: () => testRunner.runSpecificTest('login'),
            api: () => testRunner.runSpecificTest('api'),
            error: () => testRunner.runSpecificTest('error'),
            
            // 模拟游戏流程
            game: () => testRunner.simulateGameFlow(),
            
            // 清理数据
            clear: () => testRunner.clearTestData(),
            
            // 获取统计
            stats: () => testRunner.getTestStats()
        };

        console.log('%c📋 全局测试命令已注册', 'color: #00ff00;');
        console.log('可用命令:');
        console.log('- runTest.full()     // 运行完整联调测试');
        console.log('- runTest.network()  // 测试网络连接');
        console.log('- runTest.login()    // 测试微信登录');
        console.log('- runTest.api()      // 测试游戏API');
        console.log('- runTest.error()    // 测试错误处理');
        console.log('- runTest.game()     // 模拟游戏流程');
        console.log('- runTest.clear()    // 清理测试数据');
        console.log('- runTest.stats()    // 获取测试统计');
    }

    /**
     * 手动触发测试（供UI调用）
     */
    public manualTest(): void {
        this.runIntegrationTest();
    }
}