import { _decorator, Component, instantiate } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { IQuestionData } from '../data/GameData';

const { ccclass } = _decorator;

/**
 * 学习模式类型
 */
export enum LearningMode {
    BASIC = 'basic',           // 基础学习
    ADVANCED = 'advanced',     // 进阶学习
    PRACTICE = 'practice',     // 练习模式
    CHALLENGE = 'challenge'    // 挑战模式
}

/**
 * 学习难度
 */
export enum LearningDifficulty {
    BEGINNER = 'beginner',     // 初学者
    INTERMEDIATE = 'intermediate', // 中级
    ADVANCED = 'advanced',     // 高级
    EXPERT = 'expert'          // 专家
}

/**
 * 学习进度状态
 */
export enum LearningProgressStatus {
    NOT_STARTED = 'not_started',
    IN_PROGRESS = 'in_progress',
    COMPLETED = 'completed',
    MASTERED = 'mastered'
}

/**
 * 课程信息
 */
export interface ICourse {
    courseId: string;
    title: string;
    description: string;
    dialect: string;           // 方言类型
    difficulty: LearningDifficulty;
    mode: LearningMode;
    totalLessons: number;
    estimatedTime: number;     // 预计学习时间（分钟）
    prerequisites: string[];   // 前置课程
    tags: string[];
    thumbnail?: string;
    isUnlocked: boolean;
    progress: {
        completedLessons: number;
        totalScore: number;
        accuracy: number;
        timeSpent: number;
        status: LearningProgressStatus;
    };
}

/**
 * 课程单元
 */
export interface ILesson {
    lessonId: string;
    courseId: string;
    title: string;
    description: string;
    order: number;
    type: 'theory' | 'practice' | 'test';
    content: {
        theory?: {
            text: string;
            images?: string[];
            audio?: string[];
        };
        practice?: {
            questions: IQuestionData[];
            passingScore: number;
        };
        test?: {
            questions: IQuestionData[];
            timeLimit: number;
            passingScore: number;
        };
    };
    isUnlocked: boolean;
    progress: {
        isCompleted: boolean;
        score: number;
        attempts: number;
        bestScore: number;
        timeSpent: number;
        lastAttemptTime: number;
    };
}

/**
 * 学习记录
 */
export interface ILearningRecord {
    recordId: string;
    userId: string;
    courseId: string;
    lessonId: string;
    startTime: number;
    endTime: number;
    score: number;
    accuracy: number;
    mistakes: {
        questionId: string;
        selectedAnswer: number;
        correctAnswer: number;
        explanation?: string;
    }[];
    timeSpent: number;
    isCompleted: boolean;
}

/**
 * 学习统计
 */
export interface ILearningStats {
    totalCoursesStarted: number;
    totalCoursesCompleted: number;
    totalLessonsCompleted: number;
    totalTimeSpent: number;
    averageAccuracy: number;
    totalScore: number;
    dialectsLearned: string[];
    achievements: string[];
    streakDays: number;
    lastStudyDate: number;
}

/**
 * 方言课堂系统
 * 负责学习模式的课程管理、进度追踪、成就系统等功能
 */
@ccclass('DialectClassroomSystem')
export class DialectClassroomSystem extends Component {
    private static _instance: DialectClassroomSystem = null;
    
    // 课程数据
    private _courses: Map<string, ICourse> = new Map();
    private _lessons: Map<string, ILesson> = new Map();
    
    // 当前学习状态
    private _currentCourse: ICourse = null;
    private _currentLesson: ILesson = null;
    private _currentLearningRecord: ILearningRecord = null;
    
    // 学习记录
    private _learningRecords: ILearningRecord[] = [];
    private _learningStats: ILearningStats = {
        totalCoursesStarted: 0,
        totalCoursesCompleted: 0,
        totalLessonsCompleted: 0,
        totalTimeSpent: 0,
        averageAccuracy: 0,
        totalScore: 0,
        dialectsLearned: [],
        achievements: [],
        streakDays: 0,
        lastStudyDate: 0
    };
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): DialectClassroomSystem {
        return this._instance;
    }
    
    protected onLoad(): void {
        DialectClassroomSystem._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.initializeCourses();
        this.loadLearningProgress();
        this.registerEventListeners();
        
        console.log('[DialectClassroomSystem] 方言课堂系统初始化完成');
    }
    
    protected onDestroy(): void {
        DialectClassroomSystem._instance = null;
    }
    
    /**
     * 获取所有课程
     */
    public getCourses(filter?: {
        dialect?: string;
        difficulty?: LearningDifficulty;
        mode?: LearningMode;
        onlyUnlocked?: boolean;
    }): ICourse[] {
        let courses = Array.from(this._courses.values());
        
        if (filter) {
            if (filter.dialect) {
                courses = courses.filter(course => course.dialect === filter.dialect);
            }
            if (filter.difficulty) {
                courses = courses.filter(course => course.difficulty === filter.difficulty);
            }
            if (filter.mode) {
                courses = courses.filter(course => course.mode === filter.mode);
            }
            if (filter.onlyUnlocked) {
                courses = courses.filter(course => course.isUnlocked);
            }
        }
        
        return courses.sort((a, b) => a.difficulty.localeCompare(b.difficulty));
    }
    
    /**
     * 获取课程详情
     */
    public getCourse(courseId: string): ICourse | null {
        return this._courses.get(courseId) || null;
    }
    
    /**
     * 获取课程的所有课时
     */
    public getCourseLessons(courseId: string): ILesson[] {
        const lessons = Array.from(this._lessons.values())
            .filter(lesson => lesson.courseId === courseId);
        
        return lessons.sort((a, b) => a.order - b.order);
    }
    
    /**
     * 开始学习课程
     */
    public async startCourse(courseId: string): Promise<boolean> {
        const course = this._courses.get(courseId);
        if (!course) {
            console.error('[DialectClassroomSystem] 课程不存在:', courseId);
            return false;
        }
        
        if (!course.isUnlocked) {
            console.warn('[DialectClassroomSystem] 课程未解锁:', courseId);
            return false;
        }
        
        // 检查前置课程
        if (!this.checkPrerequisites(course)) {
            console.warn('[DialectClassroomSystem] 前置课程未完成:', course.prerequisites);
            return false;
        }
        
        this._currentCourse = course;
        
        // 更新统计
        if (course.progress.status === LearningProgressStatus.NOT_STARTED) {
            this._learningStats.totalCoursesStarted++;
            course.progress.status = LearningProgressStatus.IN_PROGRESS;
        }
        
        // 发送事件
        this.emitEvent('course_started', { course });
        
        console.log('[DialectClassroomSystem] 开始学习课程:', course.title);
        return true;
    }
    
    /**
     * 开始学习课时
     */
    public async startLesson(lessonId: string): Promise<boolean> {
        const lesson = this._lessons.get(lessonId);
        if (!lesson) {
            console.error('[DialectClassroomSystem] 课时不存在:', lessonId);
            return false;
        }
        
        if (!lesson.isUnlocked) {
            console.warn('[DialectClassroomSystem] 课时未解锁:', lessonId);
            return false;
        }
        
        this._currentLesson = lesson;
        
        // 创建学习记录
        this._currentLearningRecord = {
            recordId: this.generateRecordId(),
            userId: this.getCurrentUserId(),
            courseId: lesson.courseId,
            lessonId: lesson.lessonId,
            startTime: Date.now(),
            endTime: 0,
            score: 0,
            accuracy: 0,
            mistakes: [],
            timeSpent: 0,
            isCompleted: false
        };
        
        // 发送事件
        this.emitEvent('lesson_started', { lesson });
        
        console.log('[DialectClassroomSystem] 开始学习课时:', lesson.title);
        return true;
    }
    
    /**
     * 完成课时学习
     */
    public completeLesson(score: number, accuracy: number, mistakes: any[] = []): void {
        if (!this._currentLesson || !this._currentLearningRecord) {
            console.warn('[DialectClassroomSystem] 没有正在进行的课时学习');
            return;
        }
        
        const now = Date.now();
        const timeSpent = now - this._currentLearningRecord.startTime;
        
        // 更新学习记录
        this._currentLearningRecord.endTime = now;
        this._currentLearningRecord.score = score;
        this._currentLearningRecord.accuracy = accuracy;
        this._currentLearningRecord.mistakes = mistakes;
        this._currentLearningRecord.timeSpent = timeSpent;
        this._currentLearningRecord.isCompleted = true;
        
        // 更新课时进度
        this._currentLesson.progress.isCompleted = true;
        this._currentLesson.progress.score = score;
        this._currentLesson.progress.attempts++;
        this._currentLesson.progress.bestScore = Math.max(this._currentLesson.progress.bestScore, score);
        this._currentLesson.progress.timeSpent += timeSpent;
        this._currentLesson.progress.lastAttemptTime = now;
        
        // 保存学习记录
        this._learningRecords.push(this._currentLearningRecord);
        
        // 更新课程进度
        this.updateCourseProgress();
        
        // 更新统计数据
        this.updateLearningStats();
        
        // 检查解锁新内容
        this.checkUnlockContent();
        
        // 检查成就
        this.checkAchievements();
        
        // 发送事件
        this.emitEvent('lesson_completed', {
            lesson: this._currentLesson,
            record: this._currentLearningRecord
        });
        
        console.log(`[DialectClassroomSystem] 完成课时学习: ${this._currentLesson.title}, 分数: ${score}, 准确率: ${accuracy}%`);
        
        // 清空当前状态
        this._currentLesson = null;
        this._currentLearningRecord = null;
    }
    
    /**
     * 获取学习统计
     */
    public getLearningStats(): ILearningStats {
        return { ...this._learningStats };
    }
    
    /**
     * 获取学习记录
     */
    public getLearningRecords(filter?: {
        courseId?: string;
        lessonId?: string;
        limit?: number;
    }): ILearningRecord[] {
        let records = [...this._learningRecords];
        
        if (filter) {
            if (filter.courseId) {
                records = records.filter(record => record.courseId === filter.courseId);
            }
            if (filter.lessonId) {
                records = records.filter(record => record.lessonId === filter.lessonId);
            }
            if (filter.limit) {
                records = records.slice(-filter.limit);
            }
        }
        
        return records.sort((a, b) => b.startTime - a.startTime);
    }
    
    /**
     * 获取推荐课程
     */
    public getRecommendedCourses(limit: number = 5): ICourse[] {
        const courses = this.getCourses({ onlyUnlocked: true });
        
        // 根据学习进度和偏好推荐课程
        const recommendations = courses
            .filter(course => course.progress.status !== LearningProgressStatus.COMPLETED)
            .sort((a, b) => {
                // 优先推荐进行中的课程
                if (a.progress.status === LearningProgressStatus.IN_PROGRESS && 
                    b.progress.status !== LearningProgressStatus.IN_PROGRESS) {
                    return -1;
                }
                if (b.progress.status === LearningProgressStatus.IN_PROGRESS && 
                    a.progress.status !== LearningProgressStatus.IN_PROGRESS) {
                    return 1;
                }
                
                // 然后按难度排序
                return a.difficulty.localeCompare(b.difficulty);
            });
        
        return recommendations.slice(0, limit);
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化课程数据
     */
    private initializeCourses(): void {
        // 这里应该从服务器或本地配置加载课程数据
        // 示例数据
        const sampleCourses: ICourse[] = [
            {
                courseId: 'course_001',
                title: '四川话入门',
                description: '学习四川话的基础发音和常用词汇',
                dialect: '四川话',
                difficulty: LearningDifficulty.BEGINNER,
                mode: LearningMode.BASIC,
                totalLessons: 10,
                estimatedTime: 120,
                prerequisites: [],
                tags: ['基础', '发音', '词汇'],
                isUnlocked: true,
                progress: {
                    completedLessons: 0,
                    totalScore: 0,
                    accuracy: 0,
                    timeSpent: 0,
                    status: LearningProgressStatus.NOT_STARTED
                }
            },
            {
                courseId: 'course_002',
                title: '广东话进阶',
                description: '深入学习广东话的语法和表达方式',
                dialect: '广东话',
                difficulty: LearningDifficulty.INTERMEDIATE,
                mode: LearningMode.ADVANCED,
                totalLessons: 15,
                estimatedTime: 180,
                prerequisites: ['course_001'],
                tags: ['语法', '表达', '进阶'],
                isUnlocked: false,
                progress: {
                    completedLessons: 0,
                    totalScore: 0,
                    accuracy: 0,
                    timeSpent: 0,
                    status: LearningProgressStatus.NOT_STARTED
                }
            }
        ];
        
        sampleCourses.forEach(course => {
            this._courses.set(course.courseId, course);
        });
        
        console.log(`[DialectClassroomSystem] 加载了 ${sampleCourses.length} 个课程`);
    }
    
    /**
     * 加载学习进度
     */
    private loadLearningProgress(): void {
        // 这里应该从本地存储或服务器加载学习进度
        console.log('[DialectClassroomSystem] 学习进度加载完成');
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            // 可以监听其他系统的事件
        }
    }
    
    /**
     * 检查前置课程
     */
    private checkPrerequisites(course: ICourse): boolean {
        for (const prerequisiteId of course.prerequisites) {
            const prerequisite = this._courses.get(prerequisiteId);
            if (!prerequisite || prerequisite.progress.status !== LearningProgressStatus.COMPLETED) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 更新课程进度
     */
    private updateCourseProgress(): void {
        if (!this._currentCourse) return;
        
        const lessons = this.getCourseLessons(this._currentCourse.courseId);
        const completedLessons = lessons.filter(lesson => lesson.progress.isCompleted).length;
        
        this._currentCourse.progress.completedLessons = completedLessons;
        this._currentCourse.progress.totalScore = lessons.reduce((sum, lesson) => sum + lesson.progress.score, 0);
        this._currentCourse.progress.accuracy = lessons.length > 0 ? 
            lessons.reduce((sum, lesson) => sum + lesson.progress.score, 0) / lessons.length : 0;
        this._currentCourse.progress.timeSpent = lessons.reduce((sum, lesson) => sum + lesson.progress.timeSpent, 0);
        
        // 检查课程是否完成
        if (completedLessons === this._currentCourse.totalLessons) {
            this._currentCourse.progress.status = LearningProgressStatus.COMPLETED;
            this._learningStats.totalCoursesCompleted++;
            
            this.emitEvent('course_completed', { course: this._currentCourse });
        }
    }
    
    /**
     * 更新学习统计
     */
    private updateLearningStats(): void {
        this._learningStats.totalLessonsCompleted++;
        this._learningStats.totalTimeSpent += this._currentLearningRecord.timeSpent;
        this._learningStats.totalScore += this._currentLearningRecord.score;
        this._learningStats.lastStudyDate = Date.now();
        
        // 计算平均准确率
        const totalRecords = this._learningRecords.length;
        if (totalRecords > 0) {
            this._learningStats.averageAccuracy = 
                this._learningRecords.reduce((sum, record) => sum + record.accuracy, 0) / totalRecords;
        }
        
        // 更新学习的方言列表
        const currentDialect = this._currentCourse?.dialect;
        if (currentDialect && !this._learningStats.dialectsLearned.includes(currentDialect)) {
            this._learningStats.dialectsLearned.push(currentDialect);
        }
    }
    
    /**
     * 检查解锁新内容
     */
    private checkUnlockContent(): void {
        // 解锁下一个课时
        if (this._currentLesson) {
            const lessons = this.getCourseLessons(this._currentLesson.courseId);
            const currentIndex = lessons.findIndex(lesson => lesson.lessonId === this._currentLesson.lessonId);
            
            if (currentIndex >= 0 && currentIndex < lessons.length - 1) {
                const nextLesson = lessons[currentIndex + 1];
                if (!nextLesson.isUnlocked) {
                    nextLesson.isUnlocked = true;
                    this.emitEvent('content_unlocked', { type: 'lesson', content: nextLesson });
                }
            }
        }
        
        // 检查解锁新课程
        this.checkUnlockCourses();
    }
    
    /**
     * 检查解锁新课程
     */
    private checkUnlockCourses(): void {
        for (const course of this._courses.values()) {
            if (!course.isUnlocked && this.checkPrerequisites(course)) {
                course.isUnlocked = true;
                this.emitEvent('content_unlocked', { type: 'course', content: course });
            }
        }
    }
    
    /**
     * 检查成就
     */
    private checkAchievements(): void {
        // 这里可以实现各种成就检查逻辑
        const achievements = [];
        
        // 首次完成课时
        if (this._learningStats.totalLessonsCompleted === 1) {
            achievements.push('first_lesson');
        }
        
        // 完成10个课时
        if (this._learningStats.totalLessonsCompleted === 10) {
            achievements.push('lesson_master');
        }
        
        // 学习3种方言
        if (this._learningStats.dialectsLearned.length === 3) {
            achievements.push('dialect_explorer');
        }
        
        // 发送成就事件
        achievements.forEach(achievement => {
            if (!this._learningStats.achievements.includes(achievement)) {
                this._learningStats.achievements.push(achievement);
                this.emitEvent('achievement_unlocked', { achievement });
            }
        });
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`learning_${eventName}`, data);
        }
    }
    
    /**
     * 生成记录ID
     */
    private generateRecordId(): string {
        return `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取当前用户ID
     */
    private getCurrentUserId(): string {
        // 这里应该从用户管理器获取当前用户ID
        return 'user_' + Date.now();
    }
}
