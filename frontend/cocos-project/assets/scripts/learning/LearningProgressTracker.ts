import { _decorator, Component } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { DialectClassroomSystem, ICourse, ILesson, ILearningRecord, ILearningStats } from './DialectClassroomSystem';
import { TrainingCampSystem, ITrainingRecord } from './TrainingCampSystem';

const { ccclass } = _decorator;

/**
 * 学习目标类型
 */
export enum LearningGoalType {
    DAILY_STUDY = 'daily_study',           // 每日学习
    WEEKLY_LESSONS = 'weekly_lessons',     // 周学习课时
    MONTHLY_SCORE = 'monthly_score',       // 月度分数
    ACCURACY_TARGET = 'accuracy_target',   // 准确率目标
    STREAK_DAYS = 'streak_days',          // 连续学习天数
    COURSE_COMPLETION = 'course_completion' // 课程完成
}

/**
 * 学习目标
 */
export interface ILearningGoal {
    goalId: string;
    type: LearningGoalType;
    title: string;
    description: string;
    target: number;           // 目标值
    current: number;          // 当前进度
    deadline: number;         // 截止时间
    isCompleted: boolean;
    reward: {
        points: number;
        items: string[];
        title?: string;
    };
    createdTime: number;
    completedTime?: number;
}

/**
 * 学习成就
 */
export interface ILearningAchievement {
    achievementId: string;
    title: string;
    description: string;
    icon: string;
    category: 'learning' | 'accuracy' | 'speed' | 'social' | 'special';
    condition: {
        type: string;
        value: number;
        operator: 'gte' | 'lte' | 'eq';
    };
    reward: {
        points: number;
        items: string[];
        title?: string;
    };
    isUnlocked: boolean;
    unlockedTime?: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * 学习习惯分析
 */
export interface ILearningHabit {
    preferredStudyTime: number[];    // 偏好学习时间段
    averageSessionDuration: number;  // 平均学习时长
    studyFrequency: number;         // 学习频率（次/周）
    preferredDifficulty: string;    // 偏好难度
    strongDialects: string[];       // 擅长方言
    weakDialects: string[];         // 薄弱方言
    learningStreak: number;         // 连续学习天数
    totalStudyDays: number;         // 总学习天数
}

/**
 * 学习建议
 */
export interface ILearningRecommendation {
    type: 'course' | 'practice' | 'review' | 'challenge';
    title: string;
    description: string;
    reason: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: number;
    targetId?: string;          // 推荐的课程/训练营ID
    createdTime: number;
}

/**
 * 学习进度追踪系统
 * 负责学习进度的统计、分析、目标管理和个性化推荐
 */
@ccclass('LearningProgressTracker')
export class LearningProgressTracker extends Component {
    private static _instance: LearningProgressTracker = null;
    
    // 学习目标和成就
    private _learningGoals: Map<string, ILearningGoal> = new Map();
    private _achievements: Map<string, ILearningAchievement> = new Map();
    
    // 学习分析数据
    private _learningHabit: ILearningHabit = {
        preferredStudyTime: [],
        averageSessionDuration: 0,
        studyFrequency: 0,
        preferredDifficulty: 'medium',
        strongDialects: [],
        weakDialects: [],
        learningStreak: 0,
        totalStudyDays: 0
    };
    
    // 推荐系统
    private _recommendations: ILearningRecommendation[] = [];
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    private _dialectClassroomSystem: DialectClassroomSystem = null;
    private _trainingCampSystem: TrainingCampSystem = null;
    
    public static getInstance(): LearningProgressTracker {
        return this._instance;
    }
    
    protected onLoad(): void {
        LearningProgressTracker._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        this._dialectClassroomSystem = DialectClassroomSystem.getInstance();
        this._trainingCampSystem = TrainingCampSystem.getInstance();
        
        this.initializeGoalsAndAchievements();
        this.loadProgressData();
        this.registerEventListeners();
        this.startDailyAnalysis();
        
        console.log('[LearningProgressTracker] 学习进度追踪系统初始化完成');
    }
    
    protected onDestroy(): void {
        LearningProgressTracker._instance = null;
    }
    
    /**
     * 获取学习统计概览
     */
    public getLearningOverview(): {
        stats: ILearningStats;
        habit: ILearningHabit;
        activeGoals: ILearningGoal[];
        recentAchievements: ILearningAchievement[];
        recommendations: ILearningRecommendation[];
    } {
        const stats = this._dialectClassroomSystem?.getLearningStats() || {
            totalCoursesStarted: 0,
            totalCoursesCompleted: 0,
            totalLessonsCompleted: 0,
            totalTimeSpent: 0,
            averageAccuracy: 0,
            totalScore: 0,
            dialectsLearned: [],
            achievements: [],
            streakDays: 0,
            lastStudyDate: 0
        };
        
        const activeGoals = Array.from(this._learningGoals.values())
            .filter(goal => !goal.isCompleted && goal.deadline > Date.now())
            .sort((a, b) => a.deadline - b.deadline);
        
        const recentAchievements = Array.from(this._achievements.values())
            .filter(achievement => achievement.isUnlocked)
            .sort((a, b) => (b.unlockedTime || 0) - (a.unlockedTime || 0))
            .slice(0, 5);
        
        return {
            stats,
            habit: { ...this._learningHabit },
            activeGoals: activeGoals.slice(0, 5),
            recentAchievements,
            recommendations: this._recommendations.slice(0, 3)
        };
    }
    
    /**
     * 创建学习目标
     */
    public createLearningGoal(
        type: LearningGoalType,
        target: number,
        deadline: number,
        customTitle?: string
    ): ILearningGoal {
        const goalId = this.generateGoalId();
        const goalConfig = this.getGoalConfig(type);
        
        const goal: ILearningGoal = {
            goalId,
            type,
            title: customTitle || goalConfig.title,
            description: goalConfig.description.replace('{target}', target.toString()),
            target,
            current: 0,
            deadline,
            isCompleted: false,
            reward: goalConfig.reward,
            createdTime: Date.now()
        };
        
        this._learningGoals.set(goalId, goal);
        
        // 发送事件
        this.emitEvent('goal_created', { goal });
        
        console.log('[LearningProgressTracker] 创建学习目标:', goal.title);
        return goal;
    }
    
    /**
     * 更新学习目标进度
     */
    public updateGoalProgress(type: LearningGoalType, increment: number = 1): void {
        const activeGoals = Array.from(this._learningGoals.values())
            .filter(goal => goal.type === type && !goal.isCompleted);
        
        for (const goal of activeGoals) {
            goal.current = Math.min(goal.current + increment, goal.target);
            
            // 检查是否完成
            if (goal.current >= goal.target && !goal.isCompleted) {
                this.completeGoal(goal);
            }
        }
    }
    
    /**
     * 获取所有学习目标
     */
    public getLearningGoals(filter?: {
        type?: LearningGoalType;
        isCompleted?: boolean;
        isActive?: boolean;
    }): ILearningGoal[] {
        let goals = Array.from(this._learningGoals.values());
        
        if (filter) {
            if (filter.type) {
                goals = goals.filter(goal => goal.type === filter.type);
            }
            if (filter.isCompleted !== undefined) {
                goals = goals.filter(goal => goal.isCompleted === filter.isCompleted);
            }
            if (filter.isActive) {
                goals = goals.filter(goal => !goal.isCompleted && goal.deadline > Date.now());
            }
        }
        
        return goals.sort((a, b) => b.createdTime - a.createdTime);
    }
    
    /**
     * 获取所有成就
     */
    public getAchievements(filter?: {
        category?: string;
        isUnlocked?: boolean;
        rarity?: string;
    }): ILearningAchievement[] {
        let achievements = Array.from(this._achievements.values());
        
        if (filter) {
            if (filter.category) {
                achievements = achievements.filter(achievement => achievement.category === filter.category);
            }
            if (filter.isUnlocked !== undefined) {
                achievements = achievements.filter(achievement => achievement.isUnlocked === filter.isUnlocked);
            }
            if (filter.rarity) {
                achievements = achievements.filter(achievement => achievement.rarity === filter.rarity);
            }
        }
        
        return achievements.sort((a, b) => {
            if (a.isUnlocked !== b.isUnlocked) {
                return b.isUnlocked ? 1 : -1;
            }
            return (b.unlockedTime || 0) - (a.unlockedTime || 0);
        });
    }
    
    /**
     * 获取学习建议
     */
    public getLearningRecommendations(limit: number = 5): ILearningRecommendation[] {
        return this._recommendations
            .sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            })
            .slice(0, limit);
    }
    
    /**
     * 分析学习习惯
     */
    public analyzeLearningHabit(): ILearningHabit {
        const learningRecords = this._dialectClassroomSystem?.getLearningRecords() || [];
        const trainingRecords = this._trainingCampSystem?.getUserTrainingRecords() || [];
        
        if (learningRecords.length === 0 && trainingRecords.length === 0) {
            return this._learningHabit;
        }
        
        // 分析偏好学习时间
        this.analyzePreferredStudyTime(learningRecords, trainingRecords);
        
        // 分析学习时长
        this.analyzeSessionDuration(learningRecords, trainingRecords);
        
        // 分析学习频率
        this.analyzeStudyFrequency(learningRecords, trainingRecords);
        
        // 分析方言强弱项
        this.analyzeDialectStrengths(learningRecords);
        
        // 分析学习连续性
        this.analyzeLearningStreak(learningRecords, trainingRecords);
        
        console.log('[LearningProgressTracker] 学习习惯分析完成:', this._learningHabit);
        return { ...this._learningHabit };
    }
    
    /**
     * 生成个性化推荐
     */
    public generateRecommendations(): ILearningRecommendation[] {
        this._recommendations = [];
        
        const stats = this._dialectClassroomSystem?.getLearningStats();
        const habit = this._learningHabit;
        
        if (!stats) return this._recommendations;
        
        // 推荐薄弱方言的练习
        if (habit.weakDialects.length > 0) {
            this._recommendations.push({
                type: 'practice',
                title: `加强${habit.weakDialects[0]}练习`,
                description: `针对你的薄弱项${habit.weakDialects[0]}进行专项练习`,
                reason: '基于学习数据分析，这是你需要重点提升的方言',
                priority: 'high',
                estimatedTime: 20,
                createdTime: Date.now()
            });
        }
        
        // 推荐新课程
        const availableCourses = this._dialectClassroomSystem?.getCourses({ onlyUnlocked: true }) || [];
        const notStartedCourses = availableCourses.filter(course => 
            course.progress.status === 'not_started'
        );
        
        if (notStartedCourses.length > 0) {
            const recommendedCourse = notStartedCourses[0];
            this._recommendations.push({
                type: 'course',
                title: `开始学习${recommendedCourse.title}`,
                description: recommendedCourse.description,
                reason: '根据你的学习进度，这门课程很适合你',
                priority: 'medium',
                estimatedTime: recommendedCourse.estimatedTime,
                targetId: recommendedCourse.courseId,
                createdTime: Date.now()
            });
        }
        
        // 推荐复习
        if (stats.averageAccuracy < 80) {
            this._recommendations.push({
                type: 'review',
                title: '复习之前的错题',
                description: '回顾和练习之前答错的题目，提高准确率',
                reason: `你的平均准确率为${stats.averageAccuracy.toFixed(1)}%，需要加强练习`,
                priority: 'high',
                estimatedTime: 15,
                createdTime: Date.now()
            });
        }
        
        // 推荐挑战
        const activeCamps = this._trainingCampSystem?.getTrainingCamps({ status: 'active' }) || [];
        if (activeCamps.length > 0 && stats.averageAccuracy >= 70) {
            const recommendedCamp = activeCamps[0];
            this._recommendations.push({
                type: 'challenge',
                title: `参加${recommendedCamp.title}`,
                description: recommendedCamp.description,
                reason: '你的学习表现不错，可以尝试挑战训练营',
                priority: 'medium',
                estimatedTime: 30,
                targetId: recommendedCamp.campId,
                createdTime: Date.now()
            });
        }
        
        console.log(`[LearningProgressTracker] 生成了 ${this._recommendations.length} 条推荐`);
        return [...this._recommendations];
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化目标和成就
     */
    private initializeGoalsAndAchievements(): void {
        // 初始化预设成就
        const presetAchievements: ILearningAchievement[] = [
            {
                achievementId: 'first_lesson',
                title: '初学者',
                description: '完成第一个课时',
                icon: 'first_lesson_icon',
                category: 'learning',
                condition: { type: 'lessons_completed', value: 1, operator: 'gte' },
                reward: { points: 50, items: ['beginner_badge'] },
                isUnlocked: false,
                rarity: 'common'
            },
            {
                achievementId: 'accuracy_master',
                title: '精准大师',
                description: '达到90%以上的平均准确率',
                icon: 'accuracy_icon',
                category: 'accuracy',
                condition: { type: 'average_accuracy', value: 90, operator: 'gte' },
                reward: { points: 200, items: ['accuracy_badge'], title: '精准大师' },
                isUnlocked: false,
                rarity: 'rare'
            },
            {
                achievementId: 'speed_demon',
                title: '闪电侠',
                description: '平均答题时间少于10秒',
                icon: 'speed_icon',
                category: 'speed',
                condition: { type: 'average_answer_time', value: 10, operator: 'lte' },
                reward: { points: 150, items: ['speed_badge'] },
                isUnlocked: false,
                rarity: 'rare'
            },
            {
                achievementId: 'dialect_explorer',
                title: '方言探索者',
                description: '学习3种不同的方言',
                icon: 'explorer_icon',
                category: 'learning',
                condition: { type: 'dialects_learned', value: 3, operator: 'gte' },
                reward: { points: 300, items: ['explorer_badge'], title: '方言探索者' },
                isUnlocked: false,
                rarity: 'epic'
            }
        ];
        
        presetAchievements.forEach(achievement => {
            this._achievements.set(achievement.achievementId, achievement);
        });
        
        console.log(`[LearningProgressTracker] 初始化了 ${presetAchievements.length} 个成就`);
    }
    
    /**
     * 加载进度数据
     */
    private loadProgressData(): void {
        // 这里应该从本地存储或服务器加载进度数据
        console.log('[LearningProgressTracker] 进度数据加载完成');
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.on('learning_lesson_completed', this.onLessonCompleted.bind(this));
            eventManager.on('learning_course_completed', this.onCourseCompleted.bind(this));
            eventManager.on('training_camp_challenge_completed', this.onTrainingCompleted.bind(this));
        }
    }
    
    /**
     * 课时完成事件处理
     */
    private onLessonCompleted(data: any): void {
        this.updateGoalProgress(LearningGoalType.DAILY_STUDY);
        this.updateGoalProgress(LearningGoalType.WEEKLY_LESSONS);
        
        this.checkAchievements();
        this.updateLearningHabit();
    }
    
    /**
     * 课程完成事件处理
     */
    private onCourseCompleted(data: any): void {
        this.updateGoalProgress(LearningGoalType.COURSE_COMPLETION);
        this.checkAchievements();
    }
    
    /**
     * 训练营完成事件处理
     */
    private onTrainingCompleted(data: any): void {
        this.checkAchievements();
        this.updateLearningHabit();
    }
    
    /**
     * 完成目标
     */
    private completeGoal(goal: ILearningGoal): void {
        goal.isCompleted = true;
        goal.completedTime = Date.now();
        
        // 发放奖励
        this.grantReward(goal.reward);
        
        // 发送事件
        this.emitEvent('goal_completed', { goal });
        
        console.log('[LearningProgressTracker] 完成学习目标:', goal.title);
    }
    
    /**
     * 检查成就
     */
    private checkAchievements(): void {
        const stats = this._dialectClassroomSystem?.getLearningStats();
        if (!stats) return;
        
        for (const achievement of this._achievements.values()) {
            if (achievement.isUnlocked) continue;
            
            let shouldUnlock = false;
            const condition = achievement.condition;
            
            switch (condition.type) {
                case 'lessons_completed':
                    shouldUnlock = this.checkCondition(stats.totalLessonsCompleted, condition);
                    break;
                case 'average_accuracy':
                    shouldUnlock = this.checkCondition(stats.averageAccuracy, condition);
                    break;
                case 'dialects_learned':
                    shouldUnlock = this.checkCondition(stats.dialectsLearned.length, condition);
                    break;
            }
            
            if (shouldUnlock) {
                this.unlockAchievement(achievement);
            }
        }
    }
    
    /**
     * 检查条件
     */
    private checkCondition(value: number, condition: any): boolean {
        switch (condition.operator) {
            case 'gte': return value >= condition.value;
            case 'lte': return value <= condition.value;
            case 'eq': return value === condition.value;
            default: return false;
        }
    }
    
    /**
     * 解锁成就
     */
    private unlockAchievement(achievement: ILearningAchievement): void {
        achievement.isUnlocked = true;
        achievement.unlockedTime = Date.now();
        
        // 发放奖励
        this.grantReward(achievement.reward);
        
        // 发送事件
        this.emitEvent('achievement_unlocked', { achievement });
        
        console.log('[LearningProgressTracker] 解锁成就:', achievement.title);
    }
    
    /**
     * 发放奖励
     */
    private grantReward(reward: any): void {
        console.log('[LearningProgressTracker] 发放奖励:', reward);
        // 这里应该实际发放奖励给用户
    }
    
    /**
     * 更新学习习惯
     */
    private updateLearningHabit(): void {
        // 定期更新学习习惯分析
        this.analyzeLearningHabit();
        this.generateRecommendations();
    }
    
    /**
     * 开始每日分析
     */
    private startDailyAnalysis(): void {
        // 每天凌晨2点进行分析
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(2, 0, 0, 0);
        
        const timeUntilAnalysis = tomorrow.getTime() - now.getTime();
        
        setTimeout(() => {
            this.performDailyAnalysis();
            
            // 设置每日定时器
            setInterval(() => {
                this.performDailyAnalysis();
            }, 24 * 60 * 60 * 1000);
        }, timeUntilAnalysis);
    }
    
    /**
     * 执行每日分析
     */
    private performDailyAnalysis(): void {
        console.log('[LearningProgressTracker] 执行每日分析');
        
        this.analyzeLearningHabit();
        this.generateRecommendations();
        this.createDailyGoals();
        
        // 发送事件
        this.emitEvent('daily_analysis_completed', {
            habit: this._learningHabit,
            recommendations: this._recommendations
        });
    }
    
    /**
     * 创建每日目标
     */
    private createDailyGoals(): void {
        // 检查是否已有今日目标
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        
        const hasDailyGoal = Array.from(this._learningGoals.values()).some(goal =>
            goal.type === LearningGoalType.DAILY_STUDY &&
            !goal.isCompleted &&
            goal.deadline > Date.now()
        );
        
        if (!hasDailyGoal) {
            this.createLearningGoal(LearningGoalType.DAILY_STUDY, 1, today.getTime());
        }
    }
    
    /**
     * 分析偏好学习时间
     */
    private analyzePreferredStudyTime(learningRecords: ILearningRecord[], trainingRecords: ITrainingRecord[]): void {
        const timeSlots = new Array(24).fill(0);
        
        [...learningRecords, ...trainingRecords].forEach(record => {
            const hour = new Date(record.startTime).getHours();
            timeSlots[hour]++;
        });
        
        // 找出最活跃的时间段
        const maxCount = Math.max(...timeSlots);
        this._learningHabit.preferredStudyTime = timeSlots
            .map((count, hour) => ({ hour, count }))
            .filter(item => item.count >= maxCount * 0.7)
            .map(item => item.hour);
    }
    
    /**
     * 分析学习时长
     */
    private analyzeSessionDuration(learningRecords: ILearningRecord[], trainingRecords: ITrainingRecord[]): void {
        const allRecords = [...learningRecords, ...trainingRecords];
        if (allRecords.length === 0) return;
        
        const totalDuration = allRecords.reduce((sum, record) => {
            const duration = record.endTime - record.startTime;
            return sum + duration;
        }, 0);
        
        this._learningHabit.averageSessionDuration = totalDuration / allRecords.length;
    }
    
    /**
     * 分析学习频率
     */
    private analyzeStudyFrequency(learningRecords: ILearningRecord[], trainingRecords: ITrainingRecord[]): void {
        const allRecords = [...learningRecords, ...trainingRecords];
        if (allRecords.length === 0) return;
        
        const studyDates = new Set();
        allRecords.forEach(record => {
            const date = new Date(record.startTime).toDateString();
            studyDates.add(date);
        });
        
        const daysSinceFirstStudy = allRecords.length > 0 ? 
            (Date.now() - Math.min(...allRecords.map(r => r.startTime))) / (24 * 60 * 60 * 1000) : 0;
        
        this._learningHabit.studyFrequency = daysSinceFirstStudy > 0 ? 
            (studyDates.size / daysSinceFirstStudy) * 7 : 0;
        this._learningHabit.totalStudyDays = studyDates.size;
    }
    
    /**
     * 分析方言强弱项
     */
    private analyzeDialectStrengths(learningRecords: ILearningRecord[]): void {
        const dialectStats = new Map<string, { correct: number; total: number }>();
        
        learningRecords.forEach(record => {
            // 这里需要从课程信息中获取方言类型
            // 简化实现
            const dialect = '四川话'; // 应该从实际数据获取
            
            if (!dialectStats.has(dialect)) {
                dialectStats.set(dialect, { correct: 0, total: 0 });
            }
            
            const stats = dialectStats.get(dialect);
            stats.total++;
            if (record.accuracy > 70) {
                stats.correct++;
            }
        });
        
        const strongDialects = [];
        const weakDialects = [];
        
        for (const [dialect, stats] of dialectStats) {
            const accuracy = stats.total > 0 ? stats.correct / stats.total : 0;
            if (accuracy >= 0.8) {
                strongDialects.push(dialect);
            } else if (accuracy < 0.6) {
                weakDialects.push(dialect);
            }
        }
        
        this._learningHabit.strongDialects = strongDialects;
        this._learningHabit.weakDialects = weakDialects;
    }
    
    /**
     * 分析学习连续性
     */
    private analyzeLearningStreak(learningRecords: ILearningRecord[], trainingRecords: ITrainingRecord[]): void {
        const allRecords = [...learningRecords, ...trainingRecords];
        if (allRecords.length === 0) return;
        
        const studyDates = new Set();
        allRecords.forEach(record => {
            const date = new Date(record.startTime).toDateString();
            studyDates.add(date);
        });
        
        const sortedDates = Array.from(studyDates).sort();
        let currentStreak = 0;
        let maxStreak = 0;
        
        for (let i = sortedDates.length - 1; i >= 0; i--) {
            const currentDate = new Date(sortedDates[i]);
            const expectedDate = new Date();
            expectedDate.setDate(expectedDate.getDate() - currentStreak);
            
            if (currentDate.toDateString() === expectedDate.toDateString()) {
                currentStreak++;
                maxStreak = Math.max(maxStreak, currentStreak);
            } else {
                break;
            }
        }
        
        this._learningHabit.learningStreak = currentStreak;
    }
    
    /**
     * 获取目标配置
     */
    private getGoalConfig(type: LearningGoalType): any {
        const configs = {
            [LearningGoalType.DAILY_STUDY]: {
                title: '每日学习',
                description: '今天完成{target}个课时的学习',
                reward: { points: 50, items: ['daily_badge'] }
            },
            [LearningGoalType.WEEKLY_LESSONS]: {
                title: '周学习目标',
                description: '本周完成{target}个课时的学习',
                reward: { points: 200, items: ['weekly_badge'] }
            },
            [LearningGoalType.MONTHLY_SCORE]: {
                title: '月度分数目标',
                description: '本月累计获得{target}分',
                reward: { points: 500, items: ['monthly_badge'] }
            },
            [LearningGoalType.ACCURACY_TARGET]: {
                title: '准确率目标',
                description: '达到{target}%的平均准确率',
                reward: { points: 300, items: ['accuracy_badge'] }
            },
            [LearningGoalType.STREAK_DAYS]: {
                title: '连续学习',
                description: '连续学习{target}天',
                reward: { points: 400, items: ['streak_badge'] }
            },
            [LearningGoalType.COURSE_COMPLETION]: {
                title: '课程完成',
                description: '完成{target}门课程',
                reward: { points: 600, items: ['completion_badge'] }
            }
        };
        
        return configs[type] || configs[LearningGoalType.DAILY_STUDY];
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`progress_${eventName}`, data);
        }
    }
    
    /**
     * 生成目标ID
     */
    private generateGoalId(): string {
        return `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
