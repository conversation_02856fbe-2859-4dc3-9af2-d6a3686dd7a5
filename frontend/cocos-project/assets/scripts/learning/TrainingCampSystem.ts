import { _decorator, Component } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { IQuestionData } from '../data/GameData';

const { ccclass } = _decorator;

/**
 * 训练营类型
 */
export enum TrainingCampType {
    DAILY_CHALLENGE = 'daily_challenge',    // 每日挑战
    WEEKLY_TOURNAMENT = 'weekly_tournament', // 周赛
    SPECIAL_EVENT = 'special_event',        // 特殊活动
    SKILL_TRAINING = 'skill_training'       // 技能训练
}

/**
 * 训练营状态
 */
export enum TrainingCampStatus {
    UPCOMING = 'upcoming',      // 即将开始
    ACTIVE = 'active',         // 进行中
    COMPLETED = 'completed',   // 已结束
    EXPIRED = 'expired'        // 已过期
}

/**
 * 训练营难度
 */
export enum TrainingDifficulty {
    EASY = 'easy',
    MEDIUM = 'medium',
    HARD = 'hard',
    EXPERT = 'expert'
}

/**
 * 训练营信息
 */
export interface ITrainingCamp {
    campId: string;
    title: string;
    description: string;
    type: TrainingCampType;
    difficulty: TrainingDifficulty;
    status: TrainingCampStatus;
    
    // 时间信息
    startTime: number;
    endTime: number;
    duration: number;          // 持续时间（毫秒）
    
    // 参与信息
    maxParticipants: number;
    currentParticipants: number;
    entryFee: number;          // 参赛费用（积分）
    
    // 奖励信息
    rewards: {
        rank: number;
        points: number;
        items?: string[];
        title?: string;
    }[];
    
    // 题目信息
    totalQuestions: number;
    timeLimit: number;         // 答题时限（秒）
    passingScore: number;
    
    // 规则
    rules: string[];
    tags: string[];
    
    // 用户参与状态
    userStatus?: {
        isRegistered: boolean;
        isCompleted: boolean;
        score: number;
        rank: number;
        attempts: number;
    };
}

/**
 * 训练营参与记录
 */
export interface ITrainingRecord {
    recordId: string;
    userId: string;
    campId: string;
    startTime: number;
    endTime: number;
    score: number;
    accuracy: number;
    rank: number;
    totalParticipants: number;
    answers: {
        questionId: string;
        selectedAnswer: number;
        correctAnswer: number;
        isCorrect: boolean;
        timeSpent: number;
    }[];
    rewards: {
        points: number;
        items: string[];
        title?: string;
    };
    isCompleted: boolean;
}

/**
 * 排行榜条目
 */
export interface ILeaderboardEntry {
    userId: string;
    nickname: string;
    avatar?: string;
    score: number;
    accuracy: number;
    timeSpent: number;
    rank: number;
    isCurrentUser?: boolean;
}

/**
 * 训练营系统
 * 负责训练营的管理、参与、排行榜等功能
 */
@ccclass('TrainingCampSystem')
export class TrainingCampSystem extends Component {
    private static _instance: TrainingCampSystem = null;
    
    // 训练营数据
    private _trainingCamps: Map<string, ITrainingCamp> = new Map();
    
    // 当前参与的训练营
    private _currentCamp: ITrainingCamp = null;
    private _currentRecord: ITrainingRecord = null;
    
    // 训练记录
    private _trainingRecords: ITrainingRecord[] = [];
    
    // 排行榜缓存
    private _leaderboards: Map<string, ILeaderboardEntry[]> = new Map();
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): TrainingCampSystem {
        return this._instance;
    }
    
    protected onLoad(): void {
        TrainingCampSystem._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.initializeTrainingCamps();
        this.loadTrainingRecords();
        this.registerEventListeners();
        this.startStatusUpdateTimer();
        
        console.log('[TrainingCampSystem] 训练营系统初始化完成');
    }
    
    protected onDestroy(): void {
        TrainingCampSystem._instance = null;
    }
    
    /**
     * 获取所有训练营
     */
    public getTrainingCamps(filter?: {
        type?: TrainingCampType;
        status?: TrainingCampStatus;
        difficulty?: TrainingDifficulty;
    }): ITrainingCamp[] {
        let camps = Array.from(this._trainingCamps.values());
        
        if (filter) {
            if (filter.type) {
                camps = camps.filter(camp => camp.type === filter.type);
            }
            if (filter.status) {
                camps = camps.filter(camp => camp.status === filter.status);
            }
            if (filter.difficulty) {
                camps = camps.filter(camp => camp.difficulty === filter.difficulty);
            }
        }
        
        return camps.sort((a, b) => a.startTime - b.startTime);
    }
    
    /**
     * 获取训练营详情
     */
    public getTrainingCamp(campId: string): ITrainingCamp | null {
        return this._trainingCamps.get(campId) || null;
    }
    
    /**
     * 报名参加训练营
     */
    public async registerForCamp(campId: string): Promise<boolean> {
        const camp = this._trainingCamps.get(campId);
        if (!camp) {
            console.error('[TrainingCampSystem] 训练营不存在:', campId);
            return false;
        }
        
        if (camp.status !== TrainingCampStatus.UPCOMING && camp.status !== TrainingCampStatus.ACTIVE) {
            console.warn('[TrainingCampSystem] 训练营不在报名期:', camp.status);
            return false;
        }
        
        if (camp.currentParticipants >= camp.maxParticipants) {
            console.warn('[TrainingCampSystem] 训练营已满员');
            return false;
        }
        
        if (camp.userStatus?.isRegistered) {
            console.warn('[TrainingCampSystem] 已经报名过该训练营');
            return false;
        }
        
        // 检查积分是否足够
        if (!this.checkEntryFee(camp.entryFee)) {
            console.warn('[TrainingCampSystem] 积分不足，无法报名');
            return false;
        }
        
        try {
            // 扣除报名费
            this.deductEntryFee(camp.entryFee);
            
            // 更新训练营信息
            camp.currentParticipants++;
            if (!camp.userStatus) {
                camp.userStatus = {
                    isRegistered: false,
                    isCompleted: false,
                    score: 0,
                    rank: 0,
                    attempts: 0
                };
            }
            camp.userStatus.isRegistered = true;
            
            // 发送事件
            this.emitEvent('camp_registered', { camp });
            
            console.log('[TrainingCampSystem] 成功报名训练营:', camp.title);
            return true;
            
        } catch (error) {
            console.error('[TrainingCampSystem] 报名失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'TrainingCampSystem.registerForCamp' });
            return false;
        }
    }
    
    /**
     * 开始训练营挑战
     */
    public async startCampChallenge(campId: string): Promise<boolean> {
        const camp = this._trainingCamps.get(campId);
        if (!camp) {
            console.error('[TrainingCampSystem] 训练营不存在:', campId);
            return false;
        }
        
        if (!camp.userStatus?.isRegistered) {
            console.warn('[TrainingCampSystem] 未报名该训练营');
            return false;
        }
        
        if (camp.status !== TrainingCampStatus.ACTIVE) {
            console.warn('[TrainingCampSystem] 训练营未开始或已结束');
            return false;
        }
        
        if (camp.userStatus.isCompleted) {
            console.warn('[TrainingCampSystem] 已经完成该训练营');
            return false;
        }
        
        this._currentCamp = camp;
        
        // 创建训练记录
        this._currentRecord = {
            recordId: this.generateRecordId(),
            userId: this.getCurrentUserId(),
            campId: camp.campId,
            startTime: Date.now(),
            endTime: 0,
            score: 0,
            accuracy: 0,
            rank: 0,
            totalParticipants: camp.currentParticipants,
            answers: [],
            rewards: {
                points: 0,
                items: []
            },
            isCompleted: false
        };
        
        // 更新尝试次数
        camp.userStatus.attempts++;
        
        // 发送事件
        this.emitEvent('camp_challenge_started', { camp, record: this._currentRecord });
        
        console.log('[TrainingCampSystem] 开始训练营挑战:', camp.title);
        return true;
    }
    
    /**
     * 提交训练营答案
     */
    public submitAnswer(questionId: string, selectedAnswer: number, timeSpent: number): void {
        if (!this._currentRecord) {
            console.warn('[TrainingCampSystem] 没有正在进行的训练营挑战');
            return;
        }
        
        // 这里应该验证答案的正确性
        const isCorrect = this.validateAnswer(questionId, selectedAnswer);
        const correctAnswer = this.getCorrectAnswer(questionId);
        
        // 记录答案
        this._currentRecord.answers.push({
            questionId,
            selectedAnswer,
            correctAnswer,
            isCorrect,
            timeSpent
        });
        
        console.log(`[TrainingCampSystem] 提交答案: 题目${questionId}, 选择${selectedAnswer}, ${isCorrect ? '正确' : '错误'}`);
    }
    
    /**
     * 完成训练营挑战
     */
    public completeCampChallenge(): void {
        if (!this._currentCamp || !this._currentRecord) {
            console.warn('[TrainingCampSystem] 没有正在进行的训练营挑战');
            return;
        }
        
        const now = Date.now();
        
        // 计算成绩
        const correctAnswers = this._currentRecord.answers.filter(answer => answer.isCorrect).length;
        const totalAnswers = this._currentRecord.answers.length;
        const accuracy = totalAnswers > 0 ? (correctAnswers / totalAnswers * 100) : 0;
        const score = this.calculateScore(correctAnswers, totalAnswers, this._currentRecord.answers);
        
        // 更新记录
        this._currentRecord.endTime = now;
        this._currentRecord.score = score;
        this._currentRecord.accuracy = accuracy;
        this._currentRecord.isCompleted = true;
        
        // 更新训练营用户状态
        this._currentCamp.userStatus.isCompleted = true;
        this._currentCamp.userStatus.score = score;
        
        // 计算奖励
        const rewards = this.calculateRewards(this._currentCamp, score, accuracy);
        this._currentRecord.rewards = rewards;
        
        // 发放奖励
        this.grantRewards(rewards);
        
        // 保存记录
        this._trainingRecords.push(this._currentRecord);
        
        // 更新排行榜
        this.updateLeaderboard(this._currentCamp.campId);
        
        // 发送事件
        this.emitEvent('camp_challenge_completed', {
            camp: this._currentCamp,
            record: this._currentRecord
        });
        
        console.log(`[TrainingCampSystem] 完成训练营挑战: ${this._currentCamp.title}, 分数: ${score}, 准确率: ${accuracy}%`);
        
        // 清空当前状态
        this._currentCamp = null;
        this._currentRecord = null;
    }
    
    /**
     * 获取排行榜
     */
    public getLeaderboard(campId: string, limit: number = 50): ILeaderboardEntry[] {
        const leaderboard = this._leaderboards.get(campId) || [];
        return leaderboard.slice(0, limit);
    }
    
    /**
     * 获取用户训练记录
     */
    public getUserTrainingRecords(limit?: number): ITrainingRecord[] {
        const records = [...this._trainingRecords];
        records.sort((a, b) => b.startTime - a.startTime);
        
        return limit ? records.slice(0, limit) : records;
    }
    
    /**
     * 获取用户统计
     */
    public getUserStats(): {
        totalCampsJoined: number;
        totalCampsCompleted: number;
        totalScore: number;
        averageAccuracy: number;
        bestRank: number;
        totalRewards: number;
    } {
        const completedRecords = this._trainingRecords.filter(record => record.isCompleted);
        
        return {
            totalCampsJoined: this._trainingRecords.length,
            totalCampsCompleted: completedRecords.length,
            totalScore: completedRecords.reduce((sum, record) => sum + record.score, 0),
            averageAccuracy: completedRecords.length > 0 ? 
                completedRecords.reduce((sum, record) => sum + record.accuracy, 0) / completedRecords.length : 0,
            bestRank: completedRecords.length > 0 ? 
                Math.min(...completedRecords.map(record => record.rank)) : 0,
            totalRewards: completedRecords.reduce((sum, record) => sum + record.rewards.points, 0)
        };
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化训练营数据
     */
    private initializeTrainingCamps(): void {
        // 这里应该从服务器加载训练营数据
        // 示例数据
        const now = Date.now();
        const oneDay = 24 * 60 * 60 * 1000;
        
        const sampleCamps: ITrainingCamp[] = [
            {
                campId: 'camp_daily_001',
                title: '每日挑战 - 四川话专场',
                description: '测试你对四川话的掌握程度',
                type: TrainingCampType.DAILY_CHALLENGE,
                difficulty: TrainingDifficulty.MEDIUM,
                status: TrainingCampStatus.ACTIVE,
                startTime: now - oneDay,
                endTime: now + oneDay,
                duration: 2 * oneDay,
                maxParticipants: 1000,
                currentParticipants: 156,
                entryFee: 10,
                rewards: [
                    { rank: 1, points: 500, items: ['gold_medal'], title: '四川话大师' },
                    { rank: 2, points: 300, items: ['silver_medal'] },
                    { rank: 3, points: 200, items: ['bronze_medal'] },
                    { rank: 10, points: 100 },
                    { rank: 50, points: 50 }
                ],
                totalQuestions: 20,
                timeLimit: 30,
                passingScore: 60,
                rules: [
                    '每人只能参加一次',
                    '答题时间限制30秒/题',
                    '按分数和用时排名',
                    '需要60分以上才能获得奖励'
                ],
                tags: ['每日', '四川话', '中等难度']
            }
        ];
        
        sampleCamps.forEach(camp => {
            this._trainingCamps.set(camp.campId, camp);
        });
        
        console.log(`[TrainingCampSystem] 加载了 ${sampleCamps.length} 个训练营`);
    }
    
    /**
     * 加载训练记录
     */
    private loadTrainingRecords(): void {
        // 这里应该从本地存储或服务器加载训练记录
        console.log('[TrainingCampSystem] 训练记录加载完成');
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            // 可以监听其他系统的事件
        }
    }
    
    /**
     * 启动状态更新定时器
     */
    private startStatusUpdateTimer(): void {
        // 每分钟检查一次训练营状态
        setInterval(() => {
            this.updateCampStatuses();
        }, 60000);
    }
    
    /**
     * 更新训练营状态
     */
    private updateCampStatuses(): void {
        const now = Date.now();
        
        for (const camp of this._trainingCamps.values()) {
            const oldStatus = camp.status;
            
            if (now < camp.startTime) {
                camp.status = TrainingCampStatus.UPCOMING;
            } else if (now >= camp.startTime && now <= camp.endTime) {
                camp.status = TrainingCampStatus.ACTIVE;
            } else {
                camp.status = TrainingCampStatus.COMPLETED;
            }
            
            // 如果状态发生变化，发送事件
            if (oldStatus !== camp.status) {
                this.emitEvent('camp_status_changed', { camp, oldStatus, newStatus: camp.status });
            }
        }
    }
    
    /**
     * 检查报名费
     */
    private checkEntryFee(fee: number): boolean {
        // 这里应该检查用户的积分余额
        return true; // 简化实现
    }
    
    /**
     * 扣除报名费
     */
    private deductEntryFee(fee: number): void {
        // 这里应该扣除用户的积分
        console.log(`[TrainingCampSystem] 扣除报名费: ${fee} 积分`);
    }
    
    /**
     * 验证答案
     */
    private validateAnswer(questionId: string, selectedAnswer: number): boolean {
        // 这里应该验证答案的正确性
        return Math.random() > 0.3; // 简化实现，70%正确率
    }
    
    /**
     * 获取正确答案
     */
    private getCorrectAnswer(questionId: string): number {
        // 这里应该获取题目的正确答案
        return Math.floor(Math.random() * 4); // 简化实现
    }
    
    /**
     * 计算分数
     */
    private calculateScore(correctAnswers: number, totalAnswers: number, answers: any[]): number {
        if (totalAnswers === 0) return 0;
        
        const baseScore = (correctAnswers / totalAnswers) * 100;
        
        // 考虑答题速度奖励
        const avgTime = answers.reduce((sum, answer) => sum + answer.timeSpent, 0) / answers.length;
        const speedBonus = Math.max(0, (30 - avgTime) / 30 * 20); // 最多20分速度奖励
        
        return Math.round(baseScore + speedBonus);
    }
    
    /**
     * 计算奖励
     */
    private calculateRewards(camp: ITrainingCamp, score: number, accuracy: number): any {
        // 简化实现，根据分数给予奖励
        const baseReward = Math.floor(score / 10) * 10;
        
        return {
            points: baseReward,
            items: score >= 90 ? ['perfect_badge'] : []
        };
    }
    
    /**
     * 发放奖励
     */
    private grantRewards(rewards: any): void {
        console.log('[TrainingCampSystem] 发放奖励:', rewards);
        // 这里应该实际发放奖励给用户
    }
    
    /**
     * 更新排行榜
     */
    private updateLeaderboard(campId: string): void {
        const campRecords = this._trainingRecords.filter(record => 
            record.campId === campId && record.isCompleted
        );
        
        // 按分数和用时排序
        campRecords.sort((a, b) => {
            if (b.score !== a.score) {
                return b.score - a.score;
            }
            const aTime = a.endTime - a.startTime;
            const bTime = b.endTime - b.startTime;
            return aTime - bTime;
        });
        
        // 生成排行榜
        const leaderboard: ILeaderboardEntry[] = campRecords.map((record, index) => ({
            userId: record.userId,
            nickname: `用户${record.userId.slice(-4)}`,
            score: record.score,
            accuracy: record.accuracy,
            timeSpent: record.endTime - record.startTime,
            rank: index + 1,
            isCurrentUser: record.userId === this.getCurrentUserId()
        }));
        
        this._leaderboards.set(campId, leaderboard);
        
        // 更新用户排名
        const userEntry = leaderboard.find(entry => entry.isCurrentUser);
        if (userEntry && this._currentRecord) {
            this._currentRecord.rank = userEntry.rank;
            
            const camp = this._trainingCamps.get(campId);
            if (camp?.userStatus) {
                camp.userStatus.rank = userEntry.rank;
            }
        }
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`training_${eventName}`, data);
        }
    }
    
    /**
     * 生成记录ID
     */
    private generateRecordId(): string {
        return `training_record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取当前用户ID
     */
    private getCurrentUserId(): string {
        // 这里应该从用户管理器获取当前用户ID
        return 'user_' + Date.now();
    }
}
