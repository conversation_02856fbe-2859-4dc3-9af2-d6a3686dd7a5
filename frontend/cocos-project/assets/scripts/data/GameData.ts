import { GameDifficulty, AnswerResult } from '../constants/GameConstants';

/**
 * 游戏数据模型和接口定义
 * 家乡话猜猜猜小游戏
 */

// 题目数据接口
export interface IQuestionData {
    id: string;                    // 题目ID
    audioUrl: string;              // 音频文件URL
    audioLocalPath?: string;       // 本地缓存路径
    dialect: string;               // 方言类型（如：四川话、广东话等）
    region: string;                // 具体地区
    difficulty: GameDifficulty;    // 难度等级
    question: string;              // 题目文本
    options: string[];             // 答题选项（4个）
    correctAnswer: number;         // 正确答案索引（0-3）
    explanation?: string;          // 答案解释
    audioDuration: number;         // 音频时长（秒）
    tags: string[];                // 题目标签
    category: string;              // 题目分类
    createdAt: string;             // 创建时间
    updatedAt: string;             // 更新时间
}

// 答题记录接口
export interface IAnswerRecord {
    questionId: string;            // 题目ID
    selectedAnswer: number;        // 选择的答案索引
    correctAnswer: number;         // 正确答案索引
    result: AnswerResult;          // 答题结果
    answerTime: number;            // 答题用时（毫秒）
    score: number;                 // 获得分数
    audioPlayCount: number;        // 音频播放次数
    timestamp: number;             // 答题时间戳
}

// 游戏会话数据接口
export interface IGameSession {
    sessionId: string;             // 会话ID
    startTime: number;             // 开始时间戳
    endTime?: number;              // 结束时间戳
    questions: IQuestionData[];    // 题目列表
    answers: IAnswerRecord[];      // 答题记录
    totalScore: number;            // 总分
    correctCount: number;          // 正确数量
    comboCount: number;            // 最大连击数
    currentCombo: number;          // 当前连击数
    currentQuestionIndex: number;  // 当前题目索引
    isPaused: boolean;             // 是否暂停
    pauseTime: number;             // 暂停时长（毫秒）
    gameMode: string;              // 游戏模式
    difficulty: GameDifficulty;    // 整体难度
}

// 用户档案接口
export interface IUserProfile {
    userId: string;                // 用户ID
    nickname: string;              // 昵称
    avatar: string;                // 头像URL
    level: number;                 // 用户等级
    experience: number;            // 经验值
    totalGames: number;            // 总游戏次数
    totalScore: number;            // 总得分
    bestScore: number;             // 最高分
    bestAccuracy: number;          // 最高准确率
    favoriteDialects: string[];    // 喜欢的方言
    achievements: string[];        // 成就列表
    createdAt: string;             // 注册时间
    lastPlayTime: string;          // 最后游戏时间
}

// 游戏设置接口
export interface IGameSettings {
    soundEnabled: boolean;         // 是否启用音效
    musicEnabled: boolean;         // 是否启用背景音乐
    soundVolume: number;           // 音效音量（0-1）
    musicVolume: number;           // 音乐音量（0-1）
    autoPlayAudio: boolean;        // 是否自动播放音频
    showHints: boolean;            // 是否显示提示
    animationSpeed: number;        // 动画速度（0.5-2.0）
    language: string;              // 界面语言
    theme: string;                 // 主题风格
    difficulty: GameDifficulty;    // 默认难度
}

// 游戏统计接口
export interface IGameStats {
    totalGames: number;            // 总游戏次数
    totalQuestions: number;        // 总题目数
    correctAnswers: number;        // 正确答案数
    accuracy: number;              // 准确率
    averageScore: number;          // 平均分
    averageTime: number;           // 平均答题时间
    bestCombo: number;             // 最佳连击
    dialectStats: {                // 各方言统计
        [dialect: string]: {
            games: number;
            correct: number;
            accuracy: number;
        }
    };
    difficultyStats: {             // 各难度统计
        [key in GameDifficulty]: {
            games: number;
            correct: number;
            accuracy: number;
            averageScore: number;
        }
    };
    achievements: string[];        // 已获得成就
    playTime: number;              // 总游戏时长（毫秒）
    lastUpdated: string;           // 最后更新时间
}

// 音频缓存数据接口
export interface IAudioCacheData {
    url: string;                   // 原始URL
    localPath: string;             // 本地路径
    size: number;                  // 文件大小（字节）
    duration: number;              // 音频时长（秒）
    format: string;                // 音频格式
    cachedAt: number;              // 缓存时间戳
    lastAccessed: number;          // 最后访问时间
    accessCount: number;           // 访问次数
    isPreloaded: boolean;          // 是否预加载
}

// 排行榜数据接口
export interface ILeaderboardEntry {
    userId: string;                // 用户ID
    nickname: string;              // 昵称
    avatar: string;                // 头像
    score: number;                 // 分数
    accuracy: number;              // 准确率
    games: number;                 // 游戏次数
    rank: number;                  // 排名
    lastPlayTime: string;          // 最后游戏时间
}

// API响应基础接口（保持向后兼容）
export interface IApiResponse<T = any> {
    success: boolean;              // 是否成功
    code: number;                  // 响应码
    message: string;               // 响应消息
    data: T;                       // 响应数据
    timestamp: number;             // 时间戳
}

// 后端标准响应格式
export interface IBackendApiResponse<T = any> {
    code: number;                  // 0表示成功，非0表示失败
    message: string;               // 响应消息
    data: T;                       // 响应数据
    timestamp: number;             // 时间戳
    requestId: string;             // 请求ID
}

// 题目列表API响应
export interface IQuestionListResponse {
    questions: IQuestionData[];    // 题目列表
    total: number;                 // 总数量
    page: number;                  // 当前页码
    pageSize: number;              // 每页大小
    hasMore: boolean;              // 是否还有更多
}

// 提交答题结果请求
export interface ISubmitAnswerRequest {
    sessionId: string;             // 会话ID
    questionId: string;            // 题目ID
    selectedAnswer: number;        // 选择的答案
    answerTime: number;            // 答题时间
    audioPlayCount: number;        // 音频播放次数
}

// 游戏结果提交请求
export interface ISubmitGameResultRequest {
    sessionId: string;             // 会话ID
    answers: IAnswerRecord[];      // 答题记录
    totalScore: number;            // 总分
    correctCount: number;          // 正确数量
    gameTime: number;              // 游戏时长
    comboCount: number;            // 最大连击
}

// 事件数据接口
export interface IGameEvent {
    type: string;                  // 事件类型
    data: any;                     // 事件数据
    timestamp: number;             // 时间戳
}

// 音频播放事件数据
export interface IAudioEvent extends IGameEvent {
    type: 'audio_play' | 'audio_pause' | 'audio_stop' | 'audio_end';
    data: {
        questionId: string;
        audioUrl: string;
        playCount: number;
        currentTime: number;
        duration: number;
    };
}

// 答题事件数据
export interface IAnswerEvent extends IGameEvent {
    type: 'answer_selected' | 'answer_confirmed' | 'answer_timeout';
    data: {
        questionId: string;
        selectedAnswer: number;
        correctAnswer: number;
        answerTime: number;
        result: AnswerResult;
    };
}

// 游戏状态事件数据
export interface IGameStateEvent extends IGameEvent {
    type: 'game_start' | 'game_pause' | 'game_resume' | 'game_end';
    data: {
        sessionId: string;
        currentQuestionIndex: number;
        score: number;
        correctCount: number;
    };
}

// 错误事件数据
export interface IErrorEvent extends IGameEvent {
    type: 'error';
    data: {
        code: number;
        message: string;
        context?: any;
        stack?: string;
    };
}

// 性能监控数据接口
export interface IPerformanceData {
    fps: number;                   // 帧率
    memory: number;                // 内存使用（MB）
    audioLoadTime: number;         // 音频加载时间（毫秒）
    sceneLoadTime: number;         // 场景加载时间（毫秒）
    renderTime: number;            // 渲染时间（毫秒）
    timestamp: number;             // 时间戳
}

// 缓存项接口
export interface ICacheItem<T = any> {
    key: string;                   // 缓存键
    value: T;                      // 缓存值
    expiredAt: number;             // 过期时间戳
    size: number;                  // 数据大小（字节）
    createdAt: number;             // 创建时间戳
    accessedAt: number;            // 最后访问时间戳
    accessCount: number;           // 访问次数
}

// 网络状态接口
export interface INetworkStatus {
    isOnline: boolean;             // 是否在线
    type: string;                  // 网络类型（wifi, 4g, 3g, 2g, none）
    speed: number;                 // 网速（KB/s）
    quality: 'excellent' | 'good' | 'fair' | 'poor'; // 网络质量
}

// 设备信息接口
export interface IDeviceInfo {
    platform: string;             // 平台（wechatgame, web）
    system: string;                // 操作系统
    version: string;               // 系统版本
    model: string;                 // 设备型号
    brand: string;                 // 设备品牌
    screenWidth: number;           // 屏幕宽度
    screenHeight: number;          // 屏幕高度
    pixelRatio: number;            // 像素比
    language: string;              // 系统语言
    wechatVersion?: string;        // 微信版本
    supportedAudioFormats: string[]; // 支持的音频格式
}

// 本地化文本接口
export interface ILocalizationData {
    [key: string]: string | ILocalizationData;
}

// 配置数据接口
export interface IGameConfig {
    version: string;               // 配置版本
    questions: IQuestionData[];    // 题目配置
    dialects: string[];            // 支持的方言列表
    regions: string[];             // 支持的地区列表
    achievements: any[];           // 成就配置
    levels: any[];                 // 等级配置
    localization: {                // 本地化配置
        [language: string]: ILocalizationData;
    };
    features: {                    // 功能开关
        [feature: string]: boolean;
    };
    updatedAt: string;             // 配置更新时间
}