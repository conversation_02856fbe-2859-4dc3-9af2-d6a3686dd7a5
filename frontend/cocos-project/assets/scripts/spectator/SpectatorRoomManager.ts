import { _decorator } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { WebSocketManager, MessageType, IWebSocketMessage } from '../network/WebSocketManager';

const { ccclass } = _decorator;

/**
 * 围观房间状态
 */
export enum RoomState {
    WAITING = 'waiting',      // 等待中
    PLAYING = 'playing',      // 游戏中
    FINISHED = 'finished',    // 已结束
    CLOSED = 'closed'         // 已关闭
}

/**
 * 用户角色
 */
export enum UserRole {
    PLAYER = 'player',        // 玩家
    SPECTATOR = 'spectator'   // 围观者
}

/**
 * 围观房间信息
 */
export interface ISpectatorRoom {
    roomId: string;
    roomName: string;
    state: RoomState;
    playerInfo: {
        userId: string;
        nickname: string;
        avatar?: string;
        level: number;
        score: number;
    };
    spectatorCount: number;
    maxSpectators: number;
    currentQuestion?: {
        questionId: string;
        questionText: string;
        options: string[];
        timeLeft: number;
        totalTime: number;
    };
    gameProgress: {
        currentQuestionIndex: number;
        totalQuestions: number;
        totalScore: number;
    };
    createdAt: number;
    updatedAt: number;
}

/**
 * 围观者信息
 */
export interface ISpectator {
    userId: string;
    nickname: string;
    avatar?: string;
    joinTime: number;
    predictionScore: number;
    danmakuCount: number;
    isVip?: boolean;
}

/**
 * 弹幕消息
 */
export interface IDanmakuMessage {
    messageId: string;
    userId: string;
    nickname: string;
    avatar?: string;
    content: string;
    timestamp: number;
    type: 'text' | 'emoji' | 'prediction';
    color?: string;
    isVip?: boolean;
}

/**
 * 预测信息
 */
export interface IPrediction {
    predictionId: string;
    userId: string;
    questionId: string;
    selectedOption: number;
    confidence: number; // 置信度 0-100
    timestamp: number;
    points: number; // 预测获得的积分
}

/**
 * 围观房间管理器
 * 负责围观房间的创建、加入、退出、状态管理等功能
 */
@ccclass('SpectatorRoomManager')
export class SpectatorRoomManager {
    private static _instance: SpectatorRoomManager = null;
    
    // 当前房间信息
    private _currentRoom: ISpectatorRoom = null;
    
    // 当前用户角色
    private _userRole: UserRole = UserRole.SPECTATOR;
    
    // 围观者列表
    private _spectators: Map<string, ISpectator> = new Map();
    
    // 弹幕历史
    private _danmakuHistory: IDanmakuMessage[] = [];
    private readonly MAX_DANMAKU_HISTORY = 100;
    
    // 预测历史
    private _predictionHistory: IPrediction[] = [];
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    private _webSocketManager: WebSocketManager = null;
    
    public static getInstance(): SpectatorRoomManager {
        if (!this._instance) {
            this._instance = new SpectatorRoomManager();
        }
        return this._instance;
    }
    
    /**
     * 初始化围观房间管理器
     */
    public initialize(): void {
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        this._webSocketManager = WebSocketManager.getInstance();
        
        // 注册WebSocket消息处理器
        this.registerMessageHandlers();
        
        console.log('[SpectatorRoomManager] 初始化完成');
    }
    
    /**
     * 创建围观房间（玩家）
     */
    public async createRoom(roomName: string): Promise<string> {
        try {
            console.log('[SpectatorRoomManager] 创建围观房间:', roomName);
            
            const roomData = {
                roomName,
                maxSpectators: 100,
                isPublic: true
            };
            
            this._webSocketManager.sendMessage(MessageType.JOIN_ROOM, {
                action: 'create',
                ...roomData
            });
            
            this._userRole = UserRole.PLAYER;
            
            // 等待房间创建确认
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('创建房间超时'));
                }, 10000);
                
                const handleRoomCreated = (message: IWebSocketMessage) => {
                    if (message.data.action === 'created') {
                        clearTimeout(timeout);
                        this._webSocketManager.offMessage(MessageType.ROOM_STATE, handleRoomCreated);
                        resolve(message.data.roomId);
                    }
                };
                
                this._webSocketManager.onMessage(MessageType.ROOM_STATE, handleRoomCreated);
            });
            
        } catch (error) {
            console.error('[SpectatorRoomManager] 创建房间失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'SpectatorRoomManager.createRoom' });
            throw error;
        }
    }
    
    /**
     * 加入围观房间（围观者）
     */
    public async joinRoom(roomId: string): Promise<void> {
        try {
            console.log('[SpectatorRoomManager] 加入围观房间:', roomId);
            
            this._webSocketManager.sendMessage(MessageType.JOIN_ROOM, {
                action: 'join',
                roomId,
                role: UserRole.SPECTATOR
            });
            
            this._userRole = UserRole.SPECTATOR;
            
            // 等待加入确认
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('加入房间超时'));
                }, 10000);
                
                const handleRoomJoined = (message: IWebSocketMessage) => {
                    if (message.data.action === 'joined' && message.data.roomId === roomId) {
                        clearTimeout(timeout);
                        this._webSocketManager.offMessage(MessageType.ROOM_STATE, handleRoomJoined);
                        resolve();
                    }
                };
                
                this._webSocketManager.onMessage(MessageType.ROOM_STATE, handleRoomJoined);
            });
            
        } catch (error) {
            console.error('[SpectatorRoomManager] 加入房间失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'SpectatorRoomManager.joinRoom' });
            throw error;
        }
    }
    
    /**
     * 离开围观房间
     */
    public leaveRoom(): void {
        if (!this._currentRoom) {
            console.warn('[SpectatorRoomManager] 当前不在任何房间中');
            return;
        }
        
        console.log('[SpectatorRoomManager] 离开围观房间:', this._currentRoom.roomId);
        
        this._webSocketManager.sendMessage(MessageType.LEAVE_ROOM, {
            roomId: this._currentRoom.roomId
        });
        
        this.clearRoomData();
    }
    
    /**
     * 发送弹幕
     */
    public sendDanmaku(content: string, type: 'text' | 'emoji' = 'text'): void {
        if (!this._currentRoom) {
            console.warn('[SpectatorRoomManager] 当前不在房间中，无法发送弹幕');
            return;
        }
        
        if (this._userRole !== UserRole.SPECTATOR) {
            console.warn('[SpectatorRoomManager] 只有围观者可以发送弹幕');
            return;
        }
        
        const danmakuData = {
            content,
            type,
            roomId: this._currentRoom.roomId,
            timestamp: Date.now()
        };
        
        this._webSocketManager.sendMessage(MessageType.DANMAKU_SEND, danmakuData);
        
        console.log('[SpectatorRoomManager] 发送弹幕:', content);
    }
    
    /**
     * 提交预测
     */
    public submitPrediction(questionId: string, selectedOption: number, confidence: number): void {
        if (!this._currentRoom) {
            console.warn('[SpectatorRoomManager] 当前不在房间中，无法提交预测');
            return;
        }
        
        if (this._userRole !== UserRole.SPECTATOR) {
            console.warn('[SpectatorRoomManager] 只有围观者可以提交预测');
            return;
        }
        
        const predictionData = {
            questionId,
            selectedOption,
            confidence,
            roomId: this._currentRoom.roomId,
            timestamp: Date.now()
        };
        
        this._webSocketManager.sendMessage(MessageType.PREDICTION_SUBMIT, predictionData);
        
        console.log('[SpectatorRoomManager] 提交预测:', selectedOption);
    }
    
    /**
     * 获取当前房间信息
     */
    public getCurrentRoom(): ISpectatorRoom | null {
        return this._currentRoom;
    }
    
    /**
     * 获取围观者列表
     */
    public getSpectators(): ISpectator[] {
        return Array.from(this._spectators.values());
    }
    
    /**
     * 获取弹幕历史
     */
    public getDanmakuHistory(): IDanmakuMessage[] {
        return [...this._danmakuHistory];
    }
    
    /**
     * 获取预测历史
     */
    public getPredictionHistory(): IPrediction[] {
        return [...this._predictionHistory];
    }
    
    /**
     * 获取用户角色
     */
    public getUserRole(): UserRole {
        return this._userRole;
    }
    
    /**
     * 是否在房间中
     */
    public isInRoom(): boolean {
        return this._currentRoom !== null;
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 注册WebSocket消息处理器
     */
    private registerMessageHandlers(): void {
        this._webSocketManager.onMessage(MessageType.ROOM_STATE, this.handleRoomState.bind(this));
        this._webSocketManager.onMessage(MessageType.DANMAKU_RECEIVE, this.handleDanmakuReceive.bind(this));
        this._webSocketManager.onMessage(MessageType.PREDICTION_RESULT, this.handlePredictionResult.bind(this));
        this._webSocketManager.onMessage(MessageType.GAME_START, this.handleGameStart.bind(this));
        this._webSocketManager.onMessage(MessageType.GAME_END, this.handleGameEnd.bind(this));
        this._webSocketManager.onMessage(MessageType.QUESTION_START, this.handleQuestionStart.bind(this));
        this._webSocketManager.onMessage(MessageType.ANSWER_SUBMIT, this.handleAnswerSubmit.bind(this));
    }
    
    /**
     * 处理房间状态消息
     */
    private handleRoomState(message: IWebSocketMessage): void {
        const { action, roomData, spectators } = message.data;
        
        switch (action) {
            case 'created':
            case 'joined':
            case 'updated':
                this._currentRoom = roomData;
                if (spectators) {
                    this.updateSpectators(spectators);
                }
                this.emitRoomEvent('room_updated', this._currentRoom);
                break;
                
            case 'left':
                this.clearRoomData();
                this.emitRoomEvent('room_left', null);
                break;
                
            case 'closed':
                this.clearRoomData();
                this.emitRoomEvent('room_closed', null);
                break;
        }
    }
    
    /**
     * 处理弹幕接收消息
     */
    private handleDanmakuReceive(message: IWebSocketMessage): void {
        const danmaku: IDanmakuMessage = message.data;
        
        // 添加到历史记录
        this._danmakuHistory.push(danmaku);
        
        // 限制历史记录大小
        if (this._danmakuHistory.length > this.MAX_DANMAKU_HISTORY) {
            this._danmakuHistory.shift();
        }
        
        // 发送事件
        this.emitRoomEvent('danmaku_received', danmaku);
    }
    
    /**
     * 处理预测结果消息
     */
    private handlePredictionResult(message: IWebSocketMessage): void {
        const prediction: IPrediction = message.data;
        
        // 添加到历史记录
        this._predictionHistory.push(prediction);
        
        // 发送事件
        this.emitRoomEvent('prediction_result', prediction);
    }
    
    /**
     * 处理游戏开始消息
     */
    private handleGameStart(message: IWebSocketMessage): void {
        if (this._currentRoom) {
            this._currentRoom.state = RoomState.PLAYING;
            this.emitRoomEvent('game_started', message.data);
        }
    }
    
    /**
     * 处理游戏结束消息
     */
    private handleGameEnd(message: IWebSocketMessage): void {
        if (this._currentRoom) {
            this._currentRoom.state = RoomState.FINISHED;
            this.emitRoomEvent('game_ended', message.data);
        }
    }
    
    /**
     * 处理题目开始消息
     */
    private handleQuestionStart(message: IWebSocketMessage): void {
        if (this._currentRoom) {
            this._currentRoom.currentQuestion = message.data.question;
            this._currentRoom.gameProgress.currentQuestionIndex = message.data.questionIndex;
            this.emitRoomEvent('question_started', message.data);
        }
    }
    
    /**
     * 处理答题提交消息
     */
    private handleAnswerSubmit(message: IWebSocketMessage): void {
        if (this._currentRoom) {
            // 更新玩家分数
            this._currentRoom.playerInfo.score = message.data.totalScore;
            this._currentRoom.gameProgress.totalScore = message.data.totalScore;
            this.emitRoomEvent('answer_submitted', message.data);
        }
    }
    
    /**
     * 更新围观者列表
     */
    private updateSpectators(spectators: ISpectator[]): void {
        this._spectators.clear();
        spectators.forEach(spectator => {
            this._spectators.set(spectator.userId, spectator);
        });
    }
    
    /**
     * 清理房间数据
     */
    private clearRoomData(): void {
        this._currentRoom = null;
        this._spectators.clear();
        this._danmakuHistory.length = 0;
        this._predictionHistory.length = 0;
        this._userRole = UserRole.SPECTATOR;
    }
    
    /**
     * 发送房间事件
     */
    private emitRoomEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`spectator_${eventName}`, data);
        }
    }
}
