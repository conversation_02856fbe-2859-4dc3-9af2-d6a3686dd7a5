import { _decorator, Component, Node, Label, UITransform, Vec3, tween, Color } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { IDanmakuMessage } from './SpectatorRoomManager';

const { ccclass, property } = _decorator;

/**
 * 弹幕配置
 */
interface IDanmakuConfig {
    speed: number;           // 移动速度
    fontSize: number;        // 字体大小
    color: Color;           // 文字颜色
    strokeColor: Color;     // 描边颜色
    strokeWidth: number;    // 描边宽度
    opacity: number;        // 透明度
    duration: number;       // 显示时长
    verticalSpacing: number; // 垂直间距
}

/**
 * 弹幕轨道
 */
interface IDanmakuTrack {
    trackIndex: number;
    y: number;
    isOccupied: boolean;
    lastDanmakuTime: number;
    minInterval: number; // 最小间隔时间
}

/**
 * 弹幕项
 */
interface IDanmakuItem {
    node: Node;
    label: Label;
    message: IDanmakuMessage;
    track: IDanmakuTrack;
    startTime: number;
    isActive: boolean;
}

/**
 * 弹幕系统
 * 负责弹幕的显示、动画、碰撞检测等功能
 */
@ccclass('DanmakuSystem')
export class DanmakuSystem extends Component {
    @property(Node)
    public danmakuContainer: Node = null;
    
    @property(Node)
    public danmakuPrefab: Node = null;
    
    // 弹幕配置
    private _config: IDanmakuConfig = {
        speed: 100,              // 像素/秒
        fontSize: 16,
        color: new Color(255, 255, 255, 255),
        strokeColor: new Color(0, 0, 0, 255),
        strokeWidth: 2,
        opacity: 255,
        duration: 8000,          // 8秒
        verticalSpacing: 30      // 30像素间距
    };
    
    // 弹幕轨道
    private _tracks: IDanmakuTrack[] = [];
    private _trackCount: number = 10;
    
    // 活跃的弹幕项
    private _activeDanmaku: IDanmakuItem[] = [];
    
    // 弹幕队列
    private _danmakuQueue: IDanmakuMessage[] = [];
    
    // 弹幕池
    private _danmakuPool: Node[] = [];
    private readonly POOL_SIZE = 50;
    
    // 容器尺寸
    private _containerWidth: number = 0;
    private _containerHeight: number = 0;
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    // 性能控制
    private _maxDanmakuPerSecond: number = 10;
    private _lastDanmakuTime: number = 0;
    private _danmakuCountThisSecond: number = 0;
    
    protected onLoad(): void {
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.initializeTracks();
        this.initializeDanmakuPool();
        this.registerEventListeners();
        
        console.log('[DanmakuSystem] 弹幕系统初始化完成');
    }
    
    protected onDestroy(): void {
        this.clearAllDanmaku();
    }
    
    protected update(dt: number): void {
        this.updateDanmaku(dt);
        this.processQueue();
        this.updatePerformanceCounters();
    }
    
    /**
     * 显示弹幕
     */
    public showDanmaku(message: IDanmakuMessage): void {
        // 性能控制：限制每秒弹幕数量
        if (this._danmakuCountThisSecond >= this._maxDanmakuPerSecond) {
            this._danmakuQueue.push(message);
            return;
        }
        
        // 查找可用轨道
        const track = this.findAvailableTrack();
        if (!track) {
            this._danmakuQueue.push(message);
            return;
        }
        
        // 创建弹幕项
        const danmakuItem = this.createDanmakuItem(message, track);
        if (danmakuItem) {
            this.startDanmakuAnimation(danmakuItem);
            this._danmakuCountThisSecond++;
        }
    }
    
    /**
     * 设置弹幕配置
     */
    public setConfig(config: Partial<IDanmakuConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('[DanmakuSystem] 弹幕配置已更新');
    }
    
    /**
     * 清空所有弹幕
     */
    public clearAllDanmaku(): void {
        this._activeDanmaku.forEach(item => {
            this.recycleDanmakuItem(item);
        });
        this._activeDanmaku.length = 0;
        this._danmakuQueue.length = 0;
        
        // 重置轨道状态
        this._tracks.forEach(track => {
            track.isOccupied = false;
            track.lastDanmakuTime = 0;
        });
        
        console.log('[DanmakuSystem] 已清空所有弹幕');
    }
    
    /**
     * 暂停弹幕
     */
    public pauseDanmaku(): void {
        this._activeDanmaku.forEach(item => {
            if (item.node) {
                item.node.pauseAllActions();
            }
        });
    }
    
    /**
     * 恢复弹幕
     */
    public resumeDanmaku(): void {
        this._activeDanmaku.forEach(item => {
            if (item.node) {
                item.node.resumeAllActions();
            }
        });
    }
    
    /**
     * 设置弹幕透明度
     */
    public setOpacity(opacity: number): void {
        this._config.opacity = Math.max(0, Math.min(255, opacity));
        
        this._activeDanmaku.forEach(item => {
            if (item.label) {
                const color = item.label.color.clone();
                color.a = this._config.opacity;
                item.label.color = color;
            }
        });
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化轨道
     */
    private initializeTracks(): void {
        if (!this.danmakuContainer) {
            console.error('[DanmakuSystem] 弹幕容器未设置');
            return;
        }
        
        const transform = this.danmakuContainer.getComponent(UITransform);
        this._containerWidth = transform.width;
        this._containerHeight = transform.height;
        
        // 创建轨道
        const trackHeight = this._containerHeight / this._trackCount;
        
        for (let i = 0; i < this._trackCount; i++) {
            this._tracks.push({
                trackIndex: i,
                y: this._containerHeight / 2 - (i + 0.5) * trackHeight,
                isOccupied: false,
                lastDanmakuTime: 0,
                minInterval: 2000 // 2秒最小间隔
            });
        }
        
        console.log(`[DanmakuSystem] 初始化了 ${this._trackCount} 条弹幕轨道`);
    }
    
    /**
     * 初始化弹幕池
     */
    private initializeDanmakuPool(): void {
        if (!this.danmakuPrefab) {
            console.error('[DanmakuSystem] 弹幕预制体未设置');
            return;
        }
        
        for (let i = 0; i < this.POOL_SIZE; i++) {
            const node = instantiate(this.danmakuPrefab);
            node.active = false;
            this._danmakuPool.push(node);
        }
        
        console.log(`[DanmakuSystem] 初始化了 ${this.POOL_SIZE} 个弹幕对象池`);
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.on('spectator_danmaku_received', this.onDanmakuReceived.bind(this));
            eventManager.on('game_paused', this.pauseDanmaku.bind(this));
            eventManager.on('game_resumed', this.resumeDanmaku.bind(this));
        }
    }
    
    /**
     * 弹幕接收事件处理
     */
    private onDanmakuReceived(message: IDanmakuMessage): void {
        this.showDanmaku(message);
    }
    
    /**
     * 查找可用轨道
     */
    private findAvailableTrack(): IDanmakuTrack | null {
        const now = Date.now();
        
        // 优先选择未占用的轨道
        for (const track of this._tracks) {
            if (!track.isOccupied && now - track.lastDanmakuTime >= track.minInterval) {
                return track;
            }
        }
        
        // 如果没有未占用的轨道，选择最早释放的轨道
        let earliestTrack = this._tracks[0];
        for (const track of this._tracks) {
            if (track.lastDanmakuTime < earliestTrack.lastDanmakuTime) {
                earliestTrack = track;
            }
        }
        
        if (now - earliestTrack.lastDanmakuTime >= earliestTrack.minInterval) {
            return earliestTrack;
        }
        
        return null;
    }
    
    /**
     * 创建弹幕项
     */
    private createDanmakuItem(message: IDanmakuMessage, track: IDanmakuTrack): IDanmakuItem | null {
        // 从对象池获取节点
        const node = this._danmakuPool.pop();
        if (!node) {
            console.warn('[DanmakuSystem] 弹幕对象池已耗尽');
            return null;
        }
        
        // 设置节点属性
        node.active = true;
        node.parent = this.danmakuContainer;
        
        // 获取Label组件
        const label = node.getComponent(Label);
        if (!label) {
            console.error('[DanmakuSystem] 弹幕预制体缺少Label组件');
            this._danmakuPool.push(node);
            return null;
        }
        
        // 设置文本内容
        label.string = message.content;
        label.fontSize = this._config.fontSize;
        label.color = this._config.color.clone();
        
        // 设置位置
        const transform = node.getComponent(UITransform);
        node.setPosition(this._containerWidth / 2 + transform.width / 2, track.y, 0);
        
        // 创建弹幕项
        const danmakuItem: IDanmakuItem = {
            node,
            label,
            message,
            track,
            startTime: Date.now(),
            isActive: true
        };
        
        // 标记轨道为占用
        track.isOccupied = true;
        track.lastDanmakuTime = Date.now();
        
        // 添加到活跃列表
        this._activeDanmaku.push(danmakuItem);
        
        return danmakuItem;
    }
    
    /**
     * 开始弹幕动画
     */
    private startDanmakuAnimation(item: IDanmakuItem): void {
        const transform = item.node.getComponent(UITransform);
        const endX = -this._containerWidth / 2 - transform.width / 2;
        const duration = this._config.duration / 1000; // 转换为秒
        
        tween(item.node)
            .to(duration, { position: new Vec3(endX, item.track.y, 0) })
            .call(() => {
                this.recycleDanmakuItem(item);
            })
            .start();
    }
    
    /**
     * 回收弹幕项
     */
    private recycleDanmakuItem(item: IDanmakuItem): void {
        if (!item.isActive) return;
        
        item.isActive = false;
        
        // 停止动画
        item.node.stopAllActions();
        
        // 隐藏节点
        item.node.active = false;
        item.node.parent = null;
        
        // 释放轨道
        item.track.isOccupied = false;
        
        // 回收到对象池
        this._danmakuPool.push(item.node);
        
        // 从活跃列表移除
        const index = this._activeDanmaku.indexOf(item);
        if (index > -1) {
            this._activeDanmaku.splice(index, 1);
        }
    }
    
    /**
     * 更新弹幕
     */
    private updateDanmaku(dt: number): void {
        // 检查过期的弹幕
        const now = Date.now();
        const expiredItems = this._activeDanmaku.filter(item => 
            now - item.startTime > this._config.duration
        );
        
        expiredItems.forEach(item => {
            this.recycleDanmakuItem(item);
        });
    }
    
    /**
     * 处理队列
     */
    private processQueue(): void {
        if (this._danmakuQueue.length === 0) return;
        
        // 尝试处理队列中的弹幕
        while (this._danmakuQueue.length > 0 && this._danmakuCountThisSecond < this._maxDanmakuPerSecond) {
            const message = this._danmakuQueue.shift();
            const track = this.findAvailableTrack();
            
            if (track) {
                const danmakuItem = this.createDanmakuItem(message, track);
                if (danmakuItem) {
                    this.startDanmakuAnimation(danmakuItem);
                    this._danmakuCountThisSecond++;
                }
            } else {
                // 如果没有可用轨道，重新放回队列
                this._danmakuQueue.unshift(message);
                break;
            }
        }
    }
    
    /**
     * 更新性能计数器
     */
    private updatePerformanceCounters(): void {
        const now = Date.now();
        if (now - this._lastDanmakuTime >= 1000) {
            this._danmakuCountThisSecond = 0;
            this._lastDanmakuTime = now;
        }
    }
}
