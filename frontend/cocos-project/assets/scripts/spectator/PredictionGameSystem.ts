import { _decorator, Component } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { IPrediction, SpectatorRoomManager } from './SpectatorRoomManager';

const { ccclass } = _decorator;

/**
 * 预测状态
 */
export enum PredictionState {
    WAITING = 'waiting',      // 等待题目
    OPEN = 'open',           // 开放预测
    CLOSED = 'closed',       // 关闭预测
    REVEALED = 'revealed'    // 公布答案
}

/**
 * 预测统计
 */
export interface IPredictionStats {
    questionId: string;
    totalPredictions: number;
    optionStats: {
        option: number;
        count: number;
        percentage: number;
        averageConfidence: number;
    }[];
    correctOption: number;
    correctPredictions: number;
    accuracy: number;
}

/**
 * 用户预测记录
 */
export interface IUserPredictionRecord {
    userId: string;
    nickname: string;
    avatar?: string;
    totalPredictions: number;
    correctPredictions: number;
    accuracy: number;
    totalPoints: number;
    averageConfidence: number;
    streak: number; // 连续正确次数
    maxStreak: number;
    rank: number;
}

/**
 * 积分规则
 */
interface IPointsRule {
    basePoints: number;        // 基础分数
    confidenceMultiplier: number; // 置信度倍数
    speedBonus: number;        // 速度奖励
    streakBonus: number;       // 连击奖励
    maxPoints: number;         // 最大分数
}

/**
 * 预测游戏系统
 * 负责围观者预测答案、积分计算、排行榜等功能
 */
@ccclass('PredictionGameSystem')
export class PredictionGameSystem extends Component {
    private static _instance: PredictionGameSystem = null;
    
    // 当前预测状态
    private _currentState: PredictionState = PredictionState.WAITING;
    
    // 当前题目信息
    private _currentQuestionId: string = '';
    private _currentOptions: string[] = [];
    private _predictionStartTime: number = 0;
    private _predictionEndTime: number = 0;
    
    // 用户预测记录
    private _userRecords: Map<string, IUserPredictionRecord> = new Map();
    
    // 当前题目的预测数据
    private _currentPredictions: Map<string, IPrediction> = new Map();
    
    // 预测统计
    private _predictionStats: IPredictionStats[] = [];
    
    // 积分规则
    private _pointsRule: IPointsRule = {
        basePoints: 100,
        confidenceMultiplier: 1.5,
        speedBonus: 50,
        streakBonus: 20,
        maxPoints: 500
    };
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    private _spectatorRoomManager: SpectatorRoomManager = null;
    
    public static getInstance(): PredictionGameSystem {
        return this._instance;
    }
    
    protected onLoad(): void {
        PredictionGameSystem._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        this._spectatorRoomManager = SpectatorRoomManager.getInstance();
        
        this.registerEventListeners();
        
        console.log('[PredictionGameSystem] 预测游戏系统初始化完成');
    }
    
    protected onDestroy(): void {
        PredictionGameSystem._instance = null;
    }
    
    /**
     * 开始新题目的预测
     */
    public startPrediction(questionId: string, options: string[], duration: number): void {
        console.log('[PredictionGameSystem] 开始新题目预测:', questionId);
        
        this._currentQuestionId = questionId;
        this._currentOptions = [...options];
        this._predictionStartTime = Date.now();
        this._predictionEndTime = this._predictionStartTime + duration;
        this._currentState = PredictionState.OPEN;
        
        // 清空当前预测数据
        this._currentPredictions.clear();
        
        // 发送事件
        this.emitEvent('prediction_started', {
            questionId,
            options,
            duration,
            endTime: this._predictionEndTime
        });
    }
    
    /**
     * 提交预测
     */
    public submitPrediction(userId: string, selectedOption: number, confidence: number): boolean {
        if (this._currentState !== PredictionState.OPEN) {
            console.warn('[PredictionGameSystem] 当前不在预测阶段');
            return false;
        }
        
        if (Date.now() > this._predictionEndTime) {
            console.warn('[PredictionGameSystem] 预测时间已结束');
            this.closePrediction();
            return false;
        }
        
        if (selectedOption < 0 || selectedOption >= this._currentOptions.length) {
            console.warn('[PredictionGameSystem] 无效的选项:', selectedOption);
            return false;
        }
        
        if (confidence < 0 || confidence > 100) {
            console.warn('[PredictionGameSystem] 无效的置信度:', confidence);
            return false;
        }
        
        // 创建预测记录
        const prediction: IPrediction = {
            predictionId: this.generatePredictionId(),
            userId,
            questionId: this._currentQuestionId,
            selectedOption,
            confidence,
            timestamp: Date.now(),
            points: 0 // 稍后计算
        };
        
        // 保存预测
        this._currentPredictions.set(userId, prediction);
        
        // 更新用户记录
        this.updateUserRecord(userId, prediction);
        
        console.log(`[PredictionGameSystem] 用户 ${userId} 提交预测: 选项${selectedOption}, 置信度${confidence}%`);
        
        // 发送事件
        this.emitEvent('prediction_submitted', {
            userId,
            prediction,
            totalPredictions: this._currentPredictions.size
        });
        
        return true;
    }
    
    /**
     * 关闭预测
     */
    public closePrediction(): void {
        if (this._currentState !== PredictionState.OPEN) {
            return;
        }
        
        console.log('[PredictionGameSystem] 关闭预测，等待答案公布');
        
        this._currentState = PredictionState.CLOSED;
        
        // 发送事件
        this.emitEvent('prediction_closed', {
            questionId: this._currentQuestionId,
            totalPredictions: this._currentPredictions.size
        });
    }
    
    /**
     * 公布答案并计算积分
     */
    public revealAnswer(correctOption: number): void {
        if (this._currentState !== PredictionState.CLOSED) {
            console.warn('[PredictionGameSystem] 需要先关闭预测');
            return;
        }
        
        console.log('[PredictionGameSystem] 公布答案:', correctOption);
        
        this._currentState = PredictionState.REVEALED;
        
        // 计算积分
        this.calculatePoints(correctOption);
        
        // 生成统计数据
        const stats = this.generatePredictionStats(correctOption);
        this._predictionStats.push(stats);
        
        // 更新排行榜
        this.updateLeaderboard();
        
        // 发送事件
        this.emitEvent('answer_revealed', {
            questionId: this._currentQuestionId,
            correctOption,
            stats,
            leaderboard: this.getLeaderboard()
        });
    }
    
    /**
     * 获取当前预测状态
     */
    public getCurrentState(): PredictionState {
        return this._currentState;
    }
    
    /**
     * 获取当前题目的预测统计
     */
    public getCurrentPredictionStats(): any {
        if (this._currentPredictions.size === 0) {
            return null;
        }
        
        const optionCounts = new Array(this._currentOptions.length).fill(0);
        const optionConfidences = new Array(this._currentOptions.length).fill(0);
        
        for (const prediction of this._currentPredictions.values()) {
            optionCounts[prediction.selectedOption]++;
            optionConfidences[prediction.selectedOption] += prediction.confidence;
        }
        
        const total = this._currentPredictions.size;
        
        return {
            totalPredictions: total,
            options: this._currentOptions.map((option, index) => ({
                option: index,
                text: option,
                count: optionCounts[index],
                percentage: total > 0 ? (optionCounts[index] / total * 100) : 0,
                averageConfidence: optionCounts[index] > 0 ? (optionConfidences[index] / optionCounts[index]) : 0
            }))
        };
    }
    
    /**
     * 获取排行榜
     */
    public getLeaderboard(limit: number = 10): IUserPredictionRecord[] {
        const records = Array.from(this._userRecords.values());
        
        // 按总积分排序
        records.sort((a, b) => {
            if (b.totalPoints !== a.totalPoints) {
                return b.totalPoints - a.totalPoints;
            }
            // 积分相同时按准确率排序
            if (b.accuracy !== a.accuracy) {
                return b.accuracy - a.accuracy;
            }
            // 准确率相同时按连击数排序
            return b.maxStreak - a.maxStreak;
        });
        
        // 更新排名
        records.forEach((record, index) => {
            record.rank = index + 1;
        });
        
        return records.slice(0, limit);
    }
    
    /**
     * 获取用户预测记录
     */
    public getUserRecord(userId: string): IUserPredictionRecord | null {
        return this._userRecords.get(userId) || null;
    }
    
    /**
     * 重置预测游戏
     */
    public reset(): void {
        this._currentState = PredictionState.WAITING;
        this._currentQuestionId = '';
        this._currentOptions = [];
        this._currentPredictions.clear();
        this._userRecords.clear();
        this._predictionStats = [];
        
        console.log('[PredictionGameSystem] 预测游戏已重置');
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.on('spectator_question_started', this.onQuestionStarted.bind(this));
            eventManager.on('spectator_answer_submitted', this.onAnswerSubmitted.bind(this));
            eventManager.on('spectator_prediction_result', this.onPredictionResult.bind(this));
        }
    }
    
    /**
     * 题目开始事件处理
     */
    private onQuestionStarted(data: any): void {
        const { question } = data;
        if (question) {
            this.startPrediction(question.questionId, question.options, question.totalTime * 0.8); // 80%时间用于预测
        }
    }
    
    /**
     * 答案提交事件处理
     */
    private onAnswerSubmitted(data: any): void {
        this.closePrediction();
    }
    
    /**
     * 预测结果事件处理
     */
    private onPredictionResult(prediction: IPrediction): void {
        // 处理来自服务器的预测结果
        this._currentPredictions.set(prediction.userId, prediction);
    }
    
    /**
     * 计算积分
     */
    private calculatePoints(correctOption: number): void {
        const now = Date.now();
        
        for (const prediction of this._currentPredictions.values()) {
            let points = 0;
            
            if (prediction.selectedOption === correctOption) {
                // 基础分数
                points = this._pointsRule.basePoints;
                
                // 置信度奖励
                const confidenceBonus = (prediction.confidence / 100) * this._pointsRule.confidenceMultiplier * this._pointsRule.basePoints;
                points += confidenceBonus;
                
                // 速度奖励
                const responseTime = prediction.timestamp - this._predictionStartTime;
                const maxTime = this._predictionEndTime - this._predictionStartTime;
                const speedFactor = Math.max(0, 1 - (responseTime / maxTime));
                const speedBonus = speedFactor * this._pointsRule.speedBonus;
                points += speedBonus;
                
                // 连击奖励
                const userRecord = this._userRecords.get(prediction.userId);
                if (userRecord && userRecord.streak > 0) {
                    const streakBonus = Math.min(userRecord.streak, 10) * this._pointsRule.streakBonus;
                    points += streakBonus;
                }
                
                // 限制最大分数
                points = Math.min(points, this._pointsRule.maxPoints);
            }
            
            prediction.points = Math.round(points);
        }
    }
    
    /**
     * 更新用户记录
     */
    private updateUserRecord(userId: string, prediction: IPrediction): void {
        let record = this._userRecords.get(userId);
        
        if (!record) {
            record = {
                userId,
                nickname: `用户${userId.slice(-4)}`,
                totalPredictions: 0,
                correctPredictions: 0,
                accuracy: 0,
                totalPoints: 0,
                averageConfidence: 0,
                streak: 0,
                maxStreak: 0,
                rank: 0
            };
            this._userRecords.set(userId, record);
        }
        
        record.totalPredictions++;
        
        // 计算平均置信度
        const totalConfidence = record.averageConfidence * (record.totalPredictions - 1) + prediction.confidence;
        record.averageConfidence = totalConfidence / record.totalPredictions;
    }
    
    /**
     * 更新排行榜
     */
    private updateLeaderboard(): void {
        // 更新正确预测和连击
        for (const prediction of this._currentPredictions.values()) {
            const record = this._userRecords.get(prediction.userId);
            if (record) {
                if (prediction.points > 0) {
                    record.correctPredictions++;
                    record.streak++;
                    record.maxStreak = Math.max(record.maxStreak, record.streak);
                } else {
                    record.streak = 0;
                }
                
                record.totalPoints += prediction.points;
                record.accuracy = record.totalPredictions > 0 ? (record.correctPredictions / record.totalPredictions * 100) : 0;
            }
        }
    }
    
    /**
     * 生成预测统计
     */
    private generatePredictionStats(correctOption: number): IPredictionStats {
        const optionStats = this._currentOptions.map((_, index) => {
            const predictions = Array.from(this._currentPredictions.values()).filter(p => p.selectedOption === index);
            const count = predictions.length;
            const totalConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0);
            
            return {
                option: index,
                count,
                percentage: this._currentPredictions.size > 0 ? (count / this._currentPredictions.size * 100) : 0,
                averageConfidence: count > 0 ? (totalConfidence / count) : 0
            };
        });
        
        const correctPredictions = Array.from(this._currentPredictions.values()).filter(p => p.selectedOption === correctOption).length;
        
        return {
            questionId: this._currentQuestionId,
            totalPredictions: this._currentPredictions.size,
            optionStats,
            correctOption,
            correctPredictions,
            accuracy: this._currentPredictions.size > 0 ? (correctPredictions / this._currentPredictions.size * 100) : 0
        };
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`prediction_${eventName}`, data);
        }
    }
    
    /**
     * 生成预测ID
     */
    private generatePredictionId(): string {
        return `pred_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
