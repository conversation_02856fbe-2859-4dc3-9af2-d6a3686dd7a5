import { _decorator, Component } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * UGC内容类型
 */
export enum UGCContentType {
    AUDIO_QUESTION = 'audio_question',    // 音频题目
    TEXT_QUESTION = 'text_question',      // 文字题目
    DIALECT_STORY = 'dialect_story',      // 方言故事
    PRONUNCIATION_GUIDE = 'pronunciation_guide', // 发音指南
    CULTURAL_NOTE = 'cultural_note',      // 文化注释
    QUIZ_SET = 'quiz_set'                 // 题目集合
}

/**
 * 内容状态
 */
export enum ContentStatus {
    DRAFT = 'draft',           // 草稿
    PENDING = 'pending',       // 待审核
    APPROVED = 'approved',     // 已通过
    REJECTED = 'rejected',     // 已拒绝
    PUBLISHED = 'published',   // 已发布
    ARCHIVED = 'archived'      // 已归档
}

/**
 * 审核状态
 */
export enum ModerationStatus {
    PENDING = 'pending',       // 待审核
    IN_REVIEW = 'in_review',   // 审核中
    APPROVED = 'approved',     // 通过
    REJECTED = 'rejected',     // 拒绝
    FLAGGED = 'flagged'        // 被举报
}

/**
 * UGC内容
 */
export interface IUGCContent {
    contentId: string;
    authorId: string;
    authorName: string;
    type: UGCContentType;
    title: string;
    description: string;
    
    // 内容数据
    content: {
        audioUrl?: string;
        audioText?: string;
        dialect: string;
        region: string;
        difficulty: 'easy' | 'medium' | 'hard';
        tags: string[];
        
        // 题目相关
        question?: string;
        options?: string[];
        correctAnswer?: number;
        explanation?: string;
        
        // 故事相关
        story?: string;
        translation?: string;
        
        // 发音指南相关
        phonetics?: string;
        examples?: string[];
        
        // 文化注释相关
        culturalContext?: string;
        historicalBackground?: string;
    };
    
    // 状态信息
    status: ContentStatus;
    moderationStatus: ModerationStatus;
    moderationNotes?: string;
    
    // 统计信息
    stats: {
        views: number;
        likes: number;
        dislikes: number;
        shares: number;
        reports: number;
        usageCount: number;
        averageRating: number;
        totalRatings: number;
    };
    
    // 时间信息
    createdTime: number;
    updatedTime: number;
    publishedTime?: number;
    
    // 元数据
    metadata: {
        version: number;
        language: string;
        sourceType: 'original' | 'adapted' | 'translated';
        license: string;
        attribution?: string;
    };
}

/**
 * 用户评价
 */
export interface IUserRating {
    ratingId: string;
    userId: string;
    contentId: string;
    rating: number;        // 1-5星
    comment?: string;
    isHelpful: boolean;
    createdTime: number;
}

/**
 * 内容举报
 */
export interface IContentReport {
    reportId: string;
    reporterId: string;
    contentId: string;
    reason: 'inappropriate' | 'copyright' | 'spam' | 'incorrect' | 'other';
    description: string;
    evidence?: string[];
    status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
    createdTime: number;
    resolvedTime?: number;
}

/**
 * 创作者统计
 */
export interface ICreatorStats {
    userId: string;
    totalContents: number;
    publishedContents: number;
    totalViews: number;
    totalLikes: number;
    averageRating: number;
    followerCount: number;
    achievementBadges: string[];
    creatorLevel: number;
    reputation: number;
}

/**
 * UGC系统
 * 负责用户生成内容的创建、审核、发布、管理等功能
 */
@ccclass('UGCSystem')
export class UGCSystem extends Component {
    private static _instance: UGCSystem = null;
    
    // 内容数据
    private _contents: Map<string, IUGCContent> = new Map();
    private _userRatings: Map<string, IUserRating[]> = new Map();
    private _contentReports: Map<string, IContentReport[]> = new Map();
    
    // 创作者数据
    private _creatorStats: Map<string, ICreatorStats> = new Map();
    
    // 当前用户的内容
    private _userContents: Map<string, IUGCContent[]> = new Map();
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): UGCSystem {
        return this._instance;
    }
    
    protected onLoad(): void {
        UGCSystem._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.loadUGCData();
        this.registerEventListeners();
        
        console.log('[UGCSystem] UGC系统初始化完成');
    }
    
    protected onDestroy(): void {
        UGCSystem._instance = null;
    }
    
    /**
     * 创建内容
     */
    public async createContent(
        type: UGCContentType,
        title: string,
        description: string,
        content: any
    ): Promise<IUGCContent | null> {
        try {
            const contentId = this.generateContentId();
            const userId = this.getCurrentUserId();
            const userName = this.getCurrentUserName();
            
            const ugcContent: IUGCContent = {
                contentId,
                authorId: userId,
                authorName: userName,
                type,
                title,
                description,
                content: {
                    dialect: content.dialect || '',
                    region: content.region || '',
                    difficulty: content.difficulty || 'medium',
                    tags: content.tags || [],
                    ...content
                },
                status: ContentStatus.DRAFT,
                moderationStatus: ModerationStatus.PENDING,
                stats: {
                    views: 0,
                    likes: 0,
                    dislikes: 0,
                    shares: 0,
                    reports: 0,
                    usageCount: 0,
                    averageRating: 0,
                    totalRatings: 0
                },
                createdTime: Date.now(),
                updatedTime: Date.now(),
                metadata: {
                    version: 1,
                    language: 'zh-CN',
                    sourceType: 'original',
                    license: 'CC BY-SA 4.0'
                }
            };
            
            // 验证内容
            if (!this.validateContent(ugcContent)) {
                console.error('[UGCSystem] 内容验证失败');
                return null;
            }
            
            // 保存内容
            this._contents.set(contentId, ugcContent);
            
            // 更新用户内容列表
            if (!this._userContents.has(userId)) {
                this._userContents.set(userId, []);
            }
            this._userContents.get(userId).push(ugcContent);
            
            // 更新创作者统计
            this.updateCreatorStats(userId);
            
            // 发送事件
            this.emitEvent('content_created', { content: ugcContent });
            
            console.log('[UGCSystem] 创建内容成功:', title);
            return ugcContent;
            
        } catch (error) {
            console.error('[UGCSystem] 创建内容失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'UGCSystem.createContent' });
            return null;
        }
    }
    
    /**
     * 提交审核
     */
    public async submitForReview(contentId: string): Promise<boolean> {
        const content = this._contents.get(contentId);
        if (!content) {
            console.error('[UGCSystem] 内容不存在:', contentId);
            return false;
        }
        
        if (content.authorId !== this.getCurrentUserId()) {
            console.error('[UGCSystem] 无权限操作该内容');
            return false;
        }
        
        if (content.status !== ContentStatus.DRAFT) {
            console.warn('[UGCSystem] 内容状态不允许提交审核:', content.status);
            return false;
        }
        
        // 最终验证
        if (!this.validateContentForSubmission(content)) {
            console.error('[UGCSystem] 内容不符合提交要求');
            return false;
        }
        
        // 更新状态
        content.status = ContentStatus.PENDING;
        content.moderationStatus = ModerationStatus.PENDING;
        content.updatedTime = Date.now();
        
        // 发送审核请求
        await this.requestModeration(content);
        
        // 发送事件
        this.emitEvent('content_submitted', { content });
        
        console.log('[UGCSystem] 内容已提交审核:', content.title);
        return true;
    }
    
    /**
     * 获取内容列表
     */
    public getContents(filter?: {
        type?: UGCContentType;
        status?: ContentStatus;
        authorId?: string;
        dialect?: string;
        difficulty?: string;
        tags?: string[];
        sortBy?: 'newest' | 'popular' | 'rating' | 'views';
        limit?: number;
        offset?: number;
    }): IUGCContent[] {
        let contents = Array.from(this._contents.values());
        
        // 应用过滤器
        if (filter) {
            if (filter.type) {
                contents = contents.filter(content => content.type === filter.type);
            }
            if (filter.status) {
                contents = contents.filter(content => content.status === filter.status);
            }
            if (filter.authorId) {
                contents = contents.filter(content => content.authorId === filter.authorId);
            }
            if (filter.dialect) {
                contents = contents.filter(content => content.content.dialect === filter.dialect);
            }
            if (filter.difficulty) {
                contents = contents.filter(content => content.content.difficulty === filter.difficulty);
            }
            if (filter.tags && filter.tags.length > 0) {
                contents = contents.filter(content => 
                    filter.tags.some(tag => content.content.tags.includes(tag))
                );
            }
        }
        
        // 排序
        const sortBy = filter?.sortBy || 'newest';
        switch (sortBy) {
            case 'newest':
                contents.sort((a, b) => b.createdTime - a.createdTime);
                break;
            case 'popular':
                contents.sort((a, b) => (b.stats.likes + b.stats.views) - (a.stats.likes + a.stats.views));
                break;
            case 'rating':
                contents.sort((a, b) => b.stats.averageRating - a.stats.averageRating);
                break;
            case 'views':
                contents.sort((a, b) => b.stats.views - a.stats.views);
                break;
        }
        
        // 分页
        const offset = filter?.offset || 0;
        const limit = filter?.limit || 20;
        
        return contents.slice(offset, offset + limit);
    }
    
    /**
     * 获取内容详情
     */
    public getContent(contentId: string): IUGCContent | null {
        const content = this._contents.get(contentId);
        if (content) {
            // 增加浏览量
            content.stats.views++;
            content.updatedTime = Date.now();
        }
        return content || null;
    }
    
    /**
     * 评价内容
     */
    public async rateContent(
        contentId: string,
        rating: number,
        comment?: string
    ): Promise<boolean> {
        const content = this._contents.get(contentId);
        if (!content) {
            console.error('[UGCSystem] 内容不存在:', contentId);
            return false;
        }
        
        if (rating < 1 || rating > 5) {
            console.error('[UGCSystem] 评分必须在1-5之间');
            return false;
        }
        
        const userId = this.getCurrentUserId();
        const ratingId = this.generateRatingId();
        
        const userRating: IUserRating = {
            ratingId,
            userId,
            contentId,
            rating,
            comment,
            isHelpful: true,
            createdTime: Date.now()
        };
        
        // 保存评价
        if (!this._userRatings.has(contentId)) {
            this._userRatings.set(contentId, []);
        }
        
        // 检查是否已经评价过
        const existingRatings = this._userRatings.get(contentId);
        const existingIndex = existingRatings.findIndex(r => r.userId === userId);
        
        if (existingIndex >= 0) {
            // 更新现有评价
            existingRatings[existingIndex] = userRating;
        } else {
            // 添加新评价
            existingRatings.push(userRating);
        }
        
        // 更新内容统计
        this.updateContentRatingStats(content);
        
        // 发送事件
        this.emitEvent('content_rated', { content, rating: userRating });
        
        console.log(`[UGCSystem] 评价内容: ${content.title}, 评分: ${rating}`);
        return true;
    }
    
    /**
     * 举报内容
     */
    public async reportContent(
        contentId: string,
        reason: string,
        description: string,
        evidence?: string[]
    ): Promise<boolean> {
        const content = this._contents.get(contentId);
        if (!content) {
            console.error('[UGCSystem] 内容不存在:', contentId);
            return false;
        }
        
        const userId = this.getCurrentUserId();
        const reportId = this.generateReportId();
        
        const report: IContentReport = {
            reportId,
            reporterId: userId,
            contentId,
            reason: reason as any,
            description,
            evidence,
            status: 'pending',
            createdTime: Date.now()
        };
        
        // 保存举报
        if (!this._contentReports.has(contentId)) {
            this._contentReports.set(contentId, []);
        }
        this._contentReports.get(contentId).push(report);
        
        // 更新内容统计
        content.stats.reports++;
        
        // 如果举报数量过多，自动标记为需要审核
        if (content.stats.reports >= 5) {
            content.moderationStatus = ModerationStatus.FLAGGED;
        }
        
        // 发送事件
        this.emitEvent('content_reported', { content, report });
        
        console.log('[UGCSystem] 举报内容:', content.title);
        return true;
    }
    
    /**
     * 获取用户创作统计
     */
    public getUserCreatorStats(userId?: string): ICreatorStats | null {
        const targetUserId = userId || this.getCurrentUserId();
        return this._creatorStats.get(targetUserId) || null;
    }
    
    /**
     * 获取热门创作者
     */
    public getTopCreators(limit: number = 10): ICreatorStats[] {
        return Array.from(this._creatorStats.values())
            .sort((a, b) => b.reputation - a.reputation)
            .slice(0, limit);
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 加载UGC数据
     */
    private loadUGCData(): void {
        // 这里应该从服务器或本地存储加载UGC数据
        console.log('[UGCSystem] UGC数据加载完成');
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            // 可以监听其他系统的事件
        }
    }
    
    /**
     * 验证内容
     */
    private validateContent(content: IUGCContent): boolean {
        // 基本验证
        if (!content.title || content.title.trim().length === 0) {
            console.error('[UGCSystem] 标题不能为空');
            return false;
        }
        
        if (content.title.length > 100) {
            console.error('[UGCSystem] 标题过长');
            return false;
        }
        
        if (!content.description || content.description.trim().length === 0) {
            console.error('[UGCSystem] 描述不能为空');
            return false;
        }
        
        if (content.description.length > 500) {
            console.error('[UGCSystem] 描述过长');
            return false;
        }
        
        // 内容类型特定验证
        switch (content.type) {
            case UGCContentType.AUDIO_QUESTION:
                if (!content.content.audioUrl || !content.content.question || !content.content.options) {
                    console.error('[UGCSystem] 音频题目缺少必要字段');
                    return false;
                }
                break;
                
            case UGCContentType.TEXT_QUESTION:
                if (!content.content.question || !content.content.options) {
                    console.error('[UGCSystem] 文字题目缺少必要字段');
                    return false;
                }
                break;
                
            case UGCContentType.DIALECT_STORY:
                if (!content.content.story) {
                    console.error('[UGCSystem] 方言故事缺少故事内容');
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * 验证内容是否可以提交
     */
    private validateContentForSubmission(content: IUGCContent): boolean {
        // 更严格的验证
        if (!this.validateContent(content)) {
            return false;
        }
        
        // 检查是否有必要的标签
        if (content.content.tags.length === 0) {
            console.error('[UGCSystem] 至少需要一个标签');
            return false;
        }
        
        // 检查方言和地区信息
        if (!content.content.dialect || !content.content.region) {
            console.error('[UGCSystem] 方言和地区信息不能为空');
            return false;
        }
        
        return true;
    }
    
    /**
     * 请求审核
     */
    private async requestModeration(content: IUGCContent): Promise<void> {
        // 这里应该发送到审核系统
        console.log('[UGCSystem] 发送审核请求:', content.contentId);
        
        // 模拟审核过程
        setTimeout(() => {
            this.simulateModeration(content);
        }, 5000);
    }
    
    /**
     * 模拟审核过程
     */
    private simulateModeration(content: IUGCContent): void {
        // 简单的自动审核逻辑
        const isApproved = Math.random() > 0.2; // 80%通过率
        
        if (isApproved) {
            content.status = ContentStatus.APPROVED;
            content.moderationStatus = ModerationStatus.APPROVED;
            content.publishedTime = Date.now();
            
            this.emitEvent('content_approved', { content });
        } else {
            content.status = ContentStatus.REJECTED;
            content.moderationStatus = ModerationStatus.REJECTED;
            content.moderationNotes = '内容不符合社区规范';
            
            this.emitEvent('content_rejected', { content });
        }
        
        content.updatedTime = Date.now();
    }
    
    /**
     * 更新内容评分统计
     */
    private updateContentRatingStats(content: IUGCContent): void {
        const ratings = this._userRatings.get(content.contentId) || [];
        
        if (ratings.length > 0) {
            const totalRating = ratings.reduce((sum, rating) => sum + rating.rating, 0);
            content.stats.averageRating = totalRating / ratings.length;
            content.stats.totalRatings = ratings.length;
        }
    }
    
    /**
     * 更新创作者统计
     */
    private updateCreatorStats(userId: string): void {
        const userContents = this._userContents.get(userId) || [];
        const publishedContents = userContents.filter(content => content.status === ContentStatus.PUBLISHED);
        
        const stats: ICreatorStats = {
            userId,
            totalContents: userContents.length,
            publishedContents: publishedContents.length,
            totalViews: publishedContents.reduce((sum, content) => sum + content.stats.views, 0),
            totalLikes: publishedContents.reduce((sum, content) => sum + content.stats.likes, 0),
            averageRating: this.calculateAverageRating(publishedContents),
            followerCount: 0, // 这里应该从用户系统获取
            achievementBadges: this.calculateAchievementBadges(userContents),
            creatorLevel: this.calculateCreatorLevel(userContents),
            reputation: this.calculateReputation(publishedContents)
        };
        
        this._creatorStats.set(userId, stats);
    }
    
    /**
     * 计算平均评分
     */
    private calculateAverageRating(contents: IUGCContent[]): number {
        if (contents.length === 0) return 0;
        
        const totalRating = contents.reduce((sum, content) => sum + content.stats.averageRating, 0);
        return totalRating / contents.length;
    }
    
    /**
     * 计算成就徽章
     */
    private calculateAchievementBadges(contents: IUGCContent[]): string[] {
        const badges = [];
        
        if (contents.length >= 1) badges.push('first_creator');
        if (contents.length >= 10) badges.push('prolific_creator');
        if (contents.length >= 50) badges.push('master_creator');
        
        const publishedContents = contents.filter(content => content.status === ContentStatus.PUBLISHED);
        const totalViews = publishedContents.reduce((sum, content) => sum + content.stats.views, 0);
        
        if (totalViews >= 1000) badges.push('popular_creator');
        if (totalViews >= 10000) badges.push('viral_creator');
        
        return badges;
    }
    
    /**
     * 计算创作者等级
     */
    private calculateCreatorLevel(contents: IUGCContent[]): number {
        const publishedCount = contents.filter(content => content.status === ContentStatus.PUBLISHED).length;
        
        if (publishedCount >= 50) return 5;
        if (publishedCount >= 20) return 4;
        if (publishedCount >= 10) return 3;
        if (publishedCount >= 5) return 2;
        if (publishedCount >= 1) return 1;
        
        return 0;
    }
    
    /**
     * 计算声誉值
     */
    private calculateReputation(contents: IUGCContent[]): number {
        let reputation = 0;
        
        for (const content of contents) {
            reputation += content.stats.likes * 2;
            reputation += content.stats.views * 0.1;
            reputation += content.stats.averageRating * 10;
            reputation -= content.stats.reports * 5;
        }
        
        return Math.max(0, Math.round(reputation));
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`ugc_${eventName}`, data);
        }
    }
    
    /**
     * 生成内容ID
     */
    private generateContentId(): string {
        return `ugc_content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 生成评价ID
     */
    private generateRatingId(): string {
        return `rating_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 生成举报ID
     */
    private generateReportId(): string {
        return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取当前用户ID
     */
    private getCurrentUserId(): string {
        // 这里应该从用户管理器获取当前用户ID
        return 'user_' + Date.now();
    }
    
    /**
     * 获取当前用户名
     */
    private getCurrentUserName(): string {
        // 这里应该从用户管理器获取当前用户名
        return '用户' + Date.now().toString().slice(-4);
    }
}
