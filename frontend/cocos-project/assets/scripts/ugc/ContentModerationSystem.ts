import { _decorator, Component } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';
import { IUGCContent, ContentStatus, ModerationStatus } from './UGCSystem';

const { ccclass } = _decorator;

/**
 * 审核规则类型
 */
export enum ModerationRuleType {
    KEYWORD_FILTER = 'keyword_filter',       // 关键词过滤
    CONTENT_LENGTH = 'content_length',       // 内容长度
    AUDIO_QUALITY = 'audio_quality',         // 音频质量
    LANGUAGE_DETECTION = 'language_detection', // 语言检测
    SPAM_DETECTION = 'spam_detection',       // 垃圾内容检测
    DUPLICATE_CHECK = 'duplicate_check',     // 重复内容检查
    CULTURAL_SENSITIVITY = 'cultural_sensitivity' // 文化敏感性
}

/**
 * 审核规则
 */
export interface IModerationRule {
    ruleId: string;
    type: ModerationRuleType;
    name: string;
    description: string;
    isEnabled: boolean;
    severity: 'low' | 'medium' | 'high' | 'critical';
    
    // 规则配置
    config: {
        keywords?: string[];
        minLength?: number;
        maxLength?: number;
        allowedLanguages?: string[];
        similarityThreshold?: number;
        qualityThreshold?: number;
        [key: string]: any;
    };
    
    // 自动处理动作
    autoAction: 'approve' | 'reject' | 'flag' | 'manual_review';
    
    createdTime: number;
    updatedTime: number;
}

/**
 * 审核结果
 */
export interface IModerationResult {
    resultId: string;
    contentId: string;
    ruleId: string;
    ruleName: string;
    ruleType: ModerationRuleType;
    
    // 检查结果
    passed: boolean;
    score: number;        // 0-100分
    confidence: number;   // 置信度 0-1
    
    // 详细信息
    details: {
        triggeredKeywords?: string[];
        detectedLanguage?: string;
        qualityMetrics?: any;
        similarContent?: string[];
        flaggedReasons?: string[];
    };
    
    // 建议动作
    recommendedAction: 'approve' | 'reject' | 'flag' | 'manual_review';
    
    createdTime: number;
}

/**
 * 审核任务
 */
export interface IModerationTask {
    taskId: string;
    contentId: string;
    content: IUGCContent;
    
    // 任务状态
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    priority: 'low' | 'normal' | 'high' | 'urgent';
    
    // 审核结果
    results: IModerationResult[];
    overallScore: number;
    finalDecision: 'approved' | 'rejected' | 'flagged' | 'needs_review';
    
    // 审核员信息
    assignedModerator?: string;
    moderatorNotes?: string;
    
    // 时间信息
    createdTime: number;
    startedTime?: number;
    completedTime?: number;
    
    // 自动审核标记
    isAutoModerated: boolean;
}

/**
 * 审核统计
 */
export interface IModerationStats {
    totalTasks: number;
    pendingTasks: number;
    completedTasks: number;
    
    autoApproved: number;
    autoRejected: number;
    manualReview: number;
    
    averageProcessingTime: number;
    accuracyRate: number;
    
    ruleEffectiveness: Map<string, {
        triggered: number;
        accuracy: number;
        falsePositives: number;
        falseNegatives: number;
    }>;
}

/**
 * 内容审核系统
 * 负责UGC内容的自动审核、人工审核、规则管理等功能
 */
@ccclass('ContentModerationSystem')
export class ContentModerationSystem extends Component {
    private static _instance: ContentModerationSystem = null;
    
    // 审核规则
    private _moderationRules: Map<string, IModerationRule> = new Map();
    
    // 审核任务
    private _moderationTasks: Map<string, IModerationTask> = new Map();
    private _pendingTasks: IModerationTask[] = [];
    
    // 审核结果
    private _moderationResults: Map<string, IModerationResult[]> = new Map();
    
    // 审核统计
    private _stats: IModerationStats = {
        totalTasks: 0,
        pendingTasks: 0,
        completedTasks: 0,
        autoApproved: 0,
        autoRejected: 0,
        manualReview: 0,
        averageProcessingTime: 0,
        accuracyRate: 0,
        ruleEffectiveness: new Map()
    };
    
    // 处理状态
    private _isProcessing: boolean = false;
    private _processingInterval: number = null;
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): ContentModerationSystem {
        return this._instance;
    }
    
    protected onLoad(): void {
        ContentModerationSystem._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.initializeModerationRules();
        this.startProcessing();
        this.registerEventListeners();
        
        console.log('[ContentModerationSystem] 内容审核系统初始化完成');
    }
    
    protected onDestroy(): void {
        this.stopProcessing();
        ContentModerationSystem._instance = null;
    }
    
    /**
     * 提交内容审核
     */
    public async submitForModeration(content: IUGCContent, priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'): Promise<string> {
        try {
            const taskId = this.generateTaskId();
            
            const task: IModerationTask = {
                taskId,
                contentId: content.contentId,
                content,
                status: 'pending',
                priority,
                results: [],
                overallScore: 0,
                finalDecision: 'needs_review',
                createdTime: Date.now(),
                isAutoModerated: false
            };
            
            // 保存任务
            this._moderationTasks.set(taskId, task);
            this._pendingTasks.push(task);
            
            // 按优先级排序
            this._pendingTasks.sort((a, b) => {
                const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });
            
            // 更新统计
            this._stats.totalTasks++;
            this._stats.pendingTasks++;
            
            // 发送事件
            this.emitEvent('moderation_submitted', { task });
            
            console.log(`[ContentModerationSystem] 提交审核任务: ${content.title}`);
            return taskId;
            
        } catch (error) {
            console.error('[ContentModerationSystem] 提交审核失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'ContentModerationSystem.submitForModeration' });
            throw error;
        }
    }
    
    /**
     * 获取审核任务
     */
    public getModerationTask(taskId: string): IModerationTask | null {
        return this._moderationTasks.get(taskId) || null;
    }
    
    /**
     * 获取待审核任务列表
     */
    public getPendingTasks(limit: number = 10): IModerationTask[] {
        return this._pendingTasks.slice(0, limit);
    }
    
    /**
     * 手动审核决定
     */
    public async makeManualDecision(
        taskId: string,
        decision: 'approved' | 'rejected',
        moderatorId: string,
        notes?: string
    ): Promise<boolean> {
        const task = this._moderationTasks.get(taskId);
        if (!task) {
            console.error('[ContentModerationSystem] 审核任务不存在:', taskId);
            return false;
        }
        
        if (task.status === 'completed') {
            console.warn('[ContentModerationSystem] 任务已完成，无法修改决定');
            return false;
        }
        
        // 更新任务
        task.finalDecision = decision;
        task.assignedModerator = moderatorId;
        task.moderatorNotes = notes;
        task.status = 'completed';
        task.completedTime = Date.now();
        
        // 更新内容状态
        await this.updateContentStatus(task.content, decision);
        
        // 更新统计
        this.updateModerationStats(task);
        
        // 从待处理列表移除
        const index = this._pendingTasks.findIndex(t => t.taskId === taskId);
        if (index >= 0) {
            this._pendingTasks.splice(index, 1);
        }
        
        // 发送事件
        this.emitEvent('manual_decision_made', { task, decision, moderatorId });
        
        console.log(`[ContentModerationSystem] 手动审核决定: ${decision}, 任务: ${taskId}`);
        return true;
    }
    
    /**
     * 获取审核统计
     */
    public getModerationStats(): IModerationStats {
        return { ...this._stats };
    }
    
    /**
     * 添加审核规则
     */
    public addModerationRule(rule: Omit<IModerationRule, 'ruleId' | 'createdTime' | 'updatedTime'>): string {
        const ruleId = this.generateRuleId();
        const moderationRule: IModerationRule = {
            ...rule,
            ruleId,
            createdTime: Date.now(),
            updatedTime: Date.now()
        };
        
        this._moderationRules.set(ruleId, moderationRule);
        
        console.log(`[ContentModerationSystem] 添加审核规则: ${rule.name}`);
        return ruleId;
    }
    
    /**
     * 更新审核规则
     */
    public updateModerationRule(ruleId: string, updates: Partial<IModerationRule>): boolean {
        const rule = this._moderationRules.get(ruleId);
        if (!rule) {
            console.error('[ContentModerationSystem] 审核规则不存在:', ruleId);
            return false;
        }
        
        Object.assign(rule, updates, { updatedTime: Date.now() });
        
        console.log(`[ContentModerationSystem] 更新审核规则: ${rule.name}`);
        return true;
    }
    
    /**
     * 删除审核规则
     */
    public deleteModerationRule(ruleId: string): boolean {
        const deleted = this._moderationRules.delete(ruleId);
        if (deleted) {
            console.log(`[ContentModerationSystem] 删除审核规则: ${ruleId}`);
        }
        return deleted;
    }
    
    /**
     * 获取审核规则列表
     */
    public getModerationRules(): IModerationRule[] {
        return Array.from(this._moderationRules.values());
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化审核规则
     */
    private initializeModerationRules(): void {
        // 关键词过滤规则
        this.addModerationRule({
            type: ModerationRuleType.KEYWORD_FILTER,
            name: '敏感词过滤',
            description: '检测内容中的敏感词汇',
            isEnabled: true,
            severity: 'high',
            config: {
                keywords: ['垃圾', '广告', '色情', '暴力', '政治敏感']
            },
            autoAction: 'reject'
        });
        
        // 内容长度规则
        this.addModerationRule({
            type: ModerationRuleType.CONTENT_LENGTH,
            name: '内容长度检查',
            description: '检查内容长度是否符合要求',
            isEnabled: true,
            severity: 'medium',
            config: {
                minLength: 10,
                maxLength: 1000
            },
            autoAction: 'flag'
        });
        
        // 音频质量规则
        this.addModerationRule({
            type: ModerationRuleType.AUDIO_QUALITY,
            name: '音频质量检查',
            description: '检查音频文件的质量',
            isEnabled: true,
            severity: 'medium',
            config: {
                qualityThreshold: 0.7
            },
            autoAction: 'manual_review'
        });
        
        // 重复内容检查
        this.addModerationRule({
            type: ModerationRuleType.DUPLICATE_CHECK,
            name: '重复内容检查',
            description: '检查是否存在重复或相似内容',
            isEnabled: true,
            severity: 'medium',
            config: {
                similarityThreshold: 0.8
            },
            autoAction: 'flag'
        });
        
        console.log(`[ContentModerationSystem] 初始化了 ${this._moderationRules.size} 个审核规则`);
    }
    
    /**
     * 开始处理审核任务
     */
    private startProcessing(): void {
        if (this._isProcessing) {
            return;
        }
        
        this._isProcessing = true;
        this._processingInterval = setInterval(() => {
            this.processNextTask();
        }, 1000) as any;
        
        console.log('[ContentModerationSystem] 开始处理审核任务');
    }
    
    /**
     * 停止处理审核任务
     */
    private stopProcessing(): void {
        this._isProcessing = false;
        if (this._processingInterval) {
            clearInterval(this._processingInterval);
            this._processingInterval = null;
        }
        
        console.log('[ContentModerationSystem] 停止处理审核任务');
    }
    
    /**
     * 处理下一个审核任务
     */
    private async processNextTask(): Promise<void> {
        if (this._pendingTasks.length === 0) {
            return;
        }
        
        const task = this._pendingTasks.shift();
        if (!task) {
            return;
        }
        
        try {
            // 更新任务状态
            task.status = 'in_progress';
            task.startedTime = Date.now();
            
            // 执行自动审核
            await this.performAutoModeration(task);
            
            // 根据结果决定下一步
            await this.makeAutoDecision(task);
            
        } catch (error) {
            console.error('[ContentModerationSystem] 处理审核任务失败:', error);
            task.status = 'failed';
            this._errorHandlingSystem?.handleError(error, { context: 'ContentModerationSystem.processNextTask' });
        }
    }
    
    /**
     * 执行自动审核
     */
    private async performAutoModeration(task: IModerationTask): Promise<void> {
        const enabledRules = Array.from(this._moderationRules.values()).filter(rule => rule.isEnabled);
        
        for (const rule of enabledRules) {
            try {
                const result = await this.applyModerationRule(task.content, rule);
                task.results.push(result);
                
                // 保存结果
                if (!this._moderationResults.has(task.contentId)) {
                    this._moderationResults.set(task.contentId, []);
                }
                this._moderationResults.get(task.contentId).push(result);
                
            } catch (error) {
                console.error(`[ContentModerationSystem] 应用规则失败: ${rule.name}`, error);
            }
        }
        
        // 计算总体分数
        task.overallScore = this.calculateOverallScore(task.results);
        task.isAutoModerated = true;
    }
    
    /**
     * 应用审核规则
     */
    private async applyModerationRule(content: IUGCContent, rule: IModerationRule): Promise<IModerationResult> {
        const resultId = this.generateResultId();
        
        let passed = true;
        let score = 100;
        let confidence = 1.0;
        const details: any = {};
        
        switch (rule.type) {
            case ModerationRuleType.KEYWORD_FILTER:
                const keywordResult = this.checkKeywords(content, rule.config.keywords || []);
                passed = keywordResult.passed;
                score = keywordResult.score;
                details.triggeredKeywords = keywordResult.triggeredKeywords;
                break;
                
            case ModerationRuleType.CONTENT_LENGTH:
                const lengthResult = this.checkContentLength(content, rule.config);
                passed = lengthResult.passed;
                score = lengthResult.score;
                break;
                
            case ModerationRuleType.AUDIO_QUALITY:
                const qualityResult = await this.checkAudioQuality(content, rule.config.qualityThreshold || 0.7);
                passed = qualityResult.passed;
                score = qualityResult.score;
                confidence = qualityResult.confidence;
                details.qualityMetrics = qualityResult.metrics;
                break;
                
            case ModerationRuleType.DUPLICATE_CHECK:
                const duplicateResult = await this.checkDuplicateContent(content, rule.config.similarityThreshold || 0.8);
                passed = duplicateResult.passed;
                score = duplicateResult.score;
                details.similarContent = duplicateResult.similarContent;
                break;
        }
        
        return {
            resultId,
            contentId: content.contentId,
            ruleId: rule.ruleId,
            ruleName: rule.name,
            ruleType: rule.type,
            passed,
            score,
            confidence,
            details,
            recommendedAction: this.getRecommendedAction(passed, score, rule.autoAction),
            createdTime: Date.now()
        };
    }
    
    /**
     * 检查关键词
     */
    private checkKeywords(content: IUGCContent, keywords: string[]): { passed: boolean; score: number; triggeredKeywords: string[] } {
        const text = `${content.title} ${content.description} ${content.content.question || ''} ${content.content.story || ''}`.toLowerCase();
        const triggeredKeywords = keywords.filter(keyword => text.includes(keyword.toLowerCase()));
        
        const passed = triggeredKeywords.length === 0;
        const score = Math.max(0, 100 - triggeredKeywords.length * 20);
        
        return { passed, score, triggeredKeywords };
    }
    
    /**
     * 检查内容长度
     */
    private checkContentLength(content: IUGCContent, config: any): { passed: boolean; score: number } {
        const textLength = content.title.length + content.description.length;
        const minLength = config.minLength || 0;
        const maxLength = config.maxLength || 1000;
        
        const passed = textLength >= minLength && textLength <= maxLength;
        let score = 100;
        
        if (textLength < minLength) {
            score = Math.max(0, (textLength / minLength) * 100);
        } else if (textLength > maxLength) {
            score = Math.max(0, 100 - ((textLength - maxLength) / maxLength) * 50);
        }
        
        return { passed, score };
    }
    
    /**
     * 检查音频质量
     */
    private async checkAudioQuality(content: IUGCContent, threshold: number): Promise<{ passed: boolean; score: number; confidence: number; metrics: any }> {
        // 模拟音频质量检查
        const mockQuality = Math.random();
        const passed = mockQuality >= threshold;
        const score = mockQuality * 100;
        const confidence = 0.8;
        
        const metrics = {
            clarity: mockQuality,
            noiseLevel: 1 - mockQuality,
            duration: Math.random() * 60 + 10
        };
        
        return { passed, score, confidence, metrics };
    }
    
    /**
     * 检查重复内容
     */
    private async checkDuplicateContent(content: IUGCContent, threshold: number): Promise<{ passed: boolean; score: number; similarContent: string[] }> {
        // 模拟重复内容检查
        const similarity = Math.random();
        const passed = similarity < threshold;
        const score = (1 - similarity) * 100;
        const similarContent = similarity >= threshold ? ['similar_content_1', 'similar_content_2'] : [];
        
        return { passed, score, similarContent };
    }
    
    /**
     * 获取推荐动作
     */
    private getRecommendedAction(passed: boolean, score: number, autoAction: string): 'approve' | 'reject' | 'flag' | 'manual_review' {
        if (!passed) {
            return autoAction as any;
        }
        
        if (score >= 90) return 'approve';
        if (score >= 70) return 'flag';
        if (score >= 50) return 'manual_review';
        return 'reject';
    }
    
    /**
     * 计算总体分数
     */
    private calculateOverallScore(results: IModerationResult[]): number {
        if (results.length === 0) return 0;
        
        const totalScore = results.reduce((sum, result) => sum + result.score, 0);
        return totalScore / results.length;
    }
    
    /**
     * 做出自动决定
     */
    private async makeAutoDecision(task: IModerationTask): Promise<void> {
        const criticalFailures = task.results.filter(result => !result.passed && result.recommendedAction === 'reject');
        const flaggedResults = task.results.filter(result => result.recommendedAction === 'flag');
        const manualReviewResults = task.results.filter(result => result.recommendedAction === 'manual_review');
        
        let decision: 'approved' | 'rejected' | 'flagged' | 'needs_review';
        
        if (criticalFailures.length > 0) {
            decision = 'rejected';
        } else if (manualReviewResults.length > 0 || flaggedResults.length > 2) {
            decision = 'needs_review';
        } else if (flaggedResults.length > 0) {
            decision = 'flagged';
        } else if (task.overallScore >= 80) {
            decision = 'approved';
        } else {
            decision = 'needs_review';
        }
        
        task.finalDecision = decision;
        
        if (decision === 'approved' || decision === 'rejected') {
            // 自动完成任务
            task.status = 'completed';
            task.completedTime = Date.now();
            
            await this.updateContentStatus(task.content, decision);
            this.updateModerationStats(task);
            
            this.emitEvent('auto_decision_made', { task, decision });
        } else {
            // 需要人工审核
            task.status = 'pending';
            this._pendingTasks.push(task);
            
            this.emitEvent('manual_review_required', { task });
        }
    }
    
    /**
     * 更新内容状态
     */
    private async updateContentStatus(content: IUGCContent, decision: 'approved' | 'rejected'): Promise<void> {
        if (decision === 'approved') {
            content.status = ContentStatus.APPROVED;
            content.moderationStatus = ModerationStatus.APPROVED;
            content.publishedTime = Date.now();
        } else {
            content.status = ContentStatus.REJECTED;
            content.moderationStatus = ModerationStatus.REJECTED;
        }
        
        content.updatedTime = Date.now();
    }
    
    /**
     * 更新审核统计
     */
    private updateModerationStats(task: IModerationTask): void {
        this._stats.completedTasks++;
        this._stats.pendingTasks = Math.max(0, this._stats.pendingTasks - 1);
        
        if (task.finalDecision === 'approved') {
            this._stats.autoApproved++;
        } else if (task.finalDecision === 'rejected') {
            this._stats.autoRejected++;
        } else {
            this._stats.manualReview++;
        }
        
        // 计算平均处理时间
        if (task.startedTime && task.completedTime) {
            const processingTime = task.completedTime - task.startedTime;
            this._stats.averageProcessingTime = 
                (this._stats.averageProcessingTime * (this._stats.completedTasks - 1) + processingTime) / this._stats.completedTasks;
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.on('ugc_content_created', (data) => {
                this.submitForModeration(data.content);
            });
        }
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`moderation_${eventName}`, data);
        }
    }
    
    /**
     * 生成任务ID
     */
    private generateTaskId(): string {
        return `mod_task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 生成规则ID
     */
    private generateRuleId(): string {
        return `mod_rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 生成结果ID
     */
    private generateResultId(): string {
        return `mod_result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
