import { _decorator, Component, sys } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 支持的语言
 */
export enum SupportedLanguage {
    ZH_CN = 'zh-CN',    // 简体中文
    ZH_TW = 'zh-TW',    // 繁体中文
    EN_US = 'en-US',    // 英语
    JA_JP = 'ja-JP',    // 日语
    KO_KR = 'ko-KR',    // 韩语
    TH_TH = 'th-TH',    // 泰语
    VI_VN = 'vi-VN',    // 越南语
    ID_ID = 'id-ID',    // 印尼语
    MS_MY = 'ms-MY',    // 马来语
    ES_ES = 'es-ES',    // 西班牙语
    FR_FR = 'fr-FR',    // 法语
    DE_DE = 'de-DE',    // 德语
    RU_RU = 'ru-RU',    // 俄语
    AR_SA = 'ar-SA'     // 阿拉伯语
}

/**
 * 语言信息
 */
export interface ILanguageInfo {
    code: SupportedLanguage;
    name: string;
    nativeName: string;
    flag: string;
    isRTL: boolean;
    isSupported: boolean;
    completeness: number;  // 翻译完成度 0-100
}

/**
 * 翻译键值对
 */
export interface ITranslationData {
    [key: string]: string | ITranslationData;
}

/**
 * 本地化配置
 */
export interface ILocalizationConfig {
    defaultLanguage: SupportedLanguage;
    fallbackLanguage: SupportedLanguage;
    autoDetect: boolean;
    cacheTranslations: boolean;
    enablePluralization: boolean;
    enableInterpolation: boolean;
}

/**
 * 插值参数
 */
export interface IInterpolationParams {
    [key: string]: string | number;
}

/**
 * 复数规则
 */
export interface IPluralizationRule {
    zero?: string;
    one?: string;
    two?: string;
    few?: string;
    many?: string;
    other: string;
}

/**
 * 本地化管理器
 * 负责多语言支持、文本翻译、语言切换等功能
 */
@ccclass('LocalizationManager')
export class LocalizationManager extends Component {
    private static _instance: LocalizationManager = null;
    
    // 当前语言设置
    private _currentLanguage: SupportedLanguage = SupportedLanguage.ZH_CN;
    private _config: ILocalizationConfig = {
        defaultLanguage: SupportedLanguage.ZH_CN,
        fallbackLanguage: SupportedLanguage.EN_US,
        autoDetect: true,
        cacheTranslations: true,
        enablePluralization: true,
        enableInterpolation: true
    };
    
    // 翻译数据
    private _translations: Map<SupportedLanguage, ITranslationData> = new Map();
    private _translationCache: Map<string, string> = new Map();
    
    // 语言信息
    private _languageInfos: Map<SupportedLanguage, ILanguageInfo> = new Map();
    
    // 加载状态
    private _isLoading: boolean = false;
    private _loadedLanguages: Set<SupportedLanguage> = new Set();
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): LocalizationManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        LocalizationManager._instance = this;
        
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        this.initializeLanguageInfos();
        this.detectSystemLanguage();
        this.loadTranslations(this._currentLanguage);
        
        console.log('[LocalizationManager] 本地化管理器初始化完成');
    }
    
    protected onDestroy(): void {
        LocalizationManager._instance = null;
    }
    
    /**
     * 获取当前语言
     */
    public getCurrentLanguage(): SupportedLanguage {
        return this._currentLanguage;
    }
    
    /**
     * 设置语言
     */
    public async setLanguage(language: SupportedLanguage): Promise<boolean> {
        if (language === this._currentLanguage) {
            return true;
        }
        
        try {
            // 加载翻译数据
            await this.loadTranslations(language);
            
            // 更新当前语言
            const oldLanguage = this._currentLanguage;
            this._currentLanguage = language;
            
            // 清空缓存
            this._translationCache.clear();
            
            // 保存到本地存储
            this.saveLanguagePreference(language);
            
            // 发送语言变更事件
            this.emitEvent('language_changed', {
                oldLanguage,
                newLanguage: language
            });
            
            console.log(`[LocalizationManager] 语言已切换到: ${language}`);
            return true;
            
        } catch (error) {
            console.error('[LocalizationManager] 语言切换失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'LocalizationManager.setLanguage' });
            return false;
        }
    }
    
    /**
     * 获取翻译文本
     */
    public getText(key: string, params?: IInterpolationParams, count?: number): string {
        // 生成缓存键
        const cacheKey = this.generateCacheKey(key, params, count);
        
        // 检查缓存
        if (this._config.cacheTranslations && this._translationCache.has(cacheKey)) {
            return this._translationCache.get(cacheKey);
        }
        
        // 获取翻译
        let translation = this.getTranslation(key, this._currentLanguage);
        
        // 如果没有找到翻译，使用回退语言
        if (!translation && this._currentLanguage !== this._config.fallbackLanguage) {
            translation = this.getTranslation(key, this._config.fallbackLanguage);
        }
        
        // 如果仍然没有找到，返回键名
        if (!translation) {
            console.warn(`[LocalizationManager] 未找到翻译: ${key}`);
            translation = key;
        }
        
        // 处理复数
        if (this._config.enablePluralization && count !== undefined && typeof translation === 'object') {
            translation = this.handlePluralization(translation as IPluralizationRule, count);
        }
        
        // 处理插值
        if (this._config.enableInterpolation && params && typeof translation === 'string') {
            translation = this.handleInterpolation(translation, params);
        }
        
        // 缓存结果
        if (this._config.cacheTranslations && typeof translation === 'string') {
            this._translationCache.set(cacheKey, translation);
        }
        
        return typeof translation === 'string' ? translation : key;
    }
    
    /**
     * 获取支持的语言列表
     */
    public getSupportedLanguages(): ILanguageInfo[] {
        return Array.from(this._languageInfos.values())
            .filter(info => info.isSupported)
            .sort((a, b) => a.name.localeCompare(b.name));
    }
    
    /**
     * 获取语言信息
     */
    public getLanguageInfo(language: SupportedLanguage): ILanguageInfo | null {
        return this._languageInfos.get(language) || null;
    }
    
    /**
     * 检查语言是否已加载
     */
    public isLanguageLoaded(language: SupportedLanguage): boolean {
        return this._loadedLanguages.has(language);
    }
    
    /**
     * 预加载语言
     */
    public async preloadLanguage(language: SupportedLanguage): Promise<boolean> {
        if (this.isLanguageLoaded(language)) {
            return true;
        }
        
        try {
            await this.loadTranslations(language);
            return true;
        } catch (error) {
            console.error(`[LocalizationManager] 预加载语言失败: ${language}`, error);
            return false;
        }
    }
    
    /**
     * 获取翻译完成度
     */
    public getTranslationCompleteness(language: SupportedLanguage): number {
        const languageInfo = this._languageInfos.get(language);
        return languageInfo ? languageInfo.completeness : 0;
    }
    
    /**
     * 格式化数字
     */
    public formatNumber(value: number, options?: Intl.NumberFormatOptions): string {
        try {
            return new Intl.NumberFormat(this._currentLanguage, options).format(value);
        } catch (error) {
            console.warn('[LocalizationManager] 数字格式化失败:', error);
            return value.toString();
        }
    }
    
    /**
     * 格式化日期
     */
    public formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
        try {
            return new Intl.DateTimeFormat(this._currentLanguage, options).format(date);
        } catch (error) {
            console.warn('[LocalizationManager] 日期格式化失败:', error);
            return date.toLocaleDateString();
        }
    }
    
    /**
     * 格式化相对时间
     */
    public formatRelativeTime(value: number, unit: Intl.RelativeTimeFormatUnit): string {
        try {
            return new Intl.RelativeTimeFormat(this._currentLanguage).format(value, unit);
        } catch (error) {
            console.warn('[LocalizationManager] 相对时间格式化失败:', error);
            return `${value} ${unit}`;
        }
    }
    
    /**
     * 检查是否为RTL语言
     */
    public isRTL(): boolean {
        const languageInfo = this._languageInfos.get(this._currentLanguage);
        return languageInfo ? languageInfo.isRTL : false;
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 初始化语言信息
     */
    private initializeLanguageInfos(): void {
        const languageInfos: ILanguageInfo[] = [
            {
                code: SupportedLanguage.ZH_CN,
                name: 'Chinese (Simplified)',
                nativeName: '简体中文',
                flag: '🇨🇳',
                isRTL: false,
                isSupported: true,
                completeness: 100
            },
            {
                code: SupportedLanguage.ZH_TW,
                name: 'Chinese (Traditional)',
                nativeName: '繁體中文',
                flag: '🇹🇼',
                isRTL: false,
                isSupported: true,
                completeness: 95
            },
            {
                code: SupportedLanguage.EN_US,
                name: 'English',
                nativeName: 'English',
                flag: '🇺🇸',
                isRTL: false,
                isSupported: true,
                completeness: 100
            },
            {
                code: SupportedLanguage.JA_JP,
                name: 'Japanese',
                nativeName: '日本語',
                flag: '🇯🇵',
                isRTL: false,
                isSupported: true,
                completeness: 90
            },
            {
                code: SupportedLanguage.KO_KR,
                name: 'Korean',
                nativeName: '한국어',
                flag: '🇰🇷',
                isRTL: false,
                isSupported: true,
                completeness: 85
            },
            {
                code: SupportedLanguage.TH_TH,
                name: 'Thai',
                nativeName: 'ไทย',
                flag: '🇹🇭',
                isRTL: false,
                isSupported: true,
                completeness: 80
            },
            {
                code: SupportedLanguage.VI_VN,
                name: 'Vietnamese',
                nativeName: 'Tiếng Việt',
                flag: '🇻🇳',
                isRTL: false,
                isSupported: true,
                completeness: 75
            },
            {
                code: SupportedLanguage.AR_SA,
                name: 'Arabic',
                nativeName: 'العربية',
                flag: '🇸🇦',
                isRTL: true,
                isSupported: false,
                completeness: 30
            }
        ];
        
        languageInfos.forEach(info => {
            this._languageInfos.set(info.code, info);
        });
        
        console.log(`[LocalizationManager] 初始化了 ${languageInfos.length} 种语言信息`);
    }
    
    /**
     * 检测系统语言
     */
    private detectSystemLanguage(): void {
        if (!this._config.autoDetect) {
            return;
        }
        
        // 从本地存储获取用户偏好
        const savedLanguage = this.loadLanguagePreference();
        if (savedLanguage && this._languageInfos.has(savedLanguage)) {
            this._currentLanguage = savedLanguage;
            return;
        }
        
        // 检测系统语言
        let systemLanguage = sys.language as SupportedLanguage;
        
        // 处理语言代码映射
        const languageMapping: { [key: string]: SupportedLanguage } = {
            'zh': SupportedLanguage.ZH_CN,
            'zh-cn': SupportedLanguage.ZH_CN,
            'zh-tw': SupportedLanguage.ZH_TW,
            'zh-hk': SupportedLanguage.ZH_TW,
            'en': SupportedLanguage.EN_US,
            'ja': SupportedLanguage.JA_JP,
            'ko': SupportedLanguage.KO_KR,
            'th': SupportedLanguage.TH_TH,
            'vi': SupportedLanguage.VI_VN
        };
        
        const mappedLanguage = languageMapping[systemLanguage.toLowerCase()];
        if (mappedLanguage && this._languageInfos.get(mappedLanguage)?.isSupported) {
            this._currentLanguage = mappedLanguage;
        } else {
            this._currentLanguage = this._config.defaultLanguage;
        }
        
        console.log(`[LocalizationManager] 检测到系统语言: ${systemLanguage}, 使用语言: ${this._currentLanguage}`);
    }
    
    /**
     * 加载翻译数据
     */
    private async loadTranslations(language: SupportedLanguage): Promise<void> {
        if (this._loadedLanguages.has(language)) {
            return;
        }
        
        if (this._isLoading) {
            // 等待当前加载完成
            await new Promise(resolve => {
                const checkLoading = () => {
                    if (!this._isLoading) {
                        resolve(void 0);
                    } else {
                        setTimeout(checkLoading, 100);
                    }
                };
                checkLoading();
            });
            return;
        }
        
        this._isLoading = true;
        
        try {
            // 这里应该从服务器或本地文件加载翻译数据
            // 示例数据
            const translationData = this.getDefaultTranslations(language);
            
            this._translations.set(language, translationData);
            this._loadedLanguages.add(language);
            
            console.log(`[LocalizationManager] 加载翻译数据完成: ${language}`);
            
        } catch (error) {
            console.error(`[LocalizationManager] 加载翻译数据失败: ${language}`, error);
            throw error;
        } finally {
            this._isLoading = false;
        }
    }
    
    /**
     * 获取默认翻译数据
     */
    private getDefaultTranslations(language: SupportedLanguage): ITranslationData {
        const translations: { [key in SupportedLanguage]: ITranslationData } = {
            [SupportedLanguage.ZH_CN]: {
                common: {
                    ok: '确定',
                    cancel: '取消',
                    confirm: '确认',
                    back: '返回',
                    next: '下一步',
                    loading: '加载中...',
                    error: '错误',
                    success: '成功',
                    retry: '重试'
                },
                game: {
                    title: '家乡话猜猜猜',
                    start: '开始游戏',
                    score: '分数',
                    level: '等级',
                    question: '题目',
                    answer: '答案',
                    correct: '正确',
                    wrong: '错误',
                    time_up: '时间到',
                    game_over: '游戏结束'
                },
                learning: {
                    course: '课程',
                    lesson: '课时',
                    progress: '进度',
                    achievement: '成就',
                    goal: '目标',
                    study: '学习',
                    practice: '练习',
                    test: '测试'
                }
            },
            [SupportedLanguage.EN_US]: {
                common: {
                    ok: 'OK',
                    cancel: 'Cancel',
                    confirm: 'Confirm',
                    back: 'Back',
                    next: 'Next',
                    loading: 'Loading...',
                    error: 'Error',
                    success: 'Success',
                    retry: 'Retry'
                },
                game: {
                    title: 'Dialect Guessing Game',
                    start: 'Start Game',
                    score: 'Score',
                    level: 'Level',
                    question: 'Question',
                    answer: 'Answer',
                    correct: 'Correct',
                    wrong: 'Wrong',
                    time_up: 'Time Up',
                    game_over: 'Game Over'
                },
                learning: {
                    course: 'Course',
                    lesson: 'Lesson',
                    progress: 'Progress',
                    achievement: 'Achievement',
                    goal: 'Goal',
                    study: 'Study',
                    practice: 'Practice',
                    test: 'Test'
                }
            },
            [SupportedLanguage.ZH_TW]: {
                common: {
                    ok: '確定',
                    cancel: '取消',
                    confirm: '確認',
                    back: '返回',
                    next: '下一步',
                    loading: '載入中...',
                    error: '錯誤',
                    success: '成功',
                    retry: '重試'
                },
                game: {
                    title: '家鄉話猜猜猜',
                    start: '開始遊戲',
                    score: '分數',
                    level: '等級',
                    question: '題目',
                    answer: '答案',
                    correct: '正確',
                    wrong: '錯誤',
                    time_up: '時間到',
                    game_over: '遊戲結束'
                },
                learning: {
                    course: '課程',
                    lesson: '課時',
                    progress: '進度',
                    achievement: '成就',
                    goal: '目標',
                    study: '學習',
                    practice: '練習',
                    test: '測試'
                }
            },
            [SupportedLanguage.JA_JP]: {
                common: {
                    ok: 'OK',
                    cancel: 'キャンセル',
                    confirm: '確認',
                    back: '戻る',
                    next: '次へ',
                    loading: '読み込み中...',
                    error: 'エラー',
                    success: '成功',
                    retry: '再試行'
                },
                game: {
                    title: '方言当てゲーム',
                    start: 'ゲーム開始',
                    score: 'スコア',
                    level: 'レベル',
                    question: '問題',
                    answer: '答え',
                    correct: '正解',
                    wrong: '不正解',
                    time_up: '時間切れ',
                    game_over: 'ゲーム終了'
                },
                learning: {
                    course: 'コース',
                    lesson: 'レッスン',
                    progress: '進捗',
                    achievement: '実績',
                    goal: '目標',
                    study: '学習',
                    practice: '練習',
                    test: 'テスト'
                }
            },
            [SupportedLanguage.KO_KR]: {
                common: {
                    ok: '확인',
                    cancel: '취소',
                    confirm: '확인',
                    back: '뒤로',
                    next: '다음',
                    loading: '로딩 중...',
                    error: '오류',
                    success: '성공',
                    retry: '다시 시도'
                },
                game: {
                    title: '방언 맞추기 게임',
                    start: '게임 시작',
                    score: '점수',
                    level: '레벨',
                    question: '문제',
                    answer: '답',
                    correct: '정답',
                    wrong: '오답',
                    time_up: '시간 종료',
                    game_over: '게임 종료'
                },
                learning: {
                    course: '코스',
                    lesson: '레슨',
                    progress: '진행률',
                    achievement: '성취',
                    goal: '목표',
                    study: '학습',
                    practice: '연습',
                    test: '테스트'
                }
            },
            [SupportedLanguage.TH_TH]: {
                common: {
                    ok: 'ตกลง',
                    cancel: 'ยกเลิก',
                    confirm: 'ยืนยัน',
                    back: 'กลับ',
                    next: 'ถัดไป',
                    loading: 'กำลังโหลด...',
                    error: 'ข้อผิดพลาด',
                    success: 'สำเร็จ',
                    retry: 'ลองใหม่'
                },
                game: {
                    title: 'เกมทายภาษาถิ่น',
                    start: 'เริ่มเกม',
                    score: 'คะแนน',
                    level: 'ระดับ',
                    question: 'คำถาม',
                    answer: 'คำตอบ',
                    correct: 'ถูกต้อง',
                    wrong: 'ผิด',
                    time_up: 'หมดเวลา',
                    game_over: 'จบเกม'
                },
                learning: {
                    course: 'หลักสูตร',
                    lesson: 'บทเรียน',
                    progress: 'ความคืบหน้า',
                    achievement: 'ความสำเร็จ',
                    goal: 'เป้าหมาย',
                    study: 'เรียน',
                    practice: 'ฝึกฝน',
                    test: 'ทดสอบ'
                }
            },
            [SupportedLanguage.VI_VN]: {
                common: {
                    ok: 'OK',
                    cancel: 'Hủy',
                    confirm: 'Xác nhận',
                    back: 'Quay lại',
                    next: 'Tiếp theo',
                    loading: 'Đang tải...',
                    error: 'Lỗi',
                    success: 'Thành công',
                    retry: 'Thử lại'
                },
                game: {
                    title: 'Trò chơi đoán tiếng địa phương',
                    start: 'Bắt đầu',
                    score: 'Điểm',
                    level: 'Cấp độ',
                    question: 'Câu hỏi',
                    answer: 'Đáp án',
                    correct: 'Đúng',
                    wrong: 'Sai',
                    time_up: 'Hết giờ',
                    game_over: 'Kết thúc'
                },
                learning: {
                    course: 'Khóa học',
                    lesson: 'Bài học',
                    progress: 'Tiến độ',
                    achievement: 'Thành tích',
                    goal: 'Mục tiêu',
                    study: 'Học',
                    practice: 'Luyện tập',
                    test: 'Kiểm tra'
                }
            },
            [SupportedLanguage.ID_ID]: {
                common: {
                    ok: 'OK',
                    cancel: 'Batal',
                    confirm: 'Konfirmasi',
                    back: 'Kembali',
                    next: 'Selanjutnya',
                    loading: 'Memuat...',
                    error: 'Error',
                    success: 'Berhasil',
                    retry: 'Coba lagi'
                },
                game: {
                    title: 'Game Tebak Dialek',
                    start: 'Mulai Game',
                    score: 'Skor',
                    level: 'Level',
                    question: 'Pertanyaan',
                    answer: 'Jawaban',
                    correct: 'Benar',
                    wrong: 'Salah',
                    time_up: 'Waktu habis',
                    game_over: 'Game selesai'
                },
                learning: {
                    course: 'Kursus',
                    lesson: 'Pelajaran',
                    progress: 'Kemajuan',
                    achievement: 'Pencapaian',
                    goal: 'Tujuan',
                    study: 'Belajar',
                    practice: 'Latihan',
                    test: 'Tes'
                }
            },
            [SupportedLanguage.MS_MY]: {
                common: {
                    ok: 'OK',
                    cancel: 'Batal',
                    confirm: 'Sahkan',
                    back: 'Kembali',
                    next: 'Seterusnya',
                    loading: 'Memuatkan...',
                    error: 'Ralat',
                    success: 'Berjaya',
                    retry: 'Cuba lagi'
                },
                game: {
                    title: 'Permainan Teka Dialek',
                    start: 'Mula Permainan',
                    score: 'Skor',
                    level: 'Tahap',
                    question: 'Soalan',
                    answer: 'Jawapan',
                    correct: 'Betul',
                    wrong: 'Salah',
                    time_up: 'Masa tamat',
                    game_over: 'Permainan tamat'
                },
                learning: {
                    course: 'Kursus',
                    lesson: 'Pelajaran',
                    progress: 'Kemajuan',
                    achievement: 'Pencapaian',
                    goal: 'Matlamat',
                    study: 'Belajar',
                    practice: 'Latihan',
                    test: 'Ujian'
                }
            },
            [SupportedLanguage.ES_ES]: {
                common: {
                    ok: 'OK',
                    cancel: 'Cancelar',
                    confirm: 'Confirmar',
                    back: 'Atrás',
                    next: 'Siguiente',
                    loading: 'Cargando...',
                    error: 'Error',
                    success: 'Éxito',
                    retry: 'Reintentar'
                },
                game: {
                    title: 'Juego de Adivinar Dialectos',
                    start: 'Iniciar Juego',
                    score: 'Puntuación',
                    level: 'Nivel',
                    question: 'Pregunta',
                    answer: 'Respuesta',
                    correct: 'Correcto',
                    wrong: 'Incorrecto',
                    time_up: 'Tiempo agotado',
                    game_over: 'Juego terminado'
                },
                learning: {
                    course: 'Curso',
                    lesson: 'Lección',
                    progress: 'Progreso',
                    achievement: 'Logro',
                    goal: 'Objetivo',
                    study: 'Estudiar',
                    practice: 'Práctica',
                    test: 'Prueba'
                }
            },
            [SupportedLanguage.FR_FR]: {
                common: {
                    ok: 'OK',
                    cancel: 'Annuler',
                    confirm: 'Confirmer',
                    back: 'Retour',
                    next: 'Suivant',
                    loading: 'Chargement...',
                    error: 'Erreur',
                    success: 'Succès',
                    retry: 'Réessayer'
                },
                game: {
                    title: 'Jeu de Devinette de Dialectes',
                    start: 'Commencer',
                    score: 'Score',
                    level: 'Niveau',
                    question: 'Question',
                    answer: 'Réponse',
                    correct: 'Correct',
                    wrong: 'Incorrect',
                    time_up: 'Temps écoulé',
                    game_over: 'Jeu terminé'
                },
                learning: {
                    course: 'Cours',
                    lesson: 'Leçon',
                    progress: 'Progrès',
                    achievement: 'Réussite',
                    goal: 'Objectif',
                    study: 'Étudier',
                    practice: 'Pratique',
                    test: 'Test'
                }
            },
            [SupportedLanguage.DE_DE]: {
                common: {
                    ok: 'OK',
                    cancel: 'Abbrechen',
                    confirm: 'Bestätigen',
                    back: 'Zurück',
                    next: 'Weiter',
                    loading: 'Laden...',
                    error: 'Fehler',
                    success: 'Erfolg',
                    retry: 'Wiederholen'
                },
                game: {
                    title: 'Dialekt-Ratespiel',
                    start: 'Spiel starten',
                    score: 'Punkte',
                    level: 'Level',
                    question: 'Frage',
                    answer: 'Antwort',
                    correct: 'Richtig',
                    wrong: 'Falsch',
                    time_up: 'Zeit abgelaufen',
                    game_over: 'Spiel beendet'
                },
                learning: {
                    course: 'Kurs',
                    lesson: 'Lektion',
                    progress: 'Fortschritt',
                    achievement: 'Erfolg',
                    goal: 'Ziel',
                    study: 'Lernen',
                    practice: 'Übung',
                    test: 'Test'
                }
            },
            [SupportedLanguage.RU_RU]: {
                common: {
                    ok: 'OK',
                    cancel: 'Отмена',
                    confirm: 'Подтвердить',
                    back: 'Назад',
                    next: 'Далее',
                    loading: 'Загрузка...',
                    error: 'Ошибка',
                    success: 'Успех',
                    retry: 'Повторить'
                },
                game: {
                    title: 'Игра угадай диалект',
                    start: 'Начать игру',
                    score: 'Очки',
                    level: 'Уровень',
                    question: 'Вопрос',
                    answer: 'Ответ',
                    correct: 'Правильно',
                    wrong: 'Неправильно',
                    time_up: 'Время вышло',
                    game_over: 'Игра окончена'
                },
                learning: {
                    course: 'Курс',
                    lesson: 'Урок',
                    progress: 'Прогресс',
                    achievement: 'Достижение',
                    goal: 'Цель',
                    study: 'Изучать',
                    practice: 'Практика',
                    test: 'Тест'
                }
            },
            [SupportedLanguage.AR_SA]: {
                common: {
                    ok: 'موافق',
                    cancel: 'إلغاء',
                    confirm: 'تأكيد',
                    back: 'رجوع',
                    next: 'التالي',
                    loading: 'جاري التحميل...',
                    error: 'خطأ',
                    success: 'نجح',
                    retry: 'إعادة المحاولة'
                },
                game: {
                    title: 'لعبة تخمين اللهجات',
                    start: 'بدء اللعبة',
                    score: 'النقاط',
                    level: 'المستوى',
                    question: 'السؤال',
                    answer: 'الإجابة',
                    correct: 'صحيح',
                    wrong: 'خطأ',
                    time_up: 'انتهى الوقت',
                    game_over: 'انتهت اللعبة'
                },
                learning: {
                    course: 'الدورة',
                    lesson: 'الدرس',
                    progress: 'التقدم',
                    achievement: 'الإنجاز',
                    goal: 'الهدف',
                    study: 'دراسة',
                    practice: 'ممارسة',
                    test: 'اختبار'
                }
            }
        };
        
        // 为其他语言提供默认值
        const defaultTranslations = translations[SupportedLanguage.EN_US];
        return translations[language] || defaultTranslations;
    }
    
    /**
     * 获取翻译
     */
    private getTranslation(key: string, language: SupportedLanguage): string | IPluralizationRule | null {
        const translations = this._translations.get(language);
        if (!translations) {
            return null;
        }
        
        const keys = key.split('.');
        let current: any = translations;
        
        for (const k of keys) {
            if (current && typeof current === 'object' && k in current) {
                current = current[k];
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 处理复数
     */
    private handlePluralization(rule: IPluralizationRule, count: number): string {
        if (count === 0 && rule.zero) return rule.zero;
        if (count === 1 && rule.one) return rule.one;
        if (count === 2 && rule.two) return rule.two;
        
        // 简化的复数规则，实际应该根据语言特性实现
        if (count > 1 && rule.many) return rule.many;
        
        return rule.other;
    }
    
    /**
     * 处理插值
     */
    private handleInterpolation(text: string, params: IInterpolationParams): string {
        return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            const value = params[key];
            return value !== undefined ? String(value) : match;
        });
    }
    
    /**
     * 生成缓存键
     */
    private generateCacheKey(key: string, params?: IInterpolationParams, count?: number): string {
        let cacheKey = `${this._currentLanguage}:${key}`;
        
        if (params) {
            const paramStr = Object.keys(params)
                .sort()
                .map(k => `${k}=${params[k]}`)
                .join('&');
            cacheKey += `:${paramStr}`;
        }
        
        if (count !== undefined) {
            cacheKey += `:count=${count}`;
        }
        
        return cacheKey;
    }
    
    /**
     * 保存语言偏好
     */
    private saveLanguagePreference(language: SupportedLanguage): void {
        try {
            sys.localStorage.setItem('preferred_language', language);
        } catch (error) {
            console.warn('[LocalizationManager] 保存语言偏好失败:', error);
        }
    }
    
    /**
     * 加载语言偏好
     */
    private loadLanguagePreference(): SupportedLanguage | null {
        try {
            const saved = sys.localStorage.getItem('preferred_language');
            return saved as SupportedLanguage || null;
        } catch (error) {
            console.warn('[LocalizationManager] 加载语言偏好失败:', error);
            return null;
        }
    }
    
    /**
     * 发送事件
     */
    private emitEvent(eventName: string, data: any): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`i18n_${eventName}`, data);
        }
    }
}
