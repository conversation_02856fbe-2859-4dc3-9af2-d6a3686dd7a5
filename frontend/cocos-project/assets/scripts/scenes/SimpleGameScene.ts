import { _decorator, Component, Node } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 简化版游戏场景控制器
 * 用于测试反序列化问题
 */
@ccclass('SimpleGameScene')
export class SimpleGameScene extends Component {
    
    @property(Node)
    mainNode: Node = null;
    
    protected onLoad(): void {
        console.log('[SimpleGameScene] 简化版游戏场景已加载');
    }
    
    protected start(): void {
        console.log('[SimpleGameScene] 简化版游戏场景已启动');
    }
    
    protected onDestroy(): void {
        console.log('[SimpleGameScene] 简化版游戏场景已销毁');
    }
}