import { _decorator, Component, Node, Button, Label, ProgressBar, tween, Vec3, Canvas, UITransform, view, Size } from 'cc';
import { GameManager } from '../managers/GameManager';
import { SceneManager } from '../managers/SceneManager';
import { EventManager } from '../managers/EventManager';
import { WechatAPI } from '../utils/WechatAPI';
import { IGameSession } from '../data/GameData';
import { BaseComponent } from '../utils/BaseComponent';

const { ccclass, property } = _decorator;

/**
 * 结果场景控制器
 * 负责显示游戏结果、统计信息和后续操作
 */
@ccclass('ResultScene')
export class ResultScene extends BaseComponent {
    
    // ==================== 节点引用 ====================
    
    @property(Canvas)
    mainCanvas: Canvas = null;
    
    @property(Node)
    resultPanel: Node = null;
    
    // 成绩展示区域
    @property(Node)
    scorePanel: Node = null;
    
    @property(Label)
    totalScoreLabel: Label = null;
    
    @property(Label)
    accuracyLabel: Label = null;
    
    @property(Label)
    correctCountLabel: Label = null;
    
    @property(Label)
    maxComboLabel: Label = null;
    
    @property(ProgressBar)
    accuracyProgressBar: ProgressBar = null;
    
    // 评价区域
    @property(Node)
    evaluationPanel: Node = null;
    
    @property(Label)
    evaluationTitleLabel: Label = null;
    
    @property(Label)
    evaluationDescLabel: Label = null;
    
    @property(Node)
    evaluationStarsNode: Node = null;
    
    // 详细统计区域
    @property(Node)
    statisticsPanel: Node = null;
    
    @property(Label)
    playTimeLabel: Label = null;
    
    @property(Label)
    averageAnswerTimeLabel: Label = null;
    
    @property(Label)
    audioPlayCountLabel: Label = null;
    
    // 操作按钮区域
    @property(Node)
    actionPanel: Node = null;
    
    @property(Button)
    shareButton: Button = null;
    
    @property(Button)
    playAgainButton: Button = null;
    
    @property(Button)
    backToMenuButton: Button = null;
    
    @property(Button)
    viewDetailButton: Button = null;
    
    // 分享面板
    @property(Node)
    sharePanel: Node = null;
    
    @property(Button)
    shareToFriendButton: Button = null;
    
    @property(Button)
    shareToTimelineButton: Button = null;
    
    @property(Button)
    closeSharePanelButton: Button = null;
    
    // 详细结果面板
    @property(Node)
    detailPanel: Node = null;
    
    @property(Node)
    detailScrollView: Node = null;
    
    @property(Button)
    closeDetailPanelButton: Button = null;
    
    // 排行榜面板
    @property(Node)
    leaderboardPanel: Node = null;
    
    @property(Button)
    showLeaderboardButton: Button = null;
    
    @property(Button)
    closeLeaderboardButton: Button = null;
    
    // ==================== 私有属性 ====================
    
    private _gameManager: GameManager = null;
    private _sceneManager: SceneManager = null;
    private _wechatAPI: WechatAPI = null;
    
    // 游戏会话数据
    private _gameSession: IGameSession = null;
    
    // 屏幕适配
    private _screenSize: Size = new Size();
    
    // 动画状态
    private _animationCompleted: boolean = false;
    
    // ==================== 生命周期 ====================
    
    protected onLoad(): void {
        this.initializeManagers();
        this.setupUI();
        this.setupEventListeners();
        this.updateScreenLayout();
    }
    
    protected start(): void {
        this.loadGameResult();
        this.playEnterAnimation();
    }
    
    protected onDestroy(): void {
        this.cleanup();
    }
    
    // ==================== 初始化 ====================
    
    /**
     * 初始化管理器
     */
    private initializeManagers(): void {
        this._gameManager = GameManager.instance;
        this._sceneManager = SceneManager.instance;
        this._wechatAPI = WechatAPI.getInstance();
    }
    
    /**
     * 设置UI
     */
    private setupUI(): void {
        // 隐藏所有弹出面板
        this.hideAllPopupPanels();
        
        // 初始化进度条
        if (this.accuracyProgressBar) {
            this.accuracyProgressBar.progress = 0;
        }
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 操作按钮事件
        this.shareButton?.node.on('click', this.onShareClick, this);
        this.playAgainButton?.node.on('click', this.onPlayAgainClick, this);
        this.backToMenuButton?.node.on('click', this.onBackToMenuClick, this);
        this.viewDetailButton?.node.on('click', this.onViewDetailClick, this);
        this.showLeaderboardButton?.node.on('click', this.onShowLeaderboardClick, this);
        
        // 分享面板按钮事件
        this.shareToFriendButton?.node.on('click', this.onShareToFriendClick, this);
        this.shareToTimelineButton?.node.on('click', this.onShareToTimelineClick, this);
        this.closeSharePanelButton?.node.on('click', this.onCloseSharePanelClick, this);
        
        // 详细面板按钮事件
        this.closeDetailPanelButton?.node.on('click', this.onCloseDetailPanelClick, this);
        
        // 排行榜面板按钮事件
        this.closeLeaderboardButton?.node.on('click', this.onCloseLeaderboardClick, this);
        
        // 屏幕尺寸变化
        view.on('canvas-resize', this.onScreenResize, this);
    }
    
    // ==================== 屏幕适配 ====================
    
    /**
     * 更新屏幕布局
     */
    private updateScreenLayout(): void {
        const visibleSize = view.getVisibleSize();
        this._screenSize.width = visibleSize.width;
        this._screenSize.height = visibleSize.height;
        
        // 适配Canvas尺寸
        if (this.mainCanvas) {
            const canvasTransform = this.mainCanvas.getComponent(UITransform);
            if (canvasTransform) {
                canvasTransform.setContentSize(this._screenSize);
            }
        }
        
        // 适配各个面板的位置
        this.layoutResultPanels();
    }
    
    /**
     * 布局结果面板
     */
    private layoutResultPanels(): void {
        const screenHeight = this._screenSize.height;
        
        // 成绩面板 - 顶部
        if (this.scorePanel) {
            const scoreY = screenHeight * 0.25;
            this.scorePanel.setPosition(0, scoreY, 0);
        }
        
        // 评价面板 - 中上部
        if (this.evaluationPanel) {
            const evaluationY = screenHeight * 0.05;
            this.evaluationPanel.setPosition(0, evaluationY, 0);
        }
        
        // 统计面板 - 中下部
        if (this.statisticsPanel) {
            const statisticsY = -screenHeight * 0.15;
            this.statisticsPanel.setPosition(0, statisticsY, 0);
        }
        
        // 操作面板 - 底部
        if (this.actionPanel) {
            const actionY = -screenHeight * 0.35;
            this.actionPanel.setPosition(0, actionY, 0);
        }
    }
    
    /**
     * 屏幕尺寸变化处理
     */
    private onScreenResize(): void {
        this.scheduleOnce(() => {
            this.updateScreenLayout();
        }, 0.1);
    }
    
    // ==================== 游戏结果处理 ====================
    
    /**
     * 加载游戏结果
     */
    private loadGameResult(): void {
        this._gameSession = this._gameManager.getCurrentSession();
        
        if (!this._gameSession) {
            console.error('[ResultScene] 无法获取游戏会话数据');
            this.backToMainMenu();
            return;
        }
        
        // 更新结果显示
        this.updateScoreDisplay();
        this.updateEvaluationDisplay();
        this.updateStatisticsDisplay();
        
        console.log('[ResultScene] 游戏结果加载完成:', this._gameSession);
    }
    
    /**
     * 更新成绩显示
     */
    private updateScoreDisplay(): void {
        if (!this._gameSession) return;
        
        const totalQuestions = this._gameSession.questions.length;
        const correctCount = this._gameSession.correctCount;
        const accuracy = totalQuestions > 0 ? (correctCount / totalQuestions) : 0;
        
        // 总分
        if (this.totalScoreLabel) {
            this.totalScoreLabel.string = this._gameSession.totalScore.toString();
        }
        
        // 正确题数
        if (this.correctCountLabel) {
            this.correctCountLabel.string = `${correctCount}/${totalQuestions}`;
        }
        
        // 正确率
        if (this.accuracyLabel) {
            this.accuracyLabel.string = `${Math.round(accuracy * 100)}%`;
        }
        
        // 最高连击
        if (this.maxComboLabel) {
            this.maxComboLabel.string = this._gameSession.comboCount.toString();
        }
        
        // 正确率进度条（稍后用动画更新）
        if (this.accuracyProgressBar) {
            this.accuracyProgressBar.progress = 0;
            // 动画会在稍后播放
        }
    }
    
    /**
     * 更新评价显示
     */
    private updateEvaluationDisplay(): void {
        if (!this._gameSession) return;
        
        const totalQuestions = this._gameSession.questions.length;
        const correctCount = this._gameSession.correctCount;
        const accuracy = totalQuestions > 0 ? (correctCount / totalQuestions) : 0;
        
        // 根据正确率确定评价等级
        const evaluation = this.getEvaluationByAccuracy(accuracy);
        
        if (this.evaluationTitleLabel) {
            this.evaluationTitleLabel.string = evaluation.title;
        }
        
        if (this.evaluationDescLabel) {
            this.evaluationDescLabel.string = evaluation.description;
        }
        
        // 更新星级显示
        this.updateStarsDisplay(evaluation.stars);
    }
    
    /**
     * 更新统计信息显示
     */
    private updateStatisticsDisplay(): void {
        if (!this._gameSession) return;
        
        // 游戏时长
        const playTime = this._gameSession.endTime - this._gameSession.startTime;
        const playTimeInSeconds = Math.floor(playTime / 1000);
        const minutes = Math.floor(playTimeInSeconds / 60);
        const seconds = playTimeInSeconds % 60;
        
        if (this.playTimeLabel) {
            this.playTimeLabel.string = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 平均答题时间
        const totalAnswerTime = this._gameSession.answers.reduce((sum, answer) => sum + answer.answerTime, 0);
        const averageAnswerTime = this._gameSession.answers.length > 0 ? 
            totalAnswerTime / this._gameSession.answers.length : 0;
        
        if (this.averageAnswerTimeLabel) {
            this.averageAnswerTimeLabel.string = `${(averageAnswerTime / 1000).toFixed(1)}秒`;
        }
        
        // 音频播放总次数
        const totalAudioPlays = this._gameSession.answers.reduce((sum, answer) => sum + answer.audioPlayCount, 0);
        
        if (this.audioPlayCountLabel) {
            this.audioPlayCountLabel.string = totalAudioPlays.toString();
        }
    }
    
    /**
     * 根据正确率获取评价
     */
    private getEvaluationByAccuracy(accuracy: number): { title: string, description: string, stars: number } {
        if (accuracy >= 0.9) {
            return {
                title: '方言大师',
                description: '你对方言的掌握令人惊叹！',
                stars: 5
            };
        } else if (accuracy >= 0.8) {
            return {
                title: '方言专家',
                description: '你对方言有很深的了解！',
                stars: 4
            };
        } else if (accuracy >= 0.6) {
            return {
                title: '方言达人',
                description: '你的方言知识不错！',
                stars: 3
            };
        } else if (accuracy >= 0.4) {
            return {
                title: '方言学习者',
                description: '继续努力，你会越来越好的！',
                stars: 2
            };
        } else {
            return {
                title: '方言新手',
                description: '不要灰心，多练习就会进步！',
                stars: 1
            };
        }
    }
    
    /**
     * 更新星级显示
     */
    private updateStarsDisplay(starCount: number): void {
        if (!this.evaluationStarsNode) return;
        
        // 这里需要根据实际的星级UI实现来更新
        // 可以是多个星星Node的显示/隐藏，或者是单个Node的不同状态
        
        console.log(`[ResultScene] 更新星级显示: ${starCount} 星`);
    }
    
    // ==================== 动画效果 ====================
    
    /**
     * 播放进入动画
     */
    private playEnterAnimation(): void {
        const panels = [
            this.scorePanel,
            this.evaluationPanel,
            this.statisticsPanel,
            this.actionPanel
        ].filter(panel => panel);
        
        // 面板依次出现
        panels.forEach((panel, index) => {
            if (panel) {
                panel.setScale(0.8, 0.8, 1);
                panel.getComponent(UITransform).setOpacity(0);
                
                tween(panel)
                    .delay(index * 0.2)
                    .parallel(
                        tween().to(0.4, { scale: new Vec3(1, 1, 1) }),
                        tween(panel.getComponent(UITransform)).to(0.4, { opacity: 255 })
                    )
                    .start();
            }
        });
        
        // 播放正确率进度条动画
        this.scheduleOnce(() => {
            this.playAccuracyAnimation();
        }, 0.8);
        
        // 播放星级动画
        this.scheduleOnce(() => {
            this.playStarsAnimation();
        }, 1.2);
        
        // 标记动画完成
        this.scheduleOnce(() => {
            this._animationCompleted = true;
        }, 1.5);
    }
    
    /**
     * 播放正确率动画
     */
    private playAccuracyAnimation(): void {
        if (!this.accuracyProgressBar || !this._gameSession) return;
        
        const totalQuestions = this._gameSession.questions.length;
        const correctCount = this._gameSession.correctCount;
        const targetAccuracy = totalQuestions > 0 ? (correctCount / totalQuestions) : 0;
        
        tween(this.accuracyProgressBar)
            .to(1.0, { progress: targetAccuracy })
            .start();
    }
    
    /**
     * 播放星级动画
     */
    private playStarsAnimation(): void {
        if (!this.evaluationStarsNode) return;
        
        // 星级闪烁效果
        tween(this.evaluationStarsNode)
            .to(0.2, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    // ==================== UI面板管理 ====================
    
    /**
     * 隐藏所有弹出面板
     */
    private hideAllPopupPanels(): void {
        if (this.sharePanel) {
            this.sharePanel.active = false;
        }
        
        if (this.detailPanel) {
            this.detailPanel.active = false;
        }
        
        if (this.leaderboardPanel) {
            this.leaderboardPanel.active = false;
        }
    }
    
    /**
     * 显示分享面板
     */
    private showSharePanel(): void {
        if (!this.sharePanel) return;
        
        this.sharePanel.active = true;
        this.sharePanel.setScale(0.8, 0.8, 1);
        
        tween(this.sharePanel)
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /**
     * 隐藏分享面板
     */
    private hideSharePanel(): void {
        if (!this.sharePanel) return;
        
        tween(this.sharePanel)
            .to(0.3, { scale: new Vec3(0.8, 0.8, 1) })
            .call(() => {
                this.sharePanel.active = false;
            })
            .start();
    }
    
    /**
     * 显示详细面板
     */
    private showDetailPanel(): void {
        if (!this.detailPanel) return;
        
        this.detailPanel.active = true;
        this.detailPanel.setScale(0.9, 0.9, 1);
        
        tween(this.detailPanel)
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();
        
        // 加载详细数据
        this.loadDetailData();
    }
    
    /**
     * 隐藏详细面板
     */
    private hideDetailPanel(): void {
        if (!this.detailPanel) return;
        
        tween(this.detailPanel)
            .to(0.3, { scale: new Vec3(0.9, 0.9, 1) })
            .call(() => {
                this.detailPanel.active = false;
            })
            .start();
    }
    
    /**
     * 加载详细数据
     */
    private loadDetailData(): void {
        if (!this._gameSession || !this.detailScrollView) return;
        
        // 这里需要根据实际的ScrollView实现来动态创建详细结果项
        // 包括每题的答题情况、用时、播放次数等
        
        console.log('[ResultScene] 加载详细结果数据');
    }
    
    // ==================== 事件处理器 ====================
    
    /**
     * 分享按钮点击
     */
    private onShareClick(): void {
        if (!this._animationCompleted) return;
        
        this.showSharePanel();
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 再玩一次按钮点击
     */
    private async onPlayAgainClick(): Promise<void> {
        if (!this._animationCompleted) return;
        
        this._wechatAPI.vibrateShort({ type: 'medium' });
        
        try {
            const difficulty = this._gameSession?.difficulty || 'medium';
            await this._gameManager.startNewGame(difficulty as any);
        } catch (error) {
            console.error('[ResultScene] 重新开始游戏失败:', error);
            this._wechatAPI.showToast('重新开始游戏失败', 'error');
        }
    }
    
    /**
     * 返回菜单按钮点击
     */
    private onBackToMenuClick(): void {
        if (!this._animationCompleted) return;
        
        this._wechatAPI.vibrateShort({ type: 'light' });
        this.backToMainMenu();
    }
    
    /**
     * 查看详细按钮点击
     */
    private onViewDetailClick(): void {
        if (!this._animationCompleted) return;
        
        this.showDetailPanel();
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 显示排行榜按钮点击
     */
    private onShowLeaderboardClick(): void {
        if (!this._animationCompleted) return;
        
        // TODO: 实现排行榜功能
        this._wechatAPI.showToast('排行榜功能开发中', 'none');
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 分享给朋友按钮点击
     */
    private async onShareToFriendClick(): Promise<void> {
        const shareOptions = {
            title: `我在家乡话猜猜猜中得了${this._gameSession?.totalScore || 0}分！你能超过我吗？`,
            imageUrl: '', // 需要设置分享图片
            query: `score=${this._gameSession?.totalScore || 0}&accuracy=${Math.round(((this._gameSession?.correctCount || 0) / (this._gameSession?.questions.length || 1)) * 100)}`
        };
        
        try {
            await this._wechatAPI.shareAppMessage(shareOptions);
            this.hideSharePanel();
            this._wechatAPI.showToast('分享成功', 'success');
        } catch (error) {
            console.error('[ResultScene] 分享失败:', error);
            this._wechatAPI.showToast('分享失败', 'error');
        }
        
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 分享到朋友圈按钮点击
     */
    private onShareToTimelineClick(): void {
        const shareOptions = {
            title: `我在家乡话猜猜猜中得了${this._gameSession?.totalScore || 0}分！`,
            imageUrl: '', // 需要设置分享图片
            query: `score=${this._gameSession?.totalScore || 0}`
        };
        
        this._wechatAPI.setShareTimeline(shareOptions);
        this.hideSharePanel();
        this._wechatAPI.showToast('请通过右上角菜单分享到朋友圈', 'none');
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 关闭分享面板按钮点击
     */
    private onCloseSharePanelClick(): void {
        this.hideSharePanel();
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 关闭详细面板按钮点击
     */
    private onCloseDetailPanelClick(): void {
        this.hideDetailPanel();
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 关闭排行榜按钮点击
     */
    private onCloseLeaderboardClick(): void {
        // TODO: 隐藏排行榜面板
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 返回主菜单
     */
    private async backToMainMenu(): Promise<void> {
        try {
            await this._sceneManager.loadScene('MainMenuScene');
        } catch (error) {
            console.error('[ResultScene] 返回主菜单失败:', error);
            this._wechatAPI.showToast('返回主菜单失败', 'error');
        }
    }
    
    // ==================== 清理 ====================
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        // 移除按钮事件监听
        this.shareButton?.node.off('click', this.onShareClick, this);
        this.playAgainButton?.node.off('click', this.onPlayAgainClick, this);
        this.backToMenuButton?.node.off('click', this.onBackToMenuClick, this);
        this.viewDetailButton?.node.off('click', this.onViewDetailClick, this);
        this.showLeaderboardButton?.node.off('click', this.onShowLeaderboardClick, this);
        
        this.shareToFriendButton?.node.off('click', this.onShareToFriendClick, this);
        this.shareToTimelineButton?.node.off('click', this.onShareToTimelineClick, this);
        this.closeSharePanelButton?.node.off('click', this.onCloseSharePanelClick, this);
        
        this.closeDetailPanelButton?.node.off('click', this.onCloseDetailPanelClick, this);
        this.closeLeaderboardButton?.node.off('click', this.onCloseLeaderboardClick, this);
        
        // 移除屏幕事件监听
        view.off('canvas-resize', this.onScreenResize, this);
        
        // 执行BaseComponent清理
        this.onCleanup();
    }
}