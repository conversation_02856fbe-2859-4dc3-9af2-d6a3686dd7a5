import { _decorator, Component, Node, Label, Button, UITransform, ProgressBar, AudioSource, Sprite } from 'cc';
import { UISafetyHelper } from '../utils/UISafetyHelper';

const { ccclass, property } = _decorator;

/**
 * 游戏场景控制器
 * 负责游戏主场景的核心逻辑管理
 */
@ccclass('GameSceneController')
export class GameSceneController extends Component {
    // 主画布引用
    @property(UITransform)
    mainCanvas: UITransform = null;

    // 游戏面板
    @property(Node)
    gamePanel: Node = null;

    // 题目信息面板
    @property(Node)
    questionInfoPanel: Node = null;

    // 题目序号标签
    @property(Label)
    questionIndexLabel: Label = null;

    // 分数标签
    @property(Label)
    scoreLabel: Label = null;

    // 连击标签
    @property(Label)
    comboLabel: Label = null;

    // 时间进度条
    @property(ProgressBar)
    timeProgressBar: ProgressBar = null;

    // 音频控制面板
    @property(Node)
    audioControlPanel: Node = null;

    // 播放音频按钮
    @property(Button)
    playAudioButton: Button = null;

    // 播放次数标签
    @property(Label)
    playCountLabel: Label = null;

    // 波形节点
    @property(Node)
    audioWaveNode: Node = null;

    // 答案选项面板
    @property(Node)
    answerOptionsPanel: Node = null;

    // 选项按钮
    @property(Button)
    optionAButton: Button = null;
    @property(Button)
    optionBButton: Button = null;
    @property(Button)
    optionCButton: Button = null;
    @property(Button)
    optionDButton: Button = null;

    // 选项标签
    @property(Label)
    optionALabel: Label = null;
    @property(Label)
    optionBLabel: Label = null;
    @property(Label)
    optionCLabel: Label = null;
    @property(Label)
    optionDLabel: Label = null;

    // 游戏控制面板
    @property(Node)
    gameControlPanel: Node = null;

    // 暂停按钮
    @property(Button)
    pauseButton: Button = null;

    // 退出按钮
    @property(Button)
    quitButton: Button = null;

    // 结果反馈面板
    @property(Node)
    resultFeedbackPanel: Node = null;

    // 结果标签
    @property(Label)
    resultLabel: Label = null;

    // 正确答案标签
    @property(Label)
    correctAnswerLabel: Label = null;

    // 结果图标节点
    @property(Node)
    resultIconNode: Node = null;

    // 暂停面板
    @property(Node)
    pausePanel: Node = null;

    // 恢复按钮
    @property(Button)
    resumeButton: Button = null;

    // 重新开始按钮
    @property(Button)
    restartButton: Button = null;

    // 返回菜单按钮
    @property(Button)
    backToMenuButton: Button = null;

    // 音频源组件
    @property(AudioSource)
    audioSource: AudioSource = null;

    protected onLoad(): void {
        console.log('[GameSceneController] 游戏场景加载完成');
        this.initializeGame();
        this.setupButtonEvents();
    }

    protected start(): void {
        console.log('[GameSceneController] 游戏场景开始');
    }

    /**
     * 初始化游戏
     */
    private initializeGame(): void {
        this.setupUIBasics();
        this.hideAllPanels();
        this.showGamePanel();
    }

    /**
     * 设置UI基础配置
     */
    private setupUIBasics(): void {
        // 使用安全的文本设置方法
        UISafetyHelper.safeSetLabelText(this.questionIndexLabel, "1/10", "1/10");
        UISafetyHelper.safeSetLabelText(this.scoreLabel, "0分", "0分");
        UISafetyHelper.safeSetLabelText(this.comboLabel, "连击: 0", "");
        UISafetyHelper.safeSetLabelText(this.playCountLabel, "播放: 0/3", "0/3");
    }

    /**
     * 隐藏所有面板
     */
    private hideAllPanels(): void {
        if (this.resultFeedbackPanel) this.resultFeedbackPanel.active = false;
        if (this.pausePanel) this.pausePanel.active = false;
    }

    /**
     * 显示游戏面板
     */
    private showGamePanel(): void {
        if (this.gamePanel) {
            this.gamePanel.active = true;
        }
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 音频控制按钮事件
        if (this.playAudioButton) {
            this.playAudioButton.node.on(Button.EventType.CLICK, this.onPlayAudio, this);
        }

        // 答案选项按钮事件
        if (this.optionAButton) {
            this.optionAButton.node.on(Button.EventType.CLICK, () => this.onSelectOption('A'), this);
        }
        if (this.optionBButton) {
            this.optionBButton.node.on(Button.EventType.CLICK, () => this.onSelectOption('B'), this);
        }
        if (this.optionCButton) {
            this.optionCButton.node.on(Button.EventType.CLICK, () => this.onSelectOption('C'), this);
        }
        if (this.optionDButton) {
            this.optionDButton.node.on(Button.EventType.CLICK, () => this.onSelectOption('D'), this);
        }

        // 游戏控制按钮事件
        if (this.pauseButton) {
            this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseGame, this);
        }
        if (this.quitButton) {
            this.quitButton.node.on(Button.EventType.CLICK, this.onQuitGame, this);
        }

        // 暂停面板按钮事件
        if (this.resumeButton) {
            this.resumeButton.node.on(Button.EventType.CLICK, this.onResumeGame, this);
        }
        if (this.restartButton) {
            this.restartButton.node.on(Button.EventType.CLICK, this.onRestartGame, this);
        }
        if (this.backToMenuButton) {
            this.backToMenuButton.node.on(Button.EventType.CLICK, this.onBackToMenu, this);
        }
    }

    /**
     * 播放音频
     */
    private onPlayAudio(): void {
        console.log('[GameSceneController] 播放音频');
        if (this.audioSource && this.audioSource.clip) {
            this.audioSource.play();
        }
    }

    /**
     * 选择答案选项
     */
    private onSelectOption(option: string): void {
        console.log(`[GameSceneController] 选择了选项: ${option}`);
        this.showResultFeedback('?'); // 这里会是一个延迟显示的结果
    }

    /**
     * 暂停游戏
     */
    private onPauseGame(): void {
        console.log('[GameSceneController] 游戏暂停');
        if (this.pausePanel) {
            this.pausePanel.active = true;
        }
    }

    /**
     * 恢复游戏
     */
    private onResumeGame(): void {
        console.log('[GameSceneController] 恢复游戏');
        if (this.pausePanel) {
            this.pausePanel.active = false;
        }
    }

    /**
     * 重新开始游戏
     */
    private onRestartGame(): void {
        console.log('[GameSceneController] 重新开始游戏');
        this.initializeGame();
        if (this.pausePanel) {
            this.pausePanel.active = false;
        }
    }

    /**
     * 返回菜单
     */
    private onQuitGame(): void {
        console.log('[GameSceneController] 退出游戏');
        // TODO: 实现返回菜单逻辑
    }

    /**
     * 返回菜单
     */
    private onBackToMenu(): void {
        console.log('[GameSceneController] 返回菜单');
        // TODO: 实现返回菜单场景逻辑
    }

    /**
     * 显示结果反馈
     */
    private showResultFeedback(result: string): void {
        if (this.resultFeedbackPanel && this.resultLabel) {
            this.resultLabel.string = result;
            this.resultFeedbackPanel.active = true;
        }
    }
}