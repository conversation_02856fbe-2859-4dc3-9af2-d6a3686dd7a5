/**
 * 围观主界面场景控制器
 * 
 * 管理围观房间的主界面布局、导航逻辑和用户交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Canvas, UITransform, view, Size, tween, Vec3 } from 'cc';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { WatchRoomManager, RoomEvent } from '../managers/WatchRoomManager';
import { WatchNetworkManager } from '../managers/WatchNetworkManager';
import { ConnectionStatusIndicator } from '../components/ConnectionStatusIndicator';
import { RoomInfo, ConnectionStatus } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 界面区域枚举 */
export enum UIArea {
    STATUS_BAR = 'status_bar',
    GAME_AREA = 'game_area',
    BARRAGE_AREA = 'barrage_area',
    INTERACTION_AREA = 'interaction_area'
}

/** 布局配置 */
interface LayoutConfig {
    statusBarHeight: number;
    gameAreaRatio: number;
    barrageAreaRatio: number;
    interactionAreaRatio: number;
    margin: number;
    padding: number;
}

@ccclass('WatchMainScene')
export class WatchMainScene extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Canvas)
    mainCanvas: Canvas = null;
    
    @property(Node)
    statusBarArea: Node = null;
    
    @property(Node)
    gameArea: Node = null;
    
    @property(Node)
    barrageArea: Node = null;
    
    @property(Node)
    interactionArea: Node = null;
    
    @property(ConnectionStatusIndicator)
    connectionIndicator: ConnectionStatusIndicator = null;
    
    @property(Node)
    loadingOverlay: Node = null;
    
    @property(Node)
    errorOverlay: Node = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    private _roomManager: WatchRoomManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    /** 当前屏幕尺寸 */
    private _screenSize: Size = new Size();
    
    /** 布局配置 */
    private _layoutConfig: LayoutConfig = {
        statusBarHeight: 80,
        gameAreaRatio: 0.60,
        barrageAreaRatio: 0.25,
        interactionAreaRatio: 0.15,
        margin: 16,
        padding: 8
    };
    
    /** 当前房间信息 */
    private _currentRoom: RoomInfo = null;
    
    /** 界面状态 */
    private _isLayoutInitialized: boolean = false;
    private _isConnecting: boolean = false;

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeLayout();
        this.showLoadingOverlay();
    }
    
    protected start() {
        this.updateLayout();
        this.initializeConnection();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._roomManager = WatchRoomManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
        this._stateManager.on(WatchStateEvent.ROOM_JOINED, this.onRoomJoined, this);
        this._stateManager.on(WatchStateEvent.ROOM_LEFT, this.onRoomLeft, this);
        this._stateManager.on(WatchStateEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        
        // 监听房间事件
        this._roomManager.on(RoomEvent.ROOM_ENTERED, this.onRoomEntered, this);
        this._roomManager.on(RoomEvent.ROOM_ERROR, this.onRoomError, this);
        
        // 监听屏幕尺寸变化
        view.on('canvas-resize', this.onScreenResize, this);
    }
    
    /** 初始化布局 */
    private initializeLayout(): void {
        this.updateScreenSize();
        this.setupInitialLayout();
        this._isLayoutInitialized = true;
    }
    
    /** 初始化连接 */
    private async initializeConnection(): Promise<void> {
        if (this._isConnecting) return;
        
        this._isConnecting = true;
        
        try {
            // 尝试建立网络连接
            const connected = await this._networkManager.connect();
            if (connected) {
                console.log('Network connection established');
                this.hideLoadingOverlay();
            } else {
                throw new Error('Failed to establish network connection');
            }
        } catch (error) {
            console.error('Connection initialization failed:', error);
            this.showErrorOverlay('连接失败，请检查网络设置');
        } finally {
            this._isConnecting = false;
        }
    }

    // ==================== 布局管理 ====================
    
    /** 更新屏幕尺寸 */
    private updateScreenSize(): void {
        const visibleSize = view.getVisibleSize();
        this._screenSize.width = visibleSize.width;
        this._screenSize.height = visibleSize.height;
    }
    
    /** 设置初始布局 */
    private setupInitialLayout(): void {
        if (!this.mainCanvas) return;
        
        const canvasTransform = this.mainCanvas.getComponent(UITransform);
        if (canvasTransform) {
            canvasTransform.setContentSize(this._screenSize);
        }
        
        this.layoutStatusBar();
        this.layoutGameArea();
        this.layoutBarrageArea();
        this.layoutInteractionArea();
    }
    
    /** 更新布局 */
    public updateLayout(): void {
        if (!this._isLayoutInitialized) return;
        
        this.updateScreenSize();
        this.setupInitialLayout();
        
        // 触发布局更新动画
        this.playLayoutUpdateAnimation();
    }
    
    /** 布局状态栏 */
    private layoutStatusBar(): void {
        if (!this.statusBarArea) return;
        
        const transform = this.statusBarArea.getComponent(UITransform);
        if (transform) {
            const width = this._screenSize.width - this._layoutConfig.margin * 2;
            const height = this._layoutConfig.statusBarHeight;
            
            transform.setContentSize(width, height);
            
            // 定位到顶部
            const y = this._screenSize.height / 2 - height / 2 - this._layoutConfig.margin;
            this.statusBarArea.setPosition(0, y, 0);
        }
    }
    
    /** 布局游戏区域 */
    private layoutGameArea(): void {
        if (!this.gameArea) return;
        
        const transform = this.gameArea.getComponent(UITransform);
        if (transform) {
            const width = this._screenSize.width - this._layoutConfig.margin * 2;
            const availableHeight = this._screenSize.height - this._layoutConfig.statusBarHeight - this._layoutConfig.margin * 3;
            const height = availableHeight * this._layoutConfig.gameAreaRatio;
            
            transform.setContentSize(width, height);
            
            // 定位到状态栏下方
            const statusBarBottom = this._screenSize.height / 2 - this._layoutConfig.statusBarHeight - this._layoutConfig.margin * 2;
            const y = statusBarBottom - height / 2 - this._layoutConfig.padding;
            this.gameArea.setPosition(0, y, 0);
        }
    }
    
    /** 布局弹幕区域 */
    private layoutBarrageArea(): void {
        if (!this.barrageArea) return;
        
        const transform = this.barrageArea.getComponent(UITransform);
        if (transform) {
            const width = this._screenSize.width - this._layoutConfig.margin * 2;
            const availableHeight = this._screenSize.height - this._layoutConfig.statusBarHeight - this._layoutConfig.margin * 3;
            const height = availableHeight * this._layoutConfig.barrageAreaRatio;
            
            transform.setContentSize(width, height);
            
            // 定位到游戏区域下方
            const gameAreaTransform = this.gameArea.getComponent(UITransform);
            const gameAreaHeight = gameAreaTransform ? gameAreaTransform.height : 0;
            const gameAreaBottom = this.gameArea.position.y - gameAreaHeight / 2;
            const y = gameAreaBottom - height / 2 - this._layoutConfig.padding;
            this.barrageArea.setPosition(0, y, 0);
        }
    }
    
    /** 布局交互区域 */
    private layoutInteractionArea(): void {
        if (!this.interactionArea) return;
        
        const transform = this.interactionArea.getComponent(UITransform);
        if (transform) {
            const width = this._screenSize.width - this._layoutConfig.margin * 2;
            const availableHeight = this._screenSize.height - this._layoutConfig.statusBarHeight - this._layoutConfig.margin * 3;
            const height = availableHeight * this._layoutConfig.interactionAreaRatio;
            
            transform.setContentSize(width, height);
            
            // 定位到底部
            const y = -this._screenSize.height / 2 + height / 2 + this._layoutConfig.margin;
            this.interactionArea.setPosition(0, y, 0);
        }
    }
    
    /** 播放布局更新动画 */
    private playLayoutUpdateAnimation(): void {
        const areas = [this.statusBarArea, this.gameArea, this.barrageArea, this.interactionArea];
        
        areas.forEach((area, index) => {
            if (!area) return;
            
            // 淡入动画
            area.setScale(0.9, 0.9, 1);
            tween(area)
                .delay(index * 0.1)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        });
    }

    // ==================== 事件处理 ====================
    
    /** 连接状态变化处理 */
    private onConnectionStatusChanged(event: any): void {
        const { current } = event;
        
        switch (current) {
            case ConnectionStatus.CONNECTING:
                this.showLoadingOverlay('正在连接...');
                break;
                
            case ConnectionStatus.CONNECTED:
                this.hideLoadingOverlay();
                break;
                
            case ConnectionStatus.RECONNECTING:
                this.showLoadingOverlay('正在重连...');
                break;
                
            case ConnectionStatus.ERROR:
                this.showErrorOverlay('连接出现错误');
                break;
                
            case ConnectionStatus.DISCONNECTED:
                this.showErrorOverlay('连接已断开');
                break;
        }
    }
    
    /** 房间加入成功处理 */
    private onRoomJoined(roomInfo: RoomInfo): void {
        this._currentRoom = roomInfo;
        console.log('Joined room:', roomInfo.roomId);
        
        // 更新界面状态
        this.updateRoomDisplay();
    }
    
    /** 房间离开处理 */
    private onRoomLeft(roomInfo: RoomInfo): void {
        this._currentRoom = null;
        console.log('Left room:', roomInfo.roomId);
        
        // 清理界面状态
        this.clearRoomDisplay();
    }
    
    /** 房间进入成功处理 */
    private onRoomEntered(roomInfo: RoomInfo): void {
        console.log('Room entered successfully:', roomInfo.roomId);
        this.hideLoadingOverlay();
    }
    
    /** 房间错误处理 */
    private onRoomError(error: any): void {
        console.error('Room error:', error);
        this.showErrorOverlay(error.message || '房间操作失败');
    }
    
    /** 错误发生处理 */
    private onErrorOccurred(error: any): void {
        console.error('Watch error:', error);
        this.showErrorOverlay(error.message || '发生未知错误');
    }
    
    /** 屏幕尺寸变化处理 */
    private onScreenResize(): void {
        this.scheduleOnce(() => {
            this.updateLayout();
        }, 0.1);
    }

    // ==================== 界面状态管理 ====================
    
    /** 更新房间显示 */
    private updateRoomDisplay(): void {
        if (!this._currentRoom) return;
        
        // 这里可以更新房间相关的UI显示
        // 例如：玩家信息、游戏状态、围观人数等
    }
    
    /** 清理房间显示 */
    private clearRoomDisplay(): void {
        // 清理房间相关的UI显示
    }
    
    /** 显示加载覆盖层 */
    private showLoadingOverlay(message?: string): void {
        if (this.loadingOverlay) {
            this.loadingOverlay.active = true;
            
            // 更新加载消息
            if (message) {
                const label = this.loadingOverlay.getComponentInChildren(Label);
                if (label) {
                    label.string = message;
                }
            }
            
            // 淡入动画
            this.loadingOverlay.setScale(0, 0, 1);
            tween(this.loadingOverlay)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 隐藏加载覆盖层 */
    private hideLoadingOverlay(): void {
        if (this.loadingOverlay && this.loadingOverlay.active) {
            // 淡出动画
            tween(this.loadingOverlay)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.loadingOverlay.active = false;
                })
                .start();
        }
    }
    
    /** 显示错误覆盖层 */
    private showErrorOverlay(message: string): void {
        if (this.errorOverlay) {
            this.errorOverlay.active = true;
            
            // 更新错误消息
            const label = this.errorOverlay.getComponentInChildren(Label);
            if (label) {
                label.string = message;
            }
            
            // 淡入动画
            this.errorOverlay.setScale(0, 0, 1);
            tween(this.errorOverlay)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
        
        // 隐藏加载覆盖层
        this.hideLoadingOverlay();
    }
    
    /** 隐藏错误覆盖层 */
    private hideErrorOverlay(): void {
        if (this.errorOverlay && this.errorOverlay.active) {
            // 淡出动画
            tween(this.errorOverlay)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.errorOverlay.active = false;
                })
                .start();
        }
    }

    // ==================== 公共方法 ====================
    
    /** 进入指定房间 */
    public async enterRoom(roomId: string): Promise<boolean> {
        if (this._isConnecting) {
            console.log('Already connecting, please wait...');
            return false;
        }
        
        this.showLoadingOverlay('正在进入房间...');
        
        try {
            const result = await this._roomManager.enterRoom(roomId);
            if (result.success) {
                console.log('Successfully entered room:', roomId);
                return true;
            } else {
                throw new Error(result.error?.message || 'Failed to enter room');
            }
        } catch (error) {
            console.error('Failed to enter room:', error);
            this.showErrorOverlay(error.message || '进入房间失败');
            return false;
        }
    }
    
    /** 离开当前房间 */
    public async leaveRoom(): Promise<boolean> {
        if (!this._currentRoom) {
            return true;
        }
        
        try {
            const success = await this._roomManager.leaveRoom();
            if (success) {
                console.log('Successfully left room');
            }
            return success;
        } catch (error) {
            console.error('Failed to leave room:', error);
            return false;
        }
    }
    
    /** 获取指定区域节点 */
    public getAreaNode(area: UIArea): Node | null {
        switch (area) {
            case UIArea.STATUS_BAR:
                return this.statusBarArea;
            case UIArea.GAME_AREA:
                return this.gameArea;
            case UIArea.BARRAGE_AREA:
                return this.barrageArea;
            case UIArea.INTERACTION_AREA:
                return this.interactionArea;
            default:
                return null;
        }
    }
    
    /** 设置区域可见性 */
    public setAreaVisible(area: UIArea, visible: boolean): void {
        const node = this.getAreaNode(area);
        if (node) {
            node.active = visible;
        }
    }
    
    /** 重试连接 */
    public retryConnection(): void {
        this.hideErrorOverlay();
        this.initializeConnection();
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
            this._stateManager.off(WatchStateEvent.ROOM_JOINED, this.onRoomJoined, this);
            this._stateManager.off(WatchStateEvent.ROOM_LEFT, this.onRoomLeft, this);
            this._stateManager.off(WatchStateEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        }
        
        if (this._roomManager) {
            this._roomManager.off(RoomEvent.ROOM_ENTERED, this.onRoomEntered, this);
            this._roomManager.off(RoomEvent.ROOM_ERROR, this.onRoomError, this);
        }
        
        view.off('canvas-resize', this.onScreenResize, this);
    }
}
