import { _decorator, Component, Node, Button, Label, ProgressBar, tween, Vec3, Canvas, UITransform, view, Size, Layout } from 'cc';
import { GameManager } from '../managers/GameManager';
import { AudioManager } from '../managers/AudioManager';
import { EventManager } from '../managers/EventManager';
import { WechatAPI } from '../utils/WechatAPI';
import { GameState, AnswerResult } from '../constants/GameConstants';
import { IQuestionData, IAnswerRecord } from '../data/GameData';
import { BaseComponent } from '../utils/BaseComponent';
import { UISafetyHelper } from '../utils/UISafetyHelper';

const { ccclass, property } = _decorator;

/**
 * 游戏场景控制器
 * 负责游戏进行时的UI显示、用户交互和游戏逻辑
 */
@ccclass('GameScene')
export class GameScene extends BaseComponent {
    
    // ==================== 节点引用 ====================
    
    @property(Canvas)
    mainCanvas: Canvas = null;
    
    @property(Node)
    gamePanel: Node = null;
    
    // 题目信息区域
    @property(Node)
    questionInfoPanel: Node = null;
    
    @property(Label)
    questionIndexLabel: Label = null;
    
    @property(Label)
    scoreLabel: Label = null;
    
    @property(Label)
    comboLabel: Label = null;
    
    @property(ProgressBar)
    timeProgressBar: ProgressBar = null;
    
    // 音频控制区域
    @property(Node)
    audioControlPanel: Node = null;
    
    @property(Button)
    playAudioButton: Button = null;
    
    @property(Label)
    playCountLabel: Label = null;
    
    @property(Node)
    audioWaveNode: Node = null;
    
    // 答题选项区域
    @property(Node)
    answerOptionsPanel: Node = null;
    
    @property(Button)
    optionAButton: Button = null;
    
    @property(Button)
    optionBButton: Button = null;
    
    @property(Button)
    optionCButton: Button = null;
    
    @property(Button)
    optionDButton: Button = null;
    
    @property(Label)
    optionALabel: Label = null;
    
    @property(Label)
    optionBLabel: Label = null;
    
    @property(Label)
    optionCLabel: Label = null;
    
    @property(Label)
    optionDLabel: Label = null;
    
    // 游戏控制区域
    @property(Node)
    gameControlPanel: Node = null;
    
    @property(Button)
    pauseButton: Button = null;
    
    @property(Button)
    quitButton: Button = null;
    
    // 结果反馈区域
    @property(Node)
    resultFeedbackPanel: Node = null;
    
    @property(Label)
    resultLabel: Label = null;
    
    @property(Label)
    correctAnswerLabel: Label = null;
    
    @property(Node)
    resultIconNode: Node = null;
    
    // 暂停面板
    @property(Node)
    pausePanel: Node = null;
    
    @property(Button)
    resumeButton: Button = null;
    
    @property(Button)
    restartButton: Button = null;
    
    @property(Button)
    backToMenuButton: Button = null;
    
    // ==================== 私有属性 ====================
    
    private _gameManager: GameManager = null;
    private _audioManager: AudioManager = null;
    private _wechatAPI: WechatAPI = null;
    
    // 游戏状态
    private _currentQuestion: IQuestionData = null;
    private _selectedAnswer: number = -1;
    private _isAnswering: boolean = false;
    private _timeRemaining: number = 0;
    private _maxTime: number = 30; // 30秒答题时间
    
    // 定时器
    private _timeTimer: number = 0;
    
    // 答题选项按钮数组
    private _optionButtons: Button[] = [];
    private _optionLabels: Label[] = [];
    
    // 屏幕适配
    private _screenSize: Size = new Size();
    
    // ==================== 生命周期 ====================
    
    protected onLoad(): void {
        this.initializeManagers();
        this.setupUI();
        this.setupEventListeners();
        this.updateScreenLayout();
    }
    
    protected start(): void {
        this.startGame();
    }
    
    protected onDestroy(): void {
        this.cleanup();
    }
    
    // ==================== 初始化 ====================
    
    /**
     * 初始化管理器
     */
    private initializeManagers(): void {
        this._gameManager = GameManager.instance;
        this._audioManager = AudioManager.instance;
        this._wechatAPI = WechatAPI.getInstance();
    }
    
    /**
     * 设置UI
     */
    private setupUI(): void {
        // 初始化答题选项按钮数组
        this._optionButtons = [
            this.optionAButton,
            this.optionBButton,
            this.optionCButton,
            this.optionDButton
        ];
        
        this._optionLabels = [
            this.optionALabel,
            this.optionBLabel,
            this.optionCLabel,
            this.optionDLabel
        ];
        
        // 隐藏暂停面板和结果反馈面板
        if (this.pausePanel) {
            this.pausePanel.active = false;
        }
        
        if (this.resultFeedbackPanel) {
            this.resultFeedbackPanel.active = false;
        }
        
        // 初始化进度条
        if (this.timeProgressBar) {
            this.timeProgressBar.progress = 1.0;
        }
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 音频控制按钮
        this.playAudioButton?.node.on('click', this.onPlayAudioClick, this);
        
        // 答题选项按钮
        this._optionButtons.forEach((button, index) => {
            if (button) {
                button.node.on('click', () => this.onOptionClick(index), this);
            }
        });
        
        // 游戏控制按钮
        this.pauseButton?.node.on('click', this.onPauseClick, this);
        this.quitButton?.node.on('click', this.onQuitClick, this);
        
        // 暂停面板按钮
        this.resumeButton?.node.on('click', this.onResumeClick, this);
        this.restartButton?.node.on('click', this.onRestartClick, this);
        this.backToMenuButton?.node.on('click', this.onBackToMenuClick, this);
        
        // 游戏事件监听
        if (this._eventManager) {
            this.addEventListener('question_started', this.onQuestionStarted);
            this.addEventListener('answer_submitted', this.onAnswerSubmitted);
            this.addEventListener('game_ended', this.onGameEnded);
            this.addEventListener('game_paused', this.onGamePaused);
            this.addEventListener('game_resumed', this.onGameResumed);
            
            // 音频事件监听
            this.addEventListener('audio_play_started', this.onAudioPlayStarted);
            this.addEventListener('audio_play_complete', this.onAudioPlayComplete);
            this.addEventListener('audio_play_error', this.onAudioPlayError);
        }
        
        // 屏幕尺寸变化
        view.on('canvas-resize', this.onScreenResize, this);
    }
    
    // ==================== 屏幕适配 ====================
    
    /**
     * 更新屏幕布局
     */
    private updateScreenLayout(): void {
        const visibleSize = view.getVisibleSize();
        this._screenSize.width = visibleSize.width;
        this._screenSize.height = visibleSize.height;
        
        // 适配Canvas尺寸
        if (this.mainCanvas) {
            const canvasTransform = this.mainCanvas.getComponent(UITransform);
            if (canvasTransform) {
                canvasTransform.setContentSize(this._screenSize);
            }
        }
        
        // 适配各个面板的位置和大小
        this.layoutGamePanels();
    }
    
    /**
     * 布局游戏面板
     */
    private layoutGamePanels(): void {
        const screenHeight = this._screenSize.height;
        
        // 题目信息面板 - 顶部
        if (this.questionInfoPanel) {
            const infoY = screenHeight * 0.35;
            this.questionInfoPanel.setPosition(0, infoY, 0);
        }
        
        // 音频控制面板 - 中上部
        if (this.audioControlPanel) {
            const audioY = screenHeight * 0.1;
            this.audioControlPanel.setPosition(0, audioY, 0);
        }
        
        // 答题选项面板 - 中下部
        if (this.answerOptionsPanel) {
            const optionsY = -screenHeight * 0.15;
            this.answerOptionsPanel.setPosition(0, optionsY, 0);
        }
        
        // 游戏控制面板 - 底部
        if (this.gameControlPanel) {
            const controlY = -screenHeight * 0.4;
            this.gameControlPanel.setPosition(0, controlY, 0);
        }
    }
    
    /**
     * 屏幕尺寸变化处理
     */
    private onScreenResize(): void {
        this.scheduleOnce(() => {
            this.updateScreenLayout();
        }, 0.1);
    }
    
    // ==================== 游戏逻辑 ====================
    
    /**
     * 开始游戏
     */
    private startGame(): void {
        console.log('[GameScene] 游戏场景已加载');
        
        // 播放进入动画
        this.playEnterAnimation();
        
        // 游戏已经由GameManager启动，这里只需要等待题目开始事件
    }
    
    /**
     * 开始新题目
     */
    private startNewQuestion(questionData: any): void {
        const { question, questionIndex, totalQuestions } = questionData;
        
        this._currentQuestion = question;
        this._selectedAnswer = -1;
        this._isAnswering = true;
        this._timeRemaining = this._maxTime;
        
        // 更新题目信息显示
        this.updateQuestionInfo(questionIndex + 1, totalQuestions);
        
        // 更新答题选项
        this.updateAnswerOptions(question.options);
        
        // 启动计时器
        this.startTimer();
        
        // 重置答题选项状态
        this.resetAnswerOptions();
        
        // 隐藏结果反馈
        this.hideResultFeedback();
        
        // 更新音频播放信息
        this.updateAudioPlayInfo();
        
        console.log(`[GameScene] 开始第 ${questionIndex + 1} 题:`, question.id);
    }
    
    /**
     * 提交答案
     */
    private submitAnswer(selectedIndex: number): void {
        if (!this._isAnswering || this._selectedAnswer !== -1) {
            return;
        }
        
        this._selectedAnswer = selectedIndex;
        this._isAnswering = false;
        
        // 停止计时器
        this.stopTimer();
        
        // 高亮选中的答案
        this.highlightSelectedAnswer(selectedIndex);
        
        // 提交到游戏管理器
        this._gameManager.submitAnswer(selectedIndex);
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'medium' });
        
        console.log(`[GameScene] 提交答案: ${selectedIndex}`);
    }
    
    // ==================== UI更新 ====================
    
    /**
     * 更新题目信息
     */
    private updateQuestionInfo(currentIndex: number, totalQuestions: number): void {
        // 使用安全的文本设置方法
        UISafetyHelper.safeSetLabelText(
            this.questionIndexLabel,
            `${currentIndex}/${totalQuestions}`,
            `${currentIndex}/${totalQuestions}`
        );

        // 更新得分
        const currentScore = this._gameManager.getCurrentScore();
        UISafetyHelper.safeSetLabelText(
            this.scoreLabel,
            `得分: ${currentScore}`,
            `得分: 0`
        );

        // 更新连击数
        const currentCombo = this._gameManager.getCurrentCombo();
        const comboText = currentCombo > 0 ? `连击: ${currentCombo}` : '';
        UISafetyHelper.safeSetLabelText(
            this.comboLabel,
            comboText,
            ''
        );
    }
    
    /**
     * 更新答题选项
     */
    private updateAnswerOptions(options: string[]): void {
        this._optionLabels.forEach((label, index) => {
            if (label && options[index]) {
                // 使用安全的文本设置方法
                UISafetyHelper.safeSetLabelText(
                    label,
                    options[index],
                    `选项${index + 1}`
                );
            }
        });
    }
    
    /**
     * 重置答题选项状态
     */
    private resetAnswerOptions(): void {
        this._optionButtons.forEach((button) => {
            if (button) {
                button.interactable = true;
                button.node.setScale(1, 1, 1);
                
                // 重置颜色 - 这里需要根据实际UI设计调整
                // 可以通过修改button的normalColor等属性来实现
            }
        });
    }
    
    /**
     * 高亮选中的答案
     */
    private highlightSelectedAnswer(selectedIndex: number): void {
        this._optionButtons.forEach((button, index) => {
            if (button) {
                if (index === selectedIndex) {
                    // 高亮选中的按钮
                    tween(button.node)
                        .to(0.2, { scale: new Vec3(1.1, 1.1, 1) })
                        .start();
                } else {
                    // 禁用其他按钮
                    button.interactable = false;
                }
            }
        });
    }
    
    /**
     * 显示答题结果
     */
    private showAnswerResult(answerRecord: IAnswerRecord): void {
        if (!this.resultFeedbackPanel) return;
        
        this.resultFeedbackPanel.active = true;
        
        // 设置结果文本
        if (this.resultLabel) {
            const resultText = answerRecord.result === AnswerResult.CORRECT ? '回答正确！' : '回答错误！';
            this.resultLabel.string = resultText;
        }
        
        // 显示正确答案
        if (this.correctAnswerLabel && answerRecord.result === AnswerResult.WRONG) {
            const correctOption = this._currentQuestion.options[answerRecord.correctAnswer];
            this.correctAnswerLabel.string = `正确答案: ${correctOption}`;
        }
        
        // 播放结果动画
        this.playAnswerResultAnimation(answerRecord.result === AnswerResult.CORRECT);
    }
    
    /**
     * 隐藏结果反馈
     */
    private hideResultFeedback(): void {
        if (this.resultFeedbackPanel) {
            this.resultFeedbackPanel.active = false;
        }
    }
    
    /**
     * 更新音频播放信息
     */
    private updateAudioPlayInfo(): void {
        if (!this.playCountLabel || !this._currentQuestion) return;
        
        const playCount = this._audioManager.getPlayCount(this._currentQuestion.id);
        const maxPlays = 3; // 从游戏常量获取
        
        this.playCountLabel.string = `${playCount}/${maxPlays}`;
        
        // 更新播放按钮状态
        if (this.playAudioButton) {
            this.playAudioButton.interactable = playCount < maxPlays;
        }
    }
    
    // ==================== 计时器 ====================
    
    /**
     * 启动计时器
     */
    private startTimer(): void {
        this.stopTimer();
        
        this._timeTimer = setInterval(() => {
            this._timeRemaining--;
            
            // 更新进度条
            if (this.timeProgressBar) {
                const progress = this._timeRemaining / this._maxTime;
                this.timeProgressBar.progress = Math.max(0, progress);
            }
            
            // 时间用完
            if (this._timeRemaining <= 0) {
                this.onTimeUp();
            }
        }, 1000);
    }
    
    /**
     * 停止计时器
     */
    private stopTimer(): void {
        if (this._timeTimer) {
            clearInterval(this._timeTimer);
            this._timeTimer = 0;
        }
    }
    
    /**
     * 时间用完处理
     */
    private onTimeUp(): void {
        if (!this._isAnswering) return;
        
        console.log('[GameScene] 答题时间用完');
        
        this._isAnswering = false;
        this.stopTimer();
        
        // 自动提交错误答案
        const correctAnswer = this._currentQuestion.correctAnswer;
        let wrongAnswer = 0;
        while (wrongAnswer === correctAnswer) {
            wrongAnswer = Math.floor(Math.random() * 4);
        }
        
        this._selectedAnswer = wrongAnswer;
        this.highlightSelectedAnswer(wrongAnswer);
        this._gameManager.submitAnswer(wrongAnswer);
        
        // 震动反馈
        this._wechatAPI.vibrateLong();
    }
    
    // ==================== 动画效果 ====================
    
    /**
     * 播放进入动画
     */
    private playEnterAnimation(): void {
        const panels = [
            this.questionInfoPanel,
            this.audioControlPanel,
            this.answerOptionsPanel,
            this.gameControlPanel
        ].filter(panel => panel);
        
        panels.forEach((panel, index) => {
            if (panel) {
                panel.setScale(0.8, 0.8, 1);
                
                tween(panel)
                    .delay(index * 0.1)
                    .to(0.3, { scale: new Vec3(1, 1, 1) })
                    .start();
            }
        });
    }
    
    /**
     * 播放答题结果动画
     */
    private playAnswerResultAnimation(isCorrect: boolean): void {
        if (!this.resultFeedbackPanel) return;
        
        this.resultFeedbackPanel.setScale(0, 0, 1);
        
        const targetScale = isCorrect ? new Vec3(1.2, 1.2, 1) : new Vec3(1, 1, 1);
        
        tween(this.resultFeedbackPanel)
            .to(0.3, { scale: targetScale })
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /**
     * 播放音频波纹动画
     */
    private playAudioWaveAnimation(): void {
        if (!this.audioWaveNode) return;
        
        this.audioWaveNode.setScale(0.8, 0.8, 1);
        
        tween(this.audioWaveNode)
            .to(0.5, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.5, { scale: new Vec3(0.8, 0.8, 1) })
            .union()
            .repeatForever()
            .start();
    }
    
    /**
     * 停止音频波纹动画
     */
    private stopAudioWaveAnimation(): void {
        if (!this.audioWaveNode) return;
        
        this.audioWaveNode.stopAllActions();
        this.audioWaveNode.setScale(1, 1, 1);
    }
    
    // ==================== 事件处理器 ====================
    
    /**
     * 播放音频按钮点击
     */
    private async onPlayAudioClick(): Promise<void> {
        if (!this._currentQuestion || !this._gameManager.canPlayAudio()) {
            return;
        }
        
        try {
            await this._gameManager.playCurrentQuestionAudio();
        } catch (error) {
            console.error('[GameScene] 播放音频失败:', error);
            this._wechatAPI.showToast('音频播放失败', 'error');
        }
    }
    
    /**
     * 答题选项点击
     */
    private onOptionClick(selectedIndex: number): void {
        this.submitAnswer(selectedIndex);
    }
    
    /**
     * 暂停按钮点击
     */
    private onPauseClick(): void {
        this._gameManager.pauseGame();
    }
    
    /**
     * 退出按钮点击
     */
    private onQuitClick(): void {
        // 显示确认对话框
        if (confirm('确定要退出游戏吗？')) {
            this._gameManager.quitGame();
        }
    }
    
    /**
     * 恢复按钮点击
     */
    private onResumeClick(): void {
        this._gameManager.resumeGame();
    }
    
    /**
     * 重新开始按钮点击
     */
    private onRestartClick(): void {
        if (confirm('确定要重新开始游戏吗？')) {
            this._gameManager.restartGame();
        }
    }
    
    /**
     * 返回菜单按钮点击
     */
    private onBackToMenuClick(): void {
        if (confirm('确定要返回主菜单吗？')) {
            this._gameManager.quitGame();
        }
    }
    
    // ==================== 游戏事件处理器 ====================
    
    /**
     * 题目开始处理
     */
    private onQuestionStarted(data: any): void {
        this.startNewQuestion(data);
    }
    
    /**
     * 答案提交处理
     */
    private onAnswerSubmitted(data: any): void {
        const { answerRecord, currentScore, currentCombo, isCorrect } = data;
        
        // 显示答题结果
        this.showAnswerResult(answerRecord);
        
        // 更新得分和连击显示
        if (this.scoreLabel) {
            this.scoreLabel.string = `得分: ${currentScore}`;
        }
        
        if (this.comboLabel) {
            this.comboLabel.string = currentCombo > 0 ? `连击: ${currentCombo}` : '';
        }
    }
    
    /**
     * 游戏结束处理
     */
    private onGameEnded(data: any): void {
        console.log('[GameScene] 游戏结束:', data);
        
        // 停止计时器
        this.stopTimer();
        
        // 禁用所有交互
        this._isAnswering = false;
        
        // 场景切换由GameManager处理
    }
    
    /**
     * 游戏暂停处理
     */
    private onGamePaused(data: any): void {
        console.log('[GameScene] 游戏暂停');
        
        // 显示暂停面板
        if (this.pausePanel) {
            this.pausePanel.active = true;
        }
        
        // 停止计时器
        this.stopTimer();
    }
    
    /**
     * 游戏恢复处理
     */
    private onGameResumed(data: any): void {
        console.log('[GameScene] 游戏恢复');
        
        // 隐藏暂停面板
        if (this.pausePanel) {
            this.pausePanel.active = false;
        }
        
        // 重新启动计时器（如果正在答题）
        if (this._isAnswering) {
            this.startTimer();
        }
    }
    
    /**
     * 音频播放开始处理
     */
    private onAudioPlayStarted(data: any): void {
        console.log('[GameScene] 音频播放开始:', data);
        
        // 更新播放次数显示
        this.updateAudioPlayInfo();
        
        // 播放音频波纹动画
        this.playAudioWaveAnimation();
    }
    
    /**
     * 音频播放完成处理
     */
    private onAudioPlayComplete(data: any): void {
        console.log('[GameScene] 音频播放完成:', data);
        
        // 停止音频波纹动画
        this.stopAudioWaveAnimation();
    }
    
    /**
     * 音频播放错误处理
     */
    private onAudioPlayError(data: any): void {
        console.error('[GameScene] 音频播放错误:', data);
        
        // 停止音频波纹动画
        this.stopAudioWaveAnimation();
        
        // 显示错误提示
        this._wechatAPI.showToast('音频播放失败', 'error');
    }
    
    // ==================== 清理 ====================
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        // 停止计时器
        this.stopTimer();
        
        // 停止所有动画
        this.stopAudioWaveAnimation();
        
        // 移除按钮事件监听
        this.playAudioButton?.node.off('click', this.onPlayAudioClick, this);
        
        this._optionButtons.forEach((button, index) => {
            if (button) {
                button.node.off('click');
            }
        });
        
        this.pauseButton?.node.off('click', this.onPauseClick, this);
        this.quitButton?.node.off('click', this.onQuitClick, this);
        this.resumeButton?.node.off('click', this.onResumeClick, this);
        this.restartButton?.node.off('click', this.onRestartClick, this);
        this.backToMenuButton?.node.off('click', this.onBackToMenuClick, this);
        
        // 移除屏幕事件监听
        view.off('canvas-resize', this.onScreenResize, this);
        
        // 执行BaseComponent清理
        this.onCleanup();
    }
}