import { _decorator, Component, Node, Button, Label, tween, Vec3, Canvas, UITransform, view, Size } from 'cc';
import { GameManager } from '../managers/GameManager';
import { SceneManager } from '../managers/SceneManager';
import { EventManager } from '../managers/EventManager';
import { WechatAPI } from '../utils/WechatAPI';
import { GameDifficulty } from '../constants/GameConstants';
import { BaseComponent } from '../utils/BaseComponent';

const { ccclass, property } = _decorator;

/**
 * 主菜单场景控制器
 * 负责主菜单的UI显示、用户交互和场景跳转
 */
@ccclass('MainMenuScene')
export class MainMenuScene extends BaseComponent {
    
    // ==================== 节点引用 ====================
    
    @property(Canvas)
    mainCanvas: Canvas = null;
    
    @property(Node)
    titleNode: Node = null;
    
    @property(Node)
    menuPanel: Node = null;
    
    @property(Button)
    startGameButton: Button = null;
    
    @property(Button)
    watchGameButton: Button = null;
    
    @property(Button)
    learnModeButton: Button = null;
    
    @property(Button)
    settingsButton: Button = null;
    
    @property(Node)
    userInfoPanel: Node = null;
    
    @property(Label)
    userNameLabel: Label = null;
    
    @property(Node)
    userAvatarNode: Node = null;
    
    @property(Button)
    loginButton: Button = null;
    
    @property(Node)
    difficultyPanel: Node = null;
    
    @property(Button)
    easyModeButton: Button = null;
    
    @property(Button)
    mediumModeButton: Button = null;
    
    @property(Button)
    hardModeButton: Button = null;
    
    @property(Button)
    backButton: Button = null;
    
    @property(Node)
    loadingOverlay: Node = null;
    
    @property(Label)
    loadingLabel: Label = null;
    
    // ==================== 私有属性 ====================
    
    private _gameManager: GameManager = null;
    private _sceneManager: SceneManager = null;
    private _wechatAPI: WechatAPI = null;
    
    // 界面状态
    private _currentPanel: 'menu' | 'difficulty' = 'menu';
    private _isLoading: boolean = false;
    
    // 屏幕适配
    private _screenSize: Size = new Size();
    
    // ==================== 生命周期 ====================
    
    protected onLoad(): void {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeUI();
        this.checkUserLoginStatus();
    }
    
    protected start(): void {
        this.updateScreenLayout();
        this.playEnterAnimation();
    }
    
    protected onDestroy(): void {
        this.cleanup();
    }
    
    // ==================== 初始化 ====================
    
    /**
     * 初始化管理器
     */
    private initializeManagers(): void {
        this._gameManager = GameManager.instance;
        this._sceneManager = SceneManager.instance;
        this._wechatAPI = WechatAPI.getInstance();
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 按钮点击事件
        this.startGameButton?.node.on('click', this.onStartGameClick, this);
        this.watchGameButton?.node.on('click', this.onWatchGameClick, this);
        this.learnModeButton?.node.on('click', this.onLearnModeClick, this);
        this.settingsButton?.node.on('click', this.onSettingsClick, this);
        this.loginButton?.node.on('click', this.onLoginClick, this);
        
        // 难度选择按钮
        this.easyModeButton?.node.on('click', () => this.onDifficultySelect(GameDifficulty.EASY), this);
        this.mediumModeButton?.node.on('click', () => this.onDifficultySelect(GameDifficulty.MEDIUM), this);
        this.hardModeButton?.node.on('click', () => this.onDifficultySelect(GameDifficulty.HARD), this);
        this.backButton?.node.on('click', this.onBackClick, this);
        
        // 游戏管理器事件
        if (this._eventManager) {
            this.addEventListener('user-login-success', this.onUserLoginSuccess);
            this.addEventListener('user-login-failed', this.onUserLoginFailed);
            this.addEventListener('game_started', this.onGameStarted);
            
            // 微信相关事件
            this.addEventListener('wechat_show', this.onWechatShow);
            this.addEventListener('wechat_hide', this.onWechatHide);
        }
        
        // 屏幕尺寸变化
        view.on('canvas-resize', this.onScreenResize, this);
    }
    
    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 设置初始面板状态
        this.showMenuPanel();
        this.hideDifficultyPanel();
        this.hideLoadingOverlay();
        
        // 初始化用户信息显示
        this.updateUserInfoDisplay();
    }
    
    /**
     * 检查用户登录状态
     */
    private async checkUserLoginStatus(): Promise<void> {
        try {
            if (this._wechatAPI.isLoggedIn()) {
                const userInfo = this._wechatAPI.getCurrentUserInfo();
                if (userInfo) {
                    this.updateUserInfoDisplay(userInfo);
                }
            }
        } catch (error) {
            console.error('[MainMenuScene] 检查登录状态失败:', error);
        }
    }
    
    // ==================== 屏幕适配 ====================
    
    /**
     * 更新屏幕布局
     */
    private updateScreenLayout(): void {
        const visibleSize = view.getVisibleSize();
        this._screenSize.width = visibleSize.width;
        this._screenSize.height = visibleSize.height;
        
        // 适配Canvas尺寸
        if (this.mainCanvas) {
            const canvasTransform = this.mainCanvas.getComponent(UITransform);
            if (canvasTransform) {
                canvasTransform.setContentSize(this._screenSize);
            }
        }
        
        // 适配标题位置
        if (this.titleNode) {
            const titleY = this._screenSize.height * 0.35;
            this.titleNode.setPosition(0, titleY, 0);
        }
        
        // 适配菜单面板位置
        if (this.menuPanel) {
            const menuY = -this._screenSize.height * 0.1;
            this.menuPanel.setPosition(0, menuY, 0);
        }
        
        // 适配用户信息面板位置
        if (this.userInfoPanel) {
            const userInfoY = this._screenSize.height * 0.4;
            const userInfoX = -this._screenSize.width * 0.35;
            this.userInfoPanel.setPosition(userInfoX, userInfoY, 0);
        }
    }
    
    /**
     * 屏幕尺寸变化处理
     */
    private onScreenResize(): void {
        this.scheduleOnce(() => {
            this.updateScreenLayout();
        }, 0.1);
    }
    
    // ==================== 动画效果 ====================
    
    /**
     * 播放进入动画
     */
    private playEnterAnimation(): void {
        // 标题淡入动画
        if (this.titleNode) {
            this.titleNode.setScale(0.8, 0.8, 1);
            this.titleNode.getComponent(UITransform).setOpacity(0);
            
            tween(this.titleNode)
                .to(0.5, { scale: new Vec3(1, 1, 1) })
                .start();
                
            tween(this.titleNode.getComponent(UITransform))
                .to(0.5, { opacity: 255 })
                .start();
        }
        
        // 菜单按钮依次出现
        const menuButtons = [
            this.startGameButton?.node,
            this.watchGameButton?.node,
            this.learnModeButton?.node,
            this.settingsButton?.node
        ].filter(node => node);
        
        menuButtons.forEach((button, index) => {
            if (button) {
                button.setScale(0, 0, 1);
                
                tween(button)
                    .delay(index * 0.1 + 0.3)
                    .to(0.3, { scale: new Vec3(1, 1, 1) })
                    .start();
            }
        });
        
        // 用户信息面板动画
        if (this.userInfoPanel) {
            this.userInfoPanel.setScale(0.9, 0.9, 1);
            this.userInfoPanel.getComponent(UITransform).setOpacity(0);
            
            tween(this.userInfoPanel)
                .delay(0.6)
                .to(0.4, { scale: new Vec3(1, 1, 1) })
                .start();
                
            tween(this.userInfoPanel.getComponent(UITransform))
                .delay(0.6)
                .to(0.4, { opacity: 255 })
                .start();
        }
    }
    
    /**
     * 播放按钮点击动画
     */
    private playButtonClickAnimation(button: Node): void {
        if (!button) return;
        
        tween(button)
            .to(0.1, { scale: new Vec3(0.95, 0.95, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    // ==================== UI状态管理 ====================
    
    /**
     * 显示菜单面板
     */
    private showMenuPanel(): void {
        if (this.menuPanel) {
            this.menuPanel.active = true;
        }
        this._currentPanel = 'menu';
    }
    
    /**
     * 隐藏菜单面板
     */
    private hideMenuPanel(): void {
        if (this.menuPanel) {
            this.menuPanel.active = false;
        }
    }
    
    /**
     * 显示难度选择面板
     */
    private showDifficultyPanel(): void {
        if (this.difficultyPanel) {
            this.difficultyPanel.active = true;
            
            // 播放显示动画
            this.difficultyPanel.setScale(0.8, 0.8, 1);
            tween(this.difficultyPanel)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
        this._currentPanel = 'difficulty';
    }
    
    /**
     * 隐藏难度选择面板
     */
    private hideDifficultyPanel(): void {
        if (this.difficultyPanel) {
            this.difficultyPanel.active = false;
        }
    }
    
    /**
     * 显示加载覆盖层
     */
    private showLoadingOverlay(message: string = '加载中...'): void {
        if (this.loadingOverlay) {
            this.loadingOverlay.active = true;
            this._isLoading = true;
            
            if (this.loadingLabel) {
                this.loadingLabel.string = message;
            }
            
            // 播放显示动画
            this.loadingOverlay.setScale(0, 0, 1);
            tween(this.loadingOverlay)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /**
     * 隐藏加载覆盖层
     */
    private hideLoadingOverlay(): void {
        if (this.loadingOverlay && this.loadingOverlay.active) {
            tween(this.loadingOverlay)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.loadingOverlay.active = false;
                    this._isLoading = false;
                })
                .start();
        }
    }
    
    /**
     * 更新用户信息显示
     */
    private updateUserInfoDisplay(userInfo?: any): void {
        if (!this.userInfoPanel) return;
        
        if (userInfo) {
            // 显示用户信息
            this.userInfoPanel.active = true;
            
            if (this.userNameLabel) {
                this.userNameLabel.string = userInfo.nickName || '用户';
            }
            
            if (this.loginButton) {
                this.loginButton.node.active = false;
            }
        } else {
            // 显示登录按钮
            if (this.loginButton) {
                this.loginButton.node.active = true;
            }
            
            if (this.userNameLabel) {
                this.userNameLabel.string = '点击登录';
            }
        }
    }
    
    // ==================== 事件处理器 ====================
    
    /**
     * 开始游戏按钮点击
     */
    private onStartGameClick(): void {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.startGameButton.node);
        
        // 显示难度选择面板
        this.hideMenuPanel();
        this.showDifficultyPanel();
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 围观游戏按钮点击
     */
    private onWatchGameClick(): void {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.watchGameButton.node);
        
        // 跳转到围观场景
        this.showLoadingOverlay('进入围观模式...');
        this._sceneManager.loadScene('WatchScene').then(() => {
            this.hideLoadingOverlay();
        }).catch((error) => {
            console.error('[MainMenuScene] 加载围观场景失败:', error);
            this.hideLoadingOverlay();
            this._wechatAPI.showToast('进入围观模式失败', 'error');
        });
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 学习模式按钮点击
     */
    private onLearnModeClick(): void {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.learnModeButton.node);
        
        // 跳转到学习模式场景
        this.showLoadingOverlay('进入学习模式...');
        this._sceneManager.loadScene('LearningScene').then(() => {
            this.hideLoadingOverlay();
        }).catch((error) => {
            console.error('[MainMenuScene] 加载学习场景失败:', error);
            this.hideLoadingOverlay();
            this._wechatAPI.showToast('进入学习模式失败', 'error');
        });
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 设置按钮点击
     */
    private onSettingsClick(): void {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.settingsButton.node);
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
        
        // TODO: 实现设置界面
        this._wechatAPI.showToast('设置功能开发中', 'none');
    }
    
    /**
     * 登录按钮点击
     */
    private async onLoginClick(): Promise<void> {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.loginButton.node);
        this.showLoadingOverlay('正在登录...');
        
        try {
            const success = await this._gameManager.loginUser();
            if (success) {
                this._wechatAPI.showToast('登录成功', 'success');
            }
        } catch (error) {
            console.error('[MainMenuScene] 登录失败:', error);
            this._wechatAPI.showToast('登录失败，请重试', 'error');
        } finally {
            this.hideLoadingOverlay();
        }
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 难度选择
     */
    private async onDifficultySelect(difficulty: GameDifficulty): Promise<void> {
        if (this._isLoading) return;
        
        const buttonNode = difficulty === GameDifficulty.EASY ? this.easyModeButton?.node :
                          difficulty === GameDifficulty.MEDIUM ? this.mediumModeButton?.node :
                          this.hardModeButton?.node;
        
        this.playButtonClickAnimation(buttonNode);
        
        this.showLoadingOverlay('开始游戏...');
        
        try {
            await this._gameManager.startNewGame(difficulty);
        } catch (error) {
            console.error('[MainMenuScene] 开始游戏失败:', error);
            this.hideLoadingOverlay();
            this._wechatAPI.showToast('开始游戏失败，请重试', 'error');
        }
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'medium' });
    }
    
    /**
     * 返回按钮点击
     */
    private onBackClick(): void {
        if (this._isLoading) return;
        
        this.playButtonClickAnimation(this.backButton.node);
        
        // 返回主菜单
        this.hideDifficultyPanel();
        this.showMenuPanel();
        
        // 震动反馈
        this._wechatAPI.vibrateShort({ type: 'light' });
    }
    
    /**
     * 用户登录成功处理
     */
    private onUserLoginSuccess(data: any): void {
        console.log('[MainMenuScene] 用户登录成功:', data);
        this.updateUserInfoDisplay(data);
    }
    
    /**
     * 用户登录失败处理
     */
    private onUserLoginFailed(error: any): void {
        console.error('[MainMenuScene] 用户登录失败:', error);
        this.updateUserInfoDisplay();
    }
    
    /**
     * 游戏开始处理
     */
    private onGameStarted(data: any): void {
        console.log('[MainMenuScene] 游戏已开始:', data.sessionId);
        // 游戏场景会自动加载，这里不需要额外处理
    }
    
    /**
     * 微信小程序显示处理
     */
    private onWechatShow(data: any): void {
        console.log('[MainMenuScene] 小程序显示:', data);
        // 刷新用户状态
        this.checkUserLoginStatus();
    }
    
    /**
     * 微信小程序隐藏处理
     */
    private onWechatHide(): void {
        console.log('[MainMenuScene] 小程序隐藏');
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 重置到主菜单状态
     */
    public resetToMainMenu(): void {
        this.hideDifficultyPanel();
        this.showMenuPanel();
        this.hideLoadingOverlay();
        this._currentPanel = 'menu';
    }
    
    /**
     * 设置分享信息
     */
    public setupShareInfo(): void {
        const shareOptions = {
            title: '家乡话猜猜猜 - 猜猜这是哪里的方言？',
            imageUrl: '', // 需要设置分享图片
            query: 'from=share'
        };
        
        this._wechatAPI.setShareTimeline(shareOptions);
    }
    
    // ==================== 清理 ====================
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        // 移除按钮事件监听
        this.startGameButton?.node.off('click', this.onStartGameClick, this);
        this.watchGameButton?.node.off('click', this.onWatchGameClick, this);
        this.learnModeButton?.node.off('click', this.onLearnModeClick, this);
        this.settingsButton?.node.off('click', this.onSettingsClick, this);
        this.loginButton?.node.off('click', this.onLoginClick, this);
        
        this.easyModeButton?.node.off('click');
        this.mediumModeButton?.node.off('click');
        this.hardModeButton?.node.off('click');
        this.backButton?.node.off('click', this.onBackClick, this);
        
        // 移除屏幕事件监听
        view.off('canvas-resize', this.onScreenResize, this);
        
        // 执行BaseComponent清理
        this.onCleanup();
    }
}