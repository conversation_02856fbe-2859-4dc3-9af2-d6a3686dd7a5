/**
 * 围观功能相关的TypeScript类型定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

// ==================== 基础类型定义 ====================

/** 用户信息接口 */
export interface UserInfo {
    userId: string;
    nickname: string;
    avatar: string;
    level: number;
    region: string;
    isVip: boolean;
    isOnline: boolean;
    joinTime: number;
}

/** 房间信息接口 */
export interface RoomInfo {
    roomId: string;
    playerInfo: UserInfo;
    gameStatus: 'waiting' | 'playing' | 'finished';
    viewerCount: number;
    currentQuestion?: {
        id: string;
        dialect: string;
        difficulty: 'easy' | 'medium' | 'hard';
        questionNumber: number;
        totalQuestions: number;
    };
    isLive: boolean;
    createdAt: number;
    updatedAt: number;
}

/** 连接状态枚举 */
export enum ConnectionStatus {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

/** 网络质量枚举 */
export enum NetworkQuality {
    EXCELLENT = 'excellent',
    GOOD = 'good',
    POOR = 'poor',
    DISCONNECTED = 'disconnected'
}

// ==================== 弹幕系统类型 ====================

/** 弹幕消息类型枚举 */
export enum BarrageMessageType {
    NORMAL = 'normal',
    PREDICTION = 'prediction',
    SYSTEM = 'system',
    VIP = 'vip',
    GIFT = 'gift'
}

/** 弹幕消息接口 */
export interface BarrageMessage {
    id: string;
    userId: string;
    nickname: string;
    avatar: string;
    content: string;
    type: BarrageMessageType;
    timestamp: number;
    userLevel: number;
    isVip: boolean;
    roomId: string;
}

/** 弹幕过滤配置接口 */
export interface BarrageFilterConfig {
    showPredictions: boolean;
    showSystemMessages: boolean;
    showVipMessages: boolean;
    hideBlockedUsers: boolean;
    maxLength: number;
    rateLimitPerMinute: number;
}

/** 弹幕发送结果接口 */
export interface BarrageSendResult {
    success: boolean;
    messageId?: string;
    error?: string;
    rateLimitInfo?: {
        remaining: number;
        resetTime: number;
    };
}

// ==================== 预测游戏类型 ====================

/** 预测选项接口 */
export interface PredictionOption {
    id: string;
    text: string;
    count: number;
    percentage: number;
}

/** 预测问题接口 */
export interface PredictionQuestion {
    id: string;
    questionText: string;
    options: PredictionOption[];
    timeLimit: number;
    startTime: number;
    endTime?: number;
    correctAnswer?: string;
}

/** 用户预测记录接口 */
export interface UserPrediction {
    questionId: string;
    selectedOption: string;
    timestamp: number;
    isCorrect?: boolean;
    pointsEarned?: number;
}

/** 预测结果接口 */
export interface PredictionResult {
    questionId: string;
    correctAnswer: string;
    userPrediction?: string;
    isCorrect: boolean;
    pointsEarned: number;
    rank: number;
    totalParticipants: number;
    streakCount: number;
}

/** 预测统计接口 */
export interface PredictionStats {
    totalPredictions: number;
    correctPredictions: number;
    accuracy: number;
    totalPoints: number;
    currentStreak: number;
    maxStreak: number;
}

// ==================== 游戏状态类型 ====================

/** 游戏阶段枚举 */
export enum GamePhase {
    WAITING = 'waiting',
    QUESTION_PLAYING = 'question_playing',
    ANSWER_REVEALING = 'answer_revealing',
    RESULT_SHOWING = 'result_showing',
    FINISHED = 'finished'
}

/** 游戏状态接口 */
export interface GameState {
    roomId: string;
    phase: GamePhase;
    currentQuestion: number;
    totalQuestions: number;
    timeRemaining: number;
    playerScore: number;
    playerAccuracy: number;
    averageResponseTime: number;
}

// ==================== WebSocket消息类型 ====================

/** WebSocket消息类型枚举 */
export enum WSMessageType {
    // 连接相关
    JOIN_ROOM = 'join_room',
    LEAVE_ROOM = 'leave_room',
    HEARTBEAT = 'heartbeat',
    
    // 弹幕相关
    BARRAGE_MESSAGE = 'barrage_message',
    BARRAGE_SEND = 'barrage_send',
    
    // 预测相关
    PREDICTION_START = 'prediction_start',
    PREDICTION_SUBMIT = 'prediction_submit',
    PREDICTION_RESULT = 'prediction_result',
    
    // 游戏状态
    GAME_STATE_UPDATE = 'game_state_update',
    VIEWER_COUNT_UPDATE = 'viewer_count_update',
    
    // 系统消息
    SYSTEM_MESSAGE = 'system_message',
    ERROR_MESSAGE = 'error_message'
}

/** WebSocket消息基础接口 */
export interface WSMessage {
    type: WSMessageType;
    timestamp: number;
    roomId?: string;
    data?: any;
}

/** 加入房间消息 */
export interface JoinRoomMessage extends WSMessage {
    type: WSMessageType.JOIN_ROOM;
    data: {
        roomId: string;
        userInfo: UserInfo;
    };
}

/** 弹幕消息 */
export interface BarrageWSMessage extends WSMessage {
    type: WSMessageType.BARRAGE_MESSAGE;
    data: BarrageMessage;
}

/** 预测提交消息 */
export interface PredictionSubmitMessage extends WSMessage {
    type: WSMessageType.PREDICTION_SUBMIT;
    data: {
        questionId: string;
        selectedOption: string;
        timestamp: number;
    };
}

// ==================== UI组件Props类型 ====================

/** 围观房间卡片Props */
export interface WatchRoomCardProps {
    roomInfo: RoomInfo;
    onEnterRoom: (roomId: string) => void;
    onRoomDetails: (roomId: string) => void;
}

/** 弹幕容器Props */
export interface BarrageContainerProps {
    messages: BarrageMessage[];
    maxHeight: number;
    autoScroll: boolean;
    filterConfig: BarrageFilterConfig;
    onPause: () => void;
    onResume: () => void;
    onMessageClick: (message: BarrageMessage) => void;
}

/** 预测面板Props */
export interface PredictionPanelProps {
    question: PredictionQuestion;
    userPrediction?: UserPrediction;
    disabled: boolean;
    onPredict: (optionId: string) => void;
}

/** 围观者列表Props */
export interface ViewerListProps {
    viewers: UserInfo[];
    maxDisplay: number;
    showOnlineStatus: boolean;
    onViewerClick: (userId: string) => void;
}

// ==================== 状态管理类型 ====================

/** 围观功能全局状态接口 */
export interface WatchState {
    // 连接状态
    connectionStatus: ConnectionStatus;
    networkQuality: NetworkQuality;
    
    // 房间信息
    currentRoom?: RoomInfo;
    roomList: RoomInfo[];
    
    // 用户信息
    currentUser?: UserInfo;
    viewers: UserInfo[];
    
    // 弹幕系统
    barrageMessages: BarrageMessage[];
    barrageFilter: BarrageFilterConfig;
    
    // 预测游戏
    currentPrediction?: PredictionQuestion;
    userPredictions: UserPrediction[];
    predictionStats: PredictionStats;
    
    // 游戏状态
    gameState?: GameState;
    
    // UI状态
    isBarrageInputVisible: boolean;
    isPredictionPanelVisible: boolean;
    isViewerListVisible: boolean;
}

/** 状态更新Action类型 */
export enum WatchActionType {
    // 连接相关
    SET_CONNECTION_STATUS = 'SET_CONNECTION_STATUS',
    SET_NETWORK_QUALITY = 'SET_NETWORK_QUALITY',
    
    // 房间相关
    SET_CURRENT_ROOM = 'SET_CURRENT_ROOM',
    UPDATE_ROOM_LIST = 'UPDATE_ROOM_LIST',
    UPDATE_VIEWER_COUNT = 'UPDATE_VIEWER_COUNT',
    
    // 弹幕相关
    ADD_BARRAGE_MESSAGE = 'ADD_BARRAGE_MESSAGE',
    UPDATE_BARRAGE_FILTER = 'UPDATE_BARRAGE_FILTER',
    CLEAR_BARRAGE_MESSAGES = 'CLEAR_BARRAGE_MESSAGES',
    
    // 预测相关
    SET_CURRENT_PREDICTION = 'SET_CURRENT_PREDICTION',
    ADD_USER_PREDICTION = 'ADD_USER_PREDICTION',
    UPDATE_PREDICTION_STATS = 'UPDATE_PREDICTION_STATS',
    
    // 游戏状态
    UPDATE_GAME_STATE = 'UPDATE_GAME_STATE',
    
    // UI状态
    TOGGLE_BARRAGE_INPUT = 'TOGGLE_BARRAGE_INPUT',
    TOGGLE_PREDICTION_PANEL = 'TOGGLE_PREDICTION_PANEL',
    TOGGLE_VIEWER_LIST = 'TOGGLE_VIEWER_LIST'
}

/** 状态更新Action接口 */
export interface WatchAction {
    type: WatchActionType;
    payload?: any;
}

// ==================== 错误处理类型 ====================

/** 错误类型枚举 */
export enum WatchErrorType {
    NETWORK_ERROR = 'network_error',
    CONNECTION_ERROR = 'connection_error',
    AUTHENTICATION_ERROR = 'authentication_error',
    PERMISSION_ERROR = 'permission_error',
    RATE_LIMIT_ERROR = 'rate_limit_error',
    VALIDATION_ERROR = 'validation_error',
    UNKNOWN_ERROR = 'unknown_error'
}

/** 错误信息接口 */
export interface WatchError {
    type: WatchErrorType;
    message: string;
    code?: string;
    details?: any;
    timestamp: number;
}

// ==================== 配置类型 ====================

/** 围观功能配置接口 */
export interface WatchConfig {
    // WebSocket配置
    websocketUrl: string;
    heartbeatInterval: number;
    reconnectAttempts: number;
    reconnectDelay: number;
    
    // 弹幕配置
    barrageMaxLength: number;
    barrageRateLimit: number;
    barrageAutoScroll: boolean;
    
    // 预测配置
    predictionTimeLimit: number;
    predictionMaxOptions: number;
    
    // UI配置
    maxViewersDisplay: number;
    animationDuration: number;
    
    // 性能配置
    maxBarrageMessages: number;
    updateInterval: number;
}
