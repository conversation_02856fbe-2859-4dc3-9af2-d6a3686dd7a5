import { _decorator, Component, director, Scene } from 'cc';
import { EventManager } from './EventManager';

const { ccclass } = _decorator;

/**
 * 场景管理器
 * 负责场景的加载、切换和管理
 */
@ccclass('SceneManager')
export class SceneManager extends Component {
    private static _instance: SceneManager = null;
    
    private _eventManager: EventManager = null;
    private _currentSceneName: string = '';
    private _isLoading: boolean = false;
    
    // 场景预加载缓存
    private _preloadedScenes: Map<string, Scene> = new Map();
    
    public static get instance(): SceneManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (SceneManager._instance === null) {
            SceneManager._instance = this;
        }
    }
    
    protected onDestroy(): void {
        if (SceneManager._instance === this) {
            SceneManager._instance = null;
        }
    }
    
    /**
     * 初始化场景管理器
     */
    public async initialize(): Promise<void> {
        try {
            console.log('[SceneManager] 初始化场景管理器');
            
            this._eventManager = EventManager.instance;
            this._currentSceneName = director.getScene().name || 'LoadingScene';
            
            console.log('[SceneManager] 场景管理器初始化完成');
        } catch (error) {
            console.error('[SceneManager] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载场景
     */
    public async loadScene(sceneName: string, onProgress?: (progress: number) => void): Promise<void> {
        if (this._isLoading) {
            console.warn('[SceneManager] 场景正在加载中，请稍候');
            return;
        }
        
        if (this._currentSceneName === sceneName) {
            console.log(`[SceneManager] 场景已经是当前场景: ${sceneName}`);
            return;
        }
        
        try {
            console.log(`[SceneManager] 开始加载场景: ${sceneName}`);
            this._isLoading = true;
            
            // 发送场景加载开始事件
            this._eventManager?.emit('scene_load_start', {
                sceneName: sceneName,
                previousScene: this._currentSceneName
            });
            
            // 检查是否有预加载的场景
            if (this._preloadedScenes.has(sceneName)) {
                console.log(`[SceneManager] 使用预加载的场景: ${sceneName}`);
                const scene = this._preloadedScenes.get(sceneName)!;
                director.runScene(scene);
                
                this._currentSceneName = sceneName;
                this._isLoading = false;
                
                this._eventManager?.emit('scene_loaded', {
                    sceneName: sceneName,
                    loadTime: 0
                });
                
                return;
            }
            
            // 加载场景
            const startTime = Date.now();
            
            await new Promise<void>((resolve, reject) => {
                director.loadScene(sceneName, (error: Error | null) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve();
                    }
                });
            });
            
            const loadTime = Date.now() - startTime;
            this._currentSceneName = sceneName;
            this._isLoading = false;
            
            console.log(`[SceneManager] 场景加载完成: ${sceneName}, 耗时: ${loadTime}ms`);
            
            // 发送场景加载完成事件
            this._eventManager?.emit('scene_loaded', {
                sceneName: sceneName,
                loadTime: loadTime
            });
            
        } catch (error) {
            this._isLoading = false;
            console.error(`[SceneManager] 场景加载失败: ${sceneName}`, error);
            
            // 发送场景加载失败事件
            this._eventManager?.emit('scene_load_error', {
                sceneName: sceneName,
                error: error
            });
            
            throw error;
        }
    }
    
    /**
     * 预加载场景
     */
    public async preloadScene(sceneName: string): Promise<void> {
        try {
            console.log(`[SceneManager] 开始预加载场景: ${sceneName}`);
            
            if (this._preloadedScenes.has(sceneName)) {
                console.log(`[SceneManager] 场景已预加载: ${sceneName}`);
                return;
            }
            
            // 这里可以实现场景预加载逻辑
            // 由于Cocos Creator的限制，实际预加载需要更复杂的实现
            
            console.log(`[SceneManager] 场景预加载完成: ${sceneName}`);
            
        } catch (error) {
            console.error(`[SceneManager] 场景预加载失败: ${sceneName}`, error);
        }
    }
    
    /**
     * 获取当前场景名称
     */
    public getCurrentSceneName(): string {
        return this._currentSceneName;
    }
    
    /**
     * 是否正在加载
     */
    public isLoading(): boolean {
        return this._isLoading;
    }
    
    /**
     * 清理预加载的场景
     */
    public clearPreloadedScenes(): void {
        this._preloadedScenes.clear();
        console.log('[SceneManager] 已清理预加载场景');
    }
}