import { _decorator, Component, game, sys, profiler } from 'cc';
import { DEBUG_CONFIG, WECHAT_CONFIG } from '../constants/GameConstants';
import { EventManager } from './EventManager';

const { ccclass } = _decorator;

/**
 * 性能监控管理器
 * 负责监控帧率、内存使用、音频性能等指标
 */
@ccclass('PerformanceManager')
export class PerformanceManager extends Component {
    private static _instance: PerformanceManager = null;
    
    // 性能监控数据
    private _performanceData = {
        fps: 0,
        frameTime: 0,
        memoryUsage: 0,
        audioMemory: 0,
        networkLatency: 0,
        renderCalls: 0,
        triangles: 0
    };
    
    // 性能统计
    private _performanceStats = {
        avgFPS: 0,
        minFPS: 60,
        maxFPS: 0,
        fpsHistory: [] as number[],
        memoryPeak: 0,
        memoryHistory: [] as number[],
        warningCount: 0,
        criticalCount: 0
    };
    
    // 性能阈值
    private _thresholds = {
        fps: {
            target: WECHAT_CONFIG.PERFORMANCE.FPS_TARGET,
            warning: 45,
            critical: 30
        },
        memory: {
            warning: WECHAT_CONFIG.PERFORMANCE.MEMORY_WARNING_THRESHOLD * 1024 * 1024, // 转换为字节
            critical: WECHAT_CONFIG.PERFORMANCE.GC_THRESHOLD * 1024 * 1024,
            gcTrigger: WECHAT_CONFIG.PERFORMANCE.GC_THRESHOLD * 1024 * 1024
        },
        frameTime: {
            target: 16.67, // 60fps = 16.67ms per frame
            warning: 22.22, // 45fps
            critical: 33.33  // 30fps
        }
    };
    
    // 监控定时器
    private _monitorTimer: number = 0;
    private _updateInterval: number = 1000; // 1秒更新一次
    private _historySize: number = 60; // 保持60秒历史数据
    
    // 事件管理器
    private _eventManager: EventManager = null;
    
    // 内存管理
    private _lastGCTime: number = 0;
    private _gcInterval: number = 30000; // 30秒检查一次GC
    
    public static get instance(): PerformanceManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (PerformanceManager._instance === null) {
            PerformanceManager._instance = this;
            this.initialize();
        }
    }
    
    protected onDestroy(): void {
        if (PerformanceManager._instance === this) {
            PerformanceManager._instance = null;
        }
        this.cleanup();
    }
    
    /**
     * 初始化性能监控
     */
    private initialize(): void {
        try {
            console.log('[PerformanceManager] 初始化性能监控');
            
            this._eventManager = EventManager.instance;
            
            // 启用性能监控
            if (DEBUG_CONFIG.PERFORMANCE_MONITOR) {
                this.startMonitoring();
            }
            
            // 启用Cocos Creator内置profiler
            if (DEBUG_CONFIG.SHOW_FPS) {
                profiler.showStats();
            }
            
            // 注册内存警告监听器
            this.registerMemoryWarningListener();
            
            console.log('[PerformanceManager] 性能监控初始化完成');
            
        } catch (error) {
            console.error('[PerformanceManager] 初始化失败:', error);
        }
    }
    
    /**
     * 开始性能监控
     */
    public startMonitoring(): void {
        if (this._monitorTimer > 0) {
            return; // 已经在监控中
        }
        
        console.log('[PerformanceManager] 开始性能监控');
        
        this._monitorTimer = setInterval(() => {
            this.updatePerformanceData();
            this.checkPerformanceThresholds();
            this.updateStatistics();
        }, this._updateInterval);
        
        // 立即执行一次
        this.updatePerformanceData();
    }
    
    /**
     * 停止性能监控
     */
    public stopMonitoring(): void {
        if (this._monitorTimer > 0) {
            clearInterval(this._monitorTimer);
            this._monitorTimer = 0;
            console.log('[PerformanceManager] 性能监控已停止');
        }
    }
    
    /**
     * 更新性能数据
     */
    private updatePerformanceData(): void {
        try {
            // 获取FPS和帧时间
            this._performanceData.fps = game.frameRate;
            this._performanceData.frameTime = 1000 / this._performanceData.fps;
            
            // 获取内存使用情况
            this._performanceData.memoryUsage = this.getMemoryUsage();
            
            // 获取渲染统计
            const renderStats = this.getRenderStats();
            this._performanceData.renderCalls = renderStats.drawCalls;
            this._performanceData.triangles = renderStats.triangles;
            
            // 发送性能数据更新事件
            this._eventManager?.emit('performance_update', {
                ...this._performanceData,
                timestamp: Date.now()
            });
            
        } catch (error) {
            console.warn('[PerformanceManager] 更新性能数据失败:', error);
        }
    }
    
    /**
     * 获取内存使用情况
     */
    private getMemoryUsage(): number {
        try {
            // 在微信小游戏环境中使用wx.getPerformance
            if (typeof wx !== 'undefined' && wx.getPerformance) {
                const performance = wx.getPerformance();
                return performance.usedJSHeapSize || 0;
            }
            
            // Web环境使用performance.memory
            if (typeof performance !== 'undefined' && (performance as any).memory) {
                return (performance as any).memory.usedJSHeapSize || 0;
            }
            
            return 0;
        } catch (error) {
            console.warn('[PerformanceManager] 获取内存使用失败:', error);
            return 0;
        }
    }
    
    /**
     * 获取渲染统计信息
     */
    private getRenderStats(): { drawCalls: number; triangles: number } {
        try {
            // 这里需要根据Cocos Creator版本获取渲染统计
            // 简化版本，实际需要访问渲染器统计信息
            return {
                drawCalls: 0,
                triangles: 0
            };
        } catch (error) {
            console.warn('[PerformanceManager] 获取渲染统计失败:', error);
            return { drawCalls: 0, triangles: 0 };
        }
    }
    
    /**
     * 检查性能阈值
     */
    private checkPerformanceThresholds(): void {
        const warnings: string[] = [];
        const criticals: string[] = [];
        
        // 检查FPS
        if (this._performanceData.fps < this._thresholds.fps.critical) {
            criticals.push(`FPS严重过低: ${this._performanceData.fps.toFixed(1)}`);
            this._performanceStats.criticalCount++;
        } else if (this._performanceData.fps < this._thresholds.fps.warning) {
            warnings.push(`FPS较低: ${this._performanceData.fps.toFixed(1)}`);
            this._performanceStats.warningCount++;
        }
        
        // 检查内存使用
        const memoryMB = this._performanceData.memoryUsage / (1024 * 1024);
        if (this._performanceData.memoryUsage > this._thresholds.memory.critical) {
            criticals.push(`内存使用严重过高: ${memoryMB.toFixed(1)}MB`);
            this.triggerGarbageCollection();
        } else if (this._performanceData.memoryUsage > this._thresholds.memory.warning) {
            warnings.push(`内存使用较高: ${memoryMB.toFixed(1)}MB`);
        }
        
        // 检查帧时间
        if (this._performanceData.frameTime > this._thresholds.frameTime.critical) {
            criticals.push(`帧时间过长: ${this._performanceData.frameTime.toFixed(1)}ms`);
        } else if (this._performanceData.frameTime > this._thresholds.frameTime.warning) {
            warnings.push(`帧时间较长: ${this._performanceData.frameTime.toFixed(1)}ms`);
        }
        
        // 发送警告和严重警告事件
        if (warnings.length > 0) {
            this._eventManager?.emit('performance_warning', {
                warnings,
                performanceData: this._performanceData
            });
        }
        
        if (criticals.length > 0) {
            this._eventManager?.emit('performance_critical', {
                criticals,
                performanceData: this._performanceData
            });
        }
    }
    
    /**
     * 更新统计信息
     */
    private updateStatistics(): void {
        // 更新FPS统计
        this._performanceStats.fpsHistory.push(this._performanceData.fps);
        if (this._performanceStats.fpsHistory.length > this._historySize) {
            this._performanceStats.fpsHistory.shift();
        }
        
        this._performanceStats.minFPS = Math.min(this._performanceStats.minFPS, this._performanceData.fps);
        this._performanceStats.maxFPS = Math.max(this._performanceStats.maxFPS, this._performanceData.fps);
        this._performanceStats.avgFPS = this._performanceStats.fpsHistory.reduce((a, b) => a + b, 0) / this._performanceStats.fpsHistory.length;
        
        // 更新内存统计
        this._performanceStats.memoryHistory.push(this._performanceData.memoryUsage);
        if (this._performanceStats.memoryHistory.length > this._historySize) {
            this._performanceStats.memoryHistory.shift();
        }
        
        this._performanceStats.memoryPeak = Math.max(this._performanceStats.memoryPeak, this._performanceData.memoryUsage);
    }
    
    /**
     * 触发垃圾回收
     */
    private triggerGarbageCollection(): void {
        const now = Date.now();
        if (now - this._lastGCTime < this._gcInterval) {
            return; // 避免频繁GC
        }
        
        try {
            console.log('[PerformanceManager] 触发垃圾回收');
            
            // 在微信小游戏环境中触发GC
            if (typeof wx !== 'undefined' && wx.triggerGC) {
                wx.triggerGC();
            }
            
            // Web环境中尝试触发GC
            if (typeof window !== 'undefined' && (window as any).gc) {
                (window as any).gc();
            }
            
            this._lastGCTime = now;
            
            this._eventManager?.emit('gc_triggered', {
                reason: 'memory_threshold',
                memoryBefore: this._performanceData.memoryUsage,
                timestamp: now
            });
            
        } catch (error) {
            console.warn('[PerformanceManager] 触发GC失败:', error);
        }
    }
    
    /**
     * 注册内存警告监听器
     */
    private registerMemoryWarningListener(): void {
        if (typeof wx !== 'undefined' && wx.onMemoryWarning) {
            wx.onMemoryWarning(() => {
                console.warn('[PerformanceManager] 收到内存警告');
                
                this._eventManager?.emit('memory_warning_received', {
                    memoryUsage: this._performanceData.memoryUsage,
                    timestamp: Date.now()
                });
                
                // 执行内存清理
                this.performMemoryCleanup();
            });
        }
    }
    
    /**
     * 执行内存清理
     */
    public performMemoryCleanup(): void {
        try {
            console.log('[PerformanceManager] 执行内存清理');
            
            // 清理音频缓存
            this._eventManager?.emit('cleanup_audio_cache');
            
            // 清理纹理缓存
            this._eventManager?.emit('cleanup_texture_cache');
            
            // 触发垃圾回收
            this.triggerGarbageCollection();
            
            this._eventManager?.emit('memory_cleanup_complete', {
                timestamp: Date.now()
            });
            
        } catch (error) {
            console.error('[PerformanceManager] 内存清理失败:', error);
        }
    }
    
    /**
     * 获取性能报告
     */
    public getPerformanceReport(): any {
        return {
            current: { ...this._performanceData },
            statistics: { ...this._performanceStats },
            thresholds: { ...this._thresholds },
            timestamp: Date.now(),
            recommendations: this.getPerformanceRecommendations()
        };
    }
    
    /**
     * 获取性能优化建议
     */
    private getPerformanceRecommendations(): string[] {
        const recommendations: string[] = [];
        
        if (this._performanceStats.avgFPS < this._thresholds.fps.warning) {
            recommendations.push('建议降低画面质量或减少同屏元素');
            recommendations.push('检查是否有性能密集的计算或渲染操作');
        }
        
        if (this._performanceStats.memoryPeak > this._thresholds.memory.warning) {
            recommendations.push('建议清理不必要的缓存数据');
            recommendations.push('检查是否有内存泄漏');
        }
        
        if (this._performanceData.renderCalls > 100) {
            recommendations.push('建议使用批处理减少渲染调用次数');
        }
        
        return recommendations;
    }
    
    /**
     * 获取当前性能状态
     */
    public getPerformanceStatus(): 'excellent' | 'good' | 'warning' | 'critical' {
        if (this._performanceData.fps >= this._thresholds.fps.target && 
            this._performanceData.memoryUsage < this._thresholds.memory.warning) {
            return 'excellent';
        }
        
        if (this._performanceData.fps >= this._thresholds.fps.warning && 
            this._performanceData.memoryUsage < this._thresholds.memory.critical) {
            return 'good';
        }
        
        if (this._performanceData.fps >= this._thresholds.fps.critical && 
            this._performanceData.memoryUsage < this._thresholds.memory.critical) {
            return 'warning';
        }
        
        return 'critical';
    }
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        this.stopMonitoring();
        
        // 清理事件监听器
        if (typeof wx !== 'undefined' && wx.offMemoryWarning) {
            wx.offMemoryWarning();
        }
    }
    
    /**
     * 设置监控间隔
     */
    public setMonitorInterval(interval: number): void {
        this._updateInterval = Math.max(500, interval); // 最小500ms
        
        if (this._monitorTimer > 0) {
            this.stopMonitoring();
            this.startMonitoring();
        }
    }
    
    /**
     * 重置统计数据
     */
    public resetStatistics(): void {
        this._performanceStats = {
            avgFPS: 0,
            minFPS: 60,
            maxFPS: 0,
            fpsHistory: [],
            memoryPeak: 0,
            memoryHistory: [],
            warningCount: 0,
            criticalCount: 0
        };
        
        console.log('[PerformanceManager] 统计数据已重置');
    }

    // ================== 增强性能监控功能 ==================

    /**
     * 启动时间监控
     */
    private _startupMetrics = {
        engineStartTime: 0,
        gameStartTime: 0,
        firstFrameTime: 0,
        sceneLoadTimes: new Map<string, number>(),
        assetLoadTimes: new Map<string, number>()
    };

    /**
     * 网络性能监控
     */
    private _networkMetrics = {
        requestCount: 0,
        totalLatency: 0,
        avgLatency: 0,
        failureCount: 0,
        slowRequestCount: 0, // >1000ms
        timeouts: 0
    };

    /**
     * 音频性能监控
     */
    private _audioMetrics = {
        loadTime: 0,
        decodeTime: 0,
        playbackLatency: 0,
        errorCount: 0,
        cacheHitRate: 0,
        memoryUsage: 0
    };

    /**
     * 设备信息收集
     */
    private _deviceInfo = {
        platform: '',
        model: '',
        brand: '',
        system: '',
        version: '',
        pixelRatio: 1,
        screenWidth: 0,
        screenHeight: 0,
        memorySize: 0,
        storageSize: 0
    };

    /**
     * 性能基准测试
     */
    public async runPerformanceBenchmark(): Promise<any> {
        console.group('🚀 [性能基准测试] 开始执行');
        
        const benchmark = {
            timestamp: new Date().toISOString(),
            deviceInfo: await this.collectDeviceInfo(),
            tests: {},
            summary: {
                overallScore: 0,
                grade: 'Unknown'
            }
        };

        try {
            // 1. CPU性能测试
            benchmark.tests['cpu'] = await this.testCPUPerformance();

            // 2. 内存性能测试
            benchmark.tests['memory'] = await this.testMemoryPerformance();

            // 3. 渲染性能测试
            benchmark.tests['rendering'] = await this.testRenderingPerformance();

            // 4. 音频性能测试
            benchmark.tests['audio'] = await this.testAudioPerformance();

            // 5. 网络性能测试
            benchmark.tests['network'] = await this.testNetworkPerformance();

            // 计算综合评分
            benchmark.summary = this.calculateOverallScore(benchmark.tests);

        } catch (error) {
            console.error('[PerformanceManager] 基准测试失败:', error);
            benchmark.tests['benchmark_error'] = {
                score: 0,
                error: error.message,
                timestamp: Date.now()
            };
        }

        console.group('📊 [基准测试结果]');
        console.table(benchmark.tests);
        console.log(`综合评分: ${benchmark.summary.overallScore}/100 (${benchmark.summary.grade})`);
        console.groupEnd();
        console.groupEnd();

        // 触发基准测试完成事件
        this._eventManager?.emit('performance_benchmark_complete', benchmark);

        return benchmark;
    }

    /**
     * 收集设备信息
     */
    private async collectDeviceInfo(): Promise<any> {
        if (this._deviceInfo.platform) {
            return this._deviceInfo; // 已收集过
        }

        try {
            // 基础平台信息
            this._deviceInfo.platform = sys.platform;
            this._deviceInfo.pixelRatio = sys.pixelRatio;

            // 屏幕信息
            if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
                const systemInfo = wx.getSystemInfoSync();
                this._deviceInfo.model = systemInfo.model;
                this._deviceInfo.brand = systemInfo.brand;
                this._deviceInfo.system = systemInfo.system;
                this._deviceInfo.version = systemInfo.version;
                this._deviceInfo.screenWidth = systemInfo.screenWidth;
                this._deviceInfo.screenHeight = systemInfo.screenHeight;
                this._deviceInfo.memorySize = systemInfo.memorySize || 0;
                this._deviceInfo.storageSize = systemInfo.storageSize || 0;
            } else if (typeof window !== 'undefined') {
                this._deviceInfo.screenWidth = window.screen.width;
                this._deviceInfo.screenHeight = window.screen.height;
                this._deviceInfo.version = navigator.userAgent;
            }

            console.log('[PerformanceManager] 设备信息收集完成:', this._deviceInfo);

        } catch (error) {
            console.warn('[PerformanceManager] 收集设备信息失败:', error);
        }

        return this._deviceInfo;
    }

    /**
     * CPU性能测试
     */
    private async testCPUPerformance(): Promise<any> {
        const test = {
            name: 'CPU性能测试',
            startTime: Date.now(),
            score: 0,
            details: {}
        };

        try {
            // 1. 数学运算测试
            const mathStart = performance.now();
            let result = 0;
            for (let i = 0; i < 1000000; i++) {
                result += Math.sqrt(i) * Math.sin(i);
            }
            const mathTime = performance.now() - mathStart;
            test.details['math_operations'] = {
                time: Math.round(mathTime),
                score: Math.max(0, Math.min(100, 100 - mathTime / 10))
            };

            // 2. 对象创建和销毁测试
            const objectStart = performance.now();
            const objects = [];
            for (let i = 0; i < 100000; i++) {
                objects.push({ id: i, data: Math.random() });
            }
            objects.length = 0; // 清空数组
            const objectTime = performance.now() - objectStart;
            test.details['object_operations'] = {
                time: Math.round(objectTime),
                score: Math.max(0, Math.min(100, 100 - objectTime / 5))
            };

            // 3. 数组操作测试
            const arrayStart = performance.now();
            const arr = new Array(100000).fill(0).map((_, i) => i);
            arr.sort(() => Math.random() - 0.5);
            arr.reverse();
            const arrayTime = performance.now() - arrayStart;
            test.details['array_operations'] = {
                time: Math.round(arrayTime),
                score: Math.max(0, Math.min(100, 100 - arrayTime / 8))
            };

            // 计算平均分
            const scores = Object.values(test.details).map(d => d.score);
            test.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);

        } catch (error) {
            console.error('[PerformanceManager] CPU测试失败:', error);
            test.score = 0;
            test.details['error'] = error.message;
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 内存性能测试
     */
    private async testMemoryPerformance(): Promise<any> {
        const test = {
            name: '内存性能测试',
            startTime: Date.now(),
            score: 0,
            details: {}
        };

        try {
            const initialMemory = this.getMemoryUsage();

            // 1. 内存分配测试
            const allocStart = performance.now();
            const largeArray = new Array(1000000).fill('test');
            const allocTime = performance.now() - allocStart;
            const peakMemory = this.getMemoryUsage();

            test.details['memory_allocation'] = {
                time: Math.round(allocTime),
                memoryUsed: Math.round((peakMemory - initialMemory) / 1024 / 1024 * 100) / 100,
                score: Math.max(0, Math.min(100, 100 - allocTime / 2))
            };

            // 2. 垃圾回收测试
            const gcStart = performance.now();
            largeArray.length = 0; // 清空大数组
            this.triggerGarbageCollection();

            // 等待一小段时间让GC生效
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const gcTime = performance.now() - gcStart;
            const finalMemory = this.getMemoryUsage();

            test.details['garbage_collection'] = {
                time: Math.round(gcTime),
                memoryReclaimed: Math.round((peakMemory - finalMemory) / 1024 / 1024 * 100) / 100,
                score: Math.max(0, Math.min(100, 100 - gcTime / 5))
            };

            // 计算平均分
            const scores = Object.values(test.details).map(d => d.score);
            test.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);

        } catch (error) {
            console.error('[PerformanceManager] 内存测试失败:', error);
            test.score = 0;
            test.details['error'] = error.message;
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 渲染性能测试
     */
    private async testRenderingPerformance(): Promise<any> {
        const test = {
            name: '渲染性能测试',
            startTime: Date.now(),
            score: 0,
            details: {}
        };

        try {
            // 1. FPS稳定性测试
            const fpsReadings = [];
            const fpsTestDuration = 3000; // 3秒
            const fpsStartTime = Date.now();

            while (Date.now() - fpsStartTime < fpsTestDuration) {
                fpsReadings.push(game.frameRate);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            const avgFps = fpsReadings.reduce((a, b) => a + b, 0) / fpsReadings.length;
            const fpsVariance = Math.sqrt(fpsReadings.reduce((sum, fps) => sum + Math.pow(fps - avgFps, 2), 0) / fpsReadings.length);

            test.details['fps_stability'] = {
                avgFps: Math.round(avgFps * 10) / 10,
                variance: Math.round(fpsVariance * 10) / 10,
                stability: Math.round((1 - fpsVariance / avgFps) * 100),
                score: Math.max(0, Math.min(100, avgFps * 1.67 - fpsVariance * 5)) // 60fps = 100分
            };

            // 2. 帧时间一致性测试
            const frameTimeStart = performance.now();
            let frameCount = 0;
            const frameTimes = [];

            const frameTimeTest = () => {
                const now = performance.now();
                if (frameCount > 0) {
                    frameTimes.push(now - frameTimeStart - frameCount * 16.67); // 理想帧时间偏差
                }
                frameCount++;

                if (frameCount < 180) { // 测试3秒 (60fps * 3)
                    requestAnimationFrame(frameTimeTest);
                }
            };

            await new Promise(resolve => {
                frameTimeTest();
                setTimeout(resolve, 3000);
            });

            const avgFrameTimeVariance = frameTimes.reduce((sum, time) => sum + Math.abs(time), 0) / frameTimes.length;

            test.details['frame_consistency'] = {
                avgVariance: Math.round(avgFrameTimeVariance * 10) / 10,
                score: Math.max(0, Math.min(100, 100 - avgFrameTimeVariance * 2))
            };

            // 计算平均分
            const scores = Object.values(test.details).map(d => d.score);
            test.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);

        } catch (error) {
            console.error('[PerformanceManager] 渲染测试失败:', error);
            test.score = 0;
            test.details['error'] = error.message;
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 音频性能测试
     */
    private async testAudioPerformance(): Promise<any> {
        const test = {
            name: '音频性能测试',
            startTime: Date.now(),
            score: 0,
            details: {}
        };

        try {
            // 1. 音频解码性能测试
            const decodeStart = performance.now();
            
            // 模拟音频解码测试（实际需要真实音频文件）
            const mockAudioData = new ArrayBuffer(1024 * 1024); // 1MB mock数据
            const decodeTime = performance.now() - decodeStart;

            test.details['audio_decode'] = {
                time: Math.round(decodeTime),
                score: Math.max(0, Math.min(100, 100 - decodeTime / 10))
            };

            // 2. 音频缓存性能测试
            const cacheStart = performance.now();
            const audioCache = new Map();
            
            // 模拟缓存操作
            for (let i = 0; i < 100; i++) {
                audioCache.set(`audio_${i}`, { data: mockAudioData, loaded: true });
            }
            
            const cacheTime = performance.now() - cacheStart;

            test.details['audio_cache'] = {
                time: Math.round(cacheTime),
                cacheSize: audioCache.size,
                score: Math.max(0, Math.min(100, 100 - cacheTime / 5))
            };

            // 3. 音频上下文测试
            if (typeof AudioContext !== 'undefined' || typeof (window as any).webkitAudioContext !== 'undefined') {
                const AudioContextClass = AudioContext || (window as any).webkitAudioContext;
                const contextStart = performance.now();
                const audioContext = new AudioContextClass();
                const contextTime = performance.now() - contextStart;

                test.details['audio_context'] = {
                    time: Math.round(contextTime),
                    score: Math.max(0, Math.min(100, 100 - contextTime / 20))
                };

                // 清理音频上下文
                if (audioContext.close) {
                    audioContext.close();
                }
            } else {
                test.details['audio_context'] = {
                    score: 50,
                    message: '音频上下文不可用'
                };
            }

            // 计算平均分
            const scores = Object.values(test.details).map(d => d.score);
            test.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);

        } catch (error) {
            console.error('[PerformanceManager] 音频测试失败:', error);
            test.score = 0;
            test.details['error'] = error.message;
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 网络性能测试
     */
    private async testNetworkPerformance(): Promise<any> {
        const test = {
            name: '网络性能测试',
            startTime: Date.now(),
            score: 0,
            details: {}
        };

        try {
            // 1. 网络延迟测试
            const pingStart = Date.now();
            const networkManager = (await import('./NetworkManager')).NetworkManager.getInstance();
            
            try {
                await networkManager.pingServer();
                const pingTime = Date.now() - pingStart;
                
                test.details['network_latency'] = {
                    latency: pingTime,
                    score: Math.max(0, Math.min(100, 100 - pingTime / 10))
                };
            } catch (error) {
                test.details['network_latency'] = {
                    latency: -1,
                    score: 0,
                    error: error.message
                };
            }

            // 2. 网络稳定性测试
            const stabilityPromises = [];
            for (let i = 0; i < 5; i++) {
                stabilityPromises.push(
                    networkManager.pingServer().then(() => true).catch(() => false)
                );
            }

            const stabilityResults = await Promise.all(stabilityPromises);
            const successRate = stabilityResults.filter(success => success).length / stabilityResults.length;

            test.details['network_stability'] = {
                successRate: Math.round(successRate * 100),
                score: successRate * 100
            };

            // 3. 网络类型检测
            const networkStatus = await networkManager.checkNetworkStatus();
            test.details['network_type'] = {
                type: networkStatus.networkType || 'unknown',
                isOnline: networkStatus.isOnline,
                score: networkStatus.isOnline ? 100 : 0
            };

            // 计算平均分
            const scores = Object.values(test.details).map(d => d.score);
            test.score = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);

        } catch (error) {
            console.error('[PerformanceManager] 网络测试失败:', error);
            test.score = 0;
            test.details['error'] = error.message;
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 计算综合评分
     */
    private calculateOverallScore(tests: any): any {
        const weights = {
            cpu: 0.25,
            memory: 0.20,
            rendering: 0.30,
            audio: 0.15,
            network: 0.10
        };

        let totalScore = 0;
        let totalWeight = 0;

        for (const [testName, testResult] of Object.entries(tests)) {
            if (testResult && typeof testResult.score === 'number') {
                const weight = weights[testName] || 0.1;
                totalScore += testResult.score * weight;
                totalWeight += weight;
            }
        }

        const overallScore = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
        
        let grade = 'F';
        if (overallScore >= 90) grade = 'A+';
        else if (overallScore >= 80) grade = 'A';
        else if (overallScore >= 70) grade = 'B';
        else if (overallScore >= 60) grade = 'C';
        else if (overallScore >= 50) grade = 'D';

        return {
            overallScore,
            grade,
            breakdown: tests
        };
    }

    /**
     * 导出完整性能报告
     */
    public exportFullPerformanceReport(): any {
        return {
            timestamp: new Date().toISOString(),
            deviceInfo: this._deviceInfo,
            currentPerformance: this._performanceData,
            statistics: this._performanceStats,
            networkMetrics: this._networkMetrics,
            audioMetrics: this._audioMetrics,
            startupMetrics: this._startupMetrics,
            thresholds: this._thresholds,
            recommendations: this.getPerformanceRecommendations(),
            status: this.getPerformanceStatus()
        };
    }

    /**
     * 记录启动时间指标
     */
    public recordStartupMetric(metric: string, time: number): void {
        switch (metric) {
            case 'engine_start':
                this._startupMetrics.engineStartTime = time;
                break;
            case 'game_start':
                this._startupMetrics.gameStartTime = time;
                break;
            case 'first_frame':
                this._startupMetrics.firstFrameTime = time;
                break;
        }

        this._eventManager?.emit('startup_metric_recorded', {
            metric,
            time,
            timestamp: Date.now()
        });
    }

    /**
     * 记录场景加载时间
     */
    public recordSceneLoadTime(sceneName: string, loadTime: number): void {
        this._startupMetrics.sceneLoadTimes.set(sceneName, loadTime);
        
        this._eventManager?.emit('scene_load_time_recorded', {
            sceneName,
            loadTime,
            timestamp: Date.now()
        });
    }

    /**
     * 记录资源加载时间
     */
    public recordAssetLoadTime(assetName: string, loadTime: number): void {
        this._startupMetrics.assetLoadTimes.set(assetName, loadTime);
        
        this._eventManager?.emit('asset_load_time_recorded', {
            assetName,
            loadTime,
            timestamp: Date.now()
        });
    }

    /**
     * 更新网络性能指标
     */
    public updateNetworkMetrics(latency: number, success: boolean): void {
        this._networkMetrics.requestCount++;
        
        if (success) {
            this._networkMetrics.totalLatency += latency;
            this._networkMetrics.avgLatency = this._networkMetrics.totalLatency / this._networkMetrics.requestCount;
            
            if (latency > 1000) {
                this._networkMetrics.slowRequestCount++;
            }
        } else {
            this._networkMetrics.failureCount++;
            
            if (latency === -1) { // 超时
                this._networkMetrics.timeouts++;
            }
        }
    }

    /**
     * 更新音频性能指标
     */
    public updateAudioMetrics(metric: string, value: number): void {
        switch (metric) {
            case 'load_time':
                this._audioMetrics.loadTime = value;
                break;
            case 'decode_time':
                this._audioMetrics.decodeTime = value;
                break;
            case 'playback_latency':
                this._audioMetrics.playbackLatency = value;
                break;
            case 'error_count':
                this._audioMetrics.errorCount++;
                break;
            case 'cache_hit_rate':
                this._audioMetrics.cacheHitRate = value;
                break;
            case 'memory_usage':
                this._audioMetrics.memoryUsage = value;
                break;
        }
    }
}