import { _decorator, Component, EventTarget } from 'cc';

const { ccclass } = _decorator;

/**
 * 事件管理器
 * 负责全局事件的注册、发送和监听
 */
@ccclass('EventManager')
export class EventManager extends Component {
    private static _instance: EventManager = null;
    private _eventTarget: EventTarget = new EventTarget();
    
    public static get instance(): EventManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (EventManager._instance === null) {
            EventManager._instance = this;
        }
    }
    
    protected onDestroy(): void {
        if (EventManager._instance === this) {
            EventManager._instance = null;
        }
        this._eventTarget.removeAll();
    }
    
    /**
     * 初始化事件管理器
     */
    public async initialize(): Promise<void> {
        console.log('[EventManager] 事件管理器初始化完成');
    }
    
    /**
     * 注册事件监听器
     */
    public on(type: string, callback: Function, target?: any): void {
        this._eventTarget.on(type, callback, target);
    }
    
    /**
     * 注册一次性事件监听器
     */
    public once(type: string, callback: Function, target?: any): void {
        this._eventTarget.once(type, callback, target);
    }
    
    /**
     * 移除事件监听器
     */
    public off(type: string, callback?: Function, target?: any): void {
        this._eventTarget.off(type, callback, target);
    }
    
    /**
     * 发送事件
     */
    public emit(type: string, data?: any): void {
        this._eventTarget.emit(type, data);
    }
    
    /**
     * 移除目标的所有监听器
     */
    public targetOff(target: any): void {
        this._eventTarget.targetOff(target);
    }
}