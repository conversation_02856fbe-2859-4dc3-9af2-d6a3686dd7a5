import { _decorator, Component, AudioClip, AudioSource, resources } from 'cc';
import { AudioState, AUDIO_CONFIG, ERROR_CODES } from '../constants/GameConstants';
import { IQuestionData, IAudioCacheData } from '../data/GameData';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { EventManager } from './EventManager';
import { StorageManager } from '../utils/StorageManager';
import { BaseComponent } from '../utils/BaseComponent';
import { SmartAudioPreloader } from '../utils/SmartAudioPreloader';

const { ccclass, property } = _decorator;

/**
 * 音频管理器
 * 负责音频播放、缓存、预加载等功能
 */
@ccclass('AudioManager')
export class AudioManager extends BaseComponent {
    private static _instance: AudioManager = null;
    
    @property(AudioSource)
    public audioSource: AudioSource = null;
    
    // 音频缓存映射 <url, AudioClip>
    private _audioCache: Map<string, AudioClip> = new Map();
    
    // 音频状态映射 <url, AudioState>
    private _audioStates: Map<string, AudioState> = new Map();
    
    // 音频播放次数统计 <questionId, count>
    private _playCountMap: Map<string, number> = new Map();

    // 智能预加载器
    private _smartPreloader: SmartAudioPreloader;

    // 当前播放的音频信息
    private _currentPlayingInfo: {
        questionId: string;
        url: string;
        startTime: number;
        duration: number;
    } | null = null;
    
    // 预加载队列
    private _preloadQueue: string[] = [];
    private _isPreloading: boolean = false;
    
    // 管理器注册中心和错误处理系统
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;

    // 存储管理器
    private _storageManager: StorageManager = null;
    
    public static get instance(): AudioManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (AudioManager._instance === null) {
            AudioManager._instance = this;
        }
    }
    
    protected onDestroy(): void {
        if (AudioManager._instance === this) {
            AudioManager._instance = null;
        }
        this.cleanup();
    }
    
    /**
     * 初始化音频管理器
     */
    public async initialize(): Promise<void> {
        try {
            console.log('[AudioManager] 初始化音频管理器');

            // 获取核心系统引用
            this._managerRegistry = ManagerRegistry.getInstance();
            this._errorHandlingSystem = ErrorHandlingSystem.getInstance();

            // 初始化存储管理器
            this._storageManager = new StorageManager();

            // 初始化智能预加载器
            this._smartPreloader = SmartAudioPreloader.getInstance();

            // 创建音频源组件
            if (!this.audioSource) {
                this.audioSource = this.node.addComponent(AudioSource);
            }

            // 设置音频源属性
            this.audioSource.volume = AUDIO_CONFIG.PLAYBACK.VOLUME;
            this.audioSource.loop = false;

            // 加载缓存的音频信息
            await this.loadCacheInfo();

            // 注册事件监听器
            this.registerEventListeners();

            // 启动智能预加载
            this.startSmartPreloading();

            console.log('[AudioManager] 音频管理器初始化完成');
        } catch (error) {
            console.error('[AudioManager] 初始化失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'AudioManager.initialize' });
            throw error;
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        if (!this._eventManager) return;

        // 监听音频播放完成事件
        this.audioSource.node.on(AudioSource.EventType.ENDED, this.onAudioEnded, this);

        // 使用BaseComponent的安全事件监听器注册
        this.addEventListener('app_pause', this.onAppPause);
        this.addEventListener('app_resume', this.onAppResume);
        this.addEventListener('cleanup_audio_cache', this.clearCache);

        // 监听智能预加载请求
        this.addEventListener('preload_audio_request', this.onPreloadRequest);

        // 监听游戏开始事件以启动智能预加载
        this.addEventListener('game_session_started', this.onGameSessionStarted);

        // 监听游戏状态变化
        this.addEventListener('game_state_changed', this.onGameStateChanged);
    }

    /**
     * 启动智能预加载
     */
    private startSmartPreloading(): void {
        if (!this._smartPreloader) return;

        // 配置智能预加载策略
        this._smartPreloader.configure({
            maxConcurrentLoads: AUDIO_CONFIG.PRELOAD.CONCURRENT_LIMIT,
            batchSize: AUDIO_CONFIG.PRELOAD.BATCH_SIZE,
            cacheSize: AUDIO_CONFIG.CACHE.MAX_SIZE,
            priorityWeight: 0.7, // 优先级权重
            frequencyWeight: 0.3  // 频率权重
        });

        console.log('[AudioManager] 智能预加载已启动');
    }

    /**
     * 游戏状态变化处理
     */
    private onGameStateChanged(data: { newState: string; oldState: string }): void {
        switch (data.newState) {
            case 'PLAYING':
                // 游戏开始时，预加载下一批音频
                this.preloadNextBatch();
                break;
            case 'PAUSED':
                // 游戏暂停时，暂停预加载
                this._smartPreloader?.pausePreloading();
                break;
            case 'MENU':
                // 返回菜单时，清理部分缓存
                this.cleanupUnusedCache();
                break;
        }
    }

    /**
     * 预加载下一批音频
     */
    private async preloadNextBatch(): Promise<void> {
        if (!this._smartPreloader) return;

        try {
            // 获取推荐的预加载音频列表
            const recommendations = await this._smartPreloader.getPreloadRecommendations();

            // 批量预加载
            for (const audioUrl of recommendations) {
                if (!this._audioCache.has(audioUrl)) {
                    this.preloadAudio(audioUrl);
                }
            }

            console.log(`[AudioManager] 预加载 ${recommendations.length} 个音频文件`);
        } catch (error) {
            console.error('[AudioManager] 预加载失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'AudioManager.preloadNextBatch' });
        }
    }

    /**
     * 清理未使用的缓存
     */
    private cleanupUnusedCache(): void {
        const now = Date.now();
        const expireTime = AUDIO_CONFIG.CACHE.EXPIRE_TIME;
        let cleanedCount = 0;

        // 清理过期的缓存项
        for (const [url, cacheData] of this._audioCache.entries()) {
            if (now - (cacheData as any).lastAccessed > expireTime) {
                this._audioCache.delete(url);
                this._audioStates.delete(url);
                cleanedCount++;
            }
        }

        console.log(`[AudioManager] 清理了 ${cleanedCount} 个过期缓存项`);

        // 如果缓存仍然过大，清理最少使用的项
        if (this._audioCache.size > AUDIO_CONFIG.CACHE.MAX_ITEMS) {
            this.cleanupLRUCache();
        }
    }

    /**
     * LRU缓存清理
     */
    private cleanupLRUCache(): void {
        const cacheEntries = Array.from(this._audioCache.entries());

        // 按最后访问时间排序
        cacheEntries.sort((a, b) => {
            const aTime = (a[1] as any).lastAccessed || 0;
            const bTime = (b[1] as any).lastAccessed || 0;
            return aTime - bTime;
        });

        // 删除最少使用的项，直到缓存大小合适
        const targetSize = Math.floor(AUDIO_CONFIG.CACHE.MAX_ITEMS * 0.8);
        const toDelete = cacheEntries.length - targetSize;

        for (let i = 0; i < toDelete; i++) {
            const [url] = cacheEntries[i];
            this._audioCache.delete(url);
            this._audioStates.delete(url);
        }

        console.log(`[AudioManager] LRU清理了 ${toDelete} 个缓存项`);
    }
    
    /**
     * 播放题目音频
     */
    public async playQuestionAudio(question: IQuestionData): Promise<void> {
        try {
            console.log(`[AudioManager] 播放题目音频: ${question.id}`);
            
            // 检查播放次数限制
            const playCount = this.getPlayCount(question.id);
            if (playCount >= AUDIO_CONFIG.PLAYBACK.MAX_RETRY) {
                console.warn(`[AudioManager] 音频播放次数已达上限: ${question.id}`);
                return;
            }
            
            // 停止当前播放
            this.stopCurrent();
            
            // 获取音频剪辑
            const audioClip = await this.getAudioClip(question.audioUrl);
            if (!audioClip) {
                throw new Error(`无法加载音频: ${question.audioUrl}`);
            }
            
            // 设置音频源
            this.audioSource.clip = audioClip;
            
            // 更新播放信息
            this._currentPlayingInfo = {
                questionId: question.id,
                url: question.audioUrl,
                startTime: Date.now(),
                duration: audioClip.duration
            };
            
            // 更新状态
            this.setAudioState(question.audioUrl, AudioState.PLAYING);
            
            // 增加播放次数
            this.incrementPlayCount(question.id);
            
            // 开始播放
            this.audioSource.play();
            
            // 发送播放开始事件
            this._eventManager?.emit('audio_play_started', {
                questionId: question.id,
                audioUrl: question.audioUrl,
                duration: audioClip.duration,
                playCount: this.getPlayCount(question.id)
            });
            
            console.log(`[AudioManager] 音频播放开始: ${question.id}, 时长: ${audioClip.duration}s`);
            
        } catch (error) {
            console.error('[AudioManager] 播放音频失败:', error);
            this.setAudioState(question.audioUrl, AudioState.ERROR);
            
            this._eventManager?.emit('audio_play_error', {
                questionId: question.id,
                audioUrl: question.audioUrl,
                error: error,
                errorCode: ERROR_CODES.AUDIO_PLAY_FAILED
            });
            
            throw error;
        }
    }
    
    /**
     * 暂停当前播放
     */
    public pauseCurrent(): void {
        if (this.audioSource.isPlaying) {
            this.audioSource.pause();
            
            if (this._currentPlayingInfo) {
                this.setAudioState(this._currentPlayingInfo.url, AudioState.PAUSED);
                
                this._eventManager?.emit('audio_paused', {
                    questionId: this._currentPlayingInfo.questionId,
                    audioUrl: this._currentPlayingInfo.url,
                    currentTime: this.audioSource.currentTime
                });
            }
            
            console.log('[AudioManager] 音频已暂停');
        }
    }
    
    /**
     * 恢复播放
     */
    public resumeCurrent(): void {
        if (!this.audioSource.isPlaying && this.audioSource.clip) {
            this.audioSource.play();
            
            if (this._currentPlayingInfo) {
                this.setAudioState(this._currentPlayingInfo.url, AudioState.PLAYING);
                
                this._eventManager?.emit('audio_resumed', {
                    questionId: this._currentPlayingInfo.questionId,
                    audioUrl: this._currentPlayingInfo.url,
                    currentTime: this.audioSource.currentTime
                });
            }
            
            console.log('[AudioManager] 音频已恢复');
        }
    }
    
    /**
     * 停止当前播放
     */
    public stopCurrent(): void {
        if (this.audioSource.isPlaying) {
            this.audioSource.stop();
        }
        
        if (this._currentPlayingInfo) {
            this.setAudioState(this._currentPlayingInfo.url, AudioState.STOPPED);
            
            this._eventManager?.emit('audio_stopped', {
                questionId: this._currentPlayingInfo.questionId,
                audioUrl: this._currentPlayingInfo.url
            });
            
            this._currentPlayingInfo = null;
        }
        
        console.log('[AudioManager] 音频已停止');
    }
    
    /**
     * 停止所有音频
     */
    public stopAll(): void {
        this.stopCurrent();
        this._preloadQueue = [];
        this._isPreloading = false;
    }
    
    /**
     * 暂停所有音频
     */
    public pauseAll(): void {
        this.pauseCurrent();
    }
    
    /**
     * 智能预加载题目音频
     */
    public async smartPreloadQuestionAudios(questions: IQuestionData[], currentIndex: number = 0): Promise<void> {
        try {
            console.log(`[AudioManager] 开始智能预加载 ${questions.length} 个音频，当前索引: ${currentIndex}`);

            // 使用智能预加载器
            await this._smartPreloader.startSmartPreload(questions, currentIndex);

            console.log('[AudioManager] 智能预加载完成');

        } catch (error) {
            console.error('[AudioManager] 智能预加载失败:', error);
            throw error;
        }
    }

    /**
     * 传统预加载题目音频（保持向后兼容）
     */
    public async preloadQuestionAudios(questions: IQuestionData[]): Promise<void> {
        try {
            console.log(`[AudioManager] 开始传统预加载 ${questions.length} 个音频`);

            const urls = questions.map(q => q.audioUrl).filter(url => !this._audioCache.has(url));

            if (urls.length === 0) {
                console.log('[AudioManager] 所有音频已缓存，无需预加载');
                return;
            }

            // 分批预加载
            const batchSize = AUDIO_CONFIG.PRELOAD.BATCH_SIZE;
            for (let i = 0; i < urls.length; i += batchSize) {
                const batch = urls.slice(i, i + batchSize);
                await this.preloadBatch(batch);

                // 发送预加载进度事件
                this._eventManager?.emit('audio_preload_progress', {
                    loaded: Math.min(i + batchSize, urls.length),
                    total: urls.length,
                    progress: Math.min((i + batchSize) / urls.length, 1.0)
                });
            }

            console.log('[AudioManager] 传统预加载完成');

        } catch (error) {
            console.error('[AudioManager] 传统预加载失败:', error);
            throw error;
        }
    }
    
    /**
     * 批量预加载音频
     */
    private async preloadBatch(urls: string[]): Promise<void> {
        const promises = urls.map(url => this.loadAudioClip(url));
        const results = await Promise.allSettled(promises);
        
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                console.warn(`[AudioManager] 预加载音频失败: ${urls[index]}`, result.reason);
            }
        });
    }
    
    /**
     * 获取音频剪辑
     */
    private async getAudioClip(url: string): Promise<AudioClip | null> {
        // 检查缓存
        if (this._audioCache.has(url)) {
            console.log(`[AudioManager] 从缓存获取音频: ${url}`);
            return this._audioCache.get(url)!;
        }
        
        // 加载音频
        return await this.loadAudioClip(url);
    }
    
    /**
     * 加载音频剪辑
     */
    private async loadAudioClip(url: string): Promise<AudioClip | null> {
        try {
            this.setAudioState(url, AudioState.LOADING);
            
            console.log(`[AudioManager] 开始加载音频: ${url}`);
            
            // 创建加载Promise
            const loadPromise = new Promise<AudioClip>((resolve, reject) => {
                // 模拟从网络或本地加载音频
                // 实际实现中需要根据URL类型选择不同的加载方式
                if (url.startsWith('http')) {
                    // 网络音频加载
                    this.loadNetworkAudio(url).then(resolve).catch(reject);
                } else {
                    // 本地资源加载
                    resources.load(url, AudioClip, (err, clip) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve(clip);
                        }
                    });
                }
            });
            
            // 设置超时
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`音频加载超时: ${url}`));
                }, AUDIO_CONFIG.PLAYBACK.TIMEOUT);
            });
            
            // 等待加载完成或超时
            const audioClip = await Promise.race([loadPromise, timeoutPromise]);
            
            // 缓存音频
            this._audioCache.set(url, audioClip);
            this.setAudioState(url, AudioState.READY);
            
            // 保存缓存信息
            await this.saveCacheInfo(url, audioClip);
            
            console.log(`[AudioManager] 音频加载完成: ${url}, 时长: ${audioClip.duration}s`);
            
            return audioClip;
            
        } catch (error) {
            console.error(`[AudioManager] 加载音频失败: ${url}`, error);
            this.setAudioState(url, AudioState.ERROR);
            
            this._eventManager?.emit('audio_load_error', {
                audioUrl: url,
                error: error,
                errorCode: ERROR_CODES.AUDIO_LOAD_FAILED
            });
            
            return null;
        }
    }
    
    /**
     * 加载网络音频（微信小游戏环境）
     */
    private async loadNetworkAudio(url: string): Promise<AudioClip> {
        return new Promise((resolve, reject) => {
            // 在微信小游戏环境中使用wx.downloadFile下载音频
            if (typeof wx !== 'undefined' && wx.downloadFile) {
                wx.downloadFile({
                    url: url,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 使用临时文件路径加载音频
                            resources.load(res.tempFilePath, AudioClip, (err, clip) => {
                                if (err) {
                                    reject(err);
                                } else {
                                    resolve(clip);
                                }
                            });
                        } else {
                            reject(new Error(`下载失败, 状态码: ${res.statusCode}`));
                        }
                    },
                    fail: (error) => {
                        reject(error);
                    }
                });
            } else {
                // Web环境或其他环境的处理
                reject(new Error('不支持的环境或网络音频加载'));
            }
        });
    }
    
    /**
     * 设置音频状态
     */
    private setAudioState(url: string, state: AudioState): void {
        this._audioStates.set(url, state);
    }
    
    /**
     * 获取音频状态
     */
    public getAudioState(url: string): AudioState {
        return this._audioStates.get(url) || AudioState.IDLE;
    }
    
    /**
     * 增加播放次数
     */
    private incrementPlayCount(questionId: string): void {
        const currentCount = this._playCountMap.get(questionId) || 0;
        this._playCountMap.set(questionId, currentCount + 1);
    }
    
    /**
     * 获取播放次数
     */
    public getPlayCount(questionId: string): number {
        return this._playCountMap.get(questionId) || 0;
    }
    
    /**
     * 重置播放次数
     */
    public resetPlayCount(questionId: string): void {
        this._playCountMap.delete(questionId);
    }
    
    /**
     * 清理所有播放次数
     */
    public clearAllPlayCounts(): void {
        this._playCountMap.clear();
    }
    
    /**
     * 设置音量
     */
    public setVolume(volume: number): void {
        this.audioSource.volume = Math.max(0, Math.min(1, volume));
        console.log(`[AudioManager] 音量设置为: ${this.audioSource.volume}`);
    }
    
    /**
     * 获取当前音量
     */
    public getVolume(): number {
        return this.audioSource.volume;
    }
    
    /**
     * 是否正在播放
     */
    public isPlaying(): boolean {
        return this.audioSource.isPlaying;
    }
    
    /**
     * 获取当前播放信息
     */
    public getCurrentPlayingInfo(): any {
        return this._currentPlayingInfo;
    }
    
    /**
     * 保存缓存信息
     */
    private async saveCacheInfo(url: string, audioClip: AudioClip): Promise<void> {
        try {
            const cacheData: IAudioCacheData = {
                url: url,
                localPath: '', // 在实际实现中需要保存本地路径
                size: 0, // 需要计算文件大小
                duration: audioClip.duration,
                format: 'mp3', // 需要检测格式
                cachedAt: Date.now(),
                lastAccessed: Date.now(),
                accessCount: 1,
                isPreloaded: true
            };
            
            await this._storageManager.setItem(`audio_cache_${url}`, cacheData);
        } catch (error) {
            console.warn('[AudioManager] 保存缓存信息失败:', error);
        }
    }
    
    /**
     * 加载缓存信息
     */
    private async loadCacheInfo(): Promise<void> {
        try {
            // 这里可以加载之前缓存的音频信息
            // 在实际实现中需要恢复缓存的音频数据
            console.log('[AudioManager] 缓存信息加载完成');
        } catch (error) {
            console.warn('[AudioManager] 加载缓存信息失败:', error);
        }
    }
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        this.stopAll();
        this._audioCache.clear();
        this._audioStates.clear();
        this._playCountMap.clear();
        this._currentPlayingInfo = null;
    }

    /**
     * 重写BaseComponent的清理方法
     */
    protected onCleanup(): void {
        // 清理音频源事件监听器
        if (this.audioSource && this.audioSource.node) {
            this.audioSource.node.off(AudioSource.EventType.ENDED, this.onAudioEnded, this);
        }

        // 执行基础清理
        this.cleanup();

        console.log('[AudioManager] 内存清理完成');
    }
    
    // ========== 事件处理器 ==========
    
    /**
     * 音频播放结束事件处理
     */
    private onAudioEnded(): void {
        console.log('[AudioManager] 音频播放结束');
        
        if (this._currentPlayingInfo) {
            const playInfo = this._currentPlayingInfo;
            this.setAudioState(playInfo.url, AudioState.STOPPED);
            
            this._eventManager?.emit('audio_play_complete', {
                questionId: playInfo.questionId,
                audioUrl: playInfo.url,
                duration: playInfo.duration,
                playTime: Date.now() - playInfo.startTime
            });
            
            this._currentPlayingInfo = null;
        }
    }
    
    /**
     * 应用暂停事件处理
     */
    private onAppPause(): void {
        console.log('[AudioManager] 应用暂停，暂停音频播放');
        this.pauseCurrent();
    }
    
    /**
     * 应用恢复事件处理
     */
    private onAppResume(): void {
        console.log('[AudioManager] 应用恢复');
        // 不自动恢复播放，让用户手动控制
    }
    
    // ========== 缓存管理 ==========
    
    /**
     * 获取缓存大小
     */
    public getCacheSize(): number {
        // 返回当前缓存的音频数量
        return this._audioCache.size;
    }
    
    /**
     * 清理缓存
     */
    public clearCache(): void {
        console.log('[AudioManager] 清理音频缓存');
        this._audioCache.clear();
        this._audioStates.clear();
    }
    
    /**
     * 删除特定缓存
     */
    public removeFromCache(url: string): void {
        this._audioCache.delete(url);
        this._audioStates.delete(url);
        console.log(`[AudioManager] 从缓存中删除音频: ${url}`);
    }

    /**
     * 处理预加载请求
     */
    private async onPreloadRequest(data: { url: string }): Promise<void> {
        try {
            const audioClip = await this.loadAudioClip(data.url);

            if (audioClip) {
                this._eventManager?.emit('audio_preload_success', { url: data.url });
            } else {
                this._eventManager?.emit('audio_preload_error', {
                    url: data.url,
                    error: '加载失败'
                });
            }
        } catch (error) {
            this._eventManager?.emit('audio_preload_error', {
                url: data.url,
                error: error.message
            });
        }
    }

    /**
     * 处理游戏会话开始事件
     */
    private async onGameSessionStarted(data: any): Promise<void> {
        try {
            if (data.questions && Array.isArray(data.questions)) {
                console.log('[AudioManager] 游戏会话开始，启动智能预加载');
                await this.smartPreloadQuestionAudios(data.questions, 0);
            }
        } catch (error) {
            console.error('[AudioManager] 游戏会话开始时预加载失败:', error);
        }
    }

    /**
     * 获取智能预加载统计信息
     */
    public getSmartPreloadStats(): any {
        return this._smartPreloader?.getPreloadStats() || {};
    }

    /**
     * 清理音频缓存（公共方法）
     */
    public clearAudioCache(): void {
        console.log('[AudioManager] 清理音频缓存');
        this._audioCache.clear();
        this._audioStates.clear();
        this._playCountMap.clear();

        // 清理智能预加载器
        this._smartPreloader?.cleanup();

        // 发送缓存清理事件
        this._eventManager?.emit('audio_cache_cleared');
    }
}