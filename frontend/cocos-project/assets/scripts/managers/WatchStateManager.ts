/**
 * 围观功能状态管理器
 * 
 * 负责管理围观功能的全局状态，包括房间状态、弹幕消息、预测游戏等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, EventTarget } from 'cc';
import { 
    WatchState, 
    WatchAction, 
    WatchActionType,
    ConnectionStatus,
    NetworkQuality,
    BarrageFilterConfig,
    PredictionStats,
    WatchError,
    WatchErrorType
} from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 状态变化事件名称 */
export enum WatchStateEvent {
    STATE_CHANGED = 'state_changed',
    CONNECTION_STATUS_CHANGED = 'connection_status_changed',
    ROOM_JOINED = 'room_joined',
    ROOM_LEFT = 'room_left',
    BARRAGE_RECEIVED = 'barrage_received',
    PREDICTION_STARTED = 'prediction_started',
    PREDICTION_ENDED = 'prediction_ended',
    ERROR_OCCURRED = 'error_occurred'
}

@ccclass('WatchStateManager')
export class WatchStateManager extends Component {
    private static _instance: WatchStateManager = null;
    
    /** 事件系统 */
    private _eventTarget: EventTarget = new EventTarget();
    
    /** 当前状态 */
    private _state: WatchState = {
        connectionStatus: ConnectionStatus.DISCONNECTED,
        networkQuality: NetworkQuality.DISCONNECTED,
        currentRoom: null,
        roomList: [],
        currentUser: null,
        viewers: [],
        barrageMessages: [],
        barrageFilter: this.getDefaultBarrageFilter(),
        currentPrediction: null,
        userPredictions: [],
        predictionStats: this.getDefaultPredictionStats(),
        gameState: null,
        isBarrageInputVisible: false,
        isPredictionPanelVisible: false,
        isViewerListVisible: false
    };
    
    /** 状态历史记录（用于调试） */
    private _stateHistory: { action: WatchAction; state: WatchState; timestamp: number }[] = [];
    private readonly MAX_HISTORY_SIZE = 100;

    // ==================== 单例模式 ====================
    
    public static getInstance(): WatchStateManager {
        if (!WatchStateManager._instance) {
            const node = new Node('WatchStateManager');
            WatchStateManager._instance = node.addComponent(WatchStateManager);
            // 确保不被销毁
            node.parent = cc.director.getScene();
            cc.game.addPersistRootNode(node);
        }
        return WatchStateManager._instance;
    }

    protected onLoad() {
        if (WatchStateManager._instance && WatchStateManager._instance !== this) {
            this.node.destroy();
            return;
        }
        WatchStateManager._instance = this;
    }

    // ==================== 状态访问 ====================
    
    /** 获取当前状态 */
    public getState(): Readonly<WatchState> {
        return { ...this._state };
    }
    
    /** 获取状态的特定部分 */
    public getConnectionStatus(): ConnectionStatus {
        return this._state.connectionStatus;
    }
    
    public getCurrentRoom() {
        return this._state.currentRoom;
    }
    
    public getBarrageMessages() {
        return [...this._state.barrageMessages];
    }
    
    public getCurrentPrediction() {
        return this._state.currentPrediction;
    }
    
    public getPredictionStats() {
        return { ...this._state.predictionStats };
    }

    // ==================== 状态更新 ====================
    
    /** 分发Action更新状态 */
    public dispatch(action: WatchAction): void {
        const previousState = { ...this._state };
        
        try {
            this._state = this.reduce(this._state, action);
            
            // 记录状态历史
            this.addToHistory(action, this._state);
            
            // 触发状态变化事件
            this._eventTarget.emit(WatchStateEvent.STATE_CHANGED, {
                action,
                previousState,
                currentState: this._state
            });
            
            // 触发特定事件
            this.emitSpecificEvents(action, previousState, this._state);
            
        } catch (error) {
            console.error('State update failed:', error);
            this.handleError({
                type: WatchErrorType.UNKNOWN_ERROR,
                message: 'State update failed',
                details: { action, error },
                timestamp: Date.now()
            });
        }
    }
    
    /** 状态归约器 */
    private reduce(state: WatchState, action: WatchAction): WatchState {
        switch (action.type) {
            case WatchActionType.SET_CONNECTION_STATUS:
                return {
                    ...state,
                    connectionStatus: action.payload
                };
                
            case WatchActionType.SET_NETWORK_QUALITY:
                return {
                    ...state,
                    networkQuality: action.payload
                };
                
            case WatchActionType.SET_CURRENT_ROOM:
                return {
                    ...state,
                    currentRoom: action.payload
                };
                
            case WatchActionType.UPDATE_ROOM_LIST:
                return {
                    ...state,
                    roomList: action.payload
                };
                
            case WatchActionType.UPDATE_VIEWER_COUNT:
                if (state.currentRoom) {
                    return {
                        ...state,
                        currentRoom: {
                            ...state.currentRoom,
                            viewerCount: action.payload
                        }
                    };
                }
                return state;
                
            case WatchActionType.ADD_BARRAGE_MESSAGE:
                const newMessages = [...state.barrageMessages, action.payload];
                // 限制消息数量，避免内存泄漏
                const maxMessages = 200;
                if (newMessages.length > maxMessages) {
                    newMessages.splice(0, newMessages.length - maxMessages);
                }
                return {
                    ...state,
                    barrageMessages: newMessages
                };
                
            case WatchActionType.UPDATE_BARRAGE_FILTER:
                return {
                    ...state,
                    barrageFilter: { ...state.barrageFilter, ...action.payload }
                };
                
            case WatchActionType.CLEAR_BARRAGE_MESSAGES:
                return {
                    ...state,
                    barrageMessages: []
                };
                
            case WatchActionType.SET_CURRENT_PREDICTION:
                return {
                    ...state,
                    currentPrediction: action.payload
                };
                
            case WatchActionType.ADD_USER_PREDICTION:
                return {
                    ...state,
                    userPredictions: [...state.userPredictions, action.payload]
                };
                
            case WatchActionType.UPDATE_PREDICTION_STATS:
                return {
                    ...state,
                    predictionStats: { ...state.predictionStats, ...action.payload }
                };
                
            case WatchActionType.UPDATE_GAME_STATE:
                return {
                    ...state,
                    gameState: action.payload
                };
                
            case WatchActionType.TOGGLE_BARRAGE_INPUT:
                return {
                    ...state,
                    isBarrageInputVisible: action.payload ?? !state.isBarrageInputVisible
                };
                
            case WatchActionType.TOGGLE_PREDICTION_PANEL:
                return {
                    ...state,
                    isPredictionPanelVisible: action.payload ?? !state.isPredictionPanelVisible
                };
                
            case WatchActionType.TOGGLE_VIEWER_LIST:
                return {
                    ...state,
                    isViewerListVisible: action.payload ?? !state.isViewerListVisible
                };
                
            default:
                console.warn('Unknown action type:', action.type);
                return state;
        }
    }

    // ==================== 事件系统 ====================
    
    /** 监听状态变化事件 */
    public on(event: WatchStateEvent, callback: Function, target?: any): void {
        this._eventTarget.on(event, callback, target);
    }
    
    /** 移除事件监听 */
    public off(event: WatchStateEvent, callback?: Function, target?: any): void {
        this._eventTarget.off(event, callback, target);
    }
    
    /** 触发特定事件 */
    private emitSpecificEvents(action: WatchAction, previousState: WatchState, currentState: WatchState): void {
        switch (action.type) {
            case WatchActionType.SET_CONNECTION_STATUS:
                if (previousState.connectionStatus !== currentState.connectionStatus) {
                    this._eventTarget.emit(WatchStateEvent.CONNECTION_STATUS_CHANGED, {
                        previous: previousState.connectionStatus,
                        current: currentState.connectionStatus
                    });
                }
                break;
                
            case WatchActionType.SET_CURRENT_ROOM:
                if (currentState.currentRoom && !previousState.currentRoom) {
                    this._eventTarget.emit(WatchStateEvent.ROOM_JOINED, currentState.currentRoom);
                } else if (!currentState.currentRoom && previousState.currentRoom) {
                    this._eventTarget.emit(WatchStateEvent.ROOM_LEFT, previousState.currentRoom);
                }
                break;
                
            case WatchActionType.ADD_BARRAGE_MESSAGE:
                this._eventTarget.emit(WatchStateEvent.BARRAGE_RECEIVED, action.payload);
                break;
                
            case WatchActionType.SET_CURRENT_PREDICTION:
                if (currentState.currentPrediction && !previousState.currentPrediction) {
                    this._eventTarget.emit(WatchStateEvent.PREDICTION_STARTED, currentState.currentPrediction);
                } else if (!currentState.currentPrediction && previousState.currentPrediction) {
                    this._eventTarget.emit(WatchStateEvent.PREDICTION_ENDED, previousState.currentPrediction);
                }
                break;
        }
    }

    // ==================== 便捷方法 ====================
    
    /** 连接到房间 */
    public joinRoom(roomId: string): void {
        this.dispatch({
            type: WatchActionType.SET_CONNECTION_STATUS,
            payload: ConnectionStatus.CONNECTING
        });
    }
    
    /** 离开房间 */
    public leaveRoom(): void {
        this.dispatch({
            type: WatchActionType.SET_CURRENT_ROOM,
            payload: null
        });
        
        this.dispatch({
            type: WatchActionType.CLEAR_BARRAGE_MESSAGES,
            payload: null
        });
        
        this.dispatch({
            type: WatchActionType.SET_CONNECTION_STATUS,
            payload: ConnectionStatus.DISCONNECTED
        });
    }
    
    /** 发送弹幕 */
    public sendBarrage(content: string): void {
        // 这里会通过网络管理器发送，然后接收到确认后添加到状态
        console.log('Sending barrage:', content);
    }

    // ==================== 辅助方法 ====================
    
    /** 获取默认弹幕过滤配置 */
    private getDefaultBarrageFilter(): BarrageFilterConfig {
        return {
            showPredictions: true,
            showSystemMessages: true,
            showVipMessages: true,
            hideBlockedUsers: true,
            maxLength: 50,
            rateLimitPerMinute: 10
        };
    }
    
    /** 获取默认预测统计 */
    private getDefaultPredictionStats(): PredictionStats {
        return {
            totalPredictions: 0,
            correctPredictions: 0,
            accuracy: 0,
            totalPoints: 0,
            currentStreak: 0,
            maxStreak: 0
        };
    }
    
    /** 添加到历史记录 */
    private addToHistory(action: WatchAction, state: WatchState): void {
        this._stateHistory.push({
            action,
            state: { ...state },
            timestamp: Date.now()
        });
        
        // 限制历史记录大小
        if (this._stateHistory.length > this.MAX_HISTORY_SIZE) {
            this._stateHistory.shift();
        }
    }
    
    /** 处理错误 */
    private handleError(error: WatchError): void {
        console.error('Watch error:', error);
        this._eventTarget.emit(WatchStateEvent.ERROR_OCCURRED, error);
    }
    
    /** 获取状态历史（调试用） */
    public getStateHistory() {
        return [...this._stateHistory];
    }
    
    /** 重置状态 */
    public reset(): void {
        this._state = {
            connectionStatus: ConnectionStatus.DISCONNECTED,
            networkQuality: NetworkQuality.DISCONNECTED,
            currentRoom: null,
            roomList: [],
            currentUser: null,
            viewers: [],
            barrageMessages: [],
            barrageFilter: this.getDefaultBarrageFilter(),
            currentPrediction: null,
            userPredictions: [],
            predictionStats: this.getDefaultPredictionStats(),
            gameState: null,
            isBarrageInputVisible: false,
            isPredictionPanelVisible: false,
            isViewerListVisible: false
        };
        
        this._stateHistory = [];
        
        this._eventTarget.emit(WatchStateEvent.STATE_CHANGED, {
            action: { type: 'RESET' },
            previousState: null,
            currentState: this._state
        });
    }
}
