/**
 * 围观房间管理器
 * 
 * 负责围观房间的进入、退出、状态管理等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, EventTarget } from 'cc';
import { 
    RoomInfo, 
    UserInfo, 
    ConnectionStatus,
    WatchError,
    WatchErrorType,
    WSMessageType
} from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from './WatchStateManager';
import { WatchNetworkManager } from './WatchNetworkManager';

const { ccclass, property } = _decorator;

/** 房间事件名称 */
export enum RoomEvent {
    ROOM_ENTERED = 'room_entered',
    ROOM_LEFT = 'room_left',
    ROOM_ERROR = 'room_error',
    VIEWER_JOINED = 'viewer_joined',
    VIEWER_LEFT = 'viewer_left',
    ROOM_STATE_UPDATED = 'room_state_updated'
}

/** 进入房间的结果 */
export interface EnterRoomResult {
    success: boolean;
    roomInfo?: RoomInfo;
    error?: WatchError;
}

@ccclass('WatchRoomManager')
export class WatchRoomManager extends Component {
    private static _instance: WatchRoomManager = null;
    
    /** 事件系统 */
    private _eventTarget: EventTarget = new EventTarget();
    
    /** 管理器引用 */
    private _stateManager: WatchStateManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    /** 当前用户信息 */
    private _currentUser: UserInfo = null;
    
    /** 房间进入状态 */
    private _isEntering: boolean = false;
    private _enteringRoomId: string = null;
    
    /** 心跳定时器 */
    private _roomHeartbeatTimer: number = null;
    private readonly ROOM_HEARTBEAT_INTERVAL = 30000; // 30秒

    // ==================== 单例模式 ====================
    
    public static getInstance(): WatchRoomManager {
        if (!WatchRoomManager._instance) {
            const node = new Node('WatchRoomManager');
            WatchRoomManager._instance = node.addComponent(WatchRoomManager);
            node.parent = cc.director.getScene();
            cc.game.addPersistRootNode(node);
        }
        return WatchRoomManager._instance;
    }

    protected onLoad() {
        if (WatchRoomManager._instance && WatchRoomManager._instance !== this) {
            this.node.destroy();
            return;
        }
        WatchRoomManager._instance = this;
        
        this.initializeManagers();
        this.setupEventListeners();
    }

    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听连接状态变化
        this._stateManager.on(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
        
        // 监听房间相关事件
        this._stateManager.on(WatchStateEvent.ROOM_JOINED, this.onRoomJoined, this);
        this._stateManager.on(WatchStateEvent.ROOM_LEFT, this.onRoomLeft, this);
    }

    // ==================== 公共API ====================
    
    /** 进入围观房间 */
    public async enterRoom(roomId: string, userInfo?: UserInfo): Promise<EnterRoomResult> {
        if (this._isEntering) {
            return {
                success: false,
                error: {
                    type: WatchErrorType.VALIDATION_ERROR,
                    message: 'Already entering a room',
                    timestamp: Date.now()
                }
            };
        }
        
        try {
            this._isEntering = true;
            this._enteringRoomId = roomId;
            
            // 设置当前用户信息
            if (userInfo) {
                this._currentUser = userInfo;
            }
            
            // 检查网络连接
            if (!this._networkManager.isConnected()) {
                console.log('Network not connected, connecting...');
                const connected = await this._networkManager.connect(roomId);
                if (!connected) {
                    throw new Error('Failed to establish network connection');
                }
            }
            
            // 发送加入房间请求
            const joinResult = await this.sendJoinRoomRequest(roomId);
            if (!joinResult.success) {
                throw new Error(joinResult.error || 'Failed to join room');
            }
            
            // 开始房间心跳
            this.startRoomHeartbeat();
            
            // 触发进入房间事件
            this._eventTarget.emit(RoomEvent.ROOM_ENTERED, joinResult.roomInfo);
            
            return {
                success: true,
                roomInfo: joinResult.roomInfo
            };
            
        } catch (error) {
            console.error('Failed to enter room:', error);
            
            const watchError: WatchError = {
                type: WatchErrorType.CONNECTION_ERROR,
                message: error.message || 'Failed to enter room',
                details: { roomId, error },
                timestamp: Date.now()
            };
            
            this._eventTarget.emit(RoomEvent.ROOM_ERROR, watchError);
            
            return {
                success: false,
                error: watchError
            };
            
        } finally {
            this._isEntering = false;
            this._enteringRoomId = null;
        }
    }
    
    /** 离开围观房间 */
    public async leaveRoom(): Promise<boolean> {
        const currentRoom = this._stateManager.getCurrentRoom();
        if (!currentRoom) {
            console.log('No room to leave');
            return true;
        }
        
        try {
            // 停止房间心跳
            this.stopRoomHeartbeat();
            
            // 发送离开房间请求
            await this.sendLeaveRoomRequest(currentRoom.roomId);
            
            // 更新状态
            this._stateManager.leaveRoom();
            
            // 触发离开房间事件
            this._eventTarget.emit(RoomEvent.ROOM_LEFT, currentRoom);
            
            return true;
            
        } catch (error) {
            console.error('Failed to leave room:', error);
            
            // 即使发送失败，也要清理本地状态
            this._stateManager.leaveRoom();
            
            return false;
        }
    }
    
    /** 获取房间列表 */
    public async getRoomList(): Promise<RoomInfo[]> {
        try {
            // 这里应该调用API获取房间列表
            // 暂时返回模拟数据
            const mockRooms: RoomInfo[] = [
                {
                    roomId: 'room_001',
                    playerInfo: {
                        userId: 'player_001',
                        nickname: '方言达人',
                        avatar: 'avatar_001.jpg',
                        level: 15,
                        region: '四川成都',
                        isVip: true,
                        isOnline: true,
                        joinTime: Date.now() - 300000
                    },
                    gameStatus: 'playing',
                    viewerCount: 156,
                    currentQuestion: {
                        id: 'q_001',
                        dialect: '四川话',
                        difficulty: 'medium',
                        questionNumber: 3,
                        totalQuestions: 10
                    },
                    isLive: true,
                    createdAt: Date.now() - 600000,
                    updatedAt: Date.now()
                },
                {
                    roomId: 'room_002',
                    playerInfo: {
                        userId: 'player_002',
                        nickname: '粤语小王子',
                        avatar: 'avatar_002.jpg',
                        level: 12,
                        region: '广东广州',
                        isVip: false,
                        isOnline: true,
                        joinTime: Date.now() - 180000
                    },
                    gameStatus: 'waiting',
                    viewerCount: 89,
                    isLive: true,
                    createdAt: Date.now() - 300000,
                    updatedAt: Date.now()
                }
            ];
            
            // 更新状态
            this._stateManager.dispatch({
                type: 'UPDATE_ROOM_LIST',
                payload: mockRooms
            });
            
            return mockRooms;
            
        } catch (error) {
            console.error('Failed to get room list:', error);
            return [];
        }
    }
    
    /** 刷新当前房间信息 */
    public async refreshCurrentRoom(): Promise<boolean> {
        const currentRoom = this._stateManager.getCurrentRoom();
        if (!currentRoom) {
            return false;
        }
        
        try {
            // 这里应该调用API获取最新房间信息
            // 暂时模拟更新
            const updatedRoom: RoomInfo = {
                ...currentRoom,
                viewerCount: currentRoom.viewerCount + Math.floor(Math.random() * 10) - 5,
                updatedAt: Date.now()
            };
            
            this._stateManager.dispatch({
                type: 'SET_CURRENT_ROOM',
                payload: updatedRoom
            });
            
            return true;
            
        } catch (error) {
            console.error('Failed to refresh room info:', error);
            return false;
        }
    }

    // ==================== 私有方法 ====================
    
    /** 发送加入房间请求 */
    private async sendJoinRoomRequest(roomId: string): Promise<{ success: boolean; roomInfo?: RoomInfo; error?: string }> {
        return new Promise((resolve) => {
            // 发送WebSocket消息
            this._networkManager.joinRoom(roomId, this._currentUser);
            
            // 设置超时
            const timeout = setTimeout(() => {
                resolve({
                    success: false,
                    error: 'Join room timeout'
                });
            }, 10000); // 10秒超时
            
            // 监听加入房间响应
            const onRoomJoined = (roomInfo: RoomInfo) => {
                clearTimeout(timeout);
                this._stateManager.off(WatchStateEvent.ROOM_JOINED, onRoomJoined);
                resolve({
                    success: true,
                    roomInfo
                });
            };
            
            this._stateManager.on(WatchStateEvent.ROOM_JOINED, onRoomJoined);
        });
    }
    
    /** 发送离开房间请求 */
    private async sendLeaveRoomRequest(roomId: string): Promise<void> {
        this._networkManager.sendMessage({
            type: WSMessageType.LEAVE_ROOM,
            timestamp: Date.now(),
            roomId,
            data: { roomId }
        });
    }
    
    /** 开始房间心跳 */
    private startRoomHeartbeat(): void {
        this.stopRoomHeartbeat();
        
        this._roomHeartbeatTimer = setInterval(() => {
            this.sendRoomHeartbeat();
        }, this.ROOM_HEARTBEAT_INTERVAL);
    }
    
    /** 停止房间心跳 */
    private stopRoomHeartbeat(): void {
        if (this._roomHeartbeatTimer) {
            clearInterval(this._roomHeartbeatTimer);
            this._roomHeartbeatTimer = null;
        }
    }
    
    /** 发送房间心跳 */
    private sendRoomHeartbeat(): void {
        const currentRoom = this._stateManager.getCurrentRoom();
        if (!currentRoom) {
            return;
        }
        
        this._networkManager.sendMessage({
            type: WSMessageType.HEARTBEAT,
            timestamp: Date.now(),
            roomId: currentRoom.roomId,
            data: { type: 'room_heartbeat' }
        });
    }

    // ==================== 事件处理 ====================
    
    /** 连接状态变化处理 */
    private onConnectionStatusChanged(event: any): void {
        const { current } = event;
        
        if (current === ConnectionStatus.DISCONNECTED) {
            // 连接断开，清理房间状态
            this.stopRoomHeartbeat();
        } else if (current === ConnectionStatus.CONNECTED) {
            // 连接恢复，如果之前在房间中，尝试重新加入
            const currentRoom = this._stateManager.getCurrentRoom();
            if (currentRoom) {
                this.rejoinRoom(currentRoom.roomId);
            }
        }
    }
    
    /** 房间加入成功处理 */
    private onRoomJoined(roomInfo: RoomInfo): void {
        console.log('Room joined successfully:', roomInfo.roomId);
        this.startRoomHeartbeat();
    }
    
    /** 房间离开处理 */
    private onRoomLeft(roomInfo: RoomInfo): void {
        console.log('Room left:', roomInfo.roomId);
        this.stopRoomHeartbeat();
    }
    
    /** 重新加入房间 */
    private async rejoinRoom(roomId: string): Promise<void> {
        try {
            console.log('Attempting to rejoin room:', roomId);
            await this.sendJoinRoomRequest(roomId);
        } catch (error) {
            console.error('Failed to rejoin room:', error);
        }
    }

    // ==================== 事件系统 ====================
    
    /** 监听房间事件 */
    public on(event: RoomEvent, callback: Function, target?: any): void {
        this._eventTarget.on(event, callback, target);
    }
    
    /** 移除事件监听 */
    public off(event: RoomEvent, callback?: Function, target?: any): void {
        this._eventTarget.off(event, callback, target);
    }

    // ==================== 工具方法 ====================
    
    /** 获取当前房间信息 */
    public getCurrentRoom(): RoomInfo | null {
        return this._stateManager.getCurrentRoom();
    }
    
    /** 检查是否在房间中 */
    public isInRoom(): boolean {
        return this._stateManager.getCurrentRoom() !== null;
    }
    
    /** 获取当前用户信息 */
    public getCurrentUser(): UserInfo | null {
        return this._currentUser;
    }
    
    /** 设置当前用户信息 */
    public setCurrentUser(userInfo: UserInfo): void {
        this._currentUser = userInfo;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止心跳
        this.stopRoomHeartbeat();
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
            this._stateManager.off(WatchStateEvent.ROOM_JOINED, this.onRoomJoined, this);
            this._stateManager.off(WatchStateEvent.ROOM_LEFT, this.onRoomLeft, this);
        }
    }
}
