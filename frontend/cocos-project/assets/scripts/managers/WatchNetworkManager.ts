/**
 * 围观功能网络通信管理器
 * 
 * 负责WebSocket连接管理、消息收发、断线重连等网络相关功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component } from 'cc';
import { 
    WSMessage, 
    WSMessageType, 
    ConnectionStatus, 
    NetworkQuality,
    WatchConfig,
    WatchError,
    WatchErrorType,
    BarrageMessage,
    JoinRoomMessage,
    BarrageWSMessage,
    PredictionSubmitMessage
} from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from './WatchStateManager';

const { ccclass, property } = _decorator;

/** 网络事件名称 */
export enum NetworkEvent {
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
    MESSAGE_RECEIVED = 'message_received',
    ERROR_OCCURRED = 'error_occurred'
}

@ccclass('WatchNetworkManager')
export class WatchNetworkManager extends Component {
    private static _instance: WatchNetworkManager = null;
    
    /** WebSocket连接 */
    private _websocket: WebSocket = null;
    
    /** 状态管理器 */
    private _stateManager: WatchStateManager = null;
    
    /** 配置信息 */
    private _config: WatchConfig = {
        websocketUrl: 'wss://api.hometown-dialect.com/ws',
        heartbeatInterval: 30000, // 30秒心跳
        reconnectAttempts: 5,
        reconnectDelay: 2000, // 2秒重连延迟
        barrageMaxLength: 50,
        barrageRateLimit: 10,
        barrageAutoScroll: true,
        predictionTimeLimit: 30,
        predictionMaxOptions: 4,
        maxViewersDisplay: 8,
        animationDuration: 300,
        maxBarrageMessages: 200,
        updateInterval: 100
    };
    
    /** 重连相关 */
    private _reconnectAttempts: number = 0;
    private _reconnectTimer: number = null;
    private _isReconnecting: boolean = false;
    
    /** 心跳相关 */
    private _heartbeatTimer: number = null;
    private _lastHeartbeatTime: number = 0;
    
    /** 消息队列 */
    private _messageQueue: WSMessage[] = [];
    private _isProcessingQueue: boolean = false;
    
    /** 网络质量监控 */
    private _latencyHistory: number[] = [];
    private _lastPingTime: number = 0;

    // ==================== 单例模式 ====================
    
    public static getInstance(): WatchNetworkManager {
        if (!WatchNetworkManager._instance) {
            const node = new Node('WatchNetworkManager');
            WatchNetworkManager._instance = node.addComponent(WatchNetworkManager);
            node.parent = cc.director.getScene();
            cc.game.addPersistRootNode(node);
        }
        return WatchNetworkManager._instance;
    }

    protected onLoad() {
        if (WatchNetworkManager._instance && WatchNetworkManager._instance !== this) {
            this.node.destroy();
            return;
        }
        WatchNetworkManager._instance = this;
        this._stateManager = WatchStateManager.getInstance();
    }

    protected onDestroy() {
        this.disconnect();
        this.clearTimers();
    }

    // ==================== 连接管理 ====================
    
    /** 连接到WebSocket服务器 */
    public async connect(roomId?: string): Promise<boolean> {
        if (this._websocket && this._websocket.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            return true;
        }
        
        try {
            this._stateManager.dispatch({
                type: 'SET_CONNECTION_STATUS',
                payload: ConnectionStatus.CONNECTING
            });
            
            const url = roomId ? `${this._config.websocketUrl}?roomId=${roomId}` : this._config.websocketUrl;
            this._websocket = new WebSocket(url);
            
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 10000); // 10秒超时
                
                this._websocket.onopen = () => {
                    clearTimeout(timeout);
                    this.onConnected();
                    resolve(true);
                };
                
                this._websocket.onerror = (error) => {
                    clearTimeout(timeout);
                    this.onError(error);
                    reject(error);
                };
                
                this._websocket.onmessage = (event) => {
                    this.onMessage(event);
                };
                
                this._websocket.onclose = (event) => {
                    this.onDisconnected(event);
                };
            });
            
        } catch (error) {
            this.handleError({
                type: WatchErrorType.CONNECTION_ERROR,
                message: 'Failed to connect to WebSocket',
                details: error,
                timestamp: Date.now()
            });
            return false;
        }
    }
    
    /** 断开连接 */
    public disconnect(): void {
        this._isReconnecting = false;
        this.clearTimers();
        
        if (this._websocket) {
            this._websocket.close(1000, 'User disconnected');
            this._websocket = null;
        }
        
        this._stateManager.dispatch({
            type: 'SET_CONNECTION_STATUS',
            payload: ConnectionStatus.DISCONNECTED
        });
    }
    
    /** 连接成功处理 */
    private onConnected(): void {
        console.log('WebSocket connected');
        
        this._reconnectAttempts = 0;
        this._isReconnecting = false;
        
        this._stateManager.dispatch({
            type: 'SET_CONNECTION_STATUS',
            payload: ConnectionStatus.CONNECTED
        });
        
        this._stateManager.dispatch({
            type: 'SET_NETWORK_QUALITY',
            payload: NetworkQuality.EXCELLENT
        });
        
        // 开始心跳
        this.startHeartbeat();
        
        // 处理消息队列
        this.processMessageQueue();
    }
    
    /** 连接断开处理 */
    private onDisconnected(event: CloseEvent): void {
        console.log('WebSocket disconnected:', event.code, event.reason);
        
        this.clearTimers();
        
        this._stateManager.dispatch({
            type: 'SET_CONNECTION_STATUS',
            payload: ConnectionStatus.DISCONNECTED
        });
        
        this._stateManager.dispatch({
            type: 'SET_NETWORK_QUALITY',
            payload: NetworkQuality.DISCONNECTED
        });
        
        // 如果不是主动断开，尝试重连
        if (event.code !== 1000 && !this._isReconnecting) {
            this.attemptReconnect();
        }
    }
    
    /** 错误处理 */
    private onError(error: Event): void {
        console.error('WebSocket error:', error);
        
        this.handleError({
            type: WatchErrorType.CONNECTION_ERROR,
            message: 'WebSocket connection error',
            details: error,
            timestamp: Date.now()
        });
    }

    // ==================== 消息处理 ====================
    
    /** 接收消息处理 */
    private onMessage(event: MessageEvent): void {
        try {
            const message: WSMessage = JSON.parse(event.data);
            this.handleIncomingMessage(message);
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    /** 处理接收到的消息 */
    private handleIncomingMessage(message: WSMessage): void {
        switch (message.type) {
            case WSMessageType.HEARTBEAT:
                this.handleHeartbeatResponse(message);
                break;
                
            case WSMessageType.BARRAGE_MESSAGE:
                this.handleBarrageMessage(message as BarrageWSMessage);
                break;
                
            case WSMessageType.GAME_STATE_UPDATE:
                this.handleGameStateUpdate(message);
                break;
                
            case WSMessageType.VIEWER_COUNT_UPDATE:
                this.handleViewerCountUpdate(message);
                break;
                
            case WSMessageType.PREDICTION_START:
                this.handlePredictionStart(message);
                break;
                
            case WSMessageType.PREDICTION_RESULT:
                this.handlePredictionResult(message);
                break;
                
            case WSMessageType.SYSTEM_MESSAGE:
                this.handleSystemMessage(message);
                break;
                
            case WSMessageType.ERROR_MESSAGE:
                this.handleErrorMessage(message);
                break;
                
            default:
                console.warn('Unknown message type:', message.type);
        }
    }
    
    /** 发送消息 */
    public sendMessage(message: WSMessage): boolean {
        if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {
            // 连接未建立，加入队列
            this._messageQueue.push(message);
            return false;
        }
        
        try {
            this._websocket.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('Failed to send message:', error);
            this._messageQueue.push(message);
            return false;
        }
    }

    // ==================== 具体消息处理 ====================
    
    /** 处理弹幕消息 */
    private handleBarrageMessage(message: BarrageWSMessage): void {
        this._stateManager.dispatch({
            type: 'ADD_BARRAGE_MESSAGE',
            payload: message.data
        });
    }
    
    /** 处理游戏状态更新 */
    private handleGameStateUpdate(message: WSMessage): void {
        this._stateManager.dispatch({
            type: 'UPDATE_GAME_STATE',
            payload: message.data
        });
    }
    
    /** 处理围观人数更新 */
    private handleViewerCountUpdate(message: WSMessage): void {
        this._stateManager.dispatch({
            type: 'UPDATE_VIEWER_COUNT',
            payload: message.data.count
        });
    }
    
    /** 处理预测开始 */
    private handlePredictionStart(message: WSMessage): void {
        this._stateManager.dispatch({
            type: 'SET_CURRENT_PREDICTION',
            payload: message.data
        });
    }
    
    /** 处理预测结果 */
    private handlePredictionResult(message: WSMessage): void {
        this._stateManager.dispatch({
            type: 'UPDATE_PREDICTION_STATS',
            payload: message.data
        });
    }
    
    /** 处理系统消息 */
    private handleSystemMessage(message: WSMessage): void {
        console.log('System message:', message.data);
    }
    
    /** 处理错误消息 */
    private handleErrorMessage(message: WSMessage): void {
        this.handleError({
            type: WatchErrorType.UNKNOWN_ERROR,
            message: message.data.message || 'Server error',
            details: message.data,
            timestamp: Date.now()
        });
    }

    // ==================== 心跳机制 ====================
    
    /** 开始心跳 */
    private startHeartbeat(): void {
        this.clearHeartbeat();
        
        this._heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
        }, this._config.heartbeatInterval);
    }
    
    /** 发送心跳 */
    private sendHeartbeat(): void {
        this._lastPingTime = Date.now();
        
        this.sendMessage({
            type: WSMessageType.HEARTBEAT,
            timestamp: this._lastPingTime
        });
    }
    
    /** 处理心跳响应 */
    private handleHeartbeatResponse(message: WSMessage): void {
        const latency = Date.now() - this._lastPingTime;
        this.updateNetworkQuality(latency);
    }
    
    /** 清除心跳定时器 */
    private clearHeartbeat(): void {
        if (this._heartbeatTimer) {
            clearInterval(this._heartbeatTimer);
            this._heartbeatTimer = null;
        }
    }

    // ==================== 重连机制 ====================
    
    /** 尝试重连 */
    private attemptReconnect(): void {
        if (this._isReconnecting || this._reconnectAttempts >= this._config.reconnectAttempts) {
            return;
        }
        
        this._isReconnecting = true;
        this._reconnectAttempts++;
        
        this._stateManager.dispatch({
            type: 'SET_CONNECTION_STATUS',
            payload: ConnectionStatus.RECONNECTING
        });
        
        const delay = this._config.reconnectDelay * Math.pow(2, this._reconnectAttempts - 1); // 指数退避
        
        this._reconnectTimer = setTimeout(async () => {
            try {
                await this.connect();
            } catch (error) {
                console.error('Reconnect failed:', error);
                this._isReconnecting = false;
                
                if (this._reconnectAttempts < this._config.reconnectAttempts) {
                    this.attemptReconnect();
                } else {
                    this.handleError({
                        type: WatchErrorType.CONNECTION_ERROR,
                        message: 'Max reconnect attempts reached',
                        timestamp: Date.now()
                    });
                }
            }
        }, delay);
    }

    // ==================== 网络质量监控 ====================
    
    /** 更新网络质量 */
    private updateNetworkQuality(latency: number): void {
        this._latencyHistory.push(latency);
        
        // 保持最近10次的延迟记录
        if (this._latencyHistory.length > 10) {
            this._latencyHistory.shift();
        }
        
        const avgLatency = this._latencyHistory.reduce((a, b) => a + b, 0) / this._latencyHistory.length;
        
        let quality: NetworkQuality;
        if (avgLatency < 100) {
            quality = NetworkQuality.EXCELLENT;
        } else if (avgLatency < 300) {
            quality = NetworkQuality.GOOD;
        } else {
            quality = NetworkQuality.POOR;
        }
        
        this._stateManager.dispatch({
            type: 'SET_NETWORK_QUALITY',
            payload: quality
        });
    }

    // ==================== 辅助方法 ====================
    
    /** 处理消息队列 */
    private processMessageQueue(): void {
        if (this._isProcessingQueue || this._messageQueue.length === 0) {
            return;
        }
        
        this._isProcessingQueue = true;
        
        while (this._messageQueue.length > 0) {
            const message = this._messageQueue.shift();
            if (!this.sendMessage(message)) {
                // 发送失败，重新加入队列
                this._messageQueue.unshift(message);
                break;
            }
        }
        
        this._isProcessingQueue = false;
    }
    
    /** 清除所有定时器 */
    private clearTimers(): void {
        this.clearHeartbeat();
        
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = null;
        }
    }
    
    /** 处理错误 */
    private handleError(error: WatchError): void {
        console.error('Network error:', error);
        // 可以通过事件系统通知其他组件
    }
    
    /** 获取连接状态 */
    public isConnected(): boolean {
        return this._websocket && this._websocket.readyState === WebSocket.OPEN;
    }
    
    /** 获取网络延迟 */
    public getLatency(): number {
        if (this._latencyHistory.length === 0) return 0;
        return this._latencyHistory[this._latencyHistory.length - 1];
    }

    // ==================== 公共API ====================
    
    /** 加入房间 */
    public joinRoom(roomId: string, userInfo: any): void {
        const message: JoinRoomMessage = {
            type: WSMessageType.JOIN_ROOM,
            timestamp: Date.now(),
            data: { roomId, userInfo }
        };
        
        this.sendMessage(message);
    }
    
    /** 发送弹幕 */
    public sendBarrage(content: string, roomId: string): void {
        const message: WSMessage = {
            type: WSMessageType.BARRAGE_SEND,
            timestamp: Date.now(),
            roomId,
            data: { content }
        };
        
        this.sendMessage(message);
    }
    
    /** 提交预测 */
    public submitPrediction(questionId: string, selectedOption: string): void {
        const message: PredictionSubmitMessage = {
            type: WSMessageType.PREDICTION_SUBMIT,
            timestamp: Date.now(),
            data: { questionId, selectedOption, timestamp: Date.now() }
        };
        
        this.sendMessage(message);
    }
}
