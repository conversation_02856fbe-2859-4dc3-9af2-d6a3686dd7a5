import { _decorator, Component, director, Node } from 'cc';
import { GameState, GameDifficulty, AnswerResult, GAME_RULES, SCORE_CONFIG, DEBUG_CONFIG } from '../constants/GameConstants';
import { IGameSession, IQuestionData, IAnswerRecord, IGameEvent, IUserProfile } from '../data/GameData';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem } from '../core/ErrorHandlingSystem';
import { AudioManager } from './AudioManager';
import { DataManager } from './DataManager';
import { EventManager } from './EventManager';
import { SceneManager } from './SceneManager';
import { GameAPIManager } from './GameAPIManager';
import { MemoryManager } from '../utils/MemoryManager';

const { ccclass, property } = _decorator;

/**
 * 游戏核心管理器
 * 负责游戏流程控制、状态管理、数据协调
 */
@ccclass('GameManager')
export class GameManager extends Component {
    private static _instance: GameManager = null;
    
    // 当前游戏状态
    private _gameState: GameState = GameState.LOADING;
    
    // 当前游戏会话
    private _currentSession: IGameSession = null;
    
    // 管理器注册中心和错误处理系统
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    private _memoryManager: MemoryManager = null;
    
    // 计时器
    private _gameTimer: number = 0;
    private _questionTimer: number = 0;
    private _answerStartTime: number = 0;
    
    // 游戏统计
    private _totalGames: number = 0;
    private _currentCombo: number = 0;
    private _maxCombo: number = 0;
    
    public static get instance(): GameManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (GameManager._instance === null) {
            GameManager._instance = this;
            this.node.setParent(director.getScene());
            director.addPersistRootNode(this.node);
            this.initializeManagers();
        } else {
            this.destroy();
        }
    }

    protected onDestroy(): void {
        if (GameManager._instance === this) {
            GameManager._instance = null;
        }
        this.cleanup();

        // 清理内存管理器中的资源
        if (this._memoryManager) {
            this._memoryManager.cleanupComponent('GameManager');
        }
    }
    
    /**
     * 初始化管理器
     */
    private async initializeManagers(): Promise<void> {
        try {
            // 初始化核心系统
            this._managerRegistry = ManagerRegistry.getInstance();
            this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
            this._memoryManager = MemoryManager.getInstance();

            // 注册所有管理器到注册中心
            const eventManager = this.node.addComponent(EventManager);
            const dataManager = this.node.addComponent(DataManager);
            const audioManager = this.node.addComponent(AudioManager);
            const sceneManager = this.node.addComponent(SceneManager);
            const gameAPIManager = GameAPIManager.getInstance();

            this._managerRegistry.registerManager('GameManager', this, []);
            this._managerRegistry.registerManager('EventManager', eventManager, []);
            this._managerRegistry.registerManager('DataManager', dataManager, ['EventManager']);
            this._managerRegistry.registerManager('AudioManager', audioManager, ['EventManager']);
            this._managerRegistry.registerManager('SceneManager', sceneManager, ['EventManager']);
            this._managerRegistry.registerManager('GameAPIManager', gameAPIManager, ['EventManager']);

            // 检查循环依赖
            if (this._managerRegistry.checkCircularDependencies()) {
                throw new Error('检测到管理器循环依赖');
            }

            // 按依赖顺序初始化管理器
            const initOrder = this._managerRegistry.getInitializationOrder();
            console.log('[GameManager] 管理器初始化顺序:', initOrder);

            for (const managerName of initOrder) {
                if (managerName === 'GameManager') continue; // 跳过自己

                const manager = this._managerRegistry.getManager(managerName);
                if (manager && typeof manager.initialize === 'function') {
                    await manager.initialize();
                    this._managerRegistry.markInitialized(managerName);
                }
            }

            // 注册事件监听器
            this.registerEventListeners();

            // 设置初始状态
            this.setGameState(GameState.MENU);

            // 标记GameManager为已初始化
            this._managerRegistry.markInitialized('GameManager');

            console.log('[GameManager] 管理器初始化完成');

            // 调试模式下启动测试工具
            if (DEBUG_CONFIG.ENABLED) {
                this.initializeTestMode();
            }
        } catch (error) {
            console.error('[GameManager] 管理器初始化失败:', error);
            this.handleError(error);
        }
    }
    
    /**
     * 注册事件监听器
     */
    private registerEventListeners(): void {
        // 监听音频事件
        this._eventManager.on('audio_play_complete', this.onAudioPlayComplete, this);
        this._eventManager.on('audio_play_error', this.onAudioPlayError, this);
        
        // 监听答题事件
        this._eventManager.on('answer_selected', this.onAnswerSelected, this);
        this._eventManager.on('answer_timeout', this.onAnswerTimeout, this);
        
        // 监听游戏事件
        this._eventManager.on('game_pause_request', this.pauseGame, this);
        this._eventManager.on('game_resume_request', this.resumeGame, this);
        this._eventManager.on('game_restart_request', this.restartGame, this);
        this._eventManager.on('game_quit_request', this.quitGame, this);
        
        // 监听场景事件
        this._eventManager.on('scene_loaded', this.onSceneLoaded, this);
        this._eventManager.on('scene_load_error', this.onSceneLoadError, this);
        
        // 监听API相关事件
        this._eventManager.on('user-login-success', this.onUserLoginSuccess, this);
        this._eventManager.on('user-login-failed', this.onUserLoginFailed, this);
        this._eventManager.on('user-need-login', this.onUserNeedLogin, this);
        this._eventManager.on('game-session-started', this.onGameSessionStarted, this);
        this._eventManager.on('answer-submitted', this.onAnswerSubmittedToAPI, this);
        this._eventManager.on('game-finished', this.onGameFinishedToAPI, this);
    }
    
    /**
     * 开始新游戏
     */
    public async startNewGame(difficulty: GameDifficulty = GameDifficulty.MEDIUM): Promise<void> {
        try {
            console.log('[GameManager] 开始新游戏, 难度:', difficulty);
            
            // 使用GameAPIManager创建游戏会话和获取题目
            this._currentSession = await this._gameAPIManager.startNewGame(difficulty, GAME_RULES.QUESTIONS_PER_GAME);
            
            if (!this._currentSession.questions || this._currentSession.questions.length === 0) {
                throw new Error('无法获取题目数据');
            }
            
            // 预加载音频资源
            await this._audioManager.preloadQuestionAudios(this._currentSession.questions);
            
            // 设置游戏状态
            this.setGameState(GameState.PLAYING);
            
            // 切换到游戏场景
            await this._sceneManager.loadScene('GameScene');
            
            // 开始第一题
            this.startQuestion();
            
            // 发送游戏开始事件
            this._eventManager.emit('game_started', {
                sessionId: this._currentSession.sessionId,
                difficulty: difficulty,
                questionCount: this._currentSession.questions.length
            });
            
        } catch (error) {
            console.error('[GameManager] 开始游戏失败:', error);
            this.handleError(error);
        }
    }
    
    /**
     * 开始当前题目
     */
    private startQuestion(): void {
        if (!this._currentSession || this._currentSession.currentQuestionIndex >= this._currentSession.questions.length) {
            this.endGame();
            return;
        }
        
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) {
            console.error('[GameManager] 当前题目数据无效');
            return;
        }
        
        console.log(`[GameManager] 开始第 ${this._currentSession.currentQuestionIndex + 1} 题:`, currentQuestion.id);
        
        // 记录答题开始时间
        this._answerStartTime = Date.now();
        
        // 启动答题计时器
        this.startQuestionTimer();
        
        // 发送题目开始事件
        this._eventManager.emit('question_started', {
            question: currentQuestion,
            questionIndex: this._currentSession.currentQuestionIndex,
            totalQuestions: this._currentSession.questions.length
        });
        
        // 自动播放音频（如果设置了自动播放）
        const settings = this._dataManager.getGameSettings();
        if (settings && settings.autoPlayAudio) {
            this.playCurrentQuestionAudio();
        }
    }
    
    /**
     * 播放当前题目音频
     */
    public async playCurrentQuestionAudio(): Promise<void> {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) {
            console.error('[GameManager] 无法播放音频: 当前题目无效');
            return;
        }
        
        try {
            await this._audioManager.playQuestionAudio(currentQuestion);
        } catch (error) {
            console.error('[GameManager] 播放音频失败:', error);
            this._eventManager.emit('audio_play_error', { error, questionId: currentQuestion.id });
        }
    }
    
    /**
     * 提交答案
     */
    public async submitAnswer(selectedAnswer: number): Promise<void> {
        if (this._gameState !== GameState.PLAYING || !this._currentSession) {
            console.warn('[GameManager] 游戏状态异常，无法提交答案');
            return;
        }
        
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) {
            console.error('[GameManager] 无法提交答案: 当前题目无效');
            return;
        }
        
        // 停止计时器
        this.stopQuestionTimer();
        
        // 计算答题时间
        const answerTime = Date.now() - this._answerStartTime;
        
        // 判断答案是否正确
        const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
        const result = isCorrect ? AnswerResult.CORRECT : AnswerResult.WRONG;
        
        // 计算得分
        const score = this.calculateScore(currentQuestion, answerTime, isCorrect);
        
        // 更新连击统计
        if (isCorrect) {
            this._currentCombo++;
            this._maxCombo = Math.max(this._maxCombo, this._currentCombo);
            this._currentSession.correctCount++;
        } else {
            this._currentCombo = 0;
        }
        
        this._currentSession.currentCombo = this._currentCombo;
        this._currentSession.comboCount = this._maxCombo;
        
        // 创建答题记录
        const answerRecord: IAnswerRecord = {
            questionId: currentQuestion.id,
            selectedAnswer: selectedAnswer,
            correctAnswer: currentQuestion.correctAnswer,
            result: result,
            answerTime: answerTime,
            score: score,
            audioPlayCount: this._audioManager.getPlayCount(currentQuestion.id),
            timestamp: Date.now()
        };
        
        // 保存答题记录
        this._currentSession.answers.push(answerRecord);
        this._currentSession.totalScore += score;
        
        // 提交答题到API
        try {
            await this._gameAPIManager.submitAnswer(
                currentQuestion.id,
                selectedAnswer,
                answerTime,
                this._audioManager.getPlayCount(currentQuestion.id)
            );
        } catch (error) {
            console.warn('[GameManager] 提交答题到API失败:', error);
            // 继续游戏流程，不阻塞用户体验
        }
        
        // 发送答题事件
        this._eventManager.emit('answer_submitted', {
            answerRecord: answerRecord,
            currentScore: this._currentSession.totalScore,
            currentCombo: this._currentCombo,
            isCorrect: isCorrect
        });
        
        console.log(`[GameManager] 答题结果: ${result}, 得分: ${score}, 连击: ${this._currentCombo}`);
        
        // 延迟进入下一题或结束游戏
        this.scheduleOnce(() => {
            this.nextQuestion();
        }, 1.5); // 1.5秒延迟显示结果
    }
    
    /**
     * 下一题
     */
    private nextQuestion(): void {
        if (!this._currentSession) return;
        
        this._currentSession.currentQuestionIndex++;
        
        if (this._currentSession.currentQuestionIndex >= this._currentSession.questions.length) {
            this.endGame();
        } else {
            this.startQuestion();
        }
    }
    
    /**
     * 结束游戏
     */
    private async endGame(): Promise<void> {
        if (!this._currentSession) return;
        
        console.log('[GameManager] 游戏结束');
        
        // 停止所有计时器
        this.stopAllTimers();
        
        // 设置结束时间
        this._currentSession.endTime = Date.now();
        
        // 设置游戏状态
        this.setGameState(GameState.RESULT);
        
        // 保存游戏数据到API和本地
        try {
            // 提交游戏结果到API
            await this._gameAPIManager.finishGame();
            
            // 保存到本地数据管理器
            await this._dataManager.saveGameSession(this._currentSession);
            await this._dataManager.updateUserStats(this._currentSession);
        } catch (error) {
            console.error('[GameManager] 保存游戏数据失败:', error);
            this.handleError(error);
        }
        
        // 切换到结果场景
        await this._sceneManager.loadScene('ResultScene');
        
        // 发送游戏结束事件
        this._eventManager.emit('game_ended', {
            session: this._currentSession,
            totalScore: this._currentSession.totalScore,
            correctCount: this._currentSession.correctCount,
            accuracy: this._currentSession.correctCount / this._currentSession.questions.length,
            maxCombo: this._maxCombo
        });
        
        // 重置统计数据
        this._currentCombo = 0;
        this._maxCombo = 0;
        this._totalGames++;
    }
    
    /**
     * 暂停游戏
     */
    public pauseGame(): void {
        if (this._gameState !== GameState.PLAYING || !this._currentSession) return;
        
        console.log('[GameManager] 暂停游戏');
        
        this._currentSession.isPaused = true;
        this.setGameState(GameState.PAUSED);
        this.stopQuestionTimer();
        
        // 暂停音频
        this._audioManager.pauseAll();
        
        // 发送暂停事件
        this._eventManager.emit('game_paused', {
            sessionId: this._currentSession.sessionId,
            currentQuestionIndex: this._currentSession.currentQuestionIndex
        });
    }
    
    /**
     * 恢复游戏
     */
    public resumeGame(): void {
        if (this._gameState !== GameState.PAUSED || !this._currentSession) return;
        
        console.log('[GameManager] 恢复游戏');
        
        this._currentSession.isPaused = false;
        this.setGameState(GameState.PLAYING);
        this.startQuestionTimer();
        
        // 发送恢复事件
        this._eventManager.emit('game_resumed', {
            sessionId: this._currentSession.sessionId,
            currentQuestionIndex: this._currentSession.currentQuestionIndex
        });
    }
    
    /**
     * 重新开始游戏
     */
    public async restartGame(): Promise<void> {
        console.log('[GameManager] 重新开始游戏');
        
        const difficulty = this._currentSession ? this._currentSession.difficulty : GameDifficulty.MEDIUM;
        this.cleanup();
        await this.startNewGame(difficulty);
    }
    
    /**
     * 退出游戏
     */
    public async quitGame(): Promise<void> {
        console.log('[GameManager] 退出游戏');
        
        this.cleanup();
        this.setGameState(GameState.MENU);
        await this._sceneManager.loadScene('MenuScene');
        
        this._eventManager.emit('game_quit', {
            sessionId: this._currentSession?.sessionId
        });
    }
    
    /**
     * 计算得分
     */
    private calculateScore(question: IQuestionData, answerTime: number, isCorrect: boolean): number {
        if (!isCorrect) return 0;
        
        let score = SCORE_CONFIG.BASE_SCORE[question.difficulty];
        
        // 时间加成
        if (answerTime <= SCORE_CONFIG.TIME_BONUS.THRESHOLD) {
            score *= SCORE_CONFIG.TIME_BONUS.MULTIPLIER;
        }
        
        // 连击加成
        if (this._currentCombo >= SCORE_CONFIG.COMBO_BONUS.MIN_COMBO) {
            score += (this._currentCombo - SCORE_CONFIG.COMBO_BONUS.MIN_COMBO + 1) * SCORE_CONFIG.COMBO_BONUS.BONUS_PER_COMBO;
        }
        
        return Math.floor(score);
    }
    
    /**
     * 启动答题计时器
     */
    private startQuestionTimer(): void {
        this.stopQuestionTimer();
        
        this._questionTimer = setTimeout(() => {
            this.onAnswerTimeout();
        }, GAME_RULES.ANSWER_TIME_LIMIT * 1000);
    }
    
    /**
     * 停止答题计时器
     */
    private stopQuestionTimer(): void {
        if (this._questionTimer) {
            clearTimeout(this._questionTimer);
            this._questionTimer = 0;
        }
    }
    
    /**
     * 停止所有计时器
     */
    private stopAllTimers(): void {
        this.stopQuestionTimer();
        if (this._gameTimer) {
            clearInterval(this._gameTimer);
            this._gameTimer = 0;
        }
    }
    
    /**
     * 生成会话ID
     */
    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 设置游戏状态
     */
    private setGameState(state: GameState): void {
        if (this._gameState === state) return;
        
        const previousState = this._gameState;
        this._gameState = state;
        
        console.log(`[GameManager] 游戏状态变更: ${previousState} -> ${state}`);
        
        this._eventManager.emit('game_state_changed', {
            previousState: previousState,
            currentState: state,
            timestamp: Date.now()
        });
    }
    
    /**
     * 获取当前题目
     */
    private getCurrentQuestion(): IQuestionData | null {
        if (!this._currentSession || 
            this._currentSession.currentQuestionIndex >= this._currentSession.questions.length) {
            return null;
        }
        
        return this._currentSession.questions[this._currentSession.currentQuestionIndex];
    }
    
    /**
     * 清理资源
     */
    private cleanup(): void {
        this.stopAllTimers();

        if (this._audioManager) {
            this._audioManager.stopAll();
        }

        // 清理事件监听器
        if (this._eventManager) {
            this._eventManager.targetOff(this);
        }

        // 执行全局内存清理
        if (this._memoryManager) {
            this._memoryManager.performGlobalCleanup();
        }

        this._currentSession = null;
    }
    
    /**
     * 初始化测试模式
     */
    private initializeTestMode(): void {
        try {
            console.log('%c[GameManager] 调试模式已启用', 'color: #00ff00; font-weight: bold;');
            
            // 导入TestUtils（动态导入避免打包到生产环境）
            import('../utils/TestUtils').then(({ TestUtils }) => {
                const testUtils = TestUtils.getInstance();
                console.log('%c[GameManager] 测试工具已加载，可通过window.testUtils访问', 'color: #00ff00;');
                
                // 暴露GameManager到全局用于调试
                if (typeof window !== 'undefined') {
                    (window as any).gameManager = this;
                }
            }).catch(error => {
                console.warn('[GameManager] 加载测试工具失败:', error);
            });
        } catch (error) {
            console.warn('[GameManager] 初始化测试模式失败:', error);
        }
    }

    /**
     * 用户登录成功处理
     */
    public async loginUser(): Promise<boolean> {
        try {
            const user = await this._gameAPIManager.login();
            console.log('[GameManager] 用户登录成功:', user.nickname);
            return true;
        } catch (error) {
            console.error('[GameManager] 用户登录失败:', error);
            this.handleError(error);
            return false;
        }
    }

    /**
     * 检查登录状态
     */
    public isUserLoggedIn(): boolean {
        return this._gameAPIManager.isLoggedIn();
    }

    /**
     * 获取当前用户信息
     */
    public getCurrentUser(): any {
        const gameAPIManager = this._managerRegistry?.getManager<GameAPIManager>('GameAPIManager');
        return gameAPIManager?.getCurrentUser() || null;
    }

    /**
     * 错误处理
     */
    private handleError(error: Error | string | unknown): void {
        console.error('[GameManager] 错误处理:', error);

        // 使用统一错误处理系统
        if (this._errorHandlingSystem) {
            this._errorHandlingSystem.handleError(error, { context: 'GameManager' });
        }

        // 发送错误事件
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit('game_error', {
                error: error,
                timestamp: Date.now(),
                context: 'GameManager'
            });
        }
    }
    
    // ========== 事件处理器 ==========
    
    private onAudioPlayComplete(event: IGameEvent): void {
        console.log('[GameManager] 音频播放完成:', event.data);
    }
    
    private onAudioPlayError(event: IGameEvent): void {
        console.error('[GameManager] 音频播放错误:', event.data);
        // TODO: 处理音频播放错误
    }
    
    private onAnswerSelected(event: IGameEvent): void {
        const { selectedAnswer } = event.data;
        this.submitAnswer(selectedAnswer);
    }
    
    private onAnswerTimeout(): void {
        console.log('[GameManager] 答题超时');
        
        if (this._gameState !== GameState.PLAYING || !this._currentSession) return;
        
        // 自动提交错误答案
        const currentQuestion = this.getCurrentQuestion();
        if (currentQuestion) {
            // 随机选择一个错误答案
            let wrongAnswer = currentQuestion.correctAnswer;
            while (wrongAnswer === currentQuestion.correctAnswer) {
                wrongAnswer = Math.floor(Math.random() * 4);
            }
            
            this.submitAnswer(wrongAnswer);
        }
    }
    
    private onSceneLoaded(event: IGameEvent): void {
        console.log('[GameManager] 场景加载完成:', event.data);
    }
    
    private onSceneLoadError(event: IGameEvent): void {
        console.error('[GameManager] 场景加载失败:', event.data);
        this.handleError(event.data);
    }
    
    // ========== API事件处理器 ==========
    
    private onUserLoginSuccess(data: any): void {
        console.log('[GameManager] 用户登录成功:', data);
        this._eventManager.emit('ui_show_toast', {
            message: `欢迎回来，${data.nickname}！`,
            type: 'success'
        });
    }
    
    private onUserLoginFailed(error: any): void {
        console.error('[GameManager] 用户登录失败:', error);
        this._eventManager.emit('ui_show_toast', {
            message: '登录失败，请重试',
            type: 'error'
        });
    }
    
    private onUserNeedLogin(): void {
        console.log('[GameManager] 需要用户登录');
        this._eventManager.emit('ui_show_login_dialog');
    }
    
    private onGameSessionStarted(session: IGameSession): void {
        console.log('[GameManager] 游戏会话已创建:', session.sessionId);
        // 会话创建成功，可以开始游戏
    }
    
    private onAnswerSubmittedToAPI(data: any): void {
        console.log('[GameManager] 答题结果已提交到API:', data);
        // 答题结果提交成功
    }
    
    private onGameFinishedToAPI(session: IGameSession): void {
        console.log('[GameManager] 游戏结果已提交到API:', session.sessionId);
        this._eventManager.emit('ui_show_toast', {
            message: '游戏结果已保存',
            type: 'info'
        });
    }
    
    // ========== 公共接口 ==========
    
    /**
     * 获取当前游戏状态
     */
    public getGameState(): GameState {
        return this._gameState;
    }
    
    /**
     * 获取当前游戏会话
     */
    public getCurrentSession(): IGameSession | null {
        return this._currentSession;
    }
    
    /**
     * 获取当前题目信息
     */
    public getCurrentQuestionInfo(): { question: IQuestionData | null, index: number, total: number } {
        return {
            question: this.getCurrentQuestion(),
            index: this._currentSession?.currentQuestionIndex ?? -1,
            total: this._currentSession?.questions.length ?? 0
        };
    }
    
    /**
     * 获取当前得分
     */
    public getCurrentScore(): number {
        return this._currentSession?.totalScore ?? 0;
    }
    
    /**
     * 获取当前连击数
     */
    public getCurrentCombo(): number {
        return this._currentCombo;
    }
    
    /**
     * 是否可以播放音频
     */
    public canPlayAudio(): boolean {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) return false;
        
        const playCount = this._audioManager.getPlayCount(currentQuestion.id);
        return playCount < GAME_RULES.MAX_AUDIO_PLAYS;
    }
    
    /**
     * 获取音频播放次数
     */
    public getAudioPlayCount(): number {
        const currentQuestion = this.getCurrentQuestion();
        if (!currentQuestion) return 0;
        
        return this._audioManager.getPlayCount(currentQuestion.id);
    }
}