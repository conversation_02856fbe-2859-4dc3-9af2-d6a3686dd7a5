import { _decorator, Component } from 'cc';
import { GameDifficulty } from '../constants/GameConstants';
import { IQuestionData, IGameSession, IUserProfile, IGameSettings, IGameStats } from '../data/GameData';
import { StorageManager } from '../utils/StorageManager';
import { NetworkManager } from '../utils/NetworkManager';

const { ccclass } = _decorator;

/**
 * 数据管理器
 * 负责游戏数据的获取、缓存、存储
 */
@ccclass('DataManager')
export class DataManager extends Component {
    private static _instance: DataManager = null;
    
    private _storageManager: StorageManager = null;
    private _networkManager: NetworkManager = null;
    
    // 题目数据缓存
    private _questionCache: Map<string, IQuestionData[]> = new Map();
    
    // 用户数据
    private _userProfile: IUserProfile = null;
    private _gameSettings: IGameSettings = null;
    
    public static get instance(): DataManager {
        return this._instance;
    }
    
    protected onLoad(): void {
        if (DataManager._instance === null) {
            DataManager._instance = this;
        }
    }
    
    protected onDestroy(): void {
        if (DataManager._instance === this) {
            DataManager._instance = null;
        }
    }
    
    /**
     * 初始化数据管理器
     */
    public async initialize(): Promise<void> {
        try {
            console.log('[DataManager] 初始化数据管理器');
            
            this._storageManager = new StorageManager();
            this._networkManager = new NetworkManager();
            
            // 加载用户数据
            await this.loadUserData();
            
            console.log('[DataManager] 数据管理器初始化完成');
        } catch (error) {
            console.error('[DataManager] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取随机题目
     */
    public async getRandomQuestions(count: number, difficulty: GameDifficulty): Promise<IQuestionData[]> {
        try {
            // 尝试从缓存获取
            const cacheKey = `${difficulty}_${count}`;
            if (this._questionCache.has(cacheKey)) {
                console.log(`[DataManager] 从缓存获取题目: ${cacheKey}`);
                return this._questionCache.get(cacheKey)!;
            }
            
            // 从网络获取
            const questions = await this._networkManager.getQuestions(count, difficulty);
            
            // 缓存题目数据
            this._questionCache.set(cacheKey, questions);
            
            console.log(`[DataManager] 获取题目成功: ${questions.length}题`);
            return questions;
            
        } catch (error) {
            console.error('[DataManager] 获取题目失败:', error);
            
            // 返回模拟数据作为后备
            return this.getMockQuestions(count, difficulty);
        }
    }
    
    /**
     * 保存游戏会话
     */
    public async saveGameSession(session: IGameSession): Promise<void> {
        try {
            const key = `game_session_${session.sessionId}`;
            await this._storageManager.setItem(key, session);
            console.log(`[DataManager] 游戏会话已保存: ${session.sessionId}`);
        } catch (error) {
            console.error('[DataManager] 保存游戏会话失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新用户统计
     */
    public async updateUserStats(session: IGameSession): Promise<void> {
        try {
            // 更新用户档案
            if (this._userProfile) {
                this._userProfile.totalGames++;
                this._userProfile.totalScore += session.totalScore;
                this._userProfile.bestScore = Math.max(this._userProfile.bestScore, session.totalScore);
                
                const accuracy = session.correctCount / session.questions.length;
                this._userProfile.bestAccuracy = Math.max(this._userProfile.bestAccuracy, accuracy);
                this._userProfile.lastPlayTime = new Date().toISOString();
                
                await this._storageManager.setItem('user_profile', this._userProfile);
            }
            
            console.log('[DataManager] 用户统计已更新');
        } catch (error) {
            console.error('[DataManager] 更新用户统计失败:', error);
        }
    }
    
    /**
     * 获取用户档案
     */
    public getUserProfile(): IUserProfile | null {
        return this._userProfile;
    }
    
    /**
     * 获取游戏设置
     */
    public getGameSettings(): IGameSettings | null {
        return this._gameSettings;
    }
    
    /**
     * 保存游戏设置
     */
    public async saveGameSettings(settings: IGameSettings): Promise<void> {
        try {
            this._gameSettings = settings;
            await this._storageManager.setItem('game_settings', settings);
            console.log('[DataManager] 游戏设置已保存');
        } catch (error) {
            console.error('[DataManager] 保存游戏设置失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载用户数据
     */
    private async loadUserData(): Promise<void> {
        try {
            // 加载用户档案
            this._userProfile = await this._storageManager.getItem('user_profile');
            if (!this._userProfile) {
                this._userProfile = this.createDefaultUserProfile();
                await this._storageManager.setItem('user_profile', this._userProfile);
            }
            
            // 加载游戏设置
            this._gameSettings = await this._storageManager.getItem('game_settings');
            if (!this._gameSettings) {
                this._gameSettings = this.createDefaultGameSettings();
                await this._storageManager.setItem('game_settings', this._gameSettings);
            }
            
            console.log('[DataManager] 用户数据加载完成');
        } catch (error) {
            console.error('[DataManager] 加载用户数据失败:', error);
        }
    }
    
    /**
     * 创建默认用户档案
     */
    private createDefaultUserProfile(): IUserProfile {
        return {
            userId: `user_${Date.now()}`,
            nickname: '游客',
            avatar: '',
            level: 1,
            experience: 0,
            totalGames: 0,
            totalScore: 0,
            bestScore: 0,
            bestAccuracy: 0,
            favoriteDialects: [],
            achievements: [],
            createdAt: new Date().toISOString(),
            lastPlayTime: new Date().toISOString()
        };
    }
    
    /**
     * 创建默认游戏设置
     */
    private createDefaultGameSettings(): IGameSettings {
        return {
            soundEnabled: true,
            musicEnabled: true,
            soundVolume: 1.0,
            musicVolume: 0.8,
            autoPlayAudio: true,
            showHints: true,
            animationSpeed: 1.0,
            language: 'zh-CN',
            theme: 'default',
            difficulty: GameDifficulty.MEDIUM
        };
    }
    
    /**
     * 获取模拟题目数据
     */
    private getMockQuestions(count: number, difficulty: GameDifficulty): IQuestionData[] {
        const mockQuestions: IQuestionData[] = [];
        
        for (let i = 0; i < count; i++) {
            mockQuestions.push({
                id: `mock_${i + 1}`,
                audioUrl: `mock_audio_${i + 1}.mp3`,
                dialect: '四川话',
                region: '成都',
                difficulty: difficulty,
                question: `这句话是什么意思？（题目${i + 1}）`,
                options: [
                    '选项A - 正确答案',
                    '选项B - 错误答案',
                    '选项C - 错误答案',
                    '选项D - 错误答案'
                ],
                correctAnswer: 0,
                explanation: '这是一句四川话，表示...',
                audioDuration: 5,
                tags: ['日常用语'],
                category: '基础',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
        
        console.log(`[DataManager] 返回模拟题目数据: ${mockQuestions.length}题`);
        return mockQuestions;
    }
}