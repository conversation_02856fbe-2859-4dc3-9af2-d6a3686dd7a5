import { GameDifficulty, STORAGE_KEYS } from '../constants/GameConstants';
import { IQuestionData, IGameSession, IUserProfile, IAnswerRecord } from '../data/GameData';
import { NetworkManager } from '../utils/NetworkManager';
import { StorageManager } from '../utils/StorageManager';
import { EventManager } from './EventManager';

/**
 * 游戏API管理器
 * 负责游戏相关的业务逻辑和API调用
 */
export class GameAPIManager {
    private static _instance: GameAPIManager;
    private _networkManager: NetworkManager;
    private _currentSession: IGameSession | null = null;
    private _isInitialized: boolean = false;

    public static getInstance(): GameAPIManager {
        if (!GameAPIManager._instance) {
            GameAPIManager._instance = new GameAPIManager();
        }
        return GameAPIManager._instance;
    }

    private constructor() {
        this._networkManager = NetworkManager.getInstance();
    }

    /**
     * 初始化游戏API管理器
     */
    public async initialize(): Promise<void> {
        if (this._isInitialized) {
            return;
        }

        try {
            // 检查网络状态
            const networkStatus = await this._networkManager.checkNetworkStatus();
            if (!networkStatus.isOnline) {
                console.warn('[GameAPIManager] 网络不可用，启用离线模式');
                this._isInitialized = true;
                return;
            }

            // 检查服务器连接
            const serverReachable = await this._networkManager.pingServer();
            if (!serverReachable) {
                console.warn('[GameAPIManager] 服务器不可达，启用离线模式');
                this._isInitialized = true;
                return;
            }

            // 尝试自动登录
            await this.tryAutoLogin();

            this._isInitialized = true;
            console.log('[GameAPIManager] 初始化完成');
        } catch (error) {
            console.error('[GameAPIManager] 初始化失败:', error);
            this._isInitialized = true; // 即使失败也标记为已初始化，避免重复初始化
        }
    }

    /**
     * 尝试自动登录
     */
    private async tryAutoLogin(): Promise<void> {
        try {
            if (this._networkManager.isLoggedIn()) {
                // 验证当前用户信息
                const user = await this._networkManager.getCurrentUser();
                console.log('[GameAPIManager] 自动登录成功:', user.nickname);
                
                // 触发登录成功事件
                EventManager.getInstance().emit('user-login-success', user);
            } else {
                console.log('[GameAPIManager] 需要手动登录');
                // 触发需要登录事件
                EventManager.getInstance().emit('user-need-login');
            }
        } catch (error) {
            console.warn('[GameAPIManager] 自动登录失败:', error);
            // 触发需要登录事件
            EventManager.getInstance().emit('user-need-login');
        }
    }

    /**
     * 用户登录
     */
    public async login(): Promise<IUserProfile> {
        try {
            console.log('[GameAPIManager] 开始微信登录...');
            const user = await this._networkManager.wechatLogin();
            
            // 触发登录成功事件
            EventManager.getInstance().emit('user-login-success', user);
            
            return user;
        } catch (error) {
            console.error('[GameAPIManager] 登录失败:', error);
            // 触发登录失败事件
            EventManager.getInstance().emit('user-login-failed', error);
            throw error;
        }
    }

    /**
     * 用户登出
     */
    public logout(): void {
        this._networkManager.logout();
        this._currentSession = null;
        
        // 触发登出事件
        EventManager.getInstance().emit('user-logout');
        console.log('[GameAPIManager] 用户已登出');
    }

    /**
     * 检查登录状态
     */
    public isLoggedIn(): boolean {
        return this._networkManager.isLoggedIn();
    }

    /**
     * 获取当前用户信息
     */
    public getCurrentUser(): IUserProfile | null {
        try {
            return StorageManager.getItem(STORAGE_KEYS.USER_PROFILE);
        } catch (error) {
            console.error('[GameAPIManager] 获取用户信息失败:', error);
            return null;
        }
    }

    /**
     * 开始新游戏
     */
    public async startNewGame(difficulty: GameDifficulty, questionCount: number = 10): Promise<IGameSession> {
        try {
            console.log(`[GameAPIManager] 开始新游戏 - 难度: ${difficulty}, 题目数: ${questionCount}`);

            // 创建游戏会话
            let sessionId: string;
            try {
                sessionId = await this._networkManager.createGameSession(difficulty);
            } catch (error) {
                console.warn('[GameAPIManager] 创建在线会话失败，使用离线模式:', error);
                sessionId = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }

            // 获取题目数据
            const questions = await this.getQuestions(questionCount, difficulty);

            // 创建游戏会话对象
            this._currentSession = {
                sessionId,
                startTime: Date.now(),
                questions,
                answers: [],
                totalScore: 0,
                correctCount: 0,
                comboCount: 0,
                currentCombo: 0,
                currentQuestionIndex: 0,
                isPaused: false,
                pauseTime: 0,
                gameMode: 'standard',
                difficulty
            };

            // 保存到本地存储
            StorageManager.setItem(STORAGE_KEYS.GAME_PROGRESS, this._currentSession);

            // 发送游戏开始事件
            EventManager.getInstance().emit('game-session-started', this._currentSession);

            console.log('[GameAPIManager] 新游戏创建成功:', sessionId);
            return this._currentSession;
        } catch (error) {
            console.error('[GameAPIManager] 开始新游戏失败:', error);
            throw error;
        }
    }

    /**
     * 获取题目数据
     */
    public async getQuestions(count: number, difficulty: GameDifficulty): Promise<IQuestionData[]> {
        try {
            // 优先从网络获取
            const questions = await this._networkManager.getQuestions(count, difficulty);
            console.log(`[GameAPIManager] 从服务器获取 ${questions.length} 道题目`);
            return questions;
        } catch (error) {
            console.warn('[GameAPIManager] 从服务器获取题目失败，使用本地数据:', error);
            
            // 从本地获取备用题目
            const localQuestions = this.getLocalQuestions(count, difficulty);
            if (localQuestions.length > 0) {
                console.log(`[GameAPIManager] 使用本地题目 ${localQuestions.length} 道`);
                return localQuestions;
            } else {
                throw new Error('无法获取题目数据，请检查网络连接');
            }
        }
    }

    /**
     * 获取本地备用题目（用于离线模式）
     */
    private getLocalQuestions(count: number, difficulty: GameDifficulty): IQuestionData[] {
        // 这里应该返回预置的题目数据，暂时返回空数组
        // 实际项目中可以预置一些题目数据作为离线备用
        console.warn('[GameAPIManager] 本地题目数据未实现，返回空数组');
        return [];
    }

    /**
     * 提交答题
     */
    public async submitAnswer(questionId: string, selectedAnswer: number, answerTime: number, audioPlayCount: number): Promise<void> {
        if (!this._currentSession) {
            throw new Error('没有进行中的游戏会话');
        }

        const currentQuestion = this._currentSession.questions[this._currentSession.currentQuestionIndex];
        if (!currentQuestion || currentQuestion.id !== questionId) {
            throw new Error('题目ID不匹配');
        }

        // 计算得分和结果
        const isCorrect = selectedAnswer === currentQuestion.correctAnswer;
        const result = isCorrect ? 'correct' : 'wrong';
        const score = this.calculateScore(currentQuestion, answerTime, isCorrect);

        // 更新连击数
        if (isCorrect) {
            this._currentSession.currentCombo++;
            this._currentSession.comboCount = Math.max(this._currentSession.comboCount, this._currentSession.currentCombo);
            this._currentSession.correctCount++;
        } else {
            this._currentSession.currentCombo = 0;
        }

        // 创建答题记录
        const answerRecord: IAnswerRecord = {
            questionId,
            selectedAnswer,
            correctAnswer: currentQuestion.correctAnswer,
            result: result as any,
            answerTime,
            score,
            audioPlayCount,
            timestamp: Date.now()
        };

        // 添加到答题记录
        this._currentSession.answers.push(answerRecord);
        this._currentSession.totalScore += score;
        this._currentSession.currentQuestionIndex++;

        // 更新本地存储
        StorageManager.setItem(STORAGE_KEYS.GAME_PROGRESS, this._currentSession);

        // 发送答题完成事件
        EventManager.getInstance().emit('answer-submitted', {
            answerRecord,
            currentSession: this._currentSession
        });

        console.log(`[GameAPIManager] 答题提交完成 - 选择: ${selectedAnswer}, 正确: ${currentQuestion.correctAnswer}, 得分: ${score}`);
    }

    /**
     * 计算得分
     */
    private calculateScore(question: IQuestionData, answerTime: number, isCorrect: boolean): number {
        if (!isCorrect) {
            return 0;
        }

        // 基础分数
        const baseScore = question.difficulty === GameDifficulty.EASY ? 10 : 
                         question.difficulty === GameDifficulty.MEDIUM ? 20 : 30;

        // 时间加成（10秒内答题有1.5倍加成）
        const timeBonus = answerTime <= 10000 ? 1.5 : 1.0;

        // 连击加成
        const comboBonus = this._currentSession!.currentCombo >= 3 ? 
                          1 + (this._currentSession!.currentCombo - 2) * 0.1 : 1.0;

        const finalScore = Math.floor(baseScore * timeBonus * comboBonus);
        
        console.log(`[GameAPIManager] 分数计算 - 基础: ${baseScore}, 时间: x${timeBonus}, 连击: x${comboBonus.toFixed(1)}, 最终: ${finalScore}`);
        
        return finalScore;
    }

    /**
     * 完成游戏
     */
    public async finishGame(): Promise<void> {
        if (!this._currentSession) {
            throw new Error('没有进行中的游戏会话');
        }

        try {
            // 设置结束时间
            this._currentSession.endTime = Date.now();

            // 尝试提交到服务器
            try {
                const success = await this._networkManager.submitGameResult(
                    this._currentSession.sessionId,
                    this._currentSession
                );
                
                if (success) {
                    console.log('[GameAPIManager] 游戏结果提交到服务器成功');
                } else {
                    console.warn('[GameAPIManager] 游戏结果提交到服务器失败，数据已保存在本地');
                }
            } catch (error) {
                console.warn('[GameAPIManager] 提交游戏结果失败:', error);
            }

            // 保存到本地游戏历史
            this.saveGameToHistory(this._currentSession);

            // 发送游戏完成事件
            EventManager.getInstance().emit('game-finished', this._currentSession);

            console.log(`[GameAPIManager] 游戏完成 - 总分: ${this._currentSession.totalScore}, 正确率: ${(this._currentSession.correctCount / this._currentSession.questions.length * 100).toFixed(1)}%`);

            // 清理当前会话
            const completedSession = this._currentSession;
            this._currentSession = null;
            StorageManager.removeItem(STORAGE_KEYS.GAME_PROGRESS);

            return completedSession;
        } catch (error) {
            console.error('[GameAPIManager] 完成游戏失败:', error);
            throw error;
        }
    }

    /**
     * 保存游戏到历史记录
     */
    private saveGameToHistory(session: IGameSession): void {
        try {
            let history = StorageManager.getItem('game_history') || [];
            
            // 保留最近50场游戏记录
            if (history.length >= 50) {
                history = history.slice(-49);
            }
            
            history.push({
                sessionId: session.sessionId,
                startTime: session.startTime,
                endTime: session.endTime,
                totalScore: session.totalScore,
                correctCount: session.correctCount,
                questionCount: session.questions.length,
                accuracy: session.correctCount / session.questions.length,
                difficulty: session.difficulty,
                comboCount: session.comboCount
            });
            
            StorageManager.setItem('game_history', history);
            console.log('[GameAPIManager] 游戏记录已保存到历史');
        } catch (error) {
            console.error('[GameAPIManager] 保存游戏历史失败:', error);
        }
    }

    /**
     * 获取当前游戏会话
     */
    public getCurrentSession(): IGameSession | null {
        return this._currentSession;
    }

    /**
     * 恢复游戏会话（用于应用重启后继续游戏）
     */
    public restoreGameSession(): IGameSession | null {
        try {
            const savedSession = StorageManager.getItem(STORAGE_KEYS.GAME_PROGRESS);
            if (savedSession) {
                this._currentSession = savedSession;
                console.log('[GameAPIManager] 游戏会话已恢复:', savedSession.sessionId);
                return savedSession;
            }
        } catch (error) {
            console.error('[GameAPIManager] 恢复游戏会话失败:', error);
        }
        return null;
    }

    /**
     * 暂停游戏
     */
    public pauseGame(): void {
        if (this._currentSession && !this._currentSession.isPaused) {
            this._currentSession.isPaused = true;
            StorageManager.setItem(STORAGE_KEYS.GAME_PROGRESS, this._currentSession);
            
            EventManager.getInstance().emit('game-paused', this._currentSession);
            console.log('[GameAPIManager] 游戏已暂停');
        }
    }

    /**
     * 恢复游戏
     */
    public resumeGame(): void {
        if (this._currentSession && this._currentSession.isPaused) {
            this._currentSession.isPaused = false;
            StorageManager.setItem(STORAGE_KEYS.GAME_PROGRESS, this._currentSession);
            
            EventManager.getInstance().emit('game-resumed', this._currentSession);
            console.log('[GameAPIManager] 游戏已恢复');
        }
    }

    /**
     * 获取用户统计数据
     */
    public async getUserStats(): Promise<any> {
        try {
            const user = this.getCurrentUser();
            if (!user) {
                throw new Error('用户未登录');
            }

            const stats = await this._networkManager.getUserStats(user.userId);
            console.log('[GameAPIManager] 获取用户统计成功');
            return stats;
        } catch (error) {
            console.warn('[GameAPIManager] 获取在线统计失败，使用本地数据:', error);
            
            // 返回本地统计数据
            return this.getLocalUserStats();
        }
    }

    /**
     * 获取本地用户统计
     */
    private getLocalUserStats(): any {
        try {
            const history = StorageManager.getItem('game_history') || [];
            
            if (history.length === 0) {
                return {
                    totalGames: 0,
                    totalScore: 0,
                    averageScore: 0,
                    accuracy: 0,
                    bestScore: 0,
                    bestCombo: 0
                };
            }

            const totalGames = history.length;
            const totalScore = history.reduce((sum, game) => sum + game.totalScore, 0);
            const averageScore = Math.floor(totalScore / totalGames);
            const totalCorrect = history.reduce((sum, game) => sum + game.correctCount, 0);
            const totalQuestions = history.reduce((sum, game) => sum + game.questionCount, 0);
            const accuracy = totalQuestions > 0 ? totalCorrect / totalQuestions : 0;
            const bestScore = Math.max(...history.map(game => game.totalScore));
            const bestCombo = Math.max(...history.map(game => game.comboCount));

            return {
                totalGames,
                totalScore,
                averageScore,
                accuracy: Math.round(accuracy * 100) / 100,
                bestScore,
                bestCombo
            };
        } catch (error) {
            console.error('[GameAPIManager] 获取本地统计失败:', error);
            return {
                totalGames: 0,
                totalScore: 0,
                averageScore: 0,
                accuracy: 0,
                bestScore: 0,
                bestCombo: 0
            };
        }
    }

    /**
     * 检查初始化状态
     */
    public isInitialized(): boolean {
        return this._isInitialized;
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        this._currentSession = null;
        this._isInitialized = false;
        console.log('[GameAPIManager] 资源已清理');
    }
}