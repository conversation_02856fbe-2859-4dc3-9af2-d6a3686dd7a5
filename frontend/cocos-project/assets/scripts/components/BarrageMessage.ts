/**
 * 弹幕消息组件
 * 
 * 单条弹幕消息的显示和动画控制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, Sprite, RichText, tween, Vec3, Color, UITransform } from 'cc';
import { BarrageMessage as BarrageMessageData, BarrageMessageType } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 弹幕动画状态 */
export enum BarrageAnimationState {
    IDLE = 'idle',
    ENTERING = 'entering',
    SCROLLING = 'scrolling',
    EXITING = 'exiting',
    PAUSED = 'paused'
}

@ccclass('BarrageMessage')
export class BarrageMessage extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    messageContainer: Node = null;
    
    @property(RichText)
    contentLabel: RichText = null;
    
    @property(Label)
    nicknameLabel: Label = null;
    
    @property(Sprite)
    avatarSprite: Sprite = null;
    
    @property(Sprite)
    backgroundSprite: Sprite = null;
    
    @property(Node)
    typeIndicator: Node = null;
    
    @property(Node)
    vipIndicator: Node = null;
    
    // ==================== 私有属性 ====================
    
    private _messageData: BarrageMessageData = null;
    private _animationState: BarrageAnimationState = BarrageAnimationState.IDLE;
    
    // 动画相关
    private _scrollTween: any = null;
    private _scrollSpeed: number = 100; // 像素/秒
    private _containerWidth: number = 0;
    private _messageWidth: number = 0;
    
    // 样式配置
    private readonly TYPE_COLORS = {
        [BarrageMessageType.NORMAL]: new Color(255, 255, 255), // 白色
        [BarrageMessageType.PREDICTION]: new Color(255, 215, 0), // 金色
        [BarrageMessageType.SYSTEM]: new Color(255, 87, 34), // 橙色
        [BarrageMessageType.VIP]: new Color(255, 20, 147), // 深粉色
        [BarrageMessageType.GIFT]: new Color(255, 69, 0) // 红橙色
    };
    
    private readonly BACKGROUND_COLORS = {
        [BarrageMessageType.NORMAL]: new Color(0, 0, 0, 128), // 半透明黑色
        [BarrageMessageType.PREDICTION]: new Color(255, 215, 0, 64), // 半透明金色
        [BarrageMessageType.SYSTEM]: new Color(255, 87, 34, 64), // 半透明橙色
        [BarrageMessageType.VIP]: new Color(255, 20, 147, 64), // 半透明深粉色
        [BarrageMessageType.GIFT]: new Color(255, 69, 0, 64) // 半透明红橙色
    };

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeComponents();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 设置初始状态
        if (this.messageContainer) {
            this.messageContainer.setScale(1, 1, 1);
        }
        
        // 隐藏VIP指示器
        if (this.vipIndicator) {
            this.vipIndicator.active = false;
        }
        
        // 隐藏类型指示器
        if (this.typeIndicator) {
            this.typeIndicator.active = false;
        }
    }

    // ==================== 公共方法 ====================
    
    /** 设置弹幕消息数据 */
    public setMessageData(data: BarrageMessageData): void {
        this._messageData = data;
        this.updateDisplay();
        this.updateStyle();
    }
    
    /** 开始滚动动画 */
    public startScrolling(containerWidth: number, duration?: number): void {
        if (!this._messageData || this._animationState === BarrageAnimationState.SCROLLING) {
            return;
        }
        
        this._containerWidth = containerWidth;
        this._messageWidth = this.getMessageWidth();
        
        // 计算滚动距离和时间
        const scrollDistance = containerWidth + this._messageWidth;
        const scrollDuration = duration || (scrollDistance / this._scrollSpeed);
        
        // 设置初始位置（右侧屏幕外）
        this.node.setPosition(containerWidth / 2 + this._messageWidth / 2, this.node.position.y, 0);
        
        // 开始滚动动画
        this._animationState = BarrageAnimationState.SCROLLING;
        
        this._scrollTween = tween(this.node)
            .to(scrollDuration, { 
                position: new Vec3(-containerWidth / 2 - this._messageWidth / 2, this.node.position.y, 0) 
            })
            .call(() => {
                this.onScrollComplete();
            })
            .start();
    }
    
    /** 暂停滚动 */
    public pauseScrolling(): void {
        if (this._scrollTween && this._animationState === BarrageAnimationState.SCROLLING) {
            this._scrollTween.stop();
            this._animationState = BarrageAnimationState.PAUSED;
        }
    }
    
    /** 恢复滚动 */
    public resumeScrolling(): void {
        if (this._animationState === BarrageAnimationState.PAUSED) {
            // 计算剩余滚动距离和时间
            const currentX = this.node.position.x;
            const targetX = -this._containerWidth / 2 - this._messageWidth / 2;
            const remainingDistance = currentX - targetX;
            const remainingDuration = remainingDistance / this._scrollSpeed;
            
            if (remainingDuration > 0) {
                this._animationState = BarrageAnimationState.SCROLLING;
                
                this._scrollTween = tween(this.node)
                    .to(remainingDuration, { 
                        position: new Vec3(targetX, this.node.position.y, 0) 
                    })
                    .call(() => {
                        this.onScrollComplete();
                    })
                    .start();
            } else {
                this.onScrollComplete();
            }
        }
    }
    
    /** 停止滚动 */
    public stopScrolling(): void {
        if (this._scrollTween) {
            this._scrollTween.stop();
            this._scrollTween = null;
        }
        this._animationState = BarrageAnimationState.IDLE;
    }
    
    /** 播放进入动画 */
    public playEnterAnimation(): void {
        if (!this.messageContainer) return;
        
        this._animationState = BarrageAnimationState.ENTERING;
        
        // 缩放进入动画
        this.messageContainer.setScale(0, 0, 1);
        tween(this.messageContainer)
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .call(() => {
                this._animationState = BarrageAnimationState.IDLE;
            })
            .start();
    }
    
    /** 播放退出动画 */
    public playExitAnimation(callback?: Function): void {
        if (!this.messageContainer) {
            if (callback) callback();
            return;
        }
        
        this._animationState = BarrageAnimationState.EXITING;
        
        // 淡出动画
        tween(this.messageContainer)
            .to(0.3, { scale: new Vec3(0, 0, 1) })
            .call(() => {
                this._animationState = BarrageAnimationState.IDLE;
                if (callback) callback();
            })
            .start();
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示内容 */
    private updateDisplay(): void {
        if (!this._messageData) return;
        
        // 更新内容
        if (this.contentLabel) {
            this.contentLabel.string = this.formatMessageContent(this._messageData.content);
        }
        
        // 更新昵称
        if (this.nicknameLabel) {
            this.nicknameLabel.string = this._messageData.nickname;
        }
        
        // 更新VIP指示器
        if (this.vipIndicator) {
            this.vipIndicator.active = this._messageData.isVip;
        }
        
        // 更新类型指示器
        this.updateTypeIndicator();
        
        // 加载头像
        this.loadAvatar(this._messageData.avatar);
    }
    
    /** 更新样式 */
    private updateStyle(): void {
        if (!this._messageData) return;
        
        const messageType = this._messageData.type;
        
        // 更新文本颜色
        if (this.contentLabel) {
            const color = this.TYPE_COLORS[messageType] || this.TYPE_COLORS[BarrageMessageType.NORMAL];
            this.contentLabel.node.color = color;
        }
        
        // 更新背景颜色
        if (this.backgroundSprite) {
            const bgColor = this.BACKGROUND_COLORS[messageType] || this.BACKGROUND_COLORS[BarrageMessageType.NORMAL];
            this.backgroundSprite.color = bgColor;
        }
        
        // 更新昵称颜色
        if (this.nicknameLabel) {
            if (this._messageData.isVip) {
                this.nicknameLabel.color = new Color(255, 215, 0); // 金色
            } else {
                this.nicknameLabel.color = new Color(255, 255, 255); // 白色
            }
        }
    }
    
    /** 更新类型指示器 */
    private updateTypeIndicator(): void {
        if (!this.typeIndicator || !this._messageData) return;
        
        const messageType = this._messageData.type;
        
        // 只有特殊类型才显示指示器
        if (messageType === BarrageMessageType.NORMAL) {
            this.typeIndicator.active = false;
            return;
        }
        
        this.typeIndicator.active = true;
        
        // 设置指示器颜色
        const sprite = this.typeIndicator.getComponent(Sprite);
        if (sprite) {
            const color = this.TYPE_COLORS[messageType];
            sprite.color = color;
        }
        
        // 设置指示器文本
        const label = this.typeIndicator.getComponentInChildren(Label);
        if (label) {
            switch (messageType) {
                case BarrageMessageType.PREDICTION:
                    label.string = '预测';
                    break;
                case BarrageMessageType.SYSTEM:
                    label.string = '系统';
                    break;
                case BarrageMessageType.VIP:
                    label.string = 'VIP';
                    break;
                case BarrageMessageType.GIFT:
                    label.string = '礼物';
                    break;
            }
        }
    }
    
    /** 格式化消息内容 */
    private formatMessageContent(content: string): string {
        if (!this._messageData) return content;
        
        // 根据消息类型添加特殊格式
        switch (this._messageData.type) {
            case BarrageMessageType.PREDICTION:
                return `<color=#FFD700>[预测]</color> ${content}`;
            case BarrageMessageType.SYSTEM:
                return `<color=#FF5722>[系统]</color> ${content}`;
            case BarrageMessageType.GIFT:
                return `<color=#FF4500>[礼物]</color> ${content}`;
            default:
                return content;
        }
    }
    
    /** 加载头像 */
    private loadAvatar(avatarUrl: string): void {
        if (!this.avatarSprite || !avatarUrl) return;
        
        // 这里应该使用Cocos Creator的资源加载系统
        // 简化处理，实际项目中需要实现网络图片加载
        console.log('Loading avatar:', avatarUrl);
    }
    
    /** 获取消息宽度 */
    private getMessageWidth(): number {
        if (!this.messageContainer) return 0;
        
        const transform = this.messageContainer.getComponent(UITransform);
        return transform ? transform.width : 0;
    }

    // ==================== 事件处理 ====================
    
    /** 滚动完成处理 */
    private onScrollComplete(): void {
        this._animationState = BarrageAnimationState.IDLE;
        
        // 通知父组件消息滚动完成
        this.node.emit('barrage-scroll-complete', this);
    }
    
    /** 消息点击处理 */
    public onMessageClick(): void {
        if (!this._messageData) return;
        
        // 暂停滚动
        this.pauseScrolling();
        
        // 播放点击动画
        this.playClickAnimation();
        
        // 触发点击事件
        this.node.emit('barrage-message-click', this._messageData);
        
        // 延迟恢复滚动
        this.scheduleOnce(() => {
            this.resumeScrolling();
        }, 2.0);
    }
    
    /** 播放点击动画 */
    private playClickAnimation(): void {
        if (!this.messageContainer) return;
        
        // 高亮动画
        const originalColor = this.backgroundSprite?.color || new Color(255, 255, 255);
        const highlightColor = new Color(255, 255, 255, 200);
        
        if (this.backgroundSprite) {
            tween(this.backgroundSprite)
                .to(0.2, { color: highlightColor })
                .to(0.2, { color: originalColor })
                .start();
        }
    }

    // ==================== 工具方法 ====================
    
    /** 获取消息数据 */
    public getMessageData(): BarrageMessageData {
        return this._messageData;
    }
    
    /** 获取动画状态 */
    public getAnimationState(): BarrageAnimationState {
        return this._animationState;
    }
    
    /** 检查是否在滚动 */
    public isScrolling(): boolean {
        return this._animationState === BarrageAnimationState.SCROLLING;
    }
    
    /** 检查是否已暂停 */
    public isPaused(): boolean {
        return this._animationState === BarrageAnimationState.PAUSED;
    }
    
    /** 设置滚动速度 */
    public setScrollSpeed(speed: number): void {
        this._scrollSpeed = Math.max(50, Math.min(300, speed)); // 限制在50-300之间
    }
    
    /** 获取消息类型 */
    public getMessageType(): BarrageMessageType {
        return this._messageData?.type || BarrageMessageType.NORMAL;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有动画
        this.stopScrolling();
        
        // 清理其他资源
        this._messageData = null;
    }
}
