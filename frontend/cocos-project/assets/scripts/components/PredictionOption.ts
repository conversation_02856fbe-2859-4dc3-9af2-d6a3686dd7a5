/**
 * 预测选项组件
 * 
 * 管理单个预测选项的显示、交互和状态更新
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Button, Label, Sprite, ProgressBar, tween, Vec3, Color } from 'cc';
import { PredictionOptionData, PredictionOptionProps } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 选项状态枚举 */
export enum OptionState {
    NORMAL = 'normal',
    SELECTED = 'selected',
    DISABLED = 'disabled',
    CORRECT = 'correct',
    INCORRECT = 'incorrect'
}

@ccclass('PredictionOption')
export class PredictionOption extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    optionContainer: Node = null;
    
    @property(Button)
    optionButton: Button = null;
    
    @property(Label)
    optionLabel: Label = null;
    
    @property(Label)
    percentageLabel: Label = null;
    
    @property(Label)
    voteCountLabel: Label = null;
    
    @property(ProgressBar)
    voteProgressBar: ProgressBar = null;
    
    @property(Sprite)
    optionIcon: Sprite = null;
    
    @property(Node)
    selectionIndicator: Node = null;
    
    @property(Node)
    resultIndicator: Node = null;
    
    // ==================== 私有属性 ====================
    
    /** 选项数据 */
    private _optionData: PredictionOptionData = null;
    
    /** 配置属性 */
    private _props: PredictionOptionProps = null;
    
    /** 当前状态 */
    private _currentState: OptionState = OptionState.NORMAL;
    
    /** 是否已选择 */
    private _isSelected: boolean = false;
    
    /** 投票百分比 */
    private _votePercentage: number = 0;
    
    /** 投票数量 */
    private _voteCount: number = 0;
    
    /** 总投票数 */
    private _totalVotes: number = 0;
    
    /** 动画相关 */
    private _progressAnimation: any = null;
    private _selectionAnimation: any = null;

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.setupEventListeners();
        this.initializeComponents();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        if (this.optionButton) {
            this.optionButton.node.on(Button.EventType.CLICK, this.onOptionClick, this);
        }
    }
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏选择指示器
        if (this.selectionIndicator) {
            this.selectionIndicator.active = false;
        }
        
        // 隐藏结果指示器
        if (this.resultIndicator) {
            this.resultIndicator.active = false;
        }
        
        // 初始化进度条
        if (this.voteProgressBar) {
            this.voteProgressBar.progress = 0;
        }
        
        // 设置初始状态
        this.setState(OptionState.NORMAL);
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: PredictionOptionProps): void {
        this._props = props;
    }
    
    /** 设置选项数据 */
    public setOptionData(data: PredictionOptionData): void {
        this._optionData = data;
        this.updateDisplay();
    }
    
    /** 更新投票数据 */
    public updateVoteData(voteCount: number, totalVotes: number): void {
        this._voteCount = voteCount;
        this._totalVotes = totalVotes;
        this._votePercentage = totalVotes > 0 ? (voteCount / totalVotes) * 100 : 0;
        
        this.updateVoteDisplay();
        this.animateProgress();
    }
    
    /** 设置选择状态 */
    public setSelected(selected: boolean): void {
        if (this._isSelected === selected) return;
        
        this._isSelected = selected;
        
        if (selected) {
            this.setState(OptionState.SELECTED);
            this.showSelectionIndicator();
        } else {
            this.setState(OptionState.NORMAL);
            this.hideSelectionIndicator();
        }
        
        // 播放选择动画
        this.playSelectionAnimation();
    }
    
    /** 设置状态 */
    public setState(state: OptionState): void {
        if (this._currentState === state) return;
        
        this._currentState = state;
        this.updateStateDisplay();
    }
    
    /** 设置为正确答案 */
    public setCorrect(isCorrect: boolean): void {
        if (isCorrect) {
            this.setState(OptionState.CORRECT);
            this.showResultIndicator(true);
        } else {
            this.setState(OptionState.INCORRECT);
            this.showResultIndicator(false);
        }
    }
    
    /** 启用/禁用选项 */
    public setEnabled(enabled: boolean): void {
        if (this.optionButton) {
            this.optionButton.interactable = enabled;
        }
        
        if (!enabled) {
            this.setState(OptionState.DISABLED);
        } else if (this._currentState === OptionState.DISABLED) {
            this.setState(this._isSelected ? OptionState.SELECTED : OptionState.NORMAL);
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示 */
    private updateDisplay(): void {
        if (!this._optionData) return;
        
        // 更新选项文本
        if (this.optionLabel) {
            this.optionLabel.string = this._optionData.text;
        }
        
        // 更新图标
        if (this.optionIcon && this._optionData.icon) {
            // 实际项目中需要加载图标资源
            console.log('Loading option icon:', this._optionData.icon);
        }
        
        // 更新投票显示
        this.updateVoteDisplay();
    }
    
    /** 更新投票显示 */
    private updateVoteDisplay(): void {
        // 更新百分比标签
        if (this.percentageLabel) {
            this.percentageLabel.string = `${this._votePercentage.toFixed(1)}%`;
        }
        
        // 更新投票数标签
        if (this.voteCountLabel) {
            this.voteCountLabel.string = this.formatVoteCount(this._voteCount);
        }
        
        // 更新进度条
        if (this.voteProgressBar) {
            this.voteProgressBar.progress = this._votePercentage / 100;
        }
    }
    
    /** 更新状态显示 */
    private updateStateDisplay(): void {
        if (!this.optionContainer) return;
        
        let color: Color;
        let scale = 1;
        
        switch (this._currentState) {
            case OptionState.NORMAL:
                color = new Color(255, 255, 255, 255);
                break;
                
            case OptionState.SELECTED:
                color = new Color(100, 149, 237, 255); // 蓝色
                scale = 1.05;
                break;
                
            case OptionState.DISABLED:
                color = new Color(128, 128, 128, 128); // 灰色半透明
                break;
                
            case OptionState.CORRECT:
                color = new Color(34, 139, 34, 255); // 绿色
                scale = 1.1;
                break;
                
            case OptionState.INCORRECT:
                color = new Color(220, 20, 60, 255); // 红色
                break;
        }
        
        // 应用颜色
        this.optionContainer.color = color;
        
        // 应用缩放动画
        if (scale !== 1) {
            tween(this.optionContainer)
                .to(0.2, { scale: new Vec3(scale, scale, 1) })
                .start();
        } else {
            tween(this.optionContainer)
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }

    // ==================== 动画效果 ====================
    
    /** 播放选择动画 */
    private playSelectionAnimation(): void {
        if (!this.optionContainer) return;
        
        // 停止之前的动画
        if (this._selectionAnimation) {
            this._selectionAnimation.stop();
        }
        
        if (this._isSelected) {
            // 选中动画：轻微放大并添加脉冲效果
            this._selectionAnimation = tween(this.optionContainer)
                .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.1, { scale: new Vec3(1.05, 1.05, 1) })
                .start();
        } else {
            // 取消选中动画：恢复原始大小
            this._selectionAnimation = tween(this.optionContainer)
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 播放进度动画 */
    private animateProgress(): void {
        if (!this.voteProgressBar) return;
        
        // 停止之前的动画
        if (this._progressAnimation) {
            this._progressAnimation.stop();
        }
        
        const targetProgress = this._votePercentage / 100;
        const currentProgress = this.voteProgressBar.progress;
        
        // 平滑过渡到新的进度值
        this._progressAnimation = tween({ progress: currentProgress })
            .to(0.5, { progress: targetProgress })
            .call((target) => {
                if (this.voteProgressBar) {
                    this.voteProgressBar.progress = target.progress;
                }
            })
            .start();
    }
    
    /** 播放结果揭晓动画 */
    public playResultAnimation(isCorrect: boolean): void {
        if (!this.optionContainer) return;
        
        if (isCorrect) {
            // 正确答案：绿色闪烁 + 放大
            tween(this.optionContainer)
                .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
                .start();
        } else {
            // 错误答案：轻微震动
            const originalPos = this.optionContainer.position.clone();
            tween(this.optionContainer)
                .to(0.05, { position: originalPos.add3f(5, 0, 0) })
                .to(0.05, { position: originalPos.add3f(-5, 0, 0) })
                .to(0.05, { position: originalPos.add3f(5, 0, 0) })
                .to(0.05, { position: originalPos })
                .start();
        }
    }

    // ==================== 指示器管理 ====================
    
    /** 显示选择指示器 */
    private showSelectionIndicator(): void {
        if (this.selectionIndicator) {
            this.selectionIndicator.active = true;
            
            // 淡入动画
            this.selectionIndicator.setScale(0, 0, 1);
            tween(this.selectionIndicator)
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 隐藏选择指示器 */
    private hideSelectionIndicator(): void {
        if (this.selectionIndicator) {
            // 淡出动画
            tween(this.selectionIndicator)
                .to(0.2, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.selectionIndicator.active = false;
                })
                .start();
        }
    }
    
    /** 显示结果指示器 */
    private showResultIndicator(isCorrect: boolean): void {
        if (this.resultIndicator) {
            this.resultIndicator.active = true;
            
            // 设置指示器颜色
            const color = isCorrect ? new Color(34, 139, 34) : new Color(220, 20, 60);
            this.resultIndicator.color = color;
            
            // 弹出动画
            this.resultIndicator.setScale(0, 0, 1);
            tween(this.resultIndicator)
                .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.1, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }

    // ==================== 事件处理 ====================
    
    /** 选项点击处理 */
    private onOptionClick(): void {
        if (this._currentState === OptionState.DISABLED) {
            return;
        }
        
        // 切换选择状态
        const newSelected = !this._isSelected;
        this.setSelected(newSelected);
        
        // 触发回调
        if (this._props && this._props.onSelect) {
            this._props.onSelect(this._optionData.id, newSelected);
        }
        
        // 播放点击音效
        this.playClickSound();
    }
    
    /** 播放点击音效 */
    private playClickSound(): void {
        // 实际项目中需要播放音效
        console.log('Playing click sound');
    }

    // ==================== 工具方法 ====================
    
    /** 格式化投票数 */
    private formatVoteCount(count: number): string {
        if (count >= 1000000) {
            return (count / 1000000).toFixed(1) + 'M';
        } else if (count >= 1000) {
            return (count / 1000).toFixed(1) + 'K';
        } else {
            return count.toString();
        }
    }
    
    /** 获取选项数据 */
    public getOptionData(): PredictionOptionData {
        return this._optionData;
    }
    
    /** 获取选择状态 */
    public isSelected(): boolean {
        return this._isSelected;
    }
    
    /** 获取当前状态 */
    public getCurrentState(): OptionState {
        return this._currentState;
    }
    
    /** 获取投票百分比 */
    public getVotePercentage(): number {
        return this._votePercentage;
    }
    
    /** 获取投票数量 */
    public getVoteCount(): number {
        return this._voteCount;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止动画
        if (this._progressAnimation) {
            this._progressAnimation.stop();
        }
        
        if (this._selectionAnimation) {
            this._selectionAnimation.stop();
        }
        
        // 移除事件监听
        if (this.optionButton) {
            this.optionButton.node.off(Button.EventType.CLICK, this.onOptionClick, this);
        }
    }
}
