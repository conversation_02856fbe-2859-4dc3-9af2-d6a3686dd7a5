/**
 * 点赞礼物系统组件
 * 
 * 管理点赞功能、礼物发送、特效动画和虚拟货币系统
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Button, Label, Sprite, ParticleSystem, tween, Vec3, Color, Prefab, instantiate } from 'cc';
import { GiftInfo, LikeGiftSystemProps, UserInfo } from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { WatchNetworkManager } from '../managers/WatchNetworkManager';

const { ccclass, property } = _decorator;

/** 礼物类型枚举 */
export enum GiftType {
    LIKE = 'like',
    FLOWER = 'flower',
    APPLAUSE = 'applause',
    HEART = 'heart',
    DIAMOND = 'diamond',
    CROWN = 'crown'
}

/** 特效类型枚举 */
export enum EffectType {
    PARTICLE = 'particle',
    ANIMATION = 'animation',
    COMBO = 'combo'
}

@ccclass('LikeGiftSystem')
export class LikeGiftSystem extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    systemContainer: Node = null;
    
    @property(Button)
    likeButton: Button = null;
    
    @property(Node)
    giftPanel: Node = null;
    
    @property(Node)
    giftButtonsContainer: Node = null;
    
    @property(Prefab)
    giftButtonPrefab: Prefab = null;
    
    @property(Label)
    likeCountLabel: Label = null;
    
    @property(Label)
    coinBalanceLabel: Label = null;
    
    @property(Node)
    effectsContainer: Node = null;
    
    @property(ParticleSystem)
    likeParticles: ParticleSystem = null;
    
    @property(Node)
    comboIndicator: Node = null;
    
    @property(Label)
    comboCountLabel: Label = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    /** 系统配置 */
    private _props: LikeGiftSystemProps = null;
    
    /** 点赞相关 */
    private _likeCount: number = 0;
    private _likeCombo: number = 0;
    private _lastLikeTime: number = 0;
    private readonly COMBO_TIMEOUT = 3000; // 3秒连击超时
    
    /** 礼物相关 */
    private _availableGifts: GiftInfo[] = [];
    private _giftButtons: Button[] = [];
    private _isGiftPanelOpen: boolean = false;
    
    /** 用户货币 */
    private _coinBalance: number = 0;
    
    /** 动画和特效 */
    private _likeAnimation: any = null;
    private _comboTimer: number = null;
    private _effectQueue: any[] = [];
    
    /** 冷却时间 */
    private _likeCooldown: number = 0;
    private readonly LIKE_COOLDOWN_TIME = 500; // 500ms点赞冷却

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeComponents();
        this.loadGiftData();
    }
    
    protected update(dt: number) {
        this.updateCooldowns(dt);
        this.updateCombo();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 点赞按钮事件
        if (this.likeButton) {
            this.likeButton.node.on(Button.EventType.CLICK, this.onLikeClick, this);
        }
        
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.LIKE_RECEIVED, this.onLikeReceived, this);
        this._stateManager.on(WatchStateEvent.GIFT_RECEIVED, this.onGiftReceived, this);
        this._stateManager.on(WatchStateEvent.COIN_BALANCE_UPDATED, this.onCoinBalanceUpdated, this);
    }
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏礼物面板
        if (this.giftPanel) {
            this.giftPanel.active = false;
        }
        
        // 隐藏连击指示器
        if (this.comboIndicator) {
            this.comboIndicator.active = false;
        }
        
        // 初始化显示
        this.updateLikeDisplay();
        this.updateCoinDisplay();
    }
    
    /** 加载礼物数据 */
    private loadGiftData(): void {
        // 默认礼物配置
        this._availableGifts = [
            {
                id: 'flower',
                name: '鲜花',
                icon: 'gift_flower',
                price: 10,
                effect: EffectType.PARTICLE,
                rarity: 'common'
            },
            {
                id: 'applause',
                name: '掌声',
                icon: 'gift_applause',
                price: 20,
                effect: EffectType.ANIMATION,
                rarity: 'common'
            },
            {
                id: 'heart',
                name: '爱心',
                icon: 'gift_heart',
                price: 50,
                effect: EffectType.COMBO,
                rarity: 'rare'
            },
            {
                id: 'diamond',
                name: '钻石',
                icon: 'gift_diamond',
                price: 100,
                effect: EffectType.PARTICLE,
                rarity: 'epic'
            },
            {
                id: 'crown',
                name: '皇冠',
                icon: 'gift_crown',
                price: 500,
                effect: EffectType.ANIMATION,
                rarity: 'legendary'
            }
        ];
        
        this.createGiftButtons();
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: LikeGiftSystemProps): void {
        this._props = props;
        
        if (props.initialCoinBalance !== undefined) {
            this._coinBalance = props.initialCoinBalance;
            this.updateCoinDisplay();
        }
    }
    
    /** 发送点赞 */
    public sendLike(): void {
        if (this._likeCooldown > 0) {
            console.log('Like is on cooldown');
            return;
        }
        
        // 发送点赞到服务器
        this._networkManager.sendLike();
        
        // 本地更新
        this._likeCount++;
        this.updateLikeCombo();
        this.updateLikeDisplay();
        
        // 播放点赞动画
        this.playLikeAnimation();
        
        // 设置冷却时间
        this._likeCooldown = this.LIKE_COOLDOWN_TIME;
        
        // 触发回调
        if (this._props && this._props.onLike) {
            this._props.onLike(this._likeCount);
        }
    }
    
    /** 发送礼物 */
    public sendGift(giftId: string): boolean {
        const gift = this._availableGifts.find(g => g.id === giftId);
        if (!gift) {
            console.error('Gift not found:', giftId);
            return false;
        }
        
        // 检查余额
        if (this._coinBalance < gift.price) {
            this.showInsufficientFundsMessage();
            return false;
        }
        
        // 扣除货币
        this._coinBalance -= gift.price;
        this.updateCoinDisplay();
        
        // 发送礼物到服务器
        this._networkManager.sendGift(giftId);
        
        // 播放礼物特效
        this.playGiftEffect(gift);
        
        // 触发回调
        if (this._props && this._props.onGift) {
            this._props.onGift(gift);
        }
        
        return true;
    }
    
    /** 切换礼物面板 */
    public toggleGiftPanel(): void {
        this._isGiftPanelOpen = !this._isGiftPanelOpen;
        
        if (this._isGiftPanelOpen) {
            this.showGiftPanel();
        } else {
            this.hideGiftPanel();
        }
    }

    // ==================== 点赞系统 ====================
    
    /** 更新点赞连击 */
    private updateLikeCombo(): void {
        const currentTime = Date.now();
        
        if (currentTime - this._lastLikeTime < this.COMBO_TIMEOUT) {
            this._likeCombo++;
        } else {
            this._likeCombo = 1;
        }
        
        this._lastLikeTime = currentTime;
        
        // 显示连击效果
        if (this._likeCombo > 1) {
            this.showComboEffect();
        }
        
        // 重置连击计时器
        this.resetComboTimer();
    }
    
    /** 显示连击效果 */
    private showComboEffect(): void {
        if (!this.comboIndicator || !this.comboCountLabel) return;
        
        this.comboIndicator.active = true;
        this.comboCountLabel.string = `${this._likeCombo}x`;
        
        // 连击动画
        this.comboIndicator.setScale(0.5, 0.5, 1);
        tween(this.comboIndicator)
            .to(0.2, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
        
        // 根据连击数改变颜色
        if (this._likeCombo >= 10) {
            this.comboCountLabel.color = new Color(255, 215, 0); // 金色
        } else if (this._likeCombo >= 5) {
            this.comboCountLabel.color = new Color(255, 69, 0); // 橙红色
        } else {
            this.comboCountLabel.color = new Color(255, 20, 147); // 深粉色
        }
    }
    
    /** 重置连击计时器 */
    private resetComboTimer(): void {
        if (this._comboTimer) {
            clearTimeout(this._comboTimer);
        }
        
        this._comboTimer = setTimeout(() => {
            this.hideComboEffect();
        }, this.COMBO_TIMEOUT);
    }
    
    /** 隐藏连击效果 */
    private hideComboEffect(): void {
        if (this.comboIndicator) {
            tween(this.comboIndicator)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.comboIndicator.active = false;
                    this._likeCombo = 0;
                })
                .start();
        }
    }

    // ==================== 礼物系统 ====================
    
    /** 创建礼物按钮 */
    private createGiftButtons(): void {
        if (!this.giftButtonPrefab || !this.giftButtonsContainer) return;
        
        this._availableGifts.forEach(gift => {
            const buttonNode = instantiate(this.giftButtonPrefab);
            const button = buttonNode.getComponent(Button);
            const icon = buttonNode.getComponentInChildren(Sprite);
            const nameLabel = buttonNode.getComponentInChildren(Label);
            const priceLabel = buttonNode.getComponentsInChildren(Label)[1];
            
            if (button && nameLabel && priceLabel) {
                // 设置礼物信息
                nameLabel.string = gift.name;
                priceLabel.string = `${gift.price}`;
                
                // 设置稀有度颜色
                this.setGiftRarityColor(buttonNode, gift.rarity);
                
                // 设置点击事件
                button.node.on(Button.EventType.CLICK, () => {
                    this.onGiftButtonClick(gift.id);
                });
                
                // 添加到容器
                this.giftButtonsContainer.addChild(buttonNode);
                this._giftButtons.push(button);
            }
        });
    }
    
    /** 设置礼物稀有度颜色 */
    private setGiftRarityColor(buttonNode: Node, rarity: string): void {
        let color: Color;
        
        switch (rarity) {
            case 'common':
                color = new Color(255, 255, 255); // 白色
                break;
            case 'rare':
                color = new Color(30, 144, 255); // 蓝色
                break;
            case 'epic':
                color = new Color(138, 43, 226); // 紫色
                break;
            case 'legendary':
                color = new Color(255, 215, 0); // 金色
                break;
            default:
                color = new Color(255, 255, 255);
        }
        
        buttonNode.color = color;
    }
    
    /** 显示礼物面板 */
    private showGiftPanel(): void {
        if (!this.giftPanel) return;
        
        this.giftPanel.active = true;
        
        // 滑入动画
        this.giftPanel.setPosition(0, -200, 0);
        tween(this.giftPanel)
            .to(0.3, { position: new Vec3(0, 0, 0) })
            .start();
    }
    
    /** 隐藏礼物面板 */
    private hideGiftPanel(): void {
        if (!this.giftPanel) return;
        
        // 滑出动画
        tween(this.giftPanel)
            .to(0.3, { position: new Vec3(0, -200, 0) })
            .call(() => {
                this.giftPanel.active = false;
            })
            .start();
    }

    // ==================== 动画特效 ====================
    
    /** 播放点赞动画 */
    private playLikeAnimation(): void {
        if (!this.likeButton) return;
        
        // 按钮缩放动画
        this._likeAnimation = tween(this.likeButton.node)
            .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
        
        // 粒子特效
        if (this.likeParticles) {
            this.likeParticles.play();
        }
        
        // 创建飞出的点赞图标
        this.createFlyingLikeIcon();
    }
    
    /** 创建飞出的点赞图标 */
    private createFlyingLikeIcon(): void {
        // 这里应该创建一个临时的点赞图标节点
        // 播放从按钮位置飞向屏幕上方的动画
        console.log('Creating flying like icon animation');
    }
    
    /** 播放礼物特效 */
    private playGiftEffect(gift: GiftInfo): void {
        switch (gift.effect) {
            case EffectType.PARTICLE:
                this.playParticleEffect(gift);
                break;
            case EffectType.ANIMATION:
                this.playAnimationEffect(gift);
                break;
            case EffectType.COMBO:
                this.playComboEffect(gift);
                break;
        }
    }
    
    /** 播放粒子特效 */
    private playParticleEffect(gift: GiftInfo): void {
        console.log('Playing particle effect for gift:', gift.name);
        // 实现粒子特效
    }
    
    /** 播放动画特效 */
    private playAnimationEffect(gift: GiftInfo): void {
        console.log('Playing animation effect for gift:', gift.name);
        // 实现动画特效
    }
    
    /** 播放连击特效 */
    private playComboEffect(gift: GiftInfo): void {
        console.log('Playing combo effect for gift:', gift.name);
        // 实现连击特效
    }

    // ==================== 显示更新 ====================
    
    /** 更新点赞显示 */
    private updateLikeDisplay(): void {
        if (this.likeCountLabel) {
            this.likeCountLabel.string = this.formatNumber(this._likeCount);
        }
    }
    
    /** 更新货币显示 */
    private updateCoinDisplay(): void {
        if (this.coinBalanceLabel) {
            this.coinBalanceLabel.string = this.formatNumber(this._coinBalance);
        }
        
        // 更新礼物按钮状态
        this.updateGiftButtonStates();
    }
    
    /** 更新礼物按钮状态 */
    private updateGiftButtonStates(): void {
        this._giftButtons.forEach((button, index) => {
            if (index < this._availableGifts.length) {
                const gift = this._availableGifts[index];
                const canAfford = this._coinBalance >= gift.price;
                
                button.interactable = canAfford;
                button.node.color = canAfford ? 
                    new Color(255, 255, 255) : 
                    new Color(128, 128, 128);
            }
        });
    }
    
    /** 格式化数字显示 */
    private formatNumber(num: number): string {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toString();
        }
    }

    // ==================== 事件处理 ====================
    
    /** 点赞按钮点击处理 */
    private onLikeClick(): void {
        this.sendLike();
    }
    
    /** 礼物按钮点击处理 */
    private onGiftButtonClick(giftId: string): void {
        const success = this.sendGift(giftId);
        if (success) {
            this.hideGiftPanel();
            this._isGiftPanelOpen = false;
        }
    }
    
    /** 接收点赞处理 */
    private onLikeReceived(data: any): void {
        this._likeCount = data.totalLikes || this._likeCount;
        this.updateLikeDisplay();
        
        // 播放接收点赞动画
        this.playReceiveLikeAnimation();
    }
    
    /** 接收礼物处理 */
    private onGiftReceived(giftData: any): void {
        console.log('Gift received:', giftData);
        
        // 播放接收礼物特效
        const gift = this._availableGifts.find(g => g.id === giftData.giftId);
        if (gift) {
            this.playGiftEffect(gift);
        }
    }
    
    /** 货币余额更新处理 */
    private onCoinBalanceUpdated(balance: number): void {
        this._coinBalance = balance;
        this.updateCoinDisplay();
    }
    
    /** 播放接收点赞动画 */
    private playReceiveLikeAnimation(): void {
        // 实现接收点赞的视觉反馈
        console.log('Playing receive like animation');
    }
    
    /** 显示余额不足消息 */
    private showInsufficientFundsMessage(): void {
        console.log('Insufficient funds for gift');
        // 这里应该显示一个提示消息
    }

    // ==================== 更新循环 ====================
    
    /** 更新冷却时间 */
    private updateCooldowns(dt: number): void {
        if (this._likeCooldown > 0) {
            this._likeCooldown -= dt * 1000;
            
            // 更新按钮状态
            if (this.likeButton) {
                this.likeButton.interactable = this._likeCooldown <= 0;
            }
        }
    }
    
    /** 更新连击状态 */
    private updateCombo(): void {
        const currentTime = Date.now();
        if (this._likeCombo > 0 && currentTime - this._lastLikeTime > this.COMBO_TIMEOUT) {
            this.hideComboEffect();
        }
    }

    // ==================== 工具方法 ====================
    
    /** 获取点赞数量 */
    public getLikeCount(): number {
        return this._likeCount;
    }
    
    /** 获取货币余额 */
    public getCoinBalance(): number {
        return this._coinBalance;
    }
    
    /** 获取连击数 */
    public getComboCount(): number {
        return this._likeCombo;
    }
    
    /** 检查是否可以点赞 */
    public canLike(): boolean {
        return this._likeCooldown <= 0;
    }
    
    /** 检查是否可以购买礼物 */
    public canAffordGift(giftId: string): boolean {
        const gift = this._availableGifts.find(g => g.id === giftId);
        return gift ? this._coinBalance >= gift.price : false;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 清理定时器
        if (this._comboTimer) {
            clearTimeout(this._comboTimer);
        }
        
        // 停止动画
        if (this._likeAnimation) {
            this._likeAnimation.stop();
        }
        
        // 清理礼物按钮
        this._giftButtons.forEach(button => {
            if (button.node) {
                button.node.destroy();
            }
        });
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.LIKE_RECEIVED, this.onLikeReceived, this);
            this._stateManager.off(WatchStateEvent.GIFT_RECEIVED, this.onGiftReceived, this);
            this._stateManager.off(WatchStateEvent.COIN_BALANCE_UPDATED, this.onCoinBalanceUpdated, this);
        }
        
        if (this.likeButton) {
            this.likeButton.node.off(Button.EventType.CLICK, this.onLikeClick, this);
        }
    }
}
