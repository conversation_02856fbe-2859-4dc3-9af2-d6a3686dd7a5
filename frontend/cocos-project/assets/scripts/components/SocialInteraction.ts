/**
 * 社交互动组件
 * 
 * 管理用户关注、私信、用户资料查看、社交操作菜单等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Button, Label, Sprite, ScrollView, Prefab, instantiate, tween, Vec3 } from 'cc';
import { 
    UserInfo, 
    SocialInteractionProps, 
    UserRelationship, 
    PrivateMessage,
    SocialAction 
} from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { WatchNetworkManager } from '../managers/WatchNetworkManager';

const { ccclass, property } = _decorator;

/** 社交操作类型 */
export enum SocialActionType {
    FOLLOW = 'follow',
    UNFOLLOW = 'unfollow',
    BLOCK = 'block',
    UNBLOCK = 'unblock',
    REPORT = 'report',
    PRIVATE_MESSAGE = 'private_message',
    VIEW_PROFILE = 'view_profile'
}

@ccclass('SocialInteraction')
export class SocialInteraction extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    interactionContainer: Node = null;
    
    @property(Node)
    userProfilePanel: Node = null;
    
    @property(Node)
    socialMenuPanel: Node = null;
    
    @property(Node)
    privateMessagePanel: Node = null;
    
    @property(ScrollView)
    messageScrollView: ScrollView = null;
    
    @property(Node)
    messageInputContainer: Node = null;
    
    @property(Button)
    followButton: Button = null;
    
    @property(Button)
    messageButton: Button = null;
    
    @property(Button)
    blockButton: Button = null;
    
    @property(Button)
    reportButton: Button = null;
    
    // 用户资料显示
    @property(Sprite)
    userAvatar: Sprite = null;
    
    @property(Label)
    userNickname: Label = null;
    
    @property(Label)
    userLevel: Label = null;
    
    @property(Label)
    userFollowers: Label = null;
    
    @property(Label)
    userFollowing: Label = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    /** 当前查看的用户 */
    private _currentUser: UserInfo = null;
    
    /** 用户关系 */
    private _userRelationship: UserRelationship = null;
    
    /** 私信列表 */
    private _privateMessages: PrivateMessage[] = [];
    
    /** 配置属性 */
    private _props: SocialInteractionProps = null;
    
    /** 面板状态 */
    private _isProfilePanelOpen: boolean = false;
    private _isMessagePanelOpen: boolean = false;
    private _isSocialMenuOpen: boolean = false;
    
    /** 消息相关 */
    private _messageInput: any = null;
    private _isLoadingMessages: boolean = false;

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeComponents();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 按钮事件
        if (this.followButton) {
            this.followButton.node.on(Button.EventType.CLICK, this.onFollowClick, this);
        }
        
        if (this.messageButton) {
            this.messageButton.node.on(Button.EventType.CLICK, this.onMessageClick, this);
        }
        
        if (this.blockButton) {
            this.blockButton.node.on(Button.EventType.CLICK, this.onBlockClick, this);
        }
        
        if (this.reportButton) {
            this.reportButton.node.on(Button.EventType.CLICK, this.onReportClick, this);
        }
        
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.USER_RELATIONSHIP_UPDATED, this.onUserRelationshipUpdated, this);
        this._stateManager.on(WatchStateEvent.PRIVATE_MESSAGE_RECEIVED, this.onPrivateMessageReceived, this);
    }
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏所有面板
        this.hideAllPanels();
        
        // 初始化消息输入
        this.initializeMessageInput();
    }
    
    /** 初始化消息输入 */
    private initializeMessageInput(): void {
        if (this.messageInputContainer) {
            // 这里应该设置消息输入框的事件监听
            // 实际项目中需要根据具体的输入组件来实现
        }
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: SocialInteractionProps): void {
        this._props = props;
    }
    
    /** 显示用户资料 */
    public showUserProfile(userInfo: UserInfo): void {
        this._currentUser = userInfo;
        this.loadUserRelationship();
        this.updateUserProfileDisplay();
        this.showProfilePanel();
    }
    
    /** 显示社交菜单 */
    public showSocialMenu(userInfo: UserInfo): void {
        this._currentUser = userInfo;
        this.loadUserRelationship();
        this.updateSocialMenuDisplay();
        this.showSocialMenuPanel();
    }
    
    /** 开始私信对话 */
    public startPrivateMessage(userInfo: UserInfo): void {
        this._currentUser = userInfo;
        this.loadPrivateMessages();
        this.showPrivateMessagePanel();
    }
    
    /** 执行社交操作 */
    public performSocialAction(action: SocialActionType, targetUserId: string): void {
        switch (action) {
            case SocialActionType.FOLLOW:
                this.followUser(targetUserId);
                break;
            case SocialActionType.UNFOLLOW:
                this.unfollowUser(targetUserId);
                break;
            case SocialActionType.BLOCK:
                this.blockUser(targetUserId);
                break;
            case SocialActionType.UNBLOCK:
                this.unblockUser(targetUserId);
                break;
            case SocialActionType.REPORT:
                this.reportUser(targetUserId);
                break;
            case SocialActionType.PRIVATE_MESSAGE:
                this.startPrivateMessage(this._currentUser);
                break;
            case SocialActionType.VIEW_PROFILE:
                this.showUserProfile(this._currentUser);
                break;
        }
    }

    // ==================== 用户关系管理 ====================
    
    /** 关注用户 */
    private async followUser(userId: string): Promise<void> {
        try {
            await this._networkManager.followUser(userId);
            
            // 更新本地状态
            if (this._userRelationship) {
                this._userRelationship.isFollowing = true;
                this._userRelationship.followersCount++;
            }
            
            this.updateSocialButtonStates();
            
            // 触发回调
            if (this._props && this._props.onFollow) {
                this._props.onFollow(userId);
            }
            
        } catch (error) {
            console.error('Failed to follow user:', error);
        }
    }
    
    /** 取消关注用户 */
    private async unfollowUser(userId: string): Promise<void> {
        try {
            await this._networkManager.unfollowUser(userId);
            
            // 更新本地状态
            if (this._userRelationship) {
                this._userRelationship.isFollowing = false;
                this._userRelationship.followersCount--;
            }
            
            this.updateSocialButtonStates();
            
            // 触发回调
            if (this._props && this._props.onUnfollow) {
                this._props.onUnfollow(userId);
            }
            
        } catch (error) {
            console.error('Failed to unfollow user:', error);
        }
    }
    
    /** 屏蔽用户 */
    private async blockUser(userId: string): Promise<void> {
        try {
            await this._networkManager.blockUser(userId);
            
            // 更新本地状态
            if (this._userRelationship) {
                this._userRelationship.isBlocked = true;
            }
            
            this.updateSocialButtonStates();
            
            // 触发回调
            if (this._props && this._props.onBlock) {
                this._props.onBlock(userId);
            }
            
        } catch (error) {
            console.error('Failed to block user:', error);
        }
    }
    
    /** 取消屏蔽用户 */
    private async unblockUser(userId: string): Promise<void> {
        try {
            await this._networkManager.unblockUser(userId);
            
            // 更新本地状态
            if (this._userRelationship) {
                this._userRelationship.isBlocked = false;
            }
            
            this.updateSocialButtonStates();
            
            // 触发回调
            if (this._props && this._props.onUnblock) {
                this._props.onUnblock(userId);
            }
            
        } catch (error) {
            console.error('Failed to unblock user:', error);
        }
    }
    
    /** 举报用户 */
    private async reportUser(userId: string): Promise<void> {
        try {
            await this._networkManager.reportUser(userId);
            
            // 显示举报成功提示
            this.showReportSuccessMessage();
            
            // 触发回调
            if (this._props && this._props.onReport) {
                this._props.onReport(userId);
            }
            
        } catch (error) {
            console.error('Failed to report user:', error);
        }
    }

    // ==================== 私信系统 ====================
    
    /** 发送私信 */
    public async sendPrivateMessage(content: string): Promise<void> {
        if (!this._currentUser || !content.trim()) {
            return;
        }
        
        try {
            const message: PrivateMessage = {
                id: Date.now().toString(),
                senderId: 'current_user', // 应该从状态管理器获取当前用户ID
                receiverId: this._currentUser.userId,
                content: content.trim(),
                timestamp: Date.now(),
                isRead: false
            };
            
            await this._networkManager.sendPrivateMessage(message);
            
            // 添加到本地消息列表
            this._privateMessages.push(message);
            this.updateMessageDisplay();
            
            // 清空输入框
            this.clearMessageInput();
            
            // 触发回调
            if (this._props && this._props.onPrivateMessage) {
                this._props.onPrivateMessage(message);
            }
            
        } catch (error) {
            console.error('Failed to send private message:', error);
        }
    }
    
    /** 加载私信历史 */
    private async loadPrivateMessages(): Promise<void> {
        if (!this._currentUser || this._isLoadingMessages) {
            return;
        }
        
        this._isLoadingMessages = true;
        
        try {
            const messages = await this._networkManager.getPrivateMessages(this._currentUser.userId);
            this._privateMessages = messages;
            this.updateMessageDisplay();
            
        } catch (error) {
            console.error('Failed to load private messages:', error);
        } finally {
            this._isLoadingMessages = false;
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新用户资料显示 */
    private updateUserProfileDisplay(): void {
        if (!this._currentUser) return;
        
        // 更新头像
        if (this.userAvatar) {
            this.loadUserAvatar(this._currentUser.avatar);
        }
        
        // 更新昵称
        if (this.userNickname) {
            this.userNickname.string = this._currentUser.nickname;
        }
        
        // 更新等级
        if (this.userLevel) {
            this.userLevel.string = `Lv.${this._currentUser.level || 1}`;
        }
        
        // 更新关注数据
        if (this._userRelationship) {
            if (this.userFollowers) {
                this.userFollowers.string = `粉丝 ${this._userRelationship.followersCount}`;
            }
            
            if (this.userFollowing) {
                this.userFollowing.string = `关注 ${this._userRelationship.followingCount}`;
            }
        }
        
        // 更新按钮状态
        this.updateSocialButtonStates();
    }
    
    /** 更新社交菜单显示 */
    private updateSocialMenuDisplay(): void {
        this.updateSocialButtonStates();
    }
    
    /** 更新社交按钮状态 */
    private updateSocialButtonStates(): void {
        if (!this._userRelationship) return;
        
        // 更新关注按钮
        if (this.followButton) {
            const followLabel = this.followButton.getComponentInChildren(Label);
            if (followLabel) {
                followLabel.string = this._userRelationship.isFollowing ? '取消关注' : '关注';
            }
            this.followButton.interactable = !this._userRelationship.isBlocked;
        }
        
        // 更新私信按钮
        if (this.messageButton) {
            this.messageButton.interactable = !this._userRelationship.isBlocked;
        }
        
        // 更新屏蔽按钮
        if (this.blockButton) {
            const blockLabel = this.blockButton.getComponentInChildren(Label);
            if (blockLabel) {
                blockLabel.string = this._userRelationship.isBlocked ? '取消屏蔽' : '屏蔽';
            }
        }
    }
    
    /** 更新消息显示 */
    private updateMessageDisplay(): void {
        if (!this.messageScrollView) return;
        
        // 清空现有消息
        const content = this.messageScrollView.content;
        if (content) {
            content.removeAllChildren();
        }
        
        // 添加消息项
        this._privateMessages.forEach(message => {
            this.createMessageItem(message);
        });
        
        // 滚动到底部
        this.scheduleOnce(() => {
            this.messageScrollView.scrollToBottom(0.3);
        }, 0.1);
    }
    
    /** 创建消息项 */
    private createMessageItem(message: PrivateMessage): void {
        // 这里应该创建消息项的UI
        // 实际项目中需要根据具体的消息项预制体来实现
        console.log('Creating message item:', message.content);
    }

    // ==================== 面板管理 ====================
    
    /** 显示用户资料面板 */
    private showProfilePanel(): void {
        this.hideAllPanels();
        
        if (this.userProfilePanel) {
            this.userProfilePanel.active = true;
            this._isProfilePanelOpen = true;
            
            // 淡入动画
            this.userProfilePanel.setScale(0, 0, 1);
            tween(this.userProfilePanel)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 显示社交菜单面板 */
    private showSocialMenuPanel(): void {
        this.hideAllPanels();
        
        if (this.socialMenuPanel) {
            this.socialMenuPanel.active = true;
            this._isSocialMenuOpen = true;
            
            // 滑入动画
            this.socialMenuPanel.setPosition(0, -200, 0);
            tween(this.socialMenuPanel)
                .to(0.3, { position: new Vec3(0, 0, 0) })
                .start();
        }
    }
    
    /** 显示私信面板 */
    private showPrivateMessagePanel(): void {
        this.hideAllPanels();
        
        if (this.privateMessagePanel) {
            this.privateMessagePanel.active = true;
            this._isMessagePanelOpen = true;
            
            // 淡入动画
            this.privateMessagePanel.setScale(0, 0, 1);
            tween(this.privateMessagePanel)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 隐藏所有面板 */
    private hideAllPanels(): void {
        if (this.userProfilePanel) {
            this.userProfilePanel.active = false;
        }
        
        if (this.socialMenuPanel) {
            this.socialMenuPanel.active = false;
        }
        
        if (this.privateMessagePanel) {
            this.privateMessagePanel.active = false;
        }
        
        this._isProfilePanelOpen = false;
        this._isSocialMenuOpen = false;
        this._isMessagePanelOpen = false;
    }

    // ==================== 事件处理 ====================
    
    /** 关注按钮点击处理 */
    private onFollowClick(): void {
        if (!this._currentUser) return;
        
        if (this._userRelationship && this._userRelationship.isFollowing) {
            this.unfollowUser(this._currentUser.userId);
        } else {
            this.followUser(this._currentUser.userId);
        }
    }
    
    /** 私信按钮点击处理 */
    private onMessageClick(): void {
        if (!this._currentUser) return;
        
        this.startPrivateMessage(this._currentUser);
    }
    
    /** 屏蔽按钮点击处理 */
    private onBlockClick(): void {
        if (!this._currentUser) return;
        
        if (this._userRelationship && this._userRelationship.isBlocked) {
            this.unblockUser(this._currentUser.userId);
        } else {
            this.blockUser(this._currentUser.userId);
        }
    }
    
    /** 举报按钮点击处理 */
    private onReportClick(): void {
        if (!this._currentUser) return;
        
        this.reportUser(this._currentUser.userId);
    }
    
    /** 用户关系更新处理 */
    private onUserRelationshipUpdated(relationship: UserRelationship): void {
        if (this._currentUser && relationship.userId === this._currentUser.userId) {
            this._userRelationship = relationship;
            this.updateSocialButtonStates();
        }
    }
    
    /** 私信接收处理 */
    private onPrivateMessageReceived(message: PrivateMessage): void {
        if (this._currentUser && 
            (message.senderId === this._currentUser.userId || message.receiverId === this._currentUser.userId)) {
            this._privateMessages.push(message);
            this.updateMessageDisplay();
        }
    }

    // ==================== 辅助方法 ====================
    
    /** 加载用户关系 */
    private async loadUserRelationship(): Promise<void> {
        if (!this._currentUser) return;
        
        try {
            this._userRelationship = await this._networkManager.getUserRelationship(this._currentUser.userId);
            this.updateSocialButtonStates();
        } catch (error) {
            console.error('Failed to load user relationship:', error);
        }
    }
    
    /** 加载用户头像 */
    private loadUserAvatar(avatarUrl: string): void {
        // 实际项目中需要实现网络图片加载
        console.log('Loading user avatar:', avatarUrl);
    }
    
    /** 清空消息输入 */
    private clearMessageInput(): void {
        // 实际项目中需要根据具体的输入组件来实现
        console.log('Clearing message input');
    }
    
    /** 显示举报成功消息 */
    private showReportSuccessMessage(): void {
        console.log('Report submitted successfully');
        // 这里应该显示一个成功提示
    }

    // ==================== 工具方法 ====================
    
    /** 获取当前用户 */
    public getCurrentUser(): UserInfo | null {
        return this._currentUser;
    }
    
    /** 获取用户关系 */
    public getUserRelationship(): UserRelationship | null {
        return this._userRelationship;
    }
    
    /** 检查是否有打开的面板 */
    public hasOpenPanel(): boolean {
        return this._isProfilePanelOpen || this._isSocialMenuOpen || this._isMessagePanelOpen;
    }
    
    /** 关闭所有面板 */
    public closeAllPanels(): void {
        this.hideAllPanels();
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.USER_RELATIONSHIP_UPDATED, this.onUserRelationshipUpdated, this);
            this._stateManager.off(WatchStateEvent.PRIVATE_MESSAGE_RECEIVED, this.onPrivateMessageReceived, this);
        }
        
        // 移除按钮事件
        if (this.followButton) {
            this.followButton.node.off(Button.EventType.CLICK, this.onFollowClick, this);
        }
        
        if (this.messageButton) {
            this.messageButton.node.off(Button.EventType.CLICK, this.onMessageClick, this);
        }
        
        if (this.blockButton) {
            this.blockButton.node.off(Button.EventType.CLICK, this.onBlockClick, this);
        }
        
        if (this.reportButton) {
            this.reportButton.node.off(Button.EventType.CLICK, this.onReportClick, this);
        }
    }
}
