/**
 * 围观房间卡片组件
 * 
 * 显示房间信息，支持点击进入房间、查看详情等交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, Sprite, Button, tween, Vec3, Color } from 'cc';
import { RoomInfo, WatchRoomCardProps } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

@ccclass('WatchRoomCard')
export class WatchRoomCard extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    cardContainer: Node = null;
    
    @property(Label)
    playerNameLabel: Label = null;
    
    @property(Label)
    regionLabel: Label = null;
    
    @property(Label)
    viewerCountLabel: Label = null;
    
    @property(Label)
    gameStatusLabel: Label = null;
    
    @property(Label)
    difficultyLabel: Label = null;
    
    @property(Sprite)
    playerAvatar: Sprite = null;
    
    @property(Sprite)
    statusIndicator: Sprite = null;
    
    @property(Node)
    liveIndicator: Node = null;
    
    @property(Button)
    enterButton: Button = null;
    
    @property(Button)
    detailsButton: Button = null;
    
    // ==================== 私有属性 ====================
    
    private _roomInfo: RoomInfo = null;
    private _onEnterRoom: (roomId: string) => void = null;
    private _onRoomDetails: (roomId: string) => void = null;
    
    // 动画相关
    private _pulseAnimation: any = null;
    private _hoverAnimation: any = null;
    
    // 状态颜色配置
    private readonly STATUS_COLORS = {
        waiting: new Color(250, 173, 20), // 黄色
        playing: new Color(82, 196, 26),  // 绿色
        finished: new Color(153, 153, 153) // 灰色
    };

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeComponents();
        this.setupEventListeners();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 设置初始状态
        if (this.cardContainer) {
            this.cardContainer.setScale(1, 1, 1);
        }
        
        // 隐藏直播指示器
        if (this.liveIndicator) {
            this.liveIndicator.active = false;
        }
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 进入房间按钮
        if (this.enterButton) {
            this.enterButton.node.on(Button.EventType.CLICK, this.onEnterButtonClick, this);
        }
        
        // 详情按钮
        if (this.detailsButton) {
            this.detailsButton.node.on(Button.EventType.CLICK, this.onDetailsButtonClick, this);
        }
        
        // 卡片点击事件
        if (this.cardContainer) {
            this.cardContainer.on(Node.EventType.TOUCH_START, this.onCardTouchStart, this);
            this.cardContainer.on(Node.EventType.TOUCH_END, this.onCardTouchEnd, this);
            this.cardContainer.on(Node.EventType.TOUCH_CANCEL, this.onCardTouchCancel, this);
        }
    }

    // ==================== 公共方法 ====================
    
    /** 设置房间信息 */
    public setRoomInfo(roomInfo: RoomInfo, props: WatchRoomCardProps): void {
        this._roomInfo = roomInfo;
        this._onEnterRoom = props.onEnterRoom;
        this._onRoomDetails = props.onRoomDetails;
        
        this.updateDisplay();
        this.updateStatusAnimation();
    }
    
    /** 更新围观人数 */
    public updateViewerCount(count: number): void {
        if (this._roomInfo) {
            this._roomInfo.viewerCount = count;
            this.updateViewerCountDisplay();
            this.playViewerCountAnimation();
        }
    }
    
    /** 更新游戏状态 */
    public updateGameStatus(status: 'waiting' | 'playing' | 'finished'): void {
        if (this._roomInfo) {
            this._roomInfo.gameStatus = status;
            this.updateGameStatusDisplay();
            this.updateStatusAnimation();
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示内容 */
    private updateDisplay(): void {
        if (!this._roomInfo) return;
        
        // 玩家信息
        if (this.playerNameLabel) {
            this.playerNameLabel.string = this._roomInfo.playerInfo.nickname;
        }
        
        if (this.regionLabel) {
            this.regionLabel.string = this._roomInfo.playerInfo.region;
        }
        
        // 围观人数
        this.updateViewerCountDisplay();
        
        // 游戏状态
        this.updateGameStatusDisplay();
        
        // 难度信息
        if (this.difficultyLabel && this._roomInfo.currentQuestion) {
            const difficulty = this._roomInfo.currentQuestion.difficulty;
            this.difficultyLabel.string = this.getDifficultyText(difficulty);
        }
        
        // 直播状态
        if (this.liveIndicator) {
            this.liveIndicator.active = this._roomInfo.isLive;
        }
        
        // 玩家头像（这里需要加载网络图片，简化处理）
        this.loadPlayerAvatar(this._roomInfo.playerInfo.avatar);
    }
    
    /** 更新围观人数显示 */
    private updateViewerCountDisplay(): void {
        if (this.viewerCountLabel && this._roomInfo) {
            const count = this._roomInfo.viewerCount;
            this.viewerCountLabel.string = count > 999 ? '999+' : count.toString();
        }
    }
    
    /** 更新游戏状态显示 */
    private updateGameStatusDisplay(): void {
        if (!this.gameStatusLabel || !this._roomInfo) return;
        
        const status = this._roomInfo.gameStatus;
        let statusText = '';
        
        switch (status) {
            case 'waiting':
                statusText = '等待开始';
                break;
            case 'playing':
                if (this._roomInfo.currentQuestion) {
                    statusText = `第${this._roomInfo.currentQuestion.questionNumber}题`;
                } else {
                    statusText = '游戏中';
                }
                break;
            case 'finished':
                statusText = '已结束';
                break;
        }
        
        this.gameStatusLabel.string = statusText;
        
        // 更新状态指示器颜色
        if (this.statusIndicator) {
            const color = this.STATUS_COLORS[status];
            this.statusIndicator.color = color;
        }
    }
    
    /** 获取难度文本 */
    private getDifficultyText(difficulty: string): string {
        switch (difficulty) {
            case 'easy': return '简单';
            case 'medium': return '中等';
            case 'hard': return '困难';
            default: return '';
        }
    }
    
    /** 加载玩家头像 */
    private loadPlayerAvatar(avatarUrl: string): void {
        // 这里应该使用Cocos Creator的资源加载系统
        // 简化处理，实际项目中需要实现网络图片加载
        console.log('Loading avatar:', avatarUrl);
    }

    // ==================== 动画效果 ====================
    
    /** 更新状态动画 */
    private updateStatusAnimation(): void {
        if (!this._roomInfo || !this.statusIndicator) return;
        
        // 停止之前的动画
        if (this._pulseAnimation) {
            this._pulseAnimation.stop();
        }
        
        const status = this._roomInfo.gameStatus;
        
        if (status === 'playing') {
            // 进行中：脉冲动画
            this.playPulseAnimation();
        } else if (status === 'waiting') {
            // 等待中：呼吸动画
            this.playBreathingAnimation();
        }
        // finished状态不播放动画
    }
    
    /** 播放脉冲动画 */
    private playPulseAnimation(): void {
        if (!this.statusIndicator) return;
        
        this._pulseAnimation = tween(this.statusIndicator.node)
            .to(0.5, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.5, { scale: new Vec3(1, 1, 1) })
            .repeatForever()
            .start();
    }
    
    /** 播放呼吸动画 */
    private playBreathingAnimation(): void {
        if (!this.statusIndicator) return;
        
        this._pulseAnimation = tween(this.statusIndicator.node)
            .to(1.0, { scale: new Vec3(1.1, 1.1, 1) })
            .to(1.0, { scale: new Vec3(1, 1, 1) })
            .repeatForever()
            .start();
    }
    
    /** 播放围观人数变化动画 */
    private playViewerCountAnimation(): void {
        if (!this.viewerCountLabel) return;
        
        // 简单的缩放动画
        tween(this.viewerCountLabel.node)
            .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /** 播放悬停动画 */
    private playHoverAnimation(): void {
        if (!this.cardContainer) return;
        
        this._hoverAnimation = tween(this.cardContainer)
            .to(0.2, { 
                scale: new Vec3(1.02, 1.02, 1),
                position: new Vec3(this.cardContainer.position.x, this.cardContainer.position.y + 5, 0)
            })
            .start();
    }
    
    /** 停止悬停动画 */
    private stopHoverAnimation(): void {
        if (this._hoverAnimation) {
            this._hoverAnimation.stop();
        }
        
        if (this.cardContainer) {
            tween(this.cardContainer)
                .to(0.2, { 
                    scale: new Vec3(1, 1, 1),
                    position: new Vec3(this.cardContainer.position.x, this.cardContainer.position.y - 5, 0)
                })
                .start();
        }
    }

    // ==================== 事件处理 ====================
    
    /** 进入房间按钮点击 */
    private onEnterButtonClick(): void {
        if (this._roomInfo && this._onEnterRoom) {
            // 播放点击动画
            this.playClickAnimation();
            
            // 延迟执行，让动画播放完
            this.scheduleOnce(() => {
                this._onEnterRoom(this._roomInfo.roomId);
            }, 0.1);
        }
    }
    
    /** 详情按钮点击 */
    private onDetailsButtonClick(): void {
        if (this._roomInfo && this._onRoomDetails) {
            this._onRoomDetails(this._roomInfo.roomId);
        }
    }
    
    /** 卡片触摸开始 */
    private onCardTouchStart(): void {
        this.playHoverAnimation();
    }
    
    /** 卡片触摸结束 */
    private onCardTouchEnd(): void {
        this.stopHoverAnimation();
        
        // 如果是点击卡片，进入房间
        if (this._roomInfo && this._onEnterRoom) {
            this.scheduleOnce(() => {
                this._onEnterRoom(this._roomInfo.roomId);
            }, 0.1);
        }
    }
    
    /** 卡片触摸取消 */
    private onCardTouchCancel(): void {
        this.stopHoverAnimation();
    }
    
    /** 播放点击动画 */
    private playClickAnimation(): void {
        if (!this.cardContainer) return;
        
        tween(this.cardContainer)
            .to(0.1, { scale: new Vec3(0.95, 0.95, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有动画
        if (this._pulseAnimation) {
            this._pulseAnimation.stop();
            this._pulseAnimation = null;
        }
        
        if (this._hoverAnimation) {
            this._hoverAnimation.stop();
            this._hoverAnimation = null;
        }
        
        // 清理事件监听
        if (this.enterButton) {
            this.enterButton.node.off(Button.EventType.CLICK, this.onEnterButtonClick, this);
        }
        
        if (this.detailsButton) {
            this.detailsButton.node.off(Button.EventType.CLICK, this.onDetailsButtonClick, this);
        }
        
        if (this.cardContainer) {
            this.cardContainer.off(Node.EventType.TOUCH_START, this.onCardTouchStart, this);
            this.cardContainer.off(Node.EventType.TOUCH_END, this.onCardTouchEnd, this);
            this.cardContainer.off(Node.EventType.TOUCH_CANCEL, this.onCardTouchCancel, this);
        }
    }

    // ==================== 工具方法 ====================
    
    /** 获取房间信息 */
    public getRoomInfo(): RoomInfo {
        return this._roomInfo;
    }
    
    /** 设置卡片可交互状态 */
    public setInteractable(interactable: boolean): void {
        if (this.enterButton) {
            this.enterButton.interactable = interactable;
        }
        
        if (this.detailsButton) {
            this.detailsButton.interactable = interactable;
        }
        
        // 设置卡片透明度
        if (this.cardContainer) {
            const opacity = interactable ? 255 : 128;
            this.cardContainer.getComponent(Sprite)?.setColor(new Color(255, 255, 255, opacity));
        }
    }
    
    /** 高亮显示卡片 */
    public highlight(highlight: boolean): void {
        if (!this.cardContainer) return;
        
        const sprite = this.cardContainer.getComponent(Sprite);
        if (sprite) {
            if (highlight) {
                sprite.color = new Color(255, 240, 240); // 淡红色高亮
            } else {
                sprite.color = new Color(255, 255, 255); // 恢复白色
            }
        }
    }
}
