/**
 * 弹幕容器组件
 * 
 * 管理弹幕消息的显示、滚动、过滤和交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Prefab, instantiate, UITransform, ScrollView, Layout, tween, Vec3 } from 'cc';
import { 
    BarrageMessage as BarrageMessageData, 
    BarrageFilterConfig, 
    BarrageContainerProps,
    BarrageMessageType 
} from '../types/WatchTypes';
import { BarrageMessage, BarrageAnimationState } from './BarrageMessage';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';

const { ccclass, property } = _decorator;

/** 弹幕通道 */
interface BarrageTrack {
    trackIndex: number;
    isOccupied: boolean;
    lastMessageTime: number;
    currentMessage?: BarrageMessage;
}

@ccclass('BarrageContainer')
export class BarrageContainer extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    containerNode: Node = null;
    
    @property(ScrollView)
    scrollView: ScrollView = null;
    
    @property(Node)
    contentNode: Node = null;
    
    @property(Prefab)
    barrageMessagePrefab: Prefab = null;
    
    @property(Node)
    pauseIndicator: Node = null;
    
    // ==================== 配置属性 ====================
    
    @property
    maxMessages: number = 50;
    
    @property
    trackCount: number = 5;
    
    @property
    trackHeight: number = 40;
    
    @property
    scrollSpeed: number = 100;
    
    @property
    messageSpacing: number = 20;
    
    @property
    autoScroll: boolean = true;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    
    /** 弹幕消息列表 */
    private _messages: BarrageMessage[] = [];
    
    /** 弹幕通道 */
    private _tracks: BarrageTrack[] = [];
    
    /** 过滤配置 */
    private _filterConfig: BarrageFilterConfig = null;
    
    /** 容器尺寸 */
    private _containerWidth: number = 0;
    private _containerHeight: number = 0;
    
    /** 状态标志 */
    private _isPaused: boolean = false;
    private _isScrolling: boolean = true;
    
    /** 消息队列 */
    private _messageQueue: BarrageMessageData[] = [];
    private _isProcessingQueue: boolean = false;
    
    /** 性能优化 */
    private _lastUpdateTime: number = 0;
    private readonly UPDATE_INTERVAL = 100; // 100ms更新间隔

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeComponents();
        this.initializeTracks();
        this.setupEventListeners();
    }
    
    protected start() {
        this.updateContainerSize();
        this.startMessageProcessing();
    }
    
    protected update(dt: number) {
        const currentTime = Date.now();
        if (currentTime - this._lastUpdateTime > this.UPDATE_INTERVAL) {
            this.updateTracks();
            this._lastUpdateTime = currentTime;
        }
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化组件 */
    private initializeComponents(): void {
        this._stateManager = WatchStateManager.getInstance();
        
        // 获取过滤配置
        const state = this._stateManager.getState();
        this._filterConfig = state.barrageFilter;
        
        // 隐藏暂停指示器
        if (this.pauseIndicator) {
            this.pauseIndicator.active = false;
        }
    }
    
    /** 初始化弹幕通道 */
    private initializeTracks(): void {
        this._tracks = [];
        for (let i = 0; i < this.trackCount; i++) {
            this._tracks.push({
                trackIndex: i,
                isOccupied: false,
                lastMessageTime: 0,
                currentMessage: null
            });
        }
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听弹幕消息
        this._stateManager.on(WatchStateEvent.BARRAGE_RECEIVED, this.onBarrageReceived, this);
        
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
    }

    // ==================== 公共方法 ====================
    
    /** 添加弹幕消息 */
    public addMessage(messageData: BarrageMessageData): void {
        // 检查过滤条件
        if (!this.shouldShowMessage(messageData)) {
            return;
        }
        
        // 添加到队列
        this._messageQueue.push(messageData);
        
        // 处理队列
        this.processMessageQueue();
    }
    
    /** 暂停弹幕 */
    public pause(): void {
        if (this._isPaused) return;
        
        this._isPaused = true;
        
        // 暂停所有消息
        this._messages.forEach(message => {
            if (message.isScrolling()) {
                message.pauseScrolling();
            }
        });
        
        // 显示暂停指示器
        this.showPauseIndicator();
        
        // 触发暂停事件
        this.node.emit('barrage-paused');
    }
    
    /** 恢复弹幕 */
    public resume(): void {
        if (!this._isPaused) return;
        
        this._isPaused = false;
        
        // 恢复所有消息
        this._messages.forEach(message => {
            if (message.isPaused()) {
                message.resumeScrolling();
            }
        });
        
        // 隐藏暂停指示器
        this.hidePauseIndicator();
        
        // 继续处理队列
        this.processMessageQueue();
        
        // 触发恢复事件
        this.node.emit('barrage-resumed');
    }
    
    /** 清空弹幕 */
    public clear(): void {
        // 停止所有消息
        this._messages.forEach(message => {
            message.stopScrolling();
            message.node.destroy();
        });
        
        // 清空数组
        this._messages = [];
        this._messageQueue = [];
        
        // 重置通道
        this.initializeTracks();
        
        // 更新状态
        this._stateManager.dispatch({
            type: 'CLEAR_BARRAGE_MESSAGES',
            payload: null
        });
    }
    
    /** 设置过滤配置 */
    public setFilterConfig(config: BarrageFilterConfig): void {
        this._filterConfig = { ...config };
        
        // 更新状态
        this._stateManager.dispatch({
            type: 'UPDATE_BARRAGE_FILTER',
            payload: config
        });
        
        // 重新过滤现有消息
        this.applyFilter();
    }
    
    /** 设置自动滚动 */
    public setAutoScroll(autoScroll: boolean): void {
        this.autoScroll = autoScroll;
        this._isScrolling = autoScroll;
        
        if (!autoScroll) {
            this.pause();
        } else {
            this.resume();
        }
    }

    // ==================== 私有方法 ====================
    
    /** 处理消息队列 */
    private processMessageQueue(): void {
        if (this._isProcessingQueue || this._isPaused || this._messageQueue.length === 0) {
            return;
        }
        
        this._isProcessingQueue = true;
        
        // 批量处理消息，避免阻塞
        const batchSize = 3;
        let processed = 0;
        
        while (this._messageQueue.length > 0 && processed < batchSize) {
            const messageData = this._messageQueue.shift();
            if (messageData) {
                this.createAndShowMessage(messageData);
                processed++;
            }
        }
        
        this._isProcessingQueue = false;
        
        // 如果还有消息，延迟处理
        if (this._messageQueue.length > 0) {
            this.scheduleOnce(() => {
                this.processMessageQueue();
            }, 0.1);
        }
    }
    
    /** 创建并显示消息 */
    private createAndShowMessage(messageData: BarrageMessageData): void {
        if (!this.barrageMessagePrefab) {
            console.error('Barrage message prefab not set');
            return;
        }
        
        // 查找可用通道
        const track = this.findAvailableTrack();
        if (!track) {
            console.log('No available track for barrage message');
            return;
        }
        
        // 创建消息节点
        const messageNode = instantiate(this.barrageMessagePrefab);
        const messageComponent = messageNode.getComponent(BarrageMessage);
        
        if (!messageComponent) {
            console.error('BarrageMessage component not found');
            messageNode.destroy();
            return;
        }
        
        // 设置消息数据
        messageComponent.setMessageData(messageData);
        messageComponent.setScrollSpeed(this.scrollSpeed);
        
        // 添加到容器
        this.contentNode.addChild(messageNode);
        
        // 设置位置
        const y = this.getTrackY(track.trackIndex);
        messageNode.setPosition(0, y, 0);
        
        // 监听消息完成事件
        messageNode.on('barrage-scroll-complete', this.onMessageScrollComplete, this);
        messageNode.on('barrage-message-click', this.onMessageClick, this);
        
        // 开始滚动
        messageComponent.startScrolling(this._containerWidth);
        
        // 更新通道状态
        track.isOccupied = true;
        track.lastMessageTime = Date.now();
        track.currentMessage = messageComponent;
        
        // 添加到消息列表
        this._messages.push(messageComponent);
        
        // 限制消息数量
        this.limitMessageCount();
    }
    
    /** 查找可用通道 */
    private findAvailableTrack(): BarrageTrack | null {
        const currentTime = Date.now();
        const minInterval = 2000; // 2秒最小间隔
        
        // 优先查找空闲通道
        for (const track of this._tracks) {
            if (!track.isOccupied) {
                return track;
            }
        }
        
        // 查找可以复用的通道
        for (const track of this._tracks) {
            if (currentTime - track.lastMessageTime > minInterval) {
                track.isOccupied = false;
                track.currentMessage = null;
                return track;
            }
        }
        
        return null;
    }
    
    /** 获取通道Y坐标 */
    private getTrackY(trackIndex: number): number {
        const totalHeight = this.trackCount * this.trackHeight;
        const startY = totalHeight / 2 - this.trackHeight / 2;
        return startY - trackIndex * this.trackHeight;
    }
    
    /** 更新通道状态 */
    private updateTracks(): void {
        const currentTime = Date.now();
        
        this._tracks.forEach(track => {
            if (track.isOccupied && track.currentMessage) {
                // 检查消息是否还在滚动
                const state = track.currentMessage.getAnimationState();
                if (state === BarrageAnimationState.IDLE) {
                    track.isOccupied = false;
                    track.currentMessage = null;
                }
            }
        });
    }
    
    /** 限制消息数量 */
    private limitMessageCount(): void {
        while (this._messages.length > this.maxMessages) {
            const oldMessage = this._messages.shift();
            if (oldMessage) {
                oldMessage.stopScrolling();
                oldMessage.node.destroy();
            }
        }
    }
    
    /** 检查是否应该显示消息 */
    private shouldShowMessage(messageData: BarrageMessageData): boolean {
        if (!this._filterConfig) return true;
        
        // 检查消息类型过滤
        switch (messageData.type) {
            case BarrageMessageType.PREDICTION:
                if (!this._filterConfig.showPredictions) return false;
                break;
            case BarrageMessageType.SYSTEM:
                if (!this._filterConfig.showSystemMessages) return false;
                break;
            case BarrageMessageType.VIP:
                if (!this._filterConfig.showVipMessages) return false;
                break;
        }
        
        // 检查消息长度
        if (messageData.content.length > this._filterConfig.maxLength) {
            return false;
        }
        
        // 检查用户屏蔽
        if (this._filterConfig.hideBlockedUsers) {
            // 这里应该检查用户是否被屏蔽
            // 暂时跳过
        }
        
        return true;
    }
    
    /** 应用过滤器 */
    private applyFilter(): void {
        this._messages.forEach(message => {
            const messageData = message.getMessageData();
            if (!this.shouldShowMessage(messageData)) {
                message.playExitAnimation(() => {
                    message.node.destroy();
                });
            }
        });
        
        // 清理已销毁的消息
        this._messages = this._messages.filter(message => message.node && message.node.isValid);
    }
    
    /** 更新容器尺寸 */
    private updateContainerSize(): void {
        if (!this.containerNode) return;
        
        const transform = this.containerNode.getComponent(UITransform);
        if (transform) {
            this._containerWidth = transform.width;
            this._containerHeight = transform.height;
        }
    }
    
    /** 开始消息处理 */
    private startMessageProcessing(): void {
        // 定期处理消息队列
        this.schedule(() => {
            this.processMessageQueue();
        }, 0.5);
    }

    // ==================== 事件处理 ====================
    
    /** 弹幕消息接收处理 */
    private onBarrageReceived(messageData: BarrageMessageData): void {
        this.addMessage(messageData);
    }
    
    /** 状态变化处理 */
    private onStateChanged(event: any): void {
        const state = event.currentState;
        
        // 更新过滤配置
        if (state.barrageFilter !== this._filterConfig) {
            this._filterConfig = state.barrageFilter;
            this.applyFilter();
        }
    }
    
    /** 消息滚动完成处理 */
    private onMessageScrollComplete(message: BarrageMessage): void {
        // 从消息列表中移除
        const index = this._messages.indexOf(message);
        if (index >= 0) {
            this._messages.splice(index, 1);
        }
        
        // 销毁节点
        message.node.destroy();
    }
    
    /** 消息点击处理 */
    private onMessageClick(messageData: BarrageMessageData): void {
        console.log('Barrage message clicked:', messageData);
        
        // 触发点击事件
        this.node.emit('barrage-message-click', messageData);
    }
    
    /** 显示暂停指示器 */
    private showPauseIndicator(): void {
        if (this.pauseIndicator) {
            this.pauseIndicator.active = true;
            
            // 淡入动画
            this.pauseIndicator.setScale(0, 0, 1);
            tween(this.pauseIndicator)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 隐藏暂停指示器 */
    private hidePauseIndicator(): void {
        if (this.pauseIndicator && this.pauseIndicator.active) {
            // 淡出动画
            tween(this.pauseIndicator)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.pauseIndicator.active = false;
                })
                .start();
        }
    }

    // ==================== 工具方法 ====================
    
    /** 获取消息数量 */
    public getMessageCount(): number {
        return this._messages.length;
    }
    
    /** 获取队列长度 */
    public getQueueLength(): number {
        return this._messageQueue.length;
    }
    
    /** 检查是否暂停 */
    public isPaused(): boolean {
        return this._isPaused;
    }
    
    /** 获取可用通道数 */
    public getAvailableTrackCount(): number {
        return this._tracks.filter(track => !track.isOccupied).length;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有消息
        this._messages.forEach(message => {
            message.stopScrolling();
        });
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.BARRAGE_RECEIVED, this.onBarrageReceived, this);
            this._stateManager.off(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
        }
        
        // 清理定时器
        this.unscheduleAllCallbacks();
    }
}
