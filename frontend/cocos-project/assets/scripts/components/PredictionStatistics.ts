/**
 * 预测统计组件
 * 
 * 管理预测统计的实时显示、数据可视化、参与度展示、趋势分析等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, ProgressBar, Sprite, tween, Vec3, Color } from 'cc';
import { PredictionStatisticsData, PredictionStatisticsProps } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 图表类型枚举 */
export enum ChartType {
    BAR = 'bar',
    PIE = 'pie',
    LINE = 'line'
}

/** 统计数据项 */
interface StatisticItem {
    optionId: string;
    optionText: string;
    voteCount: number;
    percentage: number;
    color: Color;
}

@ccclass('PredictionStatistics')
export class PredictionStatistics extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    statisticsContainer: Node = null;
    
    @property(Node)
    chartContainer: Node = null;
    
    @property(Node)
    participationPanel: Node = null;
    
    @property(Node)
    trendPanel: Node = null;
    
    // 参与度显示
    @property(Label)
    totalParticipantsLabel: Label = null;
    
    @property(Label)
    participationRateLabel: Label = null;
    
    @property(ProgressBar)
    participationProgressBar: ProgressBar = null;
    
    // 统计信息
    @property(Label)
    totalVotesLabel: Label = null;
    
    @property(Label)
    leadingOptionLabel: Label = null;
    
    @property(Label)
    updateTimeLabel: Label = null;
    
    // 图表相关
    @property(Node)
    barChartContainer: Node = null;
    
    @property(Node)
    pieChartContainer: Node = null;
    
    @property(Node)
    trendChartContainer: Node = null;
    
    // ==================== 私有属性 ====================
    
    /** 统计数据 */
    private _statisticsData: PredictionStatisticsData = null;
    
    /** 配置属性 */
    private _props: PredictionStatisticsProps = null;
    
    /** 当前图表类型 */
    private _currentChartType: ChartType = ChartType.BAR;
    
    /** 统计项列表 */
    private _statisticItems: StatisticItem[] = [];
    
    /** 总投票数 */
    private _totalVotes: number = 0;
    
    /** 总参与人数 */
    private _totalParticipants: number = 0;
    
    /** 参与率 */
    private _participationRate: number = 0;
    
    /** 动画相关 */
    private _chartAnimations: any[] = [];
    private _updateAnimation: any = null;
    
    /** 颜色配置 */
    private readonly CHART_COLORS: Color[] = [
        new Color(255, 99, 132),   // 红色
        new Color(54, 162, 235),   // 蓝色
        new Color(255, 205, 86),   // 黄色
        new Color(75, 192, 192),   // 青色
        new Color(153, 102, 255),  // 紫色
        new Color(255, 159, 64),   // 橙色
        new Color(199, 199, 199),  // 灰色
        new Color(83, 102, 255)    // 靛蓝色
    ];

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeComponents();
        this.setupChartContainers();
    }
    
    protected update(dt: number) {
        this.updateRealTimeData();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 设置默认图表类型
        this.setChartType(ChartType.BAR);
        
        // 初始化参与度进度条
        if (this.participationProgressBar) {
            this.participationProgressBar.progress = 0;
        }
    }
    
    /** 设置图表容器 */
    private setupChartContainers(): void {
        // 隐藏所有图表容器
        if (this.barChartContainer) {
            this.barChartContainer.active = false;
        }
        
        if (this.pieChartContainer) {
            this.pieChartContainer.active = false;
        }
        
        if (this.trendChartContainer) {
            this.trendChartContainer.active = false;
        }
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: PredictionStatisticsProps): void {
        this._props = props;
    }
    
    /** 更新统计数据 */
    public updateStatistics(data: PredictionStatisticsData): void {
        this._statisticsData = data;
        this.processStatisticsData();
        this.updateDisplay();
        this.animateUpdate();
    }
    
    /** 设置图表类型 */
    public setChartType(chartType: ChartType): void {
        if (this._currentChartType === chartType) return;
        
        this._currentChartType = chartType;
        this.switchChartDisplay();
        this.renderChart();
    }
    
    /** 添加实时数据点 */
    public addDataPoint(optionId: string, voteCount: number): void {
        const item = this._statisticItems.find(item => item.optionId === optionId);
        if (item) {
            item.voteCount = voteCount;
            this.recalculatePercentages();
            this.updateDisplay();
        }
    }
    
    /** 获取领先选项 */
    public getLeadingOption(): StatisticItem | null {
        if (this._statisticItems.length === 0) return null;
        
        return this._statisticItems.reduce((leading, current) => 
            current.voteCount > leading.voteCount ? current : leading
        );
    }

    // ==================== 数据处理 ====================
    
    /** 处理统计数据 */
    private processStatisticsData(): void {
        if (!this._statisticsData) return;
        
        this._totalVotes = this._statisticsData.totalVotes;
        this._totalParticipants = this._statisticsData.totalParticipants;
        this._participationRate = this._statisticsData.participationRate;
        
        // 处理选项数据
        this._statisticItems = this._statisticsData.options.map((option, index) => ({
            optionId: option.optionId,
            optionText: option.optionText,
            voteCount: option.voteCount,
            percentage: this._totalVotes > 0 ? (option.voteCount / this._totalVotes) * 100 : 0,
            color: this.CHART_COLORS[index % this.CHART_COLORS.length]
        }));
        
        // 按投票数排序
        this._statisticItems.sort((a, b) => b.voteCount - a.voteCount);
    }
    
    /** 重新计算百分比 */
    private recalculatePercentages(): void {
        this._totalVotes = this._statisticItems.reduce((sum, item) => sum + item.voteCount, 0);
        
        this._statisticItems.forEach(item => {
            item.percentage = this._totalVotes > 0 ? (item.voteCount / this._totalVotes) * 100 : 0;
        });
        
        // 重新排序
        this._statisticItems.sort((a, b) => b.voteCount - a.voteCount);
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示 */
    private updateDisplay(): void {
        this.updateParticipationDisplay();
        this.updateStatisticsDisplay();
        this.renderChart();
    }
    
    /** 更新参与度显示 */
    private updateParticipationDisplay(): void {
        // 更新总参与人数
        if (this.totalParticipantsLabel) {
            this.totalParticipantsLabel.string = this.formatNumber(this._totalParticipants);
        }
        
        // 更新参与率
        if (this.participationRateLabel) {
            this.participationRateLabel.string = `${this._participationRate.toFixed(1)}%`;
        }
        
        // 更新参与度进度条
        if (this.participationProgressBar) {
            const targetProgress = this._participationRate / 100;
            this.animateProgressBar(this.participationProgressBar, targetProgress);
        }
    }
    
    /** 更新统计信息显示 */
    private updateStatisticsDisplay(): void {
        // 更新总投票数
        if (this.totalVotesLabel) {
            this.totalVotesLabel.string = this.formatNumber(this._totalVotes);
        }
        
        // 更新领先选项
        if (this.leadingOptionLabel) {
            const leadingOption = this.getLeadingOption();
            if (leadingOption) {
                this.leadingOptionLabel.string = `${leadingOption.optionText} (${leadingOption.percentage.toFixed(1)}%)`;
            } else {
                this.leadingOptionLabel.string = '暂无数据';
            }
        }
        
        // 更新时间
        if (this.updateTimeLabel) {
            const now = new Date();
            this.updateTimeLabel.string = `更新时间: ${now.toLocaleTimeString()}`;
        }
    }

    // ==================== 图表渲染 ====================
    
    /** 切换图表显示 */
    private switchChartDisplay(): void {
        // 隐藏所有图表
        if (this.barChartContainer) {
            this.barChartContainer.active = false;
        }
        
        if (this.pieChartContainer) {
            this.pieChartContainer.active = false;
        }
        
        if (this.trendChartContainer) {
            this.trendChartContainer.active = false;
        }
        
        // 显示当前图表
        switch (this._currentChartType) {
            case ChartType.BAR:
                if (this.barChartContainer) {
                    this.barChartContainer.active = true;
                }
                break;
                
            case ChartType.PIE:
                if (this.pieChartContainer) {
                    this.pieChartContainer.active = true;
                }
                break;
                
            case ChartType.LINE:
                if (this.trendChartContainer) {
                    this.trendChartContainer.active = true;
                }
                break;
        }
    }
    
    /** 渲染图表 */
    private renderChart(): void {
        switch (this._currentChartType) {
            case ChartType.BAR:
                this.renderBarChart();
                break;
                
            case ChartType.PIE:
                this.renderPieChart();
                break;
                
            case ChartType.LINE:
                this.renderTrendChart();
                break;
        }
    }
    
    /** 渲染柱状图 */
    private renderBarChart(): void {
        if (!this.barChartContainer) return;
        
        // 清空现有内容
        this.barChartContainer.removeAllChildren();
        
        const maxVotes = Math.max(...this._statisticItems.map(item => item.voteCount));
        const containerHeight = this.barChartContainer.getComponent('UITransform')?.height || 200;
        
        this._statisticItems.forEach((item, index) => {
            this.createBarChartItem(item, index, maxVotes, containerHeight);
        });
    }
    
    /** 创建柱状图项 */
    private createBarChartItem(item: StatisticItem, index: number, maxVotes: number, containerHeight: number): void {
        // 实际项目中需要创建柱状图的UI元素
        // 这里只是示例逻辑
        console.log(`Creating bar chart item: ${item.optionText} - ${item.voteCount} votes (${item.percentage.toFixed(1)}%)`);
        
        // 计算柱子高度
        const barHeight = maxVotes > 0 ? (item.voteCount / maxVotes) * containerHeight * 0.8 : 0;
        
        // 播放柱子增长动画
        this.animateBarGrowth(index, barHeight);
    }
    
    /** 渲染饼图 */
    private renderPieChart(): void {
        if (!this.pieChartContainer) return;
        
        console.log('Rendering pie chart with data:', this._statisticItems);
        
        // 实际项目中需要实现饼图渲染逻辑
        // 可以使用 Graphics 组件绘制饼图
        this.drawPieChart();
    }
    
    /** 绘制饼图 */
    private drawPieChart(): void {
        // 实际项目中需要使用 Graphics 组件绘制
        let startAngle = 0;
        
        this._statisticItems.forEach((item, index) => {
            const angle = (item.percentage / 100) * 360;
            console.log(`Pie slice ${index}: ${item.optionText} - ${angle.toFixed(1)}°`);
            
            // 绘制扇形
            this.drawPieSlice(startAngle, angle, item.color);
            startAngle += angle;
        });
    }
    
    /** 绘制饼图扇形 */
    private drawPieSlice(startAngle: number, angle: number, color: Color): void {
        // 实际项目中需要使用 Graphics 组件实现
        console.log(`Drawing pie slice: ${startAngle}° to ${startAngle + angle}°`);
    }
    
    /** 渲染趋势图 */
    private renderTrendChart(): void {
        if (!this.trendChartContainer) return;
        
        console.log('Rendering trend chart');
        
        // 实际项目中需要实现趋势图渲染逻辑
        // 可以显示投票数随时间的变化趋势
    }

    // ==================== 动画效果 ====================
    
    /** 播放更新动画 */
    private animateUpdate(): void {
        if (!this.statisticsContainer) return;
        
        // 停止之前的动画
        if (this._updateAnimation) {
            this._updateAnimation.stop();
        }
        
        // 轻微的脉冲动画表示数据更新
        this._updateAnimation = tween(this.statisticsContainer)
            .to(0.1, { scale: new Vec3(1.02, 1.02, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /** 播放进度条动画 */
    private animateProgressBar(progressBar: ProgressBar, targetProgress: number): void {
        const currentProgress = progressBar.progress;
        
        tween({ progress: currentProgress })
            .to(0.5, { progress: targetProgress })
            .call((target) => {
                progressBar.progress = target.progress;
            })
            .start();
    }
    
    /** 播放柱子增长动画 */
    private animateBarGrowth(index: number, targetHeight: number): void {
        // 实际项目中需要对柱状图元素进行动画
        console.log(`Animating bar ${index} to height ${targetHeight}`);
        
        // 延迟动画，创造波浪效果
        setTimeout(() => {
            // 执行增长动画
            console.log(`Bar ${index} animation started`);
        }, index * 100);
    }

    // ==================== 实时更新 ====================
    
    /** 更新实时数据 */
    private updateRealTimeData(): void {
        // 实际项目中可以在这里处理实时数据更新
        // 例如从网络管理器获取最新数据
    }
    
    /** 处理实时数据变化 */
    public onRealTimeDataChanged(optionId: string, newVoteCount: number): void {
        const item = this._statisticItems.find(item => item.optionId === optionId);
        if (item && item.voteCount !== newVoteCount) {
            item.voteCount = newVoteCount;
            this.recalculatePercentages();
            this.updateDisplay();
            
            // 触发数据变化回调
            if (this._props && this._props.onDataChanged) {
                this._props.onDataChanged(this._statisticItems);
            }
        }
    }

    // ==================== 工具方法 ====================
    
    /** 格式化数字 */
    private formatNumber(num: number): string {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toString();
        }
    }
    
    /** 获取统计数据 */
    public getStatisticsData(): PredictionStatisticsData {
        return this._statisticsData;
    }
    
    /** 获取统计项列表 */
    public getStatisticItems(): StatisticItem[] {
        return [...this._statisticItems];
    }
    
    /** 获取总投票数 */
    public getTotalVotes(): number {
        return this._totalVotes;
    }
    
    /** 获取参与率 */
    public getParticipationRate(): number {
        return this._participationRate;
    }
    
    /** 导出统计数据 */
    public exportData(): any {
        return {
            totalVotes: this._totalVotes,
            totalParticipants: this._totalParticipants,
            participationRate: this._participationRate,
            options: this._statisticItems.map(item => ({
                optionId: item.optionId,
                optionText: item.optionText,
                voteCount: item.voteCount,
                percentage: item.percentage
            })),
            timestamp: Date.now()
        };
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有动画
        this._chartAnimations.forEach(animation => {
            if (animation) {
                animation.stop();
            }
        });
        
        if (this._updateAnimation) {
            this._updateAnimation.stop();
        }
        
        // 清空数组
        this._chartAnimations = [];
        this._statisticItems = [];
    }
}
