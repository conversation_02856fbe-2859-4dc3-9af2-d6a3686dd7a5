import { _decorator, Component, Sprite, Label, Node, Button } from 'cc';
import { UserInfo } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 用户项组件 */
@ccclass('ViewerItem')
export class ViewerItem extends Component {
    @property(Sprite)
    avatarSprite: Sprite = null;
    
    @property(Label)
    nicknameLabel: Label = null;
    
    @property(Node)
    vipIndicator: Node = null;
    
    @property(Node)
    onlineIndicator: Node = null;
    
    @property(Button)
    followButton: Button = null;
    
    private _userInfo: UserInfo = null;
    
    public setUserInfo(userInfo: UserInfo): void {
        this._userInfo = userInfo;
        this.updateDisplay();
    }
    
    private updateDisplay(): void {
        if (!this._userInfo) return;
        
        // 更新昵称
        if (this.nicknameLabel) {
            this.nicknameLabel.string = this._userInfo.nickname;
        }
        
        // 更新VIP状态
        if (this.vipIndicator) {
            this.vipIndicator.active = this._userInfo.isVip;
        }
        
        // 更新在线状态
        if (this.onlineIndicator) {
            this.onlineIndicator.active = this._userInfo.isOnline;
        }
        
        // 加载头像
        this.loadAvatar(this._userInfo.avatar);
    }
    
    private loadAvatar(avatarUrl: string): void {
        // 实际项目中需要实现网络图片加载
        console.log('Loading avatar:', avatarUrl);
    }
    
    public getUserInfo(): UserInfo {
        return this._userInfo;
    }
}
