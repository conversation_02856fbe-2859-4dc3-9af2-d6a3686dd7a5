/**
 * 围观用户列表组件
 * 
 * 显示当前房间的围观用户列表和在线状态
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, ScrollView, Prefab, instantiate, Label, Sprite, Button, tween, Vec3 } from 'cc';
import { UserInfo, ViewerListProps } from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { ViewerItem } from './ViewerItem';

const { ccclass, property } = _decorator;



@ccclass('ViewerList')
export class ViewerList extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    listContainer: Node = null;
    
    @property(ScrollView)
    scrollView: ScrollView = null;
    
    @property(Node)
    contentNode: Node = null;
    
    @property(Prefab)
    viewerItemPrefab: Prefab = null;
    
    @property(Label)
    totalCountLabel: Label = null;
    
    @property(Label)
    onlineCountLabel: Label = null;
    
    @property(Button)
    refreshButton: Button = null;
    
    @property(Node)
    loadingIndicator: Node = null;
    
    @property(Node)
    emptyIndicator: Node = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    
    /** 用户列表 */
    private _viewers: UserInfo[] = [];
    
    /** 用户项组件列表 */
    private _viewerItems: ViewerItem[] = [];
    
    /** 配置属性 */
    private _props: ViewerListProps = null;
    
    /** 状态标志 */
    private _isLoading: boolean = false;
    private _isVisible: boolean = false;
    
    /** 更新定时器 */
    private _updateTimer: number = null;
    private readonly UPDATE_INTERVAL = 5000; // 5秒更新间隔

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeComponents();
    }
    
    protected start() {
        this.startAutoUpdate();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听用户列表变化
        this._stateManager.on(WatchStateEvent.VIEWER_LIST_UPDATED, this.onViewerListUpdated, this);
        this._stateManager.on(WatchStateEvent.VIEWER_JOINED, this.onViewerJoined, this);
        this._stateManager.on(WatchStateEvent.VIEWER_LEFT, this.onViewerLeft, this);
        
        // 刷新按钮事件
        if (this.refreshButton) {
            this.refreshButton.node.on(Button.EventType.CLICK, this.onRefreshClick, this);
        }
    }
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏加载指示器
        if (this.loadingIndicator) {
            this.loadingIndicator.active = false;
        }
        
        // 显示空状态指示器
        if (this.emptyIndicator) {
            this.emptyIndicator.active = true;
        }
        
        // 初始化计数标签
        this.updateCountLabels();
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: ViewerListProps): void {
        this._props = props;
    }
    
    /** 显示列表 */
    public show(): void {
        if (this._isVisible) return;
        
        this._isVisible = true;
        
        if (this.listContainer) {
            this.listContainer.active = true;
            
            // 淡入动画
            this.listContainer.setScale(0, 0, 1);
            tween(this.listContainer)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
        
        // 刷新数据
        this.refreshViewerList();
    }
    
    /** 隐藏列表 */
    public hide(): void {
        if (!this._isVisible) return;
        
        this._isVisible = false;
        
        if (this.listContainer) {
            // 淡出动画
            tween(this.listContainer)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.listContainer.active = false;
                })
                .start();
        }
    }
    
    /** 刷新用户列表 */
    public async refreshViewerList(): Promise<void> {
        if (this._isLoading) return;
        
        this._isLoading = true;
        this.showLoading();
        
        try {
            // 从状态管理器获取用户列表
            const state = this._stateManager.getState();
            const viewers = state.currentRoom?.viewers || [];
            
            this.setViewerList(viewers);
            
        } catch (error) {
            console.error('Failed to refresh viewer list:', error);
        } finally {
            this._isLoading = false;
            this.hideLoading();
        }
    }
    
    /** 设置用户列表 */
    public setViewerList(viewers: UserInfo[]): void {
        this._viewers = [...viewers];
        this.updateDisplay();
        this.updateCountLabels();
    }
    
    /** 添加用户 */
    public addViewer(userInfo: UserInfo): void {
        // 检查是否已存在
        const existingIndex = this._viewers.findIndex(viewer => viewer.userId === userInfo.userId);
        if (existingIndex >= 0) {
            // 更新现有用户信息
            this._viewers[existingIndex] = userInfo;
        } else {
            // 添加新用户
            this._viewers.push(userInfo);
        }
        
        this.updateDisplay();
        this.updateCountLabels();
        
        // 播放加入动画
        this.playJoinAnimation(userInfo);
    }
    
    /** 移除用户 */
    public removeViewer(userId: string): void {
        const index = this._viewers.findIndex(viewer => viewer.userId === userId);
        if (index >= 0) {
            const removedViewer = this._viewers.splice(index, 1)[0];
            this.updateDisplay();
            this.updateCountLabels();
            
            // 播放离开动画
            this.playLeaveAnimation(removedViewer);
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示 */
    private updateDisplay(): void {
        this.clearViewerItems();
        
        if (this._viewers.length === 0) {
            this.showEmptyState();
            return;
        }
        
        this.hideEmptyState();
        this.createViewerItems();
    }
    
    /** 创建用户项 */
    private createViewerItems(): void {
        if (!this.viewerItemPrefab || !this.contentNode) return;
        
        this._viewers.forEach((viewer, index) => {
            const itemNode = instantiate(this.viewerItemPrefab);
            const itemComponent = itemNode.getComponent(ViewerItem);
            
            if (itemComponent) {
                itemComponent.setUserInfo(viewer);
                
                // 设置点击事件
                const button = itemNode.getComponent(Button);
                if (button) {
                    button.node.on(Button.EventType.CLICK, () => {
                        this.onViewerClick(viewer);
                    });
                }
                
                // 添加到容器
                this.contentNode.addChild(itemNode);
                this._viewerItems.push(itemComponent);
                
                // 延迟显示动画
                this.scheduleOnce(() => {
                    this.playItemEnterAnimation(itemNode, index);
                }, index * 0.05);
            }
        });
    }
    
    /** 更新计数标签 */
    private updateCountLabels(): void {
        const totalCount = this._viewers.length;
        const onlineCount = this._viewers.filter(viewer => viewer.isOnline).length;
        
        if (this.totalCountLabel) {
            this.totalCountLabel.string = `总计: ${totalCount}`;
        }
        
        if (this.onlineCountLabel) {
            this.onlineCountLabel.string = `在线: ${onlineCount}`;
        }
    }
    
    /** 显示空状态 */
    private showEmptyState(): void {
        if (this.emptyIndicator) {
            this.emptyIndicator.active = true;
        }
    }
    
    /** 隐藏空状态 */
    private hideEmptyState(): void {
        if (this.emptyIndicator) {
            this.emptyIndicator.active = false;
        }
    }
    
    /** 显示加载状态 */
    private showLoading(): void {
        if (this.loadingIndicator) {
            this.loadingIndicator.active = true;
            
            // 旋转动画
            tween(this.loadingIndicator)
                .repeatForever(
                    tween().by(1, { angle: 360 })
                )
                .start();
        }
    }
    
    /** 隐藏加载状态 */
    private hideLoading(): void {
        if (this.loadingIndicator) {
            this.loadingIndicator.active = false;
            this.loadingIndicator.stopAllActions();
        }
    }

    // ==================== 动画效果 ====================
    
    /** 播放项目进入动画 */
    private playItemEnterAnimation(itemNode: Node, index: number): void {
        itemNode.setScale(0, 0, 1);
        itemNode.setPosition(itemNode.position.x + 50, itemNode.position.y, itemNode.position.z);
        
        tween(itemNode)
            .to(0.3, { 
                scale: new Vec3(1, 1, 1),
                position: new Vec3(itemNode.position.x - 50, itemNode.position.y, itemNode.position.z)
            })
            .start();
    }
    
    /** 播放加入动画 */
    private playJoinAnimation(userInfo: UserInfo): void {
        // 找到对应的用户项
        const itemComponent = this._viewerItems.find(item => 
            item.getUserInfo().userId === userInfo.userId
        );
        
        if (itemComponent) {
            // 高亮动画
            tween(itemComponent.node)
                .to(0.2, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .repeat(2)
                .start();
        }
    }
    
    /** 播放离开动画 */
    private playLeaveAnimation(userInfo: UserInfo): void {
        console.log('User left:', userInfo.nickname);
        
        // 可以在这里添加离开提示动画
        // 例如显示一个临时的"用户离开"消息
    }

    // ==================== 事件处理 ====================
    
    /** 用户列表更新处理 */
    private onViewerListUpdated(viewers: UserInfo[]): void {
        this.setViewerList(viewers);
    }
    
    /** 用户加入处理 */
    private onViewerJoined(userInfo: UserInfo): void {
        this.addViewer(userInfo);
    }
    
    /** 用户离开处理 */
    private onViewerLeft(userInfo: UserInfo): void {
        this.removeViewer(userInfo.userId);
    }
    
    /** 刷新按钮点击处理 */
    private onRefreshClick(): void {
        this.refreshViewerList();
    }
    
    /** 用户点击处理 */
    private onViewerClick(userInfo: UserInfo): void {
        console.log('Viewer clicked:', userInfo);
        
        // 触发用户点击事件
        this.node.emit('viewer-click', userInfo);
        
        // 如果有回调函数，调用它
        if (this._props && this._props.onViewerClick) {
            this._props.onViewerClick(userInfo);
        }
    }

    // ==================== 自动更新 ====================
    
    /** 开始自动更新 */
    private startAutoUpdate(): void {
        this.stopAutoUpdate();
        
        this._updateTimer = setInterval(() => {
            if (this._isVisible && !this._isLoading) {
                this.refreshViewerList();
            }
        }, this.UPDATE_INTERVAL);
    }
    
    /** 停止自动更新 */
    private stopAutoUpdate(): void {
        if (this._updateTimer) {
            clearInterval(this._updateTimer);
            this._updateTimer = null;
        }
    }

    // ==================== 辅助方法 ====================
    
    /** 清理用户项 */
    private clearViewerItems(): void {
        this._viewerItems.forEach(item => {
            if (item.node) {
                item.node.destroy();
            }
        });
        this._viewerItems = [];
    }
    
    /** 获取用户数量 */
    public getViewerCount(): number {
        return this._viewers.length;
    }
    
    /** 获取在线用户数量 */
    public getOnlineViewerCount(): number {
        return this._viewers.filter(viewer => viewer.isOnline).length;
    }
    
    /** 检查用户是否存在 */
    public hasViewer(userId: string): boolean {
        return this._viewers.some(viewer => viewer.userId === userId);
    }
    
    /** 获取用户信息 */
    public getViewer(userId: string): UserInfo | null {
        return this._viewers.find(viewer => viewer.userId === userId) || null;
    }
    
    /** 滚动到顶部 */
    public scrollToTop(): void {
        if (this.scrollView) {
            this.scrollView.scrollToTop(0.5);
        }
    }
    
    /** 滚动到底部 */
    public scrollToBottom(): void {
        if (this.scrollView) {
            this.scrollView.scrollToBottom(0.5);
        }
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止自动更新
        this.stopAutoUpdate();
        
        // 清理用户项
        this.clearViewerItems();
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.VIEWER_LIST_UPDATED, this.onViewerListUpdated, this);
            this._stateManager.off(WatchStateEvent.VIEWER_JOINED, this.onViewerJoined, this);
            this._stateManager.off(WatchStateEvent.VIEWER_LEFT, this.onViewerLeft, this);
        }
        
        if (this.refreshButton) {
            this.refreshButton.node.off(Button.EventType.CLICK, this.onRefreshClick, this);
        }
    }
}
