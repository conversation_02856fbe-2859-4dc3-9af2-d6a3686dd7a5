/**
 * 连接状态指示器组件
 * 
 * 显示WebSocket连接状态、网络质量、重连进度等信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, Sprite, ProgressBar, tween, Vec3, Color } from 'cc';
import { ConnectionStatus, NetworkQuality } from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { WatchNetworkManager } from '../managers/WatchNetworkManager';

const { ccclass, property } = _decorator;

@ccclass('ConnectionStatusIndicator')
export class ConnectionStatusIndicator extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    statusContainer: Node = null;
    
    @property(Label)
    statusLabel: Label = null;
    
    @property(Sprite)
    statusIcon: Sprite = null;
    
    @property(Label)
    networkQualityLabel: Label = null;
    
    @property(Sprite)
    networkQualityIcon: Sprite = null;
    
    @property(ProgressBar)
    reconnectProgress: ProgressBar = null;
    
    @property(Label)
    latencyLabel: Label = null;
    
    @property(Node)
    reconnectingIndicator: Node = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    // 动画相关
    private _pulseAnimation: any = null;
    private _reconnectAnimation: any = null;
    
    // 状态配置
    private readonly STATUS_CONFIG = {
        [ConnectionStatus.DISCONNECTED]: {
            text: '未连接',
            color: new Color(153, 153, 153), // 灰色
            icon: 'disconnected'
        },
        [ConnectionStatus.CONNECTING]: {
            text: '连接中',
            color: new Color(250, 173, 20), // 黄色
            icon: 'connecting'
        },
        [ConnectionStatus.CONNECTED]: {
            text: '已连接',
            color: new Color(82, 196, 26), // 绿色
            icon: 'connected'
        },
        [ConnectionStatus.RECONNECTING]: {
            text: '重连中',
            color: new Color(255, 87, 34), // 橙色
            icon: 'reconnecting'
        },
        [ConnectionStatus.ERROR]: {
            text: '连接错误',
            color: new Color(245, 34, 45), // 红色
            icon: 'error'
        }
    };
    
    private readonly QUALITY_CONFIG = {
        [NetworkQuality.EXCELLENT]: {
            text: '优秀',
            color: new Color(82, 196, 26), // 绿色
            icon: 'signal_4'
        },
        [NetworkQuality.GOOD]: {
            text: '良好',
            color: new Color(250, 173, 20), // 黄色
            icon: 'signal_3'
        },
        [NetworkQuality.POOR]: {
            text: '较差',
            color: new Color(255, 87, 34), // 橙色
            icon: 'signal_2'
        },
        [NetworkQuality.DISCONNECTED]: {
            text: '断开',
            color: new Color(153, 153, 153), // 灰色
            icon: 'signal_0'
        }
    };

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeDisplay();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听连接状态变化
        this._stateManager.on(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
        
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
    }
    
    /** 初始化显示 */
    private initializeDisplay(): void {
        // 隐藏重连指示器
        if (this.reconnectingIndicator) {
            this.reconnectingIndicator.active = false;
        }
        
        // 隐藏重连进度条
        if (this.reconnectProgress) {
            this.reconnectProgress.node.active = false;
        }
        
        // 更新初始状态
        this.updateDisplay();
    }

    // ==================== 事件处理 ====================
    
    /** 连接状态变化处理 */
    private onConnectionStatusChanged(event: any): void {
        const { previous, current } = event;
        console.log(`Connection status changed: ${previous} -> ${current}`);
        
        this.updateConnectionStatus(current);
        this.handleStatusTransition(previous, current);
    }
    
    /** 状态变化处理 */
    private onStateChanged(event: any): void {
        const state = event.currentState;
        
        // 更新网络质量
        this.updateNetworkQuality(state.networkQuality);
        
        // 更新延迟信息
        this.updateLatency();
    }
    
    /** 处理状态转换 */
    private handleStatusTransition(previous: ConnectionStatus, current: ConnectionStatus): void {
        // 停止之前的动画
        this.stopAllAnimations();
        
        switch (current) {
            case ConnectionStatus.CONNECTING:
                this.startConnectingAnimation();
                break;
                
            case ConnectionStatus.CONNECTED:
                this.playConnectedAnimation();
                break;
                
            case ConnectionStatus.RECONNECTING:
                this.startReconnectingAnimation();
                break;
                
            case ConnectionStatus.ERROR:
                this.playErrorAnimation();
                break;
                
            case ConnectionStatus.DISCONNECTED:
                this.hideReconnectingIndicator();
                break;
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示 */
    private updateDisplay(): void {
        const state = this._stateManager.getState();
        this.updateConnectionStatus(state.connectionStatus);
        this.updateNetworkQuality(state.networkQuality);
        this.updateLatency();
    }
    
    /** 更新连接状态 */
    private updateConnectionStatus(status: ConnectionStatus): void {
        const config = this.STATUS_CONFIG[status];
        if (!config) return;
        
        // 更新状态文本
        if (this.statusLabel) {
            this.statusLabel.string = config.text;
            this.statusLabel.color = config.color;
        }
        
        // 更新状态图标颜色
        if (this.statusIcon) {
            this.statusIcon.color = config.color;
        }
    }
    
    /** 更新网络质量 */
    private updateNetworkQuality(quality: NetworkQuality): void {
        const config = this.QUALITY_CONFIG[quality];
        if (!config) return;
        
        // 更新网络质量文本
        if (this.networkQualityLabel) {
            this.networkQualityLabel.string = config.text;
            this.networkQualityLabel.color = config.color;
        }
        
        // 更新网络质量图标
        if (this.networkQualityIcon) {
            this.networkQualityIcon.color = config.color;
        }
    }
    
    /** 更新延迟信息 */
    private updateLatency(): void {
        if (!this.latencyLabel || !this._networkManager) return;
        
        const latency = this._networkManager.getLatency();
        if (latency > 0) {
            this.latencyLabel.string = `${latency}ms`;
            
            // 根据延迟设置颜色
            if (latency < 100) {
                this.latencyLabel.color = new Color(82, 196, 26); // 绿色
            } else if (latency < 300) {
                this.latencyLabel.color = new Color(250, 173, 20); // 黄色
            } else {
                this.latencyLabel.color = new Color(255, 87, 34); // 橙色
            }
        } else {
            this.latencyLabel.string = '--';
            this.latencyLabel.color = new Color(153, 153, 153); // 灰色
        }
    }

    // ==================== 动画效果 ====================
    
    /** 开始连接动画 */
    private startConnectingAnimation(): void {
        if (!this.statusIcon) return;
        
        this._pulseAnimation = tween(this.statusIcon.node)
            .to(0.5, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.5, { scale: new Vec3(1, 1, 1) })
            .repeatForever()
            .start();
    }
    
    /** 播放连接成功动画 */
    private playConnectedAnimation(): void {
        if (!this.statusIcon) return;
        
        // 缩放动画
        tween(this.statusIcon.node)
            .to(0.2, { scale: new Vec3(1.3, 1.3, 1) })
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /** 开始重连动画 */
    private startReconnectingAnimation(): void {
        this.showReconnectingIndicator();
        
        if (this.statusIcon) {
            this._pulseAnimation = tween(this.statusIcon.node)
                .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.3, { scale: new Vec3(0.8, 0.8, 1) })
                .repeatForever()
                .start();
        }
        
        // 重连进度动画
        this.startReconnectProgressAnimation();
    }
    
    /** 播放错误动画 */
    private playErrorAnimation(): void {
        if (!this.statusIcon) return;
        
        // 震动动画
        const originalPos = this.statusIcon.node.position;
        tween(this.statusIcon.node)
            .to(0.1, { position: new Vec3(originalPos.x + 5, originalPos.y, originalPos.z) })
            .to(0.1, { position: new Vec3(originalPos.x - 5, originalPos.y, originalPos.z) })
            .to(0.1, { position: originalPos })
            .repeat(3)
            .start();
    }
    
    /** 开始重连进度动画 */
    private startReconnectProgressAnimation(): void {
        if (!this.reconnectProgress) return;
        
        this.reconnectProgress.node.active = true;
        this.reconnectProgress.progress = 0;
        
        this._reconnectAnimation = tween(this.reconnectProgress)
            .to(5.0, { progress: 1.0 }) // 5秒重连周期
            .call(() => {
                // 重连周期结束，重新开始
                this.reconnectProgress.progress = 0;
            })
            .repeatForever()
            .start();
    }
    
    /** 显示重连指示器 */
    private showReconnectingIndicator(): void {
        if (this.reconnectingIndicator) {
            this.reconnectingIndicator.active = true;
            
            // 淡入动画
            this.reconnectingIndicator.setScale(0, 0, 1);
            tween(this.reconnectingIndicator)
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 隐藏重连指示器 */
    private hideReconnectingIndicator(): void {
        if (this.reconnectingIndicator && this.reconnectingIndicator.active) {
            // 淡出动画
            tween(this.reconnectingIndicator)
                .to(0.3, { scale: new Vec3(0, 0, 1) })
                .call(() => {
                    this.reconnectingIndicator.active = false;
                })
                .start();
        }
        
        if (this.reconnectProgress) {
            this.reconnectProgress.node.active = false;
        }
    }
    
    /** 停止所有动画 */
    private stopAllAnimations(): void {
        if (this._pulseAnimation) {
            this._pulseAnimation.stop();
            this._pulseAnimation = null;
        }
        
        if (this._reconnectAnimation) {
            this._reconnectAnimation.stop();
            this._reconnectAnimation = null;
        }
    }

    // ==================== 公共方法 ====================
    
    /** 手动刷新显示 */
    public refresh(): void {
        this.updateDisplay();
    }
    
    /** 设置可见性 */
    public setVisible(visible: boolean): void {
        if (this.statusContainer) {
            this.statusContainer.active = visible;
        }
    }
    
    /** 获取当前连接状态 */
    public getCurrentStatus(): ConnectionStatus {
        return this._stateManager.getConnectionStatus();
    }
    
    /** 获取网络质量 */
    public getNetworkQuality(): NetworkQuality {
        const state = this._stateManager.getState();
        return state.networkQuality;
    }
    
    /** 触发重连 */
    public triggerReconnect(): void {
        if (this._networkManager) {
            this._networkManager.disconnect();
            this.scheduleOnce(() => {
                this._networkManager.connect();
            }, 1.0);
        }
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有动画
        this.stopAllAnimations();
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.CONNECTION_STATUS_CHANGED, this.onConnectionStatusChanged, this);
            this._stateManager.off(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
        }
    }

    // ==================== 调试方法 ====================
    
    /** 模拟连接状态变化（调试用） */
    public simulateStatusChange(status: ConnectionStatus): void {
        if (this._stateManager) {
            this._stateManager.dispatch({
                type: 'SET_CONNECTION_STATUS',
                payload: status
            });
        }
    }
    
    /** 模拟网络质量变化（调试用） */
    public simulateQualityChange(quality: NetworkQuality): void {
        if (this._stateManager) {
            this._stateManager.dispatch({
                type: 'SET_NETWORK_QUALITY',
                payload: quality
            });
        }
    }
}
