/**
 * 预测面板组件
 * 
 * 显示预测问题、选项和用户交互界面
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, Button, ProgressBar, tween, Vec3, Color } from 'cc';
import { 
    PredictionQuestion, 
    PredictionOption, 
    UserPrediction,
    PredictionPanelProps 
} from '../types/WatchTypes';
import { WatchStateManager, WatchStateEvent } from '../managers/WatchStateManager';
import { WatchNetworkManager } from '../managers/WatchNetworkManager';

const { ccclass, property } = _decorator;

/** 预测状态枚举 */
export enum PredictionState {
    WAITING = 'waiting',
    ACTIVE = 'active',
    SUBMITTED = 'submitted',
    ENDED = 'ended',
    RESULT_SHOWING = 'result_showing'
}

@ccclass('PredictionPanel')
export class PredictionPanel extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    panelContainer: Node = null;
    
    @property(Label)
    questionLabel: Label = null;
    
    @property(Label)
    timeLabel: Label = null;
    
    @property(ProgressBar)
    timeProgress: ProgressBar = null;
    
    @property(Node)
    optionsContainer: Node = null;
    
    @property(Node)
    optionButtonPrefab: Node = null;
    
    @property(Label)
    participantCountLabel: Label = null;
    
    @property(Node)
    resultContainer: Node = null;
    
    @property(Label)
    resultLabel: Label = null;
    
    @property(Button)
    submitButton: Button = null;
    
    // ==================== 私有属性 ====================
    
    private _stateManager: WatchStateManager = null;
    private _networkManager: WatchNetworkManager = null;
    
    /** 当前预测问题 */
    private _currentQuestion: PredictionQuestion = null;
    
    /** 用户选择 */
    private _selectedOption: string = null;
    
    /** 预测状态 */
    private _predictionState: PredictionState = PredictionState.WAITING;
    
    /** 选项按钮列表 */
    private _optionButtons: Button[] = [];
    
    /** 倒计时定时器 */
    private _countdownTimer: number = null;
    private _remainingTime: number = 0;
    
    /** 动画相关 */
    private _pulseAnimation: any = null;
    private _resultAnimation: any = null;
    
    /** 回调函数 */
    private _onPredict: (optionId: string) => void = null;

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeManagers();
        this.setupEventListeners();
        this.initializeComponents();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化管理器 */
    private initializeManagers(): void {
        this._stateManager = WatchStateManager.getInstance();
        this._networkManager = WatchNetworkManager.getInstance();
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 监听预测相关事件
        this._stateManager.on(WatchStateEvent.PREDICTION_STARTED, this.onPredictionStarted, this);
        this._stateManager.on(WatchStateEvent.PREDICTION_ENDED, this.onPredictionEnded, this);
        
        // 监听状态变化
        this._stateManager.on(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
        
        // 提交按钮事件
        if (this.submitButton) {
            this.submitButton.node.on(Button.EventType.CLICK, this.onSubmitClick, this);
        }
    }
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏面板
        this.hide();
        
        // 隐藏结果容器
        if (this.resultContainer) {
            this.resultContainer.active = false;
        }
        
        // 禁用提交按钮
        if (this.submitButton) {
            this.submitButton.interactable = false;
        }
    }

    // ==================== 公共方法 ====================
    
    /** 设置预测问题 */
    public setPredictionQuestion(question: PredictionQuestion, props?: PredictionPanelProps): void {
        this._currentQuestion = question;
        
        if (props) {
            this._onPredict = props.onPredict;
        }
        
        this.updateDisplay();
        this.createOptionButtons();
        this.startCountdown();
        this.show();
        
        this._predictionState = PredictionState.ACTIVE;
    }
    
    /** 显示面板 */
    public show(): void {
        if (!this.panelContainer) return;
        
        this.panelContainer.active = true;
        
        // 淡入动画
        this.panelContainer.setScale(0, 0, 1);
        tween(this.panelContainer)
            .to(0.3, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /** 隐藏面板 */
    public hide(): void {
        if (!this.panelContainer) return;
        
        // 淡出动画
        tween(this.panelContainer)
            .to(0.3, { scale: new Vec3(0, 0, 1) })
            .call(() => {
                this.panelContainer.active = false;
            })
            .start();
    }
    
    /** 提交预测 */
    public submitPrediction(): void {
        if (!this._selectedOption || !this._currentQuestion) {
            console.log('No option selected or no question available');
            return;
        }
        
        if (this._predictionState !== PredictionState.ACTIVE) {
            console.log('Prediction not active');
            return;
        }
        
        // 发送预测
        this._networkManager.submitPrediction(this._currentQuestion.id, this._selectedOption);
        
        // 更新状态
        this._predictionState = PredictionState.SUBMITTED;
        
        // 禁用所有选项
        this.disableAllOptions();
        
        // 显示提交成功
        this.showSubmissionSuccess();
        
        // 触发回调
        if (this._onPredict) {
            this._onPredict(this._selectedOption);
        }
    }
    
    /** 显示预测结果 */
    public showResult(correctAnswer: string, userPrediction?: string): void {
        this._predictionState = PredictionState.RESULT_SHOWING;
        
        // 停止倒计时
        this.stopCountdown();
        
        // 高亮正确答案
        this.highlightCorrectAnswer(correctAnswer);
        
        // 显示结果信息
        this.showResultInfo(correctAnswer, userPrediction);
        
        // 播放结果动画
        this.playResultAnimation(userPrediction === correctAnswer);
    }

    // ==================== 显示更新 ====================
    
    /** 更新显示内容 */
    private updateDisplay(): void {
        if (!this._currentQuestion) return;
        
        // 更新问题文本
        if (this.questionLabel) {
            this.questionLabel.string = this._currentQuestion.questionText;
        }
        
        // 更新参与人数
        this.updateParticipantCount();
        
        // 重置时间显示
        this._remainingTime = this._currentQuestion.timeLimit;
        this.updateTimeDisplay();
    }
    
    /** 创建选项按钮 */
    private createOptionButtons(): void {
        if (!this.optionsContainer || !this.optionButtonPrefab || !this._currentQuestion) {
            return;
        }
        
        // 清理现有按钮
        this.clearOptionButtons();
        
        // 创建新按钮
        this._currentQuestion.options.forEach((option, index) => {
            const buttonNode = cc.instantiate(this.optionButtonPrefab);
            const button = buttonNode.getComponent(Button);
            const label = buttonNode.getComponentInChildren(Label);
            
            if (button && label) {
                // 设置文本
                label.string = option.text;
                
                // 设置点击事件
                button.node.on(Button.EventType.CLICK, () => {
                    this.onOptionClick(option.id);
                });
                
                // 添加到容器
                this.optionsContainer.addChild(buttonNode);
                this._optionButtons.push(button);
                
                // 设置初始状态
                this.updateOptionDisplay(button, option);
            }
        });
    }
    
    /** 更新选项显示 */
    private updateOptionDisplay(button: Button, option: PredictionOption): void {
        const label = button.getComponentInChildren(Label);
        const progressBar = button.getComponentInChildren(ProgressBar);
        
        if (label) {
            label.string = `${option.text} (${option.count})`;
        }
        
        if (progressBar) {
            progressBar.progress = option.percentage / 100;
        }
        
        // 更新选中状态
        if (this._selectedOption === option.id) {
            button.node.color = new Color(255, 215, 0); // 金色
        } else {
            button.node.color = new Color(255, 255, 255); // 白色
        }
    }
    
    /** 更新参与人数 */
    private updateParticipantCount(): void {
        if (!this.participantCountLabel || !this._currentQuestion) return;
        
        const totalCount = this._currentQuestion.options.reduce((sum, option) => sum + option.count, 0);
        this.participantCountLabel.string = `${totalCount}人参与`;
    }
    
    /** 更新时间显示 */
    private updateTimeDisplay(): void {
        if (this.timeLabel) {
            this.timeLabel.string = `${this._remainingTime}s`;
        }
        
        if (this.timeProgress && this._currentQuestion) {
            const progress = this._remainingTime / this._currentQuestion.timeLimit;
            this.timeProgress.progress = progress;
            
            // 时间紧急时变红
            if (progress < 0.3) {
                this.timeProgress.node.color = new Color(245, 34, 45); // 红色
            } else if (progress < 0.6) {
                this.timeProgress.node.color = new Color(255, 87, 34); // 橙色
            } else {
                this.timeProgress.node.color = new Color(82, 196, 26); // 绿色
            }
        }
    }

    // ==================== 倒计时管理 ====================
    
    /** 开始倒计时 */
    private startCountdown(): void {
        this.stopCountdown();
        
        if (!this._currentQuestion) return;
        
        this._remainingTime = this._currentQuestion.timeLimit;
        
        this._countdownTimer = setInterval(() => {
            this._remainingTime--;
            this.updateTimeDisplay();
            
            // 时间紧急时播放脉冲动画
            if (this._remainingTime <= 10) {
                this.playUrgentAnimation();
            }
            
            // 时间到
            if (this._remainingTime <= 0) {
                this.onTimeUp();
            }
        }, 1000);
    }
    
    /** 停止倒计时 */
    private stopCountdown(): void {
        if (this._countdownTimer) {
            clearInterval(this._countdownTimer);
            this._countdownTimer = null;
        }
    }
    
    /** 时间到处理 */
    private onTimeUp(): void {
        this.stopCountdown();
        
        // 如果有选择，自动提交
        if (this._selectedOption && this._predictionState === PredictionState.ACTIVE) {
            this.submitPrediction();
        } else {
            // 没有选择，结束预测
            this._predictionState = PredictionState.ENDED;
            this.disableAllOptions();
        }
    }

    // ==================== 事件处理 ====================
    
    /** 选项点击处理 */
    private onOptionClick(optionId: string): void {
        if (this._predictionState !== PredictionState.ACTIVE) {
            return;
        }
        
        // 更新选择
        this._selectedOption = optionId;
        
        // 更新按钮状态
        this.updateOptionButtons();
        
        // 启用提交按钮
        if (this.submitButton) {
            this.submitButton.interactable = true;
        }
        
        // 播放选择动画
        this.playSelectionAnimation(optionId);
    }
    
    /** 提交按钮点击处理 */
    private onSubmitClick(): void {
        this.submitPrediction();
    }
    
    /** 预测开始处理 */
    private onPredictionStarted(question: PredictionQuestion): void {
        this.setPredictionQuestion(question);
    }
    
    /** 预测结束处理 */
    private onPredictionEnded(question: PredictionQuestion): void {
        if (this._currentQuestion && this._currentQuestion.id === question.id) {
            this._predictionState = PredictionState.ENDED;
            this.stopCountdown();
            this.disableAllOptions();
        }
    }
    
    /** 状态变化处理 */
    private onStateChanged(event: any): void {
        const state = event.currentState;
        
        // 更新当前预测
        if (state.currentPrediction !== this._currentQuestion) {
            if (state.currentPrediction) {
                this.setPredictionQuestion(state.currentPrediction);
            } else {
                this.hide();
            }
        }
    }

    // ==================== 动画效果 ====================
    
    /** 播放选择动画 */
    private playSelectionAnimation(optionId: string): void {
        const button = this._optionButtons.find(btn => {
            const label = btn.getComponentInChildren(Label);
            return label && label.string.includes(optionId);
        });
        
        if (button) {
            // 缩放动画
            tween(button.node)
                .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.1, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 播放紧急动画 */
    private playUrgentAnimation(): void {
        if (!this.timeProgress) return;
        
        this._pulseAnimation = tween(this.timeProgress.node)
            .to(0.2, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start();
    }
    
    /** 播放结果动画 */
    private playResultAnimation(isCorrect: boolean): void {
        if (!this.resultContainer) return;
        
        this.resultContainer.active = true;
        
        // 根据结果播放不同动画
        if (isCorrect) {
            // 正确：绿色闪烁
            this.resultContainer.color = new Color(82, 196, 26);
            this._resultAnimation = tween(this.resultContainer)
                .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.3, { scale: new Vec3(1, 1, 1) })
                .repeat(2)
                .start();
        } else {
            // 错误：红色震动
            this.resultContainer.color = new Color(245, 34, 45);
            const originalPos = this.resultContainer.position;
            this._resultAnimation = tween(this.resultContainer)
                .to(0.1, { position: new Vec3(originalPos.x + 10, originalPos.y, originalPos.z) })
                .to(0.1, { position: new Vec3(originalPos.x - 10, originalPos.y, originalPos.z) })
                .to(0.1, { position: originalPos })
                .repeat(3)
                .start();
        }
    }

    // ==================== 辅助方法 ====================
    
    /** 更新选项按钮状态 */
    private updateOptionButtons(): void {
        if (!this._currentQuestion) return;
        
        this._optionButtons.forEach((button, index) => {
            if (index < this._currentQuestion.options.length) {
                this.updateOptionDisplay(button, this._currentQuestion.options[index]);
            }
        });
    }
    
    /** 禁用所有选项 */
    private disableAllOptions(): void {
        this._optionButtons.forEach(button => {
            button.interactable = false;
        });
        
        if (this.submitButton) {
            this.submitButton.interactable = false;
        }
    }
    
    /** 高亮正确答案 */
    private highlightCorrectAnswer(correctAnswer: string): void {
        this._optionButtons.forEach(button => {
            const label = button.getComponentInChildren(Label);
            if (label && label.string.includes(correctAnswer)) {
                button.node.color = new Color(82, 196, 26); // 绿色
            }
        });
    }
    
    /** 显示结果信息 */
    private showResultInfo(correctAnswer: string, userPrediction?: string): void {
        if (!this.resultLabel) return;
        
        if (userPrediction === correctAnswer) {
            this.resultLabel.string = '预测正确！';
            this.resultLabel.color = new Color(82, 196, 26);
        } else {
            this.resultLabel.string = `正确答案：${correctAnswer}`;
            this.resultLabel.color = new Color(245, 34, 45);
        }
    }
    
    /** 显示提交成功 */
    private showSubmissionSuccess(): void {
        if (this.resultLabel) {
            this.resultLabel.string = '预测已提交';
            this.resultLabel.color = new Color(250, 173, 20);
        }
    }
    
    /** 清理选项按钮 */
    private clearOptionButtons(): void {
        this._optionButtons.forEach(button => {
            if (button.node) {
                button.node.destroy();
            }
        });
        this._optionButtons = [];
    }

    // ==================== 工具方法 ====================
    
    /** 获取当前预测状态 */
    public getPredictionState(): PredictionState {
        return this._predictionState;
    }
    
    /** 获取选中的选项 */
    public getSelectedOption(): string | null {
        return this._selectedOption;
    }
    
    /** 检查是否可以提交 */
    public canSubmit(): boolean {
        return this._selectedOption !== null && this._predictionState === PredictionState.ACTIVE;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止倒计时
        this.stopCountdown();
        
        // 停止动画
        if (this._pulseAnimation) {
            this._pulseAnimation.stop();
        }
        
        if (this._resultAnimation) {
            this._resultAnimation.stop();
        }
        
        // 清理按钮
        this.clearOptionButtons();
        
        // 移除事件监听
        if (this._stateManager) {
            this._stateManager.off(WatchStateEvent.PREDICTION_STARTED, this.onPredictionStarted, this);
            this._stateManager.off(WatchStateEvent.PREDICTION_ENDED, this.onPredictionEnded, this);
            this._stateManager.off(WatchStateEvent.STATE_CHANGED, this.onStateChanged, this);
        }
        
        if (this.submitButton) {
            this.submitButton.node.off(Button.EventType.CLICK, this.onSubmitClick, this);
        }
    }
}
