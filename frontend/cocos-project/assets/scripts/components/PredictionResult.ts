/**
 * 预测结果组件
 * 
 * 管理预测结果的展示、积分计算、排名显示、连击记录等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, Label, Sprite, ProgressBar, ParticleSystem, tween, Vec3, Color } from 'cc';
import { PredictionResultData, PredictionResultProps, UserRankingData } from '../types/WatchTypes';

const { ccclass, property } = _decorator;

/** 结果状态枚举 */
export enum ResultState {
    PENDING = 'pending',
    CORRECT = 'correct',
    INCORRECT = 'incorrect',
    TIMEOUT = 'timeout'
}

/** 积分动画类型 */
export enum ScoreAnimationType {
    GAIN = 'gain',
    LOSS = 'loss',
    BONUS = 'bonus',
    STREAK = 'streak'
}

@ccclass('PredictionResult')
export class PredictionResult extends Component {
    
    // ==================== 节点引用 ====================
    
    @property(Node)
    resultContainer: Node = null;
    
    @property(Node)
    resultPanel: Node = null;
    
    @property(Node)
    scorePanel: Node = null;
    
    @property(Node)
    rankingPanel: Node = null;
    
    @property(Node)
    streakPanel: Node = null;
    
    // 结果显示
    @property(Label)
    resultStatusLabel: Label = null;
    
    @property(Label)
    correctAnswerLabel: Label = null;
    
    @property(Label)
    userAnswerLabel: Label = null;
    
    @property(Sprite)
    resultIcon: Sprite = null;
    
    // 积分显示
    @property(Label)
    scoreChangeLabel: Label = null;
    
    @property(Label)
    totalScoreLabel: Label = null;
    
    @property(Label)
    bonusLabel: Label = null;
    
    @property(ProgressBar)
    scoreProgressBar: ProgressBar = null;
    
    // 排名显示
    @property(Label)
    currentRankLabel: Label = null;
    
    @property(Label)
    rankChangeLabel: Label = null;
    
    @property(Label)
    totalPlayersLabel: Label = null;
    
    // 连击显示
    @property(Label)
    streakCountLabel: Label = null;
    
    @property(Label)
    streakBonusLabel: Label = null;
    
    @property(Node)
    streakIndicator: Node = null;
    
    // 特效
    @property(ParticleSystem)
    successParticles: ParticleSystem = null;
    
    @property(ParticleSystem)
    streakParticles: ParticleSystem = null;
    
    @property(Node)
    scoreAnimationContainer: Node = null;
    
    // ==================== 私有属性 ====================
    
    /** 结果数据 */
    private _resultData: PredictionResultData = null;
    
    /** 配置属性 */
    private _props: PredictionResultProps = null;
    
    /** 当前结果状态 */
    private _resultState: ResultState = ResultState.PENDING;
    
    /** 积分相关 */
    private _scoreChange: number = 0;
    private _totalScore: number = 0;
    private _bonusScore: number = 0;
    
    /** 排名相关 */
    private _currentRank: number = 0;
    private _previousRank: number = 0;
    private _totalPlayers: number = 0;
    
    /** 连击相关 */
    private _streakCount: number = 0;
    private _streakBonus: number = 0;
    private _isNewStreak: boolean = false;
    
    /** 动画相关 */
    private _resultAnimation: any = null;
    private _scoreAnimations: any[] = [];
    private _rankAnimation: any = null;
    
    /** 显示状态 */
    private _isVisible: boolean = false;

    // ==================== 生命周期 ====================
    
    protected onLoad() {
        this.initializeComponents();
        this.setupEventListeners();
    }
    
    protected onDestroy() {
        this.cleanup();
    }

    // ==================== 初始化 ====================
    
    /** 初始化组件 */
    private initializeComponents(): void {
        // 隐藏结果面板
        if (this.resultPanel) {
            this.resultPanel.active = false;
        }
        
        // 隐藏连击指示器
        if (this.streakIndicator) {
            this.streakIndicator.active = false;
        }
        
        // 初始化进度条
        if (this.scoreProgressBar) {
            this.scoreProgressBar.progress = 0;
        }
    }
    
    /** 设置事件监听 */
    private setupEventListeners(): void {
        // 实际项目中可以添加按钮事件监听
    }

    // ==================== 公共方法 ====================
    
    /** 设置配置属性 */
    public setProps(props: PredictionResultProps): void {
        this._props = props;
    }
    
    /** 显示预测结果 */
    public showResult(resultData: PredictionResultData): void {
        this._resultData = resultData;
        this.processResultData();
        this.showResultPanel();
        this.playResultAnimation();
    }
    
    /** 隐藏结果面板 */
    public hideResult(): void {
        this.hideResultPanel();
    }
    
    /** 更新积分 */
    public updateScore(newScore: number, scoreChange: number): void {
        this._totalScore = newScore;
        this._scoreChange = scoreChange;
        
        this.updateScoreDisplay();
        this.playScoreAnimation(scoreChange > 0 ? ScoreAnimationType.GAIN : ScoreAnimationType.LOSS);
    }
    
    /** 更新排名 */
    public updateRanking(rankingData: UserRankingData): void {
        this._previousRank = this._currentRank;
        this._currentRank = rankingData.currentRank;
        this._totalPlayers = rankingData.totalPlayers;
        
        this.updateRankingDisplay();
        this.playRankingAnimation();
    }
    
    /** 更新连击 */
    public updateStreak(streakCount: number, streakBonus: number): void {
        const wasStreak = this._streakCount > 0;
        this._streakCount = streakCount;
        this._streakBonus = streakBonus;
        this._isNewStreak = !wasStreak && streakCount > 0;
        
        this.updateStreakDisplay();
        
        if (this._isNewStreak || streakCount > 0) {
            this.playStreakAnimation();
        }
    }

    // ==================== 数据处理 ====================
    
    /** 处理结果数据 */
    private processResultData(): void {
        if (!this._resultData) return;
        
        // 设置结果状态
        this._resultState = this._resultData.isCorrect ? ResultState.CORRECT : ResultState.INCORRECT;
        
        // 处理积分数据
        this._scoreChange = this._resultData.scoreChange;
        this._totalScore = this._resultData.totalScore;
        this._bonusScore = this._resultData.bonusScore || 0;
        
        // 处理排名数据
        if (this._resultData.ranking) {
            this._previousRank = this._currentRank;
            this._currentRank = this._resultData.ranking.currentRank;
            this._totalPlayers = this._resultData.ranking.totalPlayers;
        }
        
        // 处理连击数据
        if (this._resultData.streak) {
            this._streakCount = this._resultData.streak.count;
            this._streakBonus = this._resultData.streak.bonus;
        }
    }

    // ==================== 显示更新 ====================
    
    /** 更新结果显示 */
    private updateResultDisplay(): void {
        // 更新结果状态
        if (this.resultStatusLabel) {
            const statusText = this._resultState === ResultState.CORRECT ? '回答正确！' : '回答错误';
            this.resultStatusLabel.string = statusText;
            
            // 设置颜色
            const color = this._resultState === ResultState.CORRECT ? 
                new Color(34, 139, 34) : new Color(220, 20, 60);
            this.resultStatusLabel.color = color;
        }
        
        // 更新正确答案
        if (this.correctAnswerLabel && this._resultData) {
            this.correctAnswerLabel.string = `正确答案：${this._resultData.correctAnswer}`;
        }
        
        // 更新用户答案
        if (this.userAnswerLabel && this._resultData) {
            this.userAnswerLabel.string = `你的答案：${this._resultData.userAnswer}`;
        }
        
        // 更新结果图标
        this.updateResultIcon();
    }
    
    /** 更新结果图标 */
    private updateResultIcon(): void {
        if (!this.resultIcon) return;
        
        // 设置图标颜色
        const color = this._resultState === ResultState.CORRECT ? 
            new Color(34, 139, 34) : new Color(220, 20, 60);
        this.resultIcon.color = color;
        
        // 实际项目中需要设置不同的图标精灵
        console.log(`Setting result icon for state: ${this._resultState}`);
    }
    
    /** 更新积分显示 */
    private updateScoreDisplay(): void {
        // 更新积分变化
        if (this.scoreChangeLabel) {
            const prefix = this._scoreChange > 0 ? '+' : '';
            this.scoreChangeLabel.string = `${prefix}${this._scoreChange}`;
            
            // 设置颜色
            const color = this._scoreChange > 0 ? 
                new Color(34, 139, 34) : new Color(220, 20, 60);
            this.scoreChangeLabel.color = color;
        }
        
        // 更新总积分
        if (this.totalScoreLabel) {
            this.totalScoreLabel.string = this._totalScore.toString();
        }
        
        // 更新奖励积分
        if (this.bonusLabel && this._bonusScore > 0) {
            this.bonusLabel.string = `奖励: +${this._bonusScore}`;
            this.bonusLabel.node.active = true;
        } else if (this.bonusLabel) {
            this.bonusLabel.node.active = false;
        }
        
        // 更新积分进度条
        this.updateScoreProgress();
    }
    
    /** 更新积分进度条 */
    private updateScoreProgress(): void {
        if (!this.scoreProgressBar) return;
        
        // 实际项目中需要根据等级系统计算进度
        // 这里只是示例
        const progress = (this._totalScore % 1000) / 1000;
        
        tween({ progress: this.scoreProgressBar.progress })
            .to(0.5, { progress })
            .call((target) => {
                this.scoreProgressBar.progress = target.progress;
            })
            .start();
    }
    
    /** 更新排名显示 */
    private updateRankingDisplay(): void {
        // 更新当前排名
        if (this.currentRankLabel) {
            this.currentRankLabel.string = `第 ${this._currentRank} 名`;
        }
        
        // 更新排名变化
        if (this.rankChangeLabel) {
            const rankChange = this._previousRank - this._currentRank;
            if (rankChange > 0) {
                this.rankChangeLabel.string = `↑${rankChange}`;
                this.rankChangeLabel.color = new Color(34, 139, 34);
            } else if (rankChange < 0) {
                this.rankChangeLabel.string = `↓${Math.abs(rankChange)}`;
                this.rankChangeLabel.color = new Color(220, 20, 60);
            } else {
                this.rankChangeLabel.string = '—';
                this.rankChangeLabel.color = new Color(128, 128, 128);
            }
        }
        
        // 更新总玩家数
        if (this.totalPlayersLabel) {
            this.totalPlayersLabel.string = `/ ${this._totalPlayers}`;
        }
    }
    
    /** 更新连击显示 */
    private updateStreakDisplay(): void {
        // 更新连击数
        if (this.streakCountLabel) {
            this.streakCountLabel.string = `${this._streakCount}连击`;
        }
        
        // 更新连击奖励
        if (this.streakBonusLabel && this._streakBonus > 0) {
            this.streakBonusLabel.string = `连击奖励: +${this._streakBonus}`;
            this.streakBonusLabel.node.active = true;
        } else if (this.streakBonusLabel) {
            this.streakBonusLabel.node.active = false;
        }
        
        // 显示/隐藏连击指示器
        if (this.streakIndicator) {
            this.streakIndicator.active = this._streakCount > 0;
        }
    }

    // ==================== 面板管理 ====================
    
    /** 显示结果面板 */
    private showResultPanel(): void {
        if (!this.resultPanel) return;
        
        this._isVisible = true;
        this.resultPanel.active = true;
        
        // 更新所有显示
        this.updateResultDisplay();
        this.updateScoreDisplay();
        this.updateRankingDisplay();
        this.updateStreakDisplay();
        
        // 淡入动画
        this.resultPanel.setScale(0, 0, 1);
        this.resultPanel.opacity = 0;
        
        tween(this.resultPanel)
            .to(0.3, { 
                scale: new Vec3(1, 1, 1),
                opacity: 255
            })
            .start();
    }
    
    /** 隐藏结果面板 */
    private hideResultPanel(): void {
        if (!this.resultPanel || !this._isVisible) return;
        
        this._isVisible = false;
        
        // 淡出动画
        tween(this.resultPanel)
            .to(0.3, { 
                scale: new Vec3(0, 0, 1),
                opacity: 0
            })
            .call(() => {
                this.resultPanel.active = false;
            })
            .start();
    }

    // ==================== 动画效果 ====================
    
    /** 播放结果动画 */
    private playResultAnimation(): void {
        if (this._resultState === ResultState.CORRECT) {
            this.playSuccessAnimation();
        } else {
            this.playFailureAnimation();
        }
    }
    
    /** 播放成功动画 */
    private playSuccessAnimation(): void {
        // 播放成功粒子特效
        if (this.successParticles) {
            this.successParticles.play();
        }
        
        // 结果图标动画
        if (this.resultIcon) {
            tween(this.resultIcon.node)
                .to(0.2, { scale: new Vec3(1.3, 1.3, 1) })
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
        
        // 触发成功回调
        if (this._props && this._props.onSuccess) {
            this._props.onSuccess(this._resultData);
        }
    }
    
    /** 播放失败动画 */
    private playFailureAnimation(): void {
        // 结果图标震动动画
        if (this.resultIcon) {
            const originalPos = this.resultIcon.node.position.clone();
            tween(this.resultIcon.node)
                .to(0.05, { position: originalPos.add3f(5, 0, 0) })
                .to(0.05, { position: originalPos.add3f(-5, 0, 0) })
                .to(0.05, { position: originalPos.add3f(5, 0, 0) })
                .to(0.05, { position: originalPos })
                .start();
        }
        
        // 触发失败回调
        if (this._props && this._props.onFailure) {
            this._props.onFailure(this._resultData);
        }
    }
    
    /** 播放积分动画 */
    private playScoreAnimation(animationType: ScoreAnimationType): void {
        if (!this.scoreAnimationContainer) return;
        
        // 创建飞出的积分文本动画
        this.createFloatingScoreText(this._scoreChange, animationType);
        
        // 积分标签动画
        if (this.scoreChangeLabel) {
            tween(this.scoreChangeLabel.node)
                .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.1, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 创建浮动积分文本 */
    private createFloatingScoreText(scoreChange: number, animationType: ScoreAnimationType): void {
        // 实际项目中需要创建临时的文本节点
        // 播放从积分标签位置向上飞出的动画
        console.log(`Creating floating score text: ${scoreChange} (${animationType})`);
    }
    
    /** 播放排名动画 */
    private playRankingAnimation(): void {
        if (!this.currentRankLabel) return;
        
        const rankChange = this._previousRank - this._currentRank;
        
        if (rankChange !== 0) {
            // 排名变化动画
            tween(this.currentRankLabel.node)
                .to(0.2, { scale: new Vec3(1.1, 1.1, 1) })
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }
    
    /** 播放连击动画 */
    private playStreakAnimation(): void {
        // 播放连击粒子特效
        if (this.streakParticles && this._streakCount > 0) {
            this.streakParticles.play();
        }
        
        // 连击指示器动画
        if (this.streakIndicator && this._streakCount > 0) {
            tween(this.streakIndicator)
                .to(0.2, { scale: new Vec3(1.3, 1.3, 1) })
                .to(0.2, { scale: new Vec3(1, 1, 1) })
                .start();
        }
        
        // 连击数字动画
        if (this.streakCountLabel) {
            tween(this.streakCountLabel.node)
                .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
                .to(0.1, { scale: new Vec3(1, 1, 1) })
                .start();
        }
    }

    // ==================== 工具方法 ====================
    
    /** 获取结果数据 */
    public getResultData(): PredictionResultData {
        return this._resultData;
    }
    
    /** 获取结果状态 */
    public getResultState(): ResultState {
        return this._resultState;
    }
    
    /** 获取积分变化 */
    public getScoreChange(): number {
        return this._scoreChange;
    }
    
    /** 获取当前排名 */
    public getCurrentRank(): number {
        return this._currentRank;
    }
    
    /** 获取连击数 */
    public getStreakCount(): number {
        return this._streakCount;
    }
    
    /** 检查是否显示中 */
    public isVisible(): boolean {
        return this._isVisible;
    }

    // ==================== 清理 ====================
    
    /** 清理资源 */
    private cleanup(): void {
        // 停止所有动画
        if (this._resultAnimation) {
            this._resultAnimation.stop();
        }
        
        this._scoreAnimations.forEach(animation => {
            if (animation) {
                animation.stop();
            }
        });
        
        if (this._rankAnimation) {
            this._rankAnimation.stop();
        }
        
        // 停止粒子特效
        if (this.successParticles) {
            this.successParticles.stop();
        }
        
        if (this.streakParticles) {
            this.streakParticles.stop();
        }
    }
}
