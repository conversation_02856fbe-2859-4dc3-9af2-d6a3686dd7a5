import { _decorator, Component, Node, Label, Layout, UITransform, Font } from 'cc';

const { ccclass } = _decorator;

/**
 * UI安全助手
 * 提供UI组件的安全初始化和检查方法
 */
@ccclass('UISafetyHelper')
export class UISafetyHelper {
    
    /**
     * 安全地设置Label文本
     */
    public static safeSetLabelText(label: Label | null, text: string, fallbackText?: string): boolean {
        if (!label) {
            console.warn('[UISafetyHelper] Label组件为null');
            return false;
        }
        
        try {
            // 检查字体是否有效
            if (!label.font) {
                console.warn('[UISafetyHelper] Label组件缺少字体设置');
                // 尝试使用系统默认字体
                this.setDefaultFont(label);
            }
            
            label.string = text;
            return true;
        } catch (error) {
            console.error('[UISafetyHelper] 设置Label文本失败:', error);
            
            // 尝试设置fallback文本
            if (fallbackText) {
                try {
                    label.string = fallbackText;
                    return true;
                } catch (fallbackError) {
                    console.error('[UISafetyHelper] 设置fallback文本也失败:', fallbackError);
                }
            }
            
            return false;
        }
    }
    
    /**
     * 为Label设置默认字体
     */
    private static setDefaultFont(label: Label): void {
        try {
            // 在Cocos Creator中，设置为null会使用系统默认字体
            label.font = null;

            // 确保字体资源已准备好
            label.scheduleOnce(() => {
                // 延迟验证字体是否正确设置
                this.validateLabelFont(label);
            }, 0.1);

            console.log('[UISafetyHelper] 已设置默认字体');
        } catch (error) {
            console.error('[UISafetyHelper] 设置默认字体失败:', error);
        }
    }

    /**
     * 验证Label字体是否有效
     */
    private static validateLabelFont(label: Label): boolean {
        if (!label || !label.node || !label.node.isValid) {
            return false;
        }

        try {
            // 检查字体是否可以正常渲染
            const originalText = label.string;

            // 临时设置简单测试文本
            label.string = 'A';

            // 检查是否有渲染错误
            label.updateRenderData(true);

            // 恢复原始文本
            label.string = originalText;

            return true;
        } catch (error) {
            console.warn('[UISafetyHelper] 字体验证失败:', error);
            return false;
        }
    }

    /**
     * 安全地更新Label渲染数据
     */
    public static safeUpdateLabelRender(label: Label): boolean {
        if (!label || !label.node || !label.node.isValid) {
            return false;
        }

        try {
            // 检查字体是否有效
            if (!label.font) {
                this.setDefaultFont(label);
            }

            // 安全地更新渲染数据
            label.updateRenderData(true);
            return true;
        } catch (error) {
            console.warn('[UISafetyHelper] 更新Label渲染失败:', error);
            return false;
        }
    }
    
    /**
     * 安全地检查Layout组件
     */
    public static safeCheckLayout(layout: Layout | null): boolean {
        if (!layout) {
            return false;
        }
        
        try {
            const node = layout.node;
            if (!node) {
                console.warn('[UISafetyHelper] Layout组件的节点为null');
                return false;
            }
            
            // 检查所有子节点是否有效
            const children = node.children;
            for (let i = 0; i < children.length; i++) {
                const child = children[i];
                if (!child) {
                    console.warn(`[UISafetyHelper] Layout子节点[${i}]为null`);
                    continue;
                }
                
                const uiTransform = child.getComponent(UITransform);
                if (!uiTransform) {
                    console.warn(`[UISafetyHelper] Layout子节点[${i}]缺少UITransform组件`);
                    // 可以选择添加UITransform组件
                    child.addComponent(UITransform);
                }
            }
            
            return true;
        } catch (error) {
            console.error('[UISafetyHelper] 检查Layout组件失败:', error);
            return false;
        }
    }
    
    /**
     * 安全地启用Layout组件
     */
    public static safeEnableLayout(layout: Layout | null): boolean {
        if (!this.safeCheckLayout(layout)) {
            console.warn('[UISafetyHelper] Layout组件检查失败，跳过启用');
            return false;
        }

        try {
            if (layout && !layout.enabled) {
                // 延迟启用，确保所有子节点都已准备好
                layout.scheduleOnce(() => {
                    try {
                        layout.enabled = true;
                        // 强制更新Layout
                        layout.updateLayout();
                    } catch (enableError) {
                        console.error('[UISafetyHelper] 延迟启用Layout失败:', enableError);
                    }
                }, 0.1);
            }
            return true;
        } catch (error) {
            console.error('[UISafetyHelper] 启用Layout组件失败:', error);
            return false;
        }
    }
    
    /**
     * 安全地更新Layout
     */
    public static safeUpdateLayout(layout: Layout | null): boolean {
        if (!this.safeCheckLayout(layout)) {
            return false;
        }

        try {
            if (layout && layout.enabled) {
                // 验证所有子节点的UITransform组件
                const node = layout.node;
                if (node && node.isValid) {
                    for (let i = 0; i < node.children.length; i++) {
                        const child = node.children[i];
                        if (!child || !child.isValid) {
                            continue;
                        }

                        const uiTransform = child.getComponent(UITransform);
                        if (!uiTransform) {
                            console.warn(`[UISafetyHelper] 子节点[${i}]缺少UITransform，添加组件`);
                            child.addComponent(UITransform);
                        }
                    }
                }

                layout.updateLayout();
                return true;
            }
            return false;
        } catch (error) {
            console.error('[UISafetyHelper] 更新Layout失败:', error);
            return false;
        }
    }
    
    /**
     * 检查节点是否安全可用
     */
    public static isNodeSafe(node: Node | null): boolean {
        if (!node) {
            return false;
        }
        
        try {
            // 检查节点是否有效
            if (!node.isValid) {
                return false;
            }
            
            // 检查UITransform组件
            const uiTransform = node.getComponent(UITransform);
            if (!uiTransform) {
                console.warn('[UISafetyHelper] 节点缺少UITransform组件');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('[UISafetyHelper] 检查节点安全性失败:', error);
            return false;
        }
    }
    
    /**
     * 延迟执行UI操作，确保组件已准备好
     */
    public static delayedUIOperation(component: Component, operation: () => void, delay: number = 0.1): void {
        if (!component || !component.isValid) {
            console.warn('[UISafetyHelper] 组件无效，跳过延迟操作');
            return;
        }
        
        component.scheduleOnce(() => {
            try {
                if (component.isValid) {
                    operation();
                }
            } catch (error) {
                console.error('[UISafetyHelper] 延迟UI操作失败:', error);
            }
        }, delay);
    }
    
    /**
     * 批量检查UI组件
     */
    public static batchCheckUIComponents(components: Component[]): { valid: Component[], invalid: Component[] } {
        const valid: Component[] = [];
        const invalid: Component[] = [];
        
        components.forEach(component => {
            if (component && component.isValid && component.node && component.node.isValid) {
                valid.push(component);
            } else {
                invalid.push(component);
            }
        });
        
        if (invalid.length > 0) {
            console.warn(`[UISafetyHelper] 发现${invalid.length}个无效的UI组件`);
        }
        
        return { valid, invalid };
    }
}
