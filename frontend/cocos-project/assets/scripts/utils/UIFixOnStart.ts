import { _decorator, Component, Node, Label, Layout, UITransform, director } from 'cc';
import { UISafetyHelper } from './UISafetyHelper';

const { ccclass, property } = _decorator;

/**
 * UI启动修复组件
 * 在场景启动时自动检查和修复常见的UI问题
 */
@ccclass('UIFixOnStart')
export class UIFixOnStart extends Component {
    
    @property({
        tooltip: '是否修复Label组件的字体问题'
    })
    fixLabelFonts: boolean = true;
    
    @property({
        tooltip: '是否修复Layout组件问题'
    })
    fixLayouts: boolean = true;
    
    @property({
        tooltip: '是否添加缺失的UITransform组件'
    })
    addMissingUITransforms: boolean = true;
    
    @property({
        tooltip: '修复延迟时间（秒）'
    })
    fixDelay: number = 0.2;
    
    protected start(): void {
        // 延迟执行修复，确保所有组件都已初始化
        this.scheduleOnce(() => {
            this.performUIFixes();
        }, this.fixDelay);
    }
    
    /**
     * 执行UI修复
     */
    private performUIFixes(): void {
        console.log('[UIFixOnStart] 开始执行UI修复...');
        
        const scene = director.getScene();
        if (!scene) {
            console.warn('[UIFixOnStart] 无法获取当前场景');
            return;
        }
        
        let fixCount = 0;
        
        try {
            // 修复Label组件
            if (this.fixLabelFonts) {
                fixCount += this.fixLabelsInNode(scene);
            }
            
            // 修复Layout组件
            if (this.fixLayouts) {
                fixCount += this.fixLayoutsInNode(scene);
            }
            
            // 添加缺失的UITransform组件
            if (this.addMissingUITransforms) {
                fixCount += this.addMissingUITransformsInNode(scene);
            }
            
            console.log(`[UIFixOnStart] UI修复完成，共修复 ${fixCount} 个问题`);
            
        } catch (error) {
            console.error('[UIFixOnStart] UI修复过程中发生错误:', error);
        }
    }
    
    /**
     * 修复节点中的Label组件
     */
    private fixLabelsInNode(node: Node): number {
        let fixCount = 0;
        
        // 检查当前节点的Label组件
        const label = node.getComponent(Label);
        if (label) {
            if (!label.font) {
                console.warn(`[UIFixOnStart] 节点 ${node.name} 的Label组件缺少字体`);
                // 这里可以设置一个默认字体
                fixCount++;
            }
            
            // 检查Label的字符串是否会导致渲染问题
            if (label.string && label.string.length > 1000) {
                console.warn(`[UIFixOnStart] 节点 ${node.name} 的Label文本过长，可能影响性能`);
                label.string = label.string.substring(0, 1000) + '...';
                fixCount++;
            }
        }
        
        // 递归检查子节点
        node.children.forEach(child => {
            fixCount += this.fixLabelsInNode(child);
        });
        
        return fixCount;
    }
    
    /**
     * 修复节点中的Layout组件
     */
    private fixLayoutsInNode(node: Node): number {
        let fixCount = 0;
        
        // 检查当前节点的Layout组件
        const layout = node.getComponent(Layout);
        if (layout) {
            // 暂时禁用Layout，稍后安全启用
            if (layout.enabled) {
                layout.enabled = false;
                
                // 延迟启用Layout
                this.scheduleOnce(() => {
                    if (UISafetyHelper.safeCheckLayout(layout)) {
                        UISafetyHelper.safeEnableLayout(layout);
                        console.log(`[UIFixOnStart] 修复了节点 ${node.name} 的Layout组件`);
                    }
                }, 0.1);
                
                fixCount++;
            }
        }
        
        // 递归检查子节点
        node.children.forEach(child => {
            fixCount += this.fixLayoutsInNode(child);
        });
        
        return fixCount;
    }
    
    /**
     * 为缺失UITransform的节点添加组件
     */
    private addMissingUITransformsInNode(node: Node): number {
        let fixCount = 0;
        
        // 检查当前节点是否需要UITransform
        if (this.nodeNeedsUITransform(node)) {
            const uiTransform = node.getComponent(UITransform);
            if (!uiTransform) {
                node.addComponent(UITransform);
                console.log(`[UIFixOnStart] 为节点 ${node.name} 添加了UITransform组件`);
                fixCount++;
            }
        }
        
        // 递归检查子节点
        node.children.forEach(child => {
            fixCount += this.addMissingUITransformsInNode(child);
        });
        
        return fixCount;
    }
    
    /**
     * 判断节点是否需要UITransform组件
     */
    private nodeNeedsUITransform(node: Node): boolean {
        // 如果节点有UI相关组件，则需要UITransform
        const uiComponents = [
            Label,
            Layout,
            // 可以添加更多UI组件类型
        ];
        
        for (const ComponentType of uiComponents) {
            if (node.getComponent(ComponentType)) {
                return true;
            }
        }
        
        // 如果父节点有Layout组件，子节点也需要UITransform
        if (node.parent) {
            const parentLayout = node.parent.getComponent(Layout);
            if (parentLayout) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 手动触发UI修复
     */
    public manualFix(): void {
        this.performUIFixes();
    }
    
    /**
     * 获取修复统计信息
     */
    public getFixStats(): { labels: number, layouts: number, transforms: number } {
        const scene = director.getScene();
        if (!scene) {
            return { labels: 0, layouts: 0, transforms: 0 };
        }
        
        return {
            labels: this.countLabelsInNode(scene),
            layouts: this.countLayoutsInNode(scene),
            transforms: this.countUITransformsInNode(scene)
        };
    }
    
    private countLabelsInNode(node: Node): number {
        let count = node.getComponent(Label) ? 1 : 0;
        node.children.forEach(child => {
            count += this.countLabelsInNode(child);
        });
        return count;
    }
    
    private countLayoutsInNode(node: Node): number {
        let count = node.getComponent(Layout) ? 1 : 0;
        node.children.forEach(child => {
            count += this.countLayoutsInNode(child);
        });
        return count;
    }
    
    private countUITransformsInNode(node: Node): number {
        let count = node.getComponent(UITransform) ? 1 : 0;
        node.children.forEach(child => {
            count += this.countUITransformsInNode(child);
        });
        return count;
    }
}
