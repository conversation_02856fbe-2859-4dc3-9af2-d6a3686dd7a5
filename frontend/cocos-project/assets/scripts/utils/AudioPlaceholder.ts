/**
 * 音频占位方案
 * 在音频系统完整调通前使用的临时实现
 */

import { _decorator, AudioClip, resources } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('AudioPlaceholder')
export class AudioPlaceholder {
    private static _instance: AudioPlaceholder = null;
    
    public static get instance(): AudioPlaceholder {
        if (!this._instance) {
            this._instance = new AudioPlaceholder();
        }
        return this._instance;
    }

    // 模拟音频数据
    private mockAudios: Map<string, AudioClip> = new Map();
    
    // 方言分类映射
    private dialectMap = {
        '东北话': { text: '东北方言，嘎哈呢', duration: 2.5, voice: 'male' },
        '四川话': { text: '四川方言，巴适得板', duration: 1.8, voice: 'male' },
        '广东话': { text: '广东方言，食咗饭未', duration: 2.2, voice: 'female' },
        '上海话': { text: '上海方言，阿拉弗晓得了', duration: 2.1, voice: 'female' },
        '山东话': { text: '山东方言，咋整啊', duration: 2.3, voice: 'male' },
        '河南话': { text: '河南方言，恁说啥咧', duration: 1.9, voice: 'male' }
    };

    /**
     * 初始化占位音频
     */
    public async initialize(): Promise<void> {
        console.log('[AudioPlaceholder] 初始化音频占位系统');
        
        // 创建简单的TTS占位
        for (const [dialect, info] of Object.entries(this.dialectMap)) {
            const mockClip = this.createMockAudioClip(info.text, info.duration);
            this.mockAudios.set(dialect, mockClip);
        }

        console.log('[AudioPlaceholder] 占位音频创建完成，共6种方言');
    }

    /**
     * 获取方言的占位音频
     */
    public async getDialectAudio(dialect: string): Promise<AudioClip | null> {
        return this.mockAudios.get(dialect) || null;
    }

    /**
     * 根据问题ID获取音频
     */
    public async getQuestionAudio(question: { id: string; dialect: string }): Promise<AudioClip | null> {
        return this.getDialectAudio(question.dialect);
    }

    /**
     * 模拟播放音频
     */
    public async playDialectAudio(dialect: string): Promise<number> {
        const audio = await this.getDialectAudio(dialect);
        if (!audio) return 0;

        // 模拟播放，返回音频时长
        const info = this.dialectMap[dialect];
        return info.duration;
    }

    /**
     * 创建模拟音频剪辑
     */
    private createMockAudioClip(text: string, duration: number): AudioClip {
        // 创建空白的AudioClip，包含时长信息
        const clip = new AudioClip();
        clip._setNativeAsset(null);
        clip.duration = duration;
        
        // 添加方言信息
        clip.addRef();
        
        return clip;
    }

    /**
     * 检查是否支持该方言
     */
    public isDialectSupported(dialect: string): boolean {
        return this.dialectMap.hasOwnProperty(dialect);
    }

    /**
     * 获取支持的方言列表
     */
    public getSupportedDialects(): string[] {
        return Object.keys(this.dialectMap);
    }

    /**
     * 获取方言基本信息
     */
    public getDialectInfo(dialect: string): { text: string; duration: number; voice: string } {
        return this.dialectMap[dialect] || null;
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        for (const clip of this.mockAudios.values()) {
            clip.decRef();
        }
        this.mockAudios.clear();
    }
}