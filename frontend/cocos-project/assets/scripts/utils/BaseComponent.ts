import { _decorator, Component } from 'cc';
import { MemoryManager } from './MemoryManager';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 基础组件类
 * 提供统一的内存管理和事件处理机制
 */
@ccclass('BaseComponent')
export class BaseComponent extends Component {
    protected _componentId: string;
    protected _memoryManager: MemoryManager;
    protected _eventManager: EventManager;
    protected _timers: number[] = [];
    
    protected onLoad(): void {
        // 生成唯一的组件ID
        this._componentId = `${this.constructor.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 获取管理器实例
        this._memoryManager = MemoryManager.getInstance();
        this._eventManager = EventManager.instance;
        
        // 注册基础清理回调
        this._memoryManager.registerCleanup(this._componentId, () => {
            this.performCleanup();
        });
        
        console.log(`[BaseComponent] 组件初始化: ${this._componentId}`);
    }
    
    protected onDestroy(): void {
        // 执行内存清理
        this._memoryManager.cleanupComponent(this._componentId);
        console.log(`[BaseComponent] 组件销毁: ${this._componentId}`);
    }
    
    /**
     * 安全的事件监听器注册
     */
    protected addEventListener(eventType: string, callback: Function, target?: any): void {
        if (this._eventManager) {
            this._eventManager.on(eventType, callback, target || this);
            
            // 注册到内存管理器
            this._memoryManager.registerEventListener(
                this._componentId,
                eventType,
                callback,
                target || this
            );
        }
    }
    
    /**
     * 安全的一次性事件监听器注册
     */
    protected addEventListenerOnce(eventType: string, callback: Function, target?: any): void {
        if (this._eventManager) {
            this._eventManager.once(eventType, callback, target || this);
            
            // 注册到内存管理器
            this._memoryManager.registerEventListener(
                this._componentId,
                eventType,
                callback,
                target || this
            );
        }
    }
    
    /**
     * 安全的事件监听器移除
     */
    protected removeEventListener(eventType: string, callback?: Function, target?: any): void {
        if (this._eventManager) {
            this._eventManager.off(eventType, callback, target || this);
        }
    }
    
    /**
     * 发送事件
     */
    protected emitEvent(eventType: string, data?: any): void {
        if (this._eventManager) {
            this._eventManager.emit(eventType, data);
        }
    }
    
    /**
     * 安全的定时器创建
     */
    protected createTimer(callback: Function, delay: number, isInterval: boolean = false): number {
        const timerId = isInterval 
            ? setInterval(callback, delay) 
            : setTimeout(callback, delay);
        
        this._timers.push(timerId);
        this._memoryManager.registerTimer(this._componentId, timerId);
        
        return timerId;
    }
    
    /**
     * 安全的定时器清理
     */
    protected clearTimer(timerId: number): void {
        clearTimeout(timerId);
        clearInterval(timerId);
        
        const index = this._timers.indexOf(timerId);
        if (index > -1) {
            this._timers.splice(index, 1);
        }
    }
    
    /**
     * 清理所有定时器
     */
    protected clearAllTimers(): void {
        this._timers.forEach(timerId => {
            clearTimeout(timerId);
            clearInterval(timerId);
        });
        this._timers = [];
    }
    
    /**
     * 执行组件清理
     * 子类可以重写此方法来添加自定义清理逻辑
     */
    protected performCleanup(): void {
        // 清理定时器
        this.clearAllTimers();
        
        // 清理事件监听器（由MemoryManager自动处理）
        
        // 子类可以重写此方法添加更多清理逻辑
        this.onCleanup();
    }
    
    /**
     * 子类重写此方法来添加自定义清理逻辑
     */
    protected onCleanup(): void {
        // 子类实现
    }
    
    /**
     * 获取组件ID
     */
    public getComponentId(): string {
        return this._componentId;
    }
    
    /**
     * 检查组件是否有效
     */
    protected isValid(): boolean {
        return this.node && this.node.isValid;
    }
    
    /**
     * 安全执行函数
     */
    protected safeExecute(func: Function, context?: any, ...args: any[]): any {
        try {
            if (!this.isValid()) {
                console.warn(`[BaseComponent] 组件已销毁，跳过执行: ${this._componentId}`);
                return;
            }
            
            return func.apply(context || this, args);
        } catch (error) {
            console.error(`[BaseComponent] 函数执行失败: ${this._componentId}`, error);
            return null;
        }
    }
    
    /**
     * 延迟执行函数
     */
    protected delayedExecute(func: Function, delay: number, context?: any, ...args: any[]): number {
        return this.createTimer(() => {
            this.safeExecute(func, context, ...args);
        }, delay);
    }
    
    /**
     * 周期性执行函数
     */
    protected intervalExecute(func: Function, interval: number, context?: any, ...args: any[]): number {
        return this.createTimer(() => {
            this.safeExecute(func, context, ...args);
        }, interval, true);
    }
}
