import { _decorator } from 'cc';
import { ErrorHand<PERSON>, GameError, ErrorType, ErrorSeverity } from './ErrorHandler';
import { EventManager } from '../managers/EventManager';
import { MemoryManager } from './MemoryManager';

const { ccclass } = _decorator;

/**
 * 全局错误处理中间件
 * 提供统一的错误捕获、处理和恢复机制
 */
@ccclass('GlobalErrorHandler')
export class GlobalErrorHandler {
    private static _instance: GlobalErrorHandler = null;
    private _errorHandler: ErrorHandler;
    private _eventManager: EventManager;
    private _memoryManager: MemoryManager;
    
    // 错误恢复策略
    private _recoveryStrategies: Map<ErrorType, Function[]> = new Map();
    
    // 错误统计
    private _errorStats: Map<ErrorType, number> = new Map();
    private _lastErrorTime: Map<ErrorType, number> = new Map();
    
    // 错误抑制（防止错误风暴）
    private _errorSuppressionTime: number = 5000; // 5秒内相同类型错误只报告一次
    
    public static getInstance(): GlobalErrorHandler {
        if (!this._instance) {
            this._instance = new GlobalErrorHandler();
        }
        return this._instance;
    }
    
    private constructor() {
        this.initialize();
    }
    
    /**
     * 初始化全局错误处理器
     */
    private initialize(): void {
        this._errorHandler = ErrorHandler.getInstance();
        this._eventManager = EventManager.instance;
        this._memoryManager = MemoryManager.getInstance();
        
        // 设置错误恢复策略
        this.setupRecoveryStrategies();
        
        // 注册全局错误监听器
        this.setupGlobalErrorListeners();
        
        // 设置Promise错误捕获
        this.setupPromiseErrorHandling();
        
        // 设置微信小游戏特定错误处理
        this.setupWeChatErrorHandling();
        
        console.log('[GlobalErrorHandler] 全局错误处理器初始化完成');
    }
    
    /**
     * 设置错误恢复策略
     */
    private setupRecoveryStrategies(): void {
        // 网络错误恢复策略
        this._recoveryStrategies.set(ErrorType.NETWORK, [
            () => this.retryNetworkOperation(),
            () => this.switchToOfflineMode(),
            () => this.showNetworkErrorDialog()
        ]);
        
        // 音频错误恢复策略
        this._recoveryStrategies.set(ErrorType.AUDIO, [
            () => this.clearAudioCache(),
            () => this.reloadAudioResources(),
            () => this.disableAudioFeatures()
        ]);
        
        // 内存错误恢复策略
        this._recoveryStrategies.set(ErrorType.SYSTEM, [
            () => this.performMemoryCleanup(),
            () => this.clearNonEssentialCaches(),
            () => this.restartApplication()
        ]);
        
        // 认证错误恢复策略
        this._recoveryStrategies.set(ErrorType.AUTH, [
            () => this.refreshAuthToken(),
            () => this.redirectToLogin(),
            () => this.enableGuestMode()
        ]);
        
        // 游戏逻辑错误恢复策略
        this._recoveryStrategies.set(ErrorType.GAME, [
            () => this.resetGameState(),
            () => this.loadLastValidState(),
            () => this.returnToMainMenu()
        ]);
    }
    
    /**
     * 设置全局错误监听器
     */
    private setupGlobalErrorListeners(): void {
        // 监听各种错误类型
        Object.values(ErrorType).forEach(errorType => {
            this._errorHandler.addErrorListener(errorType as ErrorType, (error: GameError) => {
                this.handleGlobalError(error);
            });
        });
        
        // 监听内存警告
        if (typeof wx !== 'undefined' && wx.onMemoryWarning) {
            wx.onMemoryWarning(() => {
                this.handleMemoryWarning();
            });
        }
    }
    
    /**
     * 设置Promise错误处理
     */
    private setupPromiseErrorHandling(): void {
        // 创建安全的Promise包装器
        (window as any).safePromise = <T>(promise: Promise<T>): Promise<T> => {
            return promise.catch((error) => {
                this.handleAsyncError(error);
                throw error; // 重新抛出以保持原有的错误处理流程
            });
        };
    }
    
    /**
     * 设置微信小游戏错误处理
     */
    private setupWeChatErrorHandling(): void {
        if (typeof wx !== 'undefined') {
            // 监听微信API调用错误
            const originalRequest = wx.request;
            wx.request = (options: any) => {
                const originalFail = options.fail;
                options.fail = (error: any) => {
                    this.handleWeChatAPIError('wx.request', error);
                    if (originalFail) originalFail(error);
                };
                return originalRequest(options);
            };
            
            // 监听微信登录错误
            const originalLogin = wx.login;
            wx.login = (options: any) => {
                const originalFail = options.fail;
                options.fail = (error: any) => {
                    this.handleWeChatAPIError('wx.login', error);
                    if (originalFail) originalFail(error);
                };
                return originalLogin(options);
            };
        }
    }
    
    /**
     * 处理全局错误
     */
    private handleGlobalError(error: GameError): void {
        // 检查错误抑制
        if (this.shouldSuppressError(error)) {
            return;
        }
        
        // 更新错误统计
        this.updateErrorStats(error);
        
        // 尝试错误恢复
        this.attemptErrorRecovery(error);
        
        // 记录错误上下文
        this.logErrorContext(error);
    }
    
    /**
     * 检查是否应该抑制错误
     */
    private shouldSuppressError(error: GameError): boolean {
        const lastErrorTime = this._lastErrorTime.get(error.type);
        const now = Date.now();
        
        if (lastErrorTime && (now - lastErrorTime) < this._errorSuppressionTime) {
            return true;
        }
        
        this._lastErrorTime.set(error.type, now);
        return false;
    }
    
    /**
     * 更新错误统计
     */
    private updateErrorStats(error: GameError): void {
        const currentCount = this._errorStats.get(error.type) || 0;
        this._errorStats.set(error.type, currentCount + 1);
        
        // 如果某类型错误过多，触发特殊处理
        if (currentCount > 10) {
            this.handleErrorStorm(error.type);
        }
    }
    
    /**
     * 尝试错误恢复
     */
    private attemptErrorRecovery(error: GameError): void {
        const strategies = this._recoveryStrategies.get(error.type);
        if (!strategies) return;
        
        // 根据错误严重程度选择恢复策略
        let strategyIndex = 0;
        switch (error.severity) {
            case ErrorSeverity.LOW:
                strategyIndex = 0;
                break;
            case ErrorSeverity.MEDIUM:
                strategyIndex = 1;
                break;
            case ErrorSeverity.HIGH:
            case ErrorSeverity.CRITICAL:
                strategyIndex = 2;
                break;
        }
        
        try {
            const strategy = strategies[strategyIndex];
            if (strategy) {
                console.log(`[GlobalErrorHandler] 执行恢复策略: ${error.type} - 策略${strategyIndex}`);
                strategy();
            }
        } catch (recoveryError) {
            console.error('[GlobalErrorHandler] 错误恢复策略执行失败:', recoveryError);
        }
    }
    
    /**
     * 记录错误上下文
     */
    private logErrorContext(error: GameError): void {
        const context = {
            timestamp: error.timestamp,
            type: error.type,
            severity: error.severity,
            message: error.message,
            userAgent: navigator.userAgent,
            url: window.location?.href,
            memoryStats: this._memoryManager.getMemoryStats(),
            errorStats: Object.fromEntries(this._errorStats)
        };
        
        console.log('[GlobalErrorHandler] 错误上下文:', context);
        
        // 发送错误上下文到事件系统
        this._eventManager?.emit('error_context_logged', context);
    }
    
    // 恢复策略实现
    private retryNetworkOperation(): void {
        console.log('[GlobalErrorHandler] 重试网络操作');
        this._eventManager?.emit('retry_network_operation');
    }
    
    private switchToOfflineMode(): void {
        console.log('[GlobalErrorHandler] 切换到离线模式');
        this._eventManager?.emit('switch_to_offline_mode');
    }
    
    private showNetworkErrorDialog(): void {
        console.log('[GlobalErrorHandler] 显示网络错误对话框');
        this._eventManager?.emit('show_network_error_dialog');
    }
    
    private clearAudioCache(): void {
        console.log('[GlobalErrorHandler] 清理音频缓存');
        this._eventManager?.emit('cleanup_audio_cache');
    }
    
    private reloadAudioResources(): void {
        console.log('[GlobalErrorHandler] 重新加载音频资源');
        this._eventManager?.emit('reload_audio_resources');
    }
    
    private disableAudioFeatures(): void {
        console.log('[GlobalErrorHandler] 禁用音频功能');
        this._eventManager?.emit('disable_audio_features');
    }
    
    private performMemoryCleanup(): void {
        console.log('[GlobalErrorHandler] 执行内存清理');
        this._memoryManager.performGlobalCleanup();
    }
    
    private clearNonEssentialCaches(): void {
        console.log('[GlobalErrorHandler] 清理非必要缓存');
        this._eventManager?.emit('clear_non_essential_caches');
    }
    
    private restartApplication(): void {
        console.log('[GlobalErrorHandler] 重启应用');
        this._eventManager?.emit('restart_application');
    }
    
    private refreshAuthToken(): void {
        console.log('[GlobalErrorHandler] 刷新认证令牌');
        this._eventManager?.emit('refresh_auth_token');
    }
    
    private redirectToLogin(): void {
        console.log('[GlobalErrorHandler] 重定向到登录页面');
        this._eventManager?.emit('redirect_to_login');
    }
    
    private enableGuestMode(): void {
        console.log('[GlobalErrorHandler] 启用访客模式');
        this._eventManager?.emit('enable_guest_mode');
    }
    
    private resetGameState(): void {
        console.log('[GlobalErrorHandler] 重置游戏状态');
        this._eventManager?.emit('reset_game_state');
    }
    
    private loadLastValidState(): void {
        console.log('[GlobalErrorHandler] 加载最后有效状态');
        this._eventManager?.emit('load_last_valid_state');
    }
    
    private returnToMainMenu(): void {
        console.log('[GlobalErrorHandler] 返回主菜单');
        this._eventManager?.emit('return_to_main_menu');
    }
    
    /**
     * 处理异步错误
     */
    private handleAsyncError(error: any): void {
        const gameError = new GameError(
            `异步操作失败: ${error.message || error}`,
            ErrorType.SYSTEM,
            ErrorSeverity.MEDIUM,
            undefined,
            { originalError: error }
        );
        
        this._errorHandler.handleError(gameError);
    }
    
    /**
     * 处理微信API错误
     */
    private handleWeChatAPIError(apiName: string, error: any): void {
        const gameError = new GameError(
            `微信API调用失败: ${apiName} - ${error.errMsg || error.message || error}`,
            ErrorType.SYSTEM,
            ErrorSeverity.HIGH,
            error.errCode,
            { apiName, originalError: error }
        );
        
        this._errorHandler.handleError(gameError);
    }
    
    /**
     * 处理内存警告
     */
    private handleMemoryWarning(): void {
        console.warn('[GlobalErrorHandler] 收到内存警告');
        
        const memoryError = new GameError(
            '设备内存不足，正在执行清理操作',
            ErrorType.SYSTEM,
            ErrorSeverity.HIGH,
            undefined,
            { memoryStats: this._memoryManager.getMemoryStats() }
        );
        
        this._errorHandler.handleError(memoryError);
    }
    
    /**
     * 处理错误风暴
     */
    private handleErrorStorm(errorType: ErrorType): void {
        console.error(`[GlobalErrorHandler] 检测到错误风暴: ${errorType}`);
        
        // 重置错误计数
        this._errorStats.set(errorType, 0);
        
        // 触发紧急恢复措施
        this._eventManager?.emit('error_storm_detected', { errorType });
        
        // 执行最严格的恢复策略
        const strategies = this._recoveryStrategies.get(errorType);
        if (strategies && strategies.length > 0) {
            const lastStrategy = strategies[strategies.length - 1];
            lastStrategy();
        }
    }
    
    /**
     * 获取错误统计
     */
    public getErrorStats(): Map<ErrorType, number> {
        return new Map(this._errorStats);
    }
    
    /**
     * 重置错误统计
     */
    public resetErrorStats(): void {
        this._errorStats.clear();
        this._lastErrorTime.clear();
        console.log('[GlobalErrorHandler] 错误统计已重置');
    }
}
