import { GameDifficulty, API_CONFIG, ERROR_CODES, STORAGE_KEYS } from '../constants/GameConstants';
import { IQuestionData, IApiResponse, IQuestionListResponse, IUserProfile, IGameSession } from '../data/GameData';
import { StorageManager } from './StorageManager';
import { EventManager } from '../managers/EventManager';

/**
 * 重试配置接口
 */
interface RetryConfig {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
    retryableErrors: string[];
}

/**
 * 请求配置接口
 */
interface RequestConfig {
    method: string;
    url: string;
    data?: any;
    headers?: Record<string, string>;
    timeout?: number;
    retryConfig?: Partial<RetryConfig>;
}

/**
 * 网络状态接口
 */
interface NetworkStatus {
    isOnline: boolean;
    networkType?: string;
    signalStrength?: number;
}

/**
 * 网络管理器
 * 负责与后端API的通信，包含重试机制和网络状态监控
 */
export class NetworkManager {
    private static _instance: NetworkManager;
    private _baseUrl: string = API_CONFIG.BASE_URL;
    private _timeout: number = API_CONFIG.TIMEOUT;

    // 重试配置
    private _defaultRetryConfig: RetryConfig = {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2,
        retryableErrors: ['timeout', 'network', '500', '502', '503', '504']
    };

    // 网络状态监控
    private _networkStatus: NetworkStatus = { isOnline: true };
    private _eventManager: EventManager;

    // 请求队列和缓存
    private _requestQueue: Map<string, Promise<any>> = new Map();
    private _requestCache: Map<string, { data: any; timestamp: number }> = new Map();
    private _cacheTimeout: number = 5 * 60 * 1000; // 5分钟缓存

    public static getInstance(): NetworkManager {
        if (!NetworkManager._instance) {
            NetworkManager._instance = new NetworkManager();
        }
        return NetworkManager._instance;
    }

    private constructor() {
        this._eventManager = EventManager.instance;
        this.initializeNetworkMonitoring();
    }

    /**
     * 初始化网络监控
     */
    private initializeNetworkMonitoring(): void {
        // 监听网络状态变化
        if (typeof wx !== 'undefined') {
            // 微信小游戏环境
            wx.onNetworkStatusChange?.((res) => {
                this._networkStatus = {
                    isOnline: res.isConnected,
                    networkType: res.networkType
                };

                this._eventManager?.emit('network_status_changed', this._networkStatus);

                if (res.isConnected) {
                    console.log('[NetworkManager] 网络已连接:', res.networkType);
                } else {
                    console.warn('[NetworkManager] 网络已断开');
                }
            });
        } else if (typeof window !== 'undefined') {
            // Web环境
            window.addEventListener('online', () => {
                this._networkStatus.isOnline = true;
                this._eventManager?.emit('network_status_changed', this._networkStatus);
                console.log('[NetworkManager] 网络已连接');
            });

            window.addEventListener('offline', () => {
                this._networkStatus.isOnline = false;
                this._eventManager?.emit('network_status_changed', this._networkStatus);
                console.warn('[NetworkManager] 网络已断开');
            });
        }
    }
    
    /**
     * 带重试的请求方法
     */
    public async requestWithRetry<T = any>(config: RequestConfig): Promise<IApiResponse<T>> {
        const requestKey = this.generateRequestKey(config);

        // 检查是否有相同的请求正在进行
        if (this._requestQueue.has(requestKey)) {
            return this._requestQueue.get(requestKey);
        }

        // 检查缓存
        const cachedData = this.getCachedData(requestKey);
        if (cachedData) {
            return cachedData;
        }

        // 创建请求Promise
        const requestPromise = this.executeRequestWithRetry(config);
        this._requestQueue.set(requestKey, requestPromise);

        try {
            const result = await requestPromise;

            // 缓存成功的GET请求结果
            if (config.method.toUpperCase() === 'GET') {
                this.setCachedData(requestKey, result);
            }

            return result;
        } finally {
            this._requestQueue.delete(requestKey);
        }
    }

    /**
     * 执行带重试的请求
     */
    private async executeRequestWithRetry<T>(config: RequestConfig): Promise<IApiResponse<T>> {
        const retryConfig = { ...this._defaultRetryConfig, ...config.retryConfig };
        let lastError: Error;

        for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
            try {
                // 检查网络状态
                if (!this._networkStatus.isOnline) {
                    throw new Error('网络不可用');
                }

                console.log(`[NetworkManager] 请求尝试 ${attempt + 1}/${retryConfig.maxRetries + 1}: ${config.method} ${config.url}`);

                const response = await this.executeRequest<T>(config);

                // 请求成功，重置网络状态
                if (!this._networkStatus.isOnline) {
                    this._networkStatus.isOnline = true;
                    this._eventManager?.emit('network_recovered');
                }

                return response;

            } catch (error) {
                lastError = error;

                // 检查是否应该重试
                if (attempt >= retryConfig.maxRetries || !this.shouldRetry(error, retryConfig)) {
                    break;
                }

                // 计算延迟时间（指数退避）
                const delay = Math.min(
                    retryConfig.baseDelay * Math.pow(retryConfig.backoffFactor, attempt),
                    retryConfig.maxDelay
                );

                console.warn(`[NetworkManager] 请求失败，${delay}ms后重试:`, error.message);

                // 等待延迟时间
                await this.delay(delay);
            }
        }

        // 所有重试都失败了
        console.error(`[NetworkManager] 请求最终失败: ${config.method} ${config.url}`, lastError);
        this._eventManager?.emit('request_failed', { config, error: lastError });
        throw lastError;
    }

    /**
     * 获取题目列表
     */
    public async getQuestions(count: number, difficulty: GameDifficulty): Promise<IQuestionData[]> {
        try {
            const params = new URLSearchParams({
                count: count.toString(),
                difficulty: difficulty,
                random: 'true'
            });

            const response = await this.requestWithRetry<IQuestionListResponse>({
                method: 'GET',
                url: `/v1/questions?${params.toString()}`
            });

            if (response.success && response.data) {
                return response.data.questions;
            } else {
                throw new Error(response.message || '获取题目失败');
            }
        } catch (error) {
            console.error('[NetworkManager] 获取题目失败:', error);
            throw error;
        }
    }
    
    /**
     * 提交游戏结果
     */
    public async submitGameResult(sessionId: string, score: number, answers: any[]): Promise<boolean> {
        try {
            const response = await this.request<any>(
                'POST',
                '/v1/game/submit',
                {
                    sessionId,
                    score,
                    answers,
                    timestamp: Date.now()
                }
            );
            
            return response.success;
        } catch (error) {
            console.error('[NetworkManager] 提交游戏结果失败:', error);
            return false;
        }
    }
    
    /**
     * 生成请求键值
     */
    private generateRequestKey(config: RequestConfig): string {
        const { method, url, data } = config;
        const dataStr = data ? JSON.stringify(data) : '';
        return `${method}:${url}:${dataStr}`;
    }

    /**
     * 获取缓存数据
     */
    private getCachedData(key: string): any {
        const cached = this._requestCache.get(key);
        if (cached && (Date.now() - cached.timestamp) < this._cacheTimeout) {
            console.log('[NetworkManager] 使用缓存数据:', key);
            return cached.data;
        }

        if (cached) {
            this._requestCache.delete(key);
        }

        return null;
    }

    /**
     * 设置缓存数据
     */
    private setCachedData(key: string, data: any): void {
        this._requestCache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 检查是否应该重试
     */
    private shouldRetry(error: Error, retryConfig: RetryConfig): boolean {
        const errorMessage = error.message.toLowerCase();

        return retryConfig.retryableErrors.some(retryableError =>
            errorMessage.includes(retryableError.toLowerCase())
        );
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 执行单次请求
     */
    private async executeRequest<T>(config: RequestConfig): Promise<IApiResponse<T>> {
        const fullUrl = `${this._baseUrl}${config.url}`;
        const requestHeaders = {
            'Content-Type': 'application/json',
            ...this.getAuthHeaders(),
            ...config.headers
        };

        const requestOptions: any = {
            method: config.method,
            headers: requestHeaders,
            timeout: config.timeout || this._timeout
        };

        if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method.toUpperCase())) {
            requestOptions.body = JSON.stringify(config.data);
        }

        const startTime = Date.now();
        
        try {
            // 记录请求开始
            this.logApiRequest(config, fullUrl, requestOptions);

            let response: IApiResponse<T>;
            
            // 在微信小游戏环境中使用wx.request
            if (typeof wx !== 'undefined' && wx.request) {
                response = await this.wxRequest<T>(fullUrl, requestOptions);
            } else {
                // Web环境使用fetch
                response = await this.fetchRequest<T>(fullUrl, requestOptions);
            }

            // 记录请求成功
            const duration = Date.now() - startTime;
            this.logApiResponse(config, response, duration, true);
            
            return response;

        } catch (error) {
            // 记录请求失败
            const duration = Date.now() - startTime;
            this.logApiResponse(config, error, duration, false);
            
            console.error(`[NetworkManager] 请求失败: ${config.method} ${config.url}`, error);
            throw this.handleNetworkError(error);
        }
    }

    /**
     * 通用请求方法（保持向后兼容）
     */
    private async request<T = any>(
        method: string,
        url: string,
        data?: any,
        headers?: Record<string, string>
    ): Promise<IApiResponse<T>> {
        return await this.requestWithRetry<T>({
            method,
            url,
            data,
            headers
        });
    }
    
    /**
     * 微信小游戏请求
     */
    private async wxRequest<T>(url: string, options: any): Promise<IApiResponse<T>> {
        return new Promise((resolve, reject) => {
            wx.request({
                url: url,
                method: options.method,
                data: options.body ? JSON.parse(options.body) : undefined,
                header: options.headers,
                timeout: options.timeout,
                success: (res) => {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(res.data as IApiResponse<T>);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
                    }
                },
                fail: (error) => {
                    reject(error);
                }
            });
        });
    }
    
    /**
     * Web环境fetch请求
     */
    private async fetchRequest<T>(url: string, options: any): Promise<IApiResponse<T>> {
        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        return data as IApiResponse<T>;
    }
    
    /**
     * 处理网络错误
     */
    private handleNetworkError(error: any): Error {
        if (error.message.includes('timeout')) {
            return new Error(`请求超时: ${ERROR_CODES.REQUEST_TIMEOUT}`);
        } else if (error.message.includes('network')) {
            return new Error(`网络错误: ${ERROR_CODES.NETWORK_ERROR}`);
        } else {
            return new Error(`服务器错误: ${ERROR_CODES.SERVER_ERROR}`);
        }
    }
    
    /**
     * 检查网络状态
     */
    public async checkNetworkStatus(): Promise<NetworkStatus> {
        try {
            if (typeof wx !== 'undefined' && wx.getNetworkType) {
                return new Promise((resolve) => {
                    wx.getNetworkType({
                        success: (res) => {
                            const status: NetworkStatus = {
                                isOnline: res.networkType !== 'none',
                                networkType: res.networkType
                            };

                            // 更新内部状态
                            this._networkStatus = status;
                            resolve(status);
                        },
                        fail: () => {
                            const status: NetworkStatus = { isOnline: false };
                            this._networkStatus = status;
                            resolve(status);
                        }
                    });
                });
            } else {
                // Web环境检查
                const status: NetworkStatus = {
                    isOnline: navigator.onLine,
                    networkType: (navigator as any).connection?.effectiveType || 'unknown'
                };

                this._networkStatus = status;
                return status;
            }
        } catch (error) {
            console.error('[NetworkManager] 检查网络状态失败:', error);
            const status: NetworkStatus = { isOnline: false };
            this._networkStatus = status;
            return status;
        }
    }

    /**
     * 获取当前网络状态
     */
    public getCurrentNetworkStatus(): NetworkStatus {
        return { ...this._networkStatus };
    }

    /**
     * Ping服务器检查连通性
     */
    public async pingServer(): Promise<boolean> {
        try {
            const response = await this.requestWithRetry<any>({
                method: 'GET',
                url: '/v1/health',
                retryConfig: {
                    maxRetries: 1, // ping只重试一次
                    baseDelay: 500
                }
            });
            return response.success;
        } catch (error) {
            console.warn('[NetworkManager] 服务器ping失败:', error);
            return false;
        }
    }

    /**
     * 清理缓存
     */
    public clearCache(): void {
        this._requestCache.clear();
        console.log('[NetworkManager] 请求缓存已清理');
    }

    /**
     * 清理请求队列
     */
    public clearRequestQueue(): void {
        this._requestQueue.clear();
        console.log('[NetworkManager] 请求队列已清理');
    }

    /**
     * 获取网络统计信息
     */
    public getNetworkStats(): any {
        return {
            cacheSize: this._requestCache.size,
            queueSize: this._requestQueue.size,
            networkStatus: this._networkStatus,
            cacheTimeout: this._cacheTimeout
        };
    }

    /**
     * 检查登录状态
     */
    public isLoggedIn(): boolean {
        const userProfile = StorageManager.getItem(STORAGE_KEYS.USER_PROFILE);
        return userProfile && userProfile.userId;
    }

    /**
     * 获取当前用户信息
     */
    public async getCurrentUser(): Promise<IUserProfile> {
        const userProfile = StorageManager.getItem(STORAGE_KEYS.USER_PROFILE);
        if (!userProfile) {
            throw new Error('用户未登录');
        }
        return userProfile;
    }

    /**
     * 微信登录
     */
    public async wechatLogin(): Promise<IUserProfile> {
        try {
            // 在实际微信环境中使用wx.login获取code
            if (typeof wx !== 'undefined' && wx.login) {
                return new Promise((resolve, reject) => {
                    wx.login({
                        success: async (res) => {
                            try {
                                const response = await this.request<{ user: IUserProfile }>('POST', '/v1/auth/wechat/login', {
                                    code: res.code
                                });
                                
                                if (response.success && response.data) {
                                    // 保存用户信息
                                    StorageManager.setItem(STORAGE_KEYS.USER_PROFILE, response.data.user);
                                    resolve(response.data.user);
                                } else {
                                    reject(new Error(response.message || '登录失败'));
                                }
                            } catch (error) {
                                reject(error);
                            }
                        },
                        fail: (error) => {
                            reject(error);
                        }
                    });
                });
            } else {
                // 测试环境使用测试用户
                const testUser: IUserProfile = {
                    userId: 'test_user_001',
                    nickname: '测试用户',
                    avatar: 'https://example.com/avatar.png',
                    openId: 'test_openid',
                    unionId: 'test_unionid'
                };
                StorageManager.setItem(STORAGE_KEYS.USER_PROFILE, testUser);
                return testUser;
            }
        } catch (error) {
            console.error('[NetworkManager] 微信登录失败:', error);
            throw error;
        }
    }

    /**
     * 登出
     */
    public logout(): void {
        StorageManager.removeItem(STORAGE_KEYS.USER_PROFILE);
        console.log('[NetworkManager] 用户已登出');
    }

    /**
     * 创建游戏会话
     */
    public async createGameSession(difficulty: GameDifficulty): Promise<string> {
        try {
            const response = await this.request<{ sessionId: string }>('POST', '/v1/game-sessions', {
                difficulty
            });
            
            if (response.success && response.data) {
                return response.data.sessionId;
            } else {
                throw new Error(response.message || '创建游戏会话失败');
            }
        } catch (error) {
            console.error('[NetworkManager] 创建游戏会话失败:', error);
            throw error;
        }
    }

    /**
     * 提交游戏结果
     */
    public async submitGameResult(sessionId: string, gameSession: IGameSession): Promise<boolean> {
        try {
            const response = await this.request<any>('POST', '/v1/game/submit', {
                sessionId,
                totalScore: gameSession.totalScore,
                correctCount: gameSession.correctCount,
                answers: gameSession.answers,
                duration: gameSession.endTime! - gameSession.startTime,
                difficulty: gameSession.difficulty
            });
            
            return response.success;
        } catch (error) {
            console.error('[NetworkManager] 提交游戏结果失败:', error);
            return false;
        }
    }

    /**
     * 获取用户统计数据
     */
    public async getUserStats(userId: string): Promise<any> {
        try {
            const response = await this.request<any>('GET', `/v1/user/${userId}/stats`);
            
            if (response.success && response.data) {
                return response.data;
            } else {
                throw new Error(response.message || '获取用户统计失败');
            }
        } catch (error) {
            console.error('[NetworkManager] 获取用户统计失败:', error);
            throw error;
        }
    }

    // ================== 调试和监控辅助方法 ==================

    /**
     * 获取认证头信息
     */
    private getAuthHeaders(): Record<string, string> {
        const headers: Record<string, string> = {};
        
        try {
            const userProfile = StorageManager.getItem(STORAGE_KEYS.USER_PROFILE);
            if (userProfile && userProfile.userId) {
                headers['X-User-Id'] = userProfile.userId;
                headers['X-Open-Id'] = userProfile.openId || '';
            }

            // 添加设备信息头
            headers['X-Client-Version'] = '1.0.0';
            headers['X-Platform'] = typeof wx !== 'undefined' ? 'wechat-mini-game' : 'web';
            headers['X-Request-Id'] = this.generateRequestId();
            
        } catch (error) {
            console.warn('[NetworkManager] 获取认证头失败:', error);
        }

        return headers;
    }

    /**
     * 生成请求ID
     */
    private generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
    }

    /**
     * 记录API请求开始
     */
    private logApiRequest(config: RequestConfig, fullUrl: string, options: any): void {
        const logData = {
            timestamp: new Date().toISOString(),
            requestId: options.headers['X-Request-Id'],
            method: config.method,
            url: config.url,
            fullUrl,
            headers: options.headers,
            data: config.data,
            timeout: options.timeout,
            networkStatus: this._networkStatus
        };

        console.group(`🌐 [API REQUEST] ${config.method} ${config.url}`);
        console.info('请求详情:', logData);
        console.groupEnd();

        // 触发调试事件给测试工具
        this._eventManager?.emit('api_request_start', logData);
    }

    /**
     * 记录API响应结果
     */
    private logApiResponse(config: RequestConfig, result: any, duration: number, success: boolean): void {
        const logData = {
            timestamp: new Date().toISOString(),
            method: config.method,
            url: config.url,
            duration,
            success,
            result: success ? result : result.message || result.toString(),
            networkStatus: this._networkStatus
        };

        if (success) {
            console.group(`✅ [API SUCCESS] ${config.method} ${config.url} (${duration}ms)`);
            console.info('响应数据:', result);
        } else {
            console.group(`❌ [API ERROR] ${config.method} ${config.url} (${duration}ms)`);
            console.error('错误信息:', result);
        }
        console.groupEnd();

        // 触发调试事件给测试工具
        this._eventManager?.emit(success ? 'api_request_success' : 'api_request_error', logData);

        // 收集API性能统计
        this.updateApiStats(config.url, duration, success);
    }

    /**
     * 更新API统计信息
     */
    private apiStats: Map<string, { totalRequests: number; successCount: number; totalDuration: number; lastError?: string }> = new Map();

    private updateApiStats(url: string, duration: number, success: boolean): void {
        if (!this.apiStats.has(url)) {
            this.apiStats.set(url, {
                totalRequests: 0,
                successCount: 0,
                totalDuration: 0
            });
        }

        const stats = this.apiStats.get(url)!;
        stats.totalRequests++;
        stats.totalDuration += duration;
        
        if (success) {
            stats.successCount++;
            delete stats.lastError;
        } else {
            stats.lastError = new Date().toISOString();
        }
    }

    /**
     * 获取API统计信息（用于调试和监控）
     */
    public getApiStats(): any {
        const stats = {};
        for (const [url, data] of this.apiStats) {
            stats[url] = {
                ...data,
                successRate: data.totalRequests > 0 ? (data.successCount / data.totalRequests) : 0,
                avgDuration: data.totalRequests > 0 ? (data.totalDuration / data.totalRequests) : 0
            };
        }
        return stats;
    }

    /**
     * 导出调试信息（供backend-agent使用）
     */
    public exportDebugInfo(): any {
        return {
            timestamp: new Date().toISOString(),
            networkStatus: this._networkStatus,
            apiStats: this.getApiStats(),
            cacheInfo: {
                cacheSize: this._requestCache.size,
                queueSize: this._requestQueue.size
            },
            config: {
                baseUrl: this._baseUrl,
                timeout: this._timeout,
                retryConfig: this._defaultRetryConfig
            },
            userInfo: {
                isLoggedIn: this.isLoggedIn(),
                userId: this.isLoggedIn() ? StorageManager.getItem(STORAGE_KEYS.USER_PROFILE)?.userId : null
            }
        };
    }

    /**
     * 运行网络诊断
     */
    public async runNetworkDiagnostics(): Promise<any> {
        const diagnostics = {
            timestamp: new Date().toISOString(),
            tests: {}
        };

        try {
            // 1. 网络状态检查
            console.log('🔍 [诊断] 检查网络状态...');
            const networkStatus = await this.checkNetworkStatus();
            diagnostics.tests['network_status'] = {
                success: networkStatus.isOnline,
                data: networkStatus,
                message: networkStatus.isOnline ? '网络正常' : '网络不可用'
            };

            // 2. 服务器连通性检查
            console.log('🔍 [诊断] 检查服务器连通性...');
            const serverReachable = await this.pingServer();
            diagnostics.tests['server_ping'] = {
                success: serverReachable,
                message: serverReachable ? '服务器可达' : '服务器不可达'
            };

            // 3. 认证状态检查
            console.log('🔍 [诊断] 检查认证状态...');
            const isLoggedIn = this.isLoggedIn();
            diagnostics.tests['auth_status'] = {
                success: true,
                data: { isLoggedIn },
                message: isLoggedIn ? '用户已登录' : '用户未登录'
            };

            // 4. API基础测试
            if (networkStatus.isOnline && serverReachable) {
                try {
                    console.log('🔍 [诊断] 测试基础API...');
                    await this.getQuestions(1, GameDifficulty.EASY);
                    diagnostics.tests['api_basic'] = {
                        success: true,
                        message: 'API基础功能正常'
                    };
                } catch (error) {
                    diagnostics.tests['api_basic'] = {
                        success: false,
                        error: error.message,
                        message: 'API基础功能异常'
                    };
                }
            }

        } catch (error) {
            diagnostics.tests['diagnostic_error'] = {
                success: false,
                error: error.message,
                message: '诊断过程出错'
            };
        }

        console.group('📊 [诊断报告]');
        console.table(diagnostics.tests);
        console.groupEnd();

        return diagnostics;
    }
}