import { DEBUG_CONFIG, TEST_CONFIG } from '../constants/GameConstants';
import { GameAPIManager } from '../managers/GameAPIManager';
import { NetworkManager } from './NetworkManager';
import { ErrorHandler } from './ErrorHandler';

/**
 * 测试工具类
 * 用于联调测试和调试
 */
export class TestUtils {
    private static _instance: TestUtils;
    private _testLogger: TestLogger;

    public static getInstance(): TestUtils {
        if (!TestUtils._instance) {
            TestUtils._instance = new TestUtils();
        }
        return TestUtils._instance;
    }

    private constructor() {
        this._testLogger = new TestLogger();
        this.setupTestEnvironment();
    }

    /**
     * 设置测试环境
     */
    private setupTestEnvironment(): void {
        if (!DEBUG_CONFIG.ENABLED) {
            return;
        }

        console.log('%c[TestUtils] 测试环境已启用', 'color: #00ff00; font-weight: bold;');
        
        // 暴露测试接口到全局对象（仅在调试模式下）
        if (typeof window !== 'undefined') {
            (window as any).testUtils = this;
            (window as any).networkManager = NetworkManager.getInstance();
            (window as any).gameAPIManager = GameAPIManager.getInstance();
            (window as any).errorHandler = ErrorHandler.getInstance();
            
            console.log('%c[TestUtils] 测试接口已暴露到window对象', 'color: #00ff00;');
            console.log('可用接口: testUtils, networkManager, gameAPIManager, errorHandler');
        }
    }

    /**
     * 测试网络连接
     */
    public async testNetworkConnection(): Promise<void> {
        this._testLogger.group('网络连接测试');
        
        try {
            const networkManager = NetworkManager.getInstance();
            
            // 测试网络状态检查
            this._testLogger.log('检查网络状态...');
            const networkStatus = await networkManager.checkNetworkStatus();
            this._testLogger.success('网络状态:', networkStatus);
            
            // 测试服务器连接
            this._testLogger.log('测试服务器连接...');
            const serverReachable = await networkManager.pingServer();
            this._testLogger.success('服务器连接:', serverReachable ? '成功' : '失败');
            
            // 测试Token状态
            this._testLogger.log('检查认证状态...');
            const isLoggedIn = networkManager.isLoggedIn();
            this._testLogger.info('登录状态:', isLoggedIn);
            
            if (isLoggedIn) {
                try {
                    const user = await networkManager.getCurrentUser();
                    this._testLogger.info('用户信息:', user.nickname);
                } catch (error) {
                    this._testLogger.warn('获取用户信息失败:', error);
                }
            }
            
        } catch (error) {
            this._testLogger.error('网络连接测试失败:', error);
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 测试微信登录流程
     */
    public async testWechatLogin(): Promise<void> {
        this._testLogger.group('微信登录测试');
        
        try {
            const gameAPIManager = GameAPIManager.getInstance();
            
            this._testLogger.log('开始微信登录测试...');
            
            if (TEST_CONFIG.SKIP_WECHAT_LOGIN) {
                this._testLogger.warn('跳过微信登录，使用测试用户');
                // 模拟登录成功
                this._testLogger.success('模拟登录成功:', TEST_CONFIG.TEST_USER);
                return;
            }
            
            const user = await gameAPIManager.login();
            this._testLogger.success('登录成功:', user);
            
        } catch (error) {
            this._testLogger.error('微信登录测试失败:', error);
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 测试游戏API接口
     */
    public async testGameAPIs(): Promise<void> {
        this._testLogger.group('游戏API测试');
        
        try {
            const gameAPIManager = GameAPIManager.getInstance();
            
            // 测试获取题目
            this._testLogger.log('测试获取题目...');
            const questions = await gameAPIManager.getQuestions(3, 'easy');
            this._testLogger.success(`获取题目成功，数量: ${questions.length}`);
            
            // 测试创建游戏会话
            this._testLogger.log('测试创建游戏会话...');
            const session = await gameAPIManager.startNewGame('easy', 3);
            this._testLogger.success('创建游戏会话成功:', session.sessionId);
            
            // 测试提交答题（模拟）
            if (session.questions.length > 0) {
                this._testLogger.log('测试提交答题...');
                const question = session.questions[0];
                await gameAPIManager.submitAnswer(question.id, 0, 5000, 1);
                this._testLogger.success('提交答题成功');
            }
            
            // 测试完成游戏
            this._testLogger.log('测试完成游戏...');
            await gameAPIManager.finishGame();
            this._testLogger.success('完成游戏测试成功');
            
        } catch (error) {
            this._testLogger.error('游戏API测试失败:', error);
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 测试错误处理
     */
    public async testErrorHandling(): Promise<void> {
        this._testLogger.group('错误处理测试');
        
        try {
            const errorHandler = ErrorHandler.getInstance();
            
            // 测试不同类型的错误
            this._testLogger.log('测试网络错误处理...');
            errorHandler.handleError(ErrorHandler.createNetworkError('模拟网络连接超时'));
            
            this._testLogger.log('测试认证错误处理...');
            errorHandler.handleError(ErrorHandler.createAuthError('模拟Token过期'));
            
            this._testLogger.log('测试游戏错误处理...');
            errorHandler.handleError(ErrorHandler.createGameError('模拟游戏数据异常'));
            
            this._testLogger.log('测试音频错误处理...');
            errorHandler.handleError(ErrorHandler.createAudioError('模拟音频加载失败'));
            
            this._testLogger.success('错误处理测试完成');
            
        } catch (error) {
            this._testLogger.error('错误处理测试失败:', error);
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 运行完整的联调测试
     */
    public async runFullTest(): Promise<void> {
        this._testLogger.group('完整联调测试');
        this._testLogger.log('开始运行完整的前后端联调测试...');
        
        try {
            // 1. 初始化测试
            await this.initializeTest();
            
            // 2. 网络连接测试
            await this.testNetworkConnection();
            
            // 3. 微信登录测试
            await this.testWechatLogin();
            
            // 4. 游戏API测试
            await this.testGameAPIs();
            
            // 5. 错误处理测试
            await this.testErrorHandling();
            
            this._testLogger.success('🎉 完整联调测试通过！');
            
        } catch (error) {
            this._testLogger.error('❌ 联调测试失败:', error);
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 初始化测试
     */
    private async initializeTest(): Promise<void> {
        this._testLogger.group('初始化测试环境');
        
        try {
            // 初始化GameAPIManager
            const gameAPIManager = GameAPIManager.getInstance();
            await gameAPIManager.initialize();
            this._testLogger.success('GameAPIManager初始化完成');
            
            // 清理之前的测试数据
            this.clearTestData();
            this._testLogger.success('测试数据清理完成');
            
        } catch (error) {
            this._testLogger.error('初始化测试环境失败:', error);
            throw error;
        }
        
        this._testLogger.groupEnd();
    }

    /**
     * 清理测试数据
     */
    public clearTestData(): void {
        try {
            if (typeof localStorage !== 'undefined') {
                // 清理游戏相关的本地存储
                const keysToRemove = [
                    'auth_token',
                    'user_profile',
                    'game_progress',
                    'game_history',
                    'error_logs'
                ];
                
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                console.log('[TestUtils] 测试数据已清理');
            }
        } catch (error) {
            console.error('[TestUtils] 清理测试数据失败:', error);
        }
    }

    /**
     * 生成测试报告
     */
    public generateTestReport(): string {
        const report = {
            timestamp: new Date().toISOString(),
            environment: {
                userAgent: navigator.userAgent,
                debugEnabled: DEBUG_CONFIG.ENABLED,
                testConfigEnabled: TEST_CONFIG.USE_TEST_DATA
            },
            networkStatus: 'unknown',
            apiStatus: 'unknown',
            authStatus: 'unknown',
            logs: this._testLogger.getLogs()
        };
        
        const reportJson = JSON.stringify(report, null, 2);
        console.log('%c[TestUtils] 测试报告生成完成', 'color: #00ff00;');
        console.log(reportJson);
        
        return reportJson;
    }

    /**
     * 模拟网络错误
     */
    public simulateNetworkError(errorType: 'timeout' | 'offline' | 'server_error' = 'timeout'): void {
        this._testLogger.warn(`模拟网络错误: ${errorType}`);
        
        const errorMessages = {
            timeout: '请求超时，请检查网络连接',
            offline: '网络连接不可用',
            server_error: '服务器内部错误'
        };
        
        ErrorHandler.getInstance().handleError(
            ErrorHandler.createNetworkError(errorMessages[errorType], { simulated: true })
        );
    }

    /**
     * 获取测试统计信息
     */
    public getTestStats(): any {
        return {
            totalTests: this._testLogger.getTestCount(),
            successCount: this._testLogger.getSuccessCount(),
            errorCount: this._testLogger.getErrorCount(),
            warningCount: this._testLogger.getWarningCount(),
            successRate: this._testLogger.getSuccessRate()
        };
    }
}

/**
 * 测试日志记录器
 */
class TestLogger {
    private _logs: any[] = [];
    private _testCount: number = 0;
    private _successCount: number = 0;
    private _errorCount: number = 0;
    private _warningCount: number = 0;

    public log(message: string, ...args: any[]): void {
        const logEntry = {
            level: 'log',
            message,
            args,
            timestamp: Date.now()
        };
        this._logs.push(logEntry);
        console.log(`%c[TEST] ${message}`, 'color: #666;', ...args);
        this._testCount++;
    }

    public success(message: string, ...args: any[]): void {
        const logEntry = {
            level: 'success',
            message,
            args,
            timestamp: Date.now()
        };
        this._logs.push(logEntry);
        console.log(`%c✅ [TEST] ${message}`, 'color: #00aa00; font-weight: bold;', ...args);
        this._successCount++;
        this._testCount++;
    }

    public error(message: string, ...args: any[]): void {
        const logEntry = {
            level: 'error',
            message,
            args,
            timestamp: Date.now()
        };
        this._logs.push(logEntry);
        console.error(`%c❌ [TEST] ${message}`, 'color: #ff0000; font-weight: bold;', ...args);
        this._errorCount++;
        this._testCount++;
    }

    public warn(message: string, ...args: any[]): void {
        const logEntry = {
            level: 'warn',
            message,
            args,
            timestamp: Date.now()
        };
        this._logs.push(logEntry);
        console.warn(`%c⚠️ [TEST] ${message}`, 'color: #ff8800; font-weight: bold;', ...args);
        this._warningCount++;
        this._testCount++;
    }

    public info(message: string, ...args: any[]): void {
        const logEntry = {
            level: 'info',
            message,
            args,
            timestamp: Date.now()
        };
        this._logs.push(logEntry);
        console.info(`%cℹ️ [TEST] ${message}`, 'color: #0088ff;', ...args);
        this._testCount++;
    }

    public group(title: string): void {
        console.group(`%c📁 [TEST] ${title}`, 'color: #0088ff; font-weight: bold;');
    }

    public groupEnd(): void {
        console.groupEnd();
    }

    public getLogs(): any[] {
        return [...this._logs];
    }

    public getTestCount(): number {
        return this._testCount;
    }

    public getSuccessCount(): number {
        return this._successCount;
    }

    public getErrorCount(): number {
        return this._errorCount;
    }

    public getWarningCount(): number {
        return this._warningCount;
    }

    public getSuccessRate(): number {
        return this._testCount > 0 ? (this._successCount / this._testCount) * 100 : 0;
    }

    public clear(): void {
        this._logs = [];
        this._testCount = 0;
        this._successCount = 0;
        this._errorCount = 0;
        this._warningCount = 0;
    }
}