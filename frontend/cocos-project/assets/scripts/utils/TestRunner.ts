import { DEBUG_CONFIG, TEST_CONFIG } from '../constants/GameConstants';
import { TestUtils } from './TestUtils';
import { GameAPIManager } from '../managers/GameAPIManager';
import { NetworkManager } from './NetworkManager';

/**
 * 联调测试启动器
 * 用于自动运行前后端联调测试
 */
export class TestRunner {
    private static _instance: TestRunner;
    private _testUtils: TestUtils;
    private _isRunning: boolean = false;

    public static getInstance(): TestRunner {
        if (!TestRunner._instance) {
            TestRunner._instance = new TestRunner();
        }
        return TestRunner._instance;
    }

    private constructor() {
        this._testUtils = TestUtils.getInstance();
    }

    /**
     * 启动联调测试
     */
    public async startIntegrationTest(): Promise<void> {
        if (this._isRunning) {
            console.warn('[TestRunner] 测试已在运行中');
            return;
        }

        if (!DEBUG_CONFIG.ENABLED) {
            console.warn('[TestRunner] 调试模式未启用，跳过测试');
            return;
        }

        this._isRunning = true;
        console.log('%c[TestRunner] 🚀 开始前后端联调测试', 'color: #00ff00; font-size: 16px; font-weight: bold;');

        try {
            // 显示测试环境信息
            this.showTestEnvironment();

            // 等待系统初始化
            await this.waitForSystemReady();

            // 运行完整测试
            await this._testUtils.runFullTest();

            // 生成测试报告
            const report = this._testUtils.generateTestReport();
            
            // 显示测试结果
            this.showTestResults();

            console.log('%c[TestRunner] ✅ 联调测试完成', 'color: #00ff00; font-size: 16px; font-weight: bold;');

        } catch (error) {
            console.error('%c[TestRunner] ❌ 联调测试失败', 'color: #ff0000; font-size: 16px; font-weight: bold;', error);
        } finally {
            this._isRunning = false;
        }
    }

    /**
     * 显示测试环境信息
     */
    private showTestEnvironment(): void {
        console.group('%c📋 测试环境信息', 'color: #0088ff; font-weight: bold;');
        
        console.log('🔧 调试配置:', {
            启用状态: DEBUG_CONFIG.ENABLED,
            日志级别: DEBUG_CONFIG.LOG_LEVEL,
            API调试: DEBUG_CONFIG.API_DEBUG,
            详细错误: DEBUG_CONFIG.SHOW_DETAILED_ERRORS
        });

        console.log('🧪 测试配置:', {
            测试服务器: TEST_CONFIG.TEST_SERVER_URL,
            使用测试数据: TEST_CONFIG.USE_TEST_DATA,
            跳过微信登录: TEST_CONFIG.SKIP_WECHAT_LOGIN,
            自动完成游戏: TEST_CONFIG.AUTO_COMPLETE_GAME
        });

        console.log('🌐 环境信息:', {
            用户代理: navigator.userAgent,
            是否微信环境: typeof wx !== 'undefined',
            本地存储支持: typeof localStorage !== 'undefined',
            网络状态: navigator.onLine ? '在线' : '离线'
        });

        console.groupEnd();
    }

    /**
     * 等待系统准备就绪
     */
    private async waitForSystemReady(): Promise<void> {
        console.log('[TestRunner] 等待系统初始化...');
        
        let attempts = 0;
        const maxAttempts = 30; // 30秒超时
        
        while (attempts < maxAttempts) {
            const gameAPIManager = GameAPIManager.getInstance();
            
            if (gameAPIManager.isInitialized()) {
                console.log('✅ [TestRunner] 系统准备就绪');
                return;
            }
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
            
            if (attempts % 5 === 0) {
                console.log(`[TestRunner] 等待系统初始化... (${attempts}/${maxAttempts})`);
            }
        }
        
        throw new Error('系统初始化超时');
    }

    /**
     * 显示测试结果
     */
    private showTestResults(): void {
        const stats = this._testUtils.getTestStats();
        
        console.group('%c📊 测试结果统计', 'color: #0088ff; font-weight: bold;');
        
        console.log(`总测试数: ${stats.totalTests}`);
        console.log(`成功数: ${stats.successCount}`);
        console.log(`失败数: ${stats.errorCount}`);
        console.log(`警告数: ${stats.warningCount}`);
        console.log(`成功率: ${stats.successRate.toFixed(1)}%`);
        
        if (stats.successRate >= 90) {
            console.log('%c🎉 测试通过率优秀！', 'color: #00aa00; font-weight: bold;');
        } else if (stats.successRate >= 70) {
            console.log('%c⚠️ 测试通过率良好，建议优化', 'color: #ff8800; font-weight: bold;');
        } else {
            console.log('%c❌ 测试通过率较低，需要修复', 'color: #ff0000; font-weight: bold;');
        }
        
        console.groupEnd();
    }

    /**
     * 运行特定测试
     */
    public async runSpecificTest(testName: string): Promise<void> {
        if (!DEBUG_CONFIG.ENABLED) {
            console.warn('[TestRunner] 调试模式未启用');
            return;
        }

        console.log(`[TestRunner] 运行特定测试: ${testName}`);
        
        try {
            switch (testName) {
                case 'network':
                    await this._testUtils.testNetworkConnection();
                    break;
                case 'login':
                    await this._testUtils.testWechatLogin();
                    break;
                case 'api':
                    await this._testUtils.testGameAPIs();
                    break;
                case 'error':
                    await this._testUtils.testErrorHandling();
                    break;
                default:
                    console.warn(`[TestRunner] 未知测试: ${testName}`);
            }
        } catch (error) {
            console.error(`[TestRunner] 测试 ${testName} 失败:`, error);
        }
    }

    /**
     * 模拟游戏流程测试
     */
    public async simulateGameFlow(): Promise<void> {
        if (!DEBUG_CONFIG.ENABLED) {
            console.warn('[TestRunner] 调试模式未启用');
            return;
        }

        console.log('[TestRunner] 开始模拟游戏流程...');
        
        try {
            const gameAPIManager = GameAPIManager.getInstance();
            
            // 1. 检查登录状态
            if (!gameAPIManager.isLoggedIn()) {
                console.log('[TestRunner] 需要登录，开始登录流程...');
                await gameAPIManager.login();
            }
            
            // 2. 创建游戏会话
            console.log('[TestRunner] 创建游戏会话...');
            const session = await gameAPIManager.startNewGame('easy', TEST_CONFIG.TEST_QUESTION_COUNT);
            
            // 3. 模拟答题
            console.log('[TestRunner] 开始模拟答题...');
            for (let i = 0; i < session.questions.length; i++) {
                const question = session.questions[i];
                const selectedAnswer = Math.floor(Math.random() * 4); // 随机选择答案
                const answerTime = Math.random() * 10000 + 2000; // 2-12秒答题时间
                
                console.log(`[TestRunner] 答第${i + 1}题，选择答案: ${selectedAnswer}`);
                await gameAPIManager.submitAnswer(question.id, selectedAnswer, answerTime, 1);
                
                // 等待一段时间模拟用户思考
                await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.AUTO_ANSWER_DELAY));
            }
            
            // 4. 完成游戏
            console.log('[TestRunner] 完成游戏...');
            await gameAPIManager.finishGame();
            
            console.log('✅ [TestRunner] 游戏流程模拟完成');
            
        } catch (error) {
            console.error('❌ [TestRunner] 游戏流程模拟失败:', error);
        }
    }

    /**
     * 清理测试数据
     */
    public clearTestData(): void {
        this._testUtils.clearTestData();
        console.log('[TestRunner] 测试数据已清理');
    }

    /**
     * 获取测试状态
     */
    public isRunning(): boolean {
        return this._isRunning;
    }

    /**
     * 获取测试统计
     */
    public getTestStats(): any {
        return this._testUtils.getTestStats();
    }
}

// 自动启动测试（在调试模式下）
if (DEBUG_CONFIG.ENABLED && typeof window !== 'undefined') {
    // 暴露TestRunner到全局
    (window as any).testRunner = TestRunner.getInstance();
    
    console.log('%c[TestRunner] 测试工具已加载', 'color: #00ff00;');
    console.log('可用命令:');
    console.log('- testRunner.startIntegrationTest() // 运行完整联调测试');
    console.log('- testRunner.runSpecificTest("network") // 运行特定测试');
    console.log('- testRunner.simulateGameFlow() // 模拟游戏流程');
    console.log('- testRunner.clearTestData() // 清理测试数据');
}