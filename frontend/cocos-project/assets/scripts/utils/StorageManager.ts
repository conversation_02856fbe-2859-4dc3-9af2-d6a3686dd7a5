import { STORAGE_KEYS } from '../constants/GameConstants';

/**
 * 存储管理器
 * 负责本地存储的读取和写入
 */
export class StorageManager {
    private _storage: Storage | null = null;
    
    constructor() {
        // 检查存储环境
        if (typeof wx !== 'undefined' && wx.getStorageSync) {
            // 微信小游戏环境
            this._storage = null; // 使用微信API
        } else if (typeof localStorage !== 'undefined') {
            // Web环境
            this._storage = localStorage;
        }
    }
    
    /**
     * 存储数据
     */
    public async setItem(key: string, value: any): Promise<void> {
        try {
            const jsonValue = JSON.stringify(value);
            
            if (typeof wx !== 'undefined' && wx.setStorageSync) {
                // 微信小游戏环境
                wx.setStorageSync(key, jsonValue);
            } else if (this._storage) {
                // Web环境
                this._storage.setItem(key, jsonValue);
            } else {
                throw new Error('存储环境不可用');
            }
        } catch (error) {
            console.error(`[StorageManager] 存储数据失败: ${key}`, error);
            throw error;
        }
    }
    
    /**
     * 读取数据
     */
    public async getItem<T = any>(key: string): Promise<T | null> {
        try {
            let jsonValue: string | null = null;
            
            if (typeof wx !== 'undefined' && wx.getStorageSync) {
                // 微信小游戏环境
                jsonValue = wx.getStorageSync(key);
            } else if (this._storage) {
                // Web环境
                jsonValue = this._storage.getItem(key);
            } else {
                throw new Error('存储环境不可用');
            }
            
            if (jsonValue === null || jsonValue === '') {
                return null;
            }
            
            return JSON.parse(jsonValue);
        } catch (error) {
            console.error(`[StorageManager] 读取数据失败: ${key}`, error);
            return null;
        }
    }
    
    /**
     * 删除数据
     */
    public async removeItem(key: string): Promise<void> {
        try {
            if (typeof wx !== 'undefined' && wx.removeStorageSync) {
                // 微信小游戏环境
                wx.removeStorageSync(key);
            } else if (this._storage) {
                // Web环境
                this._storage.removeItem(key);
            } else {
                throw new Error('存储环境不可用');
            }
        } catch (error) {
            console.error(`[StorageManager] 删除数据失败: ${key}`, error);
            throw error;
        }
    }
    
    /**
     * 清空所有数据
     */
    public async clear(): Promise<void> {
        try {
            if (typeof wx !== 'undefined' && wx.clearStorageSync) {
                // 微信小游戏环境
                wx.clearStorageSync();
            } else if (this._storage) {
                // Web环境
                this._storage.clear();
            } else {
                throw new Error('存储环境不可用');
            }
        } catch (error) {
            console.error('[StorageManager] 清空存储失败', error);
            throw error;
        }
    }
    
    /**
     * 获取存储大小信息（微信小游戏）
     */
    public async getStorageInfo(): Promise<{ keys: string[], currentSize: number, limitSize: number } | null> {
        try {
            if (typeof wx !== 'undefined' && wx.getStorageInfoSync) {
                return wx.getStorageInfoSync();
            }
            return null;
        } catch (error) {
            console.error('[StorageManager] 获取存储信息失败', error);
            return null;
        }
    }
}