import { _decorator, Component, Layout, UITransform, Node } from 'cc';

const { ccclass, property } = _decorator;

/**
 * Layout安全助手组件
 * 专门处理Layout组件的安全初始化和错误预防
 */
@ccclass('LayoutSafetyHelper')
export class LayoutSafetyHelper extends Component {
    
    @property({
        tooltip: '是否在onLoad时自动检查和修复Layout'
    })
    public autoFixOnLoad: boolean = true;
    
    @property({
        tooltip: '是否延迟启用Layout组件'
    })
    public delayedEnable: boolean = true;
    
    @property({
        tooltip: '延迟启用的时间（秒）'
    })
    public enableDelay: number = 0.1;
    
    private _layout: Layout = null;
    private _isFixed: boolean = false;
    
    protected onLoad(): void {
        this._layout = this.getComponent(Layout);
        
        if (!this._layout) {
            console.warn('[LayoutSafetyHelper] 未找到Layout组件');
            return;
        }
        
        if (this.autoFixOnLoad) {
            this.fixLayoutSafety();
        }
    }
    
    protected start(): void {
        if (this.delayedEnable && this._layout && !this._isFixed) {
            this.scheduleOnce(() => {
                this.enableLayoutSafely();
            }, this.enableDelay);
        }
    }
    
    /**
     * 修复Layout安全性
     */
    public fixLayoutSafety(): void {
        if (!this._layout || this._isFixed) {
            return;
        }
        
        try {
            // 临时禁用Layout
            const wasEnabled = this._layout.enabled;
            this._layout.enabled = false;
            
            // 检查和修复所有子节点
            this.fixChildNodes();
            
            // 验证Layout配置
            this.validateLayoutConfig();
            
            // 标记为已修复
            this._isFixed = true;
            
            // 如果原来是启用的，延迟重新启用
            if (wasEnabled) {
                this.scheduleOnce(() => {
                    this.enableLayoutSafely();
                }, this.enableDelay);
            }
            
            console.log('[LayoutSafetyHelper] Layout安全性修复完成');
            
        } catch (error) {
            console.error('[LayoutSafetyHelper] 修复Layout安全性失败:', error);
        }
    }
    
    /**
     * 修复子节点
     */
    private fixChildNodes(): void {
        const children = this.node.children;
        
        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            
            if (!child || !child.isValid) {
                console.warn(`[LayoutSafetyHelper] 子节点[${i}]无效，跳过`);
                continue;
            }
            
            // 确保子节点有UITransform组件
            let uiTransform = child.getComponent(UITransform);
            if (!uiTransform) {
                console.log(`[LayoutSafetyHelper] 为子节点[${i}]添加UITransform组件`);
                uiTransform = child.addComponent(UITransform);
            }
            
            // 验证UITransform的基本属性
            if (uiTransform) {
                try {
                    // 确保contentSize有效
                    if (!uiTransform.contentSize || 
                        (uiTransform.contentSize.width === 0 && uiTransform.contentSize.height === 0)) {
                        // 设置默认大小
                        uiTransform.setContentSize(100, 100);
                        console.log(`[LayoutSafetyHelper] 为子节点[${i}]设置默认contentSize`);
                    }
                } catch (error) {
                    console.warn(`[LayoutSafetyHelper] 验证子节点[${i}]的UITransform失败:`, error);
                }
            }
        }
    }
    
    /**
     * 验证Layout配置
     */
    private validateLayoutConfig(): void {
        if (!this._layout) {
            return;
        }
        
        try {
            // 检查Layout的基本配置
            const layoutType = this._layout.type;
            const resizeMode = this._layout.resizeMode;
            
            console.log(`[LayoutSafetyHelper] Layout配置 - 类型: ${layoutType}, 调整模式: ${resizeMode}`);
            
            // 确保节点本身有UITransform
            let nodeUITransform = this.node.getComponent(UITransform);
            if (!nodeUITransform) {
                console.log('[LayoutSafetyHelper] 为Layout节点添加UITransform组件');
                nodeUITransform = this.node.addComponent(UITransform);
            }
            
        } catch (error) {
            console.error('[LayoutSafetyHelper] 验证Layout配置失败:', error);
        }
    }
    
    /**
     * 安全地启用Layout
     */
    public enableLayoutSafely(): void {
        if (!this._layout) {
            return;
        }
        
        try {
            // 最后一次检查
            this.validateAllComponents();
            
            // 启用Layout
            this._layout.enabled = true;
            
            // 强制更新一次
            this.scheduleOnce(() => {
                if (this._layout && this._layout.enabled) {
                    try {
                        this._layout.updateLayout();
                        console.log('[LayoutSafetyHelper] Layout已安全启用并更新');
                    } catch (updateError) {
                        console.error('[LayoutSafetyHelper] Layout更新失败:', updateError);
                    }
                }
            }, 0.05);
            
        } catch (error) {
            console.error('[LayoutSafetyHelper] 安全启用Layout失败:', error);
        }
    }
    
    /**
     * 验证所有组件
     */
    private validateAllComponents(): void {
        // 验证自身节点
        if (!this.node || !this.node.isValid) {
            throw new Error('Layout节点无效');
        }
        
        // 验证Layout组件
        if (!this._layout || !this._layout.isValid) {
            throw new Error('Layout组件无效');
        }
        
        // 验证子节点
        const children = this.node.children;
        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (child && child.isValid) {
                const uiTransform = child.getComponent(UITransform);
                if (!uiTransform) {
                    throw new Error(`子节点[${i}]缺少UITransform组件`);
                }
            }
        }
    }
    
    /**
     * 手动触发Layout修复
     */
    public manualFix(): void {
        this._isFixed = false;
        this.fixLayoutSafety();
    }
    
    /**
     * 获取Layout状态信息
     */
    public getLayoutStatus(): any {
        return {
            hasLayout: !!this._layout,
            isFixed: this._isFixed,
            isEnabled: this._layout?.enabled || false,
            childCount: this.node.children.length,
            layoutType: this._layout?.type,
            resizeMode: this._layout?.resizeMode
        };
    }
}
