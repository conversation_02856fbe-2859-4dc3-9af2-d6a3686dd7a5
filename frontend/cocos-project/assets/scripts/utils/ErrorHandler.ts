import { ERROR_CODES } from '../constants/GameConstants';
import { EventManager } from '../managers/EventManager';

/**
 * 错误类型枚举
 */
export enum ErrorType {
    NETWORK = 'network',           // 网络错误
    AUTH = 'auth',                 // 认证错误
    GAME = 'game',                 // 游戏逻辑错误
    AUDIO = 'audio',               // 音频错误
    STORAGE = 'storage',           // 存储错误
    SYSTEM = 'system'              // 系统错误
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
    LOW = 'low',                   // 低级错误，不影响核心功能
    MEDIUM = 'medium',             // 中级错误，影响部分功能
    HIGH = 'high',                 // 高级错误，影响主要功能
    CRITICAL = 'critical'          // 严重错误，影响游戏运行
}

/**
 * 游戏错误类
 */
export class GameError extends Error {
    public readonly type: ErrorType;
    public readonly severity: ErrorSeverity;
    public readonly code: number;
    public readonly timestamp: number;
    public readonly context?: any;

    constructor(
        message: string,
        type: ErrorType,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        code?: number,
        context?: any
    ) {
        super(message);
        this.name = 'GameError';
        this.type = type;
        this.severity = severity;
        this.code = code || 0;
        this.timestamp = Date.now();
        this.context = context;
    }
}

/**
 * 错误处理器
 * 统一处理游戏中的各种错误，提供用户友好的错误提示
 */
export class ErrorHandler {
    private static _instance: ErrorHandler;
    private _errorQueue: GameError[] = [];
    private _isShowingError: boolean = false;
    private _errorListeners: Map<ErrorType, ((error: GameError) => void)[]> = new Map();

    // 错误抑制机制
    private _errorSuppressionMap: Map<string, number> = new Map();
    private _lastErrorTime: Map<string, number> = new Map();
    private readonly ERROR_SUPPRESSION_INTERVAL = 1000; // 1秒内相同错误只处理一次
    private readonly MAX_ERROR_COUNT_PER_MINUTE = 10; // 每分钟最多处理10个相同错误

    // 防止递归调用
    private _isProcessingError: boolean = false;
    private _processingStack: Set<string> = new Set();

    public static getInstance(): ErrorHandler {
        if (!ErrorHandler._instance) {
            ErrorHandler._instance = new ErrorHandler();
        }
        return ErrorHandler._instance;
    }

    private constructor() {
        this.setupGlobalErrorHandlers();
    }

    /**
     * 设置全局错误处理器
     */
    private setupGlobalErrorHandlers(): void {
        // 捕获未处理的Promise错误
        window.addEventListener?.('unhandledrejection', (event) => {
            // 避免在错误处理过程中再次触发错误处理
            if (!this._isProcessingError) {
                this.safeConsoleLog('error', '[ErrorHandler] 未处理的Promise错误:', event.reason);
                this.handleError(new GameError(
                    `未处理的异步错误: ${event.reason?.message || event.reason}`,
                    ErrorType.SYSTEM,
                    ErrorSeverity.HIGH,
                    undefined,
                    { originalError: event.reason }
                ));
            }
            event.preventDefault();
        });

        // 捕获JavaScript运行时错误
        window.addEventListener?.('error', (event) => {
            // 避免在错误处理过程中再次触发错误处理
            if (!this._isProcessingError) {
                this.safeConsoleLog('error', '[ErrorHandler] JavaScript运行时错误:', event.error);
                this.handleError(new GameError(
                    `运行时错误: ${event.error?.message || event.message}`,
                    ErrorType.SYSTEM,
                    ErrorSeverity.HIGH,
                    undefined,
                    {
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno,
                        originalError: event.error
                    }
                ));
            }
        });
    }

    /**
     * 处理错误
     */
    public handleError(error: Error | GameError | string, context?: any): void {
        // 防止递归调用
        if (this._isProcessingError) {
            return;
        }

        this._isProcessingError = true;

        try {
            let gameError: GameError;

            if (error instanceof GameError) {
                gameError = error;
            } else if (error instanceof Error) {
                gameError = this.parseError(error, context);
            } else {
                gameError = new GameError(
                    error,
                    ErrorType.SYSTEM,
                    ErrorSeverity.LOW,
                    undefined,
                    context
                );
            }

            // 检查是否应该抑制此错误
            if (this.shouldSuppressError(gameError)) {
                return;
            }

            // 检查是否正在处理相同错误
            const errorKey = `${gameError.type}-${gameError.message}`;
            if (this._processingStack.has(errorKey)) {
                return;
            }

            this._processingStack.add(errorKey);

            try {
                // 记录错误
                this.logError(gameError);

                // 触发错误监听器
                this.notifyErrorListeners(gameError);

                // 根据严重程度处理错误
                this.processErrorBySeverity(gameError);

                // 发送错误事件
                EventManager.getInstance().emit('error-occurred', gameError);
            } finally {
                this._processingStack.delete(errorKey);
            }

        } catch (handlingError) {
            // 如果错误处理本身出错，只做最基本的日志记录
            console.warn('[ErrorHandler] 错误处理过程中发生异常:', handlingError);
        } finally {
            this._isProcessingError = false;
        }
    }

    /**
     * 解析通用错误为游戏错误
     */
    private parseError(error: Error, context?: any): GameError {
        const message = error.message.toLowerCase();

        // 网络相关错误
        if (message.includes('network') || message.includes('fetch') || 
            message.includes('timeout') || message.includes('网络') ||
            message.includes('连接') || message.includes('超时')) {
            return new GameError(
                this.getNetworkErrorMessage(error.message),
                ErrorType.NETWORK,
                ErrorSeverity.MEDIUM,
                this.getNetworkErrorCode(error.message),
                context
            );
        }

        // 认证相关错误
        if (message.includes('auth') || message.includes('login') || 
            message.includes('token') || message.includes('认证') ||
            message.includes('登录') || message.includes('授权')) {
            return new GameError(
                this.getAuthErrorMessage(error.message),
                ErrorType.AUTH,
                ErrorSeverity.HIGH,
                ERROR_CODES.WECHAT_AUTH_FAILED,
                context
            );
        }

        // 音频相关错误
        if (message.includes('audio') || message.includes('sound') || 
            message.includes('音频') || message.includes('音乐')) {
            return new GameError(
                this.getAudioErrorMessage(error.message),
                ErrorType.AUDIO,
                ErrorSeverity.LOW,
                ERROR_CODES.AUDIO_LOAD_FAILED,
                context
            );
        }

        // 存储相关错误
        if (message.includes('storage') || message.includes('localstorage') || 
            message.includes('存储') || message.includes('缓存')) {
            return new GameError(
                this.getStorageErrorMessage(error.message),
                ErrorType.STORAGE,
                ErrorSeverity.LOW,
                ERROR_CODES.SAVE_FAILED,
                context
            );
        }

        // 默认为系统错误
        return new GameError(
            error.message,
            ErrorType.SYSTEM,
            ErrorSeverity.MEDIUM,
            undefined,
            context
        );
    }

    /**
     * 获取网络错误消息
     */
    private getNetworkErrorMessage(originalMessage: string): string {
        if (originalMessage.includes('timeout') || originalMessage.includes('超时')) {
            return '网络连接超时，请检查网络设置后重试';
        }
        if (originalMessage.includes('offline') || originalMessage.includes('断网')) {
            return '网络连接不可用，请检查网络设置';
        }
        if (originalMessage.includes('500') || originalMessage.includes('502') || originalMessage.includes('503')) {
            return '服务器暂时不可用，请稍后重试';
        }
        if (originalMessage.includes('404')) {
            return '请求的资源不存在，请稍后重试';
        }
        if (originalMessage.includes('400') || originalMessage.includes('401') || originalMessage.includes('403')) {
            return '请求参数错误，请重新操作';
        }
        return '网络连接异常，请检查网络设置后重试';
    }

    /**
     * 获取网络错误代码
     */
    private getNetworkErrorCode(message: string): number {
        if (message.includes('timeout')) return ERROR_CODES.REQUEST_TIMEOUT;
        if (message.includes('network')) return ERROR_CODES.NETWORK_ERROR;
        return ERROR_CODES.SERVER_ERROR;
    }

    /**
     * 获取认证错误消息
     */
    private getAuthErrorMessage(originalMessage: string): string {
        if (originalMessage.includes('token')) {
            return '登录状态已过期，请重新登录';
        }
        if (originalMessage.includes('unauthorized') || originalMessage.includes('401')) {
            return '登录验证失败，请重新登录';
        }
        return '登录失败，请重新尝试';
    }

    /**
     * 获取音频错误消息
     */
    private getAudioErrorMessage(originalMessage: string): string {
        if (originalMessage.includes('load') || originalMessage.includes('加载')) {
            return '音频加载失败，可能影响游戏体验';
        }
        if (originalMessage.includes('play') || originalMessage.includes('播放')) {
            return '音频播放失败，请检查设备音量设置';
        }
        if (originalMessage.includes('format') || originalMessage.includes('格式')) {
            return '音频格式不支持，请升级应用版本';
        }
        return '音频系统异常，可能影响游戏体验';
    }

    /**
     * 获取存储错误消息
     */
    private getStorageErrorMessage(originalMessage: string): string {
        if (originalMessage.includes('quota') || originalMessage.includes('space') || originalMessage.includes('存储空间')) {
            return '存储空间不足，请清理设备空间';
        }
        if (originalMessage.includes('permission') || originalMessage.includes('权限')) {
            return '存储权限受限，部分功能可能无法使用';
        }
        return '数据保存失败，游戏进度可能丢失';
    }

    /**
     * 检查是否应该抑制错误
     */
    private shouldSuppressError(error: GameError): boolean {
        const errorKey = `${error.type}-${error.message}`;
        const now = Date.now();

        // 检查时间间隔抑制
        const lastTime = this._lastErrorTime.get(errorKey);
        if (lastTime && (now - lastTime) < this.ERROR_SUPPRESSION_INTERVAL) {
            return true;
        }

        // 检查频率抑制
        const count = this._errorSuppressionMap.get(errorKey) || 0;
        if (count >= this.MAX_ERROR_COUNT_PER_MINUTE) {
            return true;
        }

        // 更新记录
        this._lastErrorTime.set(errorKey, now);
        this._errorSuppressionMap.set(errorKey, count + 1);

        // 定期清理计数器
        setTimeout(() => {
            this._errorSuppressionMap.delete(errorKey);
        }, 60000); // 1分钟后清理

        return false;
    }

    /**
     * 记录错误
     */
    private logError(error: GameError): void {
        try {
            const logLevel = this.getLogLevel(error.severity);
            const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;

            // 使用安全的日志记录方式，避免触发全局错误监听器
            this.safeConsoleLog(logLevel, logMessage, error);

            // 保存错误到本地存储（用于调试和反馈）
            this.saveErrorToStorage(error);
        } catch (logError) {
            // 如果日志记录失败，不再处理，避免无限递归
        }
    }

    /**
     * 安全的控制台日志记录
     */
    private safeConsoleLog(level: string, message: string, error: GameError): void {
        try {
            // 直接调用console方法，避免触发事件监听器
            switch (level) {
                case 'error':
                    if (console.error) console.error(message, error);
                    break;
                case 'warn':
                    if (console.warn) console.warn(message, error);
                    break;
                case 'info':
                    if (console.info) console.info(message, error);
                    break;
                default:
                    if (console.log) console.log(message, error);
            }
        } catch (e) {
            // 忽略控制台日志错误
        }
    }

    /**
     * 获取日志级别
     */
    private getLogLevel(severity: ErrorSeverity): string {
        switch (severity) {
            case ErrorSeverity.CRITICAL:
            case ErrorSeverity.HIGH:
                return 'error';
            case ErrorSeverity.MEDIUM:
                return 'warn';
            case ErrorSeverity.LOW:
                return 'info';
            default:
                return 'log';
        }
    }

    /**
     * 保存错误到本地存储
     */
    private saveErrorToStorage(error: GameError): void {
        try {
            const errorLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');
            
            // 保留最近50条错误记录
            if (errorLogs.length >= 50) {
                errorLogs.splice(0, errorLogs.length - 49);
            }
            
            errorLogs.push({
                timestamp: error.timestamp,
                type: error.type,
                severity: error.severity,
                message: error.message,
                code: error.code,
                context: error.context,
                stack: error.stack
            });
            
            localStorage.setItem('error_logs', JSON.stringify(errorLogs));
        } catch (e) {
            console.warn('[ErrorHandler] 保存错误日志失败:', e);
        }
    }

    /**
     * 根据严重程度处理错误
     */
    private processErrorBySeverity(error: GameError): void {
        switch (error.severity) {
            case ErrorSeverity.CRITICAL:
                this.handleCriticalError(error);
                break;
            case ErrorSeverity.HIGH:
                this.handleHighError(error);
                break;
            case ErrorSeverity.MEDIUM:
                this.handleMediumError(error);
                break;
            case ErrorSeverity.LOW:
                this.handleLowError(error);
                break;
        }
    }

    /**
     * 处理严重错误
     */
    private handleCriticalError(error: GameError): void {
        console.error('[ErrorHandler] 严重错误:', error);
        
        // 显示错误对话框
        this.showErrorDialog(error, true);
        
        // 可能需要重启游戏或返回主菜单
        EventManager.getInstance().emit('critical-error', error);
    }

    /**
     * 处理高级错误
     */
    private handleHighError(error: GameError): void {
        console.error('[ErrorHandler] 高级错误:', error);
        
        // 显示错误提示
        this.showErrorToast(error);
        
        // 可能需要中断当前操作
        if (error.type === ErrorType.AUTH) {
            EventManager.getInstance().emit('auth-error', error);
        }
    }

    /**
     * 处理中级错误
     */
    private handleMediumError(error: GameError): void {
        console.warn('[ErrorHandler] 中级错误:', error);
        
        // 显示简短提示
        this.showErrorToast(error, 3000);
    }

    /**
     * 处理低级错误
     */
    private handleLowError(error: GameError): void {
        console.info('[ErrorHandler] 低级错误:', error);
        
        // 仅记录，不显示给用户
        // 可以发送到统计系统
    }

    /**
     * 显示错误对话框
     */
    private showErrorDialog(error: GameError, isBlocking: boolean = false): void {
        // 这里需要集成具体的UI组件来显示错误对话框
        // 暂时使用console输出代替
        console.log(`[ErrorHandler] 显示错误对话框: ${error.message}`);
        
        // 通过事件系统通知UI显示错误对话框
        EventManager.getInstance().emit('show-error-dialog', {
            error,
            isBlocking
        });
    }

    /**
     * 显示错误Toast提示
     */
    private showErrorToast(error: GameError, duration: number = 5000): void {
        // 这里需要集成具体的UI组件来显示Toast
        // 暂时使用console输出代替
        console.log(`[ErrorHandler] 显示错误Toast: ${error.message} (${duration}ms)`);
        
        // 通过事件系统通知UI显示Toast
        EventManager.getInstance().emit('show-error-toast', {
            message: error.message,
            type: error.type,
            duration
        });
    }

    /**
     * 注册错误监听器
     */
    public addErrorListener(type: ErrorType, listener: (error: GameError) => void): void {
        if (!this._errorListeners.has(type)) {
            this._errorListeners.set(type, []);
        }
        this._errorListeners.get(type)!.push(listener);
    }

    /**
     * 移除错误监听器
     */
    public removeErrorListener(type: ErrorType, listener: (error: GameError) => void): void {
        const listeners = this._errorListeners.get(type);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 通知错误监听器
     */
    private notifyErrorListeners(error: GameError): void {
        const listeners = this._errorListeners.get(error.type);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    listener(error);
                } catch (e) {
                    console.error('[ErrorHandler] 错误监听器执行失败:', e);
                }
            });
        }
    }

    /**
     * 创建网络错误
     */
    public static createNetworkError(message: string, context?: any): GameError {
        return new GameError(message, ErrorType.NETWORK, ErrorSeverity.MEDIUM, ERROR_CODES.NETWORK_ERROR, context);
    }

    /**
     * 创建认证错误
     */
    public static createAuthError(message: string, context?: any): GameError {
        return new GameError(message, ErrorType.AUTH, ErrorSeverity.HIGH, ERROR_CODES.WECHAT_AUTH_FAILED, context);
    }

    /**
     * 创建游戏错误
     */
    public static createGameError(message: string, severity: ErrorSeverity = ErrorSeverity.MEDIUM, context?: any): GameError {
        return new GameError(message, ErrorType.GAME, severity, ERROR_CODES.GAME_DATA_INVALID, context);
    }

    /**
     * 创建音频错误
     */
    public static createAudioError(message: string, context?: any): GameError {
        return new GameError(message, ErrorType.AUDIO, ErrorSeverity.LOW, ERROR_CODES.AUDIO_LOAD_FAILED, context);
    }

    /**
     * 获取错误日志（用于调试和反馈）
     */
    public getErrorLogs(): any[] {
        try {
            return JSON.parse(localStorage.getItem('error_logs') || '[]');
        } catch (e) {
            console.warn('[ErrorHandler] 获取错误日志失败:', e);
            return [];
        }
    }

    /**
     * 清理错误日志
     */
    public clearErrorLogs(): void {
        try {
            localStorage.removeItem('error_logs');
            console.log('[ErrorHandler] 错误日志已清理');
        } catch (e) {
            console.warn('[ErrorHandler] 清理错误日志失败:', e);
        }
    }
}