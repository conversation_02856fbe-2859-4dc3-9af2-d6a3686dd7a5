import { _decorator } from 'cc';
import { EventManager } from '../managers/EventManager';
import { StorageManager } from './StorageManager';
import { NetworkManager } from './NetworkManager';
import { STORAGE_KEYS } from '../constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 网络连接状态
 */
enum NetworkConnectionState {
    STABLE = 'stable',
    UNSTABLE = 'unstable',
    DISCONNECTED = 'disconnected',
    RECOVERING = 'recovering'
}

/**
 * 离线模式配置
 */
interface OfflineModeConfig {
    enabled: boolean;
    maxOfflineTime: number;
    syncOnReconnect: boolean;
    offlineDataRetention: number;
}

/**
 * 网络稳定性监控数据
 */
interface NetworkStabilityMetrics {
    connectionState: NetworkConnectionState;
    lastConnectedTime: number;
    disconnectionCount: number;
    totalOfflineTime: number;
    averageLatency: number;
    packetLossRate: number;
    reconnectionAttempts: number;
}

/**
 * 网络稳定性管理器
 * 处理网络不稳定情况下的游戏连续性和数据一致性
 */
@ccclass('NetworkStabilityManager')
export class NetworkStabilityManager {
    private static _instance: NetworkStabilityManager;
    
    private _eventManager: EventManager;
    private _storageManager: StorageManager;
    private _networkManager: NetworkManager;
    
    // 网络状态监控
    private _connectionState: NetworkConnectionState = NetworkConnectionState.STABLE;
    private _lastConnectedTime: number = Date.now();
    private _disconnectionStartTime: number = 0;
    private _stabilityMetrics: NetworkStabilityMetrics;
    
    // 离线模式管理
    private _offlineMode: OfflineModeConfig = {
        enabled: true,
        maxOfflineTime: 300000, // 5分钟最大离线时间
        syncOnReconnect: true,
        offlineDataRetention: 86400000 // 24小时离线数据保留
    };
    
    // 数据同步队列
    private _syncQueue: Array<{ id: string; type: string; data: any; timestamp: number }> = [];
    private _maxSyncQueueSize: number = 100;
    
    // 重连机制
    private _reconnectionTimer: number = 0;
    private _reconnectionAttempts: number = 0;
    private _maxReconnectionAttempts: number = 10;
    private _reconnectionDelay: number = 1000; // 初始重连延迟1秒
    
    // 网络质量监控
    private _pingResults: number[] = [];
    private _pingTimer: number = 0;
    private _pingInterval: number = 30000; // 30秒ping一次
    
    // 游戏状态备份
    private _gameStateBackup: Map<string, any> = new Map();
    private _autoBackupInterval: number = 10000; // 10秒自动备份一次
    private _backupTimer: number = 0;

    public static getInstance(): NetworkStabilityManager {
        if (!NetworkStabilityManager._instance) {
            NetworkStabilityManager._instance = new NetworkStabilityManager();
        }
        return NetworkStabilityManager._instance;
    }

    private constructor() {
        this._eventManager = EventManager.getInstance();
        this._storageManager = new StorageManager();
        this._networkManager = NetworkManager.getInstance();
        
        this.initializeStabilityMetrics();
        this.setupEventListeners();
        this.startNetworkMonitoring();
        this.startGameStateBackup();
    }

    /**
     * 初始化稳定性指标
     */
    private initializeStabilityMetrics(): void {
        this._stabilityMetrics = {
            connectionState: NetworkConnectionState.STABLE,
            lastConnectedTime: Date.now(),
            disconnectionCount: 0,
            totalOfflineTime: 0,
            averageLatency: 0,
            packetLossRate: 0,
            reconnectionAttempts: 0
        };
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 监听网络状态变化
        this._eventManager.on('network_status_changed', this.onNetworkStatusChanged, this);
        this._eventManager.on('network_recovered', this.onNetworkRecovered, this);
        
        // 监听API请求状态
        this._eventManager.on('api_request_error', this.onApiRequestError, this);
        this._eventManager.on('api_request_success', this.onApiRequestSuccess, this);
        
        // 监听游戏状态变化
        this._eventManager.on('game_session_started', this.onGameSessionStarted, this);
        this._eventManager.on('game_finished', this.onGameFinished, this);
        this._eventManager.on('answer_submitted', this.onAnswerSubmitted, this);
        
        // 监听应用生命周期
        this._eventManager.on('app_pause', this.onAppPause, this);
        this._eventManager.on('app_resume', this.onAppResume, this);
    }

    /**
     * 开始网络监控
     */
    private startNetworkMonitoring(): void {
        // 定期ping测试网络质量
        this._pingTimer = setInterval(() => {
            this.performNetworkQualityCheck();
        }, this._pingInterval);

        // 初始网络状态检查
        this.performNetworkQualityCheck();
    }

    /**
     * 开始游戏状态自动备份
     */
    private startGameStateBackup(): void {
        this._backupTimer = setInterval(() => {
            this.backupCurrentGameState();
        }, this._autoBackupInterval);
    }

    /**
     * 执行网络质量检查
     */
    private async performNetworkQualityCheck(): Promise<void> {
        try {
            const startTime = Date.now();
            const isReachable = await this._networkManager.pingServer();
            const latency = Date.now() - startTime;

            if (isReachable) {
                this._pingResults.push(latency);
                
                // 保持最近20次ping结果
                if (this._pingResults.length > 20) {
                    this._pingResults.shift();
                }

                // 更新平均延迟
                this._stabilityMetrics.averageLatency = 
                    this._pingResults.reduce((sum, ping) => sum + ping, 0) / this._pingResults.length;

                // 评估网络稳定性
                this.evaluateNetworkStability();
                
            } else {
                // ping失败，记录丢包
                this._stabilityMetrics.packetLossRate = 
                    (this._stabilityMetrics.packetLossRate * 0.9) + (0.1 * 1.0);
            }

        } catch (error) {
            console.warn('[NetworkStabilityManager] 网络质量检查失败:', error);
            this._stabilityMetrics.packetLossRate = 
                (this._stabilityMetrics.packetLossRate * 0.9) + (0.1 * 1.0);
        }
    }

    /**
     * 评估网络稳定性
     */
    private evaluateNetworkStability(): void {
        const avgLatency = this._stabilityMetrics.averageLatency;
        const packetLoss = this._stabilityMetrics.packetLossRate;
        
        let newState = NetworkConnectionState.STABLE;

        if (packetLoss > 0.3 || avgLatency > 2000) {
            newState = NetworkConnectionState.DISCONNECTED;
        } else if (packetLoss > 0.1 || avgLatency > 1000) {
            newState = NetworkConnectionState.UNSTABLE;
        }

        if (newState !== this._connectionState) {
            this.updateConnectionState(newState);
        }
    }

    /**
     * 更新连接状态
     */
    private updateConnectionState(newState: NetworkConnectionState): void {
        const oldState = this._connectionState;
        this._connectionState = newState;
        this._stabilityMetrics.connectionState = newState;

        console.log(`[NetworkStabilityManager] 连接状态变化: ${oldState} -> ${newState}`);

        // 根据状态变化执行相应操作
        switch (newState) {
            case NetworkConnectionState.DISCONNECTED:
                this.handleDisconnection();
                break;
            case NetworkConnectionState.UNSTABLE:
                this.handleUnstableConnection();
                break;
            case NetworkConnectionState.STABLE:
                if (oldState === NetworkConnectionState.DISCONNECTED || 
                    oldState === NetworkConnectionState.RECOVERING) {
                    this.handleConnectionRecovery();
                }
                break;
            case NetworkConnectionState.RECOVERING:
                this.handleConnectionRecovering();
                break;
        }

        // 触发状态变化事件
        this._eventManager.emit('network_stability_changed', {
            oldState,
            newState,
            metrics: this._stabilityMetrics
        });
    }

    /**
     * 处理网络断开
     */
    private handleDisconnection(): void {
        this._disconnectionStartTime = Date.now();
        this._stabilityMetrics.disconnectionCount++;

        console.log('[NetworkStabilityManager] 网络断开，启用离线模式');

        // 启用离线模式
        if (this._offlineMode.enabled) {
            this.enableOfflineMode();
        }

        // 开始重连尝试
        this.startReconnectionAttempts();

        // 通知用户网络问题
        this._eventManager.emit('network_disconnected', {
            timestamp: Date.now(),
            offlineModeEnabled: this._offlineMode.enabled
        });
    }

    /**
     * 处理不稳定连接
     */
    private handleUnstableConnection(): void {
        console.log('[NetworkStabilityManager] 网络不稳定，调整策略');

        // 降低网络请求频率
        this._eventManager.emit('network_unstable', {
            averageLatency: this._stabilityMetrics.averageLatency,
            packetLossRate: this._stabilityMetrics.packetLossRate,
            suggestion: 'reduce_network_activity'
        });

        // 增加本地缓存依赖
        this._eventManager.emit('increase_cache_reliance');
    }

    /**
     * 处理连接恢复
     */
    private handleConnectionRecovery(): void {
        console.log('[NetworkStabilityManager] 网络连接已恢复');

        // 计算离线时间
        if (this._disconnectionStartTime > 0) {
            const offlineTime = Date.now() - this._disconnectionStartTime;
            this._stabilityMetrics.totalOfflineTime += offlineTime;
            this._disconnectionStartTime = 0;
        }

        // 重置重连计数
        this._reconnectionAttempts = 0;
        this.stopReconnectionAttempts();

        // 禁用离线模式
        this.disableOfflineMode();

        // 同步离线数据
        if (this._offlineMode.syncOnReconnect) {
            this.syncOfflineData();
        }

        // 恢复正常网络操作
        this._eventManager.emit('network_recovered', {
            timestamp: Date.now(),
            offlineTime: this._stabilityMetrics.totalOfflineTime
        });
    }

    /**
     * 处理连接恢复中
     */
    private handleConnectionRecovering(): void {
        console.log('[NetworkStabilityManager] 网络连接恢复中...');
        
        this._eventManager.emit('network_recovering', {
            attempts: this._reconnectionAttempts,
            maxAttempts: this._maxReconnectionAttempts
        });
    }

    /**
     * 启用离线模式
     */
    private enableOfflineMode(): void {
        console.log('[NetworkStabilityManager] 启用离线模式');

        // 保存当前游戏状态
        this.backupCurrentGameState();

        // 通知各模块进入离线模式
        this._eventManager.emit('offline_mode_enabled', {
            maxOfflineTime: this._offlineMode.maxOfflineTime,
            dataRetention: this._offlineMode.offlineDataRetention
        });
    }

    /**
     * 禁用离线模式
     */
    private disableOfflineMode(): void {
        console.log('[NetworkStabilityManager] 禁用离线模式');

        this._eventManager.emit('offline_mode_disabled', {
            timestamp: Date.now()
        });
    }

    /**
     * 开始重连尝试
     */
    private startReconnectionAttempts(): void {
        this.stopReconnectionAttempts(); // 先停止现有的重连

        const attemptReconnection = async () => {
            if (this._reconnectionAttempts >= this._maxReconnectionAttempts) {
                console.log('[NetworkStabilityManager] 重连次数已达上限，停止重连');
                this._eventManager.emit('reconnection_failed', {
                    attempts: this._reconnectionAttempts,
                    giveUp: true
                });
                return;
            }

            this._reconnectionAttempts++;
            this._stabilityMetrics.reconnectionAttempts = this._reconnectionAttempts;

            console.log(`[NetworkStabilityManager] 重连尝试 ${this._reconnectionAttempts}/${this._maxReconnectionAttempts}`);

            try {
                // 更新状态为恢复中
                if (this._connectionState === NetworkConnectionState.DISCONNECTED) {
                    this.updateConnectionState(NetworkConnectionState.RECOVERING);
                }

                const isConnected = await this._networkManager.pingServer();
                
                if (isConnected) {
                    console.log('[NetworkStabilityManager] 重连成功');
                    this.updateConnectionState(NetworkConnectionState.STABLE);
                    return;
                }

            } catch (error) {
                console.warn(`[NetworkStabilityManager] 重连失败: ${error.message}`);
            }

            // 计算下次重连延迟（指数退避）
            const delay = Math.min(
                this._reconnectionDelay * Math.pow(2, this._reconnectionAttempts - 1),
                30000 // 最大30秒延迟
            );

            this._reconnectionTimer = setTimeout(attemptReconnection, delay);
        };

        // 立即开始第一次重连尝试
        attemptReconnection();
    }

    /**
     * 停止重连尝试
     */
    private stopReconnectionAttempts(): void {
        if (this._reconnectionTimer) {
            clearTimeout(this._reconnectionTimer);
            this._reconnectionTimer = 0;
        }
    }

    /**
     * 备份当前游戏状态
     */
    private async backupCurrentGameState(): Promise<void> {
        try {
            // 从各个管理器收集游戏状态
            const gameState = {
                timestamp: Date.now(),
                gameProgress: this._storageManager.getItem(STORAGE_KEYS.GAME_PROGRESS),
                userProfile: this._storageManager.getItem(STORAGE_KEYS.USER_PROFILE),
                audioCache: this._storageManager.getItem('audio_cache_info'),
                userBehavior: this._storageManager.getItem('user_behavior_pattern'),
                networkMetrics: this._stabilityMetrics
            };

            // 保存到本地存储
            await this._storageManager.setItem('game_state_backup', gameState);
            
            // 添加到内存备份
            this._gameStateBackup.set('latest', gameState);
            
            console.log('[NetworkStabilityManager] 游戏状态备份完成');

        } catch (error) {
            console.error('[NetworkStabilityManager] 备份游戏状态失败:', error);
        }
    }

    /**
     * 恢复游戏状态
     */
    public async restoreGameState(): Promise<boolean> {
        try {
            const backup = await this._storageManager.getItem('game_state_backup');
            
            if (!backup) {
                console.warn('[NetworkStabilityManager] 没有找到游戏状态备份');
                return false;
            }

            // 检查备份是否过期
            const backupAge = Date.now() - backup.timestamp;
            if (backupAge > this._offlineMode.offlineDataRetention) {
                console.warn('[NetworkStabilityManager] 游戏状态备份已过期');
                return false;
            }

            // 恢复各项数据
            if (backup.gameProgress) {
                this._storageManager.setItem(STORAGE_KEYS.GAME_PROGRESS, backup.gameProgress);
            }

            if (backup.userProfile) {
                this._storageManager.setItem(STORAGE_KEYS.USER_PROFILE, backup.userProfile);
            }

            console.log('[NetworkStabilityManager] 游戏状态恢复完成');
            
            // 通知恢复完成
            this._eventManager.emit('game_state_restored', {
                timestamp: backup.timestamp,
                dataAge: backupAge
            });

            return true;

        } catch (error) {
            console.error('[NetworkStabilityManager] 恢复游戏状态失败:', error);
            return false;
        }
    }

    /**
     * 同步离线数据
     */
    private async syncOfflineData(): Promise<void> {
        if (this._syncQueue.length === 0) {
            console.log('[NetworkStabilityManager] 没有离线数据需要同步');
            return;
        }

        console.log(`[NetworkStabilityManager] 开始同步 ${this._syncQueue.length} 条离线数据`);

        try {
            // 按时间戳排序
            this._syncQueue.sort((a, b) => a.timestamp - b.timestamp);

            let successCount = 0;
            let failCount = 0;

            for (const item of this._syncQueue) {
                try {
                    await this.syncSingleOfflineItem(item);
                    successCount++;
                } catch (error) {
                    console.warn(`[NetworkStabilityManager] 同步离线数据失败: ${item.id}`, error);
                    failCount++;
                }
            }

            // 清理已同步的数据
            this._syncQueue = [];

            console.log(`[NetworkStabilityManager] 离线数据同步完成: ${successCount} 成功, ${failCount} 失败`);

            this._eventManager.emit('offline_data_synced', {
                successCount,
                failCount,
                timestamp: Date.now()
            });

        } catch (error) {
            console.error('[NetworkStabilityManager] 同步离线数据失败:', error);
        }
    }

    /**
     * 同步单个离线数据项
     */
    private async syncSingleOfflineItem(item: any): Promise<void> {
        switch (item.type) {
            case 'game_result':
                // 同步游戏结果
                await this._networkManager.submitGameResult(item.data.sessionId, item.data);
                break;
            case 'user_stats':
                // 同步用户统计数据
                break;
            case 'answer_record':
                // 同步答题记录
                break;
            default:
                console.warn(`[NetworkStabilityManager] 未知的离线数据类型: ${item.type}`);
        }
    }

    /**
     * 添加到同步队列
     */
    public addToSyncQueue(type: string, data: any): void {
        const item = {
            id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type,
            data,
            timestamp: Date.now()
        };

        this._syncQueue.push(item);

        // 保持队列大小
        if (this._syncQueue.length > this._maxSyncQueueSize) {
            this._syncQueue.shift();
        }

        console.log(`[NetworkStabilityManager] 添加到同步队列: ${type}`);
    }

    // ================== 事件处理方法 ==================

    private onNetworkStatusChanged(status: any): void {
        if (!status.isOnline && this._connectionState !== NetworkConnectionState.DISCONNECTED) {
            this.updateConnectionState(NetworkConnectionState.DISCONNECTED);
        } else if (status.isOnline && this._connectionState === NetworkConnectionState.DISCONNECTED) {
            this.updateConnectionState(NetworkConnectionState.RECOVERING);
        }
    }

    private onNetworkRecovered(): void {
        if (this._connectionState !== NetworkConnectionState.STABLE) {
            this.updateConnectionState(NetworkConnectionState.STABLE);
        }
    }

    private onApiRequestError(data: any): void {
        // API请求失败，可能是网络问题
        console.log('[NetworkStabilityManager] API请求失败，记录网络质量');
        this._stabilityMetrics.packetLossRate = 
            (this._stabilityMetrics.packetLossRate * 0.9) + (0.1 * 1.0);
    }

    private onApiRequestSuccess(data: any): void {
        // API请求成功，记录延迟
        if (data.duration) {
            this._pingResults.push(data.duration);
            if (this._pingResults.length > 20) {
                this._pingResults.shift();
            }
        }
    }

    private onGameSessionStarted(data: any): void {
        // 游戏开始时备份状态
        this.backupCurrentGameState();
    }

    private onGameFinished(data: any): void {
        // 游戏结束时处理数据同步
        if (this._connectionState === NetworkConnectionState.DISCONNECTED) {
            this.addToSyncQueue('game_result', data);
        }
    }

    private onAnswerSubmitted(data: any): void {
        // 答题时备份进度
        if (this._connectionState === NetworkConnectionState.DISCONNECTED) {
            this.addToSyncQueue('answer_record', data);
        }
    }

    private onAppPause(): void {
        // 应用暂停时备份状态
        this.backupCurrentGameState();
    }

    private onAppResume(): void {
        // 应用恢复时检查网络状态
        setTimeout(() => {
            this.performNetworkQualityCheck();
        }, 1000);
    }

    // ================== 公共方法 ==================

    /**
     * 获取网络稳定性指标
     */
    public getStabilityMetrics(): NetworkStabilityMetrics {
        return { ...this._stabilityMetrics };
    }

    /**
     * 获取连接状态
     */
    public getConnectionState(): NetworkConnectionState {
        return this._connectionState;
    }

    /**
     * 获取同步队列状态
     */
    public getSyncQueueStatus(): any {
        return {
            queueSize: this._syncQueue.length,
            maxSize: this._maxSyncQueueSize,
            oldestItem: this._syncQueue.length > 0 ? this._syncQueue[0].timestamp : null
        };
    }

    /**
     * 手动触发网络检查
     */
    public async checkNetworkNow(): Promise<void> {
        await this.performNetworkQualityCheck();
    }

    /**
     * 手动触发重连
     */
    public forceReconnect(): void {
        this._reconnectionAttempts = 0;
        this.startReconnectionAttempts();
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        this.stopReconnectionAttempts();
        
        if (this._pingTimer) {
            clearInterval(this._pingTimer);
            this._pingTimer = 0;
        }
        
        if (this._backupTimer) {
            clearInterval(this._backupTimer);
            this._backupTimer = 0;
        }

        this._syncQueue = [];
        this._gameStateBackup.clear();
        
        console.log('[NetworkStabilityManager] 资源清理完成');
    }
}