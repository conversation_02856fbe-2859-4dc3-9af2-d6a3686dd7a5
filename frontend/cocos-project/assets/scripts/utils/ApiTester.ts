import { NetworkManager } from './NetworkManager';
import { GameDifficulty } from '../constants/GameConstants';
import { EventManager } from '../managers/EventManager';

/**
 * API测试工具
 * 专门用于帮助backend-agent诊断和修复API问题
 */
export class ApiTester {
    private static _instance: ApiTester;
    private _networkManager: NetworkManager;
    private _eventManager: EventManager;
    private _testResults: Map<string, any> = new Map();

    public static getInstance(): ApiTester {
        if (!ApiTester._instance) {
            ApiTester._instance = new ApiTester();
        }
        return ApiTester._instance;
    }

    private constructor() {
        this._networkManager = NetworkManager.getInstance();
        this._eventManager = EventManager.getInstance();
    }

    /**
     * 运行完整的API测试套件
     */
    public async runFullTestSuite(): Promise<any> {
        console.group('🧪 [API测试] 开始完整测试套件');
        
        const testSuite = {
            timestamp: new Date().toISOString(),
            testResults: {},
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                duration: 0
            }
        };

        const startTime = Date.now();

        try {
            // 1. 基础连接测试
            testSuite.testResults['connectivity'] = await this.testConnectivity();

            // 2. 认证流程测试
            testSuite.testResults['authentication'] = await this.testAuthentication();

            // 3. 游戏API测试
            testSuite.testResults['game_apis'] = await this.testGameApis();

            // 4. 数据完整性测试
            testSuite.testResults['data_integrity'] = await this.testDataIntegrity();

            // 5. 性能基准测试
            testSuite.testResults['performance'] = await this.testPerformance();

            // 6. 错误处理测试
            testSuite.testResults['error_handling'] = await this.testErrorHandling();

        } catch (error) {
            console.error('[ApiTester] 测试套件执行失败:', error);
            testSuite.testResults['suite_error'] = {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }

        // 计算统计信息
        testSuite.summary.duration = Date.now() - startTime;
        this.calculateSummary(testSuite);

        console.groupEnd();
        this.generateTestReport(testSuite);

        return testSuite;
    }

    /**
     * 测试基础连接
     */
    private async testConnectivity(): Promise<any> {
        const test = {
            name: '基础连接测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // 网络状态检查
            const networkStatus = await this._networkManager.checkNetworkStatus();
            test.tests['network_status'] = {
                success: networkStatus.isOnline,
                data: networkStatus,
                message: networkStatus.isOnline ? '网络连接正常' : '网络连接异常'
            };

            // 服务器ping测试
            const pingSuccess = await this._networkManager.pingServer();
            test.tests['server_ping'] = {
                success: pingSuccess,
                message: pingSuccess ? '服务器连接正常' : '服务器连接失败'
            };

            // DNS解析测试（模拟）
            test.tests['dns_resolution'] = {
                success: true,
                message: 'DNS解析正常（模拟测试）'
            };

        } catch (error) {
            test.tests['connectivity_error'] = {
                success: false,
                error: error.message,
                message: '连接测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 测试认证流程
     */
    private async testAuthentication(): Promise<any> {
        const test = {
            name: '认证流程测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // 检查当前登录状态
            const isLoggedIn = this._networkManager.isLoggedIn();
            test.tests['login_status'] = {
                success: true,
                data: { isLoggedIn },
                message: isLoggedIn ? '用户已登录' : '用户未登录'
            };

            // 如果未登录，尝试登录
            if (!isLoggedIn) {
                try {
                    const user = await this._networkManager.wechatLogin();
                    test.tests['wechat_login'] = {
                        success: true,
                        data: { userId: user.userId, nickname: user.nickname },
                        message: '微信登录成功'
                    };
                } catch (error) {
                    test.tests['wechat_login'] = {
                        success: false,
                        error: error.message,
                        message: '微信登录失败'
                    };
                }
            }

            // 测试用户信息获取
            try {
                const currentUser = await this._networkManager.getCurrentUser();
                test.tests['get_user_info'] = {
                    success: true,
                    data: { userId: currentUser.userId },
                    message: '获取用户信息成功'
                };
            } catch (error) {
                test.tests['get_user_info'] = {
                    success: false,
                    error: error.message,
                    message: '获取用户信息失败'
                };
            }

        } catch (error) {
            test.tests['auth_error'] = {
                success: false,
                error: error.message,
                message: '认证测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 测试游戏API
     */
    private async testGameApis(): Promise<any> {
        const test = {
            name: '游戏API测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // 测试获取题目
            try {
                const questions = await this._networkManager.getQuestions(5, GameDifficulty.EASY);
                test.tests['get_questions'] = {
                    success: questions && questions.length > 0,
                    data: { questionCount: questions?.length || 0 },
                    message: questions?.length > 0 ? `成功获取${questions.length}道题目` : '获取题目为空'
                };
            } catch (error) {
                test.tests['get_questions'] = {
                    success: false,
                    error: error.message,
                    message: '获取题目失败'
                };
            }

            // 测试创建游戏会话
            try {
                const sessionId = await this._networkManager.createGameSession(GameDifficulty.EASY);
                test.tests['create_session'] = {
                    success: !!sessionId,
                    data: { sessionId },
                    message: sessionId ? '创建游戏会话成功' : '创建游戏会话失败'
                };

                // 如果会话创建成功，测试提交结果
                if (sessionId) {
                    try {
                        const mockGameSession = {
                            sessionId,
                            startTime: Date.now() - 60000,
                            endTime: Date.now(),
                            totalScore: 100,
                            correctCount: 3,
                            questions: [1, 2, 3, 4, 5], // mock数据
                            answers: [],
                            difficulty: GameDifficulty.EASY
                        };

                        const submitSuccess = await this._networkManager.submitGameResult(sessionId, mockGameSession as any);
                        test.tests['submit_result'] = {
                            success: submitSuccess,
                            message: submitSuccess ? '提交游戏结果成功' : '提交游戏结果失败'
                        };
                    } catch (error) {
                        test.tests['submit_result'] = {
                            success: false,
                            error: error.message,
                            message: '提交游戏结果异常'
                        };
                    }
                }

            } catch (error) {
                test.tests['create_session'] = {
                    success: false,
                    error: error.message,
                    message: '创建游戏会话异常'
                };
            }

            // 测试用户统计
            try {
                const user = await this._networkManager.getCurrentUser();
                if (user) {
                    const stats = await this._networkManager.getUserStats(user.userId);
                    test.tests['get_user_stats'] = {
                        success: !!stats,
                        data: stats,
                        message: stats ? '获取用户统计成功' : '获取用户统计失败'
                    };
                }
            } catch (error) {
                test.tests['get_user_stats'] = {
                    success: false,
                    error: error.message,
                    message: '获取用户统计异常'
                };
            }

        } catch (error) {
            test.tests['game_api_error'] = {
                success: false,
                error: error.message,
                message: '游戏API测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 测试数据完整性
     */
    private async testDataIntegrity(): Promise<any> {
        const test = {
            name: '数据完整性测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // 测试题目数据结构
            try {
                const questions = await this._networkManager.getQuestions(3, GameDifficulty.EASY);
                if (questions && questions.length > 0) {
                    const question = questions[0];
                    const requiredFields = ['id', 'question', 'options', 'correctAnswer', 'difficulty', 'dialect'];
                    const missingFields = requiredFields.filter(field => !(field in question));

                    test.tests['question_structure'] = {
                        success: missingFields.length === 0,
                        data: { missingFields, sampleQuestion: question },
                        message: missingFields.length === 0 ? '题目数据结构完整' : `缺少字段: ${missingFields.join(', ')}`
                    };
                } else {
                    test.tests['question_structure'] = {
                        success: false,
                        message: '无法获取题目数据进行结构测试'
                    };
                }
            } catch (error) {
                test.tests['question_structure'] = {
                    success: false,
                    error: error.message,
                    message: '题目数据结构测试异常'
                };
            }

            // 测试不同难度的题目
            for (const difficulty of [GameDifficulty.EASY, GameDifficulty.MEDIUM, GameDifficulty.HARD]) {
                try {
                    const questions = await this._networkManager.getQuestions(2, difficulty);
                    test.tests[`difficulty_${difficulty}`] = {
                        success: questions && questions.length > 0,
                        data: { count: questions?.length || 0 },
                        message: questions?.length > 0 ? `${difficulty}难度题目可用` : `${difficulty}难度题目不可用`
                    };
                } catch (error) {
                    test.tests[`difficulty_${difficulty}`] = {
                        success: false,
                        error: error.message,
                        message: `${difficulty}难度题目测试异常`
                    };
                }
            }

        } catch (error) {
            test.tests['data_integrity_error'] = {
                success: false,
                error: error.message,
                message: '数据完整性测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 测试性能基准
     */
    private async testPerformance(): Promise<any> {
        const test = {
            name: '性能基准测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // API响应时间测试
            const apiTests = [
                { name: 'health_check', method: () => this._networkManager.pingServer() },
                { name: 'get_questions', method: () => this._networkManager.getQuestions(1, GameDifficulty.EASY) },
                { name: 'create_session', method: () => this._networkManager.createGameSession(GameDifficulty.EASY) }
            ];

            for (const apiTest of apiTests) {
                const times = [];
                const errors = [];

                // 执行3次测试
                for (let i = 0; i < 3; i++) {
                    const startTime = Date.now();
                    try {
                        await apiTest.method();
                        times.push(Date.now() - startTime);
                    } catch (error) {
                        errors.push(error.message);
                    }
                }

                const avgTime = times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
                const successRate = times.length / 3;

                test.tests[`performance_${apiTest.name}`] = {
                    success: successRate >= 0.6, // 至少60%成功率
                    data: {
                        avgResponseTime: Math.round(avgTime),
                        successRate: Math.round(successRate * 100),
                        times,
                        errors
                    },
                    message: `平均响应时间: ${Math.round(avgTime)}ms, 成功率: ${Math.round(successRate * 100)}%`
                };
            }

        } catch (error) {
            test.tests['performance_error'] = {
                success: false,
                error: error.message,
                message: '性能测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 测试错误处理
     */
    private async testErrorHandling(): Promise<any> {
        const test = {
            name: '错误处理测试',
            startTime: Date.now(),
            tests: {}
        };

        try {
            // 测试无效URL
            try {
                await this._networkManager['requestWithRetry']({
                    method: 'GET',
                    url: '/v1/nonexistent-endpoint'
                });
                test.tests['invalid_endpoint'] = {
                    success: false,
                    message: '无效端点应该返回错误'
                };
            } catch (error) {
                test.tests['invalid_endpoint'] = {
                    success: true,
                    message: `正确捕获错误: ${error.message}`
                };
            }

            // 测试超时处理
            try {
                await this._networkManager['requestWithRetry']({
                    method: 'GET',
                    url: '/v1/health',
                    timeout: 1 // 1ms超时
                });
                test.tests['timeout_handling'] = {
                    success: false,
                    message: '超时请求应该被正确处理'
                };
            } catch (error) {
                test.tests['timeout_handling'] = {
                    success: true,
                    message: `正确处理超时: ${error.message}`
                };
            }

            // 测试网络错误重试
            const originalNetworkStatus = this._networkManager['_networkStatus'];
            this._networkManager['_networkStatus'] = { isOnline: false };

            try {
                await this._networkManager['requestWithRetry']({
                    method: 'GET',
                    url: '/v1/health',
                    retryConfig: { maxRetries: 1 }
                });
                test.tests['offline_retry'] = {
                    success: false,
                    message: '离线状态应该正确处理'
                };
            } catch (error) {
                test.tests['offline_retry'] = {
                    success: true,
                    message: `正确处理离线状态: ${error.message}`
                };
            } finally {
                // 恢复网络状态
                this._networkManager['_networkStatus'] = originalNetworkStatus;
            }

        } catch (error) {
            test.tests['error_handling_error'] = {
                success: false,
                error: error.message,
                message: '错误处理测试异常'
            };
        }

        test.duration = Date.now() - test.startTime;
        return test;
    }

    /**
     * 计算测试统计信息
     */
    private calculateSummary(testSuite: any): void {
        let total = 0;
        let passed = 0;

        const countTests = (obj: any) => {
            for (const key in obj) {
                if (key === 'tests' && typeof obj[key] === 'object') {
                    for (const testKey in obj[key]) {
                        total++;
                        if (obj[key][testKey].success) {
                            passed++;
                        }
                    }
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    countTests(obj[key]);
                }
            }
        };

        countTests(testSuite.testResults);

        testSuite.summary.total = total;
        testSuite.summary.passed = passed;
        testSuite.summary.failed = total - passed;
    }

    /**
     * 生成测试报告
     */
    private generateTestReport(testSuite: any): void {
        console.group('📊 [API测试报告]');
        console.log(`⏱️  执行时间: ${testSuite.summary.duration}ms`);
        console.log(`✅ 通过: ${testSuite.summary.passed}`);
        console.log(`❌ 失败: ${testSuite.summary.failed}`);
        console.log(`📈 成功率: ${((testSuite.summary.passed / testSuite.summary.total) * 100).toFixed(1)}%`);

        // 详细结果
        for (const [category, results] of Object.entries(testSuite.testResults)) {
            console.group(`📋 ${results.name || category}`);
            if (results.tests) {
                for (const [testName, result] of Object.entries(results.tests)) {
                    const icon = result.success ? '✅' : '❌';
                    console.log(`${icon} ${testName}: ${result.message}`);
                    if (!result.success && result.error) {
                        console.error(`   错误详情: ${result.error}`);
                    }
                }
            }
            console.groupEnd();
        }

        console.groupEnd();

        // 触发测试完成事件
        this._eventManager.emit('api_test_completed', testSuite);
    }

    /**
     * 获取存储的测试结果
     */
    public getTestResults(): Map<string, any> {
        return this._testResults;
    }

    /**
     * 清理测试数据
     */
    public cleanup(): void {
        this._testResults.clear();
        console.log('[ApiTester] 测试数据已清理');
    }
}