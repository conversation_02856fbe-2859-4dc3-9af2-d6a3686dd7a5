import { _decorator } from 'cc';
import { BaseComponent } from './BaseComponent';
import { EventManager } from '../managers/EventManager';
import { StorageManager } from './StorageManager';

const { ccclass } = _decorator;

/**
 * 微信小游戏API封装
 * 提供登录、分享、支付等核心功能
 */
export interface IWechatUserInfo {
    avatarUrl: string;
    city?: string;
    country?: string;
    gender: number;
    language: string;
    nickName: string;
    province?: string;
}

export interface IWechatLoginResult {
    code: string;
    errMsg: string;
}

export interface IWechatShareOptions {
    title: string;
    imageUrl?: string;
    query?: string;
    success?: () => void;
    fail?: (error: any) => void;
}

export interface IWechatVibrateOptions {
    type: 'heavy' | 'medium' | 'light';
}

export interface IWechatLaunchOptions {
    scene: number;
    query: any;
    shareTicket?: string;
    referrerInfo?: any;
}

@ccclass('WechatAPI')
export class WechatAPI extends BaseComponent {
    private static _instance: WechatAPI = null;
    
    // 用户信息缓存
    private _userInfo: IWechatUserInfo | null = null;
    private _isLoggedIn: boolean = false;
    
    // 启动参数
    private _launchOptions: IWechatLaunchOptions | null = null;
    
    // 存储管理器
    private _storageManager: StorageManager = null;
    
    public static getInstance(): WechatAPI {
        if (!this._instance) {
            this._instance = new WechatAPI();
        }
        return this._instance;
    }
    
    protected onLoad(): void {
        if (WechatAPI._instance === null) {
            WechatAPI._instance = this;
            this.initialize();
        } else {
            this.destroy();
        }
    }
    
    protected onDestroy(): void {
        if (WechatAPI._instance === this) {
            WechatAPI._instance = null;
        }
    }
    
    /**
     * 初始化微信API
     */
    public async initialize(): Promise<void> {
        try {
            console.log('[WechatAPI] 初始化微信小游戏API');
            
            // 初始化存储管理器
            this._storageManager = new StorageManager();
            
            // 检查微信环境
            if (!this.isWechatEnvironment()) {
                console.warn('[WechatAPI] 非微信环境，部分功能将不可用');
                return;
            }
            
            // 获取启动参数
            this._launchOptions = this.getLaunchOptions();
            
            // 注册生命周期事件
            this.registerLifecycleEvents();
            
            // 加载用户信息缓存
            await this.loadUserInfoCache();
            
            console.log('[WechatAPI] 微信API初始化完成');
            
        } catch (error) {
            console.error('[WechatAPI] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 检查是否在微信环境中
     */
    public isWechatEnvironment(): boolean {
        return typeof wx !== 'undefined' && wx !== null;
    }
    
    /**
     * 注册生命周期事件
     */
    private registerLifecycleEvents(): void {
        if (!this.isWechatEnvironment()) return;
        
        // 监听小程序显示
        wx.onShow((res) => {
            console.log('[WechatAPI] 小程序显示:', res);
            this._eventManager?.emit('wechat_show', res);
        });
        
        // 监听小程序隐藏
        wx.onHide(() => {
            console.log('[WechatAPI] 小程序隐藏');
            this._eventManager?.emit('wechat_hide');
        });
        
        // 监听音频中断开始
        wx.onAudioInterruptionBegin(() => {
            console.log('[WechatAPI] 音频中断开始');
            this._eventManager?.emit('audio_interruption_begin');
        });
        
        // 监听音频中断结束
        wx.onAudioInterruptionEnd(() => {
            console.log('[WechatAPI] 音频中断结束');
            this._eventManager?.emit('audio_interruption_end');
        });
    }
    
    /**
     * 获取启动参数
     */
    public getLaunchOptions(): IWechatLaunchOptions | null {
        if (!this.isWechatEnvironment()) return null;
        
        try {
            const options = wx.getLaunchOptionsSync();
            console.log('[WechatAPI] 启动参数:', options);
            return options;
        } catch (error) {
            console.error('[WechatAPI] 获取启动参数失败:', error);
            return null;
        }
    }
    
    /**
     * 用户登录
     */
    public async login(): Promise<IWechatLoginResult> {
        return new Promise((resolve, reject) => {
            if (!this.isWechatEnvironment()) {
                reject(new Error('非微信环境'));
                return;
            }
            
            console.log('[WechatAPI] 开始用户登录');
            
            wx.login({
                success: (res) => {
                    console.log('[WechatAPI] 登录成功:', res);
                    this._isLoggedIn = true;
                    this._eventManager?.emit('wechat_login_success', res);
                    resolve(res);
                },
                fail: (error) => {
                    console.error('[WechatAPI] 登录失败:', error);
                    this._eventManager?.emit('wechat_login_fail', error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * 获取用户信息
     */
    public async getUserInfo(): Promise<IWechatUserInfo> {
        return new Promise((resolve, reject) => {
            if (!this.isWechatEnvironment()) {
                reject(new Error('非微信环境'));
                return;
            }
            
            // 如果已有缓存，直接返回
            if (this._userInfo) {
                resolve(this._userInfo);
                return;
            }
            
            console.log('[WechatAPI] 获取用户信息');
            
            wx.getUserInfo({
                success: async (res) => {
                    console.log('[WechatAPI] 获取用户信息成功:', res);
                    this._userInfo = res.userInfo;
                    
                    // 缓存用户信息
                    await this.saveUserInfoCache(this._userInfo);
                    
                    this._eventManager?.emit('wechat_userinfo_success', res.userInfo);
                    resolve(res.userInfo);
                },
                fail: (error) => {
                    console.error('[WechatAPI] 获取用户信息失败:', error);
                    this._eventManager?.emit('wechat_userinfo_fail', error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * 主动分享
     */
    public async shareAppMessage(options: IWechatShareOptions): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.isWechatEnvironment()) {
                reject(new Error('非微信环境'));
                return;
            }
            
            console.log('[WechatAPI] 主动分享:', options);
            
            wx.shareAppMessage({
                title: options.title,
                imageUrl: options.imageUrl,
                query: options.query,
                success: () => {
                    console.log('[WechatAPI] 分享成功');
                    this._eventManager?.emit('wechat_share_success', options);
                    options.success?.();
                    resolve();
                },
                fail: (error) => {
                    console.error('[WechatAPI] 分享失败:', error);
                    this._eventManager?.emit('wechat_share_fail', error);
                    options.fail?.(error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * 被动分享到朋友圈
     */
    public setShareTimeline(options: IWechatShareOptions): void {
        if (!this.isWechatEnvironment()) return;
        
        console.log('[WechatAPI] 设置朋友圈分享:', options);
        
        wx.onShareTimeline(() => {
            return {
                title: options.title,
                imageUrl: options.imageUrl,
                query: options.query
            };
        });
    }
    
    /**
     * 显示分享菜单
     */
    public showShareMenu(): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.showShareMenu({
            withShareTicket: true,
            menus: ['shareAppMessage', 'shareTimeline'],
            success: () => {
                console.log('[WechatAPI] 显示分享菜单成功');
            },
            fail: (error) => {
                console.error('[WechatAPI] 显示分享菜单失败:', error);
            }
        });
    }
    
    /**
     * 隐藏分享菜单
     */
    public hideShareMenu(): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.hideShareMenu({
            success: () => {
                console.log('[WechatAPI] 隐藏分享菜单成功');
            },
            fail: (error) => {
                console.error('[WechatAPI] 隐藏分享菜单失败:', error);
            }
        });
    }
    
    /**
     * 震动反馈
     */
    public vibrateShort(options?: IWechatVibrateOptions): void {
        if (!this.isWechatEnvironment()) return;
        
        const type = options?.type || 'light';
        
        wx.vibrateShort({
            type: type,
            success: () => {
                console.log(`[WechatAPI] 短震动成功 (${type})`);
            },
            fail: (error) => {
                console.error('[WechatAPI] 短震动失败:', error);
            }
        });
    }
    
    /**
     * 长震动
     */
    public vibrateLong(): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.vibrateLong({
            success: () => {
                console.log('[WechatAPI] 长震动成功');
            },
            fail: (error) => {
                console.error('[WechatAPI] 长震动失败:', error);
            }
        });
    }
    
    /**
     * 显示Toast
     */
    public showToast(title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none', duration: number = 2000): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.showToast({
            title: title,
            icon: icon,
            duration: duration,
            success: () => {
                console.log(`[WechatAPI] 显示Toast: ${title}`);
            },
            fail: (error) => {
                console.error('[WechatAPI] 显示Toast失败:', error);
            }
        });
    }
    
    /**
     * 显示Loading
     */
    public showLoading(title: string = '加载中...'): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.showLoading({
            title: title,
            mask: true,
            success: () => {
                console.log(`[WechatAPI] 显示Loading: ${title}`);
            },
            fail: (error) => {
                console.error('[WechatAPI] 显示Loading失败:', error);
            }
        });
    }
    
    /**
     * 隐藏Loading
     */
    public hideLoading(): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.hideLoading({
            success: () => {
                console.log('[WechatAPI] 隐藏Loading成功');
            },
            fail: (error) => {
                console.error('[WechatAPI] 隐藏Loading失败:', error);
            }
        });
    }
    
    /**
     * 获取系统信息
     */
    public getSystemInfo(): Promise<any> {
        return new Promise((resolve, reject) => {
            if (!this.isWechatEnvironment()) {
                reject(new Error('非微信环境'));
                return;
            }
            
            wx.getSystemInfo({
                success: (res) => {
                    console.log('[WechatAPI] 系统信息:', res);
                    resolve(res);
                },
                fail: (error) => {
                    console.error('[WechatAPI] 获取系统信息失败:', error);
                    reject(error);
                }
            });
        });
    }
    
    /**
     * 设置状态栏
     */
    public setStatusBarStyle(style: 'light' | 'dark' = 'dark'): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.setStatusBarStyle({
            style: style,
            success: () => {
                console.log(`[WechatAPI] 设置状态栏样式: ${style}`);
            },
            fail: (error) => {
                console.error('[WechatAPI] 设置状态栏样式失败:', error);
            }
        });
    }
    
    /**
     * 保存用户信息缓存
     */
    private async saveUserInfoCache(userInfo: IWechatUserInfo): Promise<void> {
        try {
            await this._storageManager.setItem('wechat_userinfo', userInfo);
            console.log('[WechatAPI] 用户信息缓存保存成功');
        } catch (error) {
            console.warn('[WechatAPI] 用户信息缓存保存失败:', error);
        }
    }
    
    /**
     * 加载用户信息缓存
     */
    private async loadUserInfoCache(): Promise<void> {
        try {
            const cachedUserInfo = await this._storageManager.getItem('wechat_userinfo');
            if (cachedUserInfo) {
                this._userInfo = cachedUserInfo as IWechatUserInfo;
                this._isLoggedIn = true;
                console.log('[WechatAPI] 用户信息缓存加载成功');
            }
        } catch (error) {
            console.warn('[WechatAPI] 用户信息缓存加载失败:', error);
        }
    }
    
    /**
     * 清除用户信息缓存
     */
    public async clearUserCache(): Promise<void> {
        try {
            await this._storageManager.removeItem('wechat_userinfo');
            this._userInfo = null;
            this._isLoggedIn = false;
            console.log('[WechatAPI] 用户信息缓存清除成功');
        } catch (error) {
            console.warn('[WechatAPI] 用户信息缓存清除失败:', error);
        }
    }
    
    // ========== 公共接口 ==========
    
    /**
     * 获取当前用户信息
     */
    public getCurrentUserInfo(): IWechatUserInfo | null {
        return this._userInfo;
    }
    
    /**
     * 检查登录状态
     */
    public isLoggedIn(): boolean {
        return this._isLoggedIn;
    }
    
    /**
     * 获取启动参数中的场景值
     */
    public getLaunchScene(): number | null {
        return this._launchOptions?.scene || null;
    }
    
    /**
     * 获取启动参数中的查询参数
     */
    public getLaunchQuery(): any | null {
        return this._launchOptions?.query || null;
    }
    
    /**
     * 获取分享票据
     */
    public getShareTicket(): string | null {
        return this._launchOptions?.shareTicket || null;
    }
    
    /**
     * 检查是否来自分享
     */
    public isFromShare(): boolean {
        return !!this._launchOptions?.shareTicket;
    }
    
    /**
     * 退出小程序
     */
    public exitMiniProgram(): void {
        if (!this.isWechatEnvironment()) return;
        
        wx.exitMiniProgram({
            success: () => {
                console.log('[WechatAPI] 退出小程序成功');
            },
            fail: (error) => {
                console.error('[WechatAPI] 退出小程序失败:', error);
            }
        });
    }
}