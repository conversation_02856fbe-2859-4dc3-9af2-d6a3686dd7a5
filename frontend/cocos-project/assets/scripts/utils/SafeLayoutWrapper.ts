import { _decorator, Component, Node, Layout, UITransform } from 'cc';
import { UISafetyHelper } from './UISafetyHelper';

const { ccclass, property } = _decorator;

/**
 * 安全Layout包装器
 * 防止Layout组件因子节点问题导致的错误
 */
@ccclass('SafeLayoutWrapper')
export class SafeLayoutWrapper extends Component {
    
    @property(Layout)
    targetLayout: Layout = null;
    
    @property({
        tooltip: '是否在启动时自动检查和修复Layout'
    })
    autoFixOnStart: boolean = true;
    
    @property({
        tooltip: '检查间隔（秒）'
    })
    checkInterval: number = 1.0;
    
    private _isChecking: boolean = false;
    private _checkTimer: number = 0;
    
    protected onLoad(): void {
        if (!this.targetLayout) {
            this.targetLayout = this.getComponent(Layout);
        }
        
        if (!this.targetLayout) {
            console.warn('[SafeLayoutWrapper] 未找到Layout组件');
            return;
        }
        
        // 禁用原始Layout，由我们来控制
        this.targetLayout.enabled = false;
    }
    
    protected start(): void {
        if (this.autoFixOnStart) {
            this.scheduleOnce(() => {
                this.checkAndFixLayout();
            }, 0.1);
        }
        
        // 定期检查
        this.schedule(this.periodicCheck, this.checkInterval);
    }
    
    protected onDestroy(): void {
        this.unschedule(this.periodicCheck);
    }
    
    /**
     * 检查并修复Layout
     */
    public checkAndFixLayout(): boolean {
        if (!this.targetLayout || this._isChecking) {
            return false;
        }
        
        this._isChecking = true;
        
        try {
            // 使用UISafetyHelper检查Layout
            const isValid = UISafetyHelper.safeCheckLayout(this.targetLayout);
            
            if (isValid) {
                // 安全地启用Layout
                UISafetyHelper.safeEnableLayout(this.targetLayout);
                
                // 延迟更新Layout，确保所有子节点都已准备好
                this.scheduleOnce(() => {
                    UISafetyHelper.safeUpdateLayout(this.targetLayout);
                }, 0.05);
                
                return true;
            } else {
                console.warn('[SafeLayoutWrapper] Layout检查失败，跳过启用');
                return false;
            }
        } catch (error) {
            console.error('[SafeLayoutWrapper] 检查Layout时发生错误:', error);
            return false;
        } finally {
            this._isChecking = false;
        }
    }
    
    /**
     * 定期检查
     */
    private periodicCheck(): void {
        if (!this.targetLayout) {
            return;
        }
        
        // 如果Layout被意外禁用，尝试重新启用
        if (!this.targetLayout.enabled) {
            this.checkAndFixLayout();
        }
    }
    
    /**
     * 强制更新Layout
     */
    public forceUpdateLayout(): void {
        if (this.checkAndFixLayout()) {
            UISafetyHelper.safeUpdateLayout(this.targetLayout);
        }
    }
    
    /**
     * 安全地添加子节点
     */
    public safeAddChild(child: Node): boolean {
        if (!child || !this.targetLayout) {
            return false;
        }
        
        try {
            // 确保子节点有UITransform组件
            let uiTransform = child.getComponent(UITransform);
            if (!uiTransform) {
                uiTransform = child.addComponent(UITransform);
            }
            
            // 添加到父节点
            this.node.addChild(child);
            
            // 延迟更新Layout
            this.scheduleOnce(() => {
                this.forceUpdateLayout();
            }, 0.1);
            
            return true;
        } catch (error) {
            console.error('[SafeLayoutWrapper] 添加子节点失败:', error);
            return false;
        }
    }
    
    /**
     * 安全地移除子节点
     */
    public safeRemoveChild(child: Node): boolean {
        if (!child || !this.targetLayout) {
            return false;
        }
        
        try {
            child.removeFromParent();
            
            // 延迟更新Layout
            this.scheduleOnce(() => {
                this.forceUpdateLayout();
            }, 0.1);
            
            return true;
        } catch (error) {
            console.error('[SafeLayoutWrapper] 移除子节点失败:', error);
            return false;
        }
    }
    
    /**
     * 获取Layout状态
     */
    public getLayoutStatus(): { enabled: boolean, valid: boolean, childCount: number } {
        if (!this.targetLayout) {
            return { enabled: false, valid: false, childCount: 0 };
        }
        
        const valid = UISafetyHelper.safeCheckLayout(this.targetLayout);
        
        return {
            enabled: this.targetLayout.enabled,
            valid: valid,
            childCount: this.node.children.length
        };
    }
}
