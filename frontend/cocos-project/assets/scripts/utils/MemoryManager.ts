import { _decorator, Component } from 'cc';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 内存管理器
 * 负责统一管理组件的内存清理，防止内存泄漏
 */
@ccclass('MemoryManager')
export class MemoryManager {
    private static _instance: MemoryManager = null;
    
    // 注册的组件清理函数
    private _cleanupCallbacks: Map<string, Function[]> = new Map();
    
    // 定时器管理
    private _timers: Map<string, number> = new Map();
    
    // 事件监听器管理
    private _eventListeners: Map<string, Array<{
        eventType: string;
        callback: Function;
        target?: any;
    }>> = new Map();
    
    public static getInstance(): MemoryManager {
        if (!this._instance) {
            this._instance = new MemoryManager();
        }
        return this._instance;
    }
    
    /**
     * 注册组件清理回调
     */
    public registerCleanup(componentId: string, cleanupCallback: Function): void {
        if (!this._cleanupCallbacks.has(componentId)) {
            this._cleanupCallbacks.set(componentId, []);
        }
        this._cleanupCallbacks.get(componentId)!.push(cleanupCallback);
        console.log(`[MemoryManager] 注册清理回调: ${componentId}`);
    }
    
    /**
     * 注册定时器
     */
    public registerTimer(componentId: string, timerId: number): void {
        this._timers.set(`${componentId}_${timerId}`, timerId);
        console.log(`[MemoryManager] 注册定时器: ${componentId}_${timerId}`);
    }
    
    /**
     * 注册事件监听器
     */
    public registerEventListener(
        componentId: string, 
        eventType: string, 
        callback: Function, 
        target?: any
    ): void {
        if (!this._eventListeners.has(componentId)) {
            this._eventListeners.set(componentId, []);
        }
        
        this._eventListeners.get(componentId)!.push({
            eventType,
            callback,
            target
        });
        
        console.log(`[MemoryManager] 注册事件监听器: ${componentId} -> ${eventType}`);
    }
    
    /**
     * 清理指定组件的所有资源
     */
    public cleanupComponent(componentId: string): void {
        console.log(`[MemoryManager] 开始清理组件: ${componentId}`);
        
        // 执行清理回调
        const cleanupCallbacks = this._cleanupCallbacks.get(componentId);
        if (cleanupCallbacks) {
            cleanupCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.error(`[MemoryManager] 清理回调执行失败: ${componentId}`, error);
                }
            });
            this._cleanupCallbacks.delete(componentId);
        }
        
        // 清理定时器
        this.clearComponentTimers(componentId);
        
        // 清理事件监听器
        this.clearComponentEventListeners(componentId);
        
        console.log(`[MemoryManager] 组件清理完成: ${componentId}`);
    }
    
    /**
     * 清理组件的所有定时器
     */
    private clearComponentTimers(componentId: string): void {
        const timersToRemove: string[] = [];
        
        this._timers.forEach((timerId, key) => {
            if (key.startsWith(componentId)) {
                try {
                    clearTimeout(timerId);
                    clearInterval(timerId);
                    timersToRemove.push(key);
                    console.log(`[MemoryManager] 清理定时器: ${key}`);
                } catch (error) {
                    console.warn(`[MemoryManager] 清理定时器失败: ${key}`, error);
                }
            }
        });
        
        timersToRemove.forEach(key => this._timers.delete(key));
    }
    
    /**
     * 清理组件的所有事件监听器
     */
    private clearComponentEventListeners(componentId: string): void {
        const listeners = this._eventListeners.get(componentId);
        if (listeners) {
            const eventManager = EventManager.instance;
            
            listeners.forEach(listener => {
                try {
                    if (eventManager) {
                        eventManager.off(listener.eventType, listener.callback, listener.target);
                    }
                    console.log(`[MemoryManager] 清理事件监听器: ${componentId} -> ${listener.eventType}`);
                } catch (error) {
                    console.warn(`[MemoryManager] 清理事件监听器失败: ${componentId}`, error);
                }
            });
            
            this._eventListeners.delete(componentId);
        }
    }
    
    /**
     * 全局内存清理
     */
    public performGlobalCleanup(): void {
        console.log('[MemoryManager] 执行全局内存清理');
        
        // 清理所有注册的组件
        const componentIds = Array.from(this._cleanupCallbacks.keys());
        componentIds.forEach(componentId => {
            this.cleanupComponent(componentId);
        });
        
        // 清理剩余的定时器
        this._timers.forEach((timerId, key) => {
            try {
                clearTimeout(timerId);
                clearInterval(timerId);
                console.log(`[MemoryManager] 清理剩余定时器: ${key}`);
            } catch (error) {
                console.warn(`[MemoryManager] 清理定时器失败: ${key}`, error);
            }
        });
        this._timers.clear();
        
        // 清理剩余的事件监听器
        this._eventListeners.clear();
        
        console.log('[MemoryManager] 全局内存清理完成');
    }
    
    /**
     * 获取内存使用统计
     */
    public getMemoryStats(): {
        registeredComponents: number;
        activeTimers: number;
        activeEventListeners: number;
    } {
        let totalEventListeners = 0;
        this._eventListeners.forEach(listeners => {
            totalEventListeners += listeners.length;
        });
        
        return {
            registeredComponents: this._cleanupCallbacks.size,
            activeTimers: this._timers.size,
            activeEventListeners: totalEventListeners
        };
    }
}
