import { _decorator, Node, Vec3, Color, Tween, tween } from 'cc';
import { EventManager } from '../managers/EventManager';
import { UI_CONFIG } from '../constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 动画类型枚举
 */
export enum AnimationType {
    SCALE = 'scale',
    FADE = 'fade',
    SLIDE = 'slide',
    BOUNCE = 'bounce',
    SHAKE = 'shake',
    ROTATE = 'rotate',
    PULSE = 'pulse'
}

/**
 * 动画配置接口
 */
export interface AnimationConfig {
    type: AnimationType;
    duration: number;
    delay?: number;
    easing?: string;
    loop?: boolean;
    yoyo?: boolean;
    onComplete?: () => void;
    onStart?: () => void;
    // 动画特定参数
    from?: any;
    to?: any;
    by?: any;
}

/**
 * 动画实例接口
 */
interface AnimationInstance {
    id: string;
    node: Node;
    tween: Tween<Node>;
    config: AnimationConfig;
    isActive: boolean;
    startTime: number;
    poolKey: string;
}

/**
 * UI动画对象池管理器
 * 复用动画实例，减少GC压力，提升性能
 */
@ccclass('UIAnimationPool')
export class UIAnimationPool {
    private static _instance: UIAnimationPool = null;
    
    private _eventManager: EventManager;
    
    // 动画实例池
    private _animationPools: Map<string, AnimationInstance[]> = new Map();
    private _activeAnimations: Map<string, AnimationInstance> = new Map();
    private _animationCounter: number = 0;
    
    // 池配置
    private _poolConfig = {
        maxPoolSize: 20,        // 每种动画类型最大池大小
        maxActiveAnimations: 50, // 最大同时活跃动画数
        cleanupInterval: 30000,  // 清理间隔（30秒）
        maxIdleTime: 60000      // 最大空闲时间（60秒）
    };
    
    // 性能统计
    private _stats = {
        totalCreated: 0,
        totalReused: 0,
        totalDestroyed: 0,
        currentActive: 0,
        currentPooled: 0
    };
    
    // 清理定时器
    private _cleanupTimer: number = 0;
    
    public static getInstance(): UIAnimationPool {
        if (!this._instance) {
            this._instance = new UIAnimationPool();
        }
        return this._instance;
    }
    
    private constructor() {
        this._eventManager = EventManager.instance;
        this.startCleanupTimer();
        this.setupEventListeners();
        
        console.log('[UIAnimationPool] 动画对象池初始化完成');
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 监听内存清理事件
        this._eventManager?.on('memory_cleanup_requested', this.performCleanup, this);
        
        // 监听场景切换事件
        this._eventManager?.on('scene_changing', this.stopAllAnimations, this);
    }
    
    /**
     * 启动清理定时器
     */
    private startCleanupTimer(): void {
        this._cleanupTimer = setInterval(() => {
            this.performPeriodicCleanup();
        }, this._poolConfig.cleanupInterval);
    }
    
    /**
     * 播放动画
     */
    public playAnimation(node: Node, config: AnimationConfig): string {
        if (!node || !config) {
            console.warn('[UIAnimationPool] 无效的节点或配置');
            return '';
        }
        
        // 检查活跃动画数量限制
        if (this._activeAnimations.size >= this._poolConfig.maxActiveAnimations) {
            console.warn('[UIAnimationPool] 活跃动画数量已达上限');
            return '';
        }
        
        const animationId = this.generateAnimationId();
        const poolKey = this.getPoolKey(config.type);
        
        // 从池中获取或创建动画实例
        const instance = this.getAnimationInstance(poolKey, node, config, animationId);
        
        // 启动动画
        this.startAnimation(instance);
        
        // 添加到活跃动画列表
        this._activeAnimations.set(animationId, instance);
        this._stats.currentActive = this._activeAnimations.size;
        
        console.log(`[UIAnimationPool] 播放动画: ${config.type}, ID: ${animationId}`);
        
        return animationId;
    }
    
    /**
     * 停止动画
     */
    public stopAnimation(animationId: string): void {
        const instance = this._activeAnimations.get(animationId);
        if (!instance) {
            return;
        }
        
        // 停止Tween
        if (instance.tween) {
            instance.tween.stop();
        }
        
        // 回收到池中
        this.recycleAnimation(instance);
        
        console.log(`[UIAnimationPool] 停止动画: ${animationId}`);
    }
    
    /**
     * 停止节点上的所有动画
     */
    public stopNodeAnimations(node: Node): void {
        const toStop: string[] = [];
        
        this._activeAnimations.forEach((instance, id) => {
            if (instance.node === node) {
                toStop.push(id);
            }
        });
        
        toStop.forEach(id => this.stopAnimation(id));
    }
    
    /**
     * 停止所有动画
     */
    public stopAllAnimations(): void {
        const allIds = Array.from(this._activeAnimations.keys());
        allIds.forEach(id => this.stopAnimation(id));
        
        console.log('[UIAnimationPool] 已停止所有动画');
    }
    
    /**
     * 从池中获取动画实例
     */
    private getAnimationInstance(poolKey: string, node: Node, config: AnimationConfig, id: string): AnimationInstance {
        let pool = this._animationPools.get(poolKey);
        
        if (!pool) {
            pool = [];
            this._animationPools.set(poolKey, pool);
        }
        
        // 尝试从池中获取
        let instance = pool.pop();
        
        if (instance) {
            // 重用实例
            instance.id = id;
            instance.node = node;
            instance.config = config;
            instance.isActive = false;
            instance.startTime = 0;
            this._stats.totalReused++;
        } else {
            // 创建新实例
            instance = {
                id,
                node,
                tween: null,
                config,
                isActive: false,
                startTime: 0,
                poolKey
            };
            this._stats.totalCreated++;
        }
        
        return instance;
    }
    
    /**
     * 启动动画
     */
    private startAnimation(instance: AnimationInstance): void {
        const { node, config } = instance;
        
        // 执行开始回调
        if (config.onStart) {
            config.onStart();
        }
        
        // 创建Tween动画
        instance.tween = this.createTweenAnimation(node, config);
        
        // 设置完成回调
        instance.tween.call(() => {
            if (config.onComplete) {
                config.onComplete();
            }
            this.recycleAnimation(instance);
        });
        
        // 启动动画
        instance.tween.start();
        instance.isActive = true;
        instance.startTime = Date.now();
    }
    
    /**
     * 创建Tween动画
     */
    private createTweenAnimation(node: Node, config: AnimationConfig): Tween<Node> {
        let tweenInstance = tween(node);
        
        // 添加延迟
        if (config.delay && config.delay > 0) {
            tweenInstance = tweenInstance.delay(config.delay / 1000);
        }
        
        // 根据动画类型创建不同的动画
        switch (config.type) {
            case AnimationType.SCALE:
                tweenInstance = this.createScaleAnimation(tweenInstance, config);
                break;
            case AnimationType.FADE:
                tweenInstance = this.createFadeAnimation(tweenInstance, config);
                break;
            case AnimationType.SLIDE:
                tweenInstance = this.createSlideAnimation(tweenInstance, config);
                break;
            case AnimationType.BOUNCE:
                tweenInstance = this.createBounceAnimation(tweenInstance, config);
                break;
            case AnimationType.SHAKE:
                tweenInstance = this.createShakeAnimation(tweenInstance, config);
                break;
            case AnimationType.ROTATE:
                tweenInstance = this.createRotateAnimation(tweenInstance, config);
                break;
            case AnimationType.PULSE:
                tweenInstance = this.createPulseAnimation(tweenInstance, config);
                break;
        }
        
        // 添加循环
        if (config.loop) {
            if (config.yoyo) {
                tweenInstance = tweenInstance.repeatForever();
            } else {
                tweenInstance = tweenInstance.repeatForever();
            }
        }
        
        return tweenInstance;
    }
    
    /**
     * 创建缩放动画
     */
    private createScaleAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000;
        const easing = config.easing || 'linear';
        
        if (config.to) {
            return tweenInstance.to(duration, { scale: config.to }, { easing });
        } else if (config.by) {
            return tweenInstance.by(duration, { scale: config.by }, { easing });
        } else {
            // 默认缩放动画
            return tweenInstance.to(duration, { scale: new Vec3(1.1, 1.1, 1) }, { easing })
                                 .to(duration, { scale: new Vec3(1, 1, 1) }, { easing });
        }
    }
    
    /**
     * 创建淡入淡出动画
     */
    private createFadeAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000;
        const easing = config.easing || 'linear';
        
        if (config.to !== undefined) {
            return tweenInstance.to(duration, { opacity: config.to }, { easing });
        } else {
            // 默认淡入淡出
            return tweenInstance.to(duration / 2, { opacity: 0 }, { easing })
                                 .to(duration / 2, { opacity: 255 }, { easing });
        }
    }
    
    /**
     * 创建滑动动画
     */
    private createSlideAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000;
        const easing = config.easing || 'linear';
        
        if (config.to) {
            return tweenInstance.to(duration, { position: config.to }, { easing });
        } else if (config.by) {
            return tweenInstance.by(duration, { position: config.by }, { easing });
        } else {
            return tweenInstance;
        }
    }
    
    /**
     * 创建弹跳动画
     */
    private createBounceAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000;
        
        return tweenInstance.to(duration, { scale: new Vec3(1.2, 1.2, 1) }, { easing: 'bounceOut' })
                           .to(duration / 2, { scale: new Vec3(1, 1, 1) }, { easing: 'bounceOut' });
    }
    
    /**
     * 创建震动动画
     */
    private createShakeAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000 / 8; // 分成8段
        const intensity = config.by || new Vec3(10, 0, 0);
        
        return tweenInstance.repeat(4,
            tween().by(duration, { position: intensity })
                   .by(duration, { position: intensity.clone().multiplyScalar(-2) })
                   .by(duration, { position: intensity })
        );
    }
    
    /**
     * 创建旋转动画
     */
    private createRotateAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000;
        const easing = config.easing || 'linear';
        
        if (config.by !== undefined) {
            return tweenInstance.by(duration, { angle: config.by }, { easing });
        } else {
            return tweenInstance.by(duration, { angle: 360 }, { easing });
        }
    }
    
    /**
     * 创建脉冲动画
     */
    private createPulseAnimation(tweenInstance: Tween<Node>, config: AnimationConfig): Tween<Node> {
        const duration = config.duration / 1000 / 2;
        
        return tweenInstance.to(duration, { scale: new Vec3(1.05, 1.05, 1) }, { easing: 'sineInOut' })
                           .to(duration, { scale: new Vec3(1, 1, 1) }, { easing: 'sineInOut' });
    }
    
    /**
     * 回收动画实例
     */
    private recycleAnimation(instance: AnimationInstance): void {
        // 从活跃列表中移除
        this._activeAnimations.delete(instance.id);
        this._stats.currentActive = this._activeAnimations.size;
        
        // 重置实例状态
        instance.isActive = false;
        instance.tween = null;
        instance.startTime = 0;
        
        // 回收到池中
        const pool = this._animationPools.get(instance.poolKey);
        if (pool && pool.length < this._poolConfig.maxPoolSize) {
            pool.push(instance);
            this._stats.currentPooled++;
        } else {
            // 池已满，销毁实例
            this._stats.totalDestroyed++;
        }
    }
    
    /**
     * 执行定期清理
     */
    private performPeriodicCleanup(): void {
        const now = Date.now();
        let cleanedCount = 0;
        
        // 清理长时间空闲的池实例
        this._animationPools.forEach((pool, poolKey) => {
            const toRemove: number[] = [];
            
            pool.forEach((instance, index) => {
                if (!instance.isActive && (now - instance.startTime) > this._poolConfig.maxIdleTime) {
                    toRemove.push(index);
                }
            });
            
            // 从后往前删除，避免索引问题
            toRemove.reverse().forEach(index => {
                pool.splice(index, 1);
                cleanedCount++;
                this._stats.totalDestroyed++;
            });
        });
        
        this._stats.currentPooled = this.getTotalPooledCount();
        
        if (cleanedCount > 0) {
            console.log(`[UIAnimationPool] 定期清理完成，清理了 ${cleanedCount} 个空闲实例`);
        }
    }
    
    /**
     * 执行内存清理
     */
    private performCleanup(): void {
        // 停止所有动画
        this.stopAllAnimations();
        
        // 清空所有池
        this._animationPools.clear();
        
        // 重置统计
        this._stats.currentActive = 0;
        this._stats.currentPooled = 0;
        
        console.log('[UIAnimationPool] 内存清理完成');
    }
    
    /**
     * 生成动画ID
     */
    private generateAnimationId(): string {
        return `anim_${++this._animationCounter}_${Date.now()}`;
    }
    
    /**
     * 获取池键值
     */
    private getPoolKey(type: AnimationType): string {
        return `pool_${type}`;
    }
    
    /**
     * 获取总池实例数
     */
    private getTotalPooledCount(): number {
        let total = 0;
        this._animationPools.forEach(pool => {
            total += pool.length;
        });
        return total;
    }
    
    /**
     * 获取统计信息
     */
    public getStats(): any {
        return {
            ...this._stats,
            poolSizes: Object.fromEntries(
                Array.from(this._animationPools.entries()).map(([key, pool]) => [key, pool.length])
            ),
            config: { ...this._poolConfig }
        };
    }
    
    /**
     * 销毁动画池
     */
    public destroy(): void {
        // 清理定时器
        if (this._cleanupTimer) {
            clearInterval(this._cleanupTimer);
            this._cleanupTimer = 0;
        }
        
        // 停止所有动画
        this.stopAllAnimations();
        
        // 清空池
        this._animationPools.clear();
        
        // 移除事件监听器
        this._eventManager?.off('memory_cleanup_requested', this.performCleanup, this);
        this._eventManager?.off('scene_changing', this.stopAllAnimations, this);
        
        console.log('[UIAnimationPool] 动画池已销毁');
    }
}
