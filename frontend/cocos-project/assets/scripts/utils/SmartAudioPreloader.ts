import { _decorator } from 'cc';
import { IQuestionData } from '../data/GameData';
import { EventManager } from '../managers/EventManager';
import { StorageManager } from './StorageManager';
import { AUDIO_CONFIG, STORAGE_KEYS } from '../constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 用户行为模式接口
 */
interface UserBehaviorPattern {
    userId: string;
    averageAnswerTime: number;
    preferredDifficulty: string[];
    commonMistakes: string[];
    playbackFrequency: number;
    sessionDuration: number;
    lastPlayTime: number;
    totalSessions: number;
}

/**
 * 音频预测权重接口
 */
interface AudioPredictionWeight {
    questionId: string;
    audioUrl: string;
    weight: number;
    reasons: string[];
    priority: number;
}

/**
 * 预加载策略接口
 */
interface PreloadStrategy {
    immediate: string[];    // 立即加载
    background: string[];   // 后台加载
    predicted: string[];    // 预测加载
}

/**
 * 智能音频预加载器
 * 基于用户行为模式和游戏进度预测需要预加载的音频
 */
@ccclass('SmartAudioPreloader')
export class SmartAudioPreloader {
    private static _instance: SmartAudioPreloader = null;
    
    private _eventManager: EventManager;
    private _storageManager: StorageManager;
    
    // 用户行为数据
    private _userBehavior: UserBehaviorPattern;
    private _sessionStartTime: number = 0;
    private _currentQuestionIndex: number = 0;
    private _answerTimes: number[] = [];
    private _playbackCounts: Map<string, number> = new Map();
    
    // 预加载状态
    private _preloadQueue: Map<string, AudioPredictionWeight> = new Map();
    private _loadingPromises: Map<string, Promise<void>> = new Map();
    private _preloadedAudios: Set<string> = new Set();
    
    // 配置参数
    private _maxConcurrentLoads: number = AUDIO_CONFIG.PRELOAD.CONCURRENT_LIMIT;
    private _immediateLoadCount: number = 3;  // 立即加载前3题
    private _backgroundLoadCount: number = 5; // 后台加载5题
    private _predictionLoadCount: number = 3; // 预测加载3题
    
    public static getInstance(): SmartAudioPreloader {
        if (!this._instance) {
            this._instance = new SmartAudioPreloader();
        }
        return this._instance;
    }
    
    private constructor() {
        this._eventManager = EventManager.instance;
        this._storageManager = new StorageManager();
        this.initializeUserBehavior();
        this.setupEventListeners();
    }
    
    /**
     * 初始化用户行为模式
     */
    private async initializeUserBehavior(): Promise<void> {
        try {
            const savedBehavior = await this._storageManager.getItem('user_behavior_pattern');
            
            this._userBehavior = savedBehavior || {
                userId: 'anonymous',
                averageAnswerTime: 15, // 默认15秒
                preferredDifficulty: ['easy'],
                commonMistakes: [],
                playbackFrequency: 1.5, // 平均播放1.5次
                sessionDuration: 300, // 默认5分钟
                lastPlayTime: 0,
                totalSessions: 0
            };
            
            console.log('[SmartAudioPreloader] 用户行为模式初始化完成:', this._userBehavior);
        } catch (error) {
            console.error('[SmartAudioPreloader] 初始化用户行为失败:', error);
        }
    }
    
    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 监听游戏开始
        this._eventManager?.on('game_started', this.onGameStarted, this);
        
        // 监听答题事件
        this._eventManager?.on('question_answered', this.onQuestionAnswered, this);
        
        // 监听音频播放事件
        this._eventManager?.on('audio_play_started', this.onAudioPlayed, this);
        
        // 监听游戏结束
        this._eventManager?.on('game_finished', this.onGameFinished, this);
        
        // 监听网络状态变化
        this._eventManager?.on('network_status_changed', this.onNetworkStatusChanged, this);
    }
    
    /**
     * 开始智能预加载
     */
    public async startSmartPreload(questions: IQuestionData[], currentIndex: number = 0): Promise<void> {
        try {
            console.log(`[SmartAudioPreloader] 开始智能预加载，当前题目索引: ${currentIndex}`);
            
            this._currentQuestionIndex = currentIndex;
            
            // 生成预加载策略
            const strategy = this.generatePreloadStrategy(questions, currentIndex);
            
            // 执行预加载
            await this.executePreloadStrategy(strategy);
            
            console.log('[SmartAudioPreloader] 智能预加载完成');
            
        } catch (error) {
            console.error('[SmartAudioPreloader] 智能预加载失败:', error);
        }
    }
    
    /**
     * 生成预加载策略
     */
    private generatePreloadStrategy(questions: IQuestionData[], currentIndex: number): PreloadStrategy {
        const strategy: PreloadStrategy = {
            immediate: [],
            background: [],
            predicted: []
        };
        
        // 1. 立即加载：当前题目和接下来的几题
        const immediateEnd = Math.min(currentIndex + this._immediateLoadCount, questions.length);
        for (let i = currentIndex; i < immediateEnd; i++) {
            strategy.immediate.push(questions[i].audioUrl);
        }
        
        // 2. 后台加载：基于用户答题速度预测的题目
        const backgroundStart = immediateEnd;
        const backgroundEnd = Math.min(backgroundStart + this._backgroundLoadCount, questions.length);
        for (let i = backgroundStart; i < backgroundEnd; i++) {
            strategy.background.push(questions[i].audioUrl);
        }
        
        // 3. 预测加载：基于用户行为模式预测可能重复播放的题目
        const predictedAudios = this.predictLikelyReplayAudios(questions, currentIndex);
        strategy.predicted = predictedAudios.slice(0, this._predictionLoadCount);
        
        console.log('[SmartAudioPreloader] 预加载策略生成:', {
            immediate: strategy.immediate.length,
            background: strategy.background.length,
            predicted: strategy.predicted.length
        });
        
        return strategy;
    }
    
    /**
     * 预测可能重复播放的音频
     */
    private predictLikelyReplayAudios(questions: IQuestionData[], currentIndex: number): string[] {
        const predictions: AudioPredictionWeight[] = [];
        
        questions.forEach((question, index) => {
            if (index <= currentIndex) return; // 跳过已经播放的题目
            
            let weight = 0;
            const reasons: string[] = [];
            
            // 基于难度预测（困难题目更可能重复播放）
            if (question.difficulty === 'hard') {
                weight += 0.4;
                reasons.push('困难题目');
            } else if (question.difficulty === 'medium') {
                weight += 0.2;
                reasons.push('中等难度');
            }
            
            // 基于用户历史行为预测
            if (this._userBehavior.playbackFrequency > 1.5) {
                weight += 0.3;
                reasons.push('用户习惯重复播放');
            }
            
            // 基于题目类型预测（某些方言可能更难理解）
            if (question.region && this.isChallengingRegion(question.region)) {
                weight += 0.2;
                reasons.push('挑战性方言');
            }
            
            // 基于答题时间预测（用户答题较慢时更可能重复播放）
            if (this._userBehavior.averageAnswerTime > 20) {
                weight += 0.1;
                reasons.push('答题时间较长');
            }
            
            predictions.push({
                questionId: question.id,
                audioUrl: question.audioUrl,
                weight,
                reasons,
                priority: Math.floor(weight * 10)
            });
        });
        
        // 按权重排序
        predictions.sort((a, b) => b.weight - a.weight);
        
        return predictions.map(p => p.audioUrl);
    }
    
    /**
     * 判断是否为挑战性方言
     */
    private isChallengingRegion(region: string): boolean {
        // 基于历史数据或配置判断哪些方言更具挑战性
        const challengingRegions = ['四川话', '广东话', '东北话', '上海话'];
        return challengingRegions.includes(region);
    }
    
    /**
     * 执行预加载策略
     */
    private async executePreloadStrategy(strategy: PreloadStrategy): Promise<void> {
        const allAudios = [
            ...strategy.immediate,
            ...strategy.background,
            ...strategy.predicted
        ];
        
        // 去重
        const uniqueAudios = Array.from(new Set(allAudios));
        
        // 过滤已预加载的音频
        const toLoad = uniqueAudios.filter(url => !this._preloadedAudios.has(url));
        
        if (toLoad.length === 0) {
            console.log('[SmartAudioPreloader] 所有音频已预加载');
            return;
        }
        
        console.log(`[SmartAudioPreloader] 需要预加载 ${toLoad.length} 个音频`);
        
        // 分批并发加载
        const batches = this.createLoadingBatches(toLoad, this._maxConcurrentLoads);
        
        for (const batch of batches) {
            const promises = batch.map(url => this.preloadSingleAudio(url));
            await Promise.allSettled(promises);
        }
    }
    
    /**
     * 创建加载批次
     */
    private createLoadingBatches(urls: string[], batchSize: number): string[][] {
        const batches: string[][] = [];
        
        for (let i = 0; i < urls.length; i += batchSize) {
            batches.push(urls.slice(i, i + batchSize));
        }
        
        return batches;
    }
    
    /**
     * 预加载单个音频
     */
    private async preloadSingleAudio(url: string): Promise<void> {
        if (this._loadingPromises.has(url)) {
            return this._loadingPromises.get(url);
        }
        
        const loadPromise = this.loadAudioWithRetry(url);
        this._loadingPromises.set(url, loadPromise);
        
        try {
            await loadPromise;
            this._preloadedAudios.add(url);
            console.log(`[SmartAudioPreloader] 音频预加载成功: ${url}`);
        } catch (error) {
            console.warn(`[SmartAudioPreloader] 音频预加载失败: ${url}`, error);
        } finally {
            this._loadingPromises.delete(url);
        }
    }
    
    /**
     * 带重试的音频加载
     */
    private async loadAudioWithRetry(url: string, maxRetries: number = 2): Promise<void> {
        let lastError: Error;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // 触发音频管理器加载
                this._eventManager?.emit('preload_audio_request', { url });
                
                // 等待加载完成（这里需要与AudioManager配合）
                await new Promise<void>((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('预加载超时'));
                    }, AUDIO_CONFIG.PLAYBACK.TIMEOUT);
                    
                    const onLoaded = (data: any) => {
                        if (data.url === url) {
                            clearTimeout(timeout);
                            this._eventManager?.off('audio_preload_success', onLoaded);
                            this._eventManager?.off('audio_preload_error', onError);
                            resolve();
                        }
                    };
                    
                    const onError = (data: any) => {
                        if (data.url === url) {
                            clearTimeout(timeout);
                            this._eventManager?.off('audio_preload_success', onLoaded);
                            this._eventManager?.off('audio_preload_error', onError);
                            reject(new Error(data.error));
                        }
                    };
                    
                    this._eventManager?.on('audio_preload_success', onLoaded);
                    this._eventManager?.on('audio_preload_error', onError);
                });
                
                return; // 成功加载
                
            } catch (error) {
                lastError = error;
                
                if (attempt < maxRetries) {
                    const delay = Math.pow(2, attempt) * 1000; // 指数退避
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        throw lastError;
    }
    
    // 事件处理方法
    private onGameStarted(data: any): void {
        this._sessionStartTime = Date.now();
        this._currentQuestionIndex = 0;
        this._answerTimes = [];
        this._playbackCounts.clear();
        
        console.log('[SmartAudioPreloader] 游戏开始，重置统计数据');
    }
    
    private onQuestionAnswered(data: any): void {
        const answerTime = Date.now() - (data.questionStartTime || this._sessionStartTime);
        this._answerTimes.push(answerTime);
        this._currentQuestionIndex = data.questionIndex + 1;
        
        // 更新用户行为模式
        this.updateUserBehavior();
        
        console.log(`[SmartAudioPreloader] 题目 ${data.questionIndex} 答题完成，用时: ${answerTime}ms`);
    }
    
    private onAudioPlayed(data: any): void {
        const count = this._playbackCounts.get(data.questionId) || 0;
        this._playbackCounts.set(data.questionId, count + 1);
        
        console.log(`[SmartAudioPreloader] 音频播放: ${data.questionId}, 播放次数: ${count + 1}`);
    }
    
    private onGameFinished(data: any): void {
        this.updateUserBehaviorOnGameEnd();
        this.saveUserBehavior();
        
        console.log('[SmartAudioPreloader] 游戏结束，保存用户行为数据');
    }
    
    private onNetworkStatusChanged(status: any): void {
        if (!status.isOnline) {
            console.log('[SmartAudioPreloader] 网络断开，暂停预加载');
            // 可以在这里暂停预加载或调整策略
        } else {
            console.log('[SmartAudioPreloader] 网络恢复，继续预加载');
        }
    }
    
    /**
     * 更新用户行为模式
     */
    private updateUserBehavior(): void {
        if (this._answerTimes.length > 0) {
            this._userBehavior.averageAnswerTime = 
                this._answerTimes.reduce((sum, time) => sum + time, 0) / this._answerTimes.length / 1000;
        }
        
        // 计算平均播放频率
        const totalPlaybacks = Array.from(this._playbackCounts.values()).reduce((sum, count) => sum + count, 0);
        if (this._playbackCounts.size > 0) {
            this._userBehavior.playbackFrequency = totalPlaybacks / this._playbackCounts.size;
        }
    }
    
    /**
     * 游戏结束时更新用户行为
     */
    private updateUserBehaviorOnGameEnd(): void {
        this._userBehavior.sessionDuration = Date.now() - this._sessionStartTime;
        this._userBehavior.lastPlayTime = Date.now();
        this._userBehavior.totalSessions += 1;
    }
    
    /**
     * 保存用户行为数据
     */
    private async saveUserBehavior(): Promise<void> {
        try {
            await this._storageManager.setItem('user_behavior_pattern', this._userBehavior);
            console.log('[SmartAudioPreloader] 用户行为数据已保存');
        } catch (error) {
            console.error('[SmartAudioPreloader] 保存用户行为数据失败:', error);
        }
    }
    
    /**
     * 获取预加载统计信息
     */
    public getPreloadStats(): any {
        return {
            preloadedCount: this._preloadedAudios.size,
            loadingCount: this._loadingPromises.size,
            queueSize: this._preloadQueue.size,
            userBehavior: { ...this._userBehavior }
        };
    }
    
    /**
     * 清理预加载数据
     */
    public cleanup(): void {
        this._preloadQueue.clear();
        this._loadingPromises.clear();
        this._preloadedAudios.clear();
        this._playbackCounts.clear();
        this._answerTimes = [];
        
        console.log('[SmartAudioPreloader] 预加载数据已清理');
    }

    // ================== 网络适应性增强功能 ==================

    /**
     * 网络状态评估
     */
    private _networkQuality: 'excellent' | 'good' | 'poor' | 'offline' = 'good';
    private _networkHistory: { timestamp: number; latency: number; success: boolean }[] = [];
    private _maxNetworkHistorySize: number = 20;

    /**
     * 离线缓存管理
     */
    private _offlineCache: Map<string, ArrayBuffer> = new Map();
    private _offlineCacheSize: number = 0;
    private _maxOfflineCacheSize: number = 50 * 1024 * 1024; // 50MB离线缓存上限

    /**
     * 适应性预加载参数
     */
    private _adaptiveParams = {
        excellent: { concurrent: 6, batch: 8, timeout: 5000 },
        good: { concurrent: 4, batch: 5, timeout: 8000 },
        poor: { concurrent: 2, batch: 3, timeout: 15000 },
        offline: { concurrent: 0, batch: 0, timeout: 0 }
    };

    /**
     * 网络质量评估
     */
    private assessNetworkQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
        if (this._networkHistory.length < 3) {
            return 'good'; // 默认值
        }

        const recent = this._networkHistory.slice(-10);
        const avgLatency = recent.reduce((sum, record) => sum + record.latency, 0) / recent.length;
        const successRate = recent.filter(record => record.success).length / recent.length;

        if (successRate === 0) {
            return 'offline';
        } else if (avgLatency < 200 && successRate > 0.9) {
            return 'excellent';
        } else if (avgLatency < 500 && successRate > 0.8) {
            return 'good';
        } else {
            return 'poor';
        }
    }

    /**
     * 记录网络性能
     */
    private recordNetworkPerformance(latency: number, success: boolean): void {
        this._networkHistory.push({
            timestamp: Date.now(),
            latency,
            success
        });

        // 保持历史记录大小
        if (this._networkHistory.length > this._maxNetworkHistorySize) {
            this._networkHistory.shift();
        }

        // 更新网络质量评估
        const oldQuality = this._networkQuality;
        this._networkQuality = this.assessNetworkQuality();

        if (oldQuality !== this._networkQuality) {
            console.log(`[SmartAudioPreloader] 网络质量变化: ${oldQuality} -> ${this._networkQuality}`);
            this.adjustPreloadStrategy();
        }
    }

    /**
     * 调整预加载策略
     */
    private adjustPreloadStrategy(): void {
        const params = this._adaptiveParams[this._networkQuality];
        
        this._maxConcurrentLoads = params.concurrent;
        
        // 根据网络质量调整加载数量
        switch (this._networkQuality) {
            case 'excellent':
                this._immediateLoadCount = 5;
                this._backgroundLoadCount = 8;
                this._predictionLoadCount = 5;
                break;
            case 'good':
                this._immediateLoadCount = 3;
                this._backgroundLoadCount = 5;
                this._predictionLoadCount = 3;
                break;
            case 'poor':
                this._immediateLoadCount = 2;
                this._backgroundLoadCount = 3;
                this._predictionLoadCount = 2;
                break;
            case 'offline':
                // 离线模式，依赖本地缓存
                this._immediateLoadCount = 0;
                this._backgroundLoadCount = 0;
                this._predictionLoadCount = 0;
                break;
        }

        console.log(`[SmartAudioPreloader] 预加载策略已调整为${this._networkQuality}模式`);
    }

    /**
     * 增强的网络音频加载（带重试和降级）
     */
    private async loadAudioWithAdaptiveRetry(url: string): Promise<void> {
        const params = this._adaptiveParams[this._networkQuality];
        const startTime = Date.now();
        
        if (this._networkQuality === 'offline') {
            // 离线模式，检查本地缓存
            if (this._offlineCache.has(url)) {
                console.log(`[SmartAudioPreloader] 使用离线缓存: ${url}`);
                return;
            } else {
                throw new Error('离线模式且无本地缓存');
            }
        }

        let lastError: Error;
        const maxRetries = this._networkQuality === 'poor' ? 3 : 2;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // 使用适应性超时
                const timeoutPromise = new Promise<never>((_, reject) => {
                    setTimeout(() => reject(new Error('网络加载超时')), params.timeout);
                });

                const loadPromise = this.loadAudioCore(url);
                
                await Promise.race([loadPromise, timeoutPromise]);
                
                // 记录成功的网络性能
                const latency = Date.now() - startTime;
                this.recordNetworkPerformance(latency, true);
                
                // 成功加载后，存储到离线缓存
                await this.saveToOfflineCache(url);
                
                return;

            } catch (error) {
                lastError = error;
                
                // 记录失败的网络性能
                const latency = Date.now() - startTime;
                this.recordNetworkPerformance(latency, false);

                if (attempt < maxRetries) {
                    // 指数退避，但考虑网络质量
                    const baseDelay = this._networkQuality === 'poor' ? 2000 : 1000;
                    const delay = baseDelay * Math.pow(2, attempt);
                    
                    console.warn(`[SmartAudioPreloader] 加载失败，${delay}ms后重试: ${url}`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }

    /**
     * 核心音频加载逻辑
     */
    private async loadAudioCore(url: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            // 检查是否是本地资源
            if (!url.startsWith('http')) {
                // 本地资源，直接通知加载
                this._eventManager?.emit('preload_audio_request', { url });
                resolve();
                return;
            }

            // 网络资源，使用微信API或Web API
            if (typeof wx !== 'undefined' && wx.downloadFile) {
                // 微信小游戏环境
                wx.downloadFile({
                    url: url,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 通知AudioManager加载下载的文件
                            this._eventManager?.emit('preload_audio_request', { 
                                url: url, 
                                localPath: res.tempFilePath 
                            });
                            resolve();
                        } else {
                            reject(new Error(`下载失败，状态码: ${res.statusCode}`));
                        }
                    },
                    fail: (error) => {
                        reject(new Error(`网络下载失败: ${error.errMsg || error.message}`));
                    }
                });
            } else {
                // Web环境，使用fetch
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.arrayBuffer();
                    })
                    .then(buffer => {
                        // 将数据存储到内存缓存
                        this.storeAudioBuffer(url, buffer);
                        this._eventManager?.emit('preload_audio_request', { url });
                        resolve();
                    })
                    .catch(reject);
            }
        });
    }

    /**
     * 存储音频缓冲区到内存
     */
    private storeAudioBuffer(url: string, buffer: ArrayBuffer): void {
        try {
            // 检查缓存大小限制
            if (this._offlineCacheSize + buffer.byteLength > this._maxOfflineCacheSize) {
                this.cleanupOldestCacheEntries(buffer.byteLength);
            }

            this._offlineCache.set(url, buffer);
            this._offlineCacheSize += buffer.byteLength;

            console.log(`[SmartAudioPreloader] 音频缓存到内存: ${url} (${(buffer.byteLength / 1024).toFixed(1)}KB)`);
        } catch (error) {
            console.warn(`[SmartAudioPreloader] 存储音频缓冲区失败: ${url}`, error);
        }
    }

    /**
     * 清理最旧的缓存条目
     */
    private cleanupOldestCacheEntries(neededSpace: number): void {
        console.log(`[SmartAudioPreloader] 清理缓存以释放 ${(neededSpace / 1024).toFixed(1)}KB 空间`);
        
        // 简单的LRU策略：清理前30%的缓存
        const entriesToRemove = Math.ceil(this._offlineCache.size * 0.3);
        const entries = Array.from(this._offlineCache.entries());
        
        for (let i = 0; i < entriesToRemove && i < entries.length; i++) {
            const [url, buffer] = entries[i];
            this._offlineCache.delete(url);
            this._offlineCacheSize -= buffer.byteLength;
        }

        console.log(`[SmartAudioPreloader] 清理了 ${entriesToRemove} 个缓存条目`);
    }

    /**
     * 保存到离线缓存
     */
    private async saveToOfflineCache(url: string): Promise<void> {
        if (typeof wx !== 'undefined' && wx.getFileSystemManager) {
            try {
                const fs = wx.getFileSystemManager();
                const cacheDir = `${wx.env.USER_DATA_PATH}/audio_cache`;
                const fileName = this.urlToFileName(url);
                const filePath = `${cacheDir}/${fileName}`;

                // 确保缓存目录存在
                try {
                    fs.mkdirSync(cacheDir, true);
                } catch (error) {
                    // 目录可能已存在，忽略错误
                }

                // 保存音频文件到本地
                // 这里需要与下载逻辑配合，将临时文件复制到缓存目录
                console.log(`[SmartAudioPreloader] 音频已保存到离线缓存: ${filePath}`);
                
            } catch (error) {
                console.warn(`[SmartAudioPreloader] 保存离线缓存失败: ${url}`, error);
            }
        }
    }

    /**
     * URL转文件名
     */
    private urlToFileName(url: string): string {
        return url.replace(/[^a-zA-Z0-9]/g, '_') + '.mp3';
    }

    /**
     * 增强的网络状态监听
     */
    private onNetworkStatusChangedEnhanced(status: any): void {
        const wasOffline = this._networkQuality === 'offline';
        
        if (!status.isOnline) {
            this._networkQuality = 'offline';
            console.log('[SmartAudioPreloader] 网络断开，切换到离线模式');
            
            // 取消所有正在进行的下载
            this._loadingPromises.clear();
            
        } else if (wasOffline) {
            // 从离线恢复，重新评估网络质量
            this._networkQuality = 'good'; // 默认设为良好
            console.log('[SmartAudioPreloader] 网络恢复，重新开始预加载');
            
            // 重新开始预加载
            this.resumePreloadingAfterNetworkRecovery();
        }

        // 调整预加载策略
        this.adjustPreloadStrategy();
    }

    /**
     * 网络恢复后恢复预加载
     */
    private async resumePreloadingAfterNetworkRecovery(): Promise<void> {
        try {
            // 等待一小段时间让网络稳定
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 获取当前游戏状态，如果游戏正在进行，继续预加载
            if (this._currentQuestionIndex >= 0) {
                console.log('[SmartAudioPreloader] 网络恢复后继续预加载');
                // 这里可以重新触发预加载逻辑
                this._eventManager?.emit('network_recovered_preload_request', {
                    currentIndex: this._currentQuestionIndex
                });
            }
        } catch (error) {
            console.error('[SmartAudioPreloader] 网络恢复后预加载失败:', error);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public getCacheStats(): any {
        return {
            networkQuality: this._networkQuality,
            offlineCacheSize: this._offlineCacheSize,
            offlineCacheCount: this._offlineCache.size,
            networkHistorySize: this._networkHistory.length,
            preloadedCount: this._preloadedAudios.size,
            loadingCount: this._loadingPromises.size,
            maxCacheSize: this._maxOfflineCacheSize,
            cacheUtilization: (this._offlineCacheSize / this._maxOfflineCacheSize * 100).toFixed(1) + '%'
        };
    }

    /**
     * 手动触发缓存清理
     */
    public cleanupCache(): void {
        // 清理内存缓存
        this._offlineCache.clear();
        this._offlineCacheSize = 0;
        
        // 清理网络历史
        this._networkHistory = [];
        
        // 重置网络质量评估
        this._networkQuality = 'good';
        
        console.log('[SmartAudioPreloader] 缓存清理完成');
    }

    /**
     * 预热缓存（游戏启动时调用）
     */
    public async warmupCache(essentialAudios: string[]): Promise<void> {
        console.log(`[SmartAudioPreloader] 开始预热缓存 ${essentialAudios.length} 个必需音频`);
        
        try {
            // 设置为最高优先级预加载
            const originalParams = { ...this._adaptiveParams[this._networkQuality] };
            this._maxConcurrentLoads = Math.min(essentialAudios.length, 4);
            
            const promises = essentialAudios.map(url => this.preloadSingleAudio(url));
            await Promise.allSettled(promises);
            
            // 恢复原始参数
            this._maxConcurrentLoads = originalParams.concurrent;
            
            console.log('[SmartAudioPreloader] 缓存预热完成');
            
        } catch (error) {
            console.error('[SmartAudioPreloader] 缓存预热失败:', error);
        }
    }
}
