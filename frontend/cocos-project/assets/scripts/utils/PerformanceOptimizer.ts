/**
 * 性能优化工具类
 * 
 * 提供渲染优化、内存管理、对象池等性能优化功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

import { _decorator, Component, Node, NodePool, Prefab, instantiate, director, game } from 'cc';

const { ccclass } = _decorator;

/** 对象池配置 */
interface PoolConfig {
    prefab: Prefab;
    initialSize: number;
    maxSize: number;
}

/** 性能监控数据 */
interface PerformanceMetrics {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    drawCalls: number;
    nodeCount: number;
    poolUsage: Map<string, number>;
}

/** 渲染批次信息 */
interface BatchInfo {
    nodeCount: number;
    textureId: string;
    materialId: string;
}

@ccclass('PerformanceOptimizer')
export class PerformanceOptimizer {
    
    private static _instance: PerformanceOptimizer = null;
    
    // ==================== 对象池管理 ====================
    
    /** 对象池映射 */
    private _pools: Map<string, NodePool> = new Map();
    
    /** 池配置 */
    private _poolConfigs: Map<string, PoolConfig> = new Map();
    
    // ==================== 性能监控 ====================
    
    /** 性能监控数据 */
    private _metrics: PerformanceMetrics = {
        fps: 60,
        frameTime: 16.67,
        memoryUsage: 0,
        drawCalls: 0,
        nodeCount: 0,
        poolUsage: new Map()
    };
    
    /** 监控定时器 */
    private _monitorTimer: number = null;
    private readonly MONITOR_INTERVAL = 1000; // 1秒监控间隔
    
    // ==================== 渲染优化 ====================
    
    /** 批次渲染信息 */
    private _batchInfo: Map<string, BatchInfo> = new Map();
    
    /** 可见性检查缓存 */
    private _visibilityCache: Map<string, boolean> = new Map();
    
    /** LOD管理 */
    private _lodNodes: Map<Node, number> = new Map();
    
    // ==================== 内存管理 ====================
    
    /** 资源引用计数 */
    private _resourceRefs: Map<string, number> = new Map();
    
    /** 垃圾回收计时器 */
    private _gcTimer: number = null;
    private readonly GC_INTERVAL = 30000; // 30秒垃圾回收间隔

    // ==================== 单例模式 ====================
    
    public static getInstance(): PerformanceOptimizer {
        if (!PerformanceOptimizer._instance) {
            PerformanceOptimizer._instance = new PerformanceOptimizer();
        }
        return PerformanceOptimizer._instance;
    }
    
    private constructor() {
        this.initialize();
    }

    // ==================== 初始化 ====================
    
    /** 初始化性能优化器 */
    private initialize(): void {
        this.startPerformanceMonitoring();
        this.startMemoryManagement();
        this.setupRenderingOptimizations();
    }
    
    /** 开始性能监控 */
    private startPerformanceMonitoring(): void {
        this._monitorTimer = setInterval(() => {
            this.updatePerformanceMetrics();
        }, this.MONITOR_INTERVAL);
    }
    
    /** 开始内存管理 */
    private startMemoryManagement(): void {
        this._gcTimer = setInterval(() => {
            this.performGarbageCollection();
        }, this.GC_INTERVAL);
    }
    
    /** 设置渲染优化 */
    private setupRenderingOptimizations(): void {
        // 启用批次渲染
        this.enableBatchRendering();
        
        // 设置视锥体剔除
        this.enableFrustumCulling();
    }

    // ==================== 对象池管理 ====================
    
    /** 注册对象池 */
    public registerPool(poolName: string, config: PoolConfig): void {
        const pool = new NodePool();
        
        // 预填充对象池
        for (let i = 0; i < config.initialSize; i++) {
            const node = instantiate(config.prefab);
            pool.put(node);
        }
        
        this._pools.set(poolName, pool);
        this._poolConfigs.set(poolName, config);
        
        console.log(`Registered pool '${poolName}' with ${config.initialSize} initial objects`);
    }
    
    /** 从对象池获取节点 */
    public getFromPool(poolName: string): Node | null {
        const pool = this._pools.get(poolName);
        if (!pool) {
            console.error(`Pool '${poolName}' not found`);
            return null;
        }
        
        let node: Node;
        
        if (pool.size() > 0) {
            node = pool.get();
        } else {
            // 池为空，创建新对象
            const config = this._poolConfigs.get(poolName);
            if (config) {
                node = instantiate(config.prefab);
            } else {
                console.error(`Pool config for '${poolName}' not found`);
                return null;
            }
        }
        
        // 重置节点状态
        this.resetNodeState(node);
        
        return node;
    }
    
    /** 归还节点到对象池 */
    public returnToPool(poolName: string, node: Node): void {
        const pool = this._pools.get(poolName);
        if (!pool) {
            console.error(`Pool '${poolName}' not found`);
            node.destroy();
            return;
        }
        
        const config = this._poolConfigs.get(poolName);
        if (config && pool.size() >= config.maxSize) {
            // 池已满，直接销毁
            node.destroy();
            return;
        }
        
        // 清理节点状态
        this.cleanupNodeState(node);
        
        // 归还到池
        pool.put(node);
    }
    
    /** 重置节点状态 */
    private resetNodeState(node: Node): void {
        node.active = true;
        node.setScale(1, 1, 1);
        node.setPosition(0, 0, 0);
        node.angle = 0;
        node.opacity = 255;
    }
    
    /** 清理节点状态 */
    private cleanupNodeState(node: Node): void {
        node.active = false;
        node.removeFromParent();
        
        // 停止所有动画
        node.stopAllActions();
        
        // 清理组件状态
        const components = node.getComponents(Component);
        components.forEach(comp => {
            if (comp && typeof comp['reset'] === 'function') {
                comp['reset']();
            }
        });
    }

    // ==================== 渲染优化 ====================
    
    /** 启用批次渲染 */
    private enableBatchRendering(): void {
        // 这里应该实现批次渲染逻辑
        // Cocos Creator 3.x 已经内置了批次渲染优化
        console.log('Batch rendering enabled');
    }
    
    /** 启用视锥体剔除 */
    private enableFrustumCulling(): void {
        // 这里应该实现视锥体剔除逻辑
        console.log('Frustum culling enabled');
    }
    
    /** 优化弹幕渲染 */
    public optimizeBarrageRendering(barrageNodes: Node[]): void {
        // 限制同时显示的弹幕数量
        const maxVisibleBarrages = 20;
        
        // 按距离排序，只渲染最近的弹幕
        const visibleBarrages = barrageNodes
            .filter(node => this.isNodeVisible(node))
            .slice(0, maxVisibleBarrages);
        
        // 隐藏超出限制的弹幕
        barrageNodes.forEach((node, index) => {
            const shouldVisible = index < maxVisibleBarrages && this.isNodeVisible(node);
            node.active = shouldVisible;
        });
    }
    
    /** 检查节点是否可见 */
    private isNodeVisible(node: Node): boolean {
        const nodeId = node.uuid;
        
        // 检查缓存
        if (this._visibilityCache.has(nodeId)) {
            return this._visibilityCache.get(nodeId);
        }
        
        // 简单的可见性检查（实际项目中需要更复杂的逻辑）
        const visible = node.active && node.parent && node.parent.active;
        
        // 缓存结果
        this._visibilityCache.set(nodeId, visible);
        
        // 定期清理缓存
        setTimeout(() => {
            this._visibilityCache.delete(nodeId);
        }, 5000);
        
        return visible;
    }
    
    /** 设置LOD级别 */
    public setLODLevel(node: Node, level: number): void {
        this._lodNodes.set(node, level);
        
        // 根据LOD级别调整渲染质量
        switch (level) {
            case 0: // 高质量
                node.opacity = 255;
                break;
            case 1: // 中等质量
                node.opacity = 200;
                break;
            case 2: // 低质量
                node.opacity = 150;
                break;
            default:
                node.active = false; // 不渲染
        }
    }

    // ==================== 内存管理 ====================
    
    /** 执行垃圾回收 */
    private performGarbageCollection(): void {
        // 清理对象池
        this.cleanupPools();
        
        // 清理缓存
        this.cleanupCaches();
        
        // 清理资源引用
        this.cleanupResourceReferences();
        
        console.log('Garbage collection performed');
    }
    
    /** 清理对象池 */
    private cleanupPools(): void {
        this._pools.forEach((pool, poolName) => {
            const config = this._poolConfigs.get(poolName);
            if (config && pool.size() > config.initialSize) {
                // 清理多余的对象
                const excessCount = pool.size() - config.initialSize;
                for (let i = 0; i < excessCount; i++) {
                    const node = pool.get();
                    if (node) {
                        node.destroy();
                    }
                }
            }
        });
    }
    
    /** 清理缓存 */
    private cleanupCaches(): void {
        // 清理可见性缓存
        this._visibilityCache.clear();
        
        // 清理批次信息
        this._batchInfo.clear();
    }
    
    /** 清理资源引用 */
    private cleanupResourceReferences(): void {
        // 清理未使用的资源引用
        const unusedRefs: string[] = [];
        
        this._resourceRefs.forEach((refCount, resourceId) => {
            if (refCount <= 0) {
                unusedRefs.push(resourceId);
            }
        });
        
        unusedRefs.forEach(resourceId => {
            this._resourceRefs.delete(resourceId);
        });
    }
    
    /** 增加资源引用 */
    public addResourceReference(resourceId: string): void {
        const currentRef = this._resourceRefs.get(resourceId) || 0;
        this._resourceRefs.set(resourceId, currentRef + 1);
    }
    
    /** 减少资源引用 */
    public removeResourceReference(resourceId: string): void {
        const currentRef = this._resourceRefs.get(resourceId) || 0;
        if (currentRef > 0) {
            this._resourceRefs.set(resourceId, currentRef - 1);
        }
    }

    // ==================== 性能监控 ====================
    
    /** 更新性能指标 */
    private updatePerformanceMetrics(): void {
        // 获取FPS
        this._metrics.fps = game.frameRate;
        
        // 计算帧时间
        this._metrics.frameTime = 1000 / this._metrics.fps;
        
        // 获取节点数量
        this._metrics.nodeCount = this.getSceneNodeCount();
        
        // 更新对象池使用情况
        this.updatePoolUsage();
        
        // 检查性能警告
        this.checkPerformanceWarnings();
    }
    
    /** 获取场景节点数量 */
    private getSceneNodeCount(): number {
        const scene = director.getScene();
        return scene ? this.countNodes(scene) : 0;
    }
    
    /** 递归计算节点数量 */
    private countNodes(node: Node): number {
        let count = 1;
        node.children.forEach(child => {
            count += this.countNodes(child);
        });
        return count;
    }
    
    /** 更新对象池使用情况 */
    private updatePoolUsage(): void {
        this._metrics.poolUsage.clear();
        
        this._pools.forEach((pool, poolName) => {
            this._metrics.poolUsage.set(poolName, pool.size());
        });
    }
    
    /** 检查性能警告 */
    private checkPerformanceWarnings(): void {
        // FPS过低警告
        if (this._metrics.fps < 30) {
            console.warn(`Low FPS detected: ${this._metrics.fps}`);
        }
        
        // 节点数量过多警告
        if (this._metrics.nodeCount > 1000) {
            console.warn(`High node count: ${this._metrics.nodeCount}`);
        }
        
        // 帧时间过长警告
        if (this._metrics.frameTime > 33.33) {
            console.warn(`High frame time: ${this._metrics.frameTime}ms`);
        }
    }

    // ==================== 公共接口 ====================
    
    /** 获取性能指标 */
    public getPerformanceMetrics(): PerformanceMetrics {
        return { ...this._metrics };
    }
    
    /** 获取对象池状态 */
    public getPoolStatus(poolName: string): { size: number; maxSize: number } | null {
        const pool = this._pools.get(poolName);
        const config = this._poolConfigs.get(poolName);
        
        if (pool && config) {
            return {
                size: pool.size(),
                maxSize: config.maxSize
            };
        }
        
        return null;
    }
    
    /** 强制垃圾回收 */
    public forceGarbageCollection(): void {
        this.performGarbageCollection();
    }
    
    /** 优化场景渲染 */
    public optimizeSceneRendering(): void {
        const scene = director.getScene();
        if (scene) {
            this.optimizeNodeHierarchy(scene);
        }
    }
    
    /** 优化节点层次结构 */
    private optimizeNodeHierarchy(node: Node): void {
        // 移除不活跃的节点
        const inactiveChildren = node.children.filter(child => !child.active);
        inactiveChildren.forEach(child => {
            if (this.canSafelyRemove(child)) {
                child.removeFromParent();
            }
        });
        
        // 递归优化子节点
        node.children.forEach(child => {
            this.optimizeNodeHierarchy(child);
        });
    }
    
    /** 检查节点是否可以安全移除 */
    private canSafelyRemove(node: Node): boolean {
        // 检查节点是否有重要组件或数据
        const components = node.getComponents(Component);
        return components.length === 0;
    }

    // ==================== 清理 ====================
    
    /** 销毁性能优化器 */
    public destroy(): void {
        // 清理定时器
        if (this._monitorTimer) {
            clearInterval(this._monitorTimer);
        }
        
        if (this._gcTimer) {
            clearInterval(this._gcTimer);
        }
        
        // 清理对象池
        this._pools.forEach(pool => {
            pool.clear();
        });
        
        // 清理缓存
        this._visibilityCache.clear();
        this._batchInfo.clear();
        this._resourceRefs.clear();
        
        PerformanceOptimizer._instance = null;
    }
}
