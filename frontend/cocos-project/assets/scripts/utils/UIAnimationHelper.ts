import { Node, Vec3, Color } from 'cc';
import { UIAnimationPool, AnimationType, AnimationConfig } from './UIAnimationPool';
import { UI_CONFIG } from '../constants/GameConstants';

/**
 * UI动画助手类
 * 提供常用动画的便捷方法，内部使用对象池优化性能
 */
export class UIAnimationHelper {
    private static _animationPool: UIAnimationPool = UIAnimationPool.getInstance();
    
    /**
     * 播放缩放进入动画
     */
    public static scaleIn(node: Node, duration: number = 300, delay: number = 0, onComplete?: () => void): string {
        // 设置初始状态
        node.setScale(0, 0, 1);
        
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration,
            delay,
            easing: 'backOut',
            to: new Vec3(1, 1, 1),
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放缩放退出动画
     */
    public static scaleOut(node: Node, duration: number = 200, delay: number = 0, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration,
            delay,
            easing: 'backIn',
            to: new Vec3(0, 0, 1),
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放淡入动画
     */
    public static fadeIn(node: Node, duration: number = 300, delay: number = 0, onComplete?: () => void): string {
        // 设置初始状态
        node.opacity = 0;
        
        const config: AnimationConfig = {
            type: AnimationType.FADE,
            duration,
            delay,
            easing: 'sineOut',
            to: 255,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放淡出动画
     */
    public static fadeOut(node: Node, duration: number = 300, delay: number = 0, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.FADE,
            duration,
            delay,
            easing: 'sineIn',
            to: 0,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放弹跳动画
     */
    public static bounce(node: Node, duration: number = 600, delay: number = 0, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.BOUNCE,
            duration,
            delay,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放震动动画
     */
    public static shake(node: Node, intensity: number = 10, duration: number = 400, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.SHAKE,
            duration,
            by: new Vec3(intensity, 0, 0),
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放脉冲动画
     */
    public static pulse(node: Node, duration: number = 1000, loop: boolean = true, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.PULSE,
            duration,
            loop,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放旋转动画
     */
    public static rotate(node: Node, angle: number = 360, duration: number = 1000, loop: boolean = false, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.ROTATE,
            duration,
            loop,
            by: angle,
            easing: 'linear',
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放滑入动画（从左侧）
     */
    public static slideInLeft(node: Node, distance: number = 300, duration: number = 400, onComplete?: () => void): string {
        // 设置初始位置
        const originalPos = node.position.clone();
        node.setPosition(originalPos.x - distance, originalPos.y, originalPos.z);
        
        const config: AnimationConfig = {
            type: AnimationType.SLIDE,
            duration,
            easing: 'sineOut',
            to: originalPos,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放滑入动画（从右侧）
     */
    public static slideInRight(node: Node, distance: number = 300, duration: number = 400, onComplete?: () => void): string {
        // 设置初始位置
        const originalPos = node.position.clone();
        node.setPosition(originalPos.x + distance, originalPos.y, originalPos.z);
        
        const config: AnimationConfig = {
            type: AnimationType.SLIDE,
            duration,
            easing: 'sineOut',
            to: originalPos,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放滑入动画（从上方）
     */
    public static slideInTop(node: Node, distance: number = 300, duration: number = 400, onComplete?: () => void): string {
        // 设置初始位置
        const originalPos = node.position.clone();
        node.setPosition(originalPos.x, originalPos.y + distance, originalPos.z);
        
        const config: AnimationConfig = {
            type: AnimationType.SLIDE,
            duration,
            easing: 'sineOut',
            to: originalPos,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放滑入动画（从下方）
     */
    public static slideInBottom(node: Node, distance: number = 300, duration: number = 400, onComplete?: () => void): string {
        // 设置初始位置
        const originalPos = node.position.clone();
        node.setPosition(originalPos.x, originalPos.y - distance, originalPos.z);
        
        const config: AnimationConfig = {
            type: AnimationType.SLIDE,
            duration,
            easing: 'sineOut',
            to: originalPos,
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放点击反馈动画
     */
    public static clickFeedback(node: Node, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration: 200,
            easing: 'sineInOut',
            to: new Vec3(0.95, 0.95, 1),
            onComplete: () => {
                // 恢复原始大小
                this._animationPool.playAnimation(node, {
                    type: AnimationType.SCALE,
                    duration: 100,
                    easing: 'sineOut',
                    to: new Vec3(1, 1, 1),
                    onComplete
                });
            }
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放悬停反馈动画
     */
    public static hoverFeedback(node: Node, scale: number = 1.05, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration: 200,
            easing: 'sineOut',
            to: new Vec3(scale, scale, 1),
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 停止悬停反馈动画
     */
    public static stopHoverFeedback(node: Node, onComplete?: () => void): string {
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration: 200,
            easing: 'sineOut',
            to: new Vec3(1, 1, 1),
            onComplete
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放成功反馈动画
     */
    public static successFeedback(node: Node, onComplete?: () => void): string {
        // 先缩放放大，再恢复
        const config: AnimationConfig = {
            type: AnimationType.SCALE,
            duration: 150,
            easing: 'backOut',
            to: new Vec3(1.1, 1.1, 1),
            onComplete: () => {
                this._animationPool.playAnimation(node, {
                    type: AnimationType.SCALE,
                    duration: 200,
                    easing: 'backOut',
                    to: new Vec3(1, 1, 1),
                    onComplete
                });
            }
        };
        
        return this._animationPool.playAnimation(node, config);
    }
    
    /**
     * 播放错误反馈动画
     */
    public static errorFeedback(node: Node, onComplete?: () => void): string {
        return this.shake(node, 15, 400, onComplete);
    }
    
    /**
     * 播放组合动画：缩放+淡入
     */
    public static scaleAndFadeIn(node: Node, duration: number = 400, delay: number = 0, onComplete?: () => void): string[] {
        // 设置初始状态
        node.setScale(0.8, 0.8, 1);
        node.opacity = 0;
        
        const scaleId = this.scaleIn(node, duration, delay);
        const fadeId = this.fadeIn(node, duration, delay, onComplete);
        
        return [scaleId, fadeId];
    }
    
    /**
     * 播放组合动画：缩放+淡出
     */
    public static scaleAndFadeOut(node: Node, duration: number = 300, delay: number = 0, onComplete?: () => void): string[] {
        const scaleId = this.scaleOut(node, duration, delay);
        const fadeId = this.fadeOut(node, duration, delay, onComplete);
        
        return [scaleId, fadeId];
    }
    
    /**
     * 停止节点上的所有动画
     */
    public static stopNodeAnimations(node: Node): void {
        this._animationPool.stopNodeAnimations(node);
    }
    
    /**
     * 停止指定动画
     */
    public static stopAnimation(animationId: string): void {
        this._animationPool.stopAnimation(animationId);
    }
    
    /**
     * 停止所有动画
     */
    public static stopAllAnimations(): void {
        this._animationPool.stopAllAnimations();
    }
    
    /**
     * 获取动画池统计信息
     */
    public static getAnimationStats(): any {
        return this._animationPool.getStats();
    }
}
