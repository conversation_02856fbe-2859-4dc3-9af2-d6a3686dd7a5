import { _decorator } from 'cc';
import { ERROR_CODES } from '../constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 错误类型枚举
 */
export enum ErrorType {
    NETWORK = 'network',
    AUDIO = 'audio',
    GAME_LOGIC = 'game_logic',
    STORAGE = 'storage',
    WECHAT = 'wechat',
    SYSTEM = 'system',
    USER_INPUT = 'user_input'
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * 游戏错误接口
 */
export interface IGameError {
    code: number;
    type: ErrorType;
    severity: ErrorSeverity;
    message: string;
    context?: Record<string, any>;
    timestamp: number;
    stack?: string;
    userId?: string;
    sessionId?: string;
}

/**
 * 错误处理器接口
 */
export interface IErrorHandler {
    canHandle(error: IGameError): boolean;
    handle(error: IGameError): Promise<void>;
    priority: number;
}

/**
 * 统一错误处理系统
 */
@ccclass('ErrorHandlingSystem')
export class ErrorHandlingSystem {
    private static _instance: ErrorHandlingSystem = null;
    
    // 错误处理器列表
    private _handlers: IErrorHandler[] = [];
    
    // 错误日志缓存
    private _errorLog: IGameError[] = [];
    
    // 最大错误日志数量
    private readonly MAX_ERROR_LOG_SIZE = 100;
    
    // 错误统计
    private _errorStats: Map<string, number> = new Map();
    
    public static getInstance(): ErrorHandlingSystem {
        if (!this._instance) {
            this._instance = new ErrorHandlingSystem();
            this._instance.initialize();
        }
        return this._instance;
    }
    
    /**
     * 初始化错误处理系统
     */
    private initialize(): void {
        // 注册默认错误处理器
        this.registerDefaultHandlers();
        
        // 监听全局错误
        this.setupGlobalErrorHandling();
        
        console.log('[ErrorHandlingSystem] 错误处理系统初始化完成');
    }
    
    /**
     * 注册错误处理器
     */
    public registerHandler(handler: IErrorHandler): void {
        this._handlers.push(handler);
        // 按优先级排序
        this._handlers.sort((a, b) => b.priority - a.priority);
        
        console.log(`[ErrorHandlingSystem] 注册错误处理器，优先级: ${handler.priority}`);
    }
    
    /**
     * 处理错误
     */
    public async handleError(
        error: Error | string | IGameError,
        context?: Record<string, any>
    ): Promise<void> {
        try {
            // 转换为标准错误格式
            const gameError = this.normalizeError(error, context);
            
            // 记录错误
            this.logError(gameError);
            
            // 更新错误统计
            this.updateErrorStats(gameError);
            
            // 查找合适的处理器
            const handler = this._handlers.find(h => h.canHandle(gameError));
            
            if (handler) {
                await handler.handle(gameError);
            } else {
                // 使用默认处理
                await this.defaultErrorHandling(gameError);
            }
            
        } catch (handlingError) {
            console.error('[ErrorHandlingSystem] 错误处理失败:', handlingError);
            // 避免无限递归，直接输出到控制台
            console.error('[ErrorHandlingSystem] 原始错误:', error);
        }
    }
    
    /**
     * 标准化错误格式
     */
    private normalizeError(
        error: Error | string | IGameError,
        context?: Record<string, any>
    ): IGameError {
        if (typeof error === 'string') {
            return {
                code: ERROR_CODES.GAME_DATA_INVALID,
                type: ErrorType.SYSTEM,
                severity: ErrorSeverity.MEDIUM,
                message: error,
                context,
                timestamp: Date.now()
            };
        }
        
        if (error instanceof Error) {
            return {
                code: this.getErrorCode(error),
                type: this.getErrorType(error),
                severity: this.getErrorSeverity(error),
                message: error.message,
                context,
                timestamp: Date.now(),
                stack: error.stack
            };
        }
        
        // 已经是IGameError格式
        return {
            ...error,
            context: { ...error.context, ...context },
            timestamp: error.timestamp || Date.now()
        };
    }
    
    /**
     * 获取错误代码
     */
    private getErrorCode(error: Error): number {
        // 根据错误类型和消息判断错误代码
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('fetch')) {
            return ERROR_CODES.NETWORK_ERROR;
        }
        if (message.includes('audio')) {
            return ERROR_CODES.AUDIO_LOAD_FAILED;
        }
        if (message.includes('timeout')) {
            return ERROR_CODES.REQUEST_TIMEOUT;
        }
        if (message.includes('wechat')) {
            return ERROR_CODES.WECHAT_AUTH_FAILED;
        }
        
        return ERROR_CODES.GAME_DATA_INVALID;
    }
    
    /**
     * 获取错误类型
     */
    private getErrorType(error: Error): ErrorType {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('fetch')) {
            return ErrorType.NETWORK;
        }
        if (message.includes('audio')) {
            return ErrorType.AUDIO;
        }
        if (message.includes('storage') || message.includes('localstorage')) {
            return ErrorType.STORAGE;
        }
        if (message.includes('wechat')) {
            return ErrorType.WECHAT;
        }
        
        return ErrorType.SYSTEM;
    }
    
    /**
     * 获取错误严重程度
     */
    private getErrorSeverity(error: Error): ErrorSeverity {
        const message = error.message.toLowerCase();
        
        if (message.includes('critical') || message.includes('fatal')) {
            return ErrorSeverity.CRITICAL;
        }
        if (message.includes('network') || message.includes('audio')) {
            return ErrorSeverity.HIGH;
        }
        if (message.includes('warning') || message.includes('deprecated')) {
            return ErrorSeverity.LOW;
        }
        
        return ErrorSeverity.MEDIUM;
    }
    
    /**
     * 记录错误日志
     */
    private logError(error: IGameError): void {
        this._errorLog.push(error);
        
        // 限制日志大小
        if (this._errorLog.length > this.MAX_ERROR_LOG_SIZE) {
            this._errorLog.shift();
        }
        
        // 输出到控制台
        const logLevel = this.getLogLevel(error.severity);
        console[logLevel](`[ErrorHandlingSystem] ${error.type}:${error.code} - ${error.message}`, error);
    }
    
    /**
     * 获取日志级别
     */
    private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
        switch (severity) {
            case ErrorSeverity.LOW:
                return 'log';
            case ErrorSeverity.MEDIUM:
                return 'warn';
            case ErrorSeverity.HIGH:
            case ErrorSeverity.CRITICAL:
                return 'error';
            default:
                return 'warn';
        }
    }
    
    /**
     * 更新错误统计
     */
    private updateErrorStats(error: IGameError): void {
        const key = `${error.type}:${error.code}`;
        const count = this._errorStats.get(key) || 0;
        this._errorStats.set(key, count + 1);
    }
    
    /**
     * 默认错误处理
     */
    private async defaultErrorHandling(error: IGameError): Promise<void> {
        console.warn('[ErrorHandlingSystem] 使用默认错误处理:', error);
        
        // 根据错误严重程度决定处理方式
        switch (error.severity) {
            case ErrorSeverity.CRITICAL:
                // 关键错误，可能需要重启游戏
                this.handleCriticalError(error);
                break;
            case ErrorSeverity.HIGH:
                // 高级错误，显示错误提示
                this.showErrorToast(error);
                break;
            case ErrorSeverity.MEDIUM:
            case ErrorSeverity.LOW:
                // 中低级错误，仅记录日志
                break;
        }
    }
    
    /**
     * 处理关键错误
     */
    private handleCriticalError(error: IGameError): void {
        console.error('[ErrorHandlingSystem] 关键错误，建议重启游戏:', error);
        
        // 可以在这里添加重启游戏的逻辑
        // 或者显示重启提示
    }
    
    /**
     * 显示错误提示
     */
    private showErrorToast(error: IGameError): void {
        // 这里可以集成UI系统显示错误提示
        console.warn('[ErrorHandlingSystem] 显示错误提示:', error.message);
    }
    
    /**
     * 注册默认错误处理器
     */
    private registerDefaultHandlers(): void {
        // 网络错误处理器
        this.registerHandler({
            canHandle: (error) => error.type === ErrorType.NETWORK,
            handle: async (error) => {
                console.log('[ErrorHandlingSystem] 处理网络错误:', error);
                // 可以添加重试逻辑
            },
            priority: 10
        });
        
        // 音频错误处理器
        this.registerHandler({
            canHandle: (error) => error.type === ErrorType.AUDIO,
            handle: async (error) => {
                console.log('[ErrorHandlingSystem] 处理音频错误:', error);
                // 可以添加音频降级处理
            },
            priority: 8
        });
    }
    
    /**
     * 设置全局错误处理
     */
    private setupGlobalErrorHandling(): void {
        // 捕获未处理的Promise拒绝
        if (typeof window !== 'undefined') {
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(event.reason, { source: 'unhandledrejection' });
            });
            
            // 捕获全局错误
            window.addEventListener('error', (event) => {
                this.handleError(event.error || event.message, { 
                    source: 'global_error',
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });
        }
    }
    
    /**
     * 获取错误统计
     */
    public getErrorStats(): Record<string, number> {
        const stats: Record<string, number> = {};
        for (const [key, count] of this._errorStats.entries()) {
            stats[key] = count;
        }
        return stats;
    }
    
    /**
     * 获取错误日志
     */
    public getErrorLog(): IGameError[] {
        return [...this._errorLog];
    }
    
    /**
     * 清理错误日志
     */
    public clearErrorLog(): void {
        this._errorLog.length = 0;
        this._errorStats.clear();
        console.log('[ErrorHandlingSystem] 错误日志已清理');
    }
}
