import { _decorator } from 'cc';

const { ccclass } = _decorator;

/**
 * 管理器注册中心
 * 解决循环依赖问题，提供统一的管理器访问接口
 */
@ccclass('ManagerRegistry')
export class ManagerRegistry {
    private static _instance: ManagerRegistry = null;
    
    // 管理器实例映射
    private _managers: Map<string, any> = new Map();
    
    // 管理器初始化状态
    private _initializationStatus: Map<string, boolean> = new Map();
    
    // 管理器依赖关系
    private _dependencies: Map<string, string[]> = new Map();
    
    public static getInstance(): ManagerRegistry {
        if (!this._instance) {
            this._instance = new ManagerRegistry();
        }
        return this._instance;
    }
    
    /**
     * 注册管理器
     */
    public registerManager<T>(name: string, manager: T, dependencies: string[] = []): void {
        this._managers.set(name, manager);
        this._initializationStatus.set(name, false);
        this._dependencies.set(name, dependencies);
        
        console.log(`[ManagerRegistry] 注册管理器: ${name}`);
    }
    
    /**
     * 获取管理器
     */
    public getManager<T>(name: string): T | null {
        const manager = this._managers.get(name);
        if (!manager) {
            console.warn(`[ManagerRegistry] 管理器未找到: ${name}`);
            return null;
        }
        return manager as T;
    }
    
    /**
     * 标记管理器为已初始化
     */
    public markInitialized(name: string): void {
        this._initializationStatus.set(name, true);
        console.log(`[ManagerRegistry] 管理器初始化完成: ${name}`);
    }
    
    /**
     * 检查管理器是否已初始化
     */
    public isInitialized(name: string): boolean {
        return this._initializationStatus.get(name) || false;
    }
    
    /**
     * 获取所有已注册的管理器名称
     */
    public getRegisteredManagers(): string[] {
        return Array.from(this._managers.keys());
    }
    
    /**
     * 检查循环依赖
     */
    public checkCircularDependencies(): boolean {
        const visited = new Set<string>();
        const recursionStack = new Set<string>();
        
        for (const manager of this._managers.keys()) {
            if (this.hasCircularDependency(manager, visited, recursionStack)) {
                console.error(`[ManagerRegistry] 检测到循环依赖，涉及管理器: ${manager}`);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 递归检查循环依赖
     */
    private hasCircularDependency(
        manager: string, 
        visited: Set<string>, 
        recursionStack: Set<string>
    ): boolean {
        if (recursionStack.has(manager)) {
            return true; // 发现循环依赖
        }
        
        if (visited.has(manager)) {
            return false; // 已经检查过
        }
        
        visited.add(manager);
        recursionStack.add(manager);
        
        const dependencies = this._dependencies.get(manager) || [];
        for (const dependency of dependencies) {
            if (this.hasCircularDependency(dependency, visited, recursionStack)) {
                return true;
            }
        }
        
        recursionStack.delete(manager);
        return false;
    }
    
    /**
     * 获取初始化顺序（拓扑排序）
     */
    public getInitializationOrder(): string[] {
        const result: string[] = [];
        const visited = new Set<string>();
        const temp = new Set<string>();
        
        const visit = (manager: string) => {
            if (temp.has(manager)) {
                throw new Error(`循环依赖检测到: ${manager}`);
            }
            
            if (!visited.has(manager)) {
                temp.add(manager);
                
                const dependencies = this._dependencies.get(manager) || [];
                for (const dependency of dependencies) {
                    if (this._managers.has(dependency)) {
                        visit(dependency);
                    }
                }
                
                temp.delete(manager);
                visited.add(manager);
                result.push(manager);
            }
        };
        
        for (const manager of this._managers.keys()) {
            visit(manager);
        }
        
        return result;
    }
    
    /**
     * 清理所有管理器
     */
    public cleanup(): void {
        console.log('[ManagerRegistry] 清理所有管理器');
        
        // 按照依赖关系的逆序清理
        const initOrder = this.getInitializationOrder();
        const cleanupOrder = initOrder.reverse();
        
        for (const managerName of cleanupOrder) {
            const manager = this._managers.get(managerName);
            if (manager && typeof manager.cleanup === 'function') {
                try {
                    manager.cleanup();
                    console.log(`[ManagerRegistry] 清理管理器: ${managerName}`);
                } catch (error) {
                    console.error(`[ManagerRegistry] 清理管理器失败: ${managerName}`, error);
                }
            }
        }
        
        this._managers.clear();
        this._initializationStatus.clear();
        this._dependencies.clear();
    }
    
    /**
     * 获取管理器状态报告
     */
    public getStatusReport(): {
        registered: string[];
        initialized: string[];
        pending: string[];
        dependencies: Record<string, string[]>;
    } {
        const registered = Array.from(this._managers.keys());
        const initialized = registered.filter(name => this.isInitialized(name));
        const pending = registered.filter(name => !this.isInitialized(name));
        
        const dependencies: Record<string, string[]> = {};
        for (const [name, deps] of this._dependencies.entries()) {
            dependencies[name] = deps;
        }
        
        return {
            registered,
            initialized,
            pending,
            dependencies
        };
    }
}

/**
 * 管理器装饰器
 * 自动注册管理器到注册中心
 */
export function Manager(name: string, dependencies: string[] = []) {
    return function <T extends { new(...args: any[]): {} }>(constructor: T) {
        return class extends constructor {
            constructor(...args: any[]) {
                super(...args);
                
                // 注册到管理器注册中心
                const registry = ManagerRegistry.getInstance();
                registry.registerManager(name, this, dependencies);
            }
        };
    };
}
