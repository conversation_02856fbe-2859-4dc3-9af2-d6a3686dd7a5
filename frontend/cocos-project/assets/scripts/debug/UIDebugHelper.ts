import { _decorator, Component, director, Label, Layout, UITransform } from 'cc';

const { ccclass } = _decorator;

/**
 * UI调试助手
 * 提供UI问题诊断和调试功能
 */
@ccclass('UIDebugHelper')
export class UIDebugHelper {
    
    /**
     * 诊断当前场景的UI问题
     */
    public static diagnoseCurrentScene(): void {
        console.log('=== UI诊断报告 ===');
        
        const scene = director.getScene();
        if (!scene) {
            console.error('无法获取当前场景');
            return;
        }
        
        const issues = this.findUIIssues(scene);
        
        console.log(`发现 ${issues.length} 个潜在问题:`);
        issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
        
        if (issues.length === 0) {
            console.log('✅ 未发现明显的UI问题');
        }
        
        console.log('=== 诊断完成 ===');
    }
    
    /**
     * 查找UI问题
     */
    private static findUIIssues(node: any, path: string = ''): string[] {
        const issues: string[] = [];
        const currentPath = path ? `${path}/${node.name}` : node.name;
        
        try {
            // 检查Label组件问题
            const label = node.getComponent?.(Label);
            if (label) {
                if (!label.font) {
                    issues.push(`❌ ${currentPath}: Label组件缺少字体设置`);
                }
                
                if (label.string && label.string.length > 500) {
                    issues.push(`⚠️ ${currentPath}: Label文本过长 (${label.string.length}字符)`);
                }
            }
            
            // 检查Layout组件问题
            const layout = node.getComponent?.(Layout);
            if (layout) {
                if (!node.getComponent?.(UITransform)) {
                    issues.push(`❌ ${currentPath}: Layout节点缺少UITransform组件`);
                }
                
                // 检查子节点是否都有UITransform
                const children = node.children || [];
                children.forEach((child: any, index: number) => {
                    if (!child.getComponent?.(UITransform)) {
                        issues.push(`❌ ${currentPath}/child[${index}]: Layout子节点缺少UITransform组件`);
                    }
                });
            }
            
            // 检查UITransform组件问题
            const uiTransform = node.getComponent?.(UITransform);
            if (uiTransform) {
                try {
                    const contentSize = uiTransform.contentSize;
                    if (!contentSize || contentSize.width === undefined || contentSize.height === undefined) {
                        issues.push(`❌ ${currentPath}: UITransform的contentSize无效`);
                    }
                } catch (error) {
                    issues.push(`❌ ${currentPath}: UITransform访问contentSize时出错`);
                }
            }
            
            // 递归检查子节点
            const children = node.children || [];
            children.forEach((child: any) => {
                issues.push(...this.findUIIssues(child, currentPath));
            });
            
        } catch (error) {
            issues.push(`❌ ${currentPath}: 检查节点时发生错误 - ${error.message}`);
        }
        
        return issues;
    }
    
    /**
     * 统计场景中的UI组件
     */
    public static getUIStats(): any {
        const scene = director.getScene();
        if (!scene) {
            return null;
        }
        
        const stats = {
            totalNodes: 0,
            labels: 0,
            layouts: 0,
            uiTransforms: 0,
            labelsWithoutFont: 0,
            layoutsWithoutUITransform: 0
        };
        
        this.countUIComponents(scene, stats);
        
        console.log('=== UI组件统计 ===');
        console.log(`总节点数: ${stats.totalNodes}`);
        console.log(`Label组件: ${stats.labels}`);
        console.log(`Layout组件: ${stats.layouts}`);
        console.log(`UITransform组件: ${stats.uiTransforms}`);
        console.log(`缺少字体的Label: ${stats.labelsWithoutFont}`);
        console.log(`缺少UITransform的Layout: ${stats.layoutsWithoutUITransform}`);
        console.log('=== 统计完成 ===');
        
        return stats;
    }
    
    /**
     * 递归统计UI组件
     */
    private static countUIComponents(node: any, stats: any): void {
        stats.totalNodes++;
        
        try {
            // 统计Label组件
            const label = node.getComponent?.(Label);
            if (label) {
                stats.labels++;
                if (!label.font) {
                    stats.labelsWithoutFont++;
                }
            }
            
            // 统计Layout组件
            const layout = node.getComponent?.(Layout);
            if (layout) {
                stats.layouts++;
                if (!node.getComponent?.(UITransform)) {
                    stats.layoutsWithoutUITransform++;
                }
            }
            
            // 统计UITransform组件
            const uiTransform = node.getComponent?.(UITransform);
            if (uiTransform) {
                stats.uiTransforms++;
            }
            
            // 递归统计子节点
            const children = node.children || [];
            children.forEach((child: any) => {
                this.countUIComponents(child, stats);
            });
            
        } catch (error) {
            console.warn(`统计节点 ${node.name} 时发生错误:`, error);
        }
    }
    
    /**
     * 测试UI渲染性能
     */
    public static testUIPerformance(): void {
        console.log('=== UI性能测试 ===');
        
        const startTime = performance.now();
        
        // 模拟一些UI操作
        const scene = director.getScene();
        if (scene) {
            this.performUIOperations(scene);
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`UI操作耗时: ${duration.toFixed(2)}ms`);
        
        if (duration > 16) { // 超过一帧的时间
            console.warn('⚠️ UI操作耗时较长，可能影响帧率');
        } else {
            console.log('✅ UI性能正常');
        }
        
        console.log('=== 性能测试完成 ===');
    }
    
    /**
     * 执行一些UI操作来测试性能
     */
    private static performUIOperations(node: any): void {
        try {
            // 访问节点属性
            const name = node.name;
            const active = node.active;
            const children = node.children || [];
            
            // 访问UI组件
            const label = node.getComponent?.(Label);
            if (label) {
                const text = label.string;
                const font = label.font;
            }
            
            const layout = node.getComponent?.(Layout);
            if (layout) {
                const enabled = layout.enabled;
            }
            
            const uiTransform = node.getComponent?.(UITransform);
            if (uiTransform) {
                const contentSize = uiTransform.contentSize;
            }
            
            // 递归处理子节点
            children.forEach((child: any) => {
                this.performUIOperations(child);
            });
            
        } catch (error) {
            // 忽略错误，继续测试
        }
    }
}

// 将调试助手暴露到全局，方便在控制台中使用
if (typeof window !== 'undefined') {
    (window as any).UIDebugHelper = UIDebugHelper;
}
