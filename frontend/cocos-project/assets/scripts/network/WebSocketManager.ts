import { _decorator } from 'cc';
import { ManagerRegistry } from '../core/ManagerRegistry';
import { ErrorHandlingSystem, ErrorType, ErrorSeverity } from '../core/ErrorHandlingSystem';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * WebSocket连接状态
 */
export enum WebSocketState {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

/**
 * WebSocket消息类型
 */
export enum MessageType {
    // 房间相关
    JOIN_ROOM = 'join_room',
    LEAVE_ROOM = 'leave_room',
    ROOM_STATE = 'room_state',
    
    // 游戏相关
    GAME_START = 'game_start',
    GAME_END = 'game_end',
    QUESTION_START = 'question_start',
    ANSWER_SUBMIT = 'answer_submit',
    
    // 弹幕相关
    DANMAKU_SEND = 'danmaku_send',
    DANMAKU_RECEIVE = 'danmaku_receive',
    
    // 预测相关
    PREDICTION_SUBMIT = 'prediction_submit',
    PREDICTION_RESULT = 'prediction_result',
    
    // 系统消息
    HEARTBEAT = 'heartbeat',
    ERROR = 'error',
    NOTIFICATION = 'notification'
}

/**
 * WebSocket消息接口
 */
export interface IWebSocketMessage {
    type: MessageType;
    data: any;
    timestamp: number;
    messageId?: string;
    roomId?: string;
    userId?: string;
}

/**
 * 重连配置
 */
interface IReconnectConfig {
    maxAttempts: number;
    initialDelay: number;
    maxDelay: number;
    backoffFactor: number;
}

/**
 * WebSocket管理器
 * 负责WebSocket连接管理、消息处理、重连机制
 */
@ccclass('WebSocketManager')
export class WebSocketManager {
    private static _instance: WebSocketManager = null;
    
    // WebSocket连接
    private _socket: WebSocket = null;
    
    // 连接状态
    private _state: WebSocketState = WebSocketState.DISCONNECTED;
    
    // 服务器地址
    private _serverUrl: string = '';
    
    // 消息队列
    private _messageQueue: IWebSocketMessage[] = [];
    
    // 重连配置
    private _reconnectConfig: IReconnectConfig = {
        maxAttempts: 5,
        initialDelay: 1000,
        maxDelay: 30000,
        backoffFactor: 2
    };
    
    // 重连状态
    private _reconnectAttempts: number = 0;
    private _reconnectTimer: number = null;
    
    // 心跳机制
    private _heartbeatInterval: number = null;
    private _heartbeatTimeout: number = null;
    private readonly HEARTBEAT_INTERVAL = 30000; // 30秒
    private readonly HEARTBEAT_TIMEOUT = 10000;  // 10秒超时
    
    // 消息处理器映射
    private _messageHandlers: Map<MessageType, Function[]> = new Map();
    
    // 核心系统引用
    private _managerRegistry: ManagerRegistry = null;
    private _errorHandlingSystem: ErrorHandlingSystem = null;
    
    public static getInstance(): WebSocketManager {
        if (!this._instance) {
            this._instance = new WebSocketManager();
        }
        return this._instance;
    }
    
    /**
     * 初始化WebSocket管理器
     */
    public initialize(serverUrl: string): void {
        this._serverUrl = serverUrl;
        this._managerRegistry = ManagerRegistry.getInstance();
        this._errorHandlingSystem = ErrorHandlingSystem.getInstance();
        
        console.log('[WebSocketManager] 初始化完成，服务器地址:', serverUrl);
    }
    
    /**
     * 连接WebSocket服务器
     */
    public async connect(): Promise<void> {
        if (this._state === WebSocketState.CONNECTED || this._state === WebSocketState.CONNECTING) {
            console.warn('[WebSocketManager] 已经连接或正在连接中');
            return;
        }
        
        try {
            this._state = WebSocketState.CONNECTING;
            this.emitStateChange();
            
            console.log('[WebSocketManager] 正在连接到:', this._serverUrl);
            
            this._socket = new WebSocket(this._serverUrl);
            
            // 设置事件监听器
            this._socket.onopen = this.onSocketOpen.bind(this);
            this._socket.onmessage = this.onSocketMessage.bind(this);
            this._socket.onclose = this.onSocketClose.bind(this);
            this._socket.onerror = this.onSocketError.bind(this);
            
        } catch (error) {
            console.error('[WebSocketManager] 连接失败:', error);
            this._state = WebSocketState.ERROR;
            this.emitStateChange();
            this._errorHandlingSystem?.handleError(error, { context: 'WebSocketManager.connect' });
            throw error;
        }
    }
    
    /**
     * 断开WebSocket连接
     */
    public disconnect(): void {
        if (this._socket) {
            this._socket.close();
            this._socket = null;
        }
        
        this.clearHeartbeat();
        this.clearReconnectTimer();
        
        this._state = WebSocketState.DISCONNECTED;
        this.emitStateChange();
        
        console.log('[WebSocketManager] 已断开连接');
    }
    
    /**
     * 发送消息
     */
    public sendMessage(type: MessageType, data: any, roomId?: string): void {
        const message: IWebSocketMessage = {
            type,
            data,
            timestamp: Date.now(),
            messageId: this.generateMessageId(),
            roomId,
            userId: this.getCurrentUserId()
        };
        
        if (this._state === WebSocketState.CONNECTED) {
            try {
                this._socket.send(JSON.stringify(message));
                console.log('[WebSocketManager] 发送消息:', message.type);
            } catch (error) {
                console.error('[WebSocketManager] 发送消息失败:', error);
                this._errorHandlingSystem?.handleError(error, { context: 'WebSocketManager.sendMessage' });
            }
        } else {
            // 连接断开时，将消息加入队列
            this._messageQueue.push(message);
            console.log('[WebSocketManager] 消息已加入队列:', message.type);
        }
    }
    
    /**
     * 注册消息处理器
     */
    public onMessage(type: MessageType, handler: Function): void {
        if (!this._messageHandlers.has(type)) {
            this._messageHandlers.set(type, []);
        }
        this._messageHandlers.get(type).push(handler);
    }
    
    /**
     * 移除消息处理器
     */
    public offMessage(type: MessageType, handler: Function): void {
        const handlers = this._messageHandlers.get(type);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    /**
     * 获取连接状态
     */
    public getState(): WebSocketState {
        return this._state;
    }
    
    /**
     * 是否已连接
     */
    public isConnected(): boolean {
        return this._state === WebSocketState.CONNECTED;
    }
    
    // ========== 私有方法 ==========
    
    /**
     * WebSocket打开事件
     */
    private onSocketOpen(event: Event): void {
        console.log('[WebSocketManager] 连接已建立');
        
        this._state = WebSocketState.CONNECTED;
        this._reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 发送队列中的消息
        this.flushMessageQueue();
        
        this.emitStateChange();
    }
    
    /**
     * WebSocket消息事件
     */
    private onSocketMessage(event: MessageEvent): void {
        try {
            const message: IWebSocketMessage = JSON.parse(event.data);
            console.log('[WebSocketManager] 收到消息:', message.type);
            
            // 处理心跳响应
            if (message.type === MessageType.HEARTBEAT) {
                this.handleHeartbeatResponse();
                return;
            }
            
            // 分发消息给处理器
            this.dispatchMessage(message);
            
        } catch (error) {
            console.error('[WebSocketManager] 解析消息失败:', error);
            this._errorHandlingSystem?.handleError(error, { context: 'WebSocketManager.onSocketMessage' });
        }
    }
    
    /**
     * WebSocket关闭事件
     */
    private onSocketClose(event: CloseEvent): void {
        console.log('[WebSocketManager] 连接已关闭, 代码:', event.code, '原因:', event.reason);
        
        this._state = WebSocketState.DISCONNECTED;
        this.clearHeartbeat();
        
        // 如果不是主动关闭，尝试重连
        if (event.code !== 1000) {
            this.attemptReconnect();
        }
        
        this.emitStateChange();
    }
    
    /**
     * WebSocket错误事件
     */
    private onSocketError(event: Event): void {
        console.error('[WebSocketManager] 连接错误:', event);
        
        this._state = WebSocketState.ERROR;
        this.emitStateChange();
        
        this._errorHandlingSystem?.handleError(new Error('WebSocket连接错误'), {
            context: 'WebSocketManager.onSocketError',
            event
        });
    }
    
    /**
     * 分发消息
     */
    private dispatchMessage(message: IWebSocketMessage): void {
        const handlers = this._messageHandlers.get(message.type);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(message);
                } catch (error) {
                    console.error('[WebSocketManager] 消息处理器错误:', error);
                    this._errorHandlingSystem?.handleError(error, {
                        context: 'WebSocketManager.dispatchMessage',
                        messageType: message.type
                    });
                }
            });
        }
        
        // 发送到事件管理器
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit(`websocket_${message.type}`, message);
        }
    }
    
    /**
     * 尝试重连
     */
    private attemptReconnect(): void {
        if (this._reconnectAttempts >= this._reconnectConfig.maxAttempts) {
            console.error('[WebSocketManager] 重连次数已达上限');
            return;
        }
        
        this._state = WebSocketState.RECONNECTING;
        this._reconnectAttempts++;
        
        const delay = Math.min(
            this._reconnectConfig.initialDelay * Math.pow(this._reconnectConfig.backoffFactor, this._reconnectAttempts - 1),
            this._reconnectConfig.maxDelay
        );
        
        console.log(`[WebSocketManager] ${delay}ms后尝试第${this._reconnectAttempts}次重连`);
        
        this._reconnectTimer = setTimeout(() => {
            this.connect().catch(error => {
                console.error('[WebSocketManager] 重连失败:', error);
            });
        }, delay);
        
        this.emitStateChange();
    }
    
    /**
     * 启动心跳
     */
    private startHeartbeat(): void {
        this.clearHeartbeat();
        
        this._heartbeatInterval = setInterval(() => {
            if (this._state === WebSocketState.CONNECTED) {
                this.sendMessage(MessageType.HEARTBEAT, { timestamp: Date.now() });
                
                // 设置心跳超时
                this._heartbeatTimeout = setTimeout(() => {
                    console.warn('[WebSocketManager] 心跳超时，断开连接');
                    this.disconnect();
                }, this.HEARTBEAT_TIMEOUT);
            }
        }, this.HEARTBEAT_INTERVAL);
    }
    
    /**
     * 处理心跳响应
     */
    private handleHeartbeatResponse(): void {
        if (this._heartbeatTimeout) {
            clearTimeout(this._heartbeatTimeout);
            this._heartbeatTimeout = null;
        }
    }
    
    /**
     * 清理心跳
     */
    private clearHeartbeat(): void {
        if (this._heartbeatInterval) {
            clearInterval(this._heartbeatInterval);
            this._heartbeatInterval = null;
        }
        
        if (this._heartbeatTimeout) {
            clearTimeout(this._heartbeatTimeout);
            this._heartbeatTimeout = null;
        }
    }
    
    /**
     * 清理重连定时器
     */
    private clearReconnectTimer(): void {
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = null;
        }
    }
    
    /**
     * 发送队列中的消息
     */
    private flushMessageQueue(): void {
        while (this._messageQueue.length > 0) {
            const message = this._messageQueue.shift();
            try {
                this._socket.send(JSON.stringify(message));
                console.log('[WebSocketManager] 发送队列消息:', message.type);
            } catch (error) {
                console.error('[WebSocketManager] 发送队列消息失败:', error);
                break;
            }
        }
    }
    
    /**
     * 发送状态变化事件
     */
    private emitStateChange(): void {
        const eventManager = this._managerRegistry?.getManager<EventManager>('EventManager');
        if (eventManager) {
            eventManager.emit('websocket_state_changed', {
                state: this._state,
                timestamp: Date.now()
            });
        }
    }
    
    /**
     * 生成消息ID
     */
    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取当前用户ID
     */
    private getCurrentUserId(): string {
        // 这里应该从用户管理器获取当前用户ID
        return 'user_' + Date.now();
    }
}
