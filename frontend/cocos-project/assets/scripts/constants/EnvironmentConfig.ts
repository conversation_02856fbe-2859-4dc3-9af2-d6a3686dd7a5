/**
 * 环境配置管理
 * 家乡话猜猜猜小游戏
 */

export enum Environment {
    DEVELOPMENT = 'development',
    TESTING = 'testing',
    STAGING = 'staging',
    PRODUCTION = 'production'
}

// 当前环境配置（部署时修改此值）
export const CURRENT_ENV: Environment = Environment.DEVELOPMENT;

// 环境特定配置
export const ENV_CONFIGS = {
    [Environment.DEVELOPMENT]: {
        apiBaseUrl: 'http://localhost:3001',
        debugEnabled: true,
        logLevel: 'debug',
        showFPS: true,
        showMemory: true,
        skipAudioLoading: false,
        autoAnswer: false,
        apiDebug: true,
        showDetailedErrors: true,
        simulateNetworkDelay: 0,
        simulateNetworkErrorRate: 0,
        performanceMonitor: true
    },
    [Environment.TESTING]: {
        apiBaseUrl: 'http://localhost:3001',
        debugEnabled: true,
        logLevel: 'info',
        showFPS: true,
        showMemory: true,
        skipAudioLoading: false,
        autoAnswer: false,
        apiDebug: true,
        showDetailedErrors: true,
        simulateNetworkDelay: 0,
        simulateNetworkErrorRate: 0,
        performanceMonitor: true
    },
    [Environment.STAGING]: {
        apiBaseUrl: 'https://staging-api.hometown-dialect.com',
        debugEnabled: false,
        logLevel: 'warn',
        showFPS: false,
        showMemory: false,
        skipAudioLoading: false,
        autoAnswer: false,
        apiDebug: false,
        showDetailedErrors: false,
        simulateNetworkDelay: 0,
        simulateNetworkErrorRate: 0,
        performanceMonitor: false
    },
    [Environment.PRODUCTION]: {
        apiBaseUrl: 'https://api.hometown-dialect.com',
        debugEnabled: false,
        logLevel: 'error',
        showFPS: false,
        showMemory: false,
        skipAudioLoading: false,
        autoAnswer: false,
        apiDebug: false,
        showDetailedErrors: false,
        simulateNetworkDelay: 0,
        simulateNetworkErrorRate: 0,
        performanceMonitor: false
    }
};

// 获取当前环境配置
export function getCurrentConfig() {
    return ENV_CONFIGS[CURRENT_ENV];
}

// 检查是否为开发环境
export function isDevelopment(): boolean {
    return CURRENT_ENV === Environment.DEVELOPMENT;
}

// 检查是否为测试环境
export function isTesting(): boolean {
    return CURRENT_ENV === Environment.TESTING;
}

// 检查是否为预发布环境
export function isStaging(): boolean {
    return CURRENT_ENV === Environment.STAGING;
}

// 检查是否为生产环境
export function isProduction(): boolean {
    return CURRENT_ENV === Environment.PRODUCTION;
}

// 检查是否为调试环境（开发或测试）
export function isDebugEnvironment(): boolean {
    return isDevelopment() || isTesting();
}

// 获取环境名称（用于显示）
export function getEnvironmentName(): string {
    const names = {
        [Environment.DEVELOPMENT]: '开发环境',
        [Environment.TESTING]: '测试环境',
        [Environment.STAGING]: '预发布环境',
        [Environment.PRODUCTION]: '生产环境'
    };
    return names[CURRENT_ENV];
}

// 获取API基础URL
export function getApiBaseUrl(): string {
    return getCurrentConfig().apiBaseUrl;
}

// 获取调试配置
export function getDebugConfig() {
    const config = getCurrentConfig();
    return {
        ENABLED: config.debugEnabled,
        LOG_LEVEL: config.logLevel,
        PERFORMANCE_MONITOR: config.performanceMonitor,
        SHOW_FPS: config.showFPS,
        SHOW_MEMORY: config.showMemory,
        SKIP_AUDIO_LOADING: config.skipAudioLoading,
        AUTO_ANSWER: config.autoAnswer,
        API_DEBUG: config.apiDebug,
        SHOW_DETAILED_ERRORS: config.showDetailedErrors,
        SIMULATE_NETWORK_DELAY: config.simulateNetworkDelay,
        SIMULATE_NETWORK_ERROR_RATE: config.simulateNetworkErrorRate
    };
}

// 环境切换工具（仅在非生产环境下可用）
export class EnvironmentSwitcher {
    private static readonly STORAGE_KEY = 'env_override';

    /**
     * 临时切换环境（仅在调试模式下有效）
     */
    public static switchTo(env: Environment): void {
        if (isProduction()) {
            console.warn('[EnvironmentSwitcher] 生产环境下不允许切换环境');
            return;
        }

        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(this.STORAGE_KEY, env);
            console.log(`[EnvironmentSwitcher] 环境已切换到: ${getEnvironmentName()}`);
            
            // 建议重载页面以应用新配置
            console.log('[EnvironmentSwitcher] 建议重载页面以应用新配置');
        }
    }

    /**
     * 获取当前环境（考虑临时切换）
     */
    public static getCurrentEnvironment(): Environment {
        if (isProduction()) {
            return Environment.PRODUCTION;
        }

        if (typeof localStorage !== 'undefined') {
            const override = localStorage.getItem(this.STORAGE_KEY) as Environment;
            if (override && Object.values(Environment).includes(override)) {
                return override;
            }
        }

        return CURRENT_ENV;
    }

    /**
     * 重置环境到默认值
     */
    public static reset(): void {
        if (isProduction()) {
            console.warn('[EnvironmentSwitcher] 生产环境下不允许重置环境');
            return;
        }

        if (typeof localStorage !== 'undefined') {
            localStorage.removeItem(this.STORAGE_KEY);
            console.log('[EnvironmentSwitcher] 环境已重置');
        }
    }
}

// 暴露全局环境切换工具（仅在调试模式下）
if (isDebugEnvironment() && typeof window !== 'undefined') {
    (window as any).envSwitcher = EnvironmentSwitcher;
    console.log('%c[Environment] 环境切换工具已加载', 'color: #00ff00;');
    console.log('可用命令:');
    console.log('- envSwitcher.switchTo(Environment.DEVELOPMENT) // 切换到开发环境');
    console.log('- envSwitcher.switchTo(Environment.TESTING)     // 切换到测试环境');
    console.log('- envSwitcher.switchTo(Environment.STAGING)     // 切换到预发布环境');
    console.log('- envSwitcher.reset()                           // 重置环境');
}