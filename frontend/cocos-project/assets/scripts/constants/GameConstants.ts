import { _decorator } from 'cc';
import { getApiBaseUrl, getDebugConfig } from './EnvironmentConfig';

/**
 * 游戏核心常量配置
 * 家乡话猜猜猜小游戏
 */

// 游戏难度等级
export enum GameDifficulty {
    EASY = 'easy',        // 简单：10分
    MEDIUM = 'medium',    // 中等：20分
    HARD = 'hard'         // 困难：30分
}

// 游戏状态
export enum GameState {
    LOADING = 'loading',       // 加载中
    MENU = 'menu',            // 主菜单
    PLAYING = 'playing',      // 游戏中
    PAUSED = 'paused',        // 暂停
    RESULT = 'result',        // 结果页
    FINISHED = 'finished'     // 游戏结束
}

// 音频播放状态
export enum AudioState {
    IDLE = 'idle',           // 空闲
    LOADING = 'loading',     // 加载中
    READY = 'ready',         // 准备就绪
    PLAYING = 'playing',     // 播放中
    PAUSED = 'paused',       // 暂停
    STOPPED = 'stopped',     // 停止
    ERROR = 'error'          // 错误
}

// 答题结果
export enum AnswerResult {
    CORRECT = 'correct',     // 正确
    WRONG = 'wrong',         // 错误
    TIMEOUT = 'timeout'      // 超时
}

// 积分规则配置
export const SCORE_CONFIG = {
    // 基础分数
    BASE_SCORE: {
        [GameDifficulty.EASY]: 10,
        [GameDifficulty.MEDIUM]: 20,
        [GameDifficulty.HARD]: 30
    },
    
    // 时间加成（10秒内答题）
    TIME_BONUS: {
        THRESHOLD: 10000,     // 10秒阈值（毫秒）
        MULTIPLIER: 1.5       // 1.5倍加成
    },
    
    // 连答奖励
    COMBO_BONUS: {
        MIN_COMBO: 3,         // 最少连对3题
        BONUS_PER_COMBO: 5    // 每连对一题额外5分
    },
    
    // 完美答题奖励（全对）
    PERFECT_BONUS: 100
};

// 游戏界面配置
export const UI_CONFIG = {
    // 音频播放按钮
    AUDIO_BUTTON: {
        SIZE: 80,             // 按钮大小（px）
        ANIMATION_DURATION: 1000,  // 播放动画时长（毫秒）
        RIPPLE_COUNT: 3       // 同心圆数量
    },
    
    // 答题选项
    ANSWER_OPTIONS: {
        COUNT: 4,             // 选项数量
        HEIGHT: 60,           // 选项高度（px）
        SPACING: 20,          // 选项间距（px）
        ANIMATION_DELAY: 100  // 选项显示延迟（毫秒）
    },
    
    // 颜色系统
    COLORS: {
        PRIMARY: '#C8102E',       // 中国红
        SECONDARY: '#F4A259',     // 暖橙
        SUCCESS: '#28A745',       // 正确答案绿色
        ERROR: '#DC3545',         // 错误答案红色
        BACKGROUND: '#FFF8F0',    // 背景色
        TEXT_PRIMARY: '#2C3E50',  // 主文字颜色
        TEXT_SECONDARY: '#6C757D' // 副文字颜色
    },
    
    // 动画配置
    ANIMATIONS: {
        FADE_DURATION: 300,       // 淡入淡出时间
        SCALE_DURATION: 200,      // 缩放动画时间
        SLIDE_DURATION: 400,      // 滑动动画时间
        BOUNCE_DURATION: 600      // 弹跳动画时间
    }
};

// 音频配置
export const AUDIO_CONFIG = {
    // 音频文件格式
    FORMATS: ['mp3', 'wav', 'ogg'],
    
    // 缓存配置
    CACHE: {
        MAX_SIZE: 50 * 1024 * 1024,  // 最大缓存50MB
        MAX_ITEMS: 100,              // 最大缓存条目数
        EXPIRE_TIME: 24 * 60 * 60 * 1000  // 24小时过期
    },
    
    // 播放配置
    PLAYBACK: {
        VOLUME: 1.0,              // 默认音量
        FADE_DURATION: 500,       // 淡入淡出时间
        MAX_RETRY: 3,             // 最大重试次数
        TIMEOUT: 30000            // 加载超时时间（30秒）
    },
    
    // 预加载配置
    PRELOAD: {
        BATCH_SIZE: 5,            // 批量预加载数量
        CONCURRENT_LIMIT: 3       // 并发加载限制
    }
};

// 游戏规则配置
export const GAME_RULES = {
    // 每局题目数量
    QUESTIONS_PER_GAME: 10,
    
    // 答题时间限制（秒）
    ANSWER_TIME_LIMIT: 30,
    
    // 音频最大播放次数
    MAX_AUDIO_PLAYS: 3,
    
    // 音频播放时长范围（秒）
    AUDIO_DURATION: {
        MIN: 3,
        MAX: 10
    },
    
    // 游戏暂停配置
    PAUSE: {
        AUTO_PAUSE_ON_BLUR: true,     // 失去焦点时自动暂停
        SHOW_PAUSE_BUTTON: true       // 显示暂停按钮
    },
    
    // 数据统计
    ANALYTICS: {
        TRACK_ANSWER_TIME: true,      // 记录答题时间
        TRACK_AUDIO_PLAYS: true,      // 记录音频播放次数
        TRACK_PAUSE_COUNT: true       // 记录暂停次数
    }
};

// 微信小游戏配置
export const WECHAT_CONFIG = {
    // 分享配置
    SHARE: {
        TITLE: '家乡话猜猜猜',
        DESC: '听方言，猜家乡，测测你的方言水平！',
        IMAGE_URL: 'https://your-domain.com/share-image.jpg'
    },
    
    // 广告配置（预留）
    AD: {
        BANNER_ID: '',
        INTERSTITIAL_ID: '',
        REWARDED_VIDEO_ID: ''
    },
    
    // 性能配置
    PERFORMANCE: {
        FPS_TARGET: 60,               // 目标帧率
        MEMORY_WARNING_THRESHOLD: 200, // 内存警告阈值（MB）
        GC_THRESHOLD: 150             // GC触发阈值（MB）
    }
};

// API配置
export const API_CONFIG = {
    // 基础URL（从环境配置获取）
    BASE_URL: getApiBaseUrl(),
    
    // 请求超时时间
    TIMEOUT: 10000,
    
    // 重试配置
    RETRY: {
        MAX_ATTEMPTS: 3,
        DELAY: 1000,
        BACKOFF_FACTOR: 2
    },
    
    // 缓存配置
    CACHE: {
        TTL: 5 * 60 * 1000,          // 5分钟缓存
        MAX_SIZE: 10 * 1024 * 1024   // 10MB缓存
    }
};

// 本地存储键名
export const STORAGE_KEYS = {
    // 用户数据
    USER_PROFILE: 'user_profile',
    USER_SETTINGS: 'user_settings',
    GAME_PROGRESS: 'game_progress',
    
    // 音频缓存
    AUDIO_CACHE: 'audio_cache',
    AUDIO_CACHE_INDEX: 'audio_cache_index',
    
    // 游戏统计
    GAME_STATS: 'game_stats',
    HIGH_SCORES: 'high_scores',
    
    // 系统数据
    APP_VERSION: 'app_version',
    FIRST_LAUNCH: 'first_launch',
    LAST_PLAY_TIME: 'last_play_time'
};

// 错误码定义
export const ERROR_CODES = {
    // 网络错误
    NETWORK_ERROR: 1001,
    REQUEST_TIMEOUT: 1002,
    SERVER_ERROR: 1003,
    
    // 音频错误
    AUDIO_LOAD_FAILED: 2001,
    AUDIO_PLAY_FAILED: 2002,
    AUDIO_FORMAT_UNSUPPORTED: 2003,
    
    // 游戏错误
    GAME_DATA_INVALID: 3001,
    SAVE_FAILED: 3002,
    LOAD_FAILED: 3003,
    
    // 微信相关错误
    WECHAT_AUTH_FAILED: 4001,
    WECHAT_SHARE_FAILED: 4002,
    WECHAT_PAY_FAILED: 4003
};

// 调试配置（从环境配置获取）
export const DEBUG_CONFIG = getDebugConfig();

// 联调测试配置
export const TEST_CONFIG = {
    // 测试服务器地址
    TEST_SERVER_URL: 'http://localhost:3000', // 本地测试服务器
    
    // 是否使用测试数据
    USE_TEST_DATA: true,
    
    // 测试用户信息
    TEST_USER: {
        userId: 'test_user_001',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.png'
    },
    
    // 跳过微信登录（使用测试用户）
    SKIP_WECHAT_LOGIN: false,
    
    // 自动完成游戏流程（用于压力测试）
    AUTO_COMPLETE_GAME: false,
    
    // 测试题目数量
    TEST_QUESTION_COUNT: 3,
    
    // 自动答题延迟（毫秒）
    AUTO_ANSWER_DELAY: 2000
};