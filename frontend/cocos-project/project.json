{"version": "3.8.5", "id": "hometown-dialect-game", "type": "2d", "name": "家乡话猜猜猜", "settings": {"assets": {"path": "./assets", "uuid-map": {}}, "build": {"options": {"wechatgame": {"appid": "", "orientation": "portrait", "separate-engine": false, "subpackages": [], "remoteBundles": []}}}, "physics": {"gravity": {"x": 0, "y": -640}, "allowSleep": true}, "rendering": {"macros": {"CC_USE_VULKAN": false}}}, "scripts": {"start": "npm run dev", "dev": "cocos preview", "build": "cocos build", "build:wechat": "cocos build --platform wechatgame"}, "dependencies": {}, "devDependencies": {"@types/node": "^16.0.0"}}