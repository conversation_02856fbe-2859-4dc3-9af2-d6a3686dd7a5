# 家乡话猜猜猜 - 前端项目

[![Cocos Creator](https://img.shields.io/badge/Cocos%20Creator-3.8.x-green.svg)](https://www.cocos.com/creator)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.x-blue.svg)](https://www.typescriptlang.org/)
[![WeChat Mini Game](https://img.shields.io/badge/WeChat-Mini%20Game-brightgreen.svg)](https://developers.weixin.qq.com/minigame/dev/)

一款基于 Cocos Creator 开发的方言猜谜小游戏，支持微信小游戏平台。通过听方言音频，猜测其含义，测试用户的方言水平。

## 🎮 游戏特色

- **丰富的方言内容**: 支持多地区方言，包括四川话、广东话、上海话等
- **智能音频系统**: 自动缓存、预加载，确保流畅播放体验
- **精美的UI设计**: 中国红配色，文化感十足的界面设计
- **完整的积分系统**: 基础分数、时间加成、连击奖励等多重计分机制
- **社交分享功能**: 支持微信好友分享，展示游戏成绩

## 🛠 技术架构

### 核心技术栈
- **游戏引擎**: Cocos Creator 3.8.x
- **开发语言**: TypeScript
- **目标平台**: 微信小游戏
- **音频格式**: MP3, WAV, OGG
- **性能目标**: 60fps, <3s加载时间, <50MB内存占用

### 系统架构
```
前端架构
├── 管理器层 (Managers)
│   ├── GameManager - 游戏流程控制
│   ├── AudioManager - 音频播放管理
│   ├── DataManager - 数据存储管理
│   ├── EventManager - 事件通信管理
│   └── SceneManager - 场景切换管理
├── UI组件层 (UI Components)
│   ├── GameUI - 主游戏界面
│   ├── AudioButton - 音频播放按钮
│   ├── AnswerOption - 答题选项
│   ├── ProgressBar - 进度显示
│   ├── ScoreDisplay - 分数显示
│   └── ResultUI - 结果页面
├── 工具层 (Utils)
│   ├── StorageManager - 本地存储
│   ├── NetworkManager - 网络请求
│   └── Common utilities
└── 数据层 (Data)
    ├── GameData - 数据模型
    ├── GameConstants - 常量配置
    └── Interfaces - 接口定义
```

## 📁 项目结构

```
frontend/
├── cocos-project/                 # Cocos Creator 项目
│   ├── assets/                    # 游戏资源
│   │   ├── scripts/              # TypeScript 脚本
│   │   │   ├── managers/         # 管理器类
│   │   │   │   ├── GameManager.ts
│   │   │   │   ├── AudioManager.ts
│   │   │   │   ├── DataManager.ts
│   │   │   │   ├── EventManager.ts
│   │   │   │   └── SceneManager.ts
│   │   │   ├── ui/              # UI 组件
│   │   │   │   ├── GameUI.ts
│   │   │   │   ├── AudioButton.ts
│   │   │   │   ├── AnswerOption.ts
│   │   │   │   ├── ProgressBar.ts
│   │   │   │   ├── ScoreDisplay.ts
│   │   │   │   └── ResultUI.ts
│   │   │   ├── data/            # 数据模型
│   │   │   │   └── GameData.ts
│   │   │   ├── constants/       # 常量定义
│   │   │   │   └── GameConstants.ts
│   │   │   └── utils/           # 工具类
│   │   │       ├── StorageManager.ts
│   │   │       └── NetworkManager.ts
│   │   ├── scenes/              # 游戏场景
│   │   ├── prefabs/             # 预制体
│   │   ├── textures/            # 贴图资源
│   │   ├── audio/               # 音频资源
│   │   └── fonts/               # 字体资源
│   ├── project/                 # 项目配置
│   ├── build/                   # 构建输出
│   └── tsconfig.json            # TypeScript 配置
├── wechat-config/               # 微信小游戏配置
│   ├── game.json               # 游戏配置
│   ├── project.config.json     # 项目配置
│   ├── sitemap.json           # 站点地图
│   └── game.js                # 入口文件
├── docs/                       # 开发文档
│   ├── setup-guide.md         # 环境搭建指南
│   ├── coding-standards.md    # 编码规范
│   └── component-api.md       # 组件API文档
├── tests/                      # 测试文件
└── README.md                   # 项目说明
```

## 🚀 快速开始

### 环境准备

1. **安装 Cocos Creator 3.8.x**
   ```bash
   # 下载地址：https://www.cocos.com/creator
   ```

2. **安装微信开发者工具**
   ```bash
   # 下载地址：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   ```

3. **克隆项目**
   ```bash
   git clone https://github.com/your-org/hometown-dialect-game.git
   cd hometown-dialect-game/frontend
   ```

### 开发流程

1. **打开项目**
   - 启动 Cocos Creator
   - 打开 `frontend/cocos-project` 目录

2. **配置构建**
   - 选择菜单：项目 -> 构建发布
   - 发布平台：微信小游戏
   - 点击"构建"

3. **微信开发者工具预览**
   - 打开微信开发者工具
   - 导入项目：`frontend/cocos-project/build/wechatgame`
   - 复制配置文件：`cp wechat-config/* cocos-project/build/wechatgame/`

## 🎯 核心功能

### 1. 游戏流程管理
- **GameManager**: 控制游戏状态转换、题目流程、计分逻辑
- **支持状态**: 加载中、游戏中、暂停、结果页、结束
- **智能计分**: 基础分数 + 时间加成 + 连击奖励

### 2. 音频播放系统
- **AudioManager**: 音频缓存、预加载、播放控制
- **格式支持**: MP3, WAV, OGG
- **缓存策略**: 最大50MB缓存，24小时过期
- **播放限制**: 每题最多播放3次

### 3. 答题交互系统
- **4选1选择**: 直观的选项界面
- **即时反馈**: 正确绿色，错误红色
- **倒计时**: 30秒答题时间限制
- **音频控制**: 大尺寸播放按钮，同心圆扩散动画

### 4. 积分与结果系统
- **多维度计分**: 难度系数、时间加成、连击奖励
- **星级评价**: 1-3星评价系统
- **成就系统**: 完美答题、高分达人、连击高手等
- **社交分享**: 微信好友分享成绩

## 🎨 UI设计规范

### 色彩系统
- **主色调**: 中国红 `#C8102E`
- **辅助色**: 暖橙 `#F4A259`
- **成功色**: 绿色 `#28A745`
- **错误色**: 红色 `#DC3545`
- **背景色**: 米白 `#FFF8F0`

### 动画规范
- **淡入淡出**: 300ms
- **缩放动画**: 200ms
- **滑动动画**: 400ms
- **弹跳动画**: 600ms

### 音频按钮设计
- **尺寸**: 80px 圆形按钮
- **播放动画**: 3个同心圆扩散效果
- **播放次数**: 右上角显示剩余次数
- **状态反馈**: 颜色和缩放变化

## 📊 性能指标

### 性能目标
- **帧率**: 稳定60fps
- **加载时间**: 首屏<3秒
- **内存占用**: <50MB
- **包体大小**: <20MB (不含音频)

### 优化策略
- **音频压缩**: 64kbps MP3格式
- **图片优化**: WebP格式，多级压缩
- **代码分包**: 按功能模块分包加载
- **对象池**: 复用频繁创建的对象

## 🧪 测试策略

### 功能测试
- **核心流程**: 开始游戏、答题、查看结果
- **音频播放**: 各种格式和网络条件下的播放测试
- **数据存储**: 本地数据保存和恢复
- **异常处理**: 网络中断、内存不足等场景

### 兼容性测试
- **设备测试**: iPhone 6+ / Android 5.0+
- **微信版本**: 7.0.0+
- **网络环境**: 2G/3G/4G/WiFi
- **系统版本**: iOS 10+ / Android 5.0+

### 性能测试
- **内存监控**: 长时间游戏内存泄漏检测
- **CPU使用**: 音频播放和动画性能测试
- **网络优化**: 音频加载速度和缓存命中率

## 🔧 开发工具

### 推荐工具
- **IDE**: Visual Studio Code
- **版本控制**: Git
- **调试工具**: Chrome DevTools, 微信开发者工具
- **性能分析**: Cocos Creator 性能面板

### 必要插件
- **TypeScript**: 语法支持和类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **GitLens**: Git 历史查看

## 📝 开发规范

### 编码标准
- 遵循 TypeScript 严格模式
- 使用 ESLint 规则检查
- 私有成员使用下划线前缀
- 完整的类型注解

### 提交规范
```bash
feat: 添加新功能
fix: 修复问题
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建工具或辅助工具的变动
```

### 分支策略
- **main**: 主分支，稳定版本
- **develop**: 开发分支，功能集成
- **feature/xxx**: 功能分支
- **hotfix/xxx**: 紧急修复分支

## 🚀 部署流程

### 开发环境
1. Cocos Creator 构建
2. 微信开发者工具预览
3. 本地调试测试

### 测试环境
1. 构建压缩版本
2. 上传微信测试环境
3. 功能和性能测试

### 生产环境
1. 代码审查和测试通过
2. 构建生产版本
3. 微信小游戏平台发布

## 📈 监控与分析

### 性能监控
- **FPS监控**: 实时帧率显示
- **内存监控**: 内存使用情况跟踪
- **网络监控**: 音频加载成功率
- **错误监控**: 异常情况上报

### 用户分析
- **游戏数据**: 完成率、平均分数、停留时间
- **用户行为**: 音频播放次数、退出节点分析
- **设备分析**: 设备型号、系统版本分布

## 🤝 贡献指南

### 提交代码
1. Fork 项目到个人仓库
2. 创建功能分支 `git checkout -b feature/xxx`
3. 提交更改 `git commit -m "feat: 添加xxx功能"`
4. 推送分支 `git push origin feature/xxx`
5. 创建 Pull Request

### 报告问题
- 使用 GitHub Issues
- 详细描述问题现象
- 提供复现步骤
- 附加截图或日志

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 许可证。

## 👥 开发团队

- **项目负责人**: [姓名](mailto:<EMAIL>)
- **前端开发**: [姓名](mailto:<EMAIL>)
- **UI设计**: [姓名](mailto:<EMAIL>)
- **音频制作**: [姓名](mailto:<EMAIL>)

## 📞 联系我们

- **项目地址**: https://github.com/your-org/hometown-dialect-game
- **问题反馈**: https://github.com/your-org/hometown-dialect-game/issues
- **技术交流**: [微信群二维码]

---

**让我们一起传承和弘扬中华方言文化！** 🎌