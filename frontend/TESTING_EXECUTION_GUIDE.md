# 🧪 微信小游戏真机测试执行指南

## 快速开始

### 1. 准备阶段
```bash
# 进入前端目录
cd /Users/<USER>/Public/GitHub/hometown-dialect-game/frontend

# 执行一键部署脚本
./deploy-test.sh
```

### 2. 微信开发者工具设置
1. **打开项目**: 脚本会自动打开微信开发者工具
2. **项目路径**: `frontend/cocos-project/build/wechat-game`
3. **确认编译**: 检查控制台无报错信息
4. **预览测试**: 确保基本功能正常运行

### 3. 真机调试连接
1. 点击微信开发者工具顶部的 **"真机调试"** 按钮
2. 选择调试类型：推荐选择 **"普通调试"**
3. 使用手机微信扫描生成的二维码
4. 等待真机连接成功

## 详细测试流程

### 阶段一：基础功能验证 ✅
**目标**: 确保游戏在真机环境能正常启动和运行

#### iOS设备测试 (iPhone 12+, iOS 15.0+)
```bash
测试项目：
✅ 游戏启动速度 < 3秒
✅ 首屏渲染正常
✅ 触控响应 < 50ms
✅ 内存初始化 < 50MB
✅ 微信API调用正常
```

#### Android设备测试 (Android 8.0+)
```bash
测试项目：
✅ 游戏启动速度 < 4秒
✅ 首屏渲染正常  
✅ 触控响应 < 100ms
✅ 内存初始化 < 60MB
✅ 垃圾回收机制正常
```

### 阶段二：音频系统验证 🔊
**目标**: 验证音频播放的兼容性和性能

#### 测试步骤
1. **格式兼容性测试**
   ```bash
   支持格式：MP3 ✅, M4A ✅, WAV ⚠️
   播放延迟：< 100ms (iOS), < 150ms (Android)
   缓存策略：智能预加载 + LRU清理
   并发播放：支持单通道播放
   ```

2. **网络音频测试**
   ```bash
   CDN加载：测试腾讯云COS音频加载
   弱网适应：2G/3G/4G/WiFi环境测试
   断网恢复：网络中断后的恢复机制
   缓存命中：> 90%缓存命中率目标
   ```

3. **内存管理测试**
   ```bash
   音频缓存：单个音频 < 2MB内存占用
   内存增长：播放20个音频后内存增长 < 40MB
   垃圾回收：GC后内存释放 > 30%
   内存泄漏：长时间使用后无明显内存泄漏
   ```

### 阶段三：性能压力测试 ⚡
**目标**: 验证游戏在各种性能条件下的稳定性

#### 帧率稳定性测试
```javascript
// 在微信开发者工具控制台执行
if (window.WeChatTestRunner) {
    window.WeChatTestRunner.runSpecificTest('performance')
        .then(result => {
            console.table({
                '平均FPS': result.fps.averageFPS,
                '最低FPS': result.fps.minFPS,
                '帧率稳定性': result.fps.fpsStability,
                '掉帧次数': result.fps.frameDrops
            });
        });
}
```

**性能目标**:
- **iOS**: 平均FPS ≥ 55, 最低FPS ≥ 45
- **Android**: 平均FPS ≥ 50, 最低FPS ≥ 40

#### 内存压力测试
```javascript
// 内存压力测试
async function memoryStressTest() {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    console.log('初始内存:', formatBytes(initialMemory));
    
    // 模拟20轮游戏
    for (let i = 0; i < 20; i++) {
        await simulateGameRound();
        const currentMemory = performance.memory?.usedJSHeapSize || 0;
        console.log(`第${i+1}轮后内存:`, formatBytes(currentMemory));
        
        // 每5轮触发GC
        if (i % 5 === 4 && wx.triggerGC) {
            wx.triggerGC();
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
}
```

### 阶段四：网络环境测试 📡
**目标**: 验证不同网络条件下的用户体验

#### 网络条件矩阵
| 网络类型 | 预期延迟 | 带宽限制 | 测试重点 |
|---------|----------|----------|----------|
| WiFi | < 50ms | 无限制 | 最佳体验验证 |
| 4G | < 100ms | 适中 | 正常使用场景 |
| 3G | < 200ms | 较低 | 弱网适应性 |
| 2G | < 500ms | 很低 | 极限兼容性 |

#### 弱网测试脚本
```javascript
// 在不同网络环境下测试API响应
async function networkAdaptabilityTest() {
    const apiEndpoints = [
        '/api/auth/login',
        '/api/game/questions',
        '/api/game/submit',
        '/api/user/stats'
    ];
    
    for (const endpoint of apiEndpoints) {
        const startTime = Date.now();
        try {
            await fetch(`http://localhost:3001${endpoint}`, {
                method: 'POST',
                timeout: 10000
            });
            const responseTime = Date.now() - startTime;
            console.log(`${endpoint}: ${responseTime}ms`);
        } catch (error) {
            console.error(`${endpoint} 失败:`, error.message);
        }
    }
}
```

### 阶段五：用户体验测试 👆
**目标**: 验证用户交互的流畅性和响应性

#### 触控响应测试
```javascript
// 触控延迟测试
function touchLatencyTest() {
    let touchStartTime;
    
    document.addEventListener('touchstart', () => {
        touchStartTime = performance.now();
    });
    
    document.addEventListener('touchend', () => {
        const latency = performance.now() - touchStartTime;
        console.log('触控延迟:', latency + 'ms');
        
        if (latency > 100) {
            console.warn('触控延迟过高，可能影响用户体验');
        }
    });
}
```

#### 动画流畅度评估
```javascript
// 动画帧率监控
function animationFluidityMonitor() {
    let frameCount = 0;
    let lastTime = performance.now();
    
    function measureFrame() {
        const currentTime = performance.now();
        const deltaTime = currentTime - lastTime;
        
        frameCount++;
        
        if (deltaTime > 20) { // > 50fps阈值
            console.warn(`动画掉帧: ${deltaTime.toFixed(2)}ms`);
        }
        
        lastTime = currentTime;
        requestAnimationFrame(measureFrame);
    }
    
    requestAnimationFrame(measureFrame);
}
```

## 自动化测试执行

### 使用可视化测试工具
1. **访问测试页面**: 在真机上打开 `wechat-test-runner.html`
2. **运行完整测试**: 点击 "🚀 运行完整测试套件"
3. **监控测试进度**: 观察进度条和实时日志
4. **查看测试结果**: 检查综合评分和详细结果
5. **导出测试报告**: 点击 "📋 导出测试报告"

### 测试工具功能说明
```
🔍 兼容性测试 - 平台支持度、API可用性
⚡ 性能测试 - FPS、内存、启动速度
📡 网络测试 - 延迟、带宽、可靠性  
👆 用户体验测试 - 触控、动画、加载体验
```

### 批量设备测试
```bash
# 创建测试设备清单
cat > device_test_matrix.txt << 'EOF'
iPhone 12 Pro, iOS 15.7, 微信8.0.30
iPhone 13, iOS 16.2, 微信8.0.32  
华为P40, Android 10, 微信8.0.30
小米12, Android 12, 微信8.0.32
OPPO Find X3, Android 11, 微信8.0.31
EOF

# 对每台设备执行测试流程
# 1. 连接设备进行真机调试
# 2. 运行自动化测试套件
# 3. 记录测试结果和问题
# 4. 导出测试报告
```

## 测试结果评估

### 评分标准
- **优秀** (90-100分): 所有指标达标，用户体验流畅
- **良好** (80-89分): 主要指标达标，少量优化空间  
- **合格** (70-79分): 基本功能正常，需要性能优化
- **待改进** (<70分): 存在明显问题，需要重点修复

### 关键指标阈值
| 指标类别 | iOS目标 | Android目标 | 警告阈值 |
|---------|---------|-------------|----------|
| FPS稳定性 | ≥55fps | ≥50fps | <45fps |
| 内存使用 | <50MB | <60MB | >80MB |
| 音频延迟 | <100ms | <150ms | >200ms |
| 触控响应 | <50ms | <100ms | >150ms |
| API响应 | <200ms | <300ms | >500ms |

### 问题分类和处理
```
🔴 严重问题 (阻塞发布)
- 游戏崩溃或无法启动
- 核心功能异常
- 严重性能问题

🟡 中等问题 (优化建议)  
- 性能不达标
- 用户体验问题
- 兼容性问题

🟢 轻微问题 (可接受)
- 边缘场景问题
- 非核心功能异常
- 可通过配置解决的问题
```

## 测试报告输出

### 自动生成的测试文件
```
frontend/
├── WECHAT_REALDEVICE_TEST_REPORT.md    # 详细测试方案
├── wechat-test-runner.html              # 可视化测试工具
├── deploy-test.sh                       # 一键部署脚本  
├── deploy-report-YYYYMMDD-HHMMSS.md     # 部署报告
└── test-results-YYYYMMDD-HHMMSS.json    # 测试结果数据
```

### 测试数据导出格式
```json
{
  "testDate": "2025-07-31T10:30:00.000Z",
  "deviceInfo": {
    "platform": "iOS",
    "model": "iPhone 13",
    "system": "iOS 16.2",
    "wechatVersion": "8.0.32"
  },
  "results": {
    "compatibility": { "score": 95 },
    "performance": { "score": 88 },
    "network": { "score": 92 },
    "userExperience": { "score": 90 }
  },
  "summary": {
    "overallScore": 91,
    "readyForRelease": true,
    "recommendations": [...]
  }
}
```

## 故障排除

### 常见问题解决方案

#### 问题1: 音频播放失败
```bash
解决步骤:
1. 检查音频文件格式和编码
2. 验证微信音频API权限
3. 测试网络连接状态
4. 检查内存使用情况
5. 尝试降级到备用音频格式
```

#### 问题2: 性能卡顿
```bash
解决步骤:
1. 分析内存使用峰值
2. 检查资源加载策略
3. 优化渲染调用次数
4. 启用垃圾回收机制
5. 降低画面质量设置
```

#### 问题3: 网络请求超时
```bash
解决步骤:
1. 检查后端服务状态
2. 验证网络连接质量
3. 调整请求超时设置
4. 启用重试机制
5. 使用本地缓存降级
```

## 测试完成检查清单

### ✅ 部署验证
- [ ] 后端服务正常运行 (http://localhost:3001)
- [ ] 前端项目成功构建
- [ ] 微信开发者工具正常打开项目
- [ ] 真机调试连接成功

### ✅ 功能测试
- [ ] 游戏正常启动和加载
- [ ] 音频播放功能正常
- [ ] 用户交互响应正常
- [ ] 网络API调用正常
- [ ] 数据存储功能正常

### ✅ 性能验证
- [ ] FPS达到目标要求
- [ ] 内存使用在合理范围
- [ ] 启动速度符合预期
- [ ] 垃圾回收机制有效
- [ ] 无明显内存泄漏

### ✅ 兼容性确认
- [ ] iOS设备兼容性良好
- [ ] Android设备兼容性良好  
- [ ] 不同微信版本支持正常
- [ ] 各种网络环境适应良好

### ✅ 测试报告
- [ ] 生成完整测试报告
- [ ] 记录所有测试问题
- [ ] 提供优化改进建议
- [ ] 评估发布就绪状态

---

**🎯 测试目标**: 确保家乡话猜猜猜小游戏在微信真机环境下稳定运行，为正式发布做好准备。

**📞 技术支持**: 如遇到测试问题，请参考详细测试方案文档或联系开发团队。