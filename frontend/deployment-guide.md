# 家乡话猜猜猜 - 前端部署指南

## 🏗️ 项目结构概览

```
frontend/
├── cocos-project/                 # Cocos Creator 3.8.x 项目
│   ├── assets/scripts/           # TypeScript 源代码
│   │   ├── constants/           # 配置常量
│   │   │   ├── GameConstants.ts    # 游戏核心常量
│   │   │   └── EnvironmentConfig.ts # 环境配置管理
│   │   ├── managers/            # 核心管理器
│   │   │   ├── GameAPIManager.ts   # 游戏API管理
│   │   │   ├── AudioManager.ts     # 音频管理
│   │   │   └── PerformanceManager.ts # 性能监控
│   │   └── utils/               # 工具类
│   │       ├── NetworkManager.ts   # 网络请求管理
│   │       └── TestRunner.ts      # 联调测试工具
│   └── project/                 # 项目配置
│       └── project.json         # Cocos Creator 项目配置
├── wechat-config/               # 微信小游戏配置
│   ├── game.json               # 小游戏配置
│   └── project.config.json     # 微信开发者工具配置
└── test-runner.html            # 前后端联调测试页面
```

## 🔧 环境配置

### 当前环境状态
- **开发环境**: 已配置完成，API指向 `http://localhost:3001`
- **生产环境**: 环境切换系统已就绪
- **调试模式**: 可通过环境配置动态控制

### 环境切换

项目支持四种环境：
- **Development**: 开发环境 (`http://localhost:3001`)
- **Testing**: 测试环境 (`http://localhost:3001`)  
- **Staging**: 预发布环境 (`https://staging-api.hometown-dialect.com`)
- **Production**: 生产环境 (`https://api.hometown-dialect.com`)

#### 切换环境方法

1. **编译时切换** (推荐生产环境):
   ```typescript
   // 修改 EnvironmentConfig.ts
   export const CURRENT_ENV: Environment = Environment.PRODUCTION;
   ```

2. **运行时切换** (开发/测试环境):
   ```javascript
   // 在浏览器控制台执行
   envSwitcher.switchTo(Environment.STAGING);
   // 然后重载页面
   ```

## 🚀 构建和部署

### Cocos Creator 构建配置

1. **打开 Cocos Creator 3.8.x**
2. **选择构建平台**: 微信小游戏
3. **构建配置**:
   ```json
   {
     "platform": "wechatgame",
     "debug": false,
     "sourceMaps": false,
     "md5Cache": true,
     "nativeMd5": true,
     "optimize": true,
     "inlineSpriteFrames": true,
     "mergeAtlas": true,
     "compressTexture": true,
     "bundle": {
       "commonChunk": true,
       "scriptBundle": true
     }
   }
   ```

### 微信小游戏部署

1. **构建项目**:
   - 在 Cocos Creator 中选择"构建发布"
   - 平台选择"微信小游戏"
   - 点击"构建"

2. **微信开发者工具**:
   - 导入构建后的项目目录
   - 配置 AppID: `wx1234567890abcdef`
   - 预览和真机调试

3. **上传代码**:
   - 在微信开发者工具中点击"上传"
   - 填写版本号和备注
   - 提交审核

## 🔗 API 配置

### 后端 API 路径映射

前端使用的API路径已更新为后端的实际路径：

| 功能 | 前端调用 | 后端路径 | 状态 |
|------|----------|----------|------|
| 获取题目 | `/v1/questions` | `/v1/questions` | ✅ 正常 |
| 微信登录 | `/v1/auth/wechat/login` | `/v1/auth/wechat/login` | ✅ 正常 |
| 创建游戏会话 | `/v1/game-sessions` | `/v1/game-sessions` | ✅ 正常 |
| 提交游戏结果 | `/v1/game/submit` | `/v1/game/submit` | ✅ 正常 |
| 用户统计 | `/v1/user/{userId}/stats` | `/v1/user/{userId}/stats` | ✅ 正常 |

### 联调测试

使用 `test-runner.html` 进行前后端联调测试:

1. **启动后端服务** (端口 3001)
2. **打开测试页面**: `frontend/test-runner.html`
3. **执行测试**: 点击"运行完整测试"按钮
4. **查看结果**: 检查所有API连接状态

## ⚡ 性能优化

### 音频预加载策略

```typescript
// 音频配置优化
export const AUDIO_CONFIG = {
  CACHE: {
    MAX_SIZE: 50 * 1024 * 1024,  // 50MB缓存限制
    MAX_ITEMS: 100,              // 最大缓存音频数
    EXPIRE_TIME: 24 * 60 * 60 * 1000  // 24小时过期
  },
  PRELOAD: {
    BATCH_SIZE: 5,               // 批量预加载数量
    CONCURRENT_LIMIT: 3          // 并发加载限制
  }
};
```

### 性能监控

- **帧率监控**: 目标 60fps，警告 <45fps，严重 <30fps
- **内存监控**: 警告 >200MB，严重 >150MB 触发GC
- **自动优化**: 内存压力时自动清理缓存

### 内存管理

```typescript
// 性能管理器自动处理:
// 1. 定期垃圾回收
// 2. 音频缓存清理  
// 3. 纹理缓存管理
// 4. 内存警告响应
```

## 🔧 开发工具

### 调试工具

在开发环境下，以下调试工具自动可用:

```javascript
// 浏览器控制台可用命令:
testRunner.startIntegrationTest()  // 运行完整联调测试
envSwitcher.switchTo(env)          // 切换环境
runTest.full()                     // 运行完整测试
runTest.network()                  // 测试网络连接
```

### TypeScript 配置

项目使用 TypeScript，配置文件 `tsconfig.json`:
- **Target**: ES2020
- **Module**: ES2020  
- **Strict**: true
- **装饰器支持**: 已启用

## 📦 打包优化

### 资源优化配置

```json
{
  "policies": {
    "autoCompress": {
      "enable": true,
      "type": "jpg", 
      "quality": 80
    },
    "optimization": {
      "enable": true,
      "mergeAtlas": true,
      "bundleCommonChunks": true
    }
  }
}
```

### 分包策略

```json
{
  "subpackages": [
    {
      "name": "audio-assets",
      "root": "assets/audio/"
    }
  ],
  "preloadRule": {
    "pages/index": {
      "network": "all", 
      "packages": ["audio-assets"]
    }
  }
}
```

## 🚨 部署检查清单

### 生产环境准备

- [ ] 环境配置切换到 Production
- [ ] DEBUG_CONFIG.ENABLED 设为 false
- [ ] API_CONFIG.BASE_URL 指向生产服务器
- [ ] 微信 AppID 配置正确
- [ ] 构建配置优化已启用
- [ ] 资源压缩已启用
- [ ] 源码映射已关闭

### 性能验证

- [ ] 内存使用 < 50MB
- [ ] 加载时间 < 3秒
- [ ] 帧率稳定 ≥ 45fps  
- [ ] 网络请求响应 < 500ms
- [ ] 音频播放流畅无卡顿

### 功能测试

- [ ] 微信登录流程正常
- [ ] 题目加载显示正常
- [ ] 音频播放功能正常
- [ ] 答题提交功能正常
- [ ] 分数统计功能正常
- [ ] 分享功能正常

## 🔄 持续集成

### 自动化构建

建议设置 CI/CD 流程:

1. **代码提交** → 触发构建
2. **环境检测** → 自动选择环境配置  
3. **质量检查** → TypeScript 编译 + Lint
4. **构建优化** → 自动压缩和优化
5. **自动部署** → 部署到对应环境

### 版本管理

- **开发版本**: feature branches → develop
- **测试版本**: develop → staging  
- **生产版本**: staging → main → production

## 📞 技术支持

如有部署问题，请检查:

1. **后端服务状态**: 确保后端 API 服务正常运行
2. **网络连接**: 检查前后端网络连通性
3. **配置文件**: 验证所有配置文件参数正确
4. **构建日志**: 查看 Cocos Creator 构建日志
5. **微信开发者工具**: 检查小游戏调试信息

---

*最后更新: 2025-07-31*
*项目状态: 前后端联调完成 ✅*