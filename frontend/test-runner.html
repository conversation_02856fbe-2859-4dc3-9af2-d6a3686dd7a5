<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家乡话猜猜猜 - 前后端联调测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #C8102E, #F4A259);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .status-item {
            flex: 1;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
        }
        .status-unknown { background: #f8f9fa; color: #6c757d; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
        .btn-primary { background: #C8102E; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .logs {
            background: #212529;
            color: #ffffff;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .config {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .config-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .config-label {
            font-weight: 500;
            color: #495057;
        }
        .config-value {
            color: #6c757d;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 家乡话猜猜猜</h1>
            <p>前后端联调测试工具</p>
        </div>

        <div class="config">
            <h3>🔧 当前配置</h3>
            <div class="config-row">
                <span class="config-label">后端API地址:</span>
                <span class="config-value" id="apiUrl">检测中...</span>
            </div>
            <div class="config-row">
                <span class="config-label">调试模式:</span>
                <span class="config-value" id="debugMode">检测中...</span>
            </div>
            <div class="config-row">
                <span class="config-label">测试环境:</span>
                <span class="config-value" id="testEnv">检测中...</span>
            </div>
            <div class="config-row">
                <span class="config-label">网络状态:</span>
                <span class="config-value" id="networkStatus">检测中...</span>
            </div>
        </div>

        <div class="status">
            <div class="status-item status-unknown" id="backendStatus">
                <div>后端连接</div>
                <div>检测中...</div>
            </div>
            <div class="status-item status-unknown" id="authStatus">
                <div>认证状态</div>
                <div>未测试</div>
            </div>
            <div class="status-item status-unknown" id="apiStatus">
                <div>API测试</div>
                <div>未测试</div>
            </div>
            <div class="status-item status-unknown" id="gameStatus">
                <div>游戏流程</div>
                <div>未测试</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="btn btn-secondary" onclick="testBackendConnection()">🌐 测试后端连接</button>
            <button class="btn btn-secondary" onclick="testAuth()">🔐 测试认证</button>
            <button class="btn btn-secondary" onclick="testGameAPI()">🎮 测试游戏API</button>
            <button class="btn btn-secondary" onclick="simulateGameFlow()">🎯 模拟游戏流程</button>
            <button class="btn btn-warning" onclick="clearLogs()">🗑️ 清理日志</button>
            <button class="btn btn-success" onclick="copyLogs()">📋 复制日志</button>
        </div>

        <div class="logs" id="logs">
            <div class="log-entry log-info">🚀 联调测试工具已加载，准备开始测试...</div>
        </div>
    </div>

    <script>
        // 模拟前端配置（实际项目中会从配置文件加载）
        const CONFIG = {
            API_BASE_URL: 'http://localhost:3001',
            DEBUG_ENABLED: true,
            TEST_MODE: true,
            TIMEOUT: 10000
        };

        // 日志管理
        class Logger {
            constructor() {
                this.logs = [];
                this.logsContainer = document.getElementById('logs');
            }

            log(level, message, data = null) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = {
                    level,
                    message,
                    data,
                    timestamp
                };
                
                this.logs.push(logEntry);
                this.displayLog(logEntry);
            }

            displayLog(entry) {
                const logElement = document.createElement('div');
                logElement.className = `log-entry log-${entry.level}`;
                
                let logText = `[${entry.timestamp}] ${entry.message}`;
                if (entry.data) {
                    logText += ` ${JSON.stringify(entry.data)}`;
                }
                
                logElement.textContent = logText;
                this.logsContainer.appendChild(logElement);
                this.logsContainer.scrollTop = this.logsContainer.scrollHeight;
            }

            clear() {
                this.logs = [];
                this.logsContainer.innerHTML = '';
            }

            getLogs() {
                return this.logs.map(log => 
                    `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}${log.data ? ' ' + JSON.stringify(log.data) : ''}`
                ).join('\n');
            }
        }

        const logger = new Logger();

        // 网络请求工具
        async function apiRequest(method, url, data = null) {
            const fullUrl = `${CONFIG.API_BASE_URL}${url}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: CONFIG.TIMEOUT
            };

            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(fullUrl, options);
                const result = await response.json();
                
                logger.log('info', `API请求: ${method} ${url}`, {
                    status: response.status,
                    success: response.ok
                });

                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                logger.log('error', `API请求失败: ${method} ${url}`, {
                    error: error.message
                });
                throw error;
            }
        }

        // 状态更新
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status-item status-${status}`;
            element.innerHTML = `<div>${element.querySelector('div').textContent}</div><div>${message}</div>`;
        }

        // 初始化配置显示
        function initializeConfig() {
            document.getElementById('apiUrl').textContent = CONFIG.API_BASE_URL;
            document.getElementById('debugMode').textContent = CONFIG.DEBUG_ENABLED ? '启用' : '禁用';
            document.getElementById('testEnv').textContent = CONFIG.TEST_MODE ? '测试环境' : '生产环境';
            document.getElementById('networkStatus').textContent = navigator.onLine ? '在线' : '离线';
        }

        // 测试后端连接
        async function testBackendConnection() {
            logger.log('info', '开始测试后端连接...');
            updateStatus('backendStatus', 'warning', '测试中...');

            try {
                const response = await apiRequest('GET', '/v1/questions?count=1&difficulty=easy');
                
                if (response.success) {
                    logger.log('success', '后端连接成功', response.data);
                    updateStatus('backendStatus', 'success', '连接正常');
                    return true;
                } else {
                    logger.log('error', '后端连接失败', { status: response.status });
                    updateStatus('backendStatus', 'error', `连接失败 (${response.status})`);
                    return false;
                }
            } catch (error) {
                logger.log('error', '后端连接异常', { error: error.message });
                updateStatus('backendStatus', 'error', '连接异常');
                return false;
            }
        }

        // 测试认证
        async function testAuth() {
            logger.log('info', '开始测试认证功能...');
            updateStatus('authStatus', 'warning', '测试中...');

            try {
                // 模拟登录
                const loginResponse = await apiRequest('POST', '/v1/auth/wechat/login', {
                    code: 'test_code_001'
                });

                if (loginResponse.success) {
                    logger.log('success', '认证测试成功', loginResponse.data);
                    updateStatus('authStatus', 'success', '认证正常');
                    return true;
                } else {
                    logger.log('error', '认证测试失败', { status: loginResponse.status });
                    updateStatus('authStatus', 'error', '认证失败');
                    return false;
                }
            } catch (error) {
                logger.log('error', '认证测试异常', { error: error.message });
                updateStatus('authStatus', 'error', '认证异常');
                return false;
            }
        }

        // 测试游戏API
        async function testGameAPI() {
            logger.log('info', '开始测试游戏API...');
            updateStatus('apiStatus', 'warning', '测试中...');

            try {
                // 测试获取题目
                const questionsResponse = await apiRequest('GET', '/v1/questions?count=3&difficulty=easy&random=true');
                
                if (questionsResponse.success && questionsResponse.data) {
                    logger.log('success', `获取题目成功，数量: ${questionsResponse.data.questions ? questionsResponse.data.questions.length : 0}`);
                    
                    // 测试创建游戏会话
                    const sessionResponse = await apiRequest('POST', '/v1/game-sessions', {
                        difficulty: 'easy'
                    });
                    
                    if (sessionResponse.success) {
                        logger.log('success', '创建游戏会话成功', { sessionId: sessionResponse.data.sessionId });
                        updateStatus('apiStatus', 'success', 'API正常');
                        return true;
                    }
                }
                
                logger.log('error', 'API测试失败');
                updateStatus('apiStatus', 'error', 'API异常');
                return false;
            } catch (error) {
                logger.log('error', 'API测试异常', { error: error.message });
                updateStatus('apiStatus', 'error', 'API异常');
                return false;
            }
        }

        // 模拟完整游戏流程
        async function simulateGameFlow() {
            logger.log('info', '开始模拟完整游戏流程...');
            updateStatus('gameStatus', 'warning', '测试中...');

            try {
                // 1. 获取题目
                const questionsResponse = await apiRequest('GET', '/v1/questions?count=3&difficulty=easy&random=true');
                if (!questionsResponse.success) {
                    throw new Error('获取题目失败');
                }

                // 2. 创建游戏会话
                const sessionResponse = await apiRequest('POST', '/v1/game-sessions', {
                    difficulty: 'easy'
                });
                if (!sessionResponse.success) {
                    throw new Error('创建游戏会话失败');
                }

                const sessionId = sessionResponse.data.sessionId;
                logger.log('info', `游戏会话创建成功: ${sessionId}`);

                // 3. 模拟答题
                if (questionsResponse.data.questions) {
                    for (let i = 0; i < Math.min(questionsResponse.data.questions.length, 3); i++) {
                        const selectedAnswer = Math.floor(Math.random() * 4);
                        logger.log('info', `模拟答第${i + 1}题，选择答案: ${selectedAnswer}`);
                        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟思考时间
                    }
                }

                // 4. 提交游戏结果
                const submitResponse = await apiRequest('POST', '/v1/game/submit', {
                    sessionId,
                    totalScore: 85,
                    correctCount: 2,
                    answers: [],
                    duration: 30000,
                    difficulty: 'easy'
                });

                if (submitResponse.success) {
                    logger.log('success', '游戏流程模拟完成');
                    updateStatus('gameStatus', 'success', '流程正常');
                    return true;
                } else {
                    throw new Error('提交游戏结果失败');
                }
            } catch (error) {
                logger.log('error', '游戏流程模拟失败', { error: error.message });
                updateStatus('gameStatus', 'error', '流程异常');
                return false;
            }
        }

        // 运行完整测试
        async function runFullTest() {
            logger.log('info', '🚀 开始运行完整的前后端联调测试');
            
            const tests = [
                { name: '后端连接', test: testBackendConnection },
                { name: '认证功能', test: testAuth },
                { name: '游戏API', test: testGameAPI },
                { name: '游戏流程', test: simulateGameFlow }
            ];

            let passedTests = 0;
            const totalTests = tests.length;

            for (const { name, test } of tests) {
                try {
                    const result = await test();
                    if (result) {
                        passedTests++;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 测试间隔
                } catch (error) {
                    logger.log('error', `测试 ${name} 异常`, { error: error.message });
                }
            }

            const successRate = (passedTests / totalTests * 100).toFixed(1);
            
            if (successRate >= 90) {
                logger.log('success', `🎉 联调测试完成！通过率: ${successRate}% (${passedTests}/${totalTests}) - 优秀！`);
            } else if (successRate >= 70) {
                logger.log('warning', `⚠️ 联调测试完成！通过率: ${successRate}% (${passedTests}/${totalTests}) - 良好，建议优化`);
            } else {
                logger.log('error', `❌ 联调测试完成！通过率: ${successRate}% (${passedTests}/${totalTests}) - 需要修复`);
            }
        }

        // 清理日志
        function clearLogs() {
            logger.clear();
            logger.log('info', '日志已清理');
        }

        // 复制日志
        function copyLogs() {
            const logsText = logger.getLogs();
            navigator.clipboard.writeText(logsText).then(() => {
                logger.log('info', '日志已复制到剪贴板');
            }).catch(err => {
                logger.log('error', '复制日志失败', { error: err.message });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeConfig();
            logger.log('info', '前端联调测试工具初始化完成');
            
            // 自动运行基础连接测试
            setTimeout(() => {
                testBackendConnection();
            }, 1000);
        });
    </script>
</body>
</html>