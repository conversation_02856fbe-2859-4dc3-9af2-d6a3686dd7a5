#!/bin/bash

# 微信小游戏真机测试部署脚本
# 家乡话猜猜猜 - Frontend Team

set -e

echo "🎮 家乡话猜猜猜 - 微信小游戏真机测试部署"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="/Users/<USER>/Public/GitHub/hometown-dialect-game"
FRONTEND_PATH="$PROJECT_ROOT/frontend"
COCOS_PROJECT="$FRONTEND_PATH/cocos-project"
BUILD_PATH="$COCOS_PROJECT/build/wechat-game"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 函数：检查必要条件
check_prerequisites() {
    print_info "检查部署前置条件..."
    
    # 检查项目路径
    if [ ! -d "$PROJECT_ROOT" ]; then
        print_error "项目根目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    if [ ! -d "$FRONTEND_PATH" ]; then
        print_error "前端项目目录不存在: $FRONTEND_PATH"
        exit 1
    fi
    
    if [ ! -d "$COCOS_PROJECT" ]; then
        print_error "Cocos项目目录不存在: $COCOS_PROJECT"
        exit 1
    fi
    
    # 检查微信开发者工具
    if ! command -v wechatdevtools &> /dev/null; then
        print_warning "未检测到微信开发者工具CLI，请手动打开微信开发者工具"
    else
        print_success "微信开发者工具CLI已安装"
    fi
    
    print_success "前置条件检查完成"
}

# 函数：启动后端服务
start_backend() {
    print_info "启动后端开发服务..."
    
    cd "$PROJECT_ROOT/backend"
    
    # 检查后端依赖
    if [ ! -d "node_modules" ]; then
        print_info "安装后端依赖..."
        npm install
    fi
    
    # 检查后端服务是否已运行
    if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
        print_success "后端服务已在运行 (http://localhost:3001)"
    else
        print_info "启动后端服务..."
        nohup npm run dev > ../backend.log 2>&1 &
        BACKEND_PID=$!
        
        # 等待服务启动
        print_info "等待后端服务启动..."
        for i in {1..30}; do
            if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
                print_success "后端服务启动成功 (PID: $BACKEND_PID)"
                echo $BACKEND_PID > backend.pid
                break
            fi
            sleep 1
            echo -n "."
        done
        
        if ! curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
            print_error "后端服务启动失败"
            exit 1
        fi
    fi
}

# 函数：准备前端构建
prepare_frontend() {
    print_info "准备前端构建环境..."
    
    cd "$COCOS_PROJECT"
    
    # 确保TypeScript配置正确
    if [ ! -f "tsconfig.json" ]; then
        print_warning "未找到tsconfig.json，创建默认配置..."
        cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2015",
    "module": "ES2015",
    "lib": ["ES2015", "DOM"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  "include": [
    "assets/scripts/**/*"
  ],
  "exclude": [
    "node_modules",
    "build",
    "temp",
    "library"
  ]
}
EOF
    fi
    
    # 检查重要的脚本文件
    local critical_files=(
        "assets/scripts/managers/GameManager.ts"
        "assets/scripts/managers/AudioManager.ts"
        "assets/scripts/managers/PerformanceManager.ts"
        "assets/scripts/test/WeChatRealDeviceTest.ts"
    )
    
    for file in "${critical_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "关键文件缺失: $file"
            exit 1
        fi
    done
    
    print_success "前端环境准备完成"
}

# 函数：构建微信小游戏
build_wechat_game() {
    print_info "构建微信小游戏..."
    
    cd "$COCOS_PROJECT"
    
    # 清理之前的构建
    if [ -d "$BUILD_PATH" ]; then
        print_info "清理之前的构建文件..."
        rm -rf "$BUILD_PATH"
    fi
    
    # 创建构建目录
    mkdir -p "$BUILD_PATH"
    
    print_info "正在构建微信小游戏版本..."
    print_warning "请在Cocos Creator中执行以下步骤:"
    print_warning "1. 打开项目: $COCOS_PROJECT"
    print_warning "2. 菜单 -> 项目 -> 构建发布"
    print_warning "3. 选择平台: 微信小游戏"
    print_warning "4. 设置构建路径: build/wechat-game"
    print_warning "5. 点击构建"
    
    # 等待用户完成构建
    echo ""
    read -p "请在Cocos Creator中完成构建后按Enter继续..." -r
    
    # 验证构建结果
    if [ ! -d "$BUILD_PATH" ] || [ ! -f "$BUILD_PATH/game.js" ]; then
        print_error "构建失败，请检查Cocos Creator构建过程"
        exit 1
    fi
    
    print_success "微信小游戏构建完成"
}

# 函数：复制测试文件
copy_test_files() {
    print_info "复制测试文件到构建目录..."
    
    # 复制测试工具页面
    if [ -f "$FRONTEND_PATH/wechat-test-runner.html" ]; then
        cp "$FRONTEND_PATH/wechat-test-runner.html" "$BUILD_PATH/"
        print_success "测试工具页面已复制"
    fi
    
    # 确保微信配置文件存在
    local config_files=(
        "game.json"
        "project.config.json"
        "sitemap.json"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ ! -f "$BUILD_PATH/$config_file" ]; then
            if [ -f "$FRONTEND_PATH/wechat-config/$config_file" ]; then
                cp "$FRONTEND_PATH/wechat-config/$config_file" "$BUILD_PATH/"
                print_success "配置文件已复制: $config_file"
            else
                print_warning "配置文件缺失: $config_file"
            fi
        fi
    done
}

# 函数：验证构建文件
validate_build() {
    print_info "验证构建文件..."
    
    local required_files=(
        "game.js"
        "game.json"
        "project.config.json"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$BUILD_PATH/$file" ]; then
            print_error "必需文件缺失: $file"
            exit 1
        fi
    done
    
    # 检查文件大小
    local game_js_size=$(stat -f%z "$BUILD_PATH/game.js" 2>/dev/null || stat -c%s "$BUILD_PATH/game.js" 2>/dev/null)
    if [ "$game_js_size" -lt 1000 ]; then
        print_warning "game.js文件可能异常 (大小: ${game_js_size}字节)"
    else
        print_success "game.js文件正常 (大小: ${game_js_size}字节)"
    fi
    
    print_success "构建文件验证完成"
}

# 函数：打开微信开发者工具
open_wechat_devtools() {
    print_info "打开微信开发者工具..."
    
    # 尝试不同的方式打开微信开发者工具
    if command -v wechatdevtools &> /dev/null; then
        # 使用CLI打开
        wechatdevtools --project "$BUILD_PATH"
        print_success "微信开发者工具已通过CLI打开"
    elif [ -d "/Applications/wechatwebdevtools.app" ]; then
        # macOS应用程序打开
        open -a "wechatwebdevtools" "$BUILD_PATH"
        print_success "微信开发者工具已打开"
    else
        print_warning "请手动打开微信开发者工具并导入项目:"
        print_warning "项目路径: $BUILD_PATH"
    fi
}

# 函数：显示测试说明
show_test_instructions() {
    echo ""
    echo "🧪 真机测试说明"
    echo "=============================================="
    echo ""
    print_info "1. 在微信开发者工具中:"
    echo "   - 确保项目正常加载"
    echo "   - 检查编译错误和警告"
    echo "   - 预览项目确保基本功能正常"
    echo ""
    print_info "2. 真机调试:"
    echo "   - 点击工具栏的'真机调试'"
    echo "   - 使用微信扫描二维码"
    echo "   - 在真机上测试游戏功能"
    echo ""
    print_info "3. 性能测试:"
    echo "   - 在真机上访问: wechat-test-runner.html"
    echo "   - 运行完整测试套件"
    echo "   - 导出测试报告"
    echo ""
    print_info "4. 主要测试点:"
    echo "   ✅ 音频播放是否正常"
    echo "   ✅ 触控响应是否流畅"
    echo "   ✅ 内存使用是否稳定"
    echo "   ✅ 网络请求是否正常"
    echo "   ✅ 动画是否流畅"
    echo ""
    print_success "测试完成后请查看测试报告并记录问题"
}

# 函数：生成部署报告
generate_deploy_report() {
    local report_file="$FRONTEND_PATH/deploy-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << 'EOF'
# 微信小游戏真机测试部署报告

## 部署信息
- **部署时间**: $(date)
- **项目版本**: v1.0.0
- **构建平台**: 微信小游戏
- **构建路径**: build/wechat-game

## 部署状态
- [x] 后端服务启动
- [x] 前端项目构建
- [x] 配置文件复制
- [x] 构建文件验证
- [x] 微信开发者工具打开

## 测试检查清单
- [ ] 基础功能测试
- [ ] 音频播放测试
- [ ] 性能监控测试
- [ ] 网络连接测试  
- [ ] 真机兼容性测试
- [ ] 内存使用测试
- [ ] 触控响应测试
- [ ] 动画流畅度测试

## 已知问题
- 无

## 测试结果
- 待测试

## 优化建议
- 待测试后补充

---
**注意**: 请在真机测试完成后更新此报告
EOF
    
    # 替换占位符
    sed -i.bak "s/\$(date)/$(date)/" "$report_file"
    rm "$report_file.bak"
    
    print_success "部署报告已生成: $report_file"
}

# 函数：清理后台进程
cleanup() {
    print_info "清理部署过程..."
    
    # 如果有保存的后端PID，尝试清理
    if [ -f "$PROJECT_ROOT/backend/backend.pid" ]; then
        local backend_pid=$(cat "$PROJECT_ROOT/backend/backend.pid")
        if ps -p $backend_pid > /dev/null 2>&1; then
            print_info "保持后端服务运行 (PID: $backend_pid)"
        else
            rm -f "$PROJECT_ROOT/backend/backend.pid"
        fi
    fi
}

# 主函数
main() {
    echo "开始部署流程..."
    echo ""
    
    # 检查参数
    local skip_build=false
    local skip_backend=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-backend)
                skip_backend=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-build    跳过前端构建步骤"
                echo "  --skip-backend  跳过后端启动步骤"
                echo "  -h, --help      显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_prerequisites
    
    if [ "$skip_backend" = false ]; then
        start_backend
    else
        print_info "跳过后端启动步骤"
    fi
    
    prepare_frontend
    
    if [ "$skip_build" = false ]; then
        build_wechat_game
    else
        print_info "跳过前端构建步骤"
    fi
    
    copy_test_files
    validate_build
    open_wechat_devtools
    show_test_instructions
    generate_deploy_report
    
    echo ""
    print_success "✨ 部署完成！"
    print_info "项目路径: $BUILD_PATH"
    print_info "测试工具: $BUILD_PATH/wechat-test-runner.html"
    print_info "后端API: http://localhost:3001"
    echo ""
    print_warning "请按照上述说明进行真机测试"
    
    # 注册清理函数
    trap cleanup EXIT
}

# 运行主函数
main "$@"