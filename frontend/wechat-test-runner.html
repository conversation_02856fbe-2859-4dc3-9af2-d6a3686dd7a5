<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小游戏真机测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .test-section:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 20px;
            display: flex;
            align-items: center;
        }
        
        .test-section .icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .compatibility .icon { background: #4CAF50; }
        .performance .icon { background: #FF9800; }
        .network .icon { background: #2196F3; }
        .experience .icon { background: #9C27B0; }
        
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .test-button.running {
            background: #ff6b6b;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .device-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .info-card h4 {
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .info-card p {
            color: #666;
            font-size: 13px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ready { background: #4CAF50; }
        .status-running { background: #FF9800; }
        .status-error { background: #f44336; }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .score-display {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            border-radius: 8px;
        }
        
        .score-number {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .score-label {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .recommendations ul {
            list-style: none;
        }
        
        .recommendations li {
            color: #856404;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }
        
        .recommendations li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        
        .log-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .hidden { display: none; }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 20px;
            }
            
            .device-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 家乡话猜猜猜</h1>
            <p>微信小游戏真机测试验证工具</p>
        </div>
        
        <div class="content">
            <!-- 设备信息 -->
            <div class="device-info" id="deviceInfo">
                <div class="info-card">
                    <h4>🔍 检测状态</h4>
                    <p id="detectionStatus">
                        <span class="status-indicator status-ready"></span>
                        正在检测环境...
                    </p>
                </div>
            </div>
            
            <!-- 测试套件 -->
            <div class="test-section compatibility">
                <h3><span class="icon">✓</span>兼容性测试</h3>
                <p>测试平台支持度、音频兼容性、微信API可用性等</p>
                <button class="test-button" onclick="runCompatibilityTest()" id="compatibilityBtn">
                    开始兼容性测试
                </button>
                <div class="results hidden" id="compatibilityResults"></div>
            </div>
            
            <div class="test-section performance">
                <h3><span class="icon">⚡</span>性能测试</h3>
                <p>测试FPS、内存使用、音频延迟、启动速度等性能指标</p>
                <button class="test-button" onclick="runPerformanceTest()" id="performanceBtn">
                    开始性能测试
                </button>
                <div class="results hidden" id="performanceResults"></div>
            </div>
            
            <div class="test-section network">
                <h3><span class="icon">📡</span>网络测试</h3>
                <p>测试网络延迟、带宽适应性、连接可靠性等</p>
                <button class="test-button" onclick="runNetworkTest()" id="networkBtn">
                    开始网络测试
                </button>
                <div class="results hidden" id="networkResults"></div>
            </div>
            
            <div class="test-section experience">
                <h3><span class="icon">👆</span>用户体验测试</h3>
                <p>测试触控响应、动画流畅度、加载体验等</p>
                <button class="test-button" onclick="runExperienceTest()" id="experienceBtn">
                    开始用户体验测试
                </button>
                <div class="results hidden" id="experienceResults"></div>
            </div>
            
            <!-- 综合测试 -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="test-button" onclick="runCompleteTest()" id="completeBtn" 
                        style="font-size: 16px; padding: 15px 30px;">
                    🚀 运行完整测试套件
                </button>
            </div>
            
            <!-- 测试进度 -->
            <div class="progress-bar hidden" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <!-- 综合评分 -->
            <div class="score-display hidden" id="scoreDisplay">
                <div class="score-number" id="overallScore">0</div>
                <div class="score-label">综合评分</div>
            </div>
            
            <!-- 建议和推荐 -->
            <div class="recommendations hidden" id="recommendations">
                <h4>🎯 优化建议</h4>
                <ul id="recommendationsList"></ul>
            </div>
            
            <!-- 实时日志 -->
            <div class="log-output hidden" id="logOutput"></div>
            
            <!-- 导出报告 -->
            <div style="text-align: center; margin-top: 30px;">
                <button class="test-button" onclick="exportReport()" id="exportBtn" disabled>
                    📋 导出测试报告
                </button>
                <button class="test-button" onclick="toggleLog()" id="logBtn">
                    📝 显示/隐藏日志
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testRunner = null;
        let isTestRunning = false;
        let testResults = {};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEnvironment();
        });
        
        // 初始化环境检测
        async function initializeEnvironment() {
            console.log('[TestRunner] 初始化环境检测');
            logMessage('🔍 开始环境检测...');
            
            try {
                // 检测微信环境
                const isWeChat = typeof wx !== 'undefined';
                const isWeChatGame = isWeChat && typeof wx.getSystemInfo === 'function';
                
                if (!isWeChatGame) {
                    updateDetectionStatus('error', '非微信小游戏环境，部分功能可能不可用');
                    logMessage('⚠️  警告: 当前不在微信小游戏环境中');
                } else {
                    updateDetectionStatus('ready', '微信小游戏环境检测成功');
                    logMessage('✅ 微信小游戏环境检测成功');
                    
                    // 获取设备信息
                    wx.getSystemInfo({
                        success: (res) => {
                            displayDeviceInfo(res);
                            logMessage(`📱 设备信息: ${res.model} (${res.system})`);
                        },
                        fail: (error) => {
                            logMessage(`❌ 获取设备信息失败: ${error.errMsg}`);
                        }
                    });
                }
                
                // 检测关键API
                checkAPIAvailability();
                
            } catch (error) {
                console.error('环境检测失败:', error);
                updateDetectionStatus('error', '环境检测失败');
                logMessage(`❌ 环境检测失败: ${error.message}`);
            }
        }
        
        // 更新检测状态
        function updateDetectionStatus(status, message) {
            const statusElement = document.getElementById('detectionStatus');
            const indicator = statusElement.querySelector('.status-indicator');
            
            indicator.className = `status-indicator status-${status}`;
            statusElement.innerHTML = `<span class="status-indicator status-${status}"></span>${message}`;
        }
        
        // 显示设备信息
        function displayDeviceInfo(deviceInfo) {
            const deviceInfoContainer = document.getElementById('deviceInfo');
            
            const infoHTML = `
                <div class="info-card">
                    <h4>📱 设备型号</h4>
                    <p>${deviceInfo.brand} ${deviceInfo.model}</p>
                </div>
                <div class="info-card">
                    <h4>💻 系统版本</h4>
                    <p>${deviceInfo.system}</p>
                </div>
                <div class="info-card">
                    <h4>📱 微信版本</h4>
                    <p>${deviceInfo.version}</p>
                </div>
                <div class="info-card">
                    <h4>🔧 SDK版本</h4>
                    <p>${deviceInfo.SDKVersion}</p>
                </div>
                <div class="info-card">
                    <h4>📺 屏幕信息</h4>
                    <p>${deviceInfo.screenWidth}×${deviceInfo.screenHeight} (${deviceInfo.pixelRatio}x)</p>
                </div>
                <div class="info-card">
                    <h4>🔍 检测状态</h4>
                    <p id="detectionStatus">
                        <span class="status-indicator status-ready"></span>
                        环境检测完成
                    </p>
                </div>
            `;
            
            deviceInfoContainer.innerHTML = infoHTML;
        }
        
        // 检查API可用性
        function checkAPIAvailability() {
            const apis = [
                'getSystemInfo', 'getNetworkType', 'downloadFile',
                'setStorage', 'getStorage', 'login', 'request',
                'onMemoryWarning', 'getPerformance', 'triggerGC'
            ];
            
            if (typeof wx !== 'undefined') {
                const available = apis.filter(api => typeof wx[api] === 'function');
                const unavailable = apis.filter(api => typeof wx[api] !== 'function');
                
                logMessage(`✅ 可用API (${available.length}/${apis.length}): ${available.join(', ')}`);
                
                if (unavailable.length > 0) {
                    logMessage(`⚠️  不可用API: ${unavailable.join(', ')}`);
                }
            }
        }
        
        // 兼容性测试
        async function runCompatibilityTest() {
            if (isTestRunning) return;
            
            setTestRunning('compatibility', true);
            logMessage('🔍 开始兼容性测试...');
            
            try {
                const results = {
                    platform: await testPlatformCompatibility(),
                    audio: await testAudioCompatibility(),
                    storage: await testStorageCompatibility(),
                    network: await testNetworkCompatibility()
                };
                
                displayTestResults('compatibility', results);
                testResults.compatibility = results;
                
                logMessage('✅ 兼容性测试完成');
                
            } catch (error) {
                logMessage(`❌ 兼容性测试失败: ${error.message}`);
                displayTestError('compatibility', error);
            } finally {
                setTestRunning('compatibility', false);
            }
        }
        
        // 平台兼容性测试
        async function testPlatformCompatibility() {
            logMessage('  📱 检测平台兼容性...');
            
            const result = {
                isWeChat: typeof wx !== 'undefined',
                webAudio: typeof AudioContext !== 'undefined',
                canvas: typeof HTMLCanvasElement !== 'undefined',
                webGL: testWebGLSupport(),
                localStorage: testLocalStorage(),
                touch: 'ontouchstart' in window
            };
            
            const score = Object.values(result).filter(Boolean).length * 16.67;
            result.score = Math.round(score);
            
            logMessage(`  ✅ 平台兼容性: ${result.score}/100`);
            return result;
        }
        
        // 音频兼容性测试
        async function testAudioCompatibility() {
            logMessage('  🔊 检测音频兼容性...');
            
            const result = {
                webAudio: typeof AudioContext !== 'undefined',
                htmlAudio: typeof Audio !== 'undefined',
                wechatAudio: typeof wx !== 'undefined' && typeof wx.createInnerAudioContext === 'function',
                downloadSupport: typeof wx !== 'undefined' && typeof wx.downloadFile === 'function',
                formats: {}
            };
            
            // 测试音频格式支持
            if (typeof Audio !== 'undefined') {
                const audio = new Audio();
                const formats = ['mp3', 'wav', 'm4a', 'ogg'];
                
                formats.forEach(format => {
                    const canPlay = audio.canPlayType(`audio/${format}`);
                    result.formats[format] = canPlay !== '';
                });
            }
            
            const compatibilityCount = [
                result.webAudio,
                result.htmlAudio,
                result.wechatAudio,
                result.downloadSupport
            ].filter(Boolean).length;
            
            result.score = Math.round((compatibilityCount / 4) * 100);
            
            logMessage(`  ✅ 音频兼容性: ${result.score}/100`);
            return result;
        }
        
        // 存储兼容性测试
        async function testStorageCompatibility() {
            logMessage('  💾 检测存储兼容性...');
            
            const result = {
                localStorage: false,
                sessionStorage: false,
                wechatStorage: false,
                indexedDB: false
            };
            
            // localStorage测试
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                result.localStorage = true;
            } catch (e) {
                result.localStorage = false;
            }
            
            // sessionStorage测试
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                result.sessionStorage = true;
            } catch (e) {
                result.sessionStorage = false;
            }
            
            // 微信存储测试
            if (typeof wx !== 'undefined' && wx.setStorageSync) {
                try {
                    wx.setStorageSync('test', 'test');
                    wx.removeStorageSync('test');
                    result.wechatStorage = true;
                } catch (e) {
                    result.wechatStorage = false;
                }
            }
            
            // IndexedDB测试
            result.indexedDB = typeof indexedDB !== 'undefined';
            
            const storageCount = Object.values(result).filter(Boolean).length;
            result.score = Math.round((storageCount / 4) * 100);
            
            logMessage(`  ✅ 存储兼容性: ${result.score}/100`);
            return result;
        }
        
        // 网络兼容性测试
        async function testNetworkCompatibility() {
            logMessage('  📡 检测网络兼容性...');
            
            const result = {
                fetch: typeof fetch !== 'undefined',
                xhr: typeof XMLHttpRequest !== 'undefined',
                wechatRequest: typeof wx !== 'undefined' && typeof wx.request === 'function',
                wechatDownload: typeof wx !== 'undefined' && typeof wx.downloadFile === 'function',
                networkType: 'unknown'
            };
            
            // 获取网络类型
            if (typeof wx !== 'undefined' && wx.getNetworkType) {
                try {
                    const networkInfo = await new Promise((resolve, reject) => {
                        wx.getNetworkType({
                            success: resolve,
                            fail: reject
                        });
                    });
                    result.networkType = networkInfo.networkType;
                } catch (e) {
                    result.networkType = 'unknown';
                }
            }
            
            const networkCount = [
                result.fetch,
                result.xhr,
                result.wechatRequest,
                result.wechatDownload
            ].filter(Boolean).length;
            
            result.score = Math.round((networkCount / 4) * 100);
            
            logMessage(`  ✅ 网络兼容性: ${result.score}/100 (${result.networkType})`);
            return result;
        }
        
        // 性能测试
        async function runPerformanceTest() {
            if (isTestRunning) return;
            
            setTestRunning('performance', true);
            logMessage('⚡ 开始性能测试...');
            
            try {
                const results = {
                    memory: await testMemoryPerformance(),
                    rendering: await testRenderingPerformance(),
                    computation: await testComputationPerformance()
                };
                
                displayTestResults('performance', results);
                testResults.performance = results;
                
                logMessage('✅ 性能测试完成');
                
            } catch (error) {
                logMessage(`❌ 性能测试失败: ${error.message}`);
                displayTestError('performance', error);
            } finally {
                setTestRunning('performance', false);
            }
        }
        
        // 内存性能测试
        async function testMemoryPerformance() {
            logMessage('  💾 测试内存性能...');
            
            const initialMemory = getMemoryUsage();
            
            // 创建一些临时数据来测试内存管理
            const testData = [];
            for (let i = 0; i < 1000; i++) {
                testData.push(new Array(1000).fill(Math.random()));
            }
            
            const peakMemory = getMemoryUsage();
            
            // 清理数据
            testData.length = 0;
            
            // 尝试触发GC
            if (typeof wx !== 'undefined' && wx.triggerGC) {
                wx.triggerGC();
                await delay(1000);
            }
            
            const finalMemory = getMemoryUsage();
            
            const result = {
                initial: initialMemory,
                peak: peakMemory,
                final: finalMemory,
                growth: peakMemory - initialMemory,
                gcEffect: peakMemory - finalMemory,
                score: calculateMemoryScore(initialMemory, peakMemory, finalMemory)
            };
            
            logMessage(`  ✅ 内存性能: ${result.score}/100 (峰值: ${formatBytes(result.peak)})`);
            return result;
        }
        
        // 渲染性能测试
        async function testRenderingPerformance() {
            logMessage('  🎨 测试渲染性能...');
            
            const fpsData = [];
            const testDuration = 3000; // 3秒测试
            
            const startTime = performance.now();
            
            while (performance.now() - startTime < testDuration) {
                const frameStart = performance.now();
                
                // 模拟渲染工作
                await requestAnimationFrame(() => {});
                
                const frameTime = performance.now() - frameStart;
                const fps = 1000 / frameTime;
                fpsData.push(fps);
                
                await delay(16); // ~60fps
            }
            
            const avgFPS = fpsData.reduce((sum, fps) => sum + fps, 0) / fpsData.length;
            const minFPS = Math.min(...fpsData);
            const maxFPS = Math.max(...fpsData);
            
            const result = {
                averageFPS: Math.round(avgFPS),
                minFPS: Math.round(minFPS),
                maxFPS: Math.round(maxFPS),
                stability: calculateFPSStability(fpsData),
                score: calculateRenderingScore(avgFPS, minFPS)
            };
            
            logMessage(`  ✅ 渲染性能: ${result.score}/100 (平均FPS: ${result.averageFPS})`);
            return result;
        }
        
        // 计算性能测试
        async function testComputationPerformance() {
            logMessage('  🧮 测试计算性能...');
            
            const startTime = performance.now();
            
            // 执行一些计算密集的操作
            let result = 0;
            for (let i = 0; i < 1000000; i++) {
                result += Math.sin(i) * Math.cos(i);
            }
            
            const computationTime = performance.now() - startTime;
            
            const score = Math.max(0, 100 - (computationTime / 10));
            
            const testResult = {
                computationTime: Math.round(computationTime),
                operationsPerSecond: Math.round(1000000 / (computationTime / 1000)),
                score: Math.round(score)
            };
            
            logMessage(`  ✅ 计算性能: ${testResult.score}/100 (${testResult.computationTime}ms)`);
            return testResult;
        }
        
        // 网络测试
        async function runNetworkTest() {
            if (isTestRunning) return;
            
            setTestRunning('network', true);
            logMessage('📡 开始网络测试...');
            
            try {
                const results = {
                    latency: await testNetworkLatency(),
                    reliability: await testNetworkReliability(),
                    bandwidth: await testNetworkBandwidth()
                };
                
                displayTestResults('network', results);
                testResults.network = results;
                
                logMessage('✅ 网络测试完成');
                
            } catch (error) {
                logMessage(`❌ 网络测试失败: ${error.message}`);
                displayTestError('network', error);
            } finally {
                setTestRunning('network', false);
            }
        }
        
        // 网络延迟测试
        async function testNetworkLatency() {
            logMessage('  ⏱️ 测试网络延迟...');
            
            const latencies = [];
            const testCount = 5;
            
            for (let i = 0; i < testCount; i++) {
                try {
                    const startTime = performance.now();
                    
                    // 尝试ping一个轻量级的请求
                    await fetch('data:text/plain,ping', { method: 'HEAD' });
                    
                    const latency = performance.now() - startTime;
                    latencies.push(latency);
                    
                } catch (error) {
                    // 如果fetch失败，使用备用方案
                    latencies.push(1000); // 假设1000ms延迟
                }
                
                await delay(200);
            }
            
            const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
            const minLatency = Math.min(...latencies);
            const maxLatency = Math.max(...latencies);
            
            const result = {
                average: Math.round(avgLatency),
                min: Math.round(minLatency),
                max: Math.round(maxLatency),
                score: calculateLatencyScore(avgLatency)
            };
            
            logMessage(`  ✅ 网络延迟: ${result.score}/100 (平均: ${result.average}ms)`);
            return result;
        }
        
        // 网络可靠性测试
        async function testNetworkReliability() {
            logMessage('  🔄 测试网络可靠性...');
            
            const testCount = 10;
            let successCount = 0;
            
            for (let i = 0; i < testCount; i++) {
                try {
                    // 简单的可靠性测试
                    await fetch('data:text/plain,test');
                    successCount++;
                } catch (error) {
                    // 请求失败
                }
                
                await delay(100);
            }
            
            const successRate = successCount / testCount;
            const score = Math.round(successRate * 100);
            
            const result = {
                successRate: Math.round(successRate * 100),
                failureCount: testCount - successCount,
                score: score
            };
            
            logMessage(`  ✅ 网络可靠性: ${result.score}/100 (成功率: ${result.successRate}%)`);
            return result;
        }
        
        // 网络带宽测试（简化版）
        async function testNetworkBandwidth() {
            logMessage('  📊 测试网络带宽...');
            
            // 简化的带宽测试
            const result = {
                type: 'unknown',
                estimatedSpeed: 0,
                score: 50 // 默认分数
            };
            
            if (typeof wx !== 'undefined' && wx.getNetworkType) {
                try {
                    const networkInfo = await new Promise((resolve, reject) => {
                        wx.getNetworkType({
                            success: resolve,
                            fail: reject
                        });
                    });
                    
                    result.type = networkInfo.networkType;
                    
                    // 根据网络类型估算得分
                    switch (networkInfo.networkType) {
                        case 'wifi':
                            result.score = 100;
                            result.estimatedSpeed = 'High';
                            break;
                        case '4g':
                            result.score = 80;
                            result.estimatedSpeed = 'Good';
                            break;
                        case '3g':
                            result.score = 60;
                            result.estimatedSpeed = 'Medium';
                            break;
                        case '2g':
                            result.score = 30;
                            result.estimatedSpeed = 'Low';
                            break;
                        default:
                            result.score = 50;
                            result.estimatedSpeed = 'Unknown';
                    }
                } catch (error) {
                    logMessage('  ⚠️ 无法获取网络类型');
                }
            }
            
            logMessage(`  ✅ 网络带宽: ${result.score}/100 (${result.type}: ${result.estimatedSpeed})`);
            return result;
        }
        
        // 用户体验测试
        async function runExperienceTest() {
            if (isTestRunning) return;
            
            setTestRunning('experience', true);
            logMessage('👆 开始用户体验测试...');
            
            try {
                const results = {
                    touch: await testTouchResponse(),
                    animation: await testAnimationSmootliness(),
                    loading: await testLoadingExperience()
                };
                
                displayTestResults('experience', results);
                testResults.userExperience = results;
                
                logMessage('✅ 用户体验测试完成');
                
            } catch (error) {
                logMessage(`❌ 用户体验测试失败: ${error.message}`);
                displayTestError('experience', error);
            } finally {
                setTestRunning('experience', false);
            }
        }
        
        // 触控响应测试
        async function testTouchResponse() {
            logMessage('  👆 测试触控响应...');
            
            const result = {
                supported: 'ontouchstart' in window,
                multiTouch: typeof TouchEvent !== 'undefined',
                score: 0
            };
            
            if (result.supported) {
                result.score += 50;
            }
            
            if (result.multiTouch) {
                result.score += 50;
            }
            
            logMessage(`  ✅ 触控响应: ${result.score}/100`);
            return result;
        }
        
        // 动画流畅度测试
        async function testAnimationSmootliness() {
            logMessage('  🎞️ 测试动画流畅度...');
            
            const frameData = [];
            const testDuration = 2000; // 2秒
            
            const startTime = performance.now();
            let lastFrameTime = startTime;
            
            const measureFrame = () => {
                const currentTime = performance.now();
                const frameTime = currentTime - lastFrameTime;
                frameData.push(frameTime);
                lastFrameTime = currentTime;
                
                if (currentTime - startTime < testDuration) {
                    requestAnimationFrame(measureFrame);
                }
            };
            
            requestAnimationFrame(measureFrame);
            
            // 等待测试完成
            await delay(testDuration + 100);
            
            const avgFrameTime = frameData.reduce((sum, time) => sum + time, 0) / frameData.length;
            const targetFrameTime = 16.67; // 60fps
            
            const smoothnessScore = Math.max(0, 100 - ((avgFrameTime - targetFrameTime) * 2));
            
            const result = {
                averageFrameTime: Math.round(avgFrameTime * 100) / 100,
                estimatedFPS: Math.round(1000 / avgFrameTime),
                smoothnessScore: Math.round(smoothnessScore),
                score: Math.round(smoothnessScore)
            };
            
            logMessage(`  ✅ 动画流畅度: ${result.score}/100 (${result.estimatedFPS} FPS)`);
            return result;
        }
        
        // 加载体验测试
        async function testLoadingExperience() {
            logMessage('  ⏳ 测试加载体验...');
            
            const startTime = performance.now();
            
            // 模拟资源加载
            const loadingSteps = 10;
            for (let i = 0; i < loadingSteps; i++) {
                await delay(50);
            }
            
            const loadingTime = performance.now() - startTime;
            const score = Math.max(0, 100 - (loadingTime / 50));
            
            const result = {
                loadingTime: Math.round(loadingTime),
                score: Math.round(score)
            };
            
            logMessage(`  ✅ 加载体验: ${result.score}/100 (${result.loadingTime}ms)`);
            return result;
        }
        
        // 运行完整测试套件
        async function runCompleteTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            logMessage('🚀 开始完整测试套件...');
            
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.classList.remove('hidden');
            updateProgress(0);
            
            try {
                // 1. 兼容性测试 (25%)
                await runCompatibilityTest();
                updateProgress(25);
                
                // 2. 性能测试 (50%)
                await runPerformanceTest();
                updateProgress(50);
                
                // 3. 网络测试 (75%)
                await runNetworkTest();
                updateProgress(75);
                
                // 4. 用户体验测试 (100%)
                await runExperienceTest();
                updateProgress(100);
                
                // 生成综合报告
                generateSummaryReport();
                
                logMessage('🎉 完整测试套件完成！');
                
                // 启用导出按钮
                document.getElementById('exportBtn').disabled = false;
                
            } catch (error) {
                logMessage(`❌ 完整测试套件失败: ${error.message}`);
            } finally {
                isTestRunning = false;
                
                // 重置按钮状态
                document.querySelectorAll('.test-button').forEach(btn => {
                    btn.classList.remove('running');
                    btn.disabled = false;
                });
            }
        }
        
        // 生成综合报告
        function generateSummaryReport() {
            logMessage('📊 生成综合报告...');
            
            const scores = {
                compatibility: calculateOverallScore(testResults.compatibility),
                performance: calculateOverallScore(testResults.performance),
                network: calculateOverallScore(testResults.network),
                userExperience: calculateOverallScore(testResults.userExperience)
            };
            
            const overallScore = Math.round(
                (scores.compatibility * 0.3 +
                 scores.performance * 0.3 +
                 scores.network * 0.2 +
                 scores.userExperience * 0.2)
            );
            
            // 显示综合评分
            const scoreDisplay = document.getElementById('scoreDisplay');
            const scoreNumber = document.getElementById('overallScore');
            
            scoreDisplay.classList.remove('hidden');
            scoreNumber.textContent = overallScore;
            
            // 生成建议
            const recommendations = generateRecommendations(scores, overallScore);
            displayRecommendations(recommendations);
            
            testResults.summary = {
                overallScore,
                scores,
                recommendations,
                timestamp: new Date().toISOString()
            };
            
            logMessage(`✅ 综合评分: ${overallScore}/100`);
        }
        
        // 计算总体分数
        function calculateOverallScore(categoryResults) {
            if (!categoryResults) return 0;
            
            const scores = Object.values(categoryResults)
                .filter(result => result && typeof result.score === 'number')
                .map(result => result.score);
            
            if (scores.length === 0) return 0;
            
            return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
        }
        
        // 生成建议
        function generateRecommendations(scores, overallScore) {
            const recommendations = [];
            
            if (scores.compatibility < 80) {
                recommendations.push('建议优化平台兼容性，特别是音频播放功能');
            }
            
            if (scores.performance < 70) {
                recommendations.push('性能需要优化，建议减少内存使用并提升帧率');
            }
            
            if (scores.network < 70) {
                recommendations.push('网络适应性需要改善，增强弱网环境体验');
            }
            
            if (scores.userExperience < 70) {
                recommendations.push('用户体验需要提升，优化触控响应和动画流畅度');
            }
            
            if (overallScore >= 85) {
                recommendations.push('整体表现优秀，可以发布到生产环境');
            } else if (overallScore >= 75) {
                recommendations.push('基本达到发布标准，建议进行最后优化');
            } else {
                recommendations.push('需要重点改进后再考虑发布');
            }
            
            return recommendations;
        }
        
        // 显示建议
        function displayRecommendations(recommendations) {
            const recommendationsContainer = document.getElementById('recommendations');
            const recommendationsList = document.getElementById('recommendationsList');
            
            if (recommendations.length > 0) {
                recommendationsList.innerHTML = recommendations
                    .map(rec => `<li>${rec}</li>`)
                    .join('');
                recommendationsContainer.classList.remove('hidden');
            }
        }
        
        // 显示测试结果
        function displayTestResults(testType, results) {
            const resultsContainer = document.getElementById(testType + 'Results');
            
            let html = '<h4>测试结果:</h4>';
            
            Object.entries(results).forEach(([key, value]) => {
                if (typeof value === 'object' && value !== null) {
                    html += `<div><strong>${key}:</strong> 评分 ${value.score || 'N/A'}/100</div>`;
                } else {
                    html += `<div><strong>${key}:</strong> ${value}</div>`;
                }
            });
            
            resultsContainer.innerHTML = html;
            resultsContainer.classList.remove('hidden');
        }
        
        // 显示测试错误
        function displayTestError(testType, error) {
            const resultsContainer = document.getElementById(testType + 'Results');
            resultsContainer.innerHTML = `<div style="color: red;">测试失败: ${error.message}</div>`;
            resultsContainer.classList.remove('hidden');
        }
        
        // 设置测试运行状态
        function setTestRunning(testType, running) {
            const button = document.getElementById(testType + 'Btn');
            
            if (running) {
                button.classList.add('running');
                button.disabled = true;
                button.textContent = '测试中...';
            } else {
                button.classList.remove('running');
                button.disabled = false;
                button.textContent = button.textContent.replace('测试中...', '重新测试');
            }
        }
        
        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
        }
        
        // 记录日志消息
        function logMessage(message) {
            console.log(message);
            
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 切换日志显示
        function toggleLog() {
            const logOutput = document.getElementById('logOutput');
            logOutput.classList.toggle('hidden');
        }
        
        // 导出测试报告
        function exportReport() {
            const report = {
                testDate: new Date().toISOString(),
                deviceInfo: document.getElementById('deviceInfo').textContent,
                results: testResults,
                userAgent: navigator.userAgent
            };
            
            const reportJSON = JSON.stringify(report, null, 2);
            const blob = new Blob([reportJSON], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `wechat-game-test-report-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            logMessage('📋 测试报告已导出');
        }
        
        // 辅助函数
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        function getMemoryUsage() {
            if (typeof wx !== 'undefined' && wx.getPerformance) {
                const performance = wx.getPerformance();
                return performance.usedJSHeapSize || 0;
            }
            
            if (typeof performance !== 'undefined' && performance.memory) {
                return performance.memory.usedJSHeapSize || 0;
            }
            
            return 0;
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function testWebGLSupport() {
            try {
                const canvas = document.createElement('canvas');
                return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            } catch (e) {
                return false;
            }
        }
        
        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return true;
            } catch (e) {
                return false;
            }
        }
        
        function calculateMemoryScore(initial, peak, final) {
            const growth = peak - initial;
            const gcEffect = peak - final;
            
            // 内存增长越少分数越高
            let score = Math.max(0, 100 - (growth / (1024 * 1024))); // 每MB扣1分
            
            // GC效果好加分
            if (gcEffect > growth * 0.5) {
                score += 10;
            }
            
            return Math.min(100, Math.round(score));
        }
        
        function calculateFPSStability(fpsData) {
            const avg = fpsData.reduce((sum, fps) => sum + fps, 0) / fpsData.length;
            const variance = fpsData.reduce((sum, fps) => sum + Math.pow(fps - avg, 2), 0) / fpsData.length;
            return Math.sqrt(variance);
        }
        
        function calculateRenderingScore(avgFPS, minFPS) {
            let score = 0;
            
            // 平均FPS评分
            if (avgFPS >= 55) score += 60;
            else if (avgFPS >= 45) score += 40;
            else if (avgFPS >= 30) score += 20;
            
            // 最低FPS评分
            if (minFPS >= 45) score += 40;
            else if (minFPS >= 30) score += 20;
            else if (minFPS >= 20) score += 10;
            
            return Math.min(100, score);
        }
        
        function calculateLatencyScore(avgLatency) {
            if (avgLatency < 50) return 100;
            if (avgLatency < 100) return 80;
            if (avgLatency < 200) return 60;
            if (avgLatency < 500) return 40;
            return 20;
        }
    </script>
</body>
</html>