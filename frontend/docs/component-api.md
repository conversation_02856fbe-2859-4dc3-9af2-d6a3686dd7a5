# 家乡话猜猜猜 - 组件API文档

## 概述

本文档详细描述了游戏中各个组件的API接口，包括管理器、UI组件和工具类的使用方法。

## 管理器 API

### GameManager

游戏核心管理器，负责游戏流程控制和状态管理。

#### 静态属性

```typescript
static get instance(): GameManager
```
获取单例实例。

#### 公共方法

```typescript
// 开始新游戏
public async startNewGame(difficulty: GameDifficulty = GameDifficulty.MEDIUM): Promise<void>

// 播放当前题目音频
public async playCurrentQuestionAudio(): Promise<void>

// 提交答案
public submitAnswer(selectedAnswer: number): void

// 暂停游戏
public pauseGame(): void

// 恢复游戏
public resumeGame(): void

// 重新开始游戏
public async restartGame(): Promise<void>

// 退出游戏
public async quitGame(): Promise<void>
```

#### 状态查询

```typescript
// 获取当前游戏状态
public getGameState(): GameState

// 获取当前游戏会话
public getCurrentSession(): IGameSession | null

// 获取当前题目信息
public getCurrentQuestionInfo(): { 
    question: IQuestionData | null, 
    index: number, 
    total: number 
}

// 获取当前得分
public getCurrentScore(): number

// 获取当前连击数
public getCurrentCombo(): number

// 是否可以播放音频
public canPlayAudio(): boolean

// 获取音频播放次数
public getAudioPlayCount(): number
```

### AudioManager

音频播放管理器，负责音频的播放、缓存和预加载。

#### 静态属性

```typescript
static get instance(): AudioManager
```

#### 核心方法

```typescript
// 初始化音频管理器
public async initialize(): Promise<void>

// 播放题目音频
public async playQuestionAudio(question: IQuestionData): Promise<void>

// 暂停当前播放
public pauseCurrent(): void

// 恢复播放
public resumeCurrent(): void

// 停止当前播放
public stopCurrent(): void

// 停止所有音频
public stopAll(): void

// 预加载题目音频
public async preloadQuestionAudios(questions: IQuestionData[]): Promise<void>
```

#### 状态管理

```typescript
// 获取音频状态
public getAudioState(url: string): AudioState

// 获取播放次数
public getPlayCount(questionId: string): number

// 重置播放次数
public resetPlayCount(questionId: string): void

// 设置音量
public setVolume(volume: number): void

// 获取当前音量
public getVolume(): number

// 是否正在播放
public isPlaying(): boolean

// 获取当前播放信息
public getCurrentPlayingInfo(): any
```

#### 缓存管理

```typescript
// 获取缓存大小
public getCacheSize(): number

// 清理缓存
public clearCache(): void

// 删除特定缓存
public removeFromCache(url: string): void
```

### DataManager

数据管理器，负责游戏数据的获取、缓存和存储。

#### 静态属性

```typescript
static get instance(): DataManager
```

#### 数据操作

```typescript
// 初始化数据管理器
public async initialize(): Promise<void>

// 获取随机题目
public async getRandomQuestions(count: number, difficulty: GameDifficulty): Promise<IQuestionData[]>

// 保存游戏会话
public async saveGameSession(session: IGameSession): Promise<void>

// 更新用户统计
public async updateUserStats(session: IGameSession): Promise<void>

// 获取用户档案
public getUserProfile(): IUserProfile | null

// 获取游戏设置
public getGameSettings(): IGameSettings | null

// 保存游戏设置
public async saveGameSettings(settings: IGameSettings): Promise<void>
```

### EventManager

事件管理器，负责全局事件的注册、发送和监听。

#### 静态属性

```typescript
static get instance(): EventManager
```

#### 事件操作

```typescript
// 初始化事件管理器
public async initialize(): Promise<void>

// 注册事件监听器
public on(type: string, callback: Function, target?: any): void

// 注册一次性事件监听器
public once(type: string, callback: Function, target?: any): void

// 移除事件监听器
public off(type: string, callback?: Function, target?: any): void

// 发送事件
public emit(type: string, data?: any): void

// 移除目标的所有监听器
public targetOff(target: any): void
```

### SceneManager

场景管理器，负责场景的加载、切换和管理。

#### 方法

```typescript
// 初始化场景管理器
public async initialize(): Promise<void>

// 加载场景
public async loadScene(sceneName: string, onProgress?: (progress: number) => void): Promise<void>

// 预加载场景
public async preloadScene(sceneName: string): Promise<void>

// 获取当前场景名称
public getCurrentSceneName(): string

// 是否正在加载
public isLoading(): boolean

// 清理预加载的场景
public clearPreloadedScenes(): void
```

## UI 组件 API

### GameUI

游戏主界面UI控制器。

#### 属性

```typescript
@property(Label) public questionLabel: Label = null;
@property(Label) public dialectLabel: Label = null;
@property(Label) public questionIndexLabel: Label = null;
@property(Node) public audioButtonContainer: Node = null;
@property(Node) public optionsContainer: Node = null;
@property(Button) public pauseButton: Button = null;
@property(Prefab) public answerOptionPrefab: Prefab = null;
```

#### 方法

```typescript
// 显示题目
public showQuestion(question: IQuestionData, questionIndex: number, totalQuestions: number): void

// 更新分数显示
public updateScore(score: number, combo: number): void
```

### AudioButton

音频播放按钮组件。

#### 属性

```typescript
@property(Button) public button: Button = null;
@property(Sprite) public iconSprite: Sprite = null;
@property(Label) public countLabel: Label = null;
@property(Node) public rippleContainer: Node = null;
@property([Node]) public rippleNodes: Node[] = [];
```

#### 方法

```typescript
// 重置按钮状态
public reset(): void

// 播放开始回调
public onPlayStarted(): void

// 播放完成回调
public onPlayComplete(): void

// 播放错误回调
public onPlayError(): void
```

#### 属性访问器

```typescript
// 是否正在播放
public get isPlaying(): boolean

// 播放次数
public get playCount(): number

// 是否可以播放
public get canPlay(): boolean
```

### AnswerOption

答题选项组件。

#### 属性

```typescript
@property(Button) public button: Button = null;
@property(Label) public optionLabel: Label = null;
@property(Label) public indexLabel: Label = null;
@property(Sprite) public backgroundSprite: Sprite = null;
@property(Node) public feedbackIcon: Node = null;
```

#### 方法

```typescript
// 初始化选项
public initialize(index: number, text: string, onSelectedCallback: (index: number) => void): void

// 设置交互状态
public setInteractable(interactable: boolean): void

// 显示正确状态
public showCorrect(): void

// 显示错误状态
public showWrong(): void

// 显示普通状态
public showNormal(): void
```

#### 属性访问器

```typescript
// 获取选项索引
public get index(): number

// 获取选项文本
public get text(): string

// 是否已选择
public get isSelected(): boolean

// 是否可交互
public get isInteractable(): boolean
```

### ProgressBar

进度条组件。

#### 属性

```typescript
@property(CocosProgressBar) public gameProgressBar: CocosProgressBar = null;
@property(CocosProgressBar) public timeProgressBar: CocosProgressBar = null;
@property(Label) public progressLabel: Label = null;
@property(Label) public timeLabel: Label = null;
```

#### 方法

```typescript
// 设置游戏进度
public setProgress(progress: number): void

// 设置剩余时间
public setTimeLeft(timeLeft: number): void

// 设置最大时间
public setMaxTime(maxTime: number): void

// 重置进度条
public reset(): void

// 播放完成动画
public playCompleteAnimation(): void
```

#### 属性访问器

```typescript
// 获取游戏进度
public get gameProgress(): number

// 获取时间进度
public get timeProgress(): number

// 获取剩余时间
public get timeLeft(): number
```

### ScoreDisplay

分数显示组件。

#### 属性

```typescript
@property(Label) public scoreLabel: Label = null;
@property(Label) public comboLabel: Label = null;
@property(Node) public comboContainer: Node = null;
@property(Label) public scoreChangeLabel: Label = null;
```

#### 方法

```typescript
// 更新分数
public updateScore(newScore: number, newCombo: number): void

// 重置分数显示
public reset(): void

// 播放最终分数动画
public playFinalScoreAnimation(): void

// 设置分数（无动画）
public setScore(score: number): void

// 设置连击数（无动画）
public setCombo(combo: number): void
```

#### 属性访问器

```typescript
// 获取当前分数
public get currentScore(): number

// 获取当前连击数
public get currentCombo(): number

// 是否正在播放动画
public get isAnimating(): boolean
```

### ResultUI

结果页面UI控制器。

#### 属性

```typescript
@property(Label) public titleLabel: Label = null;
@property(Label) public scoreLabel: Label = null;
@property(Label) public accuracyLabel: Label = null;
@property(Label) public correctCountLabel: Label = null;
@property(Label) public comboLabel: Label = null;
@property(Label) public timeLabel: Label = null;
@property(Node) public starsContainer: Node = null;
@property(Node) public achievementContainer: Node = null;
@property(Button) public playAgainButton: Button = null;
@property(Button) public shareButton: Button = null;
@property(Button) public homeButton: Button = null;
@property(Node) public resultPanel: Node = null;
```

#### 方法

```typescript
// 显示游戏结果
public showResult(gameSession: IGameSession): void

// 隐藏结果页面
public hide(): void
```

#### 属性访问器

```typescript
// 获取当前游戏会话
public get gameSession(): IGameSession | null
```

## 工具类 API

### StorageManager

存储管理器，负责本地存储的读取和写入。

#### 方法

```typescript
// 存储数据
public async setItem(key: string, value: any): Promise<void>

// 读取数据
public async getItem<T = any>(key: string): Promise<T | null>

// 删除数据
public async removeItem(key: string): Promise<void>

// 清空所有数据
public async clear(): Promise<void>

// 获取存储大小信息（微信小游戏）
public async getStorageInfo(): Promise<{ keys: string[], currentSize: number, limitSize: number } | null>
```

### NetworkManager

网络管理器，负责与后端API的通信。

#### 方法

```typescript
// 获取题目列表
public async getQuestions(count: number, difficulty: GameDifficulty): Promise<IQuestionData[]>

// 提交游戏结果
public async submitGameResult(sessionId: string, score: number, answers: any[]): Promise<boolean>

// 检查网络状态
public async checkNetworkStatus(): Promise<boolean>
```

## 事件系统

### 游戏事件

```typescript
// 游戏状态事件
'game_state_changed': { previousState: GameState, currentState: GameState, timestamp: number }
'game_started': { sessionId: string, difficulty: GameDifficulty, questionCount: number }
'game_ended': { session: IGameSession, totalScore: number, correctCount: number, accuracy: number, maxCombo: number }
'game_paused': { sessionId: string, currentQuestionIndex: number }
'game_resumed': { sessionId: string, currentQuestionIndex: number }
'game_quit': { sessionId: string }
'game_error': { error: any, context: string, timestamp: number }

// 题目事件
'question_started': { question: IQuestionData, questionIndex: number, totalQuestions: number }
'answer_selected': { selectedAnswer: number, correctAnswer: number, questionId: string }
'answer_submitted': { answerRecord: IAnswerRecord, currentScore: number, currentCombo: number, isCorrect: boolean }
'answer_timeout': { questionId: string }

// 音频事件
'audio_play_started': { questionId: string, audioUrl: string, duration: number, playCount: number }
'audio_play_complete': { questionId: string, audioUrl: string, duration: number, playTime: number }
'audio_play_error': { questionId: string, audioUrl: string, error: any, errorCode: number }
'audio_paused': { questionId: string, audioUrl: string, currentTime: number }
'audio_resumed': { questionId: string, audioUrl: string, currentTime: number }
'audio_stopped': { questionId: string, audioUrl: string }
'audio_preload_progress': { loaded: number, total: number, progress: number }
'audio_load_error': { audioUrl: string, error: any, errorCode: number }

// 场景事件
'scene_load_start': { sceneName: string, previousScene: string }
'scene_loaded': { sceneName: string, loadTime: number }
'scene_load_error': { sceneName: string, error: any }
```

## 常量配置

### GameConstants

```typescript
// 游戏难度等级
enum GameDifficulty {
    EASY = 'easy',
    MEDIUM = 'medium', 
    HARD = 'hard'
}

// 游戏状态
enum GameState {
    LOADING = 'loading',
    MENU = 'menu',
    PLAYING = 'playing',
    PAUSED = 'paused',
    RESULT = 'result',
    FINISHED = 'finished'
}

// 音频播放状态
enum AudioState {
    IDLE = 'idle',
    LOADING = 'loading',
    READY = 'ready',
    PLAYING = 'playing',
    PAUSED = 'paused',
    STOPPED = 'stopped',
    ERROR = 'error'
}

// 答题结果
enum AnswerResult {
    CORRECT = 'correct',
    WRONG = 'wrong',
    TIMEOUT = 'timeout'
}
```

## 使用示例

### 基本游戏流程

```typescript
// 获取游戏管理器
const gameManager = GameManager.instance;

// 开始新游戏
await gameManager.startNewGame(GameDifficulty.MEDIUM);

// 监听游戏事件
EventManager.instance.on('question_started', (event) => {
    console.log('新题目开始:', event.question);
});

// 提交答案
gameManager.submitAnswer(0); // 选择第一个选项
```

### 音频播放

```typescript
// 获取音频管理器
const audioManager = AudioManager.instance;

// 播放当前题目音频
await audioManager.playQuestionAudio(currentQuestion);

// 监听播放完成
EventManager.instance.on('audio_play_complete', (event) => {
    console.log('音频播放完成:', event.questionId);
});
```

### 数据存储

```typescript
// 保存用户设置
const settings: IGameSettings = {
    soundEnabled: true,
    musicEnabled: true,
    soundVolume: 1.0,
    // ...
};

await DataManager.instance.saveGameSettings(settings);
```

---

*此文档描述了主要组件的API接口。如需了解具体实现细节，请参考源码或联系开发团队。*