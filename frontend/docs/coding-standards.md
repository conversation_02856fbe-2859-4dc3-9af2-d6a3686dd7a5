# 家乡话猜猜猜 - 前端编码规范

## 概述

本文档规定了家乡话猜猜猜项目前端开发的编码规范，旨在提高代码质量、可维护性和团队协作效率。

## TypeScript 编码规范

### 1. 命名规范

#### 1.1 文件命名
- 使用 PascalCase: `GameManager.ts`, `AudioButton.ts`
- 接口文件使用 I 前缀: `IGameData.ts`
- 常量文件使用全大写: `GameConstants.ts`

#### 1.2 类命名
```typescript
// ✅ 正确 - PascalCase
export class GameManager extends Component {
    // ...
}

// ❌ 错误
export class gameManager {
    // ...
}
```

#### 1.3 方法和变量命名
```typescript
// ✅ 正确 - camelCase
private _currentScore: number = 0;
public updateScore(newScore: number): void {
    // ...
}

// ❌ 错误
private _current_score: number = 0;
public UpdateScore(new_score: number): void {
    // ...
}
```

#### 1.4 常量命名
```typescript
// ✅ 正确 - SCREAMING_SNAKE_CASE
export const MAX_AUDIO_PLAYS = 3;
export const DEFAULT_VOLUME = 1.0;

// ❌ 错误
export const maxAudioPlays = 3;
```

#### 1.5 私有成员前缀
```typescript
// ✅ 正确 - 私有成员使用下划线前缀
private _gameManager: GameManager = null;
private _isPlaying: boolean = false;

// 公共成员不使用前缀
public scoreLabel: Label = null;
```

### 2. 类型定义

#### 2.1 接口定义
```typescript
// ✅ 正确 - 接口使用 I 前缀
export interface IQuestionData {
    id: string;
    audioUrl: string;
    difficulty: GameDifficulty;
    options: string[];
    correctAnswer: number;
}

// 函数接口
export interface IOnSelectedCallback {
    (index: number): void;
}
```

#### 2.2 枚举定义
```typescript
// ✅ 正确 - 枚举使用 PascalCase
export enum GameState {
    LOADING = 'loading',
    PLAYING = 'playing',
    PAUSED = 'paused',
    FINISHED = 'finished'
}
```

#### 2.3 类型注解
```typescript
// ✅ 正确 - 明确的类型注解
private _audioCache: Map<string, AudioClip> = new Map();
private _currentQuestion: IQuestionData | null = null;

// 函数参数和返回值类型
public async loadAudio(url: string): Promise<AudioClip | null> {
    // ...
}
```

### 3. 代码组织

#### 3.1 Import 顺序
```typescript
// 1. Cocos Creator 核心模块
import { _decorator, Component, Node, Label } from 'cc';

// 2. 项目内部模块 - 管理器
import { GameManager } from '../managers/GameManager';
import { AudioManager } from '../managers/AudioManager';

// 3. 项目内部模块 - 其他
import { IQuestionData } from '../data/GameData';
import { UI_CONFIG } from '../constants/GameConstants';

const { ccclass, property } = _decorator;
```

#### 3.2 类成员顺序
```typescript
@ccclass('GameUI')
export class GameUI extends Component {
    // 1. 公共属性 (@property 装饰器)
    @property(Label)
    public questionLabel: Label = null;
    
    // 2. 静态成员
    private static _instance: GameUI = null;
    
    // 3. 私有属性
    private _currentQuestion: IQuestionData = null;
    private _isAnswering: boolean = false;
    
    // 4. 生命周期方法
    protected onLoad(): void {
        // ...
    }
    
    protected onDestroy(): void {
        // ...
    }
    
    // 5. 公共方法
    public showQuestion(question: IQuestionData): void {
        // ...
    }
    
    // 6. 私有方法
    private initializeComponents(): void {
        // ...
    }
    
    // 7. 事件处理方法
    private onButtonClick(): void {
        // ...
    }
    
    // 8. 属性访问器
    public get currentQuestion(): IQuestionData | null {
        return this._currentQuestion;
    }
}
```

### 4. 注释规范

#### 4.1 类注释
```typescript
/**
 * 游戏核心管理器
 * 负责游戏流程控制、状态管理、数据协调
 */
@ccclass('GameManager')
export class GameManager extends Component {
    // ...
}
```

#### 4.2 方法注释
```typescript
/**
 * 播放题目音频
 * @param question 题目数据
 * @returns Promise<void>
 */
public async playQuestionAudio(question: IQuestionData): Promise<void> {
    // ...
}
```

#### 4.3 复杂逻辑注释
```typescript
// 计算得分：基础分数 + 时间加成 + 连击奖励
let score = SCORE_CONFIG.BASE_SCORE[question.difficulty];

// 时间加成：10秒内答题获得1.5倍加成
if (answerTime <= SCORE_CONFIG.TIME_BONUS.THRESHOLD) {
    score *= SCORE_CONFIG.TIME_BONUS.MULTIPLIER;
}
```

## Cocos Creator 特定规范

### 1. 组件定义

#### 1.1 装饰器使用
```typescript
// ✅ 正确
const { ccclass, property } = _decorator;

@ccclass('AudioButton')
export class AudioButton extends Component {
    @property(Button)
    public button: Button = null;
    
    @property({ type: Label, tooltip: '分数显示标签' })
    public scoreLabel: Label = null;
}
```

#### 1.2 属性引用
```typescript
// ✅ 正确 - 在 onLoad 中获取组件引用
protected onLoad(): void {
    if (!this.button) {
        this.button = this.node.getComponent(Button);
    }
}

// ❌ 错误 - 在构造函数中获取引用
constructor() {
    super();
    this.button = this.node.getComponent(Button); // 此时节点可能未初始化
}
```

### 2. 生命周期方法

```typescript
// ✅ 正确的生命周期方法顺序和实现
export class GameUI extends Component {
    protected onLoad(): void {
        // 初始化组件引用
        this.initializeComponents();
        // 注册事件监听器
        this.registerEventListeners();
    }
    
    protected onEnable(): void {
        // 组件激活时的操作
    }
    
    protected start(): void {
        // 第一次更新前的初始化
    }
    
    protected onDisable(): void {
        // 组件禁用时的清理
    }
    
    protected onDestroy(): void {
        // 清理资源和事件监听器
        this.unregisterEventListeners();
    }
}
```

### 3. 事件处理

```typescript
// ✅ 正确的事件注册和清理
private registerEventListeners(): void {
    if (this.button) {
        this.button.node.on(Button.EventType.CLICK, this.onButtonClick, this);
    }
    
    if (this._eventManager) {
        this._eventManager.on('game_started', this.onGameStarted, this);
    }
}

private unregisterEventListeners(): void {
    if (this.button) {
        this.button.node.off(Button.EventType.CLICK, this.onButtonClick, this);
    }
    
    if (this._eventManager) {
        this._eventManager.targetOff(this);
    }
}
```

## 性能优化规范

### 1. 对象池使用

```typescript
// ✅ 正确 - 频繁创建的对象使用对象池
export class EffectManager {
    private _particlePool: cc.NodePool = new cc.NodePool();
    
    public showEffect(position: Vec3): void {
        let effectNode = this._particlePool.get();
        if (!effectNode) {
            effectNode = instantiate(this.effectPrefab);
        }
        // 使用效果节点...
    }
    
    public recycleEffect(effectNode: Node): void {
        this._particlePool.put(effectNode);
    }
}
```

### 2. 定时器管理

```typescript
// ✅ 正确 - 及时清理定时器
export class GameTimer {
    private _timerId: number = 0;
    
    public startTimer(): void {
        this.stopTimer(); // 确保之前的定时器已清理
        
        this._timerId = setInterval(() => {
            // 定时器逻辑
        }, 1000);
    }
    
    public stopTimer(): void {
        if (this._timerId) {
            clearInterval(this._timerId);
            this._timerId = 0;
        }
    }
    
    protected onDestroy(): void {
        this.stopTimer(); // 组件销毁时清理定时器
    }
}
```

### 3. 内存管理

```typescript
// ✅ 正确 - 及时释放资源引用
export class ResourceManager {
    private _textureCache: Map<string, Texture2D> = new Map();
    
    public releaseTexture(url: string): void {
        const texture = this._textureCache.get(url);
        if (texture) {
            texture.destroy();
            this._textureCache.delete(url);
        }
    }
    
    public clear(): void {
        this._textureCache.forEach(texture => texture.destroy());
        this._textureCache.clear();
    }
}
```

## 错误处理规范

### 1. 异常捕获

```typescript
// ✅ 正确 - 完整的错误处理
public async loadAudio(url: string): Promise<AudioClip | null> {
    try {
        const audioClip = await this.doLoadAudio(url);
        return audioClip;
    } catch (error) {
        console.error(`[AudioManager] 加载音频失败: ${url}`, error);
        
        // 发送错误事件
        this._eventManager?.emit('audio_load_error', {
            url: url,
            error: error,
            errorCode: ERROR_CODES.AUDIO_LOAD_FAILED
        });
        
        return null;
    }
}
```

### 2. 参数验证

```typescript
// ✅ 正确 - 参数验证
public updateScore(score: number): void {
    if (typeof score !== 'number' || score < 0) {
        console.warn('[ScoreDisplay] 无效的分数值:', score);
        return;
    }
    
    this._currentScore = score;
    this.updateDisplay();
}
```

## 代码格式化

### 1. 缩进和空格
- 使用 4 个空格缩进
- 操作符前后加空格
- 逗号后加空格

### 2. 换行规则
```typescript
// ✅ 正确的换行
const config = {
    volume: 1.0,
    autoPlay: true,
    loop: false
};

// 长参数列表换行
public initialize(
    gameManager: GameManager,
    audioManager: AudioManager,
    eventManager: EventManager
): void {
    // ...
}
```

### 3. 大括号规则
```typescript
// ✅ 正确 - 大括号不换行
if (condition) {
    // ...
} else {
    // ...
}

// 函数大括号不换行
public method(): void {
    // ...
}
```

## 工具配置

### 1. TSConfig 配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "strictNullChecks": true
  }
}
```

### 2. ESLint 配置（推荐）
```json
{
  "extends": ["@typescript-eslint/recommended"],
  "rules": {
    "@typescript-eslint/naming-convention": [
      "error",
      {
        "selector": "private",
        "format": ["camelCase"],
        "leadingUnderscore": "require"
      }
    ]
  }
}
```

## 检查清单

开发完成后，请检查以下项目：

- [ ] 所有公共方法都有类型注解
- [ ] 私有成员使用下划线前缀
- [ ] 事件监听器已正确注册和清理
- [ ] 定时器和资源已在组件销毁时清理
- [ ] 异常情况有适当的错误处理
- [ ] 代码格式符合规范
- [ ] 注释清晰且有意义

---

*遵循这些规范将有助于提高代码质量和团队协作效率。如有疑问，请参考现有代码或与团队讨论。*