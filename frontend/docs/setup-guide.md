# 家乡话猜猜猜 - 前端开发环境搭建指南

## 项目概述

家乡话猜猜猜是一款基于 Cocos Creator 3.8.x 开发的微信小游戏，采用 TypeScript 编写，具有完整的音频播放、答题交互、积分系统等功能。

## 技术栈

- **游戏引擎**: Cocos Creator 3.8.x
- **开发语言**: TypeScript
- **目标平台**: 微信小游戏
- **音频支持**: mp3, wav, ogg
- **UI框架**: Cocos Creator UI 系统

## 环境要求

### 系统要求
- macOS 10.14+ / Windows 10+ / Ubuntu 18.04+
- Node.js 14.x 或更高版本
- Python 3.7+ (部分构建工具需要)

### 开发工具
- **Cocos Creator 3.8.x**: [下载地址](https://www.cocos.com/creator)
- **微信开发者工具**: [下载地址](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- **VS Code** (推荐): TypeScript 开发环境

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/hometown-dialect-game.git
cd hometown-dialect-game/frontend
```

### 2. 安装 Cocos Creator

1. 下载并安装 Cocos Creator 3.8.x
2. 启动 Cocos Creator
3. 选择"打开项目"
4. 导航到 `frontend/cocos-project` 目录并打开

### 3. 配置项目

#### 3.1 项目设置
- 打开 Cocos Creator
- 在项目设置中确认：
  - 目标平台：微信小游戏
  - TypeScript 编译目标：ES2020
  - 启用严格模式

#### 3.2 构建设置
- 选择菜单：项目 -> 构建发布
- 发布平台：微信小游戏
- 构建路径：`build/wechatgame`

### 4. 微信开发者工具配置

1. 打开微信开发者工具
2. 导入项目：
   - 项目目录：`frontend/cocos-project/build/wechatgame`
   - AppID：使用测试号或你的小游戏 AppID
3. 复制微信小游戏配置文件：
   ```bash
   cp wechat-config/* cocos-project/build/wechatgame/
   ```

## 项目结构

```
frontend/
├── cocos-project/                 # Cocos Creator 项目主目录
│   ├── assets/                    # 游戏资源
│   │   ├── scripts/              # TypeScript 脚本
│   │   │   ├── managers/         # 管理器类
│   │   │   ├── components/       # 游戏组件
│   │   │   ├── ui/              # UI 组件
│   │   │   ├── data/            # 数据模型
│   │   │   ├── utils/           # 工具类
│   │   │   └── constants/       # 常量定义
│   │   ├── scenes/              # 游戏场景
│   │   ├── prefabs/             # 预制体
│   │   ├── textures/            # 贴图资源
│   │   ├── audio/               # 音频资源
│   │   └── fonts/               # 字体资源
│   ├── project/                 # 项目配置
│   ├── build/                   # 构建输出
│   └── tsconfig.json            # TypeScript 配置
├── wechat-config/               # 微信小游戏配置
│   ├── game.json               # 游戏配置
│   ├── project.config.json     # 项目配置
│   ├── sitemap.json           # 站点地图
│   └── game.js                # 入口文件
├── docs/                       # 开发文档
└── tests/                      # 测试文件
```

## 核心架构

### 管理器系统

- **GameManager**: 游戏核心管理器，控制游戏流程
- **AudioManager**: 音频播放管理，支持缓存和预加载
- **DataManager**: 数据管理，处理本地存储和网络请求
- **EventManager**: 事件系统，全局事件通信
- **SceneManager**: 场景管理，负责场景切换

### UI 组件系统

- **GameUI**: 游戏主界面控制器
- **AudioButton**: 音频播放按钮组件
- **AnswerOption**: 答题选项组件
- **ProgressBar**: 进度条组件
- **ScoreDisplay**: 分数显示组件
- **ResultUI**: 结果页面组件

## 开发流程

### 1. 场景搭建

1. 在 Cocos Creator 中创建场景
2. 添加 UI 节点和组件
3. 配置节点属性和引用关系
4. 绑定脚本组件

### 2. 脚本开发

1. 创建 TypeScript 脚本文件
2. 继承 Component 或使用纯类
3. 实现业务逻辑
4. 添加事件监听和处理

### 3. 资源管理

1. 将音频文件放入 `assets/audio/` 目录
2. 图片资源放入 `assets/textures/` 目录
3. 配置资源属性（压缩、格式等）
4. 在代码中通过 resources 加载

### 4. 构建测试

1. 在 Cocos Creator 中构建项目
2. 在微信开发者工具中预览测试
3. 检查性能和兼容性
4. 修复问题并重新构建

## 调试技巧

### 1. 控制台调试

```typescript
// 开启调试模式
import { DEBUG_CONFIG } from '../constants/GameConstants';

if (DEBUG_CONFIG.ENABLED) {
    console.log('[Debug] 调试信息');
}
```

### 2. 性能监控

```typescript
// 监控内存使用
if (DEBUG_CONFIG.SHOW_MEMORY) {
    this.schedule(() => {
        console.log('[Performance] 内存使用:', cc.sys.getMemoryInfo());
    }, 5.0);
}
```

### 3. 网络调试

- 在微信开发者工具中开启"不校验合法域名"
- 使用 Network 面板查看请求
- 检查音频加载状态

## 构建发布

### 1. 开发构建

```bash
# 在 Cocos Creator 中
# 项目 -> 构建发布 -> 微信小游戏 -> 构建
```

### 2. 生产构建

1. 开启代码混淆和压缩
2. 优化资源大小
3. 配置子包加载
4. 测试性能和兼容性

### 3. 上传发布

1. 在微信开发者工具中上传代码
2. 在微信公众平台配置版本信息
3. 提交审核
4. 发布上线

## 常见问题

### Q: 音频播放失败
A: 检查音频格式支持、文件路径、网络权限等

### Q: 构建失败
A: 检查 TypeScript 语法错误、资源引用路径、版本兼容性等

### Q: 性能问题
A: 优化资源大小、减少 draw call、使用对象池等

### Q: 微信API调用失败
A: 检查 AppID 配置、权限设置、API 版本等

## 技术支持

- Cocos Creator 文档: https://docs.cocos.com/creator/3.8/
- 微信小游戏文档: https://developers.weixin.qq.com/minigame/dev/
- TypeScript 文档: https://www.typescriptlang.org/docs/

## 版本历史

- v1.0.0: 基础版本，包含核心答题功能
- v1.1.0: 添加音频缓存和性能优化
- v1.2.0: 完善UI动画和用户体验

---

*最后更新: 2024-01-20*