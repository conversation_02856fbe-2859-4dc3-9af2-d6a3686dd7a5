# 前后端联调测试指南

## 📋 概览

本文档描述了"家乡话猜猜猜"小游戏的前后端联调测试流程和配置。

## 🎯 测试目标

### 1. API接口对接验证
- ✅ API路径匹配（前端 `/v1/*` ↔ 后端 `/v1/*`）
- ✅ 请求响应格式统一
- ✅ 错误处理机制
- ✅ JWT Token管理

### 2. 微信登录流程
- ✅ 微信小游戏登录集成
- ✅ Token自动刷新
- ✅ 登录状态管理
- ✅ 用户信息同步

### 3. 游戏业务流程
- ✅ 题目数据获取
- ✅ 游戏会话管理
- ✅ 答题结果提交
- ✅ 用户统计数据

### 4. 错误处理和用户体验
- ✅ 网络异常处理
- ✅ 离线模式支持
- ✅ 用户友好的错误提示
- ✅ 重试机制

## 🔧 配置说明

### 调试配置 (`GameConstants.ts`)

```typescript
export const DEBUG_CONFIG = {
    ENABLED: true,                    // 启用调试模式
    LOG_LEVEL: 'debug',              // 日志级别
    API_DEBUG: true,                 // API调试
    SHOW_DETAILED_ERRORS: true,      // 显示详细错误
    SIMULATE_NETWORK_DELAY: 0,       // 模拟网络延迟
    SIMULATE_NETWORK_ERROR_RATE: 0   // 模拟网络错误率
};

export const TEST_CONFIG = {
    TEST_SERVER_URL: 'http://localhost:3000',  // 测试服务器地址
    USE_TEST_DATA: true,                       // 使用测试数据
    SKIP_WECHAT_LOGIN: false,                 // 跳过微信登录
    AUTO_COMPLETE_GAME: false,                // 自动完成游戏
    TEST_QUESTION_COUNT: 3,                   // 测试题目数量
    AUTO_ANSWER_DELAY: 2000                   // 自动答题延迟
};
```

### API配置

```typescript
export const API_CONFIG = {
    BASE_URL: 'https://dev-api.hometown-dialect.com',  // 开发环境API地址
    TIMEOUT: 10000,                                    // 请求超时时间
    RETRY: {
        MAX_ATTEMPTS: 3,                               // 最大重试次数
        DELAY: 1000,                                   // 重试延迟
        BACKOFF_FACTOR: 2                              // 退避因子
    }
};
```

## 🚀 快速开始

### 1. 启动后端服务器

```bash
# 确保后端服务器在 localhost:3000 运行
# 或修改 TEST_CONFIG.TEST_SERVER_URL 为实际地址
```

### 2. 配置前端环境

```bash
# 1. 确保 DEBUG_CONFIG.ENABLED = true
# 2. 根据需要调整其他配置项
# 3. 启动Cocos Creator项目
```

### 3. 运行联调测试

#### 方法一：自动测试
```javascript
// 在浏览器控制台中执行
testRunner.startIntegrationTest();  // 运行完整联调测试
```

#### 方法二：分步测试
```javascript
// 测试网络连接
testRunner.runSpecificTest('network');

// 测试微信登录
testRunner.runSpecificTest('login');

// 测试游戏API
testRunner.runSpecificTest('api');

// 测试错误处理
testRunner.runSpecificTest('error');
```

#### 方法三：模拟游戏流程
```javascript
// 模拟完整游戏流程
testRunner.simulateGameFlow();
```

## 📊 测试用例

### 1. 网络连接测试
- [x] 检查网络状态
- [x] 测试服务器连接
- [x] 验证Token状态
- [x] 测试API响应格式

### 2. 微信登录测试
- [x] 获取微信登录code
- [x] 调用后端登录接口
- [x] Token存储和管理
- [x] 用户信息同步

### 3. 游戏API测试
- [x] 获取题目数据
- [x] 创建游戏会话
- [x] 提交答题结果
- [x] 完成游戏提交

### 4. 错误处理测试
- [x] 网络超时处理
- [x] 认证失败处理
- [x] 服务器错误处理
- [x] 用户友好错误提示

### 5. 离线模式测试
- [x] 离线检测
- [x] 本地数据使用
- [x] 网络恢复同步
- [x] 用户体验保证

## 🔍 调试工具

### 全局调试对象
```javascript
// 在浏览器控制台中可访问的调试对象
window.testUtils          // 测试工具
window.testRunner         // 测试运行器
window.networkManager     // 网络管理器
window.gameAPIManager     // 游戏API管理器
window.gameManager        // 游戏管理器
window.errorHandler       // 错误处理器
```

### 测试命令
```javascript
// 网络管理器测试
networkManager.checkNetworkStatus();
networkManager.pingServer();
networkManager.isLoggedIn();

// 游戏API管理器测试
gameAPIManager.login();
gameAPIManager.getQuestions(5, 'easy');
gameAPIManager.getCurrentUser();

// 错误处理器测试
errorHandler.handleError(new Error('测试错误'));
errorHandler.getErrorLogs();
```

## 📈 测试报告

### 测试统计
- 总测试数
- 成功数量
- 失败数量
- 警告数量
- 成功率

### 性能指标
- API响应时间
- 网络延迟
- 内存使用情况
- 帧率表现

### 错误日志
- 错误类型统计
- 错误发生时间
- 错误上下文信息
- 解决方案建议

## 🛠 故障排除

### 常见问题

#### 1. 网络连接失败
```
问题：无法连接到后端服务器
解决：
1. 检查后端服务器是否启动
2. 确认API_CONFIG.BASE_URL配置正确
3. 检查网络防火墙设置
4. 查看浏览器控制台CORS错误
```

#### 2. 微信登录失败
```
问题：微信登录无法获取code
解决：
1. 确认运行环境（微信开发者工具 or 真机）
2. 检查小程序配置
3. 设置TEST_CONFIG.SKIP_WECHAT_LOGIN = true使用模拟登录
```

#### 3. API接口调用失败
```
问题：API返回404或500错误
解决：
1. 检查API路径是否正确
2. 确认请求参数格式
3. 检查Token是否有效
4. 查看后端服务器日志
```

#### 4. 响应格式不匹配
```
问题：前端无法解析后端响应
解决：
1. 检查IBackendResponse接口定义
2. 确认后端返回标准格式：{code, message, data, timestamp, requestId}
3. 查看NetworkManager中的响应处理逻辑
```

### 调试技巧

#### 1. 启用详细日志
```typescript
DEBUG_CONFIG.LOG_LEVEL = 'debug';
DEBUG_CONFIG.API_DEBUG = true;
DEBUG_CONFIG.SHOW_DETAILED_ERRORS = true;
```

#### 2. 模拟网络条件
```typescript
DEBUG_CONFIG.SIMULATE_NETWORK_DELAY = 2000;      // 模拟2秒延迟
DEBUG_CONFIG.SIMULATE_NETWORK_ERROR_RATE = 0.1;  // 10%错误率
```

#### 3. 使用测试数据
```typescript
TEST_CONFIG.USE_TEST_DATA = true;
TEST_CONFIG.SKIP_WECHAT_LOGIN = true;  // 使用模拟用户
```

## 📝 测试清单

### 开发阶段测试
- [ ] 基本网络连接
- [ ] API接口响应
- [ ] 错误处理机制
- [ ] 本地数据存储

### 集成测试阶段
- [ ] 完整登录流程
- [ ] 游戏业务流程
- [ ] 数据同步机制
- [ ] 离线模式功能

### 发布前测试
- [ ] 真机测试
- [ ] 网络异常测试
- [ ] 性能压力测试
- [ ] 用户体验测试

## 🔄 持续集成

### 自动化测试
```javascript
// 在CI/CD流水线中可以调用
const testResults = await testRunner.startIntegrationTest();
if (testResults.successRate < 90) {
    throw new Error('集成测试未通过');
}
```

### 测试数据管理
```javascript
// 每次测试前清理数据
testRunner.clearTestData();

// 使用独立的测试数据库
TEST_CONFIG.USE_TEST_DATA = true;
```

## 📞 技术支持

### 联系方式
- 项目协调者：负责整体测试协调
- 前端开发：处理前端相关问题
- 后端开发：处理API和服务器问题
- QA测试：提供测试用例和验证

### 文档更新
本文档会随着功能开发不断更新，请关注最新版本。

---

**注意**：在生产环境中请确保关闭所有调试功能：
```typescript
DEBUG_CONFIG.ENABLED = false;
TEST_CONFIG.USE_TEST_DATA = false;
```