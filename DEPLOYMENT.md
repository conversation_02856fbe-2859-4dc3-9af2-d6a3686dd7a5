# 家乡话猜猜猜 - 项目部署指南

## 📋 项目概览

**家乡话猜猜猜** 是一款基于微信小游戏的方言猜测游戏，采用前后端分离架构。

- **前端**: Cocos Creator 3.8.x + TypeScript (微信小游戏)
- **后端**: Node.js + Serverless Framework (腾讯云SCF)
- **数据库**: MySQL + Redis (腾讯云)
- **存储**: 腾讯云COS (音频文件CDN)

## 🏗️ 项目架构

```
hometown-dialect-game/
├── frontend/                   # 前端微信小游戏
│   ├── cocos-project/         # Cocos Creator项目
│   ├── wechat-config/         # 微信小游戏配置
│   └── deployment-guide.md    # 前端部署指南
├── backend/                   # 后端Serverless API
│   ├── serverless/           # Lambda函数
│   ├── database/            # 数据库迁移脚本
│   ├── serverless.yml       # Serverless配置
│   └── .env                 # 环境变量
├── content/                  # 音频内容管理
└── docs/                    # 项目文档
```

## 🚀 部署环境要求

### 开发环境
- Node.js 18.15+
- MySQL 8.0+
- Redis 6.0+
- Cocos Creator 3.8.x
- 微信开发者工具

### 生产环境
- 腾讯云账号
- 微信小程序账号
- 域名和SSL证书（可选）

---

## 🎮 前端部署 (微信小游戏)

### 开发环境部署

#### 1. 环境准备
```bash
# 进入前端目录
cd frontend/

# 确保Cocos Creator 3.8.x已安装
# 下载地址: https://www.cocos.com/creator
```

#### 2. 项目配置
```bash
# 打开Cocos Creator
# 文件 → 打开项目 → 选择 frontend/cocos-project/
```

#### 3. 环境配置
```typescript
// 编辑 frontend/cocos-project/assets/scripts/constants/EnvironmentConfig.ts
export const CURRENT_ENV: Environment = Environment.DEVELOPMENT;

// 确保开发配置正确
export const ENV_CONFIGS = {
  [Environment.DEVELOPMENT]: {
    API_BASE_URL: 'http://localhost:3001/v1',
    DEBUG_ENABLED: true,
    LOG_LEVEL: 'debug'
  }
};
```

#### 4. 本地调试
```bash
# 在Cocos Creator中点击"预览"按钮
# 选择浏览器预览，确保功能正常
```

### 生产环境部署

#### 1. 环境配置切换
```typescript
// 修改 frontend/cocos-project/assets/scripts/constants/EnvironmentConfig.ts
export const CURRENT_ENV: Environment = Environment.PRODUCTION;

// 生产环境配置
export const ENV_CONFIGS = {
  [Environment.PRODUCTION]: {
    API_BASE_URL: 'https://api.hometown-dialect.com/v1',  // 你的生产域名
    DEBUG_ENABLED: false,
    LOG_LEVEL: 'error'
  }
};
```

#### 2. 微信小游戏构建
```bash
# 在Cocos Creator中:
# 1. 菜单栏 → 项目 → 构建发布
# 2. 选择平台: 微信小游戏
# 3. 构建配置:
```

**构建配置选项:**
- ✅ **调试模式**: 关闭
- ✅ **Source Maps**: 关闭  
- ✅ **MD5缓存**: 开启
- ✅ **压缩纹理**: 开启
- ✅ **内联图集**: 开启
- ✅ **合并图集**: 开启
- ✅ **脚本包**: 开启

#### 3. 微信开发者工具发布
```bash
# 1. 打开微信开发者工具
# 2. 导入 → 选择构建后的目录
# 3. 配置项目:
#    - AppID: 你的微信小程序AppID
#    - 项目名称: 家乡话猜猜猜
#    - 开发模式: 小游戏
```

#### 4. 上传发布
1. **预览测试**: 在微信开发者工具中预览，确保功能正常
2. **真机调试**: 使用手机微信扫码测试
3. **上传代码**: 点击"上传"按钮，填写版本号和更新说明
4. **提交审核**: 在微信公众平台提交审核

### 前端部署检查清单

- [ ] 环境配置已切换到生产环境
- [ ] API_BASE_URL指向生产服务器
- [ ] DEBUG_ENABLED设为false
- [ ] 构建配置优化已启用
- [ ] 微信AppID配置正确
- [ ] 预览测试功能正常
- [ ] 真机测试通过
- [ ] 资源加载时间<3秒
- [ ] 内存使用<50MB

---

## 🖥️ 后端部署 (腾讯云Serverless)

### 开发环境部署

#### 1. 环境准备
```bash
# 进入后端目录
cd backend/

# 安装依赖
npm install

# 安装全局工具
npm install -g serverless
```

#### 2. 数据库环境搭建
```bash
# 安装MySQL (macOS)
brew install mysql
brew services start mysql

# 安装Redis
brew install redis
brew services start redis

# 创建数据库
mysql -u root -e "CREATE DATABASE hometown_dialect;"
```

#### 3. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**开发环境 (.env) 配置:**
```bash
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=hometown_dialect

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置 (开发环境)
JWT_ACCESS_SECRET=dev_access_secret_at_least_64_chars_long_for_security
JWT_REFRESH_SECRET=dev_refresh_secret_at_least_64_chars_long_for_security

# 微信配置 (开发测试)
WECHAT_APP_ID=test_app_id
WECHAT_APP_SECRET=test_app_secret
```

#### 4. 数据库初始化
```bash
# 初始化数据库表结构
npm run db:init

# 验证数据库连接
npm run db:migrate
```

#### 5. 启动开发服务器
```bash
# 启动开发服务器
npm run dev

# 服务将在 http://localhost:3001 启动
# 健康检查: http://localhost:3001/health
```

### 生产环境部署

#### 1. 腾讯云准备
```bash
# 开通腾讯云服务:
# - 云函数 SCF
# - API网关
# - 云数据库 MySQL
# - 云数据库 Redis
# - 对象存储 COS
```

#### 2. 生产环境变量配置
```bash
# 创建生产环境配置
cp .env.example .env.prod
```

**生产环境 (.env.prod) 配置:**
```bash
NODE_ENV=production

# 数据库配置 (腾讯云MySQL)
DB_HOST=cdb-xxxxxxx.tencentcdb.com
DB_PORT=3306
DB_USER=your_prod_user
DB_PASSWORD=your_secure_prod_password
DB_NAME=hometown_dialect_prod

# Redis配置 (腾讯云Redis)
REDIS_HOST=redis-xxxxxxx.tencentcloudapi.com
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置 (生产环境 - 必须使用强密码)
JWT_ACCESS_SECRET=your_super_secure_64_char_access_secret_for_production_use
JWT_REFRESH_SECRET=your_super_secure_64_char_refresh_secret_for_production_use

# 微信小程序配置 (真实配置)
WECHAT_APP_ID=wx1234567890abcdef
WECHAT_APP_SECRET=your_real_wechat_app_secret

# 腾讯云COS配置
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_BUCKET=hometown-dialect-audio
COS_REGION=ap-beijing
COS_CDN_DOMAIN=cdn.hometown-dialect.com

# VPC配置 (生产环境)
VPC_ID=vpc-xxxxxxxx
SUBNET_ID=subnet-xxxxxxxx

# SSL证书 (HTTPS域名)
SSL_CERT_ARN=arn:tencentcloud:ssl:region:account:cert/cert-id
```

#### 3. 腾讯云Serverless配置
```bash
# 配置腾讯云密钥
sls config credentials \
  --provider tencent \
  --key your_secret_id \
  --secret your_secret_key
```

#### 4. 数据库生产环境初始化
```bash
# 使用生产环境配置
export $(cat .env.prod | xargs)

# 初始化生产数据库
npm run db:init

# 填充基础数据
npm run db:seed
```

#### 5. 部署到生产环境
```bash
# 构建项目
npm run build

# 部署到生产环境
npm run deploy:prod

# 等同于: serverless deploy --stage prod --config .env.prod
```

#### 6. 域名配置（可选）
```bash
# 在腾讯云API网关控制台:
# 1. 创建自定义域名: api.hometown-dialect.com
# 2. 绑定SSL证书
# 3. 配置路径映射: / → 默认环境
# 4. 设置CNAME解析
```

### 后端部署检查清单

- [ ] 腾讯云服务已开通
- [ ] 生产环境变量配置完整
- [ ] 数据库连接测试通过
- [ ] Redis缓存连接正常
- [ ] JWT密钥足够安全
- [ ] 微信配置正确
- [ ] COS存储配置正常
- [ ] Serverless部署成功
- [ ] API健康检查通过
- [ ] 域名解析配置正确

---

## 🔗 完整部署流程

### 一键部署脚本

#### 后端一键部署脚本
```bash
# 创建部署脚本 backend/deploy-production.sh
#!/bin/bash
set -e

echo "🚀 开始后端生产环境部署..."

# 1. 环境检查
echo "📋 检查环境依赖..."
node --version
npm --version
sls --version

# 2. 安装依赖
echo "📦 安装项目依赖..."
npm ci

# 3. 运行测试
echo "🧪 运行测试套件..."
npm run test

# 4. 构建项目
echo "🔨 构建项目..."
npm run build

# 5. 数据库迁移
echo "🗄️ 执行数据库迁移..."
npm run db:migrate

# 6. 部署到生产环境
echo "☁️ 部署到腾讯云..."
npm run deploy:prod

# 7. 健康检查
echo "🏥 执行健康检查..."
sleep 10
curl -f https://api.hometown-dialect.com/health || exit 1

echo "✅ 后端部署完成！"
```

#### 前端一键构建脚本
```bash
# 创建构建脚本 frontend/build-production.sh
#!/bin/bash
set -e

echo "🎮 开始前端生产环境构建..."

# 1. 检查Cocos Creator路径
COCOS_CREATOR_PATH="/Applications/CocosCreator/Creator/3.8.0/CocosCreator.app/Contents/MacOS/CocosCreator"

if [ ! -f "$COCOS_CREATOR_PATH" ]; then
    echo "❌ 未找到Cocos Creator，请确保已安装3.8.x版本"
    exit 1
fi

# 2. 切换到生产环境
echo "🔄 切换到生产环境配置..."
sed -i '' 's/Environment.DEVELOPMENT/Environment.PRODUCTION/g' cocos-project/assets/scripts/constants/EnvironmentConfig.ts

# 3. 构建项目
echo "🔨 构建微信小游戏..."
$COCOS_CREATOR_PATH --project cocos-project/ --build "configPath=build-templates/wechatgame"

# 4. 复制微信配置
echo "📱 复制微信小游戏配置..."
cp -r wechat-config/* build/wechatgame/

echo "✅ 前端构建完成！构建文件位于: build/wechatgame/"
echo "📱 请使用微信开发者工具打开构建目录进行发布"
```

### 部署执行顺序

#### 1. 首次部署
```bash
# 1. 后端部署
cd backend/
chmod +x deploy-production.sh
./deploy-production.sh

# 2. 前端构建
cd ../frontend/
chmod +x build-production.sh
./build-production.sh

# 3. 微信开发者工具发布
# 手动在微信开发者工具中完成发布流程
```

#### 2. 日常更新部署
```bash
# 仅更新后端
cd backend/ && ./deploy-production.sh

# 仅更新前端
cd frontend/ && ./build-production.sh
```

---

## 📊 监控和维护

### 性能监控指标

#### 后端API性能
- **响应时间**: < 200ms (目标 < 100ms)
- **成功率**: > 99.9%
- **并发支持**: 1000+ TPS
- **内存使用**: < 256MB per function
- **数据库连接**: < 50ms

#### 前端性能
- **加载时间**: < 3秒 (首次) / < 1秒 (后续)
- **帧率**: ≥ 45fps (目标 60fps)
- **内存使用**: < 50MB (iOS) / < 60MB (Android)
- **音频延迟**: < 100ms

### 成本控制

#### 目标成本: < $300/月 (10K DAU)

**成本构成:**
- **云函数调用**: ~$80/月
- **数据库**: ~$60/月 (MySQL + Redis)
- **CDN流量**: ~$40/月
- **存储空间**: ~$20/月
- **其他服务**: ~$100/月

**成本优化建议:**
1. 启用云函数预置并发，减少冷启动
2. 优化数据库查询，使用适当索引
3. 启用CDN缓存，降低回源流量
4. 定期清理过期数据和日志

### 监控告警设置

```bash
# 腾讯云监控告警配置:
# 1. API响应时间 > 500ms
# 2. 错误率 > 1%
# 3. 数据库连接 > 80%
# 4. 内存使用 > 80%
# 5. 月度费用 > $250
```

---

## 🛠️ 故障排查

### 常见问题及解决方案

#### 1. 前端问题

**问题**: 微信小游戏加载失败
```bash
# 检查项:
# 1. 网络连接是否正常
# 2. API域名是否可访问
# 3. AppID配置是否正确
# 4. 构建配置是否正确

# 解决方案:
# 1. 检查微信开发者工具控制台错误信息
# 2. 验证API接口连通性
# 3. 重新构建并上传
```

**问题**: 音频播放失败
```bash
# 检查项:
# 1. 音频文件格式是否支持
# 2. CDN域名是否配置正确
# 3. 音频文件是否存在

# 解决方案:
# 1. 检查音频文件URL可访问性
# 2. 验证COS配置
# 3. 查看浏览器网络请求
```

#### 2. 后端问题

**问题**: API请求超时
```bash
# 检查命令:
curl -I https://api.hometown-dialect.com/health

# 排查步骤:
# 1. 检查云函数状态
# 2. 查看云函数日志
# 3. 检查数据库连接
# 4. 验证VPC网络配置
```

**问题**: 数据库连接失败
```bash
# 检查命令:
mysql -h your-host -u user -p -e "SELECT 1"

# 排查步骤:
# 1. 检查数据库实例状态
# 2. 验证连接参数
# 3. 检查安全组配置
# 4. 查看数据库慢查询日志
```

#### 3. 部署问题

**问题**: Serverless部署失败
```bash
# 常见原因:
# 1. 权限不足
# 2. 配置错误
# 3. 依赖包问题
# 4. 网络问题

# 解决方案:
sls deploy --debug  # 开启调试模式
```

### 日志查看

#### 后端日志
```bash
# 查看云函数日志
sls logs -f api --tail

# 查看特定时间段日志
sls logs -f api --startTime 2024-01-01 --endTime 2024-01-02
```

#### 前端日志
```bash
# 微信开发者工具控制台
# 1. 打开调试器
# 2. 查看Console面板
# 3. 检查Network面板
```

---

## 📞 技术支持

### 联系信息
- **项目维护**: 开发团队
- **紧急联系**: 技术负责人
- **文档更新**: 定期更新

### 支持渠道
1. **GitHub Issues**: 技术问题和功能建议
2. **内部文档**: 详细技术文档
3. **团队群组**: 日常技术交流

### 更新记录
- **2025-08-01**: 初始版本，包含完整部署流程
- **版本**: v1.0.0
- **维护者**: 全体开发团队

---

## 🎯 快速开始

### 开发环境（5分钟快速启动）
```bash
# 1. 克隆项目
git clone <repository-url>
cd hometown-dialect-game

# 2. 后端启动
cd backend/
npm install
cp .env.example .env
npm run setup  # 一键环境搭建
npm run dev

# 3. 前端启动
cd ../frontend/
# 使用Cocos Creator打开cocos-project/目录
```

### 生产部署（一键部署）
```bash
# 1. 配置生产环境变量
# 2. 执行一键部署
cd backend/ && ./deploy-production.sh
cd ../frontend/ && ./build-production.sh
# 3. 微信开发者工具发布
```

**部署完成！你的"家乡话猜猜猜"项目现已成功部署！** 🎉

---

*最后更新时间: 2025-08-01*  
*文档版本: v1.0.0*  
*项目状态: 生产就绪 ✅*