/**
 * 围观功能数据库查询优化器
 * 
 * 优化围观相关的数据库查询，包括索引优化、查询缓存、读写分离、分表分库等
 * 目标：查询响应时间<50ms
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const mysql = require('mysql2/promise');
const Redis = require('redis');

class WatchDBOptimizer {
    constructor(options = {}) {
        // 数据库配置
        this.dbConfig = {
            master: {
                host: options.masterHost || 'localhost',
                port: options.masterPort || 3306,
                user: options.user || 'root',
                password: options.password || '',
                database: options.database || 'hometown_dialect',
                connectionLimit: options.connectionLimit || 20,
                acquireTimeout: 60000,
                timeout: 60000
            },
            slaves: options.slaves || [
                {
                    host: options.slaveHost || 'localhost',
                    port: options.slavePort || 3307,
                    user: options.user || 'root',
                    password: options.password || '',
                    database: options.database || 'hometown_dialect',
                    connectionLimit: options.slaveConnectionLimit || 30
                }
            ]
        };
        
        // Redis缓存配置
        this.redisConfig = {
            host: options.redisHost || 'localhost',
            port: options.redisPort || 6379,
            password: options.redisPassword || '',
            db: options.redisDb || 0,
            keyPrefix: 'watch:',
            defaultTTL: options.cacheTTL || 300 // 5分钟
        };
        
        // 连接池
        this.masterPool = null;
        this.slavePools = [];
        this.redisClient = null;
        
        // 查询缓存
        this.queryCache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            sets: 0
        };
        
        // 性能监控
        this.performanceStats = {
            queryTimes: [],
            slowQueries: [],
            maxSamples: 1000
        };
        
        // 分表配置
        this.shardingConfig = {
            roomShards: 10,    // 房间表分片数
            messageShards: 20, // 消息表分片数
            userShards: 5      // 用户表分片数
        };
        
        this.initialize();
    }
    
    /**
     * 初始化数据库优化器
     */
    async initialize() {
        console.log('初始化数据库优化器...');
        
        try {
            // 创建主库连接池
            this.masterPool = mysql.createPool(this.dbConfig.master);
            
            // 创建从库连接池
            for (const slaveConfig of this.dbConfig.slaves) {
                const slavePool = mysql.createPool(slaveConfig);
                this.slavePools.push(slavePool);
            }
            
            // 创建Redis连接
            this.redisClient = Redis.createClient(this.redisConfig);
            await this.redisClient.connect();
            
            // 创建索引
            await this.createOptimizedIndexes();
            
            // 启动性能监控
            this.startPerformanceMonitoring();
            
            console.log('数据库优化器初始化完成');
            
        } catch (error) {
            console.error('初始化数据库优化器失败:', error);
            throw error;
        }
    }
    
    /**
     * 创建优化索引
     */
    async createOptimizedIndexes() {
        console.log('创建优化索引...');
        
        const indexes = [
            // 房间相关索引
            'CREATE INDEX IF NOT EXISTS idx_rooms_status_created ON rooms(status, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_rooms_player_id ON rooms(player_id)',
            'CREATE INDEX IF NOT EXISTS idx_rooms_game_status ON rooms(game_status, updated_at)',
            
            // 围观记录索引
            'CREATE INDEX IF NOT EXISTS idx_watch_records_room_user ON watch_records(room_id, user_id)',
            'CREATE INDEX IF NOT EXISTS idx_watch_records_room_time ON watch_records(room_id, join_time)',
            'CREATE INDEX IF NOT EXISTS idx_watch_records_user_time ON watch_records(user_id, join_time)',
            
            // 弹幕消息索引
            'CREATE INDEX IF NOT EXISTS idx_barrage_messages_room_time ON barrage_messages(room_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_barrage_messages_user_time ON barrage_messages(user_id, created_at)',
            
            // 预测记录索引
            'CREATE INDEX IF NOT EXISTS idx_predictions_room_question ON predictions(room_id, question_id)',
            'CREATE INDEX IF NOT EXISTS idx_predictions_user_time ON predictions(user_id, created_at)',
            
            // 礼物记录索引
            'CREATE INDEX IF NOT EXISTS idx_gifts_room_time ON gifts(room_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_gifts_user_type ON gifts(user_id, gift_type)',
            
            // 复合索引
            'CREATE INDEX IF NOT EXISTS idx_rooms_composite ON rooms(status, game_status, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_watch_composite ON watch_records(room_id, status, join_time)'
        ];
        
        try {
            for (const indexSQL of indexes) {
                await this.masterPool.execute(indexSQL);
            }
            console.log('索引创建完成');
        } catch (error) {
            console.error('创建索引失败:', error);
        }
    }
    
    /**
     * 获取活跃房间列表（优化版）
     */
    async getActiveRooms(page = 1, limit = 20) {
        const cacheKey = `active_rooms:${page}:${limit}`;
        const startTime = Date.now();
        
        try {
            // 尝试从缓存获取
            const cached = await this.getFromCache(cacheKey);
            if (cached) {
                this.recordQueryTime(Date.now() - startTime, 'cache_hit');
                return cached;
            }
            
            // 优化的SQL查询
            const sql = `
                SELECT 
                    r.id,
                    r.player_id,
                    r.game_status,
                    r.viewer_count,
                    r.created_at,
                    u.nickname as player_name,
                    u.avatar_url as player_avatar,
                    COUNT(wr.id) as current_viewers
                FROM rooms r
                INNER JOIN users u ON r.player_id = u.id
                LEFT JOIN watch_records wr ON r.id = wr.room_id AND wr.status = 'watching'
                WHERE r.status = 'active' 
                    AND r.game_status IN ('waiting', 'playing')
                    AND r.created_at > DATE_SUB(NOW(), INTERVAL 2 HOUR)
                GROUP BY r.id
                HAVING current_viewers > 0
                ORDER BY current_viewers DESC, r.created_at DESC
                LIMIT ? OFFSET ?
            `;
            
            const offset = (page - 1) * limit;
            const [rows] = await this.executeReadQuery(sql, [limit, offset]);
            
            // 缓存结果
            await this.setCache(cacheKey, rows, 60); // 1分钟缓存
            
            this.recordQueryTime(Date.now() - startTime, 'db_query');
            return rows;
            
        } catch (error) {
            console.error('获取活跃房间失败:', error);
            this.recordSlowQuery('getActiveRooms', Date.now() - startTime, error);
            throw error;
        }
    }
    
    /**
     * 获取房间详情（优化版）
     */
    async getRoomDetails(roomId) {
        const cacheKey = `room_details:${roomId}`;
        const startTime = Date.now();
        
        try {
            // 尝试从缓存获取
            const cached = await this.getFromCache(cacheKey);
            if (cached) {
                this.recordQueryTime(Date.now() - startTime, 'cache_hit');
                return cached;
            }
            
            // 并行查询房间信息和统计数据
            const [roomInfo, stats] = await Promise.all([
                this.getRoomBasicInfo(roomId),
                this.getRoomStats(roomId)
            ]);
            
            const result = {
                ...roomInfo,
                stats
            };
            
            // 缓存结果
            await this.setCache(cacheKey, result, 30); // 30秒缓存
            
            this.recordQueryTime(Date.now() - startTime, 'db_query');
            return result;
            
        } catch (error) {
            console.error('获取房间详情失败:', error);
            this.recordSlowQuery('getRoomDetails', Date.now() - startTime, error);
            throw error;
        }
    }
    
    /**
     * 获取房间基本信息
     */
    async getRoomBasicInfo(roomId) {
        const sql = `
            SELECT 
                r.id,
                r.player_id,
                r.game_status,
                r.current_question_id,
                r.viewer_count,
                r.created_at,
                r.updated_at,
                u.nickname as player_name,
                u.avatar_url as player_avatar,
                q.dialect as current_dialect,
                q.difficulty as current_difficulty
            FROM rooms r
            INNER JOIN users u ON r.player_id = u.id
            LEFT JOIN questions q ON r.current_question_id = q.id
            WHERE r.id = ? AND r.status = 'active'
        `;
        
        const [rows] = await this.executeReadQuery(sql, [roomId]);
        return rows[0] || null;
    }
    
    /**
     * 获取房间统计信息
     */
    async getRoomStats(roomId) {
        const sql = `
            SELECT 
                COUNT(DISTINCT wr.user_id) as total_viewers,
                COUNT(DISTINCT CASE WHEN wr.status = 'watching' THEN wr.user_id END) as current_viewers,
                COUNT(DISTINCT bm.id) as barrage_count,
                COUNT(DISTINCT p.id) as prediction_count,
                COUNT(DISTINCT g.id) as gift_count,
                COALESCE(SUM(g.gift_value), 0) as total_gift_value
            FROM rooms r
            LEFT JOIN watch_records wr ON r.id = wr.room_id
            LEFT JOIN barrage_messages bm ON r.id = bm.room_id AND bm.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            LEFT JOIN predictions p ON r.id = p.room_id AND p.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            LEFT JOIN gifts g ON r.id = g.room_id AND g.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            WHERE r.id = ?
            GROUP BY r.id
        `;
        
        const [rows] = await this.executeReadQuery(sql, [roomId]);
        return rows[0] || {
            total_viewers: 0,
            current_viewers: 0,
            barrage_count: 0,
            prediction_count: 0,
            gift_count: 0,
            total_gift_value: 0
        };
    }
    
    /**
     * 获取房间观众列表（优化版）
     */
    async getRoomViewers(roomId, page = 1, limit = 50) {
        const cacheKey = `room_viewers:${roomId}:${page}:${limit}`;
        const startTime = Date.now();
        
        try {
            // 尝试从缓存获取
            const cached = await this.getFromCache(cacheKey);
            if (cached) {
                this.recordQueryTime(Date.now() - startTime, 'cache_hit');
                return cached;
            }
            
            const sql = `
                SELECT 
                    wr.user_id,
                    wr.join_time,
                    wr.status,
                    u.nickname,
                    u.avatar_url,
                    u.level,
                    COUNT(bm.id) as barrage_count,
                    COUNT(g.id) as gift_count
                FROM watch_records wr
                INNER JOIN users u ON wr.user_id = u.id
                LEFT JOIN barrage_messages bm ON wr.room_id = bm.room_id AND wr.user_id = bm.user_id
                LEFT JOIN gifts g ON wr.room_id = g.room_id AND wr.user_id = g.user_id
                WHERE wr.room_id = ? AND wr.status = 'watching'
                GROUP BY wr.user_id
                ORDER BY wr.join_time ASC
                LIMIT ? OFFSET ?
            `;
            
            const offset = (page - 1) * limit;
            const [rows] = await this.executeReadQuery(sql, [roomId, limit, offset]);
            
            // 缓存结果
            await this.setCache(cacheKey, rows, 30); // 30秒缓存
            
            this.recordQueryTime(Date.now() - startTime, 'db_query');
            return rows;
            
        } catch (error) {
            console.error('获取房间观众失败:', error);
            this.recordSlowQuery('getRoomViewers', Date.now() - startTime, error);
            throw error;
        }
    }
    
    /**
     * 获取弹幕消息（优化版）
     */
    async getBarrageMessages(roomId, lastMessageId = null, limit = 50) {
        const cacheKey = `barrage:${roomId}:${lastMessageId}:${limit}`;
        const startTime = Date.now();
        
        try {
            // 尝试从缓存获取
            const cached = await this.getFromCache(cacheKey);
            if (cached) {
                this.recordQueryTime(Date.now() - startTime, 'cache_hit');
                return cached;
            }
            
            let sql = `
                SELECT 
                    bm.id,
                    bm.user_id,
                    bm.content,
                    bm.created_at,
                    u.nickname,
                    u.avatar_url,
                    u.level
                FROM barrage_messages bm
                INNER JOIN users u ON bm.user_id = u.id
                WHERE bm.room_id = ?
            `;
            
            const params = [roomId];
            
            if (lastMessageId) {
                sql += ' AND bm.id > ?';
                params.push(lastMessageId);
            }
            
            sql += ' ORDER BY bm.created_at DESC LIMIT ?';
            params.push(limit);
            
            const [rows] = await this.executeReadQuery(sql, params);
            
            // 缓存结果
            await this.setCache(cacheKey, rows, 10); // 10秒缓存
            
            this.recordQueryTime(Date.now() - startTime, 'db_query');
            return rows;
            
        } catch (error) {
            console.error('获取弹幕消息失败:', error);
            this.recordSlowQuery('getBarrageMessages', Date.now() - startTime, error);
            throw error;
        }
    }
    
    /**
     * 批量插入弹幕消息（优化版）
     */
    async insertBarrageMessages(messages) {
        const startTime = Date.now();
        
        try {
            if (!messages || messages.length === 0) {
                return;
            }
            
            // 按分片分组
            const shardGroups = new Map();
            for (const message of messages) {
                const shardIndex = this.getMessageShard(message.room_id);
                if (!shardGroups.has(shardIndex)) {
                    shardGroups.set(shardIndex, []);
                }
                shardGroups.get(shardIndex).push(message);
            }
            
            // 并行插入各分片
            const promises = [];
            for (const [shardIndex, shardMessages] of shardGroups) {
                promises.push(this.insertMessagesToShard(shardIndex, shardMessages));
            }
            
            await Promise.all(promises);
            
            // 清理相关缓存
            const roomIds = [...new Set(messages.map(m => m.room_id))];
            await this.clearRoomCache(roomIds);
            
            this.recordQueryTime(Date.now() - startTime, 'batch_insert');
            
        } catch (error) {
            console.error('批量插入弹幕失败:', error);
            this.recordSlowQuery('insertBarrageMessages', Date.now() - startTime, error);
            throw error;
        }
    }
    
    /**
     * 插入消息到分片表
     */
    async insertMessagesToShard(shardIndex, messages) {
        const tableName = `barrage_messages_${shardIndex}`;
        
        const sql = `
            INSERT INTO ${tableName} (room_id, user_id, content, created_at)
            VALUES ?
        `;
        
        const values = messages.map(m => [
            m.room_id,
            m.user_id,
            m.content,
            m.created_at || new Date()
        ]);
        
        await this.executeWriteQuery(sql, [values]);
    }
    
    /**
     * 获取消息分片索引
     */
    getMessageShard(roomId) {
        return roomId % this.shardingConfig.messageShards;
    }
    
    /**
     * 获取房间分片索引
     */
    getRoomShard(roomId) {
        return roomId % this.shardingConfig.roomShards;
    }
    
    /**
     * 获取用户分片索引
     */
    getUserShard(userId) {
        return userId % this.shardingConfig.userShards;
    }
    
    /**
     * 执行读查询（从库）
     */
    async executeReadQuery(sql, params = []) {
        const pool = this.getReadPool();
        return await pool.execute(sql, params);
    }
    
    /**
     * 执行写查询（主库）
     */
    async executeWriteQuery(sql, params = []) {
        return await this.masterPool.execute(sql, params);
    }
    
    /**
     * 获取读连接池（负载均衡）
     */
    getReadPool() {
        if (this.slavePools.length === 0) {
            return this.masterPool;
        }
        
        // 简单轮询负载均衡
        const index = Math.floor(Math.random() * this.slavePools.length);
        return this.slavePools[index];
    }
    
    /**
     * 从缓存获取数据
     */
    async getFromCache(key) {
        try {
            const data = await this.redisClient.get(key);
            if (data) {
                this.cacheStats.hits++;
                return JSON.parse(data);
            }
            this.cacheStats.misses++;
            return null;
        } catch (error) {
            console.error('缓存读取失败:', error);
            return null;
        }
    }
    
    /**
     * 设置缓存数据
     */
    async setCache(key, data, ttl = null) {
        try {
            const value = JSON.stringify(data);
            const expiry = ttl || this.redisConfig.defaultTTL;
            await this.redisClient.setEx(key, expiry, value);
            this.cacheStats.sets++;
        } catch (error) {
            console.error('缓存写入失败:', error);
        }
    }
    
    /**
     * 清理房间相关缓存
     */
    async clearRoomCache(roomIds) {
        try {
            const keys = [];
            for (const roomId of roomIds) {
                keys.push(
                    `room_details:${roomId}`,
                    `room_viewers:${roomId}:*`,
                    `barrage:${roomId}:*`
                );
            }
            
            // 使用模式匹配删除
            for (const pattern of keys) {
                if (pattern.includes('*')) {
                    const matchingKeys = await this.redisClient.keys(pattern);
                    if (matchingKeys.length > 0) {
                        await this.redisClient.del(matchingKeys);
                    }
                } else {
                    await this.redisClient.del(pattern);
                }
            }
        } catch (error) {
            console.error('清理缓存失败:', error);
        }
    }
    
    /**
     * 记录查询时间
     */
    recordQueryTime(time, type) {
        this.performanceStats.queryTimes.push({
            time,
            type,
            timestamp: Date.now()
        });
        
        // 保持样本数量限制
        if (this.performanceStats.queryTimes.length > this.performanceStats.maxSamples) {
            this.performanceStats.queryTimes.shift();
        }
    }
    
    /**
     * 记录慢查询
     */
    recordSlowQuery(operation, time, error = null) {
        this.performanceStats.slowQueries.push({
            operation,
            time,
            error: error ? error.message : null,
            timestamp: Date.now()
        });
        
        // 保持样本数量限制
        if (this.performanceStats.slowQueries.length > 100) {
            this.performanceStats.slowQueries.shift();
        }
        
        console.warn(`慢查询警告: ${operation} 耗时 ${time}ms`);
    }
    
    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        setInterval(() => {
            this.reportPerformanceStats();
        }, 60000); // 每分钟报告一次
    }
    
    /**
     * 报告性能统计
     */
    reportPerformanceStats() {
        const recentQueries = this.performanceStats.queryTimes.filter(
            q => Date.now() - q.timestamp < 60000
        );
        
        if (recentQueries.length === 0) {
            return;
        }
        
        const avgTime = recentQueries.reduce((sum, q) => sum + q.time, 0) / recentQueries.length;
        const maxTime = Math.max(...recentQueries.map(q => q.time));
        const cacheHitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100;
        
        console.log(`数据库性能统计:
            查询数量: ${recentQueries.length}
            平均响应时间: ${avgTime.toFixed(2)}ms
            最大响应时间: ${maxTime}ms
            缓存命中率: ${cacheHitRate.toFixed(1)}%
            慢查询数量: ${this.performanceStats.slowQueries.length}
        `);
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            cacheStats: this.cacheStats
        };
    }
    
    /**
     * 关闭数据库优化器
     */
    async close() {
        console.log('关闭数据库优化器...');
        
        try {
            // 关闭连接池
            if (this.masterPool) {
                await this.masterPool.end();
            }
            
            for (const pool of this.slavePools) {
                await pool.end();
            }
            
            // 关闭Redis连接
            if (this.redisClient) {
                await this.redisClient.quit();
            }
            
            console.log('数据库优化器已关闭');
            
        } catch (error) {
            console.error('关闭数据库优化器失败:', error);
        }
    }
}

module.exports = WatchDBOptimizer;
