-- 学习系统相关表结构
-- 创建时间: 2024-08-01

-- 课程表
CREATE TABLE IF NOT EXISTS courses (
    id VARCHAR(50) PRIMARY KEY COMMENT '课程ID',
    title VARCHAR(200) NOT NULL COMMENT '课程标题',
    description TEXT COMMENT '课程描述',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
    category VARCHAR(100) NOT NULL COMMENT '课程分类',
    dialect_region VARCHAR(100) NOT NULL COMMENT '方言地区',
    estimated_duration INT DEFAULT 0 COMMENT '预估学习时长(分钟)',
    total_lessons INT DEFAULT 0 COMMENT '总课时数',
    total_questions INT DEFAULT 0 COMMENT '总题目数',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者ID',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '课程状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    
    INDEX idx_category (category),
    INDEX idx_dialect_region (dialect_region),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_creator (creator_id),
    INDEX idx_published_at (published_at),
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程表';

-- 课程章节表
CREATE TABLE IF NOT EXISTS course_lessons (
    id VARCHAR(50) PRIMARY KEY COMMENT '课时ID',
    course_id VARCHAR(50) NOT NULL COMMENT '课程ID',
    title VARCHAR(200) NOT NULL COMMENT '课时标题',
    description TEXT COMMENT '课时描述',
    lesson_type ENUM('video', 'audio', 'text', 'quiz', 'practice') DEFAULT 'audio' COMMENT '课时类型',
    content_url VARCHAR(500) COMMENT '内容URL',
    content_data JSON COMMENT '内容数据',
    duration INT DEFAULT 0 COMMENT '时长(秒)',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必修',
    unlock_condition JSON COMMENT '解锁条件',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_course (course_id),
    INDEX idx_type (lesson_type),
    INDEX idx_sort (sort_order),
    INDEX idx_required (is_required),
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程章节表';

-- 用户课程进度表
CREATE TABLE IF NOT EXISTS user_course_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '进度ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    course_id VARCHAR(50) NOT NULL COMMENT '课程ID',
    current_lesson_id VARCHAR(50) COMMENT '当前课时ID',
    completed_lessons INT DEFAULT 0 COMMENT '已完成课时数',
    total_lessons INT DEFAULT 0 COMMENT '总课时数',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成百分比',
    total_study_time INT DEFAULT 0 COMMENT '总学习时长(秒)',
    last_study_at TIMESTAMP NULL COMMENT '最后学习时间',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    status ENUM('not_started', 'in_progress', 'completed', 'paused') DEFAULT 'not_started' COMMENT '学习状态',
    
    UNIQUE KEY uk_user_course (user_id, course_id),
    INDEX idx_user (user_id),
    INDEX idx_course (course_id),
    INDEX idx_status (status),
    INDEX idx_last_study (last_study_at),
    INDEX idx_progress (progress_percentage),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (current_lesson_id) REFERENCES course_lessons(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课程进度表';

-- 用户课时进度表
CREATE TABLE IF NOT EXISTS user_lesson_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '课时进度ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    course_id VARCHAR(50) NOT NULL COMMENT '课程ID',
    lesson_id VARCHAR(50) NOT NULL COMMENT '课时ID',
    status ENUM('not_started', 'in_progress', 'completed') DEFAULT 'not_started' COMMENT '学习状态',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成百分比',
    study_time INT DEFAULT 0 COMMENT '学习时长(秒)',
    score DECIMAL(5,2) COMMENT '得分',
    attempts INT DEFAULT 0 COMMENT '尝试次数',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    last_position INT DEFAULT 0 COMMENT '最后播放位置(秒)',
    
    UNIQUE KEY uk_user_lesson (user_id, lesson_id),
    INDEX idx_user_course (user_id, course_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_status (status),
    INDEX idx_completed_at (completed_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户课时进度表';

-- 训练营表
CREATE TABLE IF NOT EXISTS training_camps (
    id VARCHAR(50) PRIMARY KEY COMMENT '训练营ID',
    name VARCHAR(200) NOT NULL COMMENT '训练营名称',
    description TEXT COMMENT '训练营描述',
    cover_image VARCHAR(500) COMMENT '封面图片',
    camp_type ENUM('daily', 'weekly', 'challenge', 'competition') DEFAULT 'daily' COMMENT '训练营类型',
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
    target_dialect VARCHAR(100) NOT NULL COMMENT '目标方言',
    duration_days INT DEFAULT 7 COMMENT '持续天数',
    daily_target INT DEFAULT 10 COMMENT '每日目标(题数)',
    reward_points INT DEFAULT 100 COMMENT '奖励积分',
    reward_items JSON COMMENT '奖励物品',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    max_participants INT DEFAULT 1000 COMMENT '最大参与人数',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者ID',
    status ENUM('upcoming', 'active', 'completed', 'cancelled') DEFAULT 'upcoming' COMMENT '状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (camp_type),
    INDEX idx_dialect (target_dialect),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_featured (is_featured),
    INDEX idx_creator (creator_id),
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营表';

-- 训练营参与记录表
CREATE TABLE IF NOT EXISTS training_camp_participants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '参与记录ID',
    camp_id VARCHAR(50) NOT NULL COMMENT '训练营ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    current_day INT DEFAULT 1 COMMENT '当前天数',
    completed_days INT DEFAULT 0 COMMENT '已完成天数',
    total_questions_answered INT DEFAULT 0 COMMENT '总答题数',
    correct_answers INT DEFAULT 0 COMMENT '正确答案数',
    total_points_earned INT DEFAULT 0 COMMENT '总获得积分',
    current_streak INT DEFAULT 0 COMMENT '当前连续天数',
    max_streak INT DEFAULT 0 COMMENT '最大连续天数',
    status ENUM('active', 'completed', 'dropped_out') DEFAULT 'active' COMMENT '参与状态',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
    final_rank INT COMMENT '最终排名',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    UNIQUE KEY uk_camp_user (camp_id, user_id),
    INDEX idx_user (user_id),
    INDEX idx_camp (camp_id),
    INDEX idx_status (status),
    INDEX idx_rank (final_rank),
    INDEX idx_streak (current_streak),
    
    FOREIGN KEY (camp_id) REFERENCES training_camps(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营参与记录表';

-- 训练营每日进度表
CREATE TABLE IF NOT EXISTS training_camp_daily_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '每日进度ID',
    camp_id VARCHAR(50) NOT NULL COMMENT '训练营ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    day_number INT NOT NULL COMMENT '天数',
    target_questions INT NOT NULL COMMENT '目标题数',
    completed_questions INT DEFAULT 0 COMMENT '已完成题数',
    correct_answers INT DEFAULT 0 COMMENT '正确答案数',
    points_earned INT DEFAULT 0 COMMENT '获得积分',
    study_time INT DEFAULT 0 COMMENT '学习时长(秒)',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否完成',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    date DATE NOT NULL COMMENT '日期',
    
    UNIQUE KEY uk_camp_user_day (camp_id, user_id, day_number),
    INDEX idx_user_date (user_id, date),
    INDEX idx_camp_date (camp_id, date),
    INDEX idx_completed (is_completed),
    
    FOREIGN KEY (camp_id) REFERENCES training_camps(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练营每日进度表';

-- 学习路径表
CREATE TABLE IF NOT EXISTS learning_paths (
    id VARCHAR(50) PRIMARY KEY COMMENT '学习路径ID',
    name VARCHAR(200) NOT NULL COMMENT '路径名称',
    description TEXT COMMENT '路径描述',
    cover_image VARCHAR(500) COMMENT '封面图片',
    target_dialect VARCHAR(100) NOT NULL COMMENT '目标方言',
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
    estimated_duration INT DEFAULT 0 COMMENT '预估完成时间(小时)',
    course_sequence JSON NOT NULL COMMENT '课程序列',
    prerequisites JSON COMMENT '前置条件',
    learning_objectives TEXT COMMENT '学习目标',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者ID',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_dialect (target_dialect),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_creator (creator_id),
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习路径表';

-- 用户学习路径进度表
CREATE TABLE IF NOT EXISTS user_learning_path_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '路径进度ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    path_id VARCHAR(50) NOT NULL COMMENT '学习路径ID',
    current_course_index INT DEFAULT 0 COMMENT '当前课程索引',
    completed_courses INT DEFAULT 0 COMMENT '已完成课程数',
    total_courses INT DEFAULT 0 COMMENT '总课程数',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成百分比',
    total_study_time INT DEFAULT 0 COMMENT '总学习时长(秒)',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    last_study_at TIMESTAMP NULL COMMENT '最后学习时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    status ENUM('not_started', 'in_progress', 'completed', 'paused') DEFAULT 'not_started' COMMENT '学习状态',
    
    UNIQUE KEY uk_user_path (user_id, path_id),
    INDEX idx_user (user_id),
    INDEX idx_path (path_id),
    INDEX idx_status (status),
    INDEX idx_progress (progress_percentage),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (path_id) REFERENCES learning_paths(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户学习路径进度表';

-- 学习成就表
CREATE TABLE IF NOT EXISTS learning_achievements (
    id VARCHAR(50) PRIMARY KEY COMMENT '成就ID',
    name VARCHAR(200) NOT NULL COMMENT '成就名称',
    description TEXT COMMENT '成就描述',
    icon_url VARCHAR(500) COMMENT '图标URL',
    achievement_type ENUM('course', 'streak', 'score', 'time', 'special') DEFAULT 'course' COMMENT '成就类型',
    category VARCHAR(100) NOT NULL COMMENT '成就分类',
    condition_data JSON NOT NULL COMMENT '达成条件',
    reward_points INT DEFAULT 0 COMMENT '奖励积分',
    reward_items JSON COMMENT '奖励物品',
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
    is_hidden BOOLEAN DEFAULT FALSE COMMENT '是否隐藏',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_type (achievement_type),
    INDEX idx_category (category),
    INDEX idx_rarity (rarity),
    INDEX idx_hidden (is_hidden)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习成就表';

-- 用户成就记录表
CREATE TABLE IF NOT EXISTS user_achievements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户成就ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    achievement_id VARCHAR(50) NOT NULL COMMENT '成就ID',
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    progress_data JSON COMMENT '进度数据',
    
    UNIQUE KEY uk_user_achievement (user_id, achievement_id),
    INDEX idx_user (user_id),
    INDEX idx_achievement (achievement_id),
    INDEX idx_earned_at (earned_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES learning_achievements(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户成就记录表';

-- 学习统计表
CREATE TABLE IF NOT EXISTS learning_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    date DATE NOT NULL COMMENT '统计日期',
    courses_studied INT DEFAULT 0 COMMENT '学习课程数',
    lessons_completed INT DEFAULT 0 COMMENT '完成课时数',
    questions_answered INT DEFAULT 0 COMMENT '答题数',
    correct_answers INT DEFAULT 0 COMMENT '正确答案数',
    study_time INT DEFAULT 0 COMMENT '学习时长(秒)',
    points_earned INT DEFAULT 0 COMMENT '获得积分',
    streak_days INT DEFAULT 0 COMMENT '连续学习天数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_date (user_id, date),
    INDEX idx_user (user_id),
    INDEX idx_date (date),
    INDEX idx_streak (streak_days),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习统计表';

-- 创建视图：用户学习概览
CREATE OR REPLACE VIEW user_learning_overview AS
SELECT 
    u.id as user_id,
    u.nickname,
    COUNT(DISTINCT ucp.course_id) as enrolled_courses,
    COUNT(DISTINCT CASE WHEN ucp.status = 'completed' THEN ucp.course_id END) as completed_courses,
    COUNT(DISTINCT tcp.camp_id) as joined_camps,
    COUNT(DISTINCT CASE WHEN tcp.status = 'completed' THEN tcp.camp_id END) as completed_camps,
    COUNT(DISTINCT ua.achievement_id) as total_achievements,
    COALESCE(SUM(ucp.total_study_time), 0) as total_study_time,
    COALESCE(MAX(ls.streak_days), 0) as current_streak,
    COALESCE(SUM(ls.points_earned), 0) as total_points
FROM users u
LEFT JOIN user_course_progress ucp ON u.id = ucp.user_id
LEFT JOIN training_camp_participants tcp ON u.id = tcp.user_id
LEFT JOIN user_achievements ua ON u.id = ua.user_id
LEFT JOIN learning_stats ls ON u.id = ls.user_id AND ls.date = CURDATE()
GROUP BY u.id, u.nickname;

-- 插入默认成就数据
INSERT INTO learning_achievements (id, name, description, achievement_type, category, condition_data, reward_points, rarity) VALUES
('ach_first_course', '初学者', '完成第一门课程', 'course', 'beginner', '{"courses_completed": 1}', 100, 'common'),
('ach_course_master', '课程大师', '完成10门课程', 'course', 'advanced', '{"courses_completed": 10}', 1000, 'rare'),
('ach_week_streak', '坚持一周', '连续学习7天', 'streak', 'persistence', '{"streak_days": 7}', 500, 'common'),
('ach_month_streak', '坚持一月', '连续学习30天', 'streak', 'persistence', '{"streak_days": 30}', 2000, 'epic'),
('ach_perfect_score', '满分达人', '单次测试获得满分', 'score', 'excellence', '{"perfect_scores": 1}', 200, 'rare'),
('ach_speed_learner', '学习达人', '单日学习超过2小时', 'time', 'dedication', '{"daily_study_minutes": 120}', 300, 'common');
