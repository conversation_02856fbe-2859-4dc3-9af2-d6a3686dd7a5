-- 围观系统相关表结构
-- 创建时间: 2024-08-01

-- 围观房间表
CREATE TABLE IF NOT EXISTS spectator_rooms (
    id VARCHAR(50) PRIMARY KEY COMMENT '房间ID',
    game_session_id VARCHAR(50) NOT NULL COMMENT '游戏会话ID',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者用户ID',
    settings JSON COMMENT '房间设置',
    status ENUM('active', 'closed', 'suspended') DEFAULT 'active' COMMENT '房间状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    closed_at TIMESTAMP NULL COMMENT '关闭时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_game_session (game_session_id),
    INDEX idx_creator (creator_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (game_session_id) REFERENCES game_sessions(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='围观房间表';

-- 围观记录表
CREATE TABLE IF NOT EXISTS spectator_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    room_id VARCHAR(50) NOT NULL COMMENT '房间ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    left_at TIMESTAMP NULL COMMENT '离开时间',
    duration INT DEFAULT 0 COMMENT '观看时长(秒)',
    
    INDEX idx_room_user (room_id, user_id),
    INDEX idx_user (user_id),
    INDEX idx_joined_at (joined_at),
    
    FOREIGN KEY (room_id) REFERENCES spectator_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='围观记录表';

-- 弹幕消息表
CREATE TABLE IF NOT EXISTS danmaku_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    room_id VARCHAR(50) NOT NULL COMMENT '房间ID',
    user_id VARCHAR(50) NOT NULL COMMENT '发送者ID',
    content TEXT NOT NULL COMMENT '弹幕内容',
    color VARCHAR(7) DEFAULT '#FFFFFF' COMMENT '弹幕颜色',
    position_type ENUM('scroll', 'top', 'bottom') DEFAULT 'scroll' COMMENT '弹幕位置类型',
    font_size ENUM('small', 'medium', 'large') DEFAULT 'medium' COMMENT '字体大小',
    status ENUM('normal', 'blocked', 'deleted') DEFAULT 'normal' COMMENT '消息状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    
    INDEX idx_room_time (room_id, created_at),
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (room_id) REFERENCES spectator_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹幕消息表';

-- 预测游戏表
CREATE TABLE IF NOT EXISTS prediction_games (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预测游戏ID',
    room_id VARCHAR(50) NOT NULL COMMENT '房间ID',
    question_id VARCHAR(50) NOT NULL COMMENT '题目ID',
    question_text TEXT NOT NULL COMMENT '题目内容',
    options JSON NOT NULL COMMENT '选项列表',
    correct_answer VARCHAR(10) COMMENT '正确答案',
    status ENUM('active', 'closed', 'settled') DEFAULT 'active' COMMENT '游戏状态',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    settle_time TIMESTAMP NULL COMMENT '结算时间',
    
    INDEX idx_room_question (room_id, question_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    
    FOREIGN KEY (room_id) REFERENCES spectator_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预测游戏表';

-- 预测记录表
CREATE TABLE IF NOT EXISTS prediction_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预测记录ID',
    prediction_game_id BIGINT NOT NULL COMMENT '预测游戏ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    predicted_answer VARCHAR(10) NOT NULL COMMENT '预测答案',
    confidence_level TINYINT DEFAULT 50 COMMENT '信心等级(1-100)',
    points_earned INT DEFAULT 0 COMMENT '获得积分',
    is_correct BOOLEAN NULL COMMENT '是否正确',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '预测时间',
    
    UNIQUE KEY uk_game_user (prediction_game_id, user_id),
    INDEX idx_user (user_id),
    INDEX idx_answer (predicted_answer),
    INDEX idx_correct (is_correct),
    
    FOREIGN KEY (prediction_game_id) REFERENCES prediction_games(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预测记录表';

-- 围观房间统计表
CREATE TABLE IF NOT EXISTS spectator_room_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    room_id VARCHAR(50) NOT NULL COMMENT '房间ID',
    date DATE NOT NULL COMMENT '统计日期',
    peak_spectators INT DEFAULT 0 COMMENT '峰值观众数',
    total_spectators INT DEFAULT 0 COMMENT '总观众数',
    avg_duration INT DEFAULT 0 COMMENT '平均观看时长(秒)',
    total_danmaku INT DEFAULT 0 COMMENT '弹幕总数',
    total_predictions INT DEFAULT 0 COMMENT '预测总数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_room_date (room_id, date),
    INDEX idx_date (date),
    INDEX idx_peak_spectators (peak_spectators),
    
    FOREIGN KEY (room_id) REFERENCES spectator_rooms(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='围观房间统计表';

-- 用户围观统计表
CREATE TABLE IF NOT EXISTS user_spectator_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    date DATE NOT NULL COMMENT '统计日期',
    rooms_visited INT DEFAULT 0 COMMENT '访问房间数',
    total_duration INT DEFAULT 0 COMMENT '总观看时长(秒)',
    danmaku_sent INT DEFAULT 0 COMMENT '发送弹幕数',
    predictions_made INT DEFAULT 0 COMMENT '预测次数',
    correct_predictions INT DEFAULT 0 COMMENT '正确预测数',
    points_earned INT DEFAULT 0 COMMENT '获得积分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_date (user_id, date),
    INDEX idx_date (date),
    INDEX idx_points (points_earned),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户围观统计表';

-- 弹幕过滤规则表
CREATE TABLE IF NOT EXISTS danmaku_filter_rules (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '规则ID',
    rule_type ENUM('keyword', 'regex', 'length', 'frequency') NOT NULL COMMENT '规则类型',
    rule_content TEXT NOT NULL COMMENT '规则内容',
    action ENUM('block', 'replace', 'warn') DEFAULT 'block' COMMENT '处理动作',
    replacement TEXT COMMENT '替换内容',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INT DEFAULT 0 COMMENT '优先级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type_active (rule_type, is_active),
    INDEX idx_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹幕过滤规则表';

-- 插入默认的弹幕过滤规则
INSERT INTO danmaku_filter_rules (rule_type, rule_content, action, priority) VALUES
('keyword', '垃圾,傻逼,操你妈,去死,滚蛋', 'replace', 100),
('keyword', '广告,加群,QQ,微信,淘宝', 'block', 90),
('length', '200', 'block', 80),
('frequency', '5', 'block', 70);

-- 创建视图：活跃围观房间
CREATE OR REPLACE VIEW active_spectator_rooms AS
SELECT 
    sr.*,
    gs.status as game_status,
    gs.current_question,
    u.nickname as creator_nickname,
    COALESCE(stats.peak_spectators, 0) as today_peak_spectators,
    COALESCE(stats.total_danmaku, 0) as today_danmaku_count
FROM spectator_rooms sr
JOIN game_sessions gs ON sr.game_session_id = gs.id
LEFT JOIN users u ON sr.creator_id = u.id
LEFT JOIN spectator_room_stats stats ON sr.id = stats.room_id AND stats.date = CURDATE()
WHERE sr.status = 'active' AND gs.status = 'active';

-- 创建视图：用户围观历史
CREATE OR REPLACE VIEW user_spectator_history AS
SELECT 
    sr.user_id,
    sr.room_id,
    room.game_session_id,
    sr.joined_at,
    sr.left_at,
    sr.duration,
    u.nickname,
    COALESCE(danmaku_count.count, 0) as danmaku_count,
    COALESCE(prediction_count.count, 0) as prediction_count
FROM spectator_records sr
JOIN spectator_rooms room ON sr.room_id = room.id
LEFT JOIN users u ON sr.user_id = u.id
LEFT JOIN (
    SELECT room_id, user_id, COUNT(*) as count
    FROM danmaku_messages
    WHERE status = 'normal'
    GROUP BY room_id, user_id
) danmaku_count ON sr.room_id = danmaku_count.room_id AND sr.user_id = danmaku_count.user_id
LEFT JOIN (
    SELECT pg.room_id, pr.user_id, COUNT(*) as count
    FROM prediction_records pr
    JOIN prediction_games pg ON pr.prediction_game_id = pg.id
    GROUP BY pg.room_id, pr.user_id
) prediction_count ON sr.room_id = prediction_count.room_id AND sr.user_id = prediction_count.user_id;
