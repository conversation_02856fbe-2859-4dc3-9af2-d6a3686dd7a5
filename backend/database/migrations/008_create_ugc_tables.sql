-- UGC内容管理系统相关表结构
-- 创建时间: 2024-08-01

-- UGC内容表
CREATE TABLE IF NOT EXISTS ugc_content (
    id VARCHAR(50) PRIMARY KEY COMMENT '内容ID',
    title VARCHAR(200) NOT NULL COMMENT '内容标题',
    description TEXT COMMENT '内容描述',
    content_type ENUM('audio', 'video', 'image', 'text', 'mixed') NOT NULL COMMENT '内容类型',
    dialect_region VARCHAR(100) NOT NULL COMMENT '方言地区',
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' COMMENT '难度等级',
    tags JSON COMMENT '标签列表',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者ID',
    creator_type ENUM('user', 'admin', 'expert') DEFAULT 'user' COMMENT '创建者类型',
    
    -- 内容文件信息
    primary_file_url VARCHAR(500) COMMENT '主要文件URL',
    primary_file_size BIGINT DEFAULT 0 COMMENT '主要文件大小(字节)',
    primary_file_duration INT COMMENT '音频/视频时长(秒)',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    additional_files JSON COMMENT '附加文件列表',
    
    -- 审核状态
    moderation_status ENUM('pending', 'approved', 'rejected', 'flagged') DEFAULT 'pending' COMMENT '审核状态',
    moderation_result JSON COMMENT '审核结果详情',
    moderated_by VARCHAR(50) COMMENT '审核员ID',
    moderated_at TIMESTAMP NULL COMMENT '审核时间',
    
    -- 质量评分
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分(0-10)',
    ai_quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT 'AI质量评分',
    human_quality_score DECIMAL(3,2) COMMENT '人工质量评分',
    
    -- 统计数据
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    dislike_count INT DEFAULT 0 COMMENT '点踩数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    report_count INT DEFAULT 0 COMMENT '举报次数',
    
    -- 状态管理
    status ENUM('draft', 'published', 'archived', 'deleted') DEFAULT 'draft' COMMENT '发布状态',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_premium BOOLEAN DEFAULT FALSE COMMENT '是否付费内容',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    
    INDEX idx_creator (creator_id),
    INDEX idx_dialect (dialect_region),
    INDEX idx_type (content_type),
    INDEX idx_difficulty (difficulty_level),
    INDEX idx_status (status),
    INDEX idx_moderation (moderation_status),
    INDEX idx_quality (quality_score),
    INDEX idx_featured (is_featured),
    INDEX idx_published_at (published_at),
    INDEX idx_view_count (view_count),
    INDEX idx_like_count (like_count),
    
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容表';

-- UGC内容分类表
CREATE TABLE IF NOT EXISTS ugc_categories (
    id VARCHAR(50) PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    parent_id VARCHAR(50) COMMENT '父分类ID',
    icon_url VARCHAR(500) COMMENT '图标URL',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    
    FOREIGN KEY (parent_id) REFERENCES ugc_categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容分类表';

-- UGC内容分类关联表
CREATE TABLE IF NOT EXISTS ugc_content_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    category_id VARCHAR(50) NOT NULL COMMENT '分类ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_content_category (content_id, category_id),
    INDEX idx_content (content_id),
    INDEX idx_category (category_id),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES ugc_categories(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容分类关联表';

-- UGC内容评价表
CREATE TABLE IF NOT EXISTS ugc_content_ratings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    rating_type ENUM('like', 'dislike', 'star') NOT NULL COMMENT '评价类型',
    rating_value INT COMMENT '评分值(1-5星)',
    comment TEXT COMMENT '评价内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_content_user_type (content_id, user_id, rating_type),
    INDEX idx_content (content_id),
    INDEX idx_user (user_id),
    INDEX idx_type (rating_type),
    INDEX idx_rating (rating_value),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容评价表';

-- UGC内容举报表
CREATE TABLE IF NOT EXISTS ugc_content_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '举报ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    reporter_id VARCHAR(50) NOT NULL COMMENT '举报者ID',
    report_type ENUM('inappropriate', 'copyright', 'spam', 'fake', 'other') NOT NULL COMMENT '举报类型',
    reason TEXT NOT NULL COMMENT '举报原因',
    evidence_urls JSON COMMENT '证据文件URLs',
    status ENUM('pending', 'reviewing', 'resolved', 'dismissed') DEFAULT 'pending' COMMENT '处理状态',
    handler_id VARCHAR(50) COMMENT '处理人ID',
    handler_note TEXT COMMENT '处理说明',
    handled_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '举报时间',
    
    INDEX idx_content (content_id),
    INDEX idx_reporter (reporter_id),
    INDEX idx_type (report_type),
    INDEX idx_status (status),
    INDEX idx_handler (handler_id),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (handler_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容举报表';

-- UGC内容收藏表
CREATE TABLE IF NOT EXISTS ugc_content_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '收藏ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    folder_name VARCHAR(100) DEFAULT 'default' COMMENT '收藏夹名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    
    UNIQUE KEY uk_content_user (content_id, user_id),
    INDEX idx_content (content_id),
    INDEX idx_user (user_id),
    INDEX idx_folder (folder_name),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容收藏表';

-- UGC内容下载记录表
CREATE TABLE IF NOT EXISTS ugc_content_downloads (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '下载记录ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    download_url VARCHAR(500) NOT NULL COMMENT '下载URL',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    download_ip VARCHAR(45) COMMENT '下载IP',
    user_agent TEXT COMMENT '用户代理',
    download_status ENUM('started', 'completed', 'failed') DEFAULT 'started' COMMENT '下载状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    INDEX idx_content (content_id),
    INDEX idx_user (user_id),
    INDEX idx_status (download_status),
    INDEX idx_created_at (created_at),
    INDEX idx_ip (download_ip),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容下载记录表';

-- UGC内容标签表
CREATE TABLE IF NOT EXISTS ugc_tags (
    id VARCHAR(50) PRIMARY KEY COMMENT '标签ID',
    name VARCHAR(100) NOT NULL COMMENT '标签名称',
    description TEXT COMMENT '标签描述',
    color VARCHAR(7) DEFAULT '#007bff' COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_official BOOLEAN DEFAULT FALSE COMMENT '是否官方标签',
    created_by VARCHAR(50) COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_name (name),
    INDEX idx_usage (usage_count),
    INDEX idx_official (is_official),
    INDEX idx_created_by (created_by),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容标签表';

-- UGC内容标签关联表
CREATE TABLE IF NOT EXISTS ugc_content_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    tag_id VARCHAR(50) NOT NULL COMMENT '标签ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_content_tag (content_id, tag_id),
    INDEX idx_content (content_id),
    INDEX idx_tag (tag_id),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES ugc_tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容标签关联表';

-- UGC内容审核历史表
CREATE TABLE IF NOT EXISTS ugc_moderation_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '审核历史ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    moderator_id VARCHAR(50) NOT NULL COMMENT '审核员ID',
    previous_status ENUM('pending', 'approved', 'rejected', 'flagged') COMMENT '之前状态',
    new_status ENUM('pending', 'approved', 'rejected', 'flagged') NOT NULL COMMENT '新状态',
    reason TEXT COMMENT '审核原因',
    moderation_data JSON COMMENT '审核数据',
    ai_confidence DECIMAL(5,4) COMMENT 'AI审核置信度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
    
    INDEX idx_content (content_id),
    INDEX idx_moderator (moderator_id),
    INDEX idx_status (new_status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (moderator_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容审核历史表';

-- UGC内容统计表
CREATE TABLE IF NOT EXISTS ugc_content_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    date DATE NOT NULL COMMENT '统计日期',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    dislike_count INT DEFAULT 0 COMMENT '点踩数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    share_count INT DEFAULT 0 COMMENT '分享次数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    report_count INT DEFAULT 0 COMMENT '举报次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_content_date (content_id, date),
    INDEX idx_content (content_id),
    INDEX idx_date (date),
    INDEX idx_view_count (view_count),
    
    FOREIGN KEY (content_id) REFERENCES ugc_content(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容统计表';

-- 创建视图：热门UGC内容
CREATE OR REPLACE VIEW popular_ugc_content AS
SELECT 
    uc.*,
    u.nickname as creator_nickname,
    u.avatar_url as creator_avatar,
    COALESCE(stats.view_count, 0) as today_views,
    COALESCE(stats.like_count, 0) as today_likes,
    (uc.like_count * 0.4 + uc.view_count * 0.3 + uc.download_count * 0.3) as popularity_score
FROM ugc_content uc
LEFT JOIN users u ON uc.creator_id = u.id
LEFT JOIN ugc_content_stats stats ON uc.id = stats.content_id AND stats.date = CURDATE()
WHERE uc.status = 'published' AND uc.moderation_status = 'approved'
ORDER BY popularity_score DESC;

-- 创建视图：用户UGC统计
CREATE OR REPLACE VIEW user_ugc_stats AS
SELECT 
    u.id as user_id,
    u.nickname,
    COUNT(uc.id) as total_content,
    COUNT(CASE WHEN uc.status = 'published' THEN 1 END) as published_content,
    COUNT(CASE WHEN uc.moderation_status = 'approved' THEN 1 END) as approved_content,
    SUM(uc.view_count) as total_views,
    SUM(uc.like_count) as total_likes,
    SUM(uc.download_count) as total_downloads,
    AVG(uc.quality_score) as avg_quality_score
FROM users u
LEFT JOIN ugc_content uc ON u.id = uc.creator_id
GROUP BY u.id, u.nickname;

-- 插入默认分类数据
INSERT INTO ugc_categories (id, name, description, sort_order) VALUES
('cat_audio', '音频内容', '用户上传的音频内容', 1),
('cat_video', '视频内容', '用户上传的视频内容', 2),
('cat_text', '文本内容', '用户创建的文本内容', 3),
('cat_mixed', '混合内容', '包含多种媒体类型的内容', 4),
('cat_dialect_lesson', '方言课程', '用户创建的方言学习课程', 5),
('cat_pronunciation', '发音练习', '发音相关的练习内容', 6),
('cat_culture', '文化介绍', '方言文化背景介绍', 7),
('cat_story', '方言故事', '用方言讲述的故事', 8);

-- 插入默认标签数据
INSERT INTO ugc_tags (id, name, description, is_official, created_by) VALUES
('tag_beginner', '初学者', '适合初学者的内容', TRUE, NULL),
('tag_advanced', '进阶', '适合进阶学习者的内容', TRUE, NULL),
('tag_pronunciation', '发音', '发音相关内容', TRUE, NULL),
('tag_grammar', '语法', '语法相关内容', TRUE, NULL),
('tag_vocabulary', '词汇', '词汇学习内容', TRUE, NULL),
('tag_culture', '文化', '文化背景内容', TRUE, NULL),
('tag_story', '故事', '故事类内容', TRUE, NULL),
('tag_song', '歌曲', '歌曲类内容', TRUE, NULL),
('tag_daily', '日常', '日常对话内容', TRUE, NULL),
('tag_formal', '正式', '正式场合用语', TRUE, NULL);
