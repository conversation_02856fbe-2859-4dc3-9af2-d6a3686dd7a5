-- 用户基础信息表
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(100) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint unsigned DEFAULT 0 COMMENT '性别: 0未知 1男 2女',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `language` varchar(10) DEFAULT 'zh_CN' COMMENT '语言',
  `total_score` int unsigned DEFAULT 0 COMMENT '总积分',
  `total_games` int unsigned DEFAULT 0 COMMENT '总游戏次数',
  `win_games` int unsigned DEFAULT 0 COMMENT '胜利次数',
  `max_streak` int unsigned DEFAULT 0 COMMENT '最大连胜数',
  `current_level` int unsigned DEFAULT 1 COMMENT '当前等级',
  `status` tinyint unsigned DEFAULT 1 COMMENT '状态: 0禁用 1正常',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_total_score` (`total_score` DESC),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status_score` (`status`, `total_score` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';