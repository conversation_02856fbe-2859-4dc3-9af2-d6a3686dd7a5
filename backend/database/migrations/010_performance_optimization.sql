-- 数据库性能优化脚本
-- 创建时间: 2024-08-01
-- 目标: 优化慢查询，添加复合索引，提升查询性能

-- ===== 用户相关表优化 =====

-- 用户表添加复合索引
ALTER TABLE users 
ADD INDEX idx_status_created (status, created_at),
ADD INDEX idx_region_level (region, level),
ADD INDEX idx_last_login (last_login_at),
ADD INDEX idx_nickname_search (nickname(20));

-- 用户统计表添加复合索引
ALTER TABLE user_stats 
ADD INDEX idx_user_date (user_id, date),
ADD INDEX idx_date_score (date, total_score),
ADD INDEX idx_games_accuracy (games_played, accuracy_rate);

-- ===== 游戏相关表优化 =====

-- 游戏会话表添加复合索引
ALTER TABLE game_sessions 
ADD INDEX idx_user_status_created (user_id, status, created_at),
ADD INDEX idx_status_created (status, created_at),
ADD INDEX idx_dialect_difficulty (dialect_region, difficulty_level),
ADD INDEX idx_score_time (final_score, total_time);

-- 游戏问题表添加复合索引
ALTER TABLE game_questions 
ADD INDEX idx_dialect_difficulty_active (dialect_region, difficulty_level, is_active),
ADD INDEX idx_category_type (category, question_type),
ADD INDEX idx_active_created (is_active, created_at),
ADD INDEX idx_usage_accuracy (usage_count, accuracy_rate);

-- 游戏答案表添加复合索引
ALTER TABLE game_answers 
ADD INDEX idx_session_question (session_id, question_id),
ADD INDEX idx_user_correct_time (user_id, is_correct, answered_at),
ADD INDEX idx_question_correct (question_id, is_correct),
ADD INDEX idx_answered_time (answered_at);

-- ===== 围观系统表优化 =====

-- 围观房间表添加复合索引
ALTER TABLE spectator_rooms 
ADD INDEX idx_game_status (game_session_id, status),
ADD INDEX idx_creator_created (creator_id, created_at),
ADD INDEX idx_status_created (status, created_at);

-- 围观记录表添加复合索引
ALTER TABLE spectator_records 
ADD INDEX idx_room_user (room_id, user_id),
ADD INDEX idx_user_joined (user_id, joined_at),
ADD INDEX idx_room_joined (room_id, joined_at);

-- 弹幕消息表添加复合索引
ALTER TABLE danmaku_messages 
ADD INDEX idx_room_created (room_id, created_at),
ADD INDEX idx_user_created (user_id, created_at),
ADD INDEX idx_status_created (status, created_at),
ADD INDEX idx_room_status_created (room_id, status, created_at);

-- 预测游戏表添加复合索引
ALTER TABLE prediction_games 
ADD INDEX idx_room_status (room_id, status),
ADD INDEX idx_question_status (question_id, status),
ADD INDEX idx_created_status (created_at, status);

-- 预测记录表添加复合索引
ALTER TABLE prediction_records 
ADD INDEX idx_game_user (game_id, user_id),
ADD INDEX idx_user_created (user_id, created_at),
ADD INDEX idx_game_prediction (game_id, predicted_answer),
ADD INDEX idx_correct_points (is_correct, points_earned);

-- ===== 学习系统表优化 =====

-- 课程表添加复合索引
ALTER TABLE courses 
ADD INDEX idx_dialect_difficulty (dialect_region, difficulty_level),
ADD INDEX idx_category_active (category, is_active),
ADD INDEX idx_featured_created (is_featured, created_at),
ADD INDEX idx_active_sort (is_active, sort_order);

-- 课程进度表添加复合索引
ALTER TABLE course_progress 
ADD INDEX idx_user_course (user_id, course_id),
ADD INDEX idx_user_status (user_id, status),
ADD INDEX idx_course_progress (course_id, progress_percentage),
ADD INDEX idx_updated_status (updated_at, status);

-- 课程学习记录表添加复合索引
ALTER TABLE lesson_progress 
ADD INDEX idx_user_lesson (user_id, lesson_id),
ADD INDEX idx_course_user (course_id, user_id),
ADD INDEX idx_user_completed (user_id, completed_at),
ADD INDEX idx_lesson_progress (lesson_id, progress_percentage);

-- 训练营表添加复合索引
ALTER TABLE training_camps 
ADD INDEX idx_type_status (camp_type, status),
ADD INDEX idx_dialect_difficulty (dialect_region, difficulty_level),
ADD INDEX idx_status_start (status, start_date),
ADD INDEX idx_active_featured (is_active, is_featured);

-- 训练营参与表添加复合索引
ALTER TABLE training_camp_participants 
ADD INDEX idx_camp_user (camp_id, user_id),
ADD INDEX idx_user_status (user_id, status),
ADD INDEX idx_camp_joined (camp_id, joined_at),
ADD INDEX idx_status_progress (status, current_day);

-- ===== UGC内容表优化 =====

-- UGC内容表添加复合索引
ALTER TABLE ugc_content 
ADD INDEX idx_creator_status (creator_id, status),
ADD INDEX idx_dialect_type (dialect_region, content_type),
ADD INDEX idx_status_published (status, published_at),
ADD INDEX idx_moderation_quality (moderation_status, quality_score),
ADD INDEX idx_featured_views (is_featured, view_count),
ADD INDEX idx_type_difficulty (content_type, difficulty_level),
ADD INDEX idx_published_views (published_at, view_count),
ADD INDEX idx_quality_likes (quality_score, like_count);

-- UGC内容评价表添加复合索引
ALTER TABLE ugc_content_ratings 
ADD INDEX idx_content_type (content_id, rating_type),
ADD INDEX idx_user_type (user_id, rating_type),
ADD INDEX idx_content_rating (content_id, rating_value),
ADD INDEX idx_created_rating (created_at, rating_value);

-- UGC内容举报表添加复合索引
ALTER TABLE ugc_content_reports 
ADD INDEX idx_content_status (content_id, status),
ADD INDEX idx_reporter_created (reporter_id, created_at),
ADD INDEX idx_type_status (report_type, status),
ADD INDEX idx_handler_handled (handler_id, handled_at);

-- UGC内容收藏表添加复合索引
ALTER TABLE ugc_content_favorites 
ADD INDEX idx_user_folder (user_id, folder_name),
ADD INDEX idx_content_created (content_id, created_at),
ADD INDEX idx_user_created (user_id, created_at);

-- ===== 国际化表优化 =====

-- 翻译内容表添加复合索引
ALTER TABLE i18n_translations 
ADD INDEX idx_key_language (key_id, language_code),
ADD INDEX idx_language_approved (language_code, is_approved),
ADD INDEX idx_translator_created (translator_id, created_at),
ADD INDEX idx_approved_quality (is_approved, translation_quality);

-- 翻译任务表添加复合索引
ALTER TABLE i18n_translation_tasks 
ADD INDEX idx_source_target (source_language, target_language),
ADD INDEX idx_assignee_status (assignee_id, status),
ADD INDEX idx_status_priority (status, priority),
ADD INDEX idx_deadline_status (deadline, status),
ADD INDEX idx_created_status (created_at, status);

-- 翻译任务项目表添加复合索引
ALTER TABLE i18n_translation_task_items 
ADD INDEX idx_task_status (task_id, status),
ADD INDEX idx_key_task (key_id, task_id),
ADD INDEX idx_status_quality (status, quality_score);

-- ===== 创建分区表（针对大数据量表） =====

-- 游戏答案表按月分区
ALTER TABLE game_answers 
PARTITION BY RANGE (YEAR(answered_at) * 100 + MONTH(answered_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 用户统计表按月分区
ALTER TABLE user_stats 
PARTITION BY RANGE (YEAR(date) * 100 + MONTH(date)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- ===== 创建物化视图（MySQL 8.0+） =====

-- 用户游戏统计物化视图
CREATE VIEW user_game_summary AS
SELECT 
    u.id as user_id,
    u.nickname,
    COUNT(gs.id) as total_games,
    AVG(gs.final_score) as avg_score,
    SUM(CASE WHEN gs.status = 'completed' THEN 1 ELSE 0 END) as completed_games,
    MAX(gs.final_score) as best_score,
    AVG(gs.total_time) as avg_time,
    COUNT(DISTINCT gs.dialect_region) as dialects_played
FROM users u
LEFT JOIN game_sessions gs ON u.id = gs.user_id
WHERE gs.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id, u.nickname;

-- 热门内容统计视图
CREATE VIEW popular_content_summary AS
SELECT 
    uc.id,
    uc.title,
    uc.content_type,
    uc.dialect_region,
    uc.view_count,
    uc.like_count,
    uc.download_count,
    (uc.like_count * 0.4 + uc.view_count * 0.3 + uc.download_count * 0.3) as popularity_score,
    u.nickname as creator_name
FROM ugc_content uc
JOIN users u ON uc.creator_id = u.id
WHERE uc.status = 'published' 
  AND uc.moderation_status = 'approved'
  AND uc.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY popularity_score DESC;

-- 方言地区统计视图
CREATE VIEW dialect_region_stats AS
SELECT 
    dialect_region,
    COUNT(DISTINCT id) as total_questions,
    COUNT(DISTINCT CASE WHEN is_active = TRUE THEN id END) as active_questions,
    AVG(accuracy_rate) as avg_accuracy,
    AVG(usage_count) as avg_usage,
    COUNT(DISTINCT difficulty_level) as difficulty_levels
FROM game_questions
GROUP BY dialect_region
ORDER BY total_questions DESC;

-- ===== 查询优化存储过程 =====

DELIMITER //

-- 获取用户游戏历史（优化版）
CREATE PROCEDURE GetUserGameHistory(
    IN p_user_id VARCHAR(50),
    IN p_limit INT DEFAULT 20,
    IN p_offset INT DEFAULT 0
)
BEGIN
    SELECT 
        gs.id,
        gs.dialect_region,
        gs.difficulty_level,
        gs.final_score,
        gs.total_time,
        gs.status,
        gs.created_at,
        COUNT(ga.id) as total_answers,
        SUM(CASE WHEN ga.is_correct = TRUE THEN 1 ELSE 0 END) as correct_answers
    FROM game_sessions gs
    LEFT JOIN game_answers ga ON gs.id = ga.session_id
    WHERE gs.user_id = p_user_id
    GROUP BY gs.id
    ORDER BY gs.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END //

-- 获取热门内容（优化版）
CREATE PROCEDURE GetPopularContent(
    IN p_content_type VARCHAR(50) DEFAULT NULL,
    IN p_dialect_region VARCHAR(100) DEFAULT NULL,
    IN p_time_range INT DEFAULT 7,
    IN p_limit INT DEFAULT 20
)
BEGIN
    DECLARE sql_query TEXT;
    
    SET sql_query = '
        SELECT 
            uc.*,
            u.nickname as creator_name,
            (uc.like_count * 0.4 + uc.view_count * 0.3 + uc.download_count * 0.3) as popularity_score
        FROM ugc_content uc
        JOIN users u ON uc.creator_id = u.id
        WHERE uc.status = "published" 
          AND uc.moderation_status = "approved"
          AND uc.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    
    IF p_content_type IS NOT NULL THEN
        SET sql_query = CONCAT(sql_query, ' AND uc.content_type = "', p_content_type, '"');
    END IF;
    
    IF p_dialect_region IS NOT NULL THEN
        SET sql_query = CONCAT(sql_query, ' AND uc.dialect_region = "', p_dialect_region, '"');
    END IF;
    
    SET sql_query = CONCAT(sql_query, ' ORDER BY popularity_score DESC LIMIT ?');
    
    SET @sql = sql_query;
    PREPARE stmt FROM @sql;
    EXECUTE stmt USING p_time_range, p_limit;
    DEALLOCATE PREPARE stmt;
END //

-- 获取学习进度统计（优化版）
CREATE PROCEDURE GetLearningProgressStats(
    IN p_user_id VARCHAR(50)
)
BEGIN
    SELECT 
        'courses' as type,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        AVG(progress_percentage) as avg_progress
    FROM course_progress 
    WHERE user_id = p_user_id
    
    UNION ALL
    
    SELECT 
        'training_camps' as type,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        AVG((current_day / total_days) * 100) as avg_progress
    FROM training_camp_participants tcp
    JOIN training_camps tc ON tcp.camp_id = tc.id
    WHERE tcp.user_id = p_user_id;
END //

DELIMITER ;

-- ===== 数据库配置优化建议 =====

-- 设置查询缓存（如果支持）
SET GLOBAL query_cache_type = ON;
SET GLOBAL query_cache_size = 268435456; -- 256MB

-- 设置InnoDB缓冲池大小
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 设置慢查询日志
SET GLOBAL slow_query_log = ON;
SET GLOBAL long_query_time = 2; -- 记录超过2秒的查询

-- 设置连接池配置
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 300;
SET GLOBAL interactive_timeout = 300;

-- ===== 定期维护任务 =====

-- 创建定期清理过期数据的事件
CREATE EVENT IF NOT EXISTS cleanup_expired_data
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- 清理过期的翻译缓存
    DELETE FROM i18n_translation_cache 
    WHERE expires_at < NOW();
    
    -- 清理过期的游戏会话（超过30天的未完成会话）
    DELETE FROM game_sessions 
    WHERE status != 'completed' 
      AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理过期的WebSocket连接记录（超过1天）
    DELETE FROM websocket_connections 
    WHERE last_ping < DATE_SUB(NOW(), INTERVAL 1 DAY);
    
    -- 优化表
    OPTIMIZE TABLE game_answers, user_stats, ugc_content_stats;
END;

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- ===== 性能监控查询 =====

-- 创建性能监控视图
CREATE VIEW performance_monitor AS
SELECT 
    'slow_queries' as metric,
    COUNT(*) as value,
    'count' as unit
FROM information_schema.PROCESSLIST 
WHERE TIME > 5

UNION ALL

SELECT 
    'active_connections' as metric,
    COUNT(*) as value,
    'count' as unit
FROM information_schema.PROCESSLIST 
WHERE COMMAND != 'Sleep'

UNION ALL

SELECT 
    'buffer_pool_hit_ratio' as metric,
    ROUND(
        (1 - (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Innodb_buffer_pool_reads') / 
        (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Innodb_buffer_pool_read_requests')) * 100, 2
    ) as value,
    'percentage' as unit;

-- 记录优化完成
INSERT INTO database_migrations (version, description, executed_at) 
VALUES ('010', 'Performance optimization - indexes, partitions, views, procedures', NOW());
