-- 方言题目表
CREATE TABLE `dialect_questions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `category` varchar(50) NOT NULL COMMENT '方言类别',
  `region` varchar(100) NOT NULL COMMENT '地区',
  `question_text` text NOT NULL COMMENT '题目文本',
  `question_type` tinyint unsigned DEFAULT 1 COMMENT '题目类型: 1听音辨字 2选择题 3填空题',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频文件URL',
  `audio_duration` int unsigned DEFAULT 0 COMMENT '音频时长(秒)',
  `difficulty_level` tinyint unsigned DEFAULT 1 COMMENT '难度等级: 1-5',
  `standard_answer` varchar(200) NOT NULL COMMENT '标准答案',
  `answer_options` json DEFAULT NULL COMMENT '选择题选项',
  `explanation` text DEFAULT NULL COMMENT '题目解释',
  `usage_count` int unsigned DEFAULT 0 COMMENT '使用次数',
  `correct_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '正确率',
  `avg_answer_time` decimal(8,4) DEFAULT 0.0000 COMMENT '平均答题时间',
  `status` tinyint unsigned DEFAULT 1 COMMENT '状态: 0下线 1上线',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建者ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_region` (`category`, `region`),
  KEY `idx_difficulty` (`difficulty_level`),
  KEY `idx_status_difficulty` (`status`, `difficulty_level`),
  KEY `idx_correct_rate` (`correct_rate` DESC),
  KEY `idx_usage_count` (`usage_count` DESC),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `fk_dialect_questions_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='方言题目表';