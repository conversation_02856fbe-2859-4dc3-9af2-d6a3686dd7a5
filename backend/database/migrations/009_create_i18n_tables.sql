-- 国际化内容管理系统相关表结构
-- 创建时间: 2024-08-01

-- 支持的语言表
CREATE TABLE IF NOT EXISTS i18n_languages (
    code VARCHAR(10) PRIMARY KEY COMMENT '语言代码 (如: zh-CN, en-US)',
    name VARCHAR(100) NOT NULL COMMENT '语言名称',
    native_name VARCHAR(100) NOT NULL COMMENT '本地语言名称',
    direction ENUM('ltr', 'rtl') DEFAULT 'ltr' COMMENT '文字方向',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认语言',
    sort_order INT DEFAULT 0 COMMENT '排序',
    flag_icon VARCHAR(200) COMMENT '国旗图标URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_active (is_active),
    INDEX idx_default (is_default),
    INDEX idx_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支持的语言表';

-- 翻译键表
CREATE TABLE IF NOT EXISTS i18n_keys (
    id VARCHAR(50) PRIMARY KEY COMMENT '翻译键ID',
    key_name VARCHAR(200) NOT NULL COMMENT '翻译键名称',
    category VARCHAR(100) NOT NULL COMMENT '分类 (ui, game, error, etc.)',
    description TEXT COMMENT '键描述',
    default_value TEXT COMMENT '默认值',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统键',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_key_name (key_name),
    INDEX idx_category (category),
    INDEX idx_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译键表';

-- 翻译内容表
CREATE TABLE IF NOT EXISTS i18n_translations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '翻译ID',
    key_id VARCHAR(50) NOT NULL COMMENT '翻译键ID',
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码',
    value TEXT NOT NULL COMMENT '翻译值',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核',
    translator_id VARCHAR(50) COMMENT '翻译者ID',
    reviewer_id VARCHAR(50) COMMENT '审核者ID',
    translation_quality DECIMAL(3,2) DEFAULT 0.00 COMMENT '翻译质量评分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    approved_at TIMESTAMP NULL COMMENT '审核时间',
    
    UNIQUE KEY uk_key_language (key_id, language_code),
    INDEX idx_language (language_code),
    INDEX idx_approved (is_approved),
    INDEX idx_translator (translator_id),
    INDEX idx_reviewer (reviewer_id),
    INDEX idx_quality (translation_quality),
    
    FOREIGN KEY (key_id) REFERENCES i18n_keys(id) ON DELETE CASCADE,
    FOREIGN KEY (language_code) REFERENCES i18n_languages(code) ON DELETE CASCADE,
    FOREIGN KEY (translator_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译内容表';

-- 方言地区多语言表
CREATE TABLE IF NOT EXISTS i18n_dialect_regions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '方言地区多语言ID',
    region_code VARCHAR(50) NOT NULL COMMENT '地区代码',
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码',
    name VARCHAR(200) NOT NULL COMMENT '地区名称',
    description TEXT COMMENT '地区描述',
    cultural_info TEXT COMMENT '文化信息',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_region_language (region_code, language_code),
    INDEX idx_region (region_code),
    INDEX idx_language (language_code),
    INDEX idx_approved (is_approved),
    
    FOREIGN KEY (language_code) REFERENCES i18n_languages(code) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='方言地区多语言表';

-- 游戏内容多语言表
CREATE TABLE IF NOT EXISTS i18n_game_content (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '游戏内容多语言ID',
    content_type ENUM('question', 'answer', 'hint', 'explanation') NOT NULL COMMENT '内容类型',
    content_id VARCHAR(50) NOT NULL COMMENT '内容ID',
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码',
    title VARCHAR(500) COMMENT '标题',
    content TEXT COMMENT '内容',
    audio_url VARCHAR(500) COMMENT '音频URL',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核',
    translator_id VARCHAR(50) COMMENT '翻译者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_content_language (content_type, content_id, language_code),
    INDEX idx_content_type (content_type),
    INDEX idx_content_id (content_id),
    INDEX idx_language (language_code),
    INDEX idx_approved (is_approved),
    INDEX idx_translator (translator_id),
    
    FOREIGN KEY (language_code) REFERENCES i18n_languages(code) ON DELETE CASCADE,
    FOREIGN KEY (translator_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏内容多语言表';

-- UGC内容多语言表
CREATE TABLE IF NOT EXISTS i18n_ugc_content (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'UGC内容多语言ID',
    ugc_content_id VARCHAR(50) NOT NULL COMMENT 'UGC内容ID',
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码',
    title VARCHAR(500) COMMENT '标题',
    description TEXT COMMENT '描述',
    tags JSON COMMENT '标签列表',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核',
    translator_id VARCHAR(50) COMMENT '翻译者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_ugc_language (ugc_content_id, language_code),
    INDEX idx_ugc_content (ugc_content_id),
    INDEX idx_language (language_code),
    INDEX idx_approved (is_approved),
    INDEX idx_translator (translator_id),
    
    FOREIGN KEY (ugc_content_id) REFERENCES ugc_content(id) ON DELETE CASCADE,
    FOREIGN KEY (language_code) REFERENCES i18n_languages(code) ON DELETE CASCADE,
    FOREIGN KEY (translator_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='UGC内容多语言表';

-- 翻译任务表
CREATE TABLE IF NOT EXISTS i18n_translation_tasks (
    id VARCHAR(50) PRIMARY KEY COMMENT '翻译任务ID',
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    source_language VARCHAR(10) NOT NULL COMMENT '源语言',
    target_language VARCHAR(10) NOT NULL COMMENT '目标语言',
    content_type ENUM('ui', 'game', 'ugc', 'system') NOT NULL COMMENT '内容类型',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    status ENUM('pending', 'assigned', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    assignee_id VARCHAR(50) COMMENT '分配给的翻译者ID',
    reviewer_id VARCHAR(50) COMMENT '审核者ID',
    deadline TIMESTAMP NULL COMMENT '截止时间',
    estimated_words INT DEFAULT 0 COMMENT '预估字数',
    actual_words INT DEFAULT 0 COMMENT '实际字数',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    created_by VARCHAR(50) NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    INDEX idx_source_language (source_language),
    INDEX idx_target_language (target_language),
    INDEX idx_content_type (content_type),
    INDEX idx_priority (priority),
    INDEX idx_status (status),
    INDEX idx_assignee (assignee_id),
    INDEX idx_reviewer (reviewer_id),
    INDEX idx_deadline (deadline),
    INDEX idx_created_by (created_by),
    
    FOREIGN KEY (source_language) REFERENCES i18n_languages(code) ON DELETE CASCADE,
    FOREIGN KEY (target_language) REFERENCES i18n_languages(code) ON DELETE CASCADE,
    FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译任务表';

-- 翻译任务项目表
CREATE TABLE IF NOT EXISTS i18n_translation_task_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '翻译任务项目ID',
    task_id VARCHAR(50) NOT NULL COMMENT '翻译任务ID',
    key_id VARCHAR(50) NOT NULL COMMENT '翻译键ID',
    source_text TEXT NOT NULL COMMENT '源文本',
    translated_text TEXT COMMENT '翻译文本',
    status ENUM('pending', 'translated', 'reviewed', 'approved') DEFAULT 'pending' COMMENT '状态',
    quality_score DECIMAL(3,2) COMMENT '质量评分',
    reviewer_comment TEXT COMMENT '审核意见',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_task_key (task_id, key_id),
    INDEX idx_task (task_id),
    INDEX idx_key (key_id),
    INDEX idx_status (status),
    INDEX idx_quality (quality_score),
    
    FOREIGN KEY (task_id) REFERENCES i18n_translation_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (key_id) REFERENCES i18n_keys(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译任务项目表';

-- 用户语言偏好表
CREATE TABLE IF NOT EXISTS user_language_preferences (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户语言偏好ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    primary_language VARCHAR(10) NOT NULL COMMENT '主要语言',
    secondary_languages JSON COMMENT '次要语言列表',
    auto_translate BOOLEAN DEFAULT TRUE COMMENT '是否自动翻译',
    translation_quality ENUM('fast', 'balanced', 'accurate') DEFAULT 'balanced' COMMENT '翻译质量偏好',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user (user_id),
    INDEX idx_primary_language (primary_language),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (primary_language) REFERENCES i18n_languages(code) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户语言偏好表';

-- 翻译缓存表
CREATE TABLE IF NOT EXISTS i18n_translation_cache (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '翻译缓存ID',
    cache_key VARCHAR(255) NOT NULL COMMENT '缓存键',
    language_code VARCHAR(10) NOT NULL COMMENT '语言代码',
    content_hash VARCHAR(64) NOT NULL COMMENT '内容哈希',
    cached_data JSON NOT NULL COMMENT '缓存数据',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_cache_key (cache_key, language_code),
    INDEX idx_language (language_code),
    INDEX idx_expires (expires_at),
    INDEX idx_hash (content_hash),
    
    FOREIGN KEY (language_code) REFERENCES i18n_languages(code) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译缓存表';

-- 插入默认支持的语言
INSERT INTO i18n_languages (code, name, native_name, direction, is_active, is_default, sort_order) VALUES
('zh-CN', 'Chinese (Simplified)', '简体中文', 'ltr', TRUE, TRUE, 1),
('zh-TW', 'Chinese (Traditional)', '繁體中文', 'ltr', TRUE, FALSE, 2),
('en-US', 'English (US)', 'English', 'ltr', TRUE, FALSE, 3),
('ja-JP', 'Japanese', '日本語', 'ltr', TRUE, FALSE, 4),
('ko-KR', 'Korean', '한국어', 'ltr', TRUE, FALSE, 5),
('th-TH', 'Thai', 'ไทย', 'ltr', TRUE, FALSE, 6),
('vi-VN', 'Vietnamese', 'Tiếng Việt', 'ltr', TRUE, FALSE, 7),
('id-ID', 'Indonesian', 'Bahasa Indonesia', 'ltr', TRUE, FALSE, 8),
('ms-MY', 'Malay', 'Bahasa Melayu', 'ltr', TRUE, FALSE, 9),
('tl-PH', 'Filipino', 'Filipino', 'ltr', TRUE, FALSE, 10),
('hi-IN', 'Hindi', 'हिन्दी', 'ltr', TRUE, FALSE, 11),
('ar-SA', 'Arabic', 'العربية', 'rtl', TRUE, FALSE, 12),
('es-ES', 'Spanish', 'Español', 'ltr', TRUE, FALSE, 13),
('fr-FR', 'French', 'Français', 'ltr', TRUE, FALSE, 14);

-- 插入默认翻译键
INSERT INTO i18n_keys (id, key_name, category, description, default_value, is_system) VALUES
('ui_welcome', 'ui.welcome', 'ui', '欢迎信息', '欢迎来到家乡话猜猜猜！', TRUE),
('ui_start_game', 'ui.start_game', 'ui', '开始游戏按钮', '开始游戏', TRUE),
('ui_settings', 'ui.settings', 'ui', '设置按钮', '设置', TRUE),
('ui_language', 'ui.language', 'ui', '语言设置', '语言', TRUE),
('game_correct', 'game.correct', 'game', '答对提示', '答对了！', TRUE),
('game_wrong', 'game.wrong', 'game', '答错提示', '答错了！', TRUE),
('game_score', 'game.score', 'game', '分数显示', '分数', TRUE),
('error_network', 'error.network', 'error', '网络错误', '网络连接失败，请检查网络设置', TRUE),
('error_audio', 'error.audio', 'error', '音频错误', '音频加载失败，请重试', TRUE),
('system_loading', 'system.loading', 'system', '加载中', '加载中...', TRUE);

-- 插入默认翻译内容（简体中文）
INSERT INTO i18n_translations (key_id, language_code, value, is_approved) VALUES
('ui_welcome', 'zh-CN', '欢迎来到家乡话猜猜猜！', TRUE),
('ui_start_game', 'zh-CN', '开始游戏', TRUE),
('ui_settings', 'zh-CN', '设置', TRUE),
('ui_language', 'zh-CN', '语言', TRUE),
('game_correct', 'zh-CN', '答对了！', TRUE),
('game_wrong', 'zh-CN', '答错了！', TRUE),
('game_score', 'zh-CN', '分数', TRUE),
('error_network', 'zh-CN', '网络连接失败，请检查网络设置', TRUE),
('error_audio', 'zh-CN', '音频加载失败，请重试', TRUE),
('system_loading', 'zh-CN', '加载中...', TRUE);

-- 插入默认翻译内容（英文）
INSERT INTO i18n_translations (key_id, language_code, value, is_approved) VALUES
('ui_welcome', 'en-US', 'Welcome to Hometown Dialect Game!', TRUE),
('ui_start_game', 'en-US', 'Start Game', TRUE),
('ui_settings', 'en-US', 'Settings', TRUE),
('ui_language', 'en-US', 'Language', TRUE),
('game_correct', 'en-US', 'Correct!', TRUE),
('game_wrong', 'en-US', 'Wrong!', TRUE),
('game_score', 'en-US', 'Score', TRUE),
('error_network', 'en-US', 'Network connection failed, please check your network settings', TRUE),
('error_audio', 'en-US', 'Audio loading failed, please try again', TRUE),
('system_loading', 'en-US', 'Loading...', TRUE);

-- 创建视图：翻译完成度统计
CREATE OR REPLACE VIEW i18n_completion_stats AS
SELECT 
    l.code as language_code,
    l.name as language_name,
    COUNT(k.id) as total_keys,
    COUNT(t.id) as translated_keys,
    COUNT(CASE WHEN t.is_approved = TRUE THEN 1 END) as approved_keys,
    ROUND(COUNT(t.id) * 100.0 / COUNT(k.id), 2) as completion_percentage,
    ROUND(COUNT(CASE WHEN t.is_approved = TRUE THEN 1 END) * 100.0 / COUNT(k.id), 2) as approval_percentage
FROM i18n_languages l
CROSS JOIN i18n_keys k
LEFT JOIN i18n_translations t ON k.id = t.key_id AND l.code = t.language_code
WHERE l.is_active = TRUE
GROUP BY l.code, l.name
ORDER BY completion_percentage DESC;

-- 创建视图：翻译质量统计
CREATE OR REPLACE VIEW i18n_quality_stats AS
SELECT 
    language_code,
    COUNT(*) as total_translations,
    AVG(translation_quality) as avg_quality,
    COUNT(CASE WHEN translation_quality >= 8.0 THEN 1 END) as high_quality_count,
    COUNT(CASE WHEN translation_quality < 6.0 THEN 1 END) as low_quality_count
FROM i18n_translations
WHERE translation_quality > 0
GROUP BY language_code
ORDER BY avg_quality DESC;
