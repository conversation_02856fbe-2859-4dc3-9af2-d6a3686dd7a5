-- 用户游戏统计表
CREATE TABLE `user_game_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `dialect_category` varchar(50) NOT NULL COMMENT '方言类别',
  `total_questions` int unsigned DEFAULT 0 COMMENT '总题目数',
  `correct_answers` int unsigned DEFAULT 0 COMMENT '正确答案数',
  `accuracy_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '准确率',
  `best_time` int unsigned DEFAULT 0 COMMENT '最佳用时(秒)',
  `avg_time` decimal(8,4) DEFAULT 0.0000 COMMENT '平均用时(秒)',
  `last_played_at` timestamp NULL DEFAULT NULL COMMENT '最后游戏时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_dialect` (`user_id`, `dialect_category`),
  KEY `idx_accuracy` (`accuracy_rate` DESC),
  KEY `idx_last_played` (`last_played_at`),
  CONSTRAINT `fk_user_game_stats_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户游戏统计表';