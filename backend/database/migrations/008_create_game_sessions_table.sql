-- 游戏会话表
CREATE TABLE `game_sessions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话标识',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `category` varchar(50) NOT NULL COMMENT '方言类别',
  `difficulty` tinyint unsigned NOT NULL COMMENT '难度等级',
  `question_count` int unsigned NOT NULL COMMENT '题目总数',
  `current_question` int unsigned DEFAULT 0 COMMENT '当前题目序号',
  `correct_count` int unsigned DEFAULT 0 COMMENT '正确数量',
  `total_score` int unsigned DEFAULT 0 COMMENT '总积分',
  `total_time` int unsigned DEFAULT 0 COMMENT '总用时(秒)',
  `game_mode` varchar(20) DEFAULT 'standard' COMMENT '游戏模式',
  `status` tinyint unsigned DEFAULT 1 COMMENT '状态: 1进行中 2已完成 3已取消',
  `started_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `finished_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status_expires` (`status`, `expires_at`),
  KEY `idx_category` (`category`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_game_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏会话表';