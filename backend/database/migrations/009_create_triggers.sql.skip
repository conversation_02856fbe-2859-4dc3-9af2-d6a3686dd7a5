-- 创建触发器来自动更新用户统计信息

-- 更新用户总积分和游戏次数的触发器
DELIMITER $$

CREATE TRIGGER update_user_stats_after_game_record 
AFTER INSERT ON game_records
FOR EACH ROW
BEGIN
  -- 更新用户总积分和游戏次数
  UPDATE users SET 
    total_score = total_score + NEW.score_earned,
    total_games = total_games + 1,
    win_games = CASE WHEN NEW.is_correct = 1 THEN win_games + 1 ELSE win_games END,
    updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.user_id;
  
  -- 获取题目的方言类别
  SET @category = (SELECT category FROM dialect_questions WHERE id = NEW.question_id);
  
  -- 更新或插入用户游戏分类统计
  INSERT INTO user_game_stats (
    user_id, 
    dialect_category, 
    total_questions, 
    correct_answers,
    last_played_at
  )
  VALUES (
    NEW.user_id, 
    @category,
    1, 
    NEW.is_correct,
    NEW.created_at
  )
  ON DUPLICATE KEY UPDATE
    total_questions = total_questions + 1,
    correct_answers = correct_answers + NEW.is_correct,
    accuracy_rate = correct_answers / total_questions,
    last_played_at = NEW.created_at,
    updated_at = CURRENT_TIMESTAMP;
    
  -- 更新题目统计信息
  UPDATE dialect_questions SET
    usage_count = usage_count + 1,
    correct_rate = (
      SELECT AVG(is_correct) 
      FROM game_records 
      WHERE question_id = NEW.question_id
    ),
    avg_answer_time = (
      SELECT AVG(answer_time) 
      FROM game_records 
      WHERE question_id = NEW.question_id AND answer_time > 0
    ),
    updated_at = CURRENT_TIMESTAMP
  WHERE id = NEW.question_id;
END$$

-- 游戏会话完成时更新用户最大连胜记录
CREATE TRIGGER update_user_max_streak
AFTER UPDATE ON game_sessions
FOR EACH ROW
BEGIN
  -- 当游戏会话状态变为已完成时
  IF NEW.status = 2 AND OLD.status != 2 THEN
    -- 计算本次游戏的最大连胜数
    SET @max_streak = (
      SELECT MAX(streak_count) 
      FROM game_records 
      WHERE game_session_id = NEW.session_id
    );
    
    -- 更新用户最大连胜记录
    UPDATE users SET
      max_streak = GREATEST(max_streak, IFNULL(@max_streak, 0)),
      updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.user_id;
  END IF;
END$$

DELIMITER ;

-- 创建清理过期游戏会话的事件
DELIMITER $$

CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
BEGIN
  -- 将过期的游戏会话标记为已取消
  UPDATE game_sessions 
  SET status = 3, updated_at = CURRENT_TIMESTAMP
  WHERE status = 1 AND expires_at < CURRENT_TIMESTAMP;
  
  -- 删除7天前的已取消会话
  DELETE FROM game_sessions 
  WHERE status = 3 AND updated_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY);
END$$

-- 创建自动添加游戏记录分区的事件
CREATE EVENT IF NOT EXISTS auto_add_game_records_partition
ON SCHEDULE EVERY 1 MONTH
STARTS CURRENT_TIMESTAMP
DO
BEGIN
  SET @next_month = DATE_FORMAT(DATE_ADD(CURRENT_DATE, INTERVAL 1 MONTH), '%Y%m');
  SET @partition_name = CONCAT('p', @next_month);
  SET @partition_value = CAST(@next_month AS UNSIGNED) + 1;
  
  SET @sql = CONCAT(
    'ALTER TABLE game_records ADD PARTITION (',
    'PARTITION ', @partition_name, 
    ' VALUES LESS THAN (', @partition_value, '))'
  );
  
  PREPARE stmt FROM @sql;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
END$$

DELIMITER ;