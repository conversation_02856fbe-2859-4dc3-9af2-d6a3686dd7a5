-- 排行榜表
CREATE TABLE `leaderboards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '排行榜类型: overall总榜 weekly周榜 monthly月榜',
  `category` varchar(50) DEFAULT NULL COMMENT '方言类别(可选)',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `score` int unsigned NOT NULL COMMENT '积分',
  `rank` int unsigned NOT NULL COMMENT '排名',
  `extra_data` json DEFAULT NULL COMMENT '额外数据',
  `period` varchar(20) NOT NULL COMMENT '周期标识: 2024-01, 2024-W01',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_period_user` (`type`, `period`, `user_id`),
  KEY `idx_type_period_rank` (`type`, `period`, `rank`),
  KEY `idx_type_category_rank` (`type`, `category`, `rank`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_leaderboards_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜表';