-- 分享记录表
CREATE TABLE `share_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '分享用户ID',
  `share_type` varchar(50) NOT NULL COMMENT '分享类型: score成绩 invite邀请 achievement成就',
  `share_content` json NOT NULL COMMENT '分享内容',
  `platform` varchar(20) DEFAULT 'wechat' COMMENT '分享平台',
  `click_count` int unsigned DEFAULT 0 COMMENT '点击次数',
  `conversion_count` int unsigned DEFAULT 0 COMMENT '转化次数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_share_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享记录表';