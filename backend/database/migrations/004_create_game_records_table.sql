-- 游戏记录表 (开发环境简化版本，去除分区以支持外键)
CREATE TABLE `game_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `game_session_id` varchar(64) NOT NULL COMMENT '游戏会话ID',
  `question_id` bigint unsigned NOT NULL COMMENT '题目ID',
  `user_answer` varchar(200) DEFAULT NULL COMMENT '用户答案',
  `is_correct` tinyint unsigned DEFAULT 0 COMMENT '是否正确: 0错误 1正确',
  `answer_time` int unsigned DEFAULT 0 COMMENT '答题用时(秒)',
  `score_earned` int unsigned DEFAULT 0 COMMENT '获得积分',
  `streak_count` int unsigned DEFAULT 0 COMMENT '连击数',
  `hint_used` tinyint unsigned DEFAULT 0 COMMENT '是否使用提示: 0否 1是',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '答题时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_game_session` (`game_session_id`),
  KEY `idx_question_id` (`question_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_created` (`user_id`, `created_at` DESC),
  CONSTRAINT `fk_game_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_game_records_question_id` FOREIGN KEY (`question_id`) REFERENCES `dialect_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏记录表 (开发环境)';