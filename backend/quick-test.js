const http = require('http');

const testEndpoint = (path, method = 'GET', body = null) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Quick-Test/1.0'
      },
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (body) {
      req.write(JSON.stringify(body));
    }

    req.end();
  });
};

async function quickTest() {
  console.log('🚀 快速API测试\n');

  const tests = [
    { name: '健康检查', path: '/health' },
    { name: 'API文档', path: '/docs' },
    { name: '获取题目', path: '/v1/questions?limit=3' },
    { name: '微信登录', path: '/v1/auth/wechat/login', method: 'POST', body: { code: 'test_code' } },
  ];

  for (const test of tests) {
    try {
      console.log(`⏳ ${test.name}...`);
      const result = await testEndpoint(test.path, test.method, test.body);
      console.log(`✅ ${test.name}: HTTP ${result.status}`);
      if (test.name === '健康检查' && result.data.services) {
        console.log(`   💾 数据库: ${result.data.services.database?.status}`);
        console.log(`   🗄️  缓存: ${result.data.services.redis?.status}`);
      }
      if (test.name === '获取题目' && result.data.data) {
        console.log(`   📝 题目数量: ${result.data.data.questions?.length || 0}`);
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 测试完成！');
}

quickTest().catch(console.error);