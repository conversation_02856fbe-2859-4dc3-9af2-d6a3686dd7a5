#!/usr/bin/env node

/**
 * API测试脚本
 * 测试所有后端API端点的可用性
 */

const http = require('http');
const https = require('https');

const BASE_URL = 'http://localhost:3001';

// 发起HTTP请求的工具函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestModule = urlObj.protocol === 'https:' ? https : http;
    
    const reqOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Test-Client/1.0',
        ...options.headers
      }
    };

    const req = requestModule.request(reqOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (error) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

// 测试用例配置
const testCases = [
  {
    name: '健康检查',
    url: `${BASE_URL}/health`,
    method: 'GET',
    expectedStatus: 200
  },
  {
    name: 'API文档',
    url: `${BASE_URL}/docs`,
    method: 'GET',
    expectedStatus: 200
  },
  {
    name: '获取题目列表',
    url: `${BASE_URL}/v1/questions`,
    method: 'GET',
    expectedStatus: [200, 401] // 可能需要认证
  },
  {
    name: '获取题目列表（带参数）',
    url: `${BASE_URL}/v1/questions?limit=5&dialect=四川话`,
    method: 'GET',
    expectedStatus: [200, 401]
  },
  {
    name: '创建游戏会话（无认证）',
    url: `${BASE_URL}/v1/game-sessions`,
    method: 'POST',
    body: {
      difficulty: 1,
      questionCount: 10,
      dialect: '四川话'
    },
    expectedStatus: [200, 400, 401]
  },
  {
    name: '微信登录端点',
    url: `${BASE_URL}/v1/auth/wechat/login`,
    method: 'POST',
    body: {
      code: 'test_code',
      userInfo: {
        nickname: '测试用户',
        avatarUrl: ''
      }
    },
    expectedStatus: [200, 400, 401]
  },
  {
    name: '获取当前用户信息（无认证）',
    url: `${BASE_URL}/v1/auth/me`,
    method: 'GET',
    expectedStatus: [401, 403]
  },
  {
    name: '404测试',
    url: `${BASE_URL}/v1/nonexistent`,
    method: 'GET',
    expectedStatus: 404
  }
];

// 运行测试
async function runTests() {
  console.log('🧪 开始API端点测试...\n');
  console.log(`📍 测试服务器: ${BASE_URL}\n`);

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    try {
      console.log(`⏳ 测试: ${testCase.name}`);
      console.log(`   ${testCase.method} ${testCase.url}`);

      const response = await makeRequest(testCase.url, {
        method: testCase.method,
        body: testCase.body,
        headers: testCase.headers
      });

      const expectedStatuses = Array.isArray(testCase.expectedStatus) 
        ? testCase.expectedStatus 
        : [testCase.expectedStatus];

      const isStatusOk = expectedStatuses.includes(response.status);

      if (isStatusOk) {
        console.log(`   ✅ 通过 - 状态码: ${response.status}`);
        if (testCase.name === '健康检查' && response.data) {
          console.log(`   📊 服务状态: ${response.data.status}`);
          if (response.data.services) {
            console.log(`   💾 数据库: ${response.data.services.database?.status} (${response.data.services.database?.type})`);
            console.log(`   🗄️  缓存: ${response.data.services.redis?.status} (${response.data.services.redis?.type})`);
          }
        }
        passedTests++;
      } else {
        console.log(`   ❌ 失败 - 期望状态码: ${expectedStatuses.join('或')}, 实际: ${response.status}`);
        if (response.data && typeof response.data === 'object') {
          console.log(`   📝 响应: ${JSON.stringify(response.data, null, 2)}`);
        }
      }

    } catch (error) {
      console.log(`   💥 错误: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.log('   🚨 服务器似乎没有运行，请先启动开发服务器');
        break;
      }
    }

    console.log(''); // 空行分隔
  }

  // 输出测试结果汇总
  console.log('📋 测试结果汇总:');
  console.log(`   ✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`   ❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  console.log(`   📊 成功率: ${Math.round(passedTests / totalTests * 100)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！后端API服务正常运行');
  } else if (passedTests > 0) {
    console.log('\n⚠️ 部分测试通过，可能存在配置问题');
  } else {
    console.log('\n🚨 所有测试失败，请检查服务器状态');
  }

  console.log('\n📚 可用的API端点:');
  console.log('   • GET  /health - 健康检查');
  console.log('   • GET  /docs - API文档');
  console.log('   • GET  /v1/questions - 获取题目列表');
  console.log('   • POST /v1/game-sessions - 创建游戏会话');
  console.log('   • POST /v1/auth/wechat/login - 微信登录');
  console.log('   • GET  /v1/auth/me - 获取当前用户信息');
  console.log('   • GET  /v1/users/{userId} - 获取用户信息');
  console.log('   • GET  /v1/audio/{resourceId} - 获取音频资源');
}

// 运行测试
runTests().catch(console.error);