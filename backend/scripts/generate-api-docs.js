#!/usr/bin/env node

/**
 * API文档自动生成脚本
 * 基于代码注释和路由配置自动生成OpenAPI 3.0文档
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class APIDocGenerator {
  constructor() {
    this.apiSpec = {
      openapi: '3.0.0',
      info: {
        title: '家乡话猜猜猜游戏API',
        version: '1.0.0',
        description: '家乡话猜猜猜游戏后端API文档',
        contact: {
          name: 'API Support',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: 'https://api.dialectgame.com/v1',
          description: '生产环境'
        },
        {
          url: 'https://test-api.dialectgame.com/v1',
          description: '测试环境'
        },
        {
          url: 'http://localhost:3001/v1',
          description: '开发环境'
        }
      ],
      paths: {},
      components: {
        schemas: {},
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      }
    };
    
    this.serverlessPath = path.join(__dirname, '../serverless');
  }

  /**
   * 生成完整的API文档
   */
  async generateDocs() {
    console.log('🚀 开始生成API文档...');
    
    try {
      // 1. 扫描所有handler文件
      await this.scanHandlers();
      
      // 2. 扫描数据模型
      await this.scanModels();
      
      // 3. 生成OpenAPI文档
      await this.generateOpenAPISpec();
      
      // 4. 生成HTML文档
      await this.generateHTMLDocs();
      
      console.log('✅ API文档生成完成！');
      console.log('📄 OpenAPI规范: docs/api/openapi.yaml');
      console.log('🌐 HTML文档: docs/api/index.html');
      
    } catch (error) {
      console.error('❌ 文档生成失败:', error);
      process.exit(1);
    }
  }

  /**
   * 扫描handler文件
   */
  async scanHandlers() {
    const services = ['auth', 'user', 'game', 'audio', 'leaderboard', 'social'];
    
    for (const service of services) {
      const handlerPath = path.join(this.serverlessPath, service, 'handler.js');
      
      if (fs.existsSync(handlerPath)) {
        console.log(`📖 扫描 ${service} 服务...`);
        await this.parseHandler(handlerPath, service);
      }
    }
  }

  /**
   * 解析handler文件
   */
  async parseHandler(filePath, serviceName) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取路由定义
    const routesMatch = content.match(/const routes = \{([\s\S]*?)\};/);
    if (!routesMatch) return;
    
    const routesContent = routesMatch[1];
    const routeLines = routesContent.split('\n').filter(line => line.trim());
    
    for (const line of routeLines) {
      const routeMatch = line.match(/'([A-Z]+)\s+([^']+)':\s*(\w+)/);
      if (routeMatch) {
        const [, method, path, handlerName] = routeMatch;
        await this.parseEndpoint(content, method, path, handlerName, serviceName);
      }
    }
  }

  /**
   * 解析API端点
   */
  async parseEndpoint(content, method, path, handlerName, serviceName) {
    // 查找函数定义和注释
    const functionRegex = new RegExp(`\\/\\*\\*([\\s\\S]*?)\\*\\/[\\s\\S]*?const ${handlerName}`, 'g');
    const match = functionRegex.exec(content);
    
    if (!match) return;
    
    const comment = match[1];
    const endpoint = this.parseEndpointComment(comment, method, path, serviceName);
    
    // 标准化路径
    const normalizedPath = path.replace(/\{(\w+)\}/g, '{$1}');
    
    if (!this.apiSpec.paths[normalizedPath]) {
      this.apiSpec.paths[normalizedPath] = {};
    }
    
    this.apiSpec.paths[normalizedPath][method.toLowerCase()] = endpoint;
  }

  /**
   * 解析端点注释
   */
  parseEndpointComment(comment, method, path, serviceName) {
    const lines = comment.split('\n').map(line => line.trim().replace(/^\*\s?/, ''));
    
    const endpoint = {
      tags: [serviceName],
      summary: '',
      description: '',
      parameters: [],
      responses: {
        '200': {
          description: '成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: { type: 'integer', example: 0 },
                  message: { type: 'string', example: 'success' },
                  data: { type: 'object' },
                  timestamp: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        },
        '400': { description: '请求参数错误' },
        '401': { description: '未授权' },
        '403': { description: '权限不足' },
        '404': { description: '资源不存在' },
        '500': { description: '服务器内部错误' }
      }
    };

    // 解析注释内容
    let currentSection = 'summary';
    
    for (const line of lines) {
      if (line.startsWith('@param')) {
        const paramMatch = line.match(/@param\s+\{(\w+)\}\s+(\w+)\s+(.*)/);
        if (paramMatch) {
          const [, type, name, description] = paramMatch;
          endpoint.parameters.push({
            name,
            in: path.includes(`{${name}}`) ? 'path' : 'query',
            required: path.includes(`{${name}}`),
            schema: { type: type.toLowerCase() },
            description
          });
        }
      } else if (line.startsWith('@returns')) {
        // 处理返回值描述
        currentSection = 'returns';
      } else if (line.startsWith('@')) {
        // 其他注解
        currentSection = 'other';
      } else if (line && currentSection === 'summary' && !endpoint.summary) {
        endpoint.summary = line;
      } else if (line && currentSection === 'summary') {
        endpoint.description += (endpoint.description ? '\n' : '') + line;
      }
    }

    // 添加认证要求
    if (this.requiresAuth(method, path)) {
      endpoint.security = [{ bearerAuth: [] }];
    }

    // 添加请求体（对于POST/PUT请求）
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      endpoint.requestBody = {
        required: true,
        content: {
          'application/json': {
            schema: { type: 'object' }
          }
        }
      };
    }

    return endpoint;
  }

  /**
   * 扫描数据模型
   */
  async scanModels() {
    const modelsPath = path.join(this.serverlessPath, 'models');
    
    if (!fs.existsSync(modelsPath)) return;
    
    const modelFiles = fs.readdirSync(modelsPath).filter(file => file.endsWith('.js'));
    
    for (const file of modelFiles) {
      const modelPath = path.join(modelsPath, file);
      const modelName = path.basename(file, '.js');
      
      console.log(`📋 扫描模型 ${modelName}...`);
      await this.parseModel(modelPath, modelName);
    }
  }

  /**
   * 解析数据模型
   */
  async parseModel(filePath, modelName) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 查找构造函数
    const constructorMatch = content.match(/constructor\(data = \{\}\) \{([\s\S]*?)\}/);
    if (!constructorMatch) return;
    
    const constructorContent = constructorMatch[1];
    const properties = {};
    
    // 提取属性定义
    const propertyRegex = /this\.(\w+) = data\.(\w+)(?:\s*\|\|\s*(.+?))?;/g;
    let match;
    
    while ((match = propertyRegex.exec(constructorContent)) !== null) {
      const [, propName, , defaultValue] = match;
      
      properties[propName] = {
        type: this.inferType(defaultValue),
        description: `${propName}字段`
      };
      
      if (defaultValue !== undefined) {
        properties[propName].example = this.parseDefaultValue(defaultValue);
      }
    }
    
    this.apiSpec.components.schemas[modelName] = {
      type: 'object',
      properties,
      description: `${modelName}数据模型`
    };
  }

  /**
   * 推断数据类型
   */
  inferType(defaultValue) {
    if (!defaultValue) return 'string';
    
    if (defaultValue.includes('0') || defaultValue.includes('null')) return 'integer';
    if (defaultValue.includes('false') || defaultValue.includes('true')) return 'boolean';
    if (defaultValue.includes('[]')) return 'array';
    if (defaultValue.includes('{}')) return 'object';
    
    return 'string';
  }

  /**
   * 解析默认值
   */
  parseDefaultValue(defaultValue) {
    if (!defaultValue) return null;
    
    try {
      return eval(defaultValue);
    } catch {
      return defaultValue.replace(/['"]/g, '');
    }
  }

  /**
   * 检查是否需要认证
   */
  requiresAuth(method, path) {
    // 登录相关接口不需要认证
    if (path.includes('/auth/wechat/login') || path.includes('/auth/refresh')) {
      return false;
    }
    
    // 其他接口默认需要认证
    return true;
  }

  /**
   * 生成OpenAPI规范文件
   */
  async generateOpenAPISpec() {
    const docsDir = path.join(__dirname, '../docs/api');
    
    // 确保目录存在
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    // 生成YAML文件
    const yamlContent = yaml.dump(this.apiSpec, { indent: 2 });
    fs.writeFileSync(path.join(docsDir, 'openapi.yaml'), yamlContent);
    
    // 生成JSON文件
    fs.writeFileSync(
      path.join(docsDir, 'openapi.json'), 
      JSON.stringify(this.apiSpec, null, 2)
    );
  }

  /**
   * 生成HTML文档
   */
  async generateHTMLDocs() {
    const htmlTemplate = `
<!DOCTYPE html>
<html>
<head>
  <title>家乡话猜猜猜 API 文档</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
  <style>
    html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
    *, *:before, *:after { box-sizing: inherit; }
    body { margin:0; background: #fafafa; }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: './openapi.yaml',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout"
      });
    };
  </script>
</body>
</html>`;
    
    const docsDir = path.join(__dirname, '../docs/api');
    fs.writeFileSync(path.join(docsDir, 'index.html'), htmlTemplate);
  }
}

// 执行文档生成
if (require.main === module) {
  const generator = new APIDocGenerator();
  generator.generateDocs();
}

module.exports = { APIDocGenerator };
