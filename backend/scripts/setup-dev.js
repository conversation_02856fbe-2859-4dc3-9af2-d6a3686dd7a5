#!/usr/bin/env node

/**
 * 开发环境快速设置脚本
 * 一键完成开发环境的所有设置
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

class DevSetup {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.envFile = path.join(this.projectRoot, '.env');
    this.envExampleFile = path.join(this.projectRoot, '.env.example');
  }

  async setup() {
    console.log('🚀 家乡话猜猜猜 - 开发环境设置');
    console.log('=====================================');
    console.log('');

    try {
      // 1. 检查环境配置
      await this.checkEnvironment();
      
      // 2. 安装依赖
      await this.installDependencies();
      
      // 3. 配置环境变量
      await this.setupEnvironmentVariables();
      
      // 4. 初始化数据库
      await this.initializeDatabase();
      
      // 5. 启动开发服务器
      await this.startDevServer();
      
    } catch (error) {
      console.error('❌ 设置失败:', error.message);
      process.exit(1);
    } finally {
      rl.close();
    }
  }

  async checkEnvironment() {
    console.log('🔍 检查开发环境...');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js版本过低 (${nodeVersion})，需要 >= 18.0.0`);
    }
    
    console.log(`✅ Node.js版本: ${nodeVersion}`);
    
    // 检查必需的工具
    const requiredTools = ['npm', 'mysql'];
    for (const tool of requiredTools) {
      try {
        await this.execAsync(`which ${tool}`);
        console.log(`✅ ${tool} 已安装`);
      } catch (error) {
        console.warn(`⚠️  ${tool} 未找到，可能需要手动安装`);
      }
    }
    
    console.log('');
  }

  async installDependencies() {
    console.log('📦 安装项目依赖...');
    
    try {
      await this.execAsync('npm install', { cwd: this.projectRoot });
      console.log('✅ 依赖安装完成');
    } catch (error) {
      throw new Error(`依赖安装失败: ${error.message}`);
    }
    
    console.log('');
  }

  async setupEnvironmentVariables() {
    console.log('⚙️  配置环境变量...');
    
    if (fs.existsSync(this.envFile)) {
      const answer = await this.question('发现已存在的 .env 文件，是否重新配置？(y/N): ');
      if (answer.toLowerCase() !== 'y') {
        console.log('✅ 使用现有的环境配置');
        console.log('');
        return;
      }
    }
    
    // 复制示例配置
    if (fs.existsSync(this.envExampleFile)) {
      fs.copyFileSync(this.envExampleFile, this.envFile);
    }
    
    // 交互式配置关键环境变量
    const envConfig = await this.collectEnvironmentConfig();
    await this.updateEnvFile(envConfig);
    
    console.log('✅ 环境变量配置完成');
    console.log('');
  }

  async collectEnvironmentConfig() {
    console.log('请输入以下配置信息 (直接回车使用默认值):');
    
    const config = {};
    
    // 数据库配置
    config.DB_HOST = await this.question('数据库主机 (localhost): ') || 'localhost';
    config.DB_PORT = await this.question('数据库端口 (3306): ') || '3306';
    config.DB_USER = await this.question('数据库用户名 (root): ') || 'root';
    config.DB_PASSWORD = await this.question('数据库密码: ', true);
    config.DB_NAME = await this.question('数据库名称 (hometown_dialect_game): ') || 'hometown_dialect_game';
    
    // Redis配置
    config.REDIS_HOST = await this.question('Redis主机 (localhost): ') || 'localhost';
    config.REDIS_PORT = await this.question('Redis端口 (6379): ') || '6379';
    config.REDIS_PASSWORD = await this.question('Redis密码 (可选): ');
    
    // JWT配置
    config.JWT_ACCESS_SECRET = this.generateSecret();
    config.JWT_REFRESH_SECRET = this.generateSecret();
    
    // 微信配置
    config.WECHAT_APP_ID = await this.question('微信小程序AppID: ');
    config.WECHAT_APP_SECRET = await this.question('微信小程序AppSecret: ', true);
    
    return config;
  }

  async updateEnvFile(config) {
    let envContent = fs.readFileSync(this.envFile, 'utf8');
    
    Object.keys(config).forEach(key => {
      const value = config[key];
      if (value) {
        const regex = new RegExp(`^${key}=.*$`, 'm');
        if (regex.test(envContent)) {
          envContent = envContent.replace(regex, `${key}=${value}`);
        } else {
          envContent += `\n${key}=${value}`;
        }
      }
    });
    
    fs.writeFileSync(this.envFile, envContent);
  }

  async initializeDatabase() {
    console.log('🗄️  初始化数据库...');
    
    const answer = await this.question('是否初始化数据库？(Y/n): ');
    if (answer.toLowerCase() === 'n') {
      console.log('⏭️  跳过数据库初始化');
      console.log('');
      return;
    }
    
    try {
      // 加载环境变量
      require('dotenv').config({ path: this.envFile });
      
      // 运行数据库初始化脚本
      const DatabaseInitializer = require('./init-db');
      const initializer = new DatabaseInitializer();
      await initializer.init();
      
    } catch (error) {
      console.warn(`⚠️  数据库初始化失败: ${error.message}`);
      console.log('💡 您可以稍后手动运行: npm run db:init');
    }
    
    console.log('');
  }

  async startDevServer() {
    console.log('🚀 启动开发服务器...');
    
    const answer = await this.question('是否现在启动开发服务器？(Y/n): ');
    if (answer.toLowerCase() === 'n') {
      console.log('');
      console.log('✅ 开发环境设置完成！');
      console.log('');
      console.log('下一步操作:');
      console.log('• 运行 npm run dev 启动开发服务器');
      console.log('• 访问 http://localhost:3000/docs 查看API文档');
      console.log('• 访问 http://localhost:3000/health 检查服务健康状态');
      return;
    }
    
    console.log('');
    console.log('✅ 开发环境设置完成！正在启动服务器...');
    console.log('');
    
    // 启动开发服务器
    const devServer = spawn('npm', ['run', 'dev'], {
      cwd: this.projectRoot,
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    devServer.on('error', (error) => {
      console.error('启动开发服务器失败:', error);
    });
    
    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在关闭开发服务器...');
      devServer.kill('SIGINT');
      process.exit(0);
    });
  }

  async question(prompt, hidden = false) {
    return new Promise((resolve) => {
      if (hidden) {
        // 隐藏输入 (用于密码)
        const stdin = process.stdin;
        const stdout = process.stdout;
        
        stdout.write(prompt);
        stdin.setRawMode(true);
        stdin.resume();
        stdin.setEncoding('utf8');
        
        let input = '';
        
        const onData = (char) => {
          switch (char) {
            case '\n':
            case '\r':
            case '\u0004': // Ctrl+D
              stdin.setRawMode(false);
              stdin.pause();
              stdin.removeListener('data', onData);
              stdout.write('\n');
              resolve(input);
              break;
            case '\u0003': // Ctrl+C
              process.exit();
              break;
            case '\u007f': // Backspace
              if (input.length > 0) {
                input = input.slice(0, -1);
                stdout.write('\b \b');
              }
              break;
            default:
              input += char;
              stdout.write('*');
              break;
          }
        };
        
        stdin.on('data', onData);
      } else {
        rl.question(prompt, resolve);
      }
    });
  }

  async execAsync(command, options = {}) {
    return new Promise((resolve, reject) => {
      exec(command, options, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout.trim());
        }
      });
    });
  }

  generateSecret(length = 64) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// 主函数
async function main() {
  const setup = new DevSetup();
  await setup.setup();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('设置失败:', error);
    process.exit(1);
  });
}

module.exports = DevSetup;