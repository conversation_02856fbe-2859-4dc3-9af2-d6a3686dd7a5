#!/usr/bin/env node

/**
 * API文档同步脚本
 * 监控代码变化并自动更新API文档
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const { APIDocGenerator } = require('./generate-api-docs');

class APIDocSyncer {
  constructor() {
    this.generator = new APIDocGenerator();
    this.watchPaths = [
      'serverless/**/*.js',
      'database/models/*.js'
    ];
    this.debounceTimeout = null;
    this.debounceDelay = 2000; // 2秒防抖
  }

  /**
   * 启动文档同步监控
   */
  start() {
    console.log('🚀 启动API文档同步监控...');
    
    // 初始生成文档
    this.generateDocs();
    
    // 监控文件变化
    const watcher = chokidar.watch(this.watchPaths, {
      ignored: /(^|[\/\\])\../, // 忽略隐藏文件
      persistent: true,
      cwd: process.cwd()
    });

    watcher
      .on('change', (filePath) => {
        console.log(`📝 文件变化: ${filePath}`);
        this.debouncedGenerate();
      })
      .on('add', (filePath) => {
        console.log(`➕ 新增文件: ${filePath}`);
        this.debouncedGenerate();
      })
      .on('unlink', (filePath) => {
        console.log(`🗑️ 删除文件: ${filePath}`);
        this.debouncedGenerate();
      })
      .on('ready', () => {
        console.log('👀 开始监控文件变化...');
      })
      .on('error', (error) => {
        console.error('❌ 文件监控错误:', error);
      });

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止API文档同步监控...');
      watcher.close();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 停止API文档同步监控...');
      watcher.close();
      process.exit(0);
    });
  }

  /**
   * 防抖生成文档
   */
  debouncedGenerate() {
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }

    this.debounceTimeout = setTimeout(() => {
      this.generateDocs();
    }, this.debounceDelay);
  }

  /**
   * 生成API文档
   */
  async generateDocs() {
    try {
      console.log('📚 正在更新API文档...');
      
      const startTime = Date.now();
      await this.generator.generate();
      const duration = Date.now() - startTime;
      
      console.log(`✅ API文档更新完成 (${duration}ms)`);
      
      // 记录更新时间
      await this.recordUpdate();
      
    } catch (error) {
      console.error('❌ API文档生成失败:', error);
    }
  }

  /**
   * 记录文档更新
   */
  async recordUpdate() {
    const updateRecord = {
      timestamp: new Date().toISOString(),
      version: this.generator.apiSpec.info.version,
      generator: 'auto-sync'
    };

    const recordPath = path.join(process.cwd(), 'docs/api/update-history.json');
    
    try {
      let history = [];
      if (fs.existsSync(recordPath)) {
        const content = fs.readFileSync(recordPath, 'utf8');
        history = JSON.parse(content);
      }
      
      history.unshift(updateRecord);
      
      // 只保留最近50次更新记录
      if (history.length > 50) {
        history = history.slice(0, 50);
      }
      
      fs.writeFileSync(recordPath, JSON.stringify(history, null, 2));
      
    } catch (error) {
      console.warn('⚠️ 记录更新历史失败:', error.message);
    }
  }

  /**
   * 验证API文档
   */
  async validateDocs() {
    const yamlPath = path.join(process.cwd(), 'docs/api/openapi.yaml');
    const htmlPath = path.join(process.cwd(), 'docs/api/index.html');
    
    const issues = [];
    
    // 检查文件是否存在
    if (!fs.existsSync(yamlPath)) {
      issues.push('OpenAPI YAML文件不存在');
    }
    
    if (!fs.existsSync(htmlPath)) {
      issues.push('HTML文档文件不存在');
    }
    
    // 检查YAML格式
    if (fs.existsSync(yamlPath)) {
      try {
        const yamlContent = fs.readFileSync(yamlPath, 'utf8');
        const parsed = require('js-yaml').load(yamlContent);
        
        if (!parsed.openapi) {
          issues.push('缺少OpenAPI版本信息');
        }
        
        if (!parsed.info || !parsed.info.title) {
          issues.push('缺少API标题信息');
        }
        
        if (!parsed.paths || Object.keys(parsed.paths).length === 0) {
          issues.push('没有定义API路径');
        }
        
      } catch (error) {
        issues.push(`YAML格式错误: ${error.message}`);
      }
    }
    
    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * 生成文档统计信息
   */
  async generateStats() {
    const yamlPath = path.join(process.cwd(), 'docs/api/openapi.yaml');
    
    if (!fs.existsSync(yamlPath)) {
      return null;
    }
    
    try {
      const yamlContent = fs.readFileSync(yamlPath, 'utf8');
      const spec = require('js-yaml').load(yamlContent);
      
      const stats = {
        timestamp: new Date().toISOString(),
        version: spec.info?.version || 'unknown',
        totalPaths: Object.keys(spec.paths || {}).length,
        totalOperations: 0,
        totalSchemas: Object.keys(spec.components?.schemas || {}).length,
        operationsByMethod: {},
        operationsByTag: {}
      };
      
      // 统计操作
      Object.values(spec.paths || {}).forEach(pathItem => {
        Object.entries(pathItem).forEach(([method, operation]) => {
          if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
            stats.totalOperations++;
            
            // 按方法统计
            stats.operationsByMethod[method.toUpperCase()] = 
              (stats.operationsByMethod[method.toUpperCase()] || 0) + 1;
            
            // 按标签统计
            if (operation.tags) {
              operation.tags.forEach(tag => {
                stats.operationsByTag[tag] = (stats.operationsByTag[tag] || 0) + 1;
              });
            }
          }
        });
      });
      
      return stats;
      
    } catch (error) {
      console.error('生成文档统计失败:', error);
      return null;
    }
  }

  /**
   * 部署文档到静态服务器
   */
  async deployDocs(target = 'local') {
    console.log(`🚀 部署API文档到 ${target}...`);
    
    const docsDir = path.join(process.cwd(), 'docs/api');
    
    switch (target) {
      case 'local':
        // 本地部署 - 启动简单的HTTP服务器
        await this.startLocalServer(docsDir);
        break;
        
      case 'github':
        // GitHub Pages部署
        await this.deployToGitHub(docsDir);
        break;
        
      case 's3':
        // AWS S3部署
        await this.deployToS3(docsDir);
        break;
        
      default:
        console.warn(`不支持的部署目标: ${target}`);
    }
  }

  /**
   * 启动本地文档服务器
   */
  async startLocalServer(docsDir, port = 8080) {
    const http = require('http');
    const url = require('url');
    const mime = require('mime-types');
    
    const server = http.createServer((req, res) => {
      const parsedUrl = url.parse(req.url);
      let pathname = parsedUrl.pathname;
      
      // 默认页面
      if (pathname === '/') {
        pathname = '/index.html';
      }
      
      const filePath = path.join(docsDir, pathname);
      
      fs.readFile(filePath, (err, data) => {
        if (err) {
          res.writeHead(404);
          res.end('Not Found');
          return;
        }
        
        const mimeType = mime.lookup(filePath) || 'text/plain';
        res.writeHead(200, { 'Content-Type': mimeType });
        res.end(data);
      });
    });
    
    server.listen(port, () => {
      console.log(`📖 API文档服务器启动: http://localhost:${port}`);
    });
    
    return server;
  }

  /**
   * 部署到GitHub Pages
   */
  async deployToGitHub(docsDir) {
    // 这里可以实现GitHub Pages部署逻辑
    console.log('GitHub Pages部署功能待实现');
  }

  /**
   * 部署到AWS S3
   */
  async deployToS3(docsDir) {
    // 这里可以实现S3部署逻辑
    console.log('S3部署功能待实现');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const syncer = new APIDocSyncer();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'watch':
      syncer.start();
      break;
      
    case 'generate':
      syncer.generateDocs();
      break;
      
    case 'validate':
      syncer.validateDocs().then(result => {
        if (result.valid) {
          console.log('✅ API文档验证通过');
        } else {
          console.log('❌ API文档验证失败:');
          result.issues.forEach(issue => console.log(`  - ${issue}`));
        }
      });
      break;
      
    case 'stats':
      syncer.generateStats().then(stats => {
        if (stats) {
          console.log('📊 API文档统计:');
          console.log(JSON.stringify(stats, null, 2));
        }
      });
      break;
      
    case 'serve':
      const port = process.argv[3] || 8080;
      syncer.deployDocs('local');
      break;
      
    default:
      console.log('用法:');
      console.log('  node sync-api-docs.js watch    # 监控文件变化并自动更新文档');
      console.log('  node sync-api-docs.js generate # 生成API文档');
      console.log('  node sync-api-docs.js validate # 验证API文档');
      console.log('  node sync-api-docs.js stats    # 显示文档统计');
      console.log('  node sync-api-docs.js serve    # 启动本地文档服务器');
  }
}

module.exports = { APIDocSyncer };
