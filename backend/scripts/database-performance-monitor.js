#!/usr/bin/env node

/**
 * 数据库性能监控脚本
 * 监控数据库连接、查询性能、慢查询等指标
 */

const { DatabaseManager } = require('../serverless/utils/database');
const { DatabaseOptimizer } = require('../serverless/utils/database-optimizer');
const { RedisManager } = require('../serverless/utils/redis');

class DatabasePerformanceMonitor {
  constructor() {
    this.db = DatabaseManager.getInstance();
    this.optimizer = new DatabaseOptimizer(this.db.config);
    this.redis = RedisManager.getInstance();
    this.monitoringInterval = 60000; // 1分钟
    this.isRunning = false;
  }

  /**
   * 启动性能监控
   */
  async start() {
    console.log('🚀 启动数据库性能监控...');
    this.isRunning = true;
    
    // 初始化监控
    await this.initializeMonitoring();
    
    // 定期监控
    this.monitoringTimer = setInterval(async () => {
      try {
        await this.collectMetrics();
      } catch (error) {
        console.error('监控数据收集失败:', error);
      }
    }, this.monitoringInterval);

    console.log('✅ 数据库性能监控已启动');
  }

  /**
   * 停止性能监控
   */
  async stop() {
    console.log('🛑 停止数据库性能监控...');
    this.isRunning = false;
    
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    
    console.log('✅ 数据库性能监控已停止');
  }

  /**
   * 初始化监控
   */
  async initializeMonitoring() {
    // 创建监控表（如果不存在）
    await this.createMonitoringTables();
    
    // 清理旧数据
    await this.cleanupOldData();
  }

  /**
   * 收集性能指标
   */
  async collectMetrics() {
    const timestamp = new Date().toISOString();
    
    try {
      // 收集数据库状态
      const dbStatus = await this.collectDatabaseStatus();
      
      // 收集连接池状态
      const poolStatus = await this.collectConnectionPoolStatus();
      
      // 收集慢查询统计
      const slowQueries = await this.collectSlowQueryStats();
      
      // 收集查询缓存统计
      const cacheStats = await this.collectCacheStats();
      
      // 汇总指标
      const metrics = {
        timestamp,
        database: dbStatus,
        connectionPool: poolStatus,
        slowQueries,
        cache: cacheStats
      };
      
      // 存储指标
      await this.storeMetrics(metrics);
      
      // 检查告警条件
      await this.checkAlerts(metrics);
      
      console.log(`📊 [${timestamp}] 数据库性能指标已收集`);
      
    } catch (error) {
      console.error('收集性能指标失败:', error);
    }
  }

  /**
   * 收集数据库状态
   */
  async collectDatabaseStatus() {
    const queries = [
      'SHOW STATUS LIKE "Threads_connected"',
      'SHOW STATUS LIKE "Threads_running"',
      'SHOW STATUS LIKE "Questions"',
      'SHOW STATUS LIKE "Uptime"',
      'SHOW STATUS LIKE "Slow_queries"',
      'SHOW STATUS LIKE "Innodb_buffer_pool_read_requests"',
      'SHOW STATUS LIKE "Innodb_buffer_pool_reads"'
    ];

    const status = {};
    
    for (const query of queries) {
      try {
        const result = await this.db.query(query);
        if (result.length > 0) {
          const key = result[0].Variable_name.toLowerCase();
          status[key] = parseInt(result[0].Value) || result[0].Value;
        }
      } catch (error) {
        console.warn(`获取状态失败: ${query}`, error.message);
      }
    }

    return status;
  }

  /**
   * 收集连接池状态
   */
  async collectConnectionPoolStatus() {
    const pool = this.db.pool;
    
    return {
      totalConnections: pool.config.connectionLimit,
      activeConnections: pool._allConnections ? pool._allConnections.length : 0,
      idleConnections: pool._freeConnections ? pool._freeConnections.length : 0,
      queuedRequests: pool._connectionQueue ? pool._connectionQueue.length : 0
    };
  }

  /**
   * 收集慢查询统计
   */
  async collectSlowQueryStats() {
    try {
      // 从Redis获取慢查询数据
      const slowQueries = await this.redis.lrange('db:slow_queries', 0, -1);
      const recentSlowQueries = slowQueries
        .map(q => JSON.parse(q))
        .filter(q => Date.now() - new Date(q.timestamp).getTime() < 3600000); // 最近1小时

      return {
        total: recentSlowQueries.length,
        averageDuration: recentSlowQueries.length > 0 
          ? recentSlowQueries.reduce((sum, q) => sum + q.duration, 0) / recentSlowQueries.length 
          : 0,
        maxDuration: recentSlowQueries.length > 0 
          ? Math.max(...recentSlowQueries.map(q => q.duration)) 
          : 0
      };
    } catch (error) {
      console.warn('获取慢查询统计失败:', error.message);
      return { total: 0, averageDuration: 0, maxDuration: 0 };
    }
  }

  /**
   * 收集缓存统计
   */
  async collectCacheStats() {
    try {
      const cacheKeys = await this.redis.keys('query_cache:*');
      const cacheHits = await this.redis.get('cache:hits') || 0;
      const cacheMisses = await this.redis.get('cache:misses') || 0;
      const totalRequests = parseInt(cacheHits) + parseInt(cacheMisses);
      
      return {
        totalKeys: cacheKeys.length,
        hits: parseInt(cacheHits),
        misses: parseInt(cacheMisses),
        hitRate: totalRequests > 0 ? (parseInt(cacheHits) / totalRequests) * 100 : 0
      };
    } catch (error) {
      console.warn('获取缓存统计失败:', error.message);
      return { totalKeys: 0, hits: 0, misses: 0, hitRate: 0 };
    }
  }

  /**
   * 存储性能指标
   */
  async storeMetrics(metrics) {
    // 存储到Redis
    const key = `db:metrics:${Date.now()}`;
    await this.redis.setex(key, 86400, JSON.stringify(metrics)); // 保存24小时
    
    // 添加到时间序列
    await this.redis.zadd('db:metrics:timeline', Date.now(), key);
    
    // 清理旧数据
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
    await this.redis.zremrangebyscore('db:metrics:timeline', 0, cutoff);
  }

  /**
   * 检查告警条件
   */
  async checkAlerts(metrics) {
    const alerts = [];
    
    // 检查连接数告警
    const { connectionPool } = metrics;
    const connectionUsage = (connectionPool.activeConnections / connectionPool.totalConnections) * 100;
    
    if (connectionUsage > 80) {
      alerts.push({
        type: 'high_connection_usage',
        severity: 'warning',
        message: `数据库连接使用率过高: ${connectionUsage.toFixed(1)}%`,
        value: connectionUsage
      });
    }
    
    // 检查慢查询告警
    if (metrics.slowQueries.total > 10) {
      alerts.push({
        type: 'too_many_slow_queries',
        severity: 'warning',
        message: `慢查询过多: ${metrics.slowQueries.total}个`,
        value: metrics.slowQueries.total
      });
    }
    
    // 检查缓存命中率告警
    if (metrics.cache.hitRate < 50 && metrics.cache.hits + metrics.cache.misses > 100) {
      alerts.push({
        type: 'low_cache_hit_rate',
        severity: 'info',
        message: `缓存命中率较低: ${metrics.cache.hitRate.toFixed(1)}%`,
        value: metrics.cache.hitRate
      });
    }
    
    // 发送告警
    for (const alert of alerts) {
      await this.sendAlert(alert);
    }
  }

  /**
   * 发送告警
   */
  async sendAlert(alert) {
    console.warn(`🚨 数据库告警 [${alert.severity}]: ${alert.message}`);
    
    // 存储告警记录
    await this.redis.lpush('db:alerts', JSON.stringify({
      ...alert,
      timestamp: new Date().toISOString()
    }));
    
    // 限制告警记录数量
    await this.redis.ltrim('db:alerts', 0, 99);
  }

  /**
   * 创建监控表
   */
  async createMonitoringTables() {
    // 这里可以创建专门的监控表，如果需要的话
    // 目前使用Redis存储监控数据
  }

  /**
   * 清理旧数据
   */
  async cleanupOldData() {
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7天前
    
    // 清理旧的指标数据
    await this.redis.zremrangebyscore('db:metrics:timeline', 0, cutoff);
    
    // 清理慢查询记录
    const slowQueries = await this.redis.lrange('db:slow_queries', 0, -1);
    const validQueries = slowQueries
      .map(q => JSON.parse(q))
      .filter(q => Date.now() - new Date(q.timestamp).getTime() < 7 * 24 * 60 * 60 * 1000);
    
    await this.redis.del('db:slow_queries');
    for (const query of validQueries) {
      await this.redis.lpush('db:slow_queries', JSON.stringify(query));
    }
  }

  /**
   * 生成性能报告
   */
  async generateReport(hours = 24) {
    const endTime = Date.now();
    const startTime = endTime - (hours * 60 * 60 * 1000);
    
    // 获取时间范围内的指标
    const metricKeys = await this.redis.zrangebyscore('db:metrics:timeline', startTime, endTime);
    const metrics = [];
    
    for (const key of metricKeys) {
      const data = await this.redis.get(key);
      if (data) {
        metrics.push(JSON.parse(data));
      }
    }
    
    if (metrics.length === 0) {
      return { message: '没有找到指定时间范围内的数据' };
    }
    
    // 计算统计信息
    const report = {
      timeRange: { start: new Date(startTime).toISOString(), end: new Date(endTime).toISOString() },
      summary: {
        totalDataPoints: metrics.length,
        averageConnections: this.calculateAverage(metrics.map(m => m.connectionPool?.activeConnections || 0)),
        totalSlowQueries: metrics.reduce((sum, m) => sum + (m.slowQueries?.total || 0), 0),
        averageCacheHitRate: this.calculateAverage(metrics.map(m => m.cache?.hitRate || 0))
      },
      trends: {
        connections: metrics.map(m => ({ timestamp: m.timestamp, value: m.connectionPool?.activeConnections || 0 })),
        slowQueries: metrics.map(m => ({ timestamp: m.timestamp, value: m.slowQueries?.total || 0 })),
        cacheHitRate: metrics.map(m => ({ timestamp: m.timestamp, value: m.cache?.hitRate || 0 }))
      }
    };
    
    return report;
  }

  /**
   * 计算平均值
   */
  calculateAverage(values) {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const monitor = new DatabasePerformanceMonitor();
  
  // 处理退出信号
  process.on('SIGINT', async () => {
    await monitor.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await monitor.stop();
    process.exit(0);
  });
  
  // 启动监控
  monitor.start().catch(error => {
    console.error('启动数据库性能监控失败:', error);
    process.exit(1);
  });
}

module.exports = { DatabasePerformanceMonitor };
