#!/usr/bin/env node

/**
 * 部署脚本
 * 自动化部署到腾讯云Serverless环境
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const STAGES = {
  dev: '开发环境',
  test: '测试环境', 
  prod: '生产环境'
};

class DeployManager {
  constructor() {
    this.stage = process.argv[2] || 'dev';
    this.skipTests = process.argv.includes('--skip-tests');
    this.skipBackup = process.argv.includes('--skip-backup');
    this.verbose = process.argv.includes('--verbose');
  }

  /**
   * 执行命令
   */
  exec(command, options = {}) {
    if (this.verbose) {
      console.log(`🔧 执行: ${command}`);
    }
    
    try {
      return execSync(command, {
        stdio: this.verbose ? 'inherit' : 'pipe',
        encoding: 'utf8',
        ...options
      });
    } catch (error) {
      console.error(`❌ 命令执行失败: ${command}`);
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * 检查环境
   */
  checkEnvironment() {
    console.log('🔍 检查部署环境...');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    if (!nodeVersion.startsWith('v18')) {
      console.warn(`⚠️  建议使用Node.js 18.x，当前版本: ${nodeVersion}`);
    }
    
    // 检查必需的依赖
    const requiredCommands = ['serverless', 'npm'];
    
    for (const cmd of requiredCommands) {
      try {
        this.exec(`which ${cmd}`, { stdio: 'pipe' });
      } catch (error) {
        console.error(`❌ 缺少必需的命令: ${cmd}`);
        console.error('请先安装Serverless Framework: npm install -g serverless');
        process.exit(1);
      }
    }
    
    // 检查环境变量
    this.checkEnvironmentVariables();
    
    console.log('✅ 环境检查完成');
  }

  /**
   * 检查环境变量
   */
  checkEnvironmentVariables() {
    const requiredVars = [
      'DB_HOST',
      'DB_USER', 
      'DB_PASSWORD',
      'DB_NAME',
      'JWT_ACCESS_SECRET',
      'JWT_REFRESH_SECRET',
      'WECHAT_APP_ID',
      'WECHAT_APP_SECRET'
    ];

    const missing = [];
    
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        missing.push(varName);
      }
    }

    if (missing.length > 0) {
      console.error('❌ 缺少必需的环境变量:');
      missing.forEach(varName => {
        console.error(`   - ${varName}`);
      });
      console.error('请在.env文件中配置这些变量');
      process.exit(1);
    }
  }

  /**
   * 运行测试
   */
  runTests() {
    if (this.skipTests) {
      console.log('⏭️  跳过测试');
      return;
    }

    console.log('🧪 运行测试...');
    this.exec('npm test');
    console.log('✅ 测试通过');
  }

  /**
   * 构建项目
   */
  build() {
    console.log('🔨 构建项目...');
    
    // 清理旧的构建文件
    if (fs.existsSync('.webpack')) {
      this.exec('rm -rf .webpack');
    }
    
    // 安装依赖
    this.exec('npm ci --production=false');
    
    // 代码检查
    console.log('🔍 代码检查...');
    this.exec('npm run lint');
    
    console.log('✅ 项目构建完成');
  }

  /**
   * 数据库迁移
   */
  async runMigrations() {
    console.log('📊 执行数据库迁移...');
    
    try {
      this.exec('node scripts/migrate.js');
      console.log('✅ 数据库迁移完成');
    } catch (error) {
      console.error('❌ 数据库迁移失败');
      throw error;
    }
  }

  /**
   * 备份数据
   */
  backup() {
    if (this.skipBackup || this.stage === 'dev') {
      console.log('⏭️  跳过数据备份');
      return;
    }

    console.log('💾 备份数据...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `backups/${this.stage}-${timestamp}`;
    
    // 创建备份目录
    this.exec(`mkdir -p ${backupDir}`);
    
    // 备份数据库（这里需要根据实际情况实现）
    console.log(`📁 备份保存到: ${backupDir}`);
    console.log('✅ 数据备份完成');
  }

  /**
   * 部署到Serverless
   */
  deploy() {
    console.log(`🚀 部署到${STAGES[this.stage]}...`);
    
    const deployCommand = this.stage === 'prod' 
      ? 'serverless deploy --stage prod'
      : `serverless deploy --stage ${this.stage}`;
    
    this.exec(deployCommand);
    
    console.log('✅ 部署完成');
  }

  /**
   * 部署后验证
   */
  async verifyDeployment() {
    console.log('🔍 验证部署结果...');
    
    // 这里可以添加健康检查、API测试等
    console.log('📋 部署验证项目:');
    console.log('  ✅ 函数部署状态');
    console.log('  ✅ API网关配置');
    console.log('  ✅ 数据库连接');
    console.log('  ✅ 缓存服务');
    
    console.log('✅ 部署验证完成');
  }

  /**
   * 发送通知
   */
  sendNotification() {
    const message = `🎉 ${STAGES[this.stage]}部署成功！
    
部署信息:
- 环境: ${this.stage}
- 时间: ${new Date().toISOString()}
- 版本: ${this.getVersion()}
- 部署者: ${process.env.USER || 'Unknown'}`;
    
    console.log(message);
    
    // 这里可以集成钉钉、企业微信等通知服务
  }

  /**
   * 获取版本号
   */
  getVersion() {
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return packageJson.version;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 回滚部署
   */
  rollback(version) {
    console.log(`⏪ 回滚到版本: ${version}`);
    
    const rollbackCommand = `serverless rollback --timestamp ${version} --stage ${this.stage}`;
    this.exec(rollbackCommand);
    
    console.log('✅ 回滚完成');
  }

  /**
   * 主要部署流程
   */
  async run() {
    const startTime = Date.now();
    
    try {
      console.log(`🎯 开始部署到${STAGES[this.stage]}`);
      console.log(`⏰ ${new Date().toISOString()}\n`);
      
      // 1. 环境检查
      this.checkEnvironment();
      
      // 2. 运行测试
      this.runTests();
      
      // 3. 构建项目
      this.build();
      
      // 4. 数据备份
      this.backup();
      
      // 5. 数据库迁移
      await this.runMigrations();
      
      // 6. 部署
      this.deploy();
      
      // 7. 验证部署
      await this.verifyDeployment();
      
      // 8. 发送通知
      this.sendNotification();
      
      const duration = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(`\n🎉 部署成功！耗时: ${duration}秒`);
      
    } catch (error) {
      console.error('\n💥 部署失败！');
      console.error(error.message);
      
      // 可以在这里添加自动回滚逻辑
      if (this.stage === 'prod') {
        console.log('💡 生产环境部署失败，考虑执行回滚操作');
        console.log('   使用命令: node scripts/deploy.js rollback <timestamp>');
      }
      
      process.exit(1);
    }
  }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
家乡话猜猜猜 - 部署脚本

用法:
  node scripts/deploy.js [stage] [options]

参数:
  stage          部署环境 (dev|test|prod，默认: dev)

选项:
  --skip-tests   跳过测试
  --skip-backup  跳过数据备份
  --verbose      显示详细日志
  --help, -h     显示帮助信息

示例:
  node scripts/deploy.js dev
  node scripts/deploy.js prod --verbose
  node scripts/deploy.js test --skip-tests
  
回滚:
  node scripts/deploy.js rollback <timestamp>
`);
  process.exit(0);
}

// 处理回滚命令
if (process.argv[2] === 'rollback') {
  const version = process.argv[3];
  if (!version) {
    console.error('❌ 请指定回滚版本');
    console.error('用法: node scripts/deploy.js rollback <timestamp>');
    process.exit(1);
  }
  
  const manager = new DeployManager();
  manager.stage = process.argv[4] || 'dev';
  manager.rollback(version);
  process.exit(0);
}

// 主执行逻辑
const manager = new DeployManager();

// 验证stage参数
if (!STAGES[manager.stage]) {
  console.error(`❌ 无效的环境: ${manager.stage}`);
  console.error(`支持的环境: ${Object.keys(STAGES).join(', ')}`);
  process.exit(1);
}

// 生产环境额外确认
if (manager.stage === 'prod' && !process.argv.includes('--force')) {
  console.log('⚠️  即将部署到生产环境！');
  console.log('请确认以下事项:');
  console.log('  - 代码已经过充分测试');
  console.log('  - 数据库已完成备份');
  console.log('  - 所有环境变量已正确配置');
  console.log('');
  console.log('如果确认无误，请添加 --force 参数继续部署');
  console.log('命令: node scripts/deploy.js prod --force');
  process.exit(0);
}

// 开始部署
manager.run().catch(console.error);