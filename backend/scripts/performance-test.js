#!/usr/bin/env node

/**
 * 性能和限流测试脚本
 * 验证API性能指标和限流配置
 */

const axios = require('axios');
const config = require('../config');

class PerformanceTest {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.results = {
      performance: {},
      rateLimit: {},
      summary: {}
    };
  }

  async runAllTests() {
    console.log('🚀 开始性能和限流测试...');
    console.log('====================================');
    console.log('');

    try {
      // 1. 性能测试
      console.log('📊 运行性能测试...');
      await this.runPerformanceTests();
      
      // 2. 限流测试
      console.log('\n🔒 运行限流测试...');
      await this.runRateLimitTests();
      
      // 3. 生成测试报告
      console.log('\n📋 生成测试报告...');
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
      process.exit(1);
    }
  }

  async runPerformanceTests() {
    const tests = [
      { name: '健康检查', endpoint: '/health', target: 100 },
      { name: 'API文档', endpoint: '/docs', target: 200 },
      { name: '获取题目列表', endpoint: '/v1/questions?count=10', target: 500 },
      { name: '音频资源列表', endpoint: '/v1/audio?limit=20', target: 500 }
    ];

    for (const test of tests) {
      console.log(`  测试: ${test.name}`);
      
      const results = await this.performanceTest(test.endpoint, {
        concurrent: 5,
        requests: 20,
        timeout: 10000
      });
      
      this.results.performance[test.name] = {
        ...results,
        target: test.target,
        passed: results.averageTime <= test.target
      };
      
      const status = results.averageTime <= test.target ? '✅ 通过' : '❌ 失败';
      console.log(`    平均响应时间: ${results.averageTime}ms (目标: ${test.target}ms) ${status}`);
      console.log(`    成功率: ${results.successRate}%`);
    }
  }

  async performanceTest(endpoint, options) {
    const { concurrent, requests, timeout } = options;
    const results = [];
    
    const makeRequest = async () => {
      const startTime = Date.now();
      try {
        const response = await axios.get(`${this.baseUrl}${endpoint}`, {
          timeout: timeout
        });
        
        const endTime = Date.now();
        return {
          success: true,
          responseTime: endTime - startTime,
          status: response.status
        };
      } catch (error) {
        const endTime = Date.now();
        return {
          success: false,
          responseTime: endTime - startTime,
          error: error.message
        };
      }
    };

    // 并发请求测试
    const promises = [];
    for (let i = 0; i < requests; i++) {
      promises.push(makeRequest());
    }

    const responses = await Promise.all(promises);
    
    const successCount = responses.filter(r => r.success).length;
    const responseTimes = responses.filter(r => r.success).map(r => r.responseTime);
    
    return {
      totalRequests: requests,
      successfulRequests: successCount,
      successRate: Math.round((successCount / requests) * 100),
      averageTime: responseTimes.length > 0 ? 
        Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0,
      minTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
      maxTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
      p95Time: responseTimes.length > 0 ? 
        this.calculatePercentile(responseTimes, 95) : 0
    };
  }

  async runRateLimitTests() {
    const tests = [
      {
        name: 'IP限流测试',
        endpoint: '/v1/questions',
        rateLimit: 50, // 假设IP限流为50次/分钟
        timeWindow: 60000, // 1分钟
        testRequests: 60
      },
      {
        name: 'API限流测试',
        endpoint: '/v1/questions',
        rateLimit: 100, // 假设API限流为100次/分钟
        timeWindow: 60000,
        testRequests: 120
      }
    ];

    for (const test of tests) {
      console.log(`  测试: ${test.name}`);
      
      const results = await this.rateLimitTest(test.endpoint, {
        requests: test.testRequests,
        expectedLimit: test.rateLimit,
        timeWindow: test.timeWindow
      });
      
      this.results.rateLimit[test.name] = results;
      
      const status = results.rateLimitTriggered ? '✅ 通过' : '❌ 失败';
      console.log(`    总请求数: ${results.totalRequests}`);
      console.log(`    成功请求数: ${results.successfulRequests}`);
      console.log(`    被限流请求数: ${results.rateLimitedRequests}`);
      console.log(`    限流触发: ${results.rateLimitTriggered ? '是' : '否'} ${status}`);
    }
  }

  async rateLimitTest(endpoint, options) {
    const { requests, expectedLimit } = options;
    const results = [];
    
    // 快速发送请求以触发限流
    for (let i = 0; i < requests; i++) {
      try {
        const response = await axios.get(`${this.baseUrl}${endpoint}`, {
          timeout: 5000
        });
        
        results.push({
          success: true,
          status: response.status,
          rateLimited: false
        });
        
      } catch (error) {
        const isRateLimit = error.response && 
          (error.response.status === 429 || 
           error.response.data?.code === 'RATE_LIMIT_EXCEEDED');
        
        results.push({
          success: false,
          status: error.response?.status || 0,
          rateLimited: isRateLimit,
          error: error.message
        });
        
        if (isRateLimit) {
          console.log(`    限流在第 ${i + 1} 个请求时触发`);
          break; // 触发限流后停止测试
        }
      }
      
      // 短暂延迟避免过快请求
      await this.sleep(10);
    }
    
    const successCount = results.filter(r => r.success).length;
    const rateLimitCount = results.filter(r => r.rateLimited).length;
    
    return {
      totalRequests: results.length,
      successfulRequests: successCount,
      rateLimitedRequests: rateLimitCount,
      rateLimitTriggered: rateLimitCount > 0,
      expectedLimit: expectedLimit,
      actualLimit: successCount
    };
  }

  async checkHealthAndBasics() {
    console.log('🔍 检查基础服务...');
    
    try {
      // 检查健康端点
      const healthResponse = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000
      });
      
      console.log('  ✅ 健康检查通过');
      console.log(`     状态: ${healthResponse.data.status}`);
      
      if (healthResponse.data.services) {
        Object.keys(healthResponse.data.services).forEach(service => {
          const status = healthResponse.data.services[service].status;
          const emoji = status === 'healthy' ? '✅' : '❌';
          console.log(`     ${service}: ${status} ${emoji}`);
        });
      }
      
    } catch (error) {
      console.error('  ❌ 健康检查失败:', error.message);
      throw new Error('服务不可用，请检查开发服务器是否正常运行');
    }
    
    console.log('');
  }

  generateReport() {
    console.log('🎯 测试结果汇总');
    console.log('================');
    console.log('');
    
    // 性能测试汇总
    console.log('📊 性能测试结果:');
    let performancePassed = 0;
    let performanceTotal = 0;
    
    Object.keys(this.results.performance).forEach(testName => {
      const result = this.results.performance[testName];
      const status = result.passed ? '✅' : '❌';
      console.log(`  ${testName}: ${result.averageTime}ms ${status}`);
      
      if (result.passed) performancePassed++;
      performanceTotal++;
    });
    
    console.log(`  通过率: ${performancePassed}/${performanceTotal} (${Math.round(performancePassed/performanceTotal*100)}%)`);
    console.log('');
    
    // 限流测试汇总
    console.log('🔒 限流测试结果:');
    let rateLimitPassed = 0;
    let rateLimitTotal = 0;
    
    Object.keys(this.results.rateLimit).forEach(testName => {
      const result = this.results.rateLimit[testName];
      const status = result.rateLimitTriggered ? '✅' : '❌';
      console.log(`  ${testName}: ${result.rateLimitTriggered ? '正常' : '异常'} ${status}`);
      
      if (result.rateLimitTriggered) rateLimitPassed++;
      rateLimitTotal++;
    });
    
    console.log(`  通过率: ${rateLimitPassed}/${rateLimitTotal} (${Math.round(rateLimitPassed/rateLimitTotal*100)}%)`);
    console.log('');
    
    // 整体评估
    const totalPassed = performancePassed + rateLimitPassed;
    const totalTests = performanceTotal + rateLimitTotal;
    const overallPassRate = Math.round(totalPassed / totalTests * 100);
    
    console.log('🏆 整体评估:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过数: ${totalPassed}`);
    console.log(`  通过率: ${overallPassRate}%`);
    
    const grade = overallPassRate >= 90 ? '优秀' : 
                  overallPassRate >= 80 ? '良好' : 
                  overallPassRate >= 70 ? '及格' : '需要改进';
    console.log(`  评级: ${grade}`);
    
    // 建议
    console.log('\n💡 优化建议:');
    
    Object.keys(this.results.performance).forEach(testName => {
      const result = this.results.performance[testName];
      if (!result.passed) {
        console.log(`  • ${testName}: 响应时间 ${result.averageTime}ms 超过目标 ${result.target}ms`);
        console.log(`    建议: 检查数据库查询、缓存策略和网络延迟`);
      }
    });
    
    Object.keys(this.results.rateLimit).forEach(testName => {
      const result = this.results.rateLimit[testName];
      if (!result.rateLimitTriggered) {
        console.log(`  • ${testName}: 限流未按预期触发`);
        console.log(`    建议: 检查限流配置和Redis连接`);
      }
    });
  }

  calculatePercentile(arr, percentile) {
    const sorted = arr.sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 主函数
async function main() {
  const baseUrl = process.argv[2] || 'http://localhost:3000';
  const tester = new PerformanceTest(baseUrl);
  
  console.log(`🎯 测试目标: ${baseUrl}`);
  console.log('');
  
  // 检查基础服务
  await tester.checkHealthAndBasics();
  
  // 运行所有测试
  await tester.runAllTests();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = PerformanceTest;