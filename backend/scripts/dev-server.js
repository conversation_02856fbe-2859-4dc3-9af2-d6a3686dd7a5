#!/usr/bin/env node

/**
 * 开发服务器
 * 本地模拟Serverless环境进行开发和测试
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

// 导入开发版处理器
const authHandler = require('../serverless/auth/dev-handler');
const userHandler = require('../serverless/user/dev-handler');
const gameHandler = require('../serverless/game/dev-handler');
const audioHandler = require('../serverless/audio/dev-handler');

// 加载环境变量
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件 - 配置CORS支持前端联调
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:8080', 'http://127.0.0.1:8080'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-Id', 'X-Requested-With'],
  credentials: true
}));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// 请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// 模拟Serverless Context
const createContext = (req) => ({
  requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  functionName: 'dev-server',
  functionVersion: '1.0.0',
  memoryLimitInMB: 512,
  remainingTimeInMS: 30000
});

// 模拟Serverless Event
const createEvent = (req) => ({
  httpMethod: req.method,
  path: req.path,
  queryStringParameters: req.query,
  pathParameters: req.params,
  headers: req.headers,
  body: req.body ? (typeof req.body === 'string' ? req.body : JSON.stringify(req.body)) : null,
  isBase64Encoded: false,
  requestContext: {
    httpMethod: req.method,
    path: req.path,
    requestId: createContext(req).requestId,
    identity: {
      sourceIp: req.ip || req.connection.remoteAddress
    }
  },
  sourceIP: req.ip || req.connection.remoteAddress
});

// 处理Serverless响应
const handleServerlessResponse = (res, response) => {
  if (!response) {
    return res.status(500).json({
      code: 'INTERNAL_ERROR',
      message: '服务器内部错误'
    });
  }

  const statusCode = response.statusCode || 200;
  const headers = response.headers || {};
  
  // 设置响应头
  Object.keys(headers).forEach(key => {
    res.set(key, headers[key]);
  });

  let body = response.body;
  
  // 处理响应体
  if (typeof body === 'string') {
    try {
      body = JSON.parse(body);
    } catch (error) {
      // 如果不是JSON，直接返回字符串
    }
  }

  res.status(statusCode).json(body);
};

// 路由处理器工厂
const createRouteHandler = (handler) => {
  return async (req, res) => {
    try {
      const event = createEvent(req);
      const context = createContext(req);
      
      const response = await handler.main(event, context);
      handleServerlessResponse(res, response);
      
    } catch (error) {
      console.error('Route handler error:', error);
      res.status(500).json({
        code: 'INTERNAL_ERROR',
        message: error.message || '服务器内部错误',
        timestamp: new Date().toISOString()
      });
    }
  };
};

// 认证服务路由
app.all('/v1/auth/*', createRouteHandler(authHandler));

// 用户服务路由
app.all('/v1/users/*', createRouteHandler(userHandler));

// 游戏服务路由
app.all('/v1/questions*', createRouteHandler(gameHandler));
app.all('/v1/game-sessions*', createRouteHandler(gameHandler));

// 音频服务路由
app.all('/v1/audio*', createRouteHandler(audioHandler));

// 健康检查
app.get('/health', async (req, res) => {
  try {
    // 优先使用真实服务，fallback到Mock服务
    let dbHealth, redisHealth;
    
    try {
      const db = require('../serverless/utils/database');
      dbHealth = await db.healthCheck();
    } catch (error) {
      console.log('使用Mock数据库服务');
      const devDb = require('../serverless/utils/dev-database');
      dbHealth = await devDb.healthCheck();
    }
    
    try {
      const redis = require('../serverless/utils/redis');
      redisHealth = await redis.healthCheck();
    } catch (error) {
      console.log('使用Mock Redis服务');
      const devRedis = require('../serverless/utils/dev-redis');
      redisHealth = await devRedis.healthCheck();
    }
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: 'development',
      services: {
        database: dbHealth,
        redis: redisHealth
      }
    };
    
    const overallHealthy = dbHealth.status === 'healthy' && redisHealth.status === 'healthy';
    
    res.status(overallHealthy ? 200 : 503).json(health);
    
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API文档
app.get('/docs', (req, res) => {
  res.json({
    name: '家乡话猜猜猜 - 后端API',
    version: '1.0.0',
    description: '提供用户认证、游戏管理、排行榜等服务',
    endpoints: {
      auth: {
        'POST /v1/auth/wechat/login': '微信登录',
        'POST /v1/auth/refresh': '刷新Token',
        'POST /v1/auth/logout': '退出登录',
        'GET /v1/auth/me': '获取当前用户信息',
        'GET /v1/auth/verify': '验证Token'
      },
      users: {
        'GET /v1/users/{userId}': '获取用户信息',
        'PUT /v1/users/{userId}/profile': '更新用户资料',
        'GET /v1/users/{userId}/stats': '获取用户游戏统计',
        'GET /v1/users/{userId}/game-history': '获取用户游戏历史',
        'GET /v1/users/search': '搜索用户'
      },
      game: {
        'GET /v1/questions': '获取题目列表',
        'GET /v1/questions/{questionId}': '获取题目详情',
        'POST /v1/game-sessions': '创建游戏会话',
        'GET /v1/game-sessions/{sessionId}': '获取游戏会话状态',
        'POST /v1/game-sessions/{sessionId}/submit': '提交游戏答案',
        'GET /v1/game-sessions/{sessionId}/results': '获取游戏结果',
        'DELETE /v1/game-sessions/{sessionId}': '取消游戏会话'
      },
      audio: {
        'GET /v1/audio': '获取音频资源列表',
        'GET /v1/audio/{resourceId}': '获取音频资源信息',
        'GET /v1/audio/stats': '获取音频统计(管理员)',
        'POST /v1/audio/{resourceId}': '上传音频资源(管理员)',
        'DELETE /v1/audio/{resourceId}': '删除音频资源(管理员)'
      }
    },
    development: {
      baseUrl: `http://localhost:${PORT}`,
      healthCheck: '/health',
      documentation: '/docs'
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 'ROUTE_NOT_FOUND',
    message: `接口 ${req.method} ${req.originalUrl} 不存在`,
    timestamp: new Date().toISOString()
  });
});

// 错误处理
app.use((error, req, res, next) => {
  console.error('Express error:', error);
  res.status(500).json({
    code: 'INTERNAL_ERROR',
    message: error.message || '服务器内部错误',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 开发服务器启动成功！`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`💊 健康检查: http://localhost:${PORT}/health`);
  console.log(`📚 API文档: http://localhost:${PORT}/docs`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
  console.log('');
  console.log('可用的API端点:');
  console.log('• POST /v1/auth/wechat/login - 微信登录');
  console.log('• POST /v1/auth/refresh - 刷新Token');
  console.log('• GET /v1/auth/me - 获取当前用户信息');
  console.log('• GET /v1/users/{userId} - 获取用户信息');
  console.log('• GET /v1/questions - 获取题目列表');
  console.log('• POST /v1/game-sessions - 创建游戏会话');
  console.log('• GET /v1/audio/{resourceId} - 获取音频资源');
  console.log('');
  console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  
  try {
    // 尝试关闭真实服务连接
    try {
      const db = require('../serverless/utils/database');
      await db.close();
    } catch (error) {
      const devDb = require('../serverless/utils/dev-database');
      await devDb.close();
    }
    
    try {
      const redis = require('../serverless/utils/redis');
      await redis.close();
    } catch (error) {
      const devRedis = require('../serverless/utils/dev-redis');
      await devRedis.close();
    }
    
    console.log('✅ 数据库和缓存连接已关闭');
  } catch (error) {
    console.error('❌ 关闭连接时出错:', error);
  }
  
  process.exit(0);
});

module.exports = app;