#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 创建数据库、执行迁移和填充测试数据
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'hometown_dialect_game',
  charset: 'utf8mb4'
};

class DatabaseInitializer {
  constructor() {
    this.connection = null;
    this.migrationsPath = path.join(__dirname, '../database/migrations');
  }

  async init() {
    try {
      console.log('🚀 开始初始化数据库...');
      
      // 1. 连接MySQL (不指定数据库)
      await this.connect();
      
      // 2. 创建数据库
      await this.createDatabase();
      
      // 3. 使用目标数据库
      await this.useDatabase();
      
      // 4. 执行迁移
      await this.runMigrations();
      
      // 5. 填充测试数据
      await this.seedTestData();
      
      console.log('✅ 数据库初始化完成！');
      
    } catch (error) {
      console.error('❌ 数据库初始化失败:', error);
      process.exit(1);
    } finally {
      if (this.connection) {
        await this.connection.end();
      }
    }
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        charset: config.charset
      });
      
      console.log('✅ 连接MySQL服务器成功');
    } catch (error) {
      throw new Error(`连接MySQL失败: ${error.message}`);
    }
  }

  async createDatabase() {
    try {
      const createDbQuery = `CREATE DATABASE IF NOT EXISTS \`${config.database}\` 
                            CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`;
      
      await this.connection.execute(createDbQuery);
      console.log(`✅ 数据库 "${config.database}" 创建成功`);
    } catch (error) {
      throw new Error(`创建数据库失败: ${error.message}`);
    }
  }

  async useDatabase() {
    try {
      // 关闭当前连接
      await this.connection.end();
      
      // 重新创建连接，这次指定数据库
      this.connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        charset: config.charset
      });
      
      console.log(`✅ 切换到数据库 "${config.database}"`);
    } catch (error) {
      throw new Error(`切换数据库失败: ${error.message}`);
    }
  }

  async runMigrations() {
    try {
      if (!fs.existsSync(this.migrationsPath)) {
        throw new Error(`迁移目录不存在: ${this.migrationsPath}`);
      }

      const migrationFiles = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort();

      console.log(`📁 找到 ${migrationFiles.length} 个迁移文件`);

      for (const file of migrationFiles) {
        const filePath = path.join(this.migrationsPath, file);
        const sql = fs.readFileSync(filePath, 'utf8');
        
        console.log(`⚡ 执行迁移: ${file}`);
        
        // 分割SQL语句 (以分号分割)
        const statements = sql.split(';')
          .map(s => s.trim())
          .filter(s => s.length > 0);

        for (const statement of statements) {
          if (statement.trim()) {
            await this.connection.execute(statement);
          }
        }
        
        console.log(`✅ 迁移完成: ${file}`);
      }

      console.log('✅ 所有迁移执行完成');
    } catch (error) {
      throw new Error(`执行迁移失败: ${error.message}`);
    }
  }

  async seedTestData() {
    try {
      console.log('🌱 开始填充测试数据...');

      // 1. 创建测试用户
      await this.seedTestUsers();
      
      // 2. 创建测试题目
      await this.seedTestQuestions();
      
      console.log('✅ 测试数据填充完成');
    } catch (error) {
      console.error('⚠️  填充测试数据失败:', error.message);
      // 测试数据失败不阻止初始化过程
    }
  }

  async seedTestUsers() {
    const testUsers = [
      {
        openid: 'test_openid_001',
        nickname: '测试用户1',
        avatar_url: 'https://example.com/avatar1.jpg',
        gender: 1,
        province: '四川省',
        city: '成都市',
        country: '中国'
      },
      {
        openid: 'test_openid_002', 
        nickname: '测试用户2',
        avatar_url: 'https://example.com/avatar2.jpg',
        gender: 2,
        province: '广东省',
        city: '广州市',
        country: '中国'
      }
    ];

    for (const user of testUsers) {
      try {
        await this.connection.execute(
          `INSERT IGNORE INTO users (openid, nickname, avatar_url, gender, province, city, country) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [user.openid, user.nickname, user.avatar_url, user.gender, user.province, user.city, user.country]
        );
      } catch (error) {
        console.warn(`创建测试用户失败: ${error.message}`);
      }
    }

    console.log('✅ 测试用户创建完成');
  }

  async seedTestQuestions() {
    const testQuestions = [
      {
        category: 'sichuan',
        region: '四川',
        question_text: '请听音频，选择正确的普通话意思',
        question_type: 1,
        audio_url: 'https://example.com/audio/sichuan_001.mp3',
        difficulty_level: 1,
        standard_answer: '好的',
        answer_options: JSON.stringify(['好的', '不行', '可以', '没有']),
        explanation: '四川话"撒子"意思是"好的"'
      },
      {
        category: 'guangdong',
        region: '广东',
        question_text: '请听音频，选择正确的普通话意思',
        question_type: 1,
        audio_url: 'https://example.com/audio/guangdong_001.mp3',
        difficulty_level: 2,
        standard_answer: '吃饭',
        answer_options: JSON.stringify(['吃饭', '喝水', '睡觉', '上班']),
        explanation: '粤语"食饭"意思是"吃饭"'
      },
      {
        category: 'beijing',
        region: '北京',
        question_text: '请听音频，选择正确的普通话意思',
        question_type: 1,
        audio_url: 'https://example.com/audio/beijing_001.mp3',
        difficulty_level: 1,
        standard_answer: '聊天',
        answer_options: JSON.stringify(['聊天', '工作', '学习', '休息']),
        explanation: '北京话"侃大山"意思是"聊天"'
      }
    ];

    for (const question of testQuestions) {
      try {
        await this.connection.execute(
          `INSERT IGNORE INTO dialect_questions 
           (category, region, question_text, question_type, audio_url, difficulty_level, 
            standard_answer, answer_options, explanation) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            question.category, question.region, question.question_text,
            question.question_type, question.audio_url, question.difficulty_level,
            question.standard_answer, question.answer_options, question.explanation
          ]
        );
      } catch (error) {
        console.warn(`创建测试题目失败: ${error.message}`);
      }
    }

    console.log('✅ 测试题目创建完成');
  }

  async checkConnection() {
    try {
      const [rows] = await this.connection.execute('SELECT 1 as test');
      return rows.length > 0;
    } catch (error) {
      return false;
    }
  }
}

// 主函数
async function main() {
  const initializer = new DatabaseInitializer();
  await initializer.init();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = DatabaseInitializer;