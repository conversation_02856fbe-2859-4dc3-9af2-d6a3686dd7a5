#!/usr/bin/env node

/**
 * 数据库迁移脚本
 * 执行所有SQL迁移文件，创建数据库表结构
 */

const fs = require('fs');
const path = require('path');
const db = require('../serverless/utils/database');

async function runMigrations() {
  try {
    console.log('🚀 Starting database migrations...');
    
    // 连接数据库
    await db.connect();
    console.log('✅ Database connected');

    // 创建迁移记录表
    await createMigrationsTable();

    // 获取所有迁移文件
    const migrationsDir = path.join(__dirname, '../database/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    console.log(`📄 Found ${migrationFiles.length} migration files`);

    // 获取已执行的迁移
    const executedMigrations = await getExecutedMigrations();

    // 执行未运行的迁移
    for (const file of migrationFiles) {
      if (executedMigrations.includes(file)) {
        console.log(`⏭️  Skipping ${file} (already executed)`);
        continue;
      }

      console.log(`🔄 Executing ${file}...`);
      
      try {
        const filePath = path.join(migrationsDir, file);
        const sql = fs.readFileSync(filePath, 'utf8');
        
        // 分割多个SQL语句
        const statements = sql.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
          if (statement.trim()) {
            await db.query(statement);
          }
        }
        
        // 记录迁移执行
        await recordMigration(file);
        console.log(`✅ Completed ${file}`);
        
      } catch (error) {
        console.error(`❌ Failed to execute ${file}:`, error.message);
        throw error;
      }
    }

    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

/**
 * 创建迁移记录表
 */
async function createMigrationsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS migrations (
      id INT AUTO_INCREMENT PRIMARY KEY,
      filename VARCHAR(255) NOT NULL UNIQUE,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_filename (filename)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `;
  
  await db.query(sql);
}

/**
 * 获取已执行的迁移
 */
async function getExecutedMigrations() {
  try {
    const rows = await db.query('SELECT filename FROM migrations ORDER BY executed_at');
    return rows.map(row => row.filename);
  } catch (error) {
    // 如果表不存在，返回空数组
    return [];
  }
}

/**
 * 记录迁移执行
 */
async function recordMigration(filename) {
  await db.query(
    'INSERT INTO migrations (filename) VALUES (?)',
    [filename]
  );
}

/**
 * 回滚迁移（可选功能）
 */
async function rollbackMigration(filename) {
  if (!filename) {
    console.error('Please specify migration filename to rollback');
    return;
  }

  try {
    await db.connect();
    
    // 删除迁移记录
    await db.query('DELETE FROM migrations WHERE filename = ?', [filename]);
    
    console.log(`✅ Rollback recorded for ${filename}`);
    console.log('⚠️  Note: You need to manually revert the database changes');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
  } finally {
    await db.close();
  }
}

/**
 * 显示迁移状态
 */
async function showStatus() {
  try {
    await db.connect();
    
    const migrationsDir = path.join(__dirname, '../database/migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    const executedMigrations = await getExecutedMigrations();
    
    console.log('\n📊 Migration Status:');
    console.log('===================');
    
    migrationFiles.forEach(file => {
      const status = executedMigrations.includes(file) ? '✅' : '⏳';
      console.log(`${status} ${file}`);
    });
    
    console.log(`\nTotal: ${migrationFiles.length} migrations`);
    console.log(`Executed: ${executedMigrations.length} migrations`);
    console.log(`Pending: ${migrationFiles.length - executedMigrations.length} migrations`);
    
  } catch (error) {
    console.error('❌ Failed to show status:', error);
  } finally {
    await db.close();
  }
}

// 命令行参数处理
const command = process.argv[2];

switch (command) {
  case 'up':
  case undefined:
    runMigrations();
    break;
  case 'rollback':
    rollbackMigration(process.argv[3]);
    break;
  case 'status':
    showStatus();
    break;
  case 'reset':
    console.log('⚠️  Reset functionality not implemented for safety');
    console.log('Please manually drop and recreate the database if needed');
    break;
  default:
    console.log('Usage:');
    console.log('  node migrate.js [up]     - Run pending migrations');
    console.log('  node migrate.js rollback <filename> - Rollback specific migration');
    console.log('  node migrate.js status   - Show migration status');
    console.log('  node migrate.js reset    - Reset database (not implemented)');
}

module.exports = {
  runMigrations,
  rollbackMigration,
  showStatus
};