
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for models</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> models</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">23.09% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>82/355</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.55% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>123/319</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.6% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/66</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.03% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>81/337</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="GameResult.js"><a href="GameResult.js.html">GameResult.js</a></td>
	<td data-value="3.63" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.63" class="pct low">3.63%</td>
	<td data-value="55" class="abs low">2/55</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="55" class="abs low">0/55</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="3.84" class="pct low">3.84%</td>
	<td data-value="52" class="abs low">2/52</td>
	</tr>

<tr>
	<td class="file low" data-value="GameSession.js"><a href="GameSession.js.html">GameSession.js</a></td>
	<td data-value="30.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.37" class="pct low">30.37%</td>
	<td data-value="79" class="abs low">24/79</td>
	<td data-value="60.91" class="pct high">60.91%</td>
	<td data-value="87" class="abs high">53/87</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="14" class="abs low">2/14</td>
	<td data-value="31.57" class="pct low">31.57%</td>
	<td data-value="76" class="abs low">24/76</td>
	</tr>

<tr>
	<td class="file low" data-value="Question.js"><a href="Question.js.html">Question.js</a></td>
	<td data-value="33.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 33%"></div><div class="cover-empty" style="width: 67%"></div></div>
	</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="93" class="abs low">31/93</td>
	<td data-value="59.78" class="pct high">59.78%</td>
	<td data-value="92" class="abs high">55/92</td>
	<td data-value="27.27" class="pct low">27.27%</td>
	<td data-value="11" class="abs low">3/11</td>
	<td data-value="33.7" class="pct low">33.7%</td>
	<td data-value="89" class="abs low">30/89</td>
	</tr>

<tr>
	<td class="file low" data-value="User.js"><a href="User.js.html">User.js</a></td>
	<td data-value="19.53" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 19%"></div><div class="cover-empty" style="width: 81%"></div></div>
	</td>
	<td data-value="19.53" class="pct low">19.53%</td>
	<td data-value="128" class="abs low">25/128</td>
	<td data-value="17.64" class="pct low">17.64%</td>
	<td data-value="85" class="abs low">15/85</td>
	<td data-value="7.4" class="pct low">7.4%</td>
	<td data-value="27" class="abs low">2/27</td>
	<td data-value="20.83" class="pct low">20.83%</td>
	<td data-value="120" class="abs low">25/120</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-01T23:12:09.108Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    