# 测试环境配置
NODE_ENV=test

# JWT配置
JWT_SECRET=test_jwt_secret_key_for_testing_only_do_not_use_in_production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 数据库配置（测试环境）
DB_HOST=localhost
DB_PORT=3306
DB_USER=test
DB_PASSWORD=test
DB_NAME=dialect_game_test
DB_CONNECTION_LIMIT=5
DB_TIMEOUT=5000

# Redis配置（测试环境）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_TIMEOUT=5000

# 微信配置（测试环境）
WECHAT_APP_ID=test_wechat_appid_for_testing
WECHAT_APP_SECRET=test_wechat_secret_for_testing

# COS配置（测试环境）
COS_SECRET_ID=test_cos_secret_id
COS_SECRET_KEY=test_cos_secret_key
COS_BUCKET=test-dialect-game-bucket
COS_REGION=ap-guangzhou
COS_DOMAIN=https://test-dialect-game-bucket.cos.ap-guangzhou.myqcloud.com

# 测试配置
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80
TEST_PARALLEL=false

# 日志配置
LOG_LEVEL=error
LOG_FILE=false

# 性能配置
PERFORMANCE_MONITORING=false
RATE_LIMIT_ENABLED=false
