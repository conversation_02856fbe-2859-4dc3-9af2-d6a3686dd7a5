/**
 * 配置管理
 * 统一管理所有环境变量和配置项
 */

const config = {
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  isDev: process.env.NODE_ENV === 'development',
  isProd: process.env.NODE_ENV === 'production',
  
  // 数据库配置（优化版）
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    charset: 'utf8mb4',
    timezone: '+08:00',
    // 优化连接池配置以支持10K DAU
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 50, // 增加到50个连接
    acquireTimeout: 60000,
    timeout: 60000,
    idleTimeout: 300000, // 5分钟空闲超时
    queueLimit: 0, // 无限队列
    reconnect: true,
    // 性能优化参数
    multipleStatements: false, // 安全考虑
    namedPlaceholders: true,
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: false,
    // SSL配置
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
    // 连接池监控
    debug: process.env.NODE_ENV === 'development',
    trace: process.env.NODE_ENV === 'development'
  },
  
  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: 0,
    keyPrefix: process.env.CACHE_PREFIX || 'hdg:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    connectTimeout: 10000,
    commandTimeout: 5000
  },
  
  // JWT配置
  jwt: {
    accessSecret: process.env.JWT_ACCESS_SECRET,
    refreshSecret: process.env.JWT_REFRESH_SECRET,
    accessTokenTTL: 24 * 60 * 60, // 24小时
    refreshTokenTTL: 30 * 24 * 60 * 60, // 30天
    issuer: 'api.dialectgame.com',
    audience: 'dialect-game'
  },
  
  // 微信小程序配置
  wechat: {
    appId: process.env.WECHAT_APP_ID,
    appSecret: process.env.WECHAT_APP_SECRET,
    apiUrl: 'https://api.weixin.qq.com',
    sessionTimeout: 7 * 24 * 60 * 60 * 1000 // 7天
  },
  
  // 腾讯云COS配置
  cos: {
    secretId: process.env.COS_SECRET_ID,
    secretKey: process.env.COS_SECRET_KEY,
    bucket: process.env.COS_BUCKET,
    region: process.env.COS_REGION || 'ap-beijing',
    cdnDomain: process.env.COS_CDN_DOMAIN,
    uploadPath: 'audio/',
    maxFileSize: 10 * 1024 * 1024 // 10MB
  },
  
  // 限流配置（增强版）
  rateLimit: {
    // 全局限流
    global: {
      capacity: 2000,      // 增加全局容量以支持10K DAU
      refillRate: 20       // 增加补充速率
    },
    // 用户级限流
    user: {
      capacity: 200,       // 增加用户容量
      refillRate: 3        // 增加用户补充速率
    },
    // 设备级限流
    device: {
      capacity: 150,
      refillRate: 2
    },
    // API级限流
    api: {
      '/v1/auth/wechat/login': { capacity: 15, refillRate: 0.2 },
      '/v1/auth/refresh': { capacity: 30, refillRate: 0.5 },
      '/v1/questions': { capacity: 200, refillRate: 8 },
      '/v1/game-sessions': { capacity: 50, refillRate: 1 },
      '/v1/game-sessions/{id}/answer': { capacity: 100, refillRate: 3 },
      '/v1/leaderboards': { capacity: 100, refillRate: 2 },
      '/v1/audio/stream': { capacity: 300, refillRate: 10 }
    },
    // IP级限流
    ip: {
      windowSize: 60,
      maxRequests: 500     // 增加IP限制以支持更高并发
    }
  },

  // 安全配置
  security: {
    // CSRF防护
    csrf: {
      enabled: process.env.NODE_ENV === 'production',
      secret: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production'
    },

    // 请求重放攻击防护
    replayAttack: {
      enabled: process.env.NODE_ENV === 'production',
      windowMs: 5 * 60 * 1000  // 5分钟窗口
    },

    // 暴力破解防护
    bruteForce: {
      maxAttempts: 5,
      windowMs: 15 * 60 * 1000,        // 15分钟
      blockDurationMs: 60 * 60 * 1000  // 1小时
    },

    // 输入验证
    validation: {
      maxBodySize: 10 * 1024 * 1024,  // 10MB
      maxHeaderCount: 50,
      strictMode: process.env.NODE_ENV === 'production'
    },

    // 审计日志
    audit: {
      enabled: true,
      retentionDays: 30,
      sensitiveFields: ['password', 'token', 'secret', 'key', 'code', 'openid']
    },

    // 加密配置
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16
    }
  },
  
  // 缓存配置
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 3600,
    prefix: process.env.CACHE_PREFIX || 'hdg:',
    // 不同数据类型的TTL
    userProfile: 30 * 60, // 30分钟
    gameSession: 60 * 60, // 1小时
    questions: 24 * 60 * 60, // 24小时
    leaderboard: 5 * 60, // 5分钟
    userStats: 10 * 60 // 10分钟
  },
  
  // 游戏配置
  game: {
    // 积分规则
    scoring: {
      correct: 100,        // 正确答案基础分
      timeBonus: 50,       // 时间奖励最大分
      streakMultiplier: 1.2, // 连击倍数
      hintPenalty: 0.5     // 使用提示扣分比例
    },
    // 游戏限制
    limits: {
      maxQuestionsPerSession: 50,
      minQuestionsPerSession: 5,
      maxDailyGames: 100,
      sessionTimeout: 30 * 60 * 1000 // 30分钟
    },
    // 难度等级
    difficulty: {
      1: { name: '入门', multiplier: 1.0 },
      2: { name: '初级', multiplier: 1.2 },
      3: { name: '中级', multiplier: 1.5 },
      4: { name: '高级', multiplier: 2.0 },
      5: { name: '专家', multiplier: 3.0 }
    }
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE,
    maxFiles: 7,
    maxSize: '100MB',
    format: 'json'
  },
  
  // 安全配置
  security: {
    // 防刷配置
    antiFraud: {
      maxDeviceUsers: 3,
      minAnswerTime: 1, // 秒
      maxAccuracyThreshold: 0.95,
      maxDailyGames: 100,
      suspiciousScoreThreshold: 0.8
    },
    // 加密配置
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16
    }
  },
  
  // API响应配置
  api: {
    version: 'v1',
    timeout: 30000,
    maxBodySize: '1mb',
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }
  },
  
  // 监控配置
  monitoring: {
    metricsInterval: 60000, // 1分钟
    healthCheck: {
      timeout: 5000,
      interval: 30000
    }
  }
};

// 验证必需的配置项
const requiredConfigs = [
  'database.host',
  'database.user', 
  'database.password',
  'database.database',
  'jwt.accessSecret',
  'jwt.refreshSecret',
  'wechat.appId',
  'wechat.appSecret'
];

function validateConfig() {
  const missingConfigs = [];
  
  requiredConfigs.forEach(path => {
    const value = path.split('.').reduce((obj, key) => obj && obj[key], config);
    if (!value) {
      missingConfigs.push(path);
    }
  });
  
  if (missingConfigs.length > 0) {
    throw new Error(`Missing required configurations: ${missingConfigs.join(', ')}`);
  }
}

// 开发环境不验证必需配置
if (config.isProd) {
  validateConfig();
}

module.exports = config;