# 家乡话猜猜猜 - 后端API接口指南

## 🎯 项目概述

本项目为"家乡话猜猜猜"微信小游戏的后端API接口系统，基于腾讯云Serverless架构，提供完整的游戏功能支持。

## 🔗 API基础信息

- **基础URL**: `https://api.dialectgame.com/v1` (生产环境)
- **开发URL**: `http://localhost:3001/v1` (本地开发)
- **认证方式**: Bearer <PERSON> (JWT)
- **请求格式**: JSON
- **响应格式**: JSON

## 📋 核心API接口

### 1. 用户认证服务 (Auth)

#### 1.1 微信小程序登录
```http
POST /v1/auth/wechat/login
Content-Type: application/json

{
  "code": "微信授权码",
  "encryptedData": "加密用户信息(可选)",
  "iv": "初始向量(可选)"
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400,
    "tokenType": "Bearer",
    "user": {
      "id": 12345,
      "nickname": "用户昵称",
      "avatarUrl": "头像URL",
      "openid": "微信openid"
    },
    "isNewUser": true
  }
}
```

#### 1.2 刷新访问令牌
```http
POST /v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "刷新令牌"
}
```

#### 1.3 获取当前用户信息
```http
GET /v1/auth/me
Authorization: Bearer {accessToken}
```

### 2. 游戏核心服务 (Game)

#### 2.1 获取题目列表
```http
GET /v1/questions?count=10&difficulty=1&category=cantonese&random=true
```

**参数说明:**
- `count`: 题目数量 (1-50, 默认10)
- `difficulty`: 难度等级 (1-5, 可选)
- `category`: 方言类别 (cantonese/shanghai/sichuan等, 可选)
- `random`: 是否随机 (true/false, 默认true)

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "questions": [
      {
        "id": 1001,
        "category": "cantonese",
        "region": "广东",
        "questionText": "听音辨字：这个词的意思是什么？",
        "questionType": 1,
        "audioUrl": "https://cdn.dialectgame.com/audio/cantonese/daily/001.mp3",
        "audioDuration": 3,
        "difficultyLevel": 1,
        "answerOptions": ["选项A", "选项B", "选项C", "选项D"],
        "explanation": "题目解释"
      }
    ],
    "total": 10,
    "params": {
      "count": 10,
      "difficulty": 1,
      "category": "cantonese",
      "random": true
    }
  }
}
```

#### 2.2 创建游戏会话
```http
POST /v1/game-sessions
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "category": "cantonese",
  "difficulty": 1,
  "questionCount": 10,
  "gameMode": "standard"
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "gameSession": {
      "sessionId": "sess_1234567890",
      "userId": 12345,
      "category": "cantonese",
      "difficulty": 1,
      "questionCount": 10,
      "gameMode": "standard",
      "currentQuestionIndex": 0,
      "totalScore": 0,
      "streakCount": 0,
      "status": 1,
      "startTime": "2024-01-15T10:30:00Z",
      "expiresAt": "2024-01-15T11:00:00Z"
    },
    "firstQuestion": {
      // 第一题数据
    }
  }
}
```

#### 2.3 提交答案
```http
POST /v1/game-sessions/{sessionId}/submit
Authorization: Bearer {accessToken}
Content-Type: application/json

{
  "questionId": 1001,
  "userAnswer": "用户答案",
  "answerTime": 5,
  "hintUsed": false
}
```

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "result": {
      "isCorrect": true,
      "correctAnswer": "标准答案",
      "explanation": "答案解释",
      "scoreEarned": 120,
      "streakCount": 3
    },
    "gameSession": {
      "sessionId": "sess_1234567890",
      "currentQuestionIndex": 1,
      "totalScore": 120,
      "streakCount": 3,
      "status": 1
    },
    "isGameCompleted": false
  }
}
```

#### 2.4 获取游戏结果
```http
GET /v1/game-sessions/{sessionId}/results
Authorization: Bearer {accessToken}
```

### 3. 排行榜服务 (Leaderboard)

#### 3.1 获取全球排行榜
```http
GET /v1/leaderboard/global?type=total_score&period=all_time&page=1&size=50
```

**参数说明:**
- `type`: 排行类型 (total_score/total_games/win_rate/max_streak)
- `period`: 时间周期 (all_time/weekly/monthly/daily)
- `page`: 页码 (默认1)
- `size`: 每页数量 (1-100, 默认50)

**响应示例:**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "user": {
          "id": 12345,
          "nickname": "排行榜第一名",
          "avatarUrl": "头像URL",
          "level": 15
        },
        "score": 125000,
        "totalGames": 500,
        "winGames": 450,
        "winRate": 0.9000,
        "maxStreak": 25
      }
    ],
    "pagination": {
      "page": 1,
      "size": 50,
      "total": 1000,
      "totalPages": 20,
      "hasNext": true,
      "hasPrev": false
    },
    "type": "total_score",
    "period": "all_time",
    "currentUserRank": 156
  }
}
```

#### 3.2 获取地区排行榜
```http
GET /v1/leaderboard/region?region=guangdong&category=cantonese&page=1&size=50
```

#### 3.3 获取好友排行榜
```http
GET /v1/leaderboard/friends
Authorization: Bearer {accessToken}
```

### 4. 用户服务 (User)

#### 4.1 获取用户信息
```http
GET /v1/users/{userId}
Authorization: Bearer {accessToken}
```

#### 4.2 获取用户游戏统计
```http
GET /v1/users/{userId}/stats?category=cantonese
Authorization: Bearer {accessToken}
```

#### 4.3 获取用户游戏历史
```http
GET /v1/users/{userId}/game-history?page=1&size=20
Authorization: Bearer {accessToken}
```

## 🔧 前端集成指南

### 微信小程序集成

#### 1. 基础配置
```javascript
// config/api.js
const API_CONFIG = {
  baseURL: 'https://api.dialectgame.com/v1', // 生产环境
  // baseURL: 'http://localhost:3001/v1', // 开发环境
  timeout: 10000
};
```

#### 2. HTTP请求封装
```javascript
// utils/request.js
class APIClient {
  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.token = wx.getStorageSync('accessToken');
  }

  async request(options) {
    const { url, method = 'GET', data = {}, needAuth = true } = options;
    
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (needAuth && this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseURL}${url}`,
          method,
          data,
          header: headers,
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`HTTP ${response.statusCode}: ${response.data.message}`);
      }
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // 微信登录
  async wechatLogin(code) {
    return this.request({
      url: '/auth/wechat/login',
      method: 'POST',
      data: { code },
      needAuth: false
    });
  }

  // 获取题目
  async getQuestions(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request({
      url: `/questions${query ? '?' + query : ''}`,
      needAuth: false
    });
  }

  // 创建游戏会话
  async createGameSession(gameConfig) {
    return this.request({
      url: '/game-sessions',
      method: 'POST',
      data: gameConfig
    });
  }

  // 提交答案
  async submitAnswer(sessionId, answerData) {
    return this.request({
      url: `/game-sessions/${sessionId}/submit`,
      method: 'POST',
      data: answerData
    });
  }

  // 获取排行榜
  async getLeaderboard(type = 'global', params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request({
      url: `/leaderboard/${type}${query ? '?' + query : ''}`,
      needAuth: false
    });
  }
}

export default new APIClient();
```

#### 3. 游戏流程示例
```javascript
// 游戏管理器
class GameManager {
  constructor() {
    this.currentSession = null;
    this.currentQuestion = null;
    this.questions = [];
  }

  // 开始游戏
  async startGame(gameConfig = {}) {
    try {
      // 1. 创建游戏会话
      const sessionResponse = await APIClient.createGameSession({
        category: gameConfig.category || 'cantonese',
        difficulty: gameConfig.difficulty || 1,
        questionCount: gameConfig.questionCount || 10,
        gameMode: gameConfig.gameMode || 'standard'
      });

      this.currentSession = sessionResponse.data.gameSession;
      this.currentQuestion = sessionResponse.data.firstQuestion;

      return {
        success: true,
        session: this.currentSession,
        question: this.currentQuestion
      };
    } catch (error) {
      console.error('Start game failed:', error);
      return { success: false, error: error.message };
    }
  }

  // 提交答案
  async submitAnswer(userAnswer, answerTime, hintUsed = false) {
    if (!this.currentSession || !this.currentQuestion) {
      throw new Error('No active game session');
    }

    try {
      const response = await APIClient.submitAnswer(this.currentSession.sessionId, {
        questionId: this.currentQuestion.id,
        userAnswer: userAnswer,
        answerTime: answerTime,
        hintUsed: hintUsed
      });

      const result = response.data;
      
      // 更新会话状态
      this.currentSession = result.gameSession;
      
      return {
        success: true,
        result: result.result,
        isGameCompleted: result.isGameCompleted,
        finalStats: result.finalStats || null
      };
    } catch (error) {
      console.error('Submit answer failed:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取下一题
  async getNextQuestion() {
    if (!this.currentSession) {
      throw new Error('No active game session');
    }

    // 从题目列表中获取下一题
    const nextIndex = this.currentSession.currentQuestionIndex;
    if (nextIndex < this.questions.length) {
      this.currentQuestion = this.questions[nextIndex];
      return this.currentQuestion;
    }

    return null; // 游戏结束
  }
}

export default GameManager;
```

#### 4. 音频播放集成
```javascript
// 音频管理器
class AudioManager {
  constructor() {
    this.currentAudio = null;
  }

  async playQuestionAudio(audioUrl) {
    try {
      // 停止当前播放
      if (this.currentAudio) {
        this.currentAudio.stop();
      }

      // 创建新的音频实例
      this.currentAudio = wx.createInnerAudioContext();
      this.currentAudio.src = audioUrl;

      return new Promise((resolve, reject) => {
        this.currentAudio.onPlay(() => {
          console.log('Audio started playing');
        });

        this.currentAudio.onEnded(() => {
          console.log('Audio finished playing');
          resolve();
        });

        this.currentAudio.onError((error) => {
          console.error('Audio play failed:', error);
          reject(error);
        });

        this.currentAudio.play();
      });
    } catch (error) {
      console.error('Play audio failed:', error);
      throw error;
    }
  }

  stopCurrentAudio() {
    if (this.currentAudio) {
      this.currentAudio.stop();
      this.currentAudio = null;
    }
  }
}

export default new AudioManager();
```

## 🚨 错误处理

### 标准错误响应格式
```json
{
  "code": "ERROR_CODE",
  "message": "错误描述",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_1234567890"
}
```

### 常见错误码
- `INVALID_TOKEN`: Token无效或已过期
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `QUESTION_NOT_FOUND`: 题目不存在
- `SESSION_EXPIRED`: 游戏会话已过期
- `INSUFFICIENT_QUESTIONS`: 可用题目数量不足

## 🔐 安全注意事项

1. **Token管理**: 访问令牌应安全存储，定期刷新
2. **HTTPS**: 生产环境必须使用HTTPS
3. **限流**: 遵守API限流规则，避免被封禁
4. **数据验证**: 前端应验证所有用户输入
5. **错误处理**: 妥善处理网络错误和业务错误

## 📊 性能优化建议

1. **缓存策略**: 题目列表、用户信息等数据应适当缓存
2. **音频预加载**: 游戏开始前预加载音频文件
3. **分页加载**: 排行榜等长列表使用分页加载
4. **请求合并**: 合并相关API请求减少网络开销
5. **错误重试**: 网络请求失败时实现自动重试机制

## 🎯 完整的游戏流程示例

```javascript
// 完整游戏流程示例
async function startCompleteGameFlow() {
  try {
    // 1. 用户登录
    const loginCode = await wx.login();
    const authResponse = await APIClient.wechatLogin(loginCode.code);
    
    // 保存Token
    wx.setStorageSync('accessToken', authResponse.data.accessToken);
    wx.setStorageSync('refreshToken', authResponse.data.refreshToken);

    // 2. 开始游戏
    const gameManager = new GameManager();
    const gameResult = await gameManager.startGame({
      category: 'cantonese',
      difficulty: 1,
      questionCount: 10
    });

    if (!gameResult.success) {
      throw new Error(gameResult.error);
    }

    // 3. 游戏循环
    while (!gameResult.isGameCompleted) {
      // 播放音频
      await AudioManager.playQuestionAudio(gameResult.question.audioUrl);
      
      // 等待用户答题
      const userAnswer = await waitForUserAnswer();
      const answerTime = calculateAnswerTime();
      
      // 提交答案
      const submitResult = await gameManager.submitAnswer(userAnswer, answerTime);
      
      if (submitResult.success) {
        // 显示答题结果
        showAnswerResult(submitResult.result);
        
        if (submitResult.isGameCompleted) {
          // 游戏结束，显示最终统计
          showFinalStats(submitResult.finalStats);
          break;
        } else {
          // 获取下一题
          gameResult.question = await gameManager.getNextQuestion();
        }
      }
    }

    // 4. 显示排行榜
    const leaderboard = await APIClient.getLeaderboard('global', {
      type: 'total_score',
      page: 1,
      size: 50
    });
    
    showLeaderboard(leaderboard.data);

  } catch (error) {
    console.error('Game flow error:', error);
    showErrorMessage(error.message);
  }
}
```

这份API接口指南提供了完整的后端API对接信息，前端开发者可以根据此文档直接进行对接开发。