const http = require('http');

const testCORS = (path, origin) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'OPTIONS',
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      },
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      const corsHeaders = {
        'access-control-allow-origin': res.headers['access-control-allow-origin'],
        'access-control-allow-methods': res.headers['access-control-allow-methods'],
        'access-control-allow-headers': res.headers['access-control-allow-headers'],
        'access-control-allow-credentials': res.headers['access-control-allow-credentials']
      };
      
      resolve({ 
        status: res.statusCode, 
        headers: corsHeaders,
        origin: origin
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
};

async function testCORSConfiguration() {
  console.log('🌐 CORS配置测试\n');

  const testOrigins = [
    'http://localhost:3000',
    'http://127.0.0.1:3000', 
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'https://example.com' // 应该被拒绝
  ];

  const testPaths = [
    '/v1/questions',
    '/v1/auth/wechat/login',
    '/v1/game-sessions'
  ];

  for (const origin of testOrigins) {
    console.log(`🔍 测试源: ${origin}`);
    
    for (const path of testPaths) {
      try {
        const result = await testCORS(path, origin);
        const allowed = result.headers['access-control-allow-origin'];
        
        if (allowed === origin || allowed === '*') {
          console.log(`   ✅ ${path}: 允许访问`);
        } else {
          console.log(`   ❌ ${path}: 拒绝访问 (返回: ${allowed})`);
        }
      } catch (error) {
        console.log(`   💥 ${path}: 错误 - ${error.message}`);
      }
    }
    console.log('');
  }

  // 测试实际请求
  console.log('📝 测试实际API请求：');
  
  const testActualRequest = (path, origin) => {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3001,
        path: path,
        method: 'GET',
        headers: {
          'Origin': origin,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      };

      const req = http.request(options, (res) => {
        const corsHeader = res.headers['access-control-allow-origin'];
        resolve({ 
          status: res.statusCode, 
          corsHeader: corsHeader,
          origin: origin
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  };

  try {
    const result = await testActualRequest('/v1/questions?limit=1', 'http://localhost:3000');
    console.log(`   ✅ GET /v1/questions: HTTP ${result.status}`);
    console.log(`   🌐 CORS Header: ${result.corsHeader}`);
  } catch (error) {
    console.log(`   ❌ GET /v1/questions: ${error.message}`);
  }

  console.log('\n🎉 CORS测试完成！');
}

testCORSConfiguration().catch(console.error);