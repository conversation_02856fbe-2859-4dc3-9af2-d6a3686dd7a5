module.exports = {
  env: {
    node: true,
    es2022: true,
    jest: true
  },
  
  extends: [
    'standard',
    'eslint:recommended'
  ],
  
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module'
  },
  
  rules: {
    // 代码风格
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    
    // 变量和函数
    'no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_' 
    }],
    'no-console': 'off', // 允许console.log用于日志
    'no-debugger': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
    
    // 函数和箭头函数
    'arrow-spacing': 'error',
    'arrow-parens': ['error', 'as-needed'],
    'prefer-arrow-callback': 'error',
    
    // 对象和数组
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'comma-spacing': ['error', { before: false, after: true }],
    
    // 条件和循环
    'curly': ['error', 'all'],
    'brace-style': ['error', '1tbs'],
    'keyword-spacing': 'error',
    'space-before-blocks': 'error',
    
    // 异步代码
    'no-async-promise-executor': 'error',
    'require-await': 'error',
    'no-return-await': 'error',
    
    // 错误处理
    'no-throw-literal': 'error',
    'prefer-promise-reject-errors': 'error',
    
    // 安全相关
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // 性能相关
    'no-loop-func': 'error',
    'no-inner-declarations': 'error'
  },
  
  overrides: [
    {
      files: ['tests/**/*.js'],
      env: {
        jest: true
      },
      rules: {
        'no-unused-expressions': 'off' // Jest断言可能看起来像未使用的表达式
      }
    },
    {
      files: ['scripts/**/*.js'],
      rules: {
        'no-console': 'off' // 脚本文件允许console输出
      }
    }
  ],
  
  globals: {
    // 全局变量定义
    process: 'readonly',
    Buffer: 'readonly',
    __dirname: 'readonly',
    __filename: 'readonly'
  }
};
