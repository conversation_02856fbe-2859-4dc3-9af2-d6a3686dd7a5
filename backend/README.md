# 家乡话猜猜猜 - 后端API服务

基于腾讯云Serverless架构的方言猜谜游戏后端服务。

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL 8.0+
- Redis 6.0+
- npm 或 yarn

### 一键设置开发环境

```bash
# 克隆项目
git clone <repository-url>
cd hometown-dialect-game/backend

# 一键设置开发环境（推荐）
npm run setup
```

这个命令会自动完成：
- 安装项目依赖
- 配置环境变量 
- 初始化数据库
- 启动开发服务器

### 手动设置

如果需要手动设置，请按照以下步骤：

```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填写必要配置

# 3. 初始化数据库
npm run db:init

# 4. 启动开发服务器
npm run dev
```

## 📋 API文档

### 认证服务 (/v1/auth)

| 接口 | 方法 | 描述 | 需要认证 |
|------|------|------|----------|
| `/v1/auth/wechat/login` | POST | 微信登录 | ❌ |
| `/v1/auth/refresh` | POST | 刷新Token | ❌ |
| `/v1/auth/logout` | POST | 退出登录 | ✅ |
| `/v1/auth/me` | GET | 获取当前用户信息 | ✅ |
| `/v1/auth/verify` | GET | 验证Token | ✅ |

### 游戏服务 (/v1/questions, /v1/game-sessions)

| 接口 | 方法 | 描述 | 需要认证 |
|------|------|------|----------|
| `/v1/questions` | GET | 获取题目列表 | ❌ |
| `/v1/questions/{id}` | GET | 获取题目详情 | ❌ |
| `/v1/game-sessions` | POST | 创建游戏会话 | ✅ |
| `/v1/game-sessions/{sessionId}` | GET | 获取游戏会话状态 | ✅ |
| `/v1/game-sessions/{sessionId}/submit` | POST | 提交游戏答案 | ✅ |
| `/v1/game-sessions/{sessionId}/results` | GET | 获取游戏结果 | ✅ |
| `/v1/game-sessions/{sessionId}` | DELETE | 取消游戏会话 | ✅ |

### 音频服务 (/v1/audio)

| 接口 | 方法 | 描述 | 需要认证 |
|------|------|------|----------|
| `/v1/audio` | GET | 获取音频资源列表 | ❌ |
| `/v1/audio/{resourceId}` | GET | 获取音频资源信息 | ❌ |
| `/v1/audio/stats` | GET | 获取音频统计 | ✅ (管理员) |
| `/v1/audio/{resourceId}` | POST | 上传音频资源 | ✅ (管理员) |
| `/v1/audio/{resourceId}` | DELETE | 删除音频资源 | ✅ (管理员) |

### 用户服务 (/v1/users)

| 接口 | 方法 | 描述 | 需要认证 |
|------|------|------|----------|
| `/v1/users/{userId}` | GET | 获取用户信息 | ✅ |
| `/v1/users/{userId}/profile` | PUT | 更新用户资料 | ✅ |
| `/v1/users/{userId}/stats` | GET | 获取用户游戏统计 | ✅ |
| `/v1/users/search` | GET | 搜索用户 | ✅ |

## 🔧 开发

### 本地开发

```bash
# 启动开发服务器
npm run dev

# 服务器将在 http://localhost:3000 启动
# API文档: http://localhost:3000/docs
# 健康检查: http://localhost:3000/health
```

### 数据库操作

```bash
# 初始化数据库（创建表和测试数据）
npm run db:init

# 执行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed
```

### 代码质量

```bash
# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix

# 运行测试
npm test

# 运行测试（监视模式）
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

## 🏗️ 部署

### 开发环境部署

```bash
# 部署到腾讯云（开发环境）
npm run deploy
```

### 生产环境部署

```bash
# 部署到腾讯云（生产环境）
npm run deploy:prod
```

### 查看日志

```bash
# 查看实时日志
npm run logs
```

## 📁 项目结构

```
backend/
├── serverless/           # Serverless函数代码
│   ├── auth/             # 认证服务
│   ├── user/             # 用户服务
│   ├── game/             # 游戏服务
│   ├── audio/            # 音频服务
│   ├── models/           # 数据模型
│   ├── services/         # 业务服务
│   ├── middleware/       # 中间件
│   └── utils/            # 工具函数
├── database/             # 数据库相关
│   └── migrations/       # 数据库迁移文件
├── scripts/              # 脚本文件
├── config/               # 配置文件
└── tests/                # 测试文件
```

## ⚙️ 环境变量

主要环境变量说明：

```bash
# 基础配置
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=hometown_dialect_game

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_ACCESS_SECRET=your_access_secret
JWT_REFRESH_SECRET=your_refresh_secret

# 微信小程序配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 腾讯云COS配置
COS_SECRET_ID=your_secret_id
COS_SECRET_KEY=your_secret_key
COS_BUCKET=your_bucket_name
COS_REGION=ap-beijing
COS_CDN_DOMAIN=your_cdn_domain
```

## 📊 性能指标

- API响应时间目标: < 500ms
- 数据库查询优化: 索引覆盖率 > 90%
- 缓存策略: Redis缓存常用数据
- 限流配置: 防止API滥用
- 成本控制: 月度运营成本 < $200

## 🔒 安全特性

- JWT Token认证
- 请求参数验证
- SQL注入防护
- XSS防护
- 限流和防刷
- 微信登录集成
- 数据加密存储

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   mysql -h localhost -u root -p
   
   # 检查数据库是否存在
   SHOW DATABASES;
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis服务状态
   redis-cli ping
   
   # 检查Redis配置
   redis-cli config get "*"
   ```

3. **微信登录失败**
   - 检查WECHAT_APP_ID和WECHAT_APP_SECRET配置
   - 确认微信小程序域名白名单设置
   - 检查网络连接和微信API可用性

4. **COS音频服务失败**
   - 检查COS密钥配置
   - 确认存储桶权限设置
   - 验证CDN域名配置

### 日志查看

```bash
# 本地开发日志
npm run dev

# 生产环境日志
npm run logs

# 数据库日志
# 查看 MySQL 错误日志和慢查询日志
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

MIT License - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。