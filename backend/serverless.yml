service: hometown-dialect-game-backend

provider:
  name: tencent
  runtime: Nodejs18.15
  region: ap-beijing
  memorySize: 512
  timeout: 30
  environment:
    NODE_ENV: ${opt:stage, 'dev'}
    DB_HOST: ${env:DB_HOST}
    DB_PORT: ${env:DB_PORT, '3306'}
    DB_USER: ${env:DB_USER}
    DB_PASSWORD: ${env:DB_PASSWORD}
    DB_NAME: ${env:DB_NAME}
    REDIS_HOST: ${env:REDIS_HOST}
    REDIS_PORT: ${env:REDIS_PORT, '6379'}
    REDIS_PASSWORD: ${env:REDIS_PASSWORD}
    JWT_ACCESS_SECRET: ${env:JWT_ACCESS_SECRET}
    JWT_REFRESH_SECRET: ${env:JWT_REFRESH_SECRET}
    WECHAT_APP_ID: ${env:WECHAT_APP_ID}
    WECHAT_APP_SECRET: ${env:WECHAT_APP_SECRET}
    COS_SECRET_ID: ${env:COS_SECRET_ID}
    COS_SECRET_KEY: ${env:COS_SECRET_KEY}
    COS_BUCKET: ${env:COS_BUCKET}
    COS_REGION: ${env:COS_REGION}
    WEBSOCKET_ENDPOINT: ${env:WEBSOCKET_ENDPOINT}

plugins:
  - serverless-webpack

custom:
  webpack:
    webpackConfig: webpack.config.js
    includeModules: true
    packager: npm
    excludeFiles: src/**/*.test.js

functions:
  # 认证服务
  auth:
    handler: serverless/auth/handler.main
    events:
      - apigw:
          path: /v1/auth/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}
  
  # 用户服务
  user:
    handler: serverless/user/handler.main
    events:
      - apigw:
          path: /v1/users/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}
  
  # 游戏服务
  game:
    handler: serverless/game/handler.main
    events:
      - apigw:
          path: /v1/game-sessions/{proxy+}
          method: ANY
          cors: true
      - apigw:
          path: /v1/questions/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}
  
  # 音频资源服务
  audio:
    handler: serverless/audio/handler.main
    events:
      - apigw:
          path: /v1/audio/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}
  
  # 排行榜服务
  leaderboard:
    handler: serverless/leaderboard/handler.main
    events:
      - apigw:
          path: /v1/leaderboards/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}
  
  # 社交服务
  social:
    handler: serverless/social/handler.main
    events:
      - apigw:
          path: /v1/shares/{proxy+}
          method: ANY
          cors: true
      - apigw:
          path: /v1/invitations/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  # WebSocket 服务
  websocketConnect:
    handler: serverless/websocket/handler.connect
    events:
      - websocket:
          route: $connect
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  websocketDisconnect:
    handler: serverless/websocket/handler.disconnect
    events:
      - websocket:
          route: $disconnect
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  websocketMessage:
    handler: serverless/websocket/handler.message
    events:
      - websocket:
          route: $default
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  # 围观系统服务
  spectator:
    handler: serverless/spectator/handler.main
    events:
      - apigw:
          path: /v1/spectator/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  # 弹幕系统服务
  danmaku:
    handler: serverless/danmaku/handler.main
    events:
      - apigw:
          path: /v1/danmaku/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

  # 预测游戏服务
  prediction:
    handler: serverless/prediction/handler.main
    events:
      - apigw:
          path: /v1/prediction/{proxy+}
          method: ANY
          cors: true
    vpc:
      vpcId: ${env:VPC_ID}
      subnetId: ${env:SUBNET_ID}

resources:
  Resources:
    # API网关配置
    ApiGatewayRestApi:
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${opt:stage, 'dev'}
        EndpointConfiguration:
          Types:
            - REGIONAL
        BinaryMediaTypes:
          - "*/*"
    
    # 自定义域名（生产环境）
    ApiGatewayDomainName:
      Type: AWS::ApiGateway::DomainName
      Condition: IsProd
      Properties:
        DomainName: api.dialectgame.com
        CertificateArn: ${env:SSL_CERT_ARN}
        SecurityPolicy: TLS_1_2
        EndpointConfiguration:
          Types:
            - REGIONAL
  
  Conditions:
    IsProd: !Equals [${opt:stage, 'dev'}, 'prod']

package:
  excludeDevDependencies: true
  exclude:
    - .git/**
    - .gitignore
    - README.md
    - docs/**
    - scripts/**
    - tests/**
    - "*.md"