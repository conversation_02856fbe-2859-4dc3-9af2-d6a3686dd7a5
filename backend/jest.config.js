/**
 * Jest测试配置
 * 配置测试环境、覆盖率要求、测试文件匹配等
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 根目录
  rootDir: '.',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js'
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/docs/'
  ],

  // 覆盖率收集配置
  collectCoverage: true,
  collectCoverageFrom: [
    'serverless/**/*.js',
    '!serverless/**/*.test.js',
    '!serverless/**/*.spec.js',
    '!serverless/**/dev-handler.js',
    '!**/node_modules/**',
    '!**/coverage/**'
  ],

  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],

  // 覆盖率输出目录
  coverageDirectory: 'coverage',

  // 覆盖率阈值 - 暂时降低以便测试运行
  coverageThreshold: {
    global: {
      branches: 10,
      functions: 10,
      lines: 10,
      statements: 10
    },
    // 关键模块的更高要求
    './serverless/middleware/': {
      branches: 10,
      functions: 10,
      lines: 10,
      statements: 10
    },
    './serverless/utils/': {
      branches: 10,
      functions: 10,
      lines: 10,
      statements: 10
    }
  },

  // 测试设置文件
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.js'
  ],

  // 模块路径映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/serverless/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },

  // 测试超时时间（毫秒）
  testTimeout: 30000,

  // 并发测试数量
  maxConcurrency: 5,

  // 详细输出
  verbose: true,

  // 静默模式（减少输出）
  silent: false,

  // 在第一个测试失败后停止
  bail: false,

  // 强制退出
  forceExit: true,

  // 检测打开的句柄
  detectOpenHandles: true,

  // 检测泄漏
  detectLeaks: false,

  // 全局变量
  globals: {
    'process.env.NODE_ENV': 'test',
    'process.env.JWT_SECRET': 'test_jwt_secret_key_for_testing_only'
  },

  // 模块文件扩展名
  moduleFileExtensions: [
    'js',
    'json',
    'node'
  ],

  // 清除模拟
  clearMocks: true,

  // 恢复模拟
  restoreMocks: true,

  // 监视模式配置
  watchman: true,
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/docs/',
    '/logs/'
  ],

  // 错误处理
  errorOnDeprecated: true,

  // 通知配置
  notify: false,
  notifyMode: 'failure-change',

  // 缓存配置
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  // 模块目录
  moduleDirectories: [
    'node_modules',
    '<rootDir>/serverless',
    '<rootDir>/tests'
  ]
};