
> hometown-dialect-game-backend@1.0.0 dev
> node scripts/dev-server.js

🚀 开发服务器启动成功！
📍 服务地址: http://localhost:3001
💊 健康检查: http://localhost:3001/health
📚 API文档: http://localhost:3001/docs
⏰ 启动时间: 2025-07-31T01:16:48.817Z

可用的API端点:
• POST /v1/auth/wechat/login - 微信登录
• POST /v1/auth/refresh - 刷新Token
• GET /v1/auth/me - 获取当前用户信息
• GET /v1/users/{userId} - 获取用户信息
• GET /v1/questions - 获取题目列表
• POST /v1/game-sessions - 创建游戏会话
• GET /v1/audio/{resourceId} - 获取音频资源

按 Ctrl+C 停止服务器
2025-07-31T01:16:57.761Z - GET /health
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: reconnect. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Database connection failed: AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1134:18)
    at afterConnectMultiple (node:net:1715:7) {
  code: 'ECONNREFUSED',
  fatal: true,
  [errors]: [
    Error: connect ECONNREFUSED ::1:3306
        at createConnectionError (node:net:1678:14)
        at afterConnectMultiple (node:net:1708:16) {
      errno: -61,
      code: 'ECONNREFUSED',
      syscall: 'connect',
      address: '::1',
      port: 3306
    },
    Error: connect ECONNREFUSED 127.0.0.1:3306
        at createConnectionError (node:net:1678:14)
        at afterConnectMultiple (node:net:1708:16) {
      errno: -61,
      code: 'ECONNREFUSED',
      syscall: 'connect',
      address: '127.0.0.1',
      port: 3306
    }
  ]
}
Redis connected
2025-07-31T01:17:21.451Z - GET /v1/questions
Game Handler: GET /v1/questions
2025-07-31T01:17:23.369Z - POST /v1/auth/wechat/login
Auth Handler: POST /v1/auth/wechat/login
2025-07-31T01:17:25.243Z - POST /v1/game-sessions
Game Handler: POST /v1/game-sessions
2025-07-31T01:20:05.331Z - GET /api/health
2025-07-31T01:20:15.378Z - GET /v1/questions
Game Handler: GET /v1/questions
2025-07-31T01:23:09.315Z - GET /v1/questions
Game Handler: GET /v1/questions
2025-07-31T01:23:20.188Z - POST /v1/game-sessions
Game Handler: POST /v1/game-sessions
2025-07-31T01:23:31.451Z - POST /v1/auth/wechat/login
Auth Handler: POST /v1/auth/wechat/login
2025-07-31T01:29:16.876Z - GET /health
2025-07-31T01:29:25.613Z - GET /v1/questions
Game Handler: GET /v1/questions
