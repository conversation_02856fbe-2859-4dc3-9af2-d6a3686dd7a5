# 环境配置
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=hometown_dialect_game

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_ACCESS_SECRET=your_very_long_and_secure_access_secret_key_here_at_least_64_chars
JWT_REFRESH_SECRET=your_very_long_and_secure_refresh_secret_key_here_at_least_64_chars

# 微信小程序配置
WECHAT_APP_ID=your_wechat_miniprogram_app_id
WECHAT_APP_SECRET=your_wechat_miniprogram_app_secret

# 腾讯云COS配置
COS_SECRET_ID=your_tencent_cloud_secret_id
COS_SECRET_KEY=your_tencent_cloud_secret_key
COS_BUCKET=your-audio-bucket-name
COS_REGION=ap-beijing
COS_CDN_DOMAIN=your-cdn-domain.com

# 腾讯云VPC配置 (生产环境)
VPC_ID=vpc-xxxxxxxx
SUBNET_ID=subnet-xxxxxxxx

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=hdg:dev:

# 日志配置
LOG_LEVEL=debug
LOG_FILE=

# CORS配置
CORS_ORIGIN=*

# SSL证书 (生产环境)
SSL_CERT_ARN=arn:aws:acm:region:account-id:certificate/certificate-id