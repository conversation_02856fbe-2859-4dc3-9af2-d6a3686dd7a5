module.exports = {
  // 基本格式化选项
  semi: true,                    // 语句末尾添加分号
  trailingComma: 'none',        // 尾随逗号：无
  singleQuote: true,            // 使用单引号
  doubleQuote: false,           // 不使用双引号
  quoteProps: 'as-needed',      // 对象属性引号：按需
  
  // 缩进和空格
  tabWidth: 2,                  // Tab宽度：2个空格
  useTabs: false,               // 使用空格而不是Tab
  
  // 行宽和换行
  printWidth: 100,              // 行宽：100字符
  endOfLine: 'lf',              // 行尾：LF
  
  // 括号和空格
  bracketSpacing: true,         // 对象括号内空格：{ foo: bar }
  bracketSameLine: false,       // JSX括号不在同一行
  arrowParens: 'avoid',         // 箭头函数参数括号：避免单参数括号
  
  // 特定文件类型配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.yaml',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    }
  ]
};
