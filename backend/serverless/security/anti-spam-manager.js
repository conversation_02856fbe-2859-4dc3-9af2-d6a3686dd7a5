/**
 * 防刷屏与频率限制管理器
 * 
 * 实现弹幕发送的频率限制、防刷屏机制、异常行为检测等
 * 包括用户行为分析和自动封禁机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class AntiSpamManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 频率限制配置
            rateLimit: {
                normal: { messages: 10, window: 60000 },      // 普通用户：10条/分钟
                vip: { messages: 20, window: 60000 },         // VIP用户：20条/分钟
                newUser: { messages: 5, window: 60000 },      // 新用户：5条/分钟
                burst: { messages: 3, window: 10000 }         // 突发限制：3条/10秒
            },
            
            // 刷屏检测配置
            spamDetection: {
                duplicateThreshold: 3,                        // 重复内容阈值
                similarityThreshold: 0.8,                     // 相似度阈值
                rapidFireThreshold: 5,                        // 快速发送阈值
                rapidFireWindow: 5000,                        // 快速发送窗口5秒
                lengthVariationThreshold: 0.1                 // 长度变化阈值
            },
            
            // 异常行为检测
            anomalyDetection: {
                enabled: true,
                scoreThreshold: 80,                           // 异常分数阈值
                patternWindow: 300000,                        // 模式分析窗口5分钟
                behaviorSamples: 50                           // 行为样本数量
            },
            
            // 封禁配置
            punishment: {
                warningThreshold: 3,                          // 警告阈值
                tempBanDuration: [300000, 900000, 3600000],   // 临时封禁时长：5分钟、15分钟、1小时
                permBanThreshold: 5,                          // 永久封禁阈值
                appealCooldown: 86400000                      // 申诉冷却24小时
            }
        };
        
        // 用户行为追踪
        this.userBehavior = new Map();
        
        // 消息历史记录
        this.messageHistory = new Map();
        
        // 封禁列表
        this.banList = new Map();
        
        // 白名单
        this.whitelist = new Set();
        
        // 异常检测器
        this.anomalyDetector = {
            patterns: new Map(),
            scores: new Map(),
            alerts: []
        };
        
        // 统计信息
        this.stats = {
            totalMessages: 0,
            blockedMessages: 0,
            spamDetected: 0,
            anomaliesDetected: 0,
            tempBans: 0,
            permBans: 0,
            warnings: 0,
            blockRate: 0
        };
        
        // 定时器
        this.timers = {
            cleanup: null,
            analysis: null,
            banExpiry: null
        };
        
        this.initialize();
    }
    
    /**
     * 初始化防刷屏管理器
     */
    initialize() {
        console.log('初始化防刷屏与频率限制管理器...');
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        console.log('防刷屏管理器初始化完成');
    }
    
    /**
     * 启动定期任务
     */
    startPeriodicTasks() {
        // 清理过期数据
        this.timers.cleanup = setInterval(() => {
            this.cleanupExpiredData();
        }, 300000); // 5分钟清理一次
        
        // 异常行为分析
        this.timers.analysis = setInterval(() => {
            this.analyzeUserBehavior();
        }, 60000); // 1分钟分析一次
        
        // 检查封禁过期
        this.timers.banExpiry = setInterval(() => {
            this.checkBanExpiry();
        }, 30000); // 30秒检查一次
    }
    
    /**
     * 检查消息是否允许发送
     */
    async checkMessage(userId, content, context = {}) {
        this.stats.totalMessages++;
        
        try {
            // 检查封禁状态
            const banCheck = this.checkBanStatus(userId);
            if (!banCheck.allowed) {
                this.stats.blockedMessages++;
                return {
                    allowed: false,
                    reason: banCheck.reason,
                    banInfo: banCheck.banInfo
                };
            }
            
            // 检查白名单
            if (this.whitelist.has(userId)) {
                this.recordMessage(userId, content, context);
                return { allowed: true, reason: 'whitelist' };
            }
            
            // 检查频率限制
            const rateLimitCheck = this.checkRateLimit(userId, context);
            if (!rateLimitCheck.allowed) {
                this.stats.blockedMessages++;
                await this.handleViolation(userId, 'rate_limit', rateLimitCheck);
                return rateLimitCheck;
            }
            
            // 检查刷屏行为
            const spamCheck = this.checkSpamBehavior(userId, content, context);
            if (!spamCheck.allowed) {
                this.stats.blockedMessages++;
                this.stats.spamDetected++;
                await this.handleViolation(userId, 'spam', spamCheck);
                return spamCheck;
            }
            
            // 记录消息
            this.recordMessage(userId, content, context);
            
            // 更新用户行为
            this.updateUserBehavior(userId, content, context);
            
            return { allowed: true, reason: 'passed' };
            
        } catch (error) {
            console.error('检查消息失败:', error);
            return {
                allowed: false,
                reason: 'system_error',
                error: error.message
            };
        }
    }
    
    /**
     * 检查封禁状态
     */
    checkBanStatus(userId) {
        const banInfo = this.banList.get(userId);
        
        if (!banInfo) {
            return { allowed: true };
        }
        
        // 检查永久封禁
        if (banInfo.type === 'permanent') {
            return {
                allowed: false,
                reason: 'permanent_ban',
                banInfo
            };
        }
        
        // 检查临时封禁
        if (banInfo.type === 'temporary' && Date.now() < banInfo.expireTime) {
            return {
                allowed: false,
                reason: 'temporary_ban',
                banInfo: {
                    ...banInfo,
                    remainingTime: banInfo.expireTime - Date.now()
                }
            };
        }
        
        // 临时封禁已过期，移除封禁
        if (banInfo.type === 'temporary' && Date.now() >= banInfo.expireTime) {
            this.banList.delete(userId);
            this.emit('banExpired', { userId, banInfo });
        }
        
        return { allowed: true };
    }
    
    /**
     * 检查频率限制
     */
    checkRateLimit(userId, context) {
        const now = Date.now();
        const userType = this.getUserType(userId, context);
        const limits = this.config.rateLimit[userType] || this.config.rateLimit.normal;
        const burstLimits = this.config.rateLimit.burst;
        
        // 获取用户行为记录
        let behavior = this.userBehavior.get(userId);
        if (!behavior) {
            behavior = {
                messages: [],
                violations: [],
                score: 0,
                firstSeen: now,
                lastActivity: now
            };
            this.userBehavior.set(userId, behavior);
        }
        
        // 清理过期消息记录
        behavior.messages = behavior.messages.filter(msg => 
            now - msg.timestamp < Math.max(limits.window, burstLimits.window)
        );
        
        // 检查突发限制
        const recentMessages = behavior.messages.filter(msg => 
            now - msg.timestamp < burstLimits.window
        );
        
        if (recentMessages.length >= burstLimits.messages) {
            return {
                allowed: false,
                reason: 'burst_limit_exceeded',
                limit: burstLimits.messages,
                window: burstLimits.window,
                current: recentMessages.length
            };
        }
        
        // 检查常规频率限制
        const windowMessages = behavior.messages.filter(msg => 
            now - msg.timestamp < limits.window
        );
        
        if (windowMessages.length >= limits.messages) {
            return {
                allowed: false,
                reason: 'rate_limit_exceeded',
                limit: limits.messages,
                window: limits.window,
                current: windowMessages.length
            };
        }
        
        return { allowed: true };
    }
    
    /**
     * 检查刷屏行为
     */
    checkSpamBehavior(userId, content, context) {
        const userMessages = this.messageHistory.get(userId) || [];
        const now = Date.now();
        const recentMessages = userMessages.filter(msg => 
            now - msg.timestamp < 300000 // 5分钟内的消息
        );
        
        // 检查重复内容
        const duplicateCount = recentMessages.filter(msg => 
            msg.content === content
        ).length;
        
        if (duplicateCount >= this.config.spamDetection.duplicateThreshold) {
            return {
                allowed: false,
                reason: 'duplicate_content',
                duplicateCount,
                threshold: this.config.spamDetection.duplicateThreshold
            };
        }
        
        // 检查相似内容
        const similarMessages = recentMessages.filter(msg => 
            this.calculateSimilarity(msg.content, content) > this.config.spamDetection.similarityThreshold
        );
        
        if (similarMessages.length >= this.config.spamDetection.duplicateThreshold) {
            return {
                allowed: false,
                reason: 'similar_content',
                similarCount: similarMessages.length,
                threshold: this.config.spamDetection.duplicateThreshold
            };
        }
        
        // 检查快速发送
        const rapidFireMessages = recentMessages.filter(msg => 
            now - msg.timestamp < this.config.spamDetection.rapidFireWindow
        );
        
        if (rapidFireMessages.length >= this.config.spamDetection.rapidFireThreshold) {
            return {
                allowed: false,
                reason: 'rapid_fire',
                count: rapidFireMessages.length,
                threshold: this.config.spamDetection.rapidFireThreshold,
                window: this.config.spamDetection.rapidFireWindow
            };
        }
        
        // 检查长度变化模式（可能的机器人行为）
        if (recentMessages.length >= 5) {
            const lengths = recentMessages.slice(-5).map(msg => msg.content.length);
            const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
            const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
            const coefficient = Math.sqrt(variance) / avgLength;
            
            if (coefficient < this.config.spamDetection.lengthVariationThreshold) {
                return {
                    allowed: false,
                    reason: 'pattern_detected',
                    pattern: 'uniform_length',
                    coefficient
                };
            }
        }
        
        return { allowed: true };
    }
    
    /**
     * 记录消息
     */
    recordMessage(userId, content, context) {
        const now = Date.now();
        
        // 记录到消息历史
        let userMessages = this.messageHistory.get(userId) || [];
        userMessages.push({
            content,
            timestamp: now,
            context
        });
        
        // 保持历史记录限制
        if (userMessages.length > 100) {
            userMessages = userMessages.slice(-100);
        }
        
        this.messageHistory.set(userId, userMessages);
        
        // 更新用户行为记录
        let behavior = this.userBehavior.get(userId);
        if (!behavior) {
            behavior = {
                messages: [],
                violations: [],
                score: 0,
                firstSeen: now,
                lastActivity: now
            };
        }
        
        behavior.messages.push({
            content,
            timestamp: now,
            length: content.length
        });
        behavior.lastActivity = now;
        
        this.userBehavior.set(userId, behavior);
    }
    
    /**
     * 更新用户行为
     */
    updateUserBehavior(userId, content, context) {
        const behavior = this.userBehavior.get(userId);
        if (!behavior) return;
        
        // 计算行为分数
        const score = this.calculateBehaviorScore(userId, content, context);
        behavior.score = (behavior.score * 0.9) + (score * 0.1); // 指数移动平均
        
        // 检查异常行为
        if (this.config.anomalyDetection.enabled && 
            behavior.score > this.config.anomalyDetection.scoreThreshold) {
            this.detectAnomalousUser(userId, behavior);
        }
    }
    
    /**
     * 计算行为分数
     */
    calculateBehaviorScore(userId, content, context) {
        let score = 0;
        const behavior = this.userBehavior.get(userId);
        if (!behavior) return score;
        
        const now = Date.now();
        const recentMessages = behavior.messages.filter(msg => 
            now - msg.timestamp < 300000 // 5分钟内
        );
        
        // 发送频率分数
        const frequency = recentMessages.length / 5; // 每分钟消息数
        score += Math.min(frequency * 10, 50);
        
        // 内容多样性分数
        const uniqueContents = new Set(recentMessages.map(msg => msg.content)).size;
        const diversityRatio = uniqueContents / Math.max(recentMessages.length, 1);
        score += (1 - diversityRatio) * 30;
        
        // 长度一致性分数
        if (recentMessages.length >= 3) {
            const lengths = recentMessages.map(msg => msg.length);
            const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
            const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
            const coefficient = Math.sqrt(variance) / Math.max(avgLength, 1);
            score += (1 - Math.min(coefficient, 1)) * 20;
        }
        
        return Math.min(score, 100);
    }
    
    /**
     * 检测异常用户
     */
    detectAnomalousUser(userId, behavior) {
        this.stats.anomaliesDetected++;
        
        const anomaly = {
            userId,
            score: behavior.score,
            timestamp: Date.now(),
            patterns: this.analyzeUserPatterns(userId),
            severity: behavior.score > 90 ? 'high' : 'medium'
        };
        
        this.anomalyDetector.alerts.push(anomaly);
        
        // 保持警报历史限制
        if (this.anomalyDetector.alerts.length > 100) {
            this.anomalyDetector.alerts.shift();
        }
        
        this.emit('anomalyDetected', anomaly);
        
        // 高严重性异常自动处理
        if (anomaly.severity === 'high') {
            this.handleViolation(userId, 'anomaly', { anomaly });
        }
    }
    
    /**
     * 分析用户模式
     */
    analyzeUserPatterns(userId) {
        const behavior = this.userBehavior.get(userId);
        if (!behavior) return [];
        
        const patterns = [];
        const recentMessages = behavior.messages.filter(msg => 
            Date.now() - msg.timestamp < 300000
        );
        
        // 时间间隔模式
        if (recentMessages.length >= 3) {
            const intervals = [];
            for (let i = 1; i < recentMessages.length; i++) {
                intervals.push(recentMessages[i].timestamp - recentMessages[i-1].timestamp);
            }
            
            const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
            const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
            
            if (Math.sqrt(variance) < avgInterval * 0.2) {
                patterns.push({
                    type: 'regular_intervals',
                    avgInterval,
                    variance: Math.sqrt(variance)
                });
            }
        }
        
        // 内容长度模式
        const lengths = recentMessages.map(msg => msg.length);
        if (lengths.length >= 3) {
            const uniqueLengths = new Set(lengths).size;
            if (uniqueLengths <= 2) {
                patterns.push({
                    type: 'uniform_length',
                    lengths: Array.from(new Set(lengths))
                });
            }
        }
        
        return patterns;
    }
    
    /**
     * 处理违规行为
     */
    async handleViolation(userId, violationType, details) {
        let behavior = this.userBehavior.get(userId);
        if (!behavior) {
            behavior = {
                messages: [],
                violations: [],
                score: 0,
                firstSeen: Date.now(),
                lastActivity: Date.now()
            };
            this.userBehavior.set(userId, behavior);
        }
        
        // 记录违规
        const violation = {
            type: violationType,
            timestamp: Date.now(),
            details
        };
        
        behavior.violations.push(violation);
        
        // 保持违规记录限制
        if (behavior.violations.length > 50) {
            behavior.violations = behavior.violations.slice(-50);
        }
        
        // 计算惩罚级别
        const recentViolations = behavior.violations.filter(v => 
            Date.now() - v.timestamp < 3600000 // 1小时内的违规
        );
        
        const punishmentLevel = this.calculatePunishmentLevel(recentViolations);
        
        // 执行惩罚
        await this.executePunishment(userId, punishmentLevel, violation);
        
        this.emit('violationHandled', {
            userId,
            violation,
            punishmentLevel,
            totalViolations: behavior.violations.length
        });
    }
    
    /**
     * 计算惩罚级别
     */
    calculatePunishmentLevel(violations) {
        const severityWeights = {
            rate_limit: 1,
            spam: 2,
            anomaly: 3,
            content_violation: 2
        };
        
        let totalSeverity = 0;
        violations.forEach(v => {
            totalSeverity += severityWeights[v.type] || 1;
        });
        
        if (totalSeverity >= 10) return 'permanent_ban';
        if (totalSeverity >= 7) return 'temp_ban_3';
        if (totalSeverity >= 5) return 'temp_ban_2';
        if (totalSeverity >= 3) return 'temp_ban_1';
        return 'warning';
    }
    
    /**
     * 执行惩罚
     */
    async executePunishment(userId, level, violation) {
        const now = Date.now();
        
        switch (level) {
            case 'warning':
                this.stats.warnings++;
                this.emit('userWarned', { userId, violation });
                break;
                
            case 'temp_ban_1':
                this.stats.tempBans++;
                this.banList.set(userId, {
                    type: 'temporary',
                    level: 1,
                    startTime: now,
                    expireTime: now + this.config.punishment.tempBanDuration[0],
                    reason: violation.type,
                    details: violation.details
                });
                this.emit('userTempBanned', { userId, duration: this.config.punishment.tempBanDuration[0] });
                break;
                
            case 'temp_ban_2':
                this.stats.tempBans++;
                this.banList.set(userId, {
                    type: 'temporary',
                    level: 2,
                    startTime: now,
                    expireTime: now + this.config.punishment.tempBanDuration[1],
                    reason: violation.type,
                    details: violation.details
                });
                this.emit('userTempBanned', { userId, duration: this.config.punishment.tempBanDuration[1] });
                break;
                
            case 'temp_ban_3':
                this.stats.tempBans++;
                this.banList.set(userId, {
                    type: 'temporary',
                    level: 3,
                    startTime: now,
                    expireTime: now + this.config.punishment.tempBanDuration[2],
                    reason: violation.type,
                    details: violation.details
                });
                this.emit('userTempBanned', { userId, duration: this.config.punishment.tempBanDuration[2] });
                break;
                
            case 'permanent_ban':
                this.stats.permBans++;
                this.banList.set(userId, {
                    type: 'permanent',
                    startTime: now,
                    reason: violation.type,
                    details: violation.details
                });
                this.emit('userPermBanned', { userId });
                break;
        }
    }
    
    /**
     * 计算文本相似度
     */
    calculateSimilarity(text1, text2) {
        if (text1 === text2) return 1.0;
        
        const len1 = text1.length;
        const len2 = text2.length;
        
        if (len1 === 0 || len2 === 0) return 0.0;
        
        // 简单的编辑距离相似度
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
        
        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;
        
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = text1[i-1] === text2[j-1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i-1][j] + 1,
                    matrix[i][j-1] + 1,
                    matrix[i-1][j-1] + cost
                );
            }
        }
        
        const distance = matrix[len1][len2];
        const maxLen = Math.max(len1, len2);
        
        return 1 - (distance / maxLen);
    }
    
    /**
     * 获取用户类型
     */
    getUserType(userId, context) {
        if (context.isVip) return 'vip';
        if (context.isNewUser) return 'newUser';
        return 'normal';
    }
    
    /**
     * 清理过期数据
     */
    cleanupExpiredData() {
        const now = Date.now();
        const maxAge = 3600000; // 1小时
        
        // 清理用户行为数据
        for (const [userId, behavior] of this.userBehavior) {
            if (now - behavior.lastActivity > maxAge * 24) { // 24小时未活跃
                this.userBehavior.delete(userId);
                continue;
            }
            
            // 清理过期消息
            behavior.messages = behavior.messages.filter(msg => 
                now - msg.timestamp < maxAge
            );
            
            // 清理过期违规记录
            behavior.violations = behavior.violations.filter(v => 
                now - v.timestamp < maxAge * 24
            );
        }
        
        // 清理消息历史
        for (const [userId, messages] of this.messageHistory) {
            const filteredMessages = messages.filter(msg => 
                now - msg.timestamp < maxAge
            );
            
            if (filteredMessages.length === 0) {
                this.messageHistory.delete(userId);
            } else {
                this.messageHistory.set(userId, filteredMessages);
            }
        }
        
        // 清理异常警报
        this.anomalyDetector.alerts = this.anomalyDetector.alerts.filter(alert => 
            now - alert.timestamp < maxAge * 24
        );
    }
    
    /**
     * 分析用户行为
     */
    analyzeUserBehavior() {
        // 更新统计信息
        this.updateStats();
        
        // 分析全局模式
        this.analyzeGlobalPatterns();
    }
    
    /**
     * 检查封禁过期
     */
    checkBanExpiry() {
        const now = Date.now();
        
        for (const [userId, banInfo] of this.banList) {
            if (banInfo.type === 'temporary' && now >= banInfo.expireTime) {
                this.banList.delete(userId);
                this.emit('banExpired', { userId, banInfo });
            }
        }
    }
    
    /**
     * 分析全局模式
     */
    analyzeGlobalPatterns() {
        // 分析全局异常模式
        const recentAnomalies = this.anomalyDetector.alerts.filter(alert => 
            Date.now() - alert.timestamp < 3600000 // 1小时内
        );
        
        if (recentAnomalies.length > 10) {
            this.emit('globalAnomalySpike', {
                count: recentAnomalies.length,
                timeWindow: 3600000
            });
        }
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        this.stats.blockRate = this.stats.totalMessages > 0 ? 
            this.stats.blockedMessages / this.stats.totalMessages : 0;
        
        this.emit('statsUpdate', this.getStats());
    }
    
    /**
     * 添加到白名单
     */
    addToWhitelist(userId) {
        this.whitelist.add(userId);
        this.emit('whitelistAdded', { userId });
    }
    
    /**
     * 从白名单移除
     */
    removeFromWhitelist(userId) {
        this.whitelist.delete(userId);
        this.emit('whitelistRemoved', { userId });
    }
    
    /**
     * 手动封禁用户
     */
    manualBan(userId, type, duration, reason) {
        const now = Date.now();
        const banInfo = {
            type,
            startTime: now,
            reason,
            manual: true
        };
        
        if (type === 'temporary') {
            banInfo.expireTime = now + duration;
        }
        
        this.banList.set(userId, banInfo);
        this.emit('manualBan', { userId, banInfo });
    }
    
    /**
     * 解除封禁
     */
    unban(userId, reason) {
        const banInfo = this.banList.get(userId);
        if (banInfo) {
            this.banList.delete(userId);
            this.emit('userUnbanned', { userId, banInfo, reason });
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            activeUsers: this.userBehavior.size,
            bannedUsers: this.banList.size,
            whitelistedUsers: this.whitelist.size,
            pendingAnomalies: this.anomalyDetector.alerts.length,
            messageHistory: this.messageHistory.size
        };
    }
    
    /**
     * 获取用户状态
     */
    getUserStatus(userId) {
        const behavior = this.userBehavior.get(userId);
        const banInfo = this.banList.get(userId);
        const isWhitelisted = this.whitelist.has(userId);
        
        return {
            userId,
            isWhitelisted,
            isBanned: !!banInfo,
            banInfo,
            behavior: behavior ? {
                score: behavior.score,
                violationCount: behavior.violations.length,
                messageCount: behavior.messages.length,
                firstSeen: behavior.firstSeen,
                lastActivity: behavior.lastActivity
            } : null
        };
    }
    
    /**
     * 关闭防刷屏管理器
     */
    close() {
        console.log('关闭防刷屏与频率限制管理器...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.userBehavior.clear();
        this.messageHistory.clear();
        this.banList.clear();
        this.whitelist.clear();
        this.anomalyDetector.alerts = [];
        
        console.log('防刷屏管理器已关闭');
    }
}

module.exports = AntiSpamManager;
