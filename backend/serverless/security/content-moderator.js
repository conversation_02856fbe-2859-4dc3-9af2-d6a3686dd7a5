/**
 * 弹幕内容审核系统
 * 
 * 实现弹幕内容的实时审核，包括敏感词过滤、AI内容检测、人工审核队列等
 * 目标：99%+的不合规内容被拦截
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class ContentModerator extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 审核策略
            moderation: {
                enableSensitiveWords: true,
                enableAIDetection: true,
                enableManualReview: true,
                strictMode: options.strictMode || false,
                autoRejectThreshold: 0.8,    // 自动拒绝阈值
                manualReviewThreshold: 0.6   // 人工审核阈值
            },
            
            // 性能配置
            performance: {
                maxConcurrentChecks: 100,
                cacheSize: 10000,
                cacheTTL: 3600000,           // 1小时缓存
                batchSize: 50,
                processingTimeout: 5000      // 5秒超时
            },
            
            // 限流配置
            rateLimit: {
                userLimit: 10,               // 用户每分钟10条
                roomLimit: 1000,             // 房间每分钟1000条
                globalLimit: 10000,          // 全局每分钟10000条
                windowSize: 60000            // 1分钟窗口
            }
        };
        
        // 敏感词库
        this.sensitiveWords = {
            // 政治敏感词
            political: new Set([
                '政治', '政府', '领导人', '党派', '选举', '抗议', '示威',
                '革命', '政权', '独立', '分裂', '台独', '港独', '藏独'
            ]),
            
            // 色情低俗词汇
            sexual: new Set([
                '色情', '黄色', '裸体', '性爱', '做爱', '性交', '淫秽',
                '色狼', '强奸', '性骚扰', '卖淫', '嫖娼', '援交'
            ]),
            
            // 暴力恐怖词汇
            violence: new Set([
                '杀人', '谋杀', '自杀', '恐怖', '爆炸', '炸弹', '枪击',
                '暴力', '血腥', '残忍', '虐待', '酷刑', '死亡'
            ]),
            
            // 赌博相关
            gambling: new Set([
                '赌博', '赌场', '赌钱', '博彩', '彩票', '六合彩', '赌球',
                '老虎机', '轮盘', '扑克', '麻将', '斗地主', '炸金花'
            ]),
            
            // 毒品相关
            drugs: new Set([
                '毒品', '大麻', '海洛因', '可卡因', '冰毒', '摇头丸', '鸦片',
                '吸毒', '贩毒', '制毒', '毒贩', '毒瘾', '戒毒'
            ]),
            
            // 诈骗相关
            fraud: new Set([
                '诈骗', '骗钱', '传销', '非法集资', '庞氏骗局', '网络诈骗',
                '电信诈骗', '刷单', '洗钱', '假币', '伪造', '仿冒'
            ])
        };
        
        // 正则表达式规则
        this.regexRules = [
            // 手机号码
            { pattern: /1[3-9]\d{9}/, type: 'phone', severity: 'medium' },
            
            // QQ号码
            { pattern: /[1-9]\d{4,10}/, type: 'qq', severity: 'low' },
            
            // 微信号
            { pattern: /[a-zA-Z][a-zA-Z0-9_-]{5,19}/, type: 'wechat', severity: 'low' },
            
            // 网址链接
            { pattern: /(https?:\/\/[^\s]+)|(www\.[^\s]+)/i, type: 'url', severity: 'high' },
            
            // 重复字符
            { pattern: /(.)\1{4,}/, type: 'repeat', severity: 'low' },
            
            // 特殊符号刷屏
            { pattern: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{5,}/, type: 'symbols', severity: 'medium' }
        ];
        
        // 审核状态
        this.moderationState = {
            totalChecked: 0,
            totalBlocked: 0,
            totalPassed: 0,
            totalManualReview: 0,
            blockRate: 0,
            avgProcessingTime: 0
        };
        
        // 缓存系统
        this.cache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            hitRate: 0
        };
        
        // 限流追踪
        this.rateLimitTracker = {
            users: new Map(),
            rooms: new Map(),
            global: { count: 0, windowStart: Date.now() }
        };
        
        // 人工审核队列
        this.manualReviewQueue = [];
        
        // AI检测模拟（实际项目中应该调用真实的AI服务）
        this.aiDetector = {
            enabled: true,
            confidence: 0.85,
            processingTime: 200
        };
        
        // 统计信息
        this.stats = {
            sensitiveWordBlocks: 0,
            aiDetectionBlocks: 0,
            regexBlocks: 0,
            rateLimitBlocks: 0,
            manualReviews: 0,
            falsePositives: 0,
            falseNegatives: 0
        };
        
        this.initialize();
    }
    
    /**
     * 初始化内容审核系统
     */
    initialize() {
        console.log('初始化弹幕内容审核系统...');
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        // 预热缓存
        this.warmupCache();
        
        console.log(`内容审核系统初始化完成，敏感词库包含 ${this.getTotalSensitiveWords()} 个词汇`);
    }
    
    /**
     * 启动定期任务
     */
    startPeriodicTasks() {
        // 清理限流计数器
        setInterval(() => {
            this.cleanupRateLimitCounters();
        }, 60000); // 每分钟清理一次
        
        // 清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, 300000); // 每5分钟清理一次
        
        // 处理人工审核队列
        setInterval(() => {
            this.processManualReviewQueue();
        }, 10000); // 每10秒处理一次
        
        // 更新统计信息
        setInterval(() => {
            this.updateStats();
        }, 30000); // 每30秒更新一次
    }
    
    /**
     * 预热缓存
     */
    warmupCache() {
        // 预缓存一些常见的安全内容
        const safeContents = [
            '666', '厉害', '好棒', '加油', '支持', '赞', '哈哈',
            '太强了', '牛逼', '不错', '可以', '真的假的', '哇'
        ];
        
        safeContents.forEach(content => {
            const result = {
                passed: true,
                confidence: 1.0,
                reasons: [],
                timestamp: Date.now()
            };
            this.cache.set(this.getCacheKey(content), result);
        });
    }
    
    /**
     * 审核内容
     */
    async moderateContent(content, context = {}) {
        const startTime = Date.now();
        
        try {
            // 基本验证
            if (!content || typeof content !== 'string') {
                return this.createModerationResult(false, 1.0, ['内容为空或格式错误']);
            }
            
            // 长度检查
            if (content.length > 500) {
                return this.createModerationResult(false, 1.0, ['内容过长']);
            }
            
            // 检查缓存
            const cacheKey = this.getCacheKey(content);
            const cached = this.cache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.config.performance.cacheTTL) {
                this.cacheStats.hits++;
                return cached;
            }
            this.cacheStats.misses++;
            
            // 限流检查
            const rateLimitResult = this.checkRateLimit(context);
            if (!rateLimitResult.passed) {
                this.stats.rateLimitBlocks++;
                return this.createModerationResult(false, 1.0, ['发送频率过快']);
            }
            
            // 执行各种检查
            const checks = await Promise.all([
                this.checkSensitiveWords(content),
                this.checkRegexRules(content),
                this.checkAIDetection(content, context)
            ]);
            
            // 合并检查结果
            const result = this.combineCheckResults(checks);
            
            // 缓存结果
            this.cache.set(cacheKey, result);
            
            // 更新统计
            this.updateModerationStats(result);
            
            // 记录处理时间
            const processingTime = Date.now() - startTime;
            this.updateProcessingTime(processingTime);
            
            return result;
            
        } catch (error) {
            console.error('内容审核失败:', error);
            return this.createModerationResult(false, 1.0, ['审核系统错误']);
        }
    }
    
    /**
     * 检查敏感词
     */
    async checkSensitiveWords(content) {
        const normalizedContent = content.toLowerCase().replace(/\s+/g, '');
        const violations = [];
        let maxSeverity = 0;
        
        // 检查各类敏感词
        for (const [category, words] of Object.entries(this.sensitiveWords)) {
            for (const word of words) {
                if (normalizedContent.includes(word.toLowerCase())) {
                    violations.push(`包含${category}敏感词: ${word}`);
                    maxSeverity = Math.max(maxSeverity, this.getSeverityScore(category));
                }
            }
        }
        
        if (violations.length > 0) {
            this.stats.sensitiveWordBlocks++;
        }
        
        return {
            passed: violations.length === 0,
            confidence: violations.length > 0 ? maxSeverity : 0,
            reasons: violations,
            type: 'sensitive_words'
        };
    }
    
    /**
     * 检查正则规则
     */
    async checkRegexRules(content) {
        const violations = [];
        let maxSeverity = 0;
        
        for (const rule of this.regexRules) {
            if (rule.pattern.test(content)) {
                violations.push(`匹配${rule.type}规则`);
                maxSeverity = Math.max(maxSeverity, this.getSeverityValue(rule.severity));
            }
        }
        
        if (violations.length > 0) {
            this.stats.regexBlocks++;
        }
        
        return {
            passed: violations.length === 0,
            confidence: violations.length > 0 ? maxSeverity : 0,
            reasons: violations,
            type: 'regex_rules'
        };
    }
    
    /**
     * AI内容检测
     */
    async checkAIDetection(content, context) {
        if (!this.config.moderation.enableAIDetection || !this.aiDetector.enabled) {
            return {
                passed: true,
                confidence: 0,
                reasons: [],
                type: 'ai_detection'
            };
        }
        
        // 模拟AI检测延迟
        await new Promise(resolve => setTimeout(resolve, this.aiDetector.processingTime));
        
        // 模拟AI检测结果
        const suspiciousPatterns = [
            /[买卖][QQ微信]/i,
            /加[QQ微信]/i,
            /代[刷充]/i,
            /[免费].*[领取]/i,
            /[点击].*[链接]/i
        ];
        
        let aiConfidence = 0;
        const violations = [];
        
        for (const pattern of suspiciousPatterns) {
            if (pattern.test(content)) {
                aiConfidence = Math.max(aiConfidence, 0.7 + Math.random() * 0.3);
                violations.push('AI检测到可疑内容');
                break;
            }
        }
        
        // 随机增加一些噪声
        if (aiConfidence === 0 && Math.random() < 0.05) {
            aiConfidence = Math.random() * 0.3;
        }
        
        const passed = aiConfidence < this.config.moderation.manualReviewThreshold;
        
        if (!passed) {
            this.stats.aiDetectionBlocks++;
        }
        
        return {
            passed,
            confidence: aiConfidence,
            reasons: violations,
            type: 'ai_detection'
        };
    }
    
    /**
     * 合并检查结果
     */
    combineCheckResults(checks) {
        let maxConfidence = 0;
        const allReasons = [];
        let passed = true;
        
        for (const check of checks) {
            if (!check.passed) {
                passed = false;
                maxConfidence = Math.max(maxConfidence, check.confidence);
                allReasons.push(...check.reasons);
            }
        }
        
        // 决定最终处理方式
        let action = 'pass';
        if (!passed) {
            if (maxConfidence >= this.config.moderation.autoRejectThreshold) {
                action = 'reject';
            } else if (maxConfidence >= this.config.moderation.manualReviewThreshold) {
                action = 'manual_review';
            } else {
                action = 'pass';
                passed = true; // 置信度不够，放行
            }
        }
        
        return this.createModerationResult(passed, maxConfidence, allReasons, action);
    }
    
    /**
     * 创建审核结果
     */
    createModerationResult(passed, confidence, reasons, action = null) {
        return {
            passed,
            confidence,
            reasons: reasons || [],
            action: action || (passed ? 'pass' : 'reject'),
            timestamp: Date.now()
        };
    }
    
    /**
     * 检查限流
     */
    checkRateLimit(context) {
        const now = Date.now();
        const windowSize = this.config.rateLimit.windowSize;
        
        // 检查全局限流
        if (now - this.rateLimitTracker.global.windowStart > windowSize) {
            this.rateLimitTracker.global = { count: 0, windowStart: now };
        }
        
        if (this.rateLimitTracker.global.count >= this.config.rateLimit.globalLimit) {
            return { passed: false, reason: 'global_rate_limit' };
        }
        
        // 检查用户限流
        if (context.userId) {
            const userKey = `user_${context.userId}`;
            const userLimit = this.rateLimitTracker.users.get(userKey) || { count: 0, windowStart: now };
            
            if (now - userLimit.windowStart > windowSize) {
                userLimit.count = 0;
                userLimit.windowStart = now;
            }
            
            if (userLimit.count >= this.config.rateLimit.userLimit) {
                return { passed: false, reason: 'user_rate_limit' };
            }
            
            userLimit.count++;
            this.rateLimitTracker.users.set(userKey, userLimit);
        }
        
        // 检查房间限流
        if (context.roomId) {
            const roomKey = `room_${context.roomId}`;
            const roomLimit = this.rateLimitTracker.rooms.get(roomKey) || { count: 0, windowStart: now };
            
            if (now - roomLimit.windowStart > windowSize) {
                roomLimit.count = 0;
                roomLimit.windowStart = now;
            }
            
            if (roomLimit.count >= this.config.rateLimit.roomLimit) {
                return { passed: false, reason: 'room_rate_limit' };
            }
            
            roomLimit.count++;
            this.rateLimitTracker.rooms.set(roomKey, roomLimit);
        }
        
        // 更新全局计数
        this.rateLimitTracker.global.count++;
        
        return { passed: true };
    }
    
    /**
     * 添加到人工审核队列
     */
    addToManualReview(content, context, moderationResult) {
        const reviewItem = {
            id: crypto.randomUUID(),
            content,
            context,
            moderationResult,
            timestamp: Date.now(),
            status: 'pending'
        };
        
        this.manualReviewQueue.push(reviewItem);
        this.stats.manualReviews++;
        
        this.emit('manualReviewAdded', reviewItem);
        
        return reviewItem.id;
    }
    
    /**
     * 处理人工审核队列
     */
    processManualReviewQueue() {
        // 模拟人工审核处理
        const batchSize = Math.min(5, this.manualReviewQueue.length);
        
        for (let i = 0; i < batchSize; i++) {
            const item = this.manualReviewQueue.shift();
            if (item) {
                // 模拟审核决定
                const decision = Math.random() > 0.3 ? 'approve' : 'reject';
                
                item.status = 'completed';
                item.decision = decision;
                item.reviewTime = Date.now();
                
                this.emit('manualReviewCompleted', item);
            }
        }
    }
    
    /**
     * 获取缓存键
     */
    getCacheKey(content) {
        return crypto.createHash('md5').update(content).digest('hex');
    }
    
    /**
     * 获取严重程度分数
     */
    getSeverityScore(category) {
        const scores = {
            political: 1.0,
            sexual: 0.9,
            violence: 0.9,
            gambling: 0.8,
            drugs: 1.0,
            fraud: 0.8
        };
        return scores[category] || 0.5;
    }
    
    /**
     * 获取严重程度值
     */
    getSeverityValue(severity) {
        const values = {
            low: 0.3,
            medium: 0.6,
            high: 0.9
        };
        return values[severity] || 0.5;
    }
    
    /**
     * 清理限流计数器
     */
    cleanupRateLimitCounters() {
        const now = Date.now();
        const windowSize = this.config.rateLimit.windowSize;
        
        // 清理用户限流
        for (const [key, limit] of this.rateLimitTracker.users) {
            if (now - limit.windowStart > windowSize * 2) {
                this.rateLimitTracker.users.delete(key);
            }
        }
        
        // 清理房间限流
        for (const [key, limit] of this.rateLimitTracker.rooms) {
            if (now - limit.windowStart > windowSize * 2) {
                this.rateLimitTracker.rooms.delete(key);
            }
        }
    }
    
    /**
     * 清理缓存
     */
    cleanupCache() {
        const now = Date.now();
        const ttl = this.config.performance.cacheTTL;
        
        for (const [key, result] of this.cache) {
            if (now - result.timestamp > ttl) {
                this.cache.delete(key);
            }
        }
        
        // 如果缓存过大，清理最旧的条目
        if (this.cache.size > this.config.performance.cacheSize) {
            const entries = Array.from(this.cache.entries())
                .sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const toDelete = entries.slice(0, this.cache.size - this.config.performance.cacheSize);
            toDelete.forEach(([key]) => this.cache.delete(key));
        }
    }
    
    /**
     * 更新审核统计
     */
    updateModerationStats(result) {
        this.moderationState.totalChecked++;
        
        if (result.passed) {
            this.moderationState.totalPassed++;
        } else {
            this.moderationState.totalBlocked++;
        }
        
        if (result.action === 'manual_review') {
            this.moderationState.totalManualReview++;
        }
        
        this.moderationState.blockRate = 
            this.moderationState.totalBlocked / this.moderationState.totalChecked;
    }
    
    /**
     * 更新处理时间
     */
    updateProcessingTime(time) {
        const currentAvg = this.moderationState.avgProcessingTime;
        const totalChecked = this.moderationState.totalChecked;
        
        this.moderationState.avgProcessingTime = 
            (currentAvg * (totalChecked - 1) + time) / totalChecked;
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        // 更新缓存命中率
        const totalCacheRequests = this.cacheStats.hits + this.cacheStats.misses;
        this.cacheStats.hitRate = totalCacheRequests > 0 ? 
            this.cacheStats.hits / totalCacheRequests : 0;
        
        // 触发统计更新事件
        this.emit('statsUpdate', this.getStats());
    }
    
    /**
     * 获取敏感词总数
     */
    getTotalSensitiveWords() {
        return Object.values(this.sensitiveWords)
            .reduce((total, wordSet) => total + wordSet.size, 0);
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            moderation: { ...this.moderationState },
            cache: { ...this.cacheStats },
            rateLimit: {
                activeUsers: this.rateLimitTracker.users.size,
                activeRooms: this.rateLimitTracker.rooms.size,
                globalCount: this.rateLimitTracker.global.count
            },
            manualReview: {
                queueSize: this.manualReviewQueue.length,
                totalReviews: this.stats.manualReviews
            },
            blocks: { ...this.stats },
            sensitiveWords: {
                totalWords: this.getTotalSensitiveWords(),
                categories: Object.keys(this.sensitiveWords).length
            }
        };
    }
    
    /**
     * 添加敏感词
     */
    addSensitiveWords(category, words) {
        if (!this.sensitiveWords[category]) {
            this.sensitiveWords[category] = new Set();
        }
        
        words.forEach(word => {
            this.sensitiveWords[category].add(word);
        });
        
        console.log(`添加了 ${words.length} 个${category}敏感词`);
    }
    
    /**
     * 移除敏感词
     */
    removeSensitiveWords(category, words) {
        if (this.sensitiveWords[category]) {
            words.forEach(word => {
                this.sensitiveWords[category].delete(word);
            });
            
            console.log(`移除了 ${words.length} 个${category}敏感词`);
        }
    }
    
    /**
     * 关闭内容审核系统
     */
    close() {
        console.log('关闭弹幕内容审核系统...');
        
        // 清理缓存
        this.cache.clear();
        
        // 清理限流追踪
        this.rateLimitTracker.users.clear();
        this.rateLimitTracker.rooms.clear();
        
        // 清理审核队列
        this.manualReviewQueue = [];
        
        console.log('内容审核系统已关闭');
    }
}

module.exports = ContentModerator;
