/**
 * 用户权限与访问控制管理器
 * 
 * 实现用户权限管理、访问控制、操作日志记录等安全功能
 * 包括管理员权限、用户封禁、操作审计等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class AccessControlManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 权限级别定义
            permissions: {
                // 基础权限
                VIEW_ROOMS: 'view_rooms',
                JOIN_ROOM: 'join_room',
                SEND_BARRAGE: 'send_barrage',
                SEND_GIFTS: 'send_gifts',
                MAKE_PREDICTIONS: 'make_predictions',
                
                // 管理权限
                MODERATE_CONTENT: 'moderate_content',
                BAN_USERS: 'ban_users',
                MANAGE_ROOMS: 'manage_rooms',
                VIEW_ANALYTICS: 'view_analytics',
                
                // 系统权限
                SYSTEM_CONFIG: 'system_config',
                USER_MANAGEMENT: 'user_management',
                AUDIT_LOGS: 'audit_logs',
                EMERGENCY_CONTROL: 'emergency_control'
            },
            
            // 角色定义
            roles: {
                guest: {
                    name: '游客',
                    permissions: ['view_rooms'],
                    restrictions: {
                        maxRooms: 3,
                        maxBarragePerMinute: 0,
                        canSendGifts: false
                    }
                },
                user: {
                    name: '普通用户',
                    permissions: ['view_rooms', 'join_room', 'send_barrage', 'make_predictions'],
                    restrictions: {
                        maxRooms: 10,
                        maxBarragePerMinute: 10,
                        canSendGifts: true,
                        maxGiftValue: 100
                    }
                },
                vip: {
                    name: 'VIP用户',
                    permissions: ['view_rooms', 'join_room', 'send_barrage', 'send_gifts', 'make_predictions'],
                    restrictions: {
                        maxRooms: 50,
                        maxBarragePerMinute: 20,
                        canSendGifts: true,
                        maxGiftValue: 1000
                    }
                },
                moderator: {
                    name: '版主',
                    permissions: ['view_rooms', 'join_room', 'send_barrage', 'send_gifts', 'make_predictions', 'moderate_content', 'ban_users'],
                    restrictions: {
                        maxRooms: 100,
                        maxBarragePerMinute: 50,
                        canSendGifts: true,
                        maxGiftValue: 500,
                        canBanUsers: true,
                        maxBanDuration: 86400000 // 24小时
                    }
                },
                admin: {
                    name: '管理员',
                    permissions: ['*'], // 所有权限
                    restrictions: {
                        maxRooms: -1, // 无限制
                        maxBarragePerMinute: -1,
                        canSendGifts: true,
                        maxGiftValue: -1,
                        canBanUsers: true,
                        maxBanDuration: -1
                    }
                }
            },
            
            // 访问控制配置
            accessControl: {
                sessionTimeout: 7200000,        // 2小时会话超时
                maxConcurrentSessions: 3,       // 最大并发会话数
                ipWhitelist: new Set(),         // IP白名单
                ipBlacklist: new Set(),         // IP黑名单
                deviceLimit: 5,                 // 每用户最大设备数
                geoRestrictions: new Set()      // 地理位置限制
            },
            
            // 审计日志配置
            audit: {
                enabled: true,
                logLevel: 'info',
                retentionDays: 90,
                sensitiveActions: new Set([
                    'user_ban', 'user_unban', 'role_change', 'permission_grant',
                    'permission_revoke', 'system_config_change', 'emergency_action'
                ])
            }
        };
        
        // 用户会话管理
        this.sessions = new Map();
        
        // 用户权限缓存
        this.userPermissions = new Map();
        
        // 操作日志
        this.auditLogs = [];
        
        // 访问统计
        this.accessStats = {
            totalRequests: 0,
            deniedRequests: 0,
            uniqueUsers: new Set(),
            activeUsers: new Set(),
            suspiciousActivities: 0
        };
        
        // 安全事件追踪
        this.securityEvents = {
            failedLogins: new Map(),
            suspiciousIPs: new Map(),
            rateLimitViolations: new Map(),
            permissionViolations: new Map()
        };
        
        // 定时器
        this.timers = {
            sessionCleanup: null,
            auditCleanup: null,
            securityAnalysis: null
        };
        
        this.initialize();
    }
    
    /**
     * 初始化访问控制管理器
     */
    initialize() {
        console.log('初始化用户权限与访问控制管理器...');
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        console.log('访问控制管理器初始化完成');
    }
    
    /**
     * 启动定期任务
     */
    startPeriodicTasks() {
        // 会话清理
        this.timers.sessionCleanup = setInterval(() => {
            this.cleanupExpiredSessions();
        }, 300000); // 5分钟清理一次
        
        // 审计日志清理
        this.timers.auditCleanup = setInterval(() => {
            this.cleanupAuditLogs();
        }, 86400000); // 每天清理一次
        
        // 安全分析
        this.timers.securityAnalysis = setInterval(() => {
            this.performSecurityAnalysis();
        }, 600000); // 10分钟分析一次
    }
    
    /**
     * 用户认证
     */
    async authenticateUser(userId, token, context = {}) {
        try {
            // 验证token
            const tokenValid = await this.validateToken(token);
            if (!tokenValid) {
                this.logSecurityEvent('invalid_token', { userId, ip: context.ip });
                return { success: false, reason: 'invalid_token' };
            }
            
            // 检查用户状态
            const userStatus = await this.checkUserStatus(userId);
            if (!userStatus.active) {
                this.logSecurityEvent('inactive_user_login', { userId, reason: userStatus.reason });
                return { success: false, reason: userStatus.reason };
            }
            
            // 检查IP限制
            const ipCheck = this.checkIPRestrictions(context.ip);
            if (!ipCheck.allowed) {
                this.logSecurityEvent('ip_restricted', { userId, ip: context.ip });
                return { success: false, reason: 'ip_restricted' };
            }
            
            // 检查并发会话限制
            const sessionCheck = this.checkConcurrentSessions(userId);
            if (!sessionCheck.allowed) {
                this.logSecurityEvent('session_limit_exceeded', { userId, currentSessions: sessionCheck.count });
                return { success: false, reason: 'session_limit_exceeded' };
            }
            
            // 创建会话
            const session = await this.createSession(userId, context);
            
            // 记录成功登录
            this.logAuditEvent('user_login', userId, { ip: context.ip, userAgent: context.userAgent });
            
            return {
                success: true,
                session,
                permissions: await this.getUserPermissions(userId)
            };
            
        } catch (error) {
            console.error('用户认证失败:', error);
            this.logSecurityEvent('authentication_error', { userId, error: error.message });
            return { success: false, reason: 'system_error' };
        }
    }
    
    /**
     * 检查用户权限
     */
    async checkPermission(userId, permission, context = {}) {
        this.accessStats.totalRequests++;
        
        try {
            // 检查会话有效性
            const session = this.getActiveSession(userId);
            if (!session) {
                this.accessStats.deniedRequests++;
                return { allowed: false, reason: 'no_active_session' };
            }
            
            // 获取用户权限
            const userPermissions = await this.getUserPermissions(userId);
            
            // 检查权限
            const hasPermission = this.hasPermission(userPermissions, permission);
            if (!hasPermission) {
                this.accessStats.deniedRequests++;
                this.logSecurityEvent('permission_denied', { userId, permission, context });
                return { allowed: false, reason: 'insufficient_permissions' };
            }
            
            // 检查资源限制
            const resourceCheck = await this.checkResourceLimits(userId, permission, context);
            if (!resourceCheck.allowed) {
                this.accessStats.deniedRequests++;
                return resourceCheck;
            }
            
            // 更新会话活动时间
            this.updateSessionActivity(userId);
            
            return { allowed: true };
            
        } catch (error) {
            console.error('权限检查失败:', error);
            this.accessStats.deniedRequests++;
            return { allowed: false, reason: 'system_error' };
        }
    }
    
    /**
     * 创建用户会话
     */
    async createSession(userId, context) {
        const sessionId = crypto.randomUUID();
        const now = Date.now();
        
        const session = {
            id: sessionId,
            userId,
            createdAt: now,
            lastActivity: now,
            expiresAt: now + this.config.accessControl.sessionTimeout,
            ip: context.ip,
            userAgent: context.userAgent,
            deviceId: context.deviceId,
            location: context.location
        };
        
        // 清理该用户的过期会话
        this.cleanupUserSessions(userId);
        
        // 存储新会话
        if (!this.sessions.has(userId)) {
            this.sessions.set(userId, new Map());
        }
        
        this.sessions.get(userId).set(sessionId, session);
        
        // 更新统计
        this.accessStats.uniqueUsers.add(userId);
        this.accessStats.activeUsers.add(userId);
        
        return session;
    }
    
    /**
     * 获取用户权限
     */
    async getUserPermissions(userId) {
        // 检查缓存
        if (this.userPermissions.has(userId)) {
            const cached = this.userPermissions.get(userId);
            if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
                return cached.permissions;
            }
        }
        
        // 从数据库获取用户角色和权限
        const userRole = await this.getUserRole(userId);
        const roleConfig = this.config.roles[userRole] || this.config.roles.user;
        
        let permissions = [...roleConfig.permissions];
        
        // 获取用户特殊权限
        const specialPermissions = await this.getUserSpecialPermissions(userId);
        permissions = permissions.concat(specialPermissions);
        
        // 缓存权限
        this.userPermissions.set(userId, {
            permissions,
            role: userRole,
            restrictions: roleConfig.restrictions,
            timestamp: Date.now()
        });
        
        return {
            permissions,
            role: userRole,
            restrictions: roleConfig.restrictions
        };
    }
    
    /**
     * 检查是否有权限
     */
    hasPermission(userPermissions, requiredPermission) {
        const permissions = userPermissions.permissions;
        
        // 检查是否有全部权限
        if (permissions.includes('*')) {
            return true;
        }
        
        // 检查具体权限
        return permissions.includes(requiredPermission);
    }
    
    /**
     * 检查资源限制
     */
    async checkResourceLimits(userId, permission, context) {
        const userPermissions = await this.getUserPermissions(userId);
        const restrictions = userPermissions.restrictions;
        
        switch (permission) {
            case this.config.permissions.JOIN_ROOM:
                if (restrictions.maxRooms > 0) {
                    const currentRooms = await this.getUserActiveRooms(userId);
                    if (currentRooms >= restrictions.maxRooms) {
                        return { allowed: false, reason: 'room_limit_exceeded', limit: restrictions.maxRooms };
                    }
                }
                break;
                
            case this.config.permissions.SEND_BARRAGE:
                if (restrictions.maxBarragePerMinute > 0) {
                    const recentBarrage = await this.getUserRecentBarrage(userId);
                    if (recentBarrage >= restrictions.maxBarragePerMinute) {
                        return { allowed: false, reason: 'barrage_rate_limit', limit: restrictions.maxBarragePerMinute };
                    }
                }
                break;
                
            case this.config.permissions.SEND_GIFTS:
                if (!restrictions.canSendGifts) {
                    return { allowed: false, reason: 'gifts_not_allowed' };
                }
                if (restrictions.maxGiftValue > 0 && context.giftValue > restrictions.maxGiftValue) {
                    return { allowed: false, reason: 'gift_value_exceeded', limit: restrictions.maxGiftValue };
                }
                break;
                
            case this.config.permissions.BAN_USERS:
                if (!restrictions.canBanUsers) {
                    return { allowed: false, reason: 'ban_not_allowed' };
                }
                if (restrictions.maxBanDuration > 0 && context.banDuration > restrictions.maxBanDuration) {
                    return { allowed: false, reason: 'ban_duration_exceeded', limit: restrictions.maxBanDuration };
                }
                break;
        }
        
        return { allowed: true };
    }
    
    /**
     * 检查IP限制
     */
    checkIPRestrictions(ip) {
        // 检查黑名单
        if (this.config.accessControl.ipBlacklist.has(ip)) {
            return { allowed: false, reason: 'ip_blacklisted' };
        }
        
        // 检查白名单（如果启用）
        if (this.config.accessControl.ipWhitelist.size > 0 && 
            !this.config.accessControl.ipWhitelist.has(ip)) {
            return { allowed: false, reason: 'ip_not_whitelisted' };
        }
        
        return { allowed: true };
    }
    
    /**
     * 检查并发会话限制
     */
    checkConcurrentSessions(userId) {
        const userSessions = this.sessions.get(userId);
        if (!userSessions) {
            return { allowed: true, count: 0 };
        }
        
        // 清理过期会话
        this.cleanupUserSessions(userId);
        
        const activeSessions = userSessions.size;
        const maxSessions = this.config.accessControl.maxConcurrentSessions;
        
        if (activeSessions >= maxSessions) {
            return { allowed: false, count: activeSessions, limit: maxSessions };
        }
        
        return { allowed: true, count: activeSessions };
    }
    
    /**
     * 获取活跃会话
     */
    getActiveSession(userId) {
        const userSessions = this.sessions.get(userId);
        if (!userSessions) {
            return null;
        }
        
        const now = Date.now();
        
        // 查找有效会话
        for (const [sessionId, session] of userSessions) {
            if (session.expiresAt > now) {
                return session;
            }
        }
        
        return null;
    }
    
    /**
     * 更新会话活动时间
     */
    updateSessionActivity(userId) {
        const userSessions = this.sessions.get(userId);
        if (!userSessions) {
            return;
        }
        
        const now = Date.now();
        
        for (const [sessionId, session] of userSessions) {
            if (session.expiresAt > now) {
                session.lastActivity = now;
                session.expiresAt = now + this.config.accessControl.sessionTimeout;
            }
        }
    }
    
    /**
     * 清理用户会话
     */
    cleanupUserSessions(userId) {
        const userSessions = this.sessions.get(userId);
        if (!userSessions) {
            return;
        }
        
        const now = Date.now();
        
        for (const [sessionId, session] of userSessions) {
            if (session.expiresAt <= now) {
                userSessions.delete(sessionId);
            }
        }
        
        if (userSessions.size === 0) {
            this.sessions.delete(userId);
            this.accessStats.activeUsers.delete(userId);
        }
    }
    
    /**
     * 清理过期会话
     */
    cleanupExpiredSessions() {
        const now = Date.now();
        let cleanedSessions = 0;
        
        for (const [userId, userSessions] of this.sessions) {
            for (const [sessionId, session] of userSessions) {
                if (session.expiresAt <= now) {
                    userSessions.delete(sessionId);
                    cleanedSessions++;
                }
            }
            
            if (userSessions.size === 0) {
                this.sessions.delete(userId);
                this.accessStats.activeUsers.delete(userId);
            }
        }
        
        if (cleanedSessions > 0) {
            console.log(`清理了 ${cleanedSessions} 个过期会话`);
        }
    }
    
    /**
     * 记录审计日志
     */
    logAuditEvent(action, userId, details = {}) {
        if (!this.config.audit.enabled) {
            return;
        }
        
        const auditLog = {
            id: crypto.randomUUID(),
            timestamp: Date.now(),
            action,
            userId,
            details,
            sensitive: this.config.audit.sensitiveActions.has(action)
        };
        
        this.auditLogs.push(auditLog);
        
        // 保持日志数量限制
        if (this.auditLogs.length > 10000) {
            this.auditLogs.shift();
        }
        
        // 触发审计事件
        this.emit('auditLog', auditLog);
        
        // 敏感操作特殊处理
        if (auditLog.sensitive) {
            this.emit('sensitiveAction', auditLog);
        }
    }
    
    /**
     * 记录安全事件
     */
    logSecurityEvent(eventType, details) {
        const securityEvent = {
            id: crypto.randomUUID(),
            timestamp: Date.now(),
            type: eventType,
            details
        };
        
        // 更新安全事件统计
        this.updateSecurityStats(eventType, details);
        
        // 触发安全事件
        this.emit('securityEvent', securityEvent);
        
        // 记录到审计日志
        this.logAuditEvent('security_event', details.userId || 'system', { eventType, details });
    }
    
    /**
     * 更新安全统计
     */
    updateSecurityStats(eventType, details) {
        this.accessStats.suspiciousActivities++;
        
        switch (eventType) {
            case 'invalid_token':
            case 'inactive_user_login':
                const userId = details.userId;
                const failedCount = this.securityEvents.failedLogins.get(userId) || 0;
                this.securityEvents.failedLogins.set(userId, failedCount + 1);
                break;
                
            case 'ip_restricted':
                const ip = details.ip;
                const suspiciousCount = this.securityEvents.suspiciousIPs.get(ip) || 0;
                this.securityEvents.suspiciousIPs.set(ip, suspiciousCount + 1);
                break;
                
            case 'permission_denied':
                const permissionUserId = details.userId;
                const violationCount = this.securityEvents.permissionViolations.get(permissionUserId) || 0;
                this.securityEvents.permissionViolations.set(permissionUserId, violationCount + 1);
                break;
        }
    }
    
    /**
     * 执行安全分析
     */
    performSecurityAnalysis() {
        // 分析失败登录
        this.analyzeFailedLogins();
        
        // 分析可疑IP
        this.analyzeSuspiciousIPs();
        
        // 分析权限违规
        this.analyzePermissionViolations();
        
        // 清理过期统计
        this.cleanupSecurityStats();
    }
    
    /**
     * 分析失败登录
     */
    analyzeFailedLogins() {
        const threshold = 5; // 5次失败登录阈值
        const timeWindow = 3600000; // 1小时窗口
        
        for (const [userId, count] of this.securityEvents.failedLogins) {
            if (count >= threshold) {
                this.emit('securityAlert', {
                    type: 'multiple_failed_logins',
                    userId,
                    count,
                    threshold
                });
                
                // 可以考虑自动锁定账户
                this.logAuditEvent('security_alert', userId, {
                    type: 'multiple_failed_logins',
                    count
                });
            }
        }
    }
    
    /**
     * 分析可疑IP
     */
    analyzeSuspiciousIPs() {
        const threshold = 10; // 10次可疑活动阈值
        
        for (const [ip, count] of this.securityEvents.suspiciousIPs) {
            if (count >= threshold) {
                this.emit('securityAlert', {
                    type: 'suspicious_ip_activity',
                    ip,
                    count,
                    threshold
                });
                
                // 可以考虑自动加入黑名单
                this.logAuditEvent('security_alert', 'system', {
                    type: 'suspicious_ip_activity',
                    ip,
                    count
                });
            }
        }
    }
    
    /**
     * 分析权限违规
     */
    analyzePermissionViolations() {
        const threshold = 20; // 20次权限违规阈值
        
        for (const [userId, count] of this.securityEvents.permissionViolations) {
            if (count >= threshold) {
                this.emit('securityAlert', {
                    type: 'excessive_permission_violations',
                    userId,
                    count,
                    threshold
                });
                
                this.logAuditEvent('security_alert', userId, {
                    type: 'excessive_permission_violations',
                    count
                });
            }
        }
    }
    
    /**
     * 清理安全统计
     */
    cleanupSecurityStats() {
        // 这里可以实现基于时间的统计清理
        // 例如清理1小时前的统计数据
    }
    
    /**
     * 清理审计日志
     */
    cleanupAuditLogs() {
        const retentionTime = this.config.audit.retentionDays * 24 * 60 * 60 * 1000;
        const cutoffTime = Date.now() - retentionTime;
        
        const originalLength = this.auditLogs.length;
        this.auditLogs = this.auditLogs.filter(log => log.timestamp > cutoffTime);
        
        const cleanedCount = originalLength - this.auditLogs.length;
        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 条过期审计日志`);
        }
    }
    
    /**
     * 模拟数据库操作
     */
    async validateToken(token) {
        // 模拟token验证
        return token && token.length > 10;
    }
    
    async checkUserStatus(userId) {
        // 模拟用户状态检查
        return { active: true };
    }
    
    async getUserRole(userId) {
        // 模拟获取用户角色
        return 'user'; // 默认普通用户
    }
    
    async getUserSpecialPermissions(userId) {
        // 模拟获取用户特殊权限
        return [];
    }
    
    async getUserActiveRooms(userId) {
        // 模拟获取用户活跃房间数
        return Math.floor(Math.random() * 5);
    }
    
    async getUserRecentBarrage(userId) {
        // 模拟获取用户最近弹幕数
        return Math.floor(Math.random() * 15);
    }
    
    /**
     * 管理操作
     */
    
    /**
     * 添加IP到黑名单
     */
    addToBlacklist(ip, reason) {
        this.config.accessControl.ipBlacklist.add(ip);
        this.logAuditEvent('ip_blacklisted', 'system', { ip, reason });
        this.emit('ipBlacklisted', { ip, reason });
    }
    
    /**
     * 从黑名单移除IP
     */
    removeFromBlacklist(ip, reason) {
        this.config.accessControl.ipBlacklist.delete(ip);
        this.logAuditEvent('ip_unblacklisted', 'system', { ip, reason });
        this.emit('ipUnblacklisted', { ip, reason });
    }
    
    /**
     * 强制用户下线
     */
    forceLogout(userId, reason) {
        const userSessions = this.sessions.get(userId);
        if (userSessions) {
            userSessions.clear();
            this.sessions.delete(userId);
            this.accessStats.activeUsers.delete(userId);
        }
        
        this.logAuditEvent('force_logout', userId, { reason });
        this.emit('userForcedLogout', { userId, reason });
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            access: {
                ...this.accessStats,
                uniqueUsers: this.accessStats.uniqueUsers.size,
                activeUsers: this.accessStats.activeUsers.size,
                denialRate: this.accessStats.totalRequests > 0 ? 
                    (this.accessStats.deniedRequests / this.accessStats.totalRequests * 100).toFixed(2) : 0
            },
            sessions: {
                totalSessions: Array.from(this.sessions.values()).reduce((sum, userSessions) => sum + userSessions.size, 0),
                activeUsers: this.sessions.size
            },
            security: {
                failedLogins: this.securityEvents.failedLogins.size,
                suspiciousIPs: this.securityEvents.suspiciousIPs.size,
                permissionViolations: this.securityEvents.permissionViolations.size,
                suspiciousActivities: this.accessStats.suspiciousActivities
            },
            audit: {
                totalLogs: this.auditLogs.length,
                sensitiveLogs: this.auditLogs.filter(log => log.sensitive).length
            }
        };
    }
    
    /**
     * 获取审计日志
     */
    getAuditLogs(filters = {}) {
        let logs = this.auditLogs;
        
        if (filters.userId) {
            logs = logs.filter(log => log.userId === filters.userId);
        }
        
        if (filters.action) {
            logs = logs.filter(log => log.action === filters.action);
        }
        
        if (filters.startTime) {
            logs = logs.filter(log => log.timestamp >= filters.startTime);
        }
        
        if (filters.endTime) {
            logs = logs.filter(log => log.timestamp <= filters.endTime);
        }
        
        if (filters.sensitiveOnly) {
            logs = logs.filter(log => log.sensitive);
        }
        
        return logs.sort((a, b) => b.timestamp - a.timestamp);
    }
    
    /**
     * 关闭访问控制管理器
     */
    close() {
        console.log('关闭用户权限与访问控制管理器...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.sessions.clear();
        this.userPermissions.clear();
        this.auditLogs = [];
        this.securityEvents.failedLogins.clear();
        this.securityEvents.suspiciousIPs.clear();
        this.securityEvents.permissionViolations.clear();
        
        console.log('访问控制管理器已关闭');
    }
}

module.exports = AccessControlManager;
