/**
 * 安全配置
 * 定义安全策略、防护规则和审计设置
 */

module.exports = {
  // 安全头配置
  headers: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=(), payment=()',
    'X-Permitted-Cross-Domain-Policies': 'none'
  },

  // 输入过滤配置
  inputFilter: {
    // 敏感字段列表
    sensitiveFields: [
      'password', 'token', 'secret', 'key', 'apiKey', 'accessToken',
      'refreshToken', 'sessionId', 'csrf', 'signature'
    ],
    
    // XSS过滤规则
    xssPatterns: [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
    ],
    
    // SQL注入过滤规则
    sqlInjectionPatterns: [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /('|(\\')|(;)|(--)|(\s)|(\/\*)|(\*\/))/gi
    ],
    
    // 最大字符串长度
    maxStringLength: 10000,
    
    // 最大对象深度
    maxObjectDepth: 10
  },

  // 防暴力破解配置
  bruteForce: {
    // 登录尝试限制
    login: {
      maxAttempts: 5,           // 最大尝试次数
      windowMs: 15 * 60 * 1000, // 时间窗口（15分钟）
      blockDuration: 30 * 60 * 1000, // 封锁时间（30分钟）
      progressiveDelay: true    // 渐进式延迟
    },
    
    // API调用限制
    api: {
      maxAttempts: 100,         // 最大调用次数
      windowMs: 60 * 1000,      // 时间窗口（1分钟）
      blockDuration: 5 * 60 * 1000, // 封锁时间（5分钟）
      skipSuccessfulRequests: true
    },
    
    // 密码重置限制
    passwordReset: {
      maxAttempts: 3,
      windowMs: 60 * 60 * 1000, // 1小时
      blockDuration: 24 * 60 * 60 * 1000 // 24小时
    }
  },

  // 审计日志配置
  audit: {
    // 需要记录的事件类型
    events: [
      'login_success',
      'login_failure',
      'logout',
      'token_refresh',
      'password_change',
      'data_access',
      'data_modification',
      'admin_action',
      'security_violation',
      'rate_limit_exceeded'
    ],
    
    // 敏感操作
    sensitiveOperations: [
      'user_creation',
      'user_deletion',
      'permission_change',
      'config_change',
      'data_export',
      'admin_login'
    ],
    
    // 日志保留时间（天）
    retentionDays: 90,
    
    // 日志级别
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    
    // 是否记录请求体
    logRequestBody: false,
    
    // 是否记录响应体
    logResponseBody: false
  },

  // 数据加密配置
  encryption: {
    // 加密算法
    algorithm: 'aes-256-gcm',
    
    // 密钥长度
    keyLength: 32,
    
    // IV长度
    ivLength: 16,
    
    // 标签长度
    tagLength: 16,
    
    // 密钥派生迭代次数
    iterations: 100000
  },

  // IP白名单配置
  ipWhitelist: {
    // 管理员IP白名单
    admin: process.env.ADMIN_IP_WHITELIST ? 
      process.env.ADMIN_IP_WHITELIST.split(',') : [],
    
    // API访问IP白名单
    api: process.env.API_IP_WHITELIST ? 
      process.env.API_IP_WHITELIST.split(',') : [],
    
    // 是否启用IP白名单
    enabled: process.env.NODE_ENV === 'production'
  },

  // CORS配置
  cors: {
    // 允许的源
    allowedOrigins: process.env.ALLOWED_ORIGINS ? 
      process.env.ALLOWED_ORIGINS.split(',') : ['*'],
    
    // 允许的方法
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    
    // 允许的头部
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-Device-Id',
      'X-Request-Id'
    ],
    
    // 是否允许凭证
    credentials: true,
    
    // 预检请求缓存时间
    maxAge: 86400
  },

  // 会话安全配置
  session: {
    // 会话超时时间（秒）
    timeout: 30 * 60, // 30分钟
    
    // 是否启用会话固定保护
    regenerateId: true,
    
    // Cookie安全配置
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'strict',
      maxAge: 30 * 60 * 1000 // 30分钟
    }
  },

  // 文件上传安全配置
  fileUpload: {
    // 允许的文件类型
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'audio/mpeg',
      'audio/wav',
      'audio/ogg'
    ],
    
    // 最大文件大小（字节）
    maxSize: 10 * 1024 * 1024, // 10MB
    
    // 文件名过滤规则
    filenamePattern: /^[a-zA-Z0-9._-]+$/,
    
    // 是否扫描病毒
    virusScan: process.env.NODE_ENV === 'production'
  },

  // 安全监控配置
  monitoring: {
    // 异常检测阈值
    anomalyThresholds: {
      requestRate: 1000,        // 每分钟请求数
      errorRate: 0.1,           // 错误率（10%）
      responseTime: 5000,       // 响应时间（毫秒）
      concurrentUsers: 500      // 并发用户数
    },
    
    // 告警配置
    alerts: {
      enabled: true,
      channels: ['console', 'redis'],
      webhook: process.env.SECURITY_WEBHOOK_URL
    },
    
    // 自动响应配置
    autoResponse: {
      enabled: process.env.NODE_ENV === 'production',
      actions: ['rate_limit', 'ip_block', 'alert']
    }
  }
};
