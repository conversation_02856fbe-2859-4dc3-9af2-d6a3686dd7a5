/**
 * 性能监控配置
 * 定义性能监控的阈值、告警规则和报告设置
 */

module.exports = {
  // 性能阈值配置
  thresholds: {
    // API响应时间阈值（毫秒）
    responseTime: {
      warning: 1000,    // 1秒警告
      critical: 3000,   // 3秒严重
      timeout: 30000    // 30秒超时
    },
    
    // 内存使用阈值（MB）
    memory: {
      warning: 100,     // 100MB警告
      critical: 200,    // 200MB严重
      max: 512          // 512MB最大限制
    },
    
    // 数据库查询时间阈值（毫秒）
    database: {
      warning: 500,     // 500ms警告
      critical: 2000,   // 2秒严重
      timeout: 10000    // 10秒超时
    },
    
    // 错误率阈值（百分比）
    errorRate: {
      warning: 5,       // 5%警告
      critical: 10      // 10%严重
    },
    
    // 并发请求数阈值
    concurrency: {
      warning: 50,      // 50个并发警告
      critical: 100     // 100个并发严重
    }
  },
  
  // 监控采样配置
  sampling: {
    // 采样率（0-1）
    rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 慢查询采样率
    slowQueryRate: 1.0,
    
    // 错误采样率
    errorRate: 1.0
  },
  
  // 数据保留配置
  retention: {
    // 实时数据保留时间（秒）
    realtime: 3600,     // 1小时
    
    // 聚合数据保留时间（秒）
    aggregated: 86400 * 7,  // 7天
    
    // 历史数据保留时间（秒）
    historical: 86400 * 30  // 30天
  },
  
  // 告警配置
  alerts: {
    // 告警通道
    channels: {
      console: true,
      redis: true,
      webhook: process.env.ALERT_WEBHOOK_URL || null
    },
    
    // 告警频率限制（秒）
    rateLimit: {
      warning: 300,     // 5分钟内最多1次警告
      critical: 60      // 1分钟内最多1次严重告警
    },
    
    // 告警恢复通知
    recovery: true
  },
  
  // 报告配置
  reporting: {
    // 自动报告间隔（秒）
    interval: 3600,     // 每小时生成报告
    
    // 报告包含的指标
    metrics: [
      'responseTime',
      'memory',
      'database',
      'errorRate',
      'throughput',
      'concurrency'
    ],
    
    // 报告格式
    format: 'json',
    
    // 报告存储
    storage: {
      redis: true,
      file: false
    }
  },
  
  // Redis键前缀
  redis: {
    prefix: 'perf:',
    keys: {
      metrics: 'metrics',
      alerts: 'alerts',
      reports: 'reports',
      aggregated: 'agg'
    }
  },
  
  // 性能监控开关
  enabled: {
    responseTime: true,
    memory: true,
    database: true,
    redis: true,
    external: true,
    alerts: true,
    reporting: true
  }
};
