/**
 * 游戏服务处理器
 * 处理题目管理、游戏会话、游戏结果等API请求
 */

const Question = require('../models/Question');
const GameSession = require('../models/GameSession');
const GameResult = require('../models/GameResult');
const { authMiddleware, optionalAuth } = require('../middleware/auth');
const { validateRequest, VALIDATION_SCHEMAS } = require('../middleware/validation');
const { errorHandler } = require('../middleware/error');
const { rateLimitMiddleware } = require('../middleware/rateLimit');
const { APIError } = require('../utils/errors');

/**
 * 获取题目列表
 * GET /v1/questions
 */
const getQuestions = errorHandler(async (event, context) => {
  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('api')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const queryParams = event.queryStringParameters || {};
  const {
    count = 10,
    difficulty,
    category,
    random = 'true'
  } = queryParams;

  // 验证参数
  const questionCount = Math.min(Math.max(parseInt(count), 1), 50); // 限制1-50题
  const isRandom = random === 'true';

  try {
    let questions = [];

    if (isRandom) {
      // 获取随机题目
      questions = await Question.getRandomQuestions(
        questionCount,
        difficulty ? parseInt(difficulty) : null,
        category
      );
    } else {
      // 按条件查询
      const conditions = {
        limit: questionCount
      };
      
      if (difficulty) {
        conditions.difficulty = parseInt(difficulty);
      }
      
      if (category) {
        conditions.category = category;
      }

      questions = await Question.findByConditions(conditions);
    }

    return {
      questions: questions.map(q => q.toJSON(false)), // 不包含答案
      total: questions.length,
      params: {
        count: questionCount,
        difficulty: difficulty ? parseInt(difficulty) : null,
        category: category || null,
        random: isRandom
      }
    };

  } catch (error) {
    console.error('Get questions error:', error);
    throw new APIError('GET_QUESTIONS_FAILED', '获取题目失败', null, 500);
  }
});

/**
 * 获取单个题目详情
 * GET /v1/questions/{questionId}
 */
const getQuestion = errorHandler(async (event, context) => {
  const questionId = event.pathParameters?.questionId;
  
  if (!questionId) {
    throw new APIError('MISSING_QUESTION_ID', '题目ID不能为空', null, 400);
  }

  try {
    const question = await Question.findById(questionId);
    
    if (!question) {
      throw new APIError('QUESTION_NOT_FOUND', '题目不存在', null, 404);
    }

    return {
      question: question.toJSON(false) // 不包含答案
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Get question error:', error);
    throw new APIError('GET_QUESTION_FAILED', '获取题目详情失败', null, 500);
  }
});

/**
 * 创建游戏会话
 * POST /v1/game-sessions
 */
const createGameSession = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('game')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const data = validateRequest({
    type: 'object',
    properties: {
      category: { type: 'string', minLength: 1, maxLength: 50 },
      difficulty: { type: 'integer', minimum: 1, maximum: 5 },
      questionCount: { type: 'integer', minimum: 5, maximum: 50 },
      gameMode: { type: 'string', enum: ['standard', 'timed', 'challenge'] }
    },
    additionalProperties: false
  })(event);

  const userId = event.user.id;

  try {
    // 检查是否有活跃的游戏会话
    const activeSession = await GameSession.findActiveByUserId(userId);
    if (activeSession) {
      return {
        message: '已有进行中的游戏会话',
        gameSession: activeSession.toJSON()
      };
    }

    // 创建新的游戏会话
    const gameSession = await GameSession.create(userId, {
      category: data.category || 'general',
      difficulty: data.difficulty || 1,
      questionCount: data.questionCount || 10,
      gameMode: data.gameMode || 'standard'
    });

    // 为会话准备题目
    const questions = await Question.getRandomQuestions(
      gameSession.questionCount,
      gameSession.difficulty,
      gameSession.category === 'general' ? null : gameSession.category
    );

    if (questions.length < gameSession.questionCount) {
      throw new APIError('INSUFFICIENT_QUESTIONS', '可用题目数量不足', null, 400);
    }

    // 保存题目到会话中（实际项目中可能需要存储到Redis或数据库）
    gameSession.questions = questions;

    return {
      gameSession: gameSession.toJSON(),
      firstQuestion: questions[0] ? questions[0].toJSON(false) : null
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Create game session error:', error);
    throw new APIError('CREATE_SESSION_FAILED', '创建游戏会话失败', null, 500);
  }
});

/**
 * 获取游戏会话状态
 * GET /v1/game-sessions/{sessionId}
 */
const getGameSession = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const sessionId = event.pathParameters?.sessionId;
  
  if (!sessionId) {
    throw new APIError('MISSING_SESSION_ID', '会话ID不能为空', null, 400);
  }

  try {
    const gameSession = await GameSession.findBySessionId(sessionId);
    
    if (!gameSession) {
      throw new APIError('SESSION_NOT_FOUND', '游戏会话不存在', null, 404);
    }

    // 检查用户权限
    if (gameSession.userId !== event.user.id) {
      throw new APIError('SESSION_ACCESS_DENIED', '无权访问此游戏会话', null, 403);
    }

    // 检查会话是否过期
    if (!gameSession.isValid() && gameSession.status === 1) {
      await gameSession.cancel();
    }

    return {
      gameSession: gameSession.toJSON()
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Get game session error:', error);
    throw new APIError('GET_SESSION_FAILED', '获取游戏会话失败', null, 500);
  }
});

/**
 * 提交游戏答案
 * POST /v1/game-sessions/{sessionId}/submit
 */
const submitAnswer = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('game')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  const sessionId = event.pathParameters?.sessionId;
  
  if (!sessionId) {
    throw new APIError('MISSING_SESSION_ID', '会话ID不能为空', null, 400);
  }

  // 参数验证
  const data = validateRequest({
    type: 'object',
    required: ['questionId', 'userAnswer', 'answerTime'],
    properties: {
      questionId: { type: 'integer', minimum: 1 },
      userAnswer: { type: 'string', minLength: 1 },
      answerTime: { type: 'integer', minimum: 0, maximum: 300 },
      hintUsed: { type: 'boolean' }
    },
    additionalProperties: false
  })(event);

  const userId = event.user.id;

  try {
    // 获取游戏会话
    const gameSession = await GameSession.findBySessionId(sessionId);
    
    if (!gameSession) {
      throw new APIError('SESSION_NOT_FOUND', '游戏会话不存在', null, 404);
    }

    // 检查用户权限
    if (gameSession.userId !== userId) {
      throw new APIError('SESSION_ACCESS_DENIED', '无权访问此游戏会话', null, 403);
    }

    // 检查会话状态
    if (!gameSession.isValid()) {
      throw new APIError('SESSION_EXPIRED', '游戏会话已过期或已结束', null, 400);
    }

    // 获取题目
    const question = await Question.findById(data.questionId);
    if (!question) {
      throw new APIError('QUESTION_NOT_FOUND', '题目不存在', null, 404);
    }

    // 检查答案
    const isCorrect = question.checkAnswer(data.userAnswer);
    
    // 计算得分
    const score = question.calculateScore(
      isCorrect, 
      data.answerTime, 
      gameSession.streakCount
    );

    // 记录游戏结果
    await GameResult.create({
      userId: userId,
      gameSessionId: sessionId,
      questionId: data.questionId,
      userAnswer: data.userAnswer,
      isCorrect: isCorrect,
      answerTime: data.answerTime,
      scoreEarned: score,
      streakCount: gameSession.streakCount + (isCorrect ? 1 : 0),
      hintUsed: data.hintUsed || false
    });

    // 更新题目统计
    await Question.updateUsageStats(data.questionId, isCorrect, data.answerTime);

    // 更新游戏进度
    const isGameCompleted = await gameSession.updateProgress(isCorrect, score, data.answerTime);

    // 准备响应
    const response = {
      result: {
        isCorrect: isCorrect,
        correctAnswer: question.standardAnswer,
        explanation: question.explanation,
        scoreEarned: score,
        streakCount: isCorrect ? gameSession.streakCount : 0
      },
      gameSession: gameSession.toJSON(),
      isGameCompleted: isGameCompleted
    };

    // 如果游戏完成，添加最终统计
    if (isGameCompleted) {
      response.finalStats = gameSession.getStats();
      response.gameResults = await GameResult.findBySessionId(sessionId);
    }

    return response;

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Submit answer error:', error);
    throw new APIError('SUBMIT_ANSWER_FAILED', '提交答案失败', null, 500);
  }
});

/**
 * 获取游戏结果
 * GET /v1/game-sessions/{sessionId}/results
 */
const getGameResults = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const sessionId = event.pathParameters?.sessionId;
  
  if (!sessionId) {
    throw new APIError('MISSING_SESSION_ID', '会话ID不能为空', null, 400);
  }

  try {
    const gameSession = await GameSession.findBySessionId(sessionId);
    
    if (!gameSession) {
      throw new APIError('SESSION_NOT_FOUND', '游戏会话不存在', null, 404);
    }

    // 检查用户权限
    if (gameSession.userId !== event.user.id) {
      throw new APIError('SESSION_ACCESS_DENIED', '无权访问此游戏会话', null, 403);
    }

    const results = await GameResult.findBySessionId(sessionId);

    return {
      gameSession: gameSession.toJSON(),
      results: results,
      stats: gameSession.getStats()
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Get game results error:', error);
    throw new APIError('GET_RESULTS_FAILED', '获取游戏结果失败', null, 500);
  }
});

/**
 * 取消游戏会话
 * DELETE /v1/game-sessions/{sessionId}
 */
const cancelGameSession = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const sessionId = event.pathParameters?.sessionId;
  
  if (!sessionId) {
    throw new APIError('MISSING_SESSION_ID', '会话ID不能为空', null, 400);
  }

  try {
    const gameSession = await GameSession.findBySessionId(sessionId);
    
    if (!gameSession) {
      throw new APIError('SESSION_NOT_FOUND', '游戏会话不存在', null, 404);
    }

    // 检查用户权限
    if (gameSession.userId !== event.user.id) {
      throw new APIError('SESSION_ACCESS_DENIED', '无权访问此游戏会话', null, 403);
    }

    // 检查会话状态
    if (gameSession.status !== 1) {
      throw new APIError('SESSION_NOT_ACTIVE', '游戏会话非进行中状态', null, 400);
    }

    await gameSession.cancel();

    return {
      message: '游戏会话已取消',
      gameSession: gameSession.toJSON()
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Cancel game session error:', error);
    throw new APIError('CANCEL_SESSION_FAILED', '取消游戏会话失败', null, 500);
  }
});

/**
 * 路由处理器
 */
const routes = {
  'GET /v1/questions': getQuestions,
  'GET /v1/questions/{questionId}': getQuestion,
  'POST /v1/game-sessions': createGameSession,
  'GET /v1/game-sessions/{sessionId}': getGameSession,
  'POST /v1/game-sessions/{sessionId}/submit': submitAnswer,
  'GET /v1/game-sessions/{sessionId}/results': getGameResults,
  'DELETE /v1/game-sessions/{sessionId}': cancelGameSession
};

/**
 * 主处理器
 */
const main = async (event, context) => {
  const method = event.httpMethod || event.requestContext?.httpMethod;
  const path = event.path || event.requestContext?.path;
  
  // 处理路径参数
  let routeKey = `${method} ${path}`;
  
  // 匹配动态路由
  for (const route of Object.keys(routes)) {
    const routePattern = route.replace(/\{[^}]+\}/g, '[^/]+');
    const regex = new RegExp(`^${routePattern}$`);
    
    if (regex.test(routeKey)) {
      routeKey = route;
      break;
    }
  }
  
  console.log(`Game Service - ${routeKey}`);
  
  // 添加CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Device-Id',
    'Access-Control-Max-Age': '86400'
  };
  
  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  
  // 查找对应的路由处理器
  const handler = routes[routeKey];
  
  if (!handler) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: '接口不存在',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
  
  try {
    const result = await handler(event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 0,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
    
  } catch (error) {
    console.error('Game handler error:', error);
    
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error instanceof APIError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      message = error.message;
    }
    
    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
};

module.exports = {
  main,
  getQuestions,
  getQuestion,
  createGameSession,
  getGameSession,
  submitAnswer,
  getGameResults,
  cancelGameSession
};