/**
 * 游戏服务开发处理器
 * 使用Mock数据服务的简化版本
 */

const devDb = require('../utils/dev-database').getInstance();
const devRedis = require('../utils/dev-redis').getInstance();

/**
 * 主处理器 - 路由分发
 */
const main = async (event, context) => {
  const { httpMethod, path } = event;
  const pathSegments = path.split('/').filter(Boolean);

  console.log(`Game Handler: ${httpMethod} ${path}`);

  try {
    // 路由分发
    if (path.includes('/questions')) {
      if (httpMethod === 'GET') {
        return await getQuestions(event, context);
      }
    } else if (path.includes('/game-sessions')) {
      if (httpMethod === 'POST' && pathSegments.length === 2) {
        return await createGameSession(event, context);
      } else if (httpMethod === 'GET' && pathSegments.length === 3) {
        return await getGameSession(event, context);
      } else if (httpMethod === 'POST' && path.includes('/submit')) {
        return await submitAnswer(event, context);
      } else if (httpMethod === 'GET' && path.includes('/results')) {
        return await getGameResults(event, context);
      }
    }

    // 404处理
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: `游戏服务不支持 ${httpMethod} ${path}`,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Game Handler Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INTERNAL_ERROR',
        message: error.message || '服务器内部错误',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 获取题目列表
 * GET /v1/questions
 */
const getQuestions = async (event, context) => {
  const queryParams = event.queryStringParameters || {};
  const {
    limit = 10,
    difficulty,
    dialect,
    random = 'true'
  } = queryParams;

  const questionCount = Math.min(Math.max(parseInt(limit), 1), 50);
  
  const filters = {
    limit: questionCount,
    random: random === 'true'
  };

  if (difficulty) {
    filters.difficulty = parseInt(difficulty);
  }
  
  if (dialect) {
    filters.dialect = dialect;
  }

  const questions = await devDb.queryQuestions(filters);

  // 简化输出，只返回必要字段
  const simplifiedQuestions = questions.map(q => ({
    id: q.question_id,
    text: q.question_text,
    audioUrl: q.audio_url,
    options: q.options,
    difficulty: q.difficulty_level,
    dialect: q.dialect_region,
    hint: q.hint,
    tags: q.tags
  }));

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        questions: simplifiedQuestions,
        total: simplifiedQuestions.length,
        filters: {
          difficulty: difficulty || 'all',
          dialect: dialect || 'all',
          limit: questionCount
        }
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 创建游戏会话
 * POST /v1/game-sessions
 */
const createGameSession = async (event, context) => {
  const body = JSON.parse(event.body || '{}');
  const {
    difficulty = 1,
    questionCount = 10,
    dialect,
    userId = 'guest_user'
  } = body;

  // 获取题目
  const filters = {
    limit: Math.min(Math.max(questionCount, 5), 20),
    random: true
  };

  if (difficulty) {
    filters.difficulty = difficulty;
  }
  
  if (dialect) {
    filters.dialect = dialect;
  }

  const questions = await devDb.queryQuestions(filters);
  
  if (questions.length === 0) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'NO_QUESTIONS',
        message: '没有找到符合条件的题目',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 创建游戏会话
  const session = await devDb.createGameSession({
    user_id: userId,
    question_count: questions.length,
    questions: questions.map(q => ({
      id: q.question_id,
      text: q.question_text,
      audioUrl: q.audio_url,
      options: q.options,
      correctAnswer: q.correct_answer,
      difficulty: q.difficulty_level,
      dialect: q.dialect_region,
      hint: q.hint
    }))
  });

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        sessionId: session.session_id,
        totalQuestions: session.total_questions,
        currentQuestion: session.current_question,
        status: session.status,
        questions: session.questions.map(q => ({
          id: q.id,
          text: q.text,
          audioUrl: q.audioUrl,
          options: q.options,
          difficulty: q.difficulty,
          dialect: q.dialect,
          hint: q.hint
          // 注意：不返回正确答案
        }))
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 获取游戏会话
 * GET /v1/game-sessions/{sessionId}
 */
const getGameSession = async (event, context) => {
  const sessionId = event.pathParameters?.sessionId || event.path.split('/').pop();
  
  const session = await devDb.getGameSession(sessionId);
  
  if (!session) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SESSION_NOT_FOUND',
        message: '游戏会话不存在',
        timestamp: new Date().toISOString()
      })
    };
  }

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        sessionId: session.session_id,
        status: session.status,
        currentQuestion: session.current_question,
        totalQuestions: session.total_questions,
        score: session.score,
        startTime: session.start_time,
        answers: session.answers
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 提交答案
 * POST /v1/game-sessions/{sessionId}/submit
 */
const submitAnswer = async (event, context) => {
  const sessionId = event.path.split('/')[3]; // /v1/game-sessions/{sessionId}/submit
  const body = JSON.parse(event.body || '{}');
  const { questionId, answer, timeUsed = 0 } = body;

  const session = await devDb.getGameSession(sessionId);
  
  if (!session) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SESSION_NOT_FOUND',
        message: '游戏会话不存在',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 检查题目
  const question = session.questions.find(q => q.id === questionId);
  if (!question) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'QUESTION_NOT_FOUND',
        message: '题目不存在',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 检查是否已回答
  const existingAnswer = session.answers.find(a => a.questionId === questionId);
  if (existingAnswer) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'ALREADY_ANSWERED',
        message: '该题目已经回答过了',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 判断答案
  const isCorrect = answer === question.correctAnswer;
  
  // 计算得分
  let score = 0;
  if (isCorrect) {
    const baseScore = 100;
    const timeBonus = Math.max(0, 50 - timeUsed); // 时间奖励
    const difficultyMultiplier = question.difficulty * 0.2 + 0.8;
    score = Math.round((baseScore + timeBonus) * difficultyMultiplier);
  }

  // 记录答案
  const answerRecord = {
    questionId,
    answer,
    isCorrect,
    score,
    timeUsed,
    timestamp: new Date()
  };

  session.answers.push(answerRecord);
  session.score += score;
  session.current_question = session.answers.length;

  // 检查游戏是否结束
  if (session.current_question >= session.total_questions) {
    session.status = 'completed';
    session.end_time = new Date();
    session.duration = new Date() - new Date(session.start_time);
    
    // 记录游戏结果
    const correctCount = session.answers.filter(a => a.isCorrect).length;
    await devDb.recordGameResult({
      user_id: session.user_id,
      session_id: session.session_id,
      total_questions: session.total_questions,
      correct_answers: correctCount,
      score: session.score,
      duration: session.duration,
      accuracy_rate: correctCount / session.total_questions,
      difficulty_distribution: {},
      dialect_distribution: {}
    });
  }

  // 更新会话
  await devDb.updateGameSession(sessionId, session);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        isCorrect,
        correctAnswer: question.correctAnswer,
        score,
        totalScore: session.score,
        currentQuestion: session.current_question,
        totalQuestions: session.total_questions,
        gameCompleted: session.status === 'completed',
        explanation: question.explanation || `正确答案是：${question.correctAnswer}`
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 获取游戏结果
 * GET /v1/game-sessions/{sessionId}/results
 */
const getGameResults = async (event, context) => {
  const sessionId = event.path.split('/')[3]; // /v1/game-sessions/{sessionId}/results
  
  const session = await devDb.getGameSession(sessionId);
  
  if (!session) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SESSION_NOT_FOUND',
        message: '游戏会话不存在',
        timestamp: new Date().toISOString()
      })
    };
  }

  const correctCount = session.answers.filter(a => a.isCorrect).length;
  const accuracy = session.answers.length > 0 ? correctCount / session.answers.length : 0;

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        sessionId: session.session_id,
        status: session.status,
        totalQuestions: session.total_questions,
        answeredQuestions: session.answers.length,
        correctAnswers: correctCount,
        accuracy: Math.round(accuracy * 100),
        totalScore: session.score,
        duration: session.duration,
        startTime: session.start_time,
        endTime: session.end_time,
        answers: session.answers.map(a => ({
          questionId: a.questionId,
          isCorrect: a.isCorrect,
          score: a.score,
          timeUsed: a.timeUsed
        }))
      },
      timestamp: new Date().toISOString()
    })
  };
};

module.exports = { main };