/**
 * 认证中间件
 * 处理JWT验证、权限检查等
 */

const tokenManager = require('../services/TokenManager');
const User = require('../models/User');

/**
 * 认证中间件工厂
 * @param {Array} requiredScopes 需要的权限范围
 * @returns {Function} 中间件函数
 */
const authMiddleware = (requiredScopes = []) => {
  return async (event, context, next) => {
    try {
      // 提取Authorization头
      const authHeader = event.headers.authorization || event.headers.Authorization;
      
      if (!authHeader) {
        throw new AuthError('MISSING_AUTH_HEADER', '缺少认证头');
      }
      
      if (!authHeader.startsWith('Bearer ')) {
        throw new AuthError('INVALID_AUTH_FORMAT', '认证格式错误');
      }
      
      const token = authHeader.substring(7);
      
      if (!token) {
        throw new AuthError('MISSING_TOKEN', '缺少访问令牌');
      }
      
      // 验证Token
      const payload = await tokenManager.verifyAccessToken(token);
      
      // 获取用户信息
      const user = await User.findById(payload.sub);
      if (!user) {
        throw new AuthError('USER_NOT_FOUND', '用户不存在');
      }
      
      if (user.status !== 1) {
        throw new AuthError('USER_INACTIVE', '用户已被禁用');
      }
      
      // 检查权限
      if (requiredScopes.length > 0) {
        const hasPermission = tokenManager.hasRequiredScopes(payload.scope, requiredScopes);
        if (!hasPermission) {
          throw new AuthError('INSUFFICIENT_PERMISSIONS', '权限不足');
        }
      }
      
      // 将用户信息和令牌信息添加到事件对象
      event.user = user;
      event.tokenPayload = payload;
      event.scopes = payload.scope ? payload.scope.split(' ') : [];
      
      // 继续执行下一个中间件
      if (next) {
        return await next();
      }
      
      return { authorized: true };
      
    } catch (error) {
      console.error('Authentication failed:', error);
      
      let statusCode = 401;
      let errorCode = 'UNAUTHORIZED';
      let message = '认证失败';
      
      if (error instanceof AuthError) {
        errorCode = error.code;
        message = error.message;
      } else if (error.message.includes('expired')) {
        errorCode = 'TOKEN_EXPIRED';
        message = '访问令牌已过期';
      } else if (error.message.includes('invalid')) {
        errorCode = 'INVALID_TOKEN';
        message = '无效的访问令牌';
      }
      
      return {
        statusCode,
        headers: {
          'Content-Type': 'application/json',
          'WWW-Authenticate': 'Bearer realm="API"'
        },
        body: JSON.stringify({
          code: errorCode,
          message,
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
    }
  };
};

/**
 * 可选认证中间件
 * 如果有Token则验证，没有则跳过
 */
const optionalAuth = () => {
  return async (event, context, next) => {
    const authHeader = event.headers.authorization || event.headers.Authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有认证头，继续执行
      event.user = null;
      event.tokenPayload = null;
      event.scopes = [];
      
      if (next) {
        return await next();
      }
      return { authorized: false };
    }
    
    // 有认证头，执行认证
    return await authMiddleware([])(event, context, next);
  };
};

/**
 * 管理员权限中间件
 */
const adminAuth = () => {
  return authMiddleware(['admin:read', 'admin:write']);
};

/**
 * 游戏权限中间件
 */
const gameAuth = () => {
  return authMiddleware(['game:play']);
};

/**
 * 用户写权限中间件
 */
const userWriteAuth = () => {
  return authMiddleware(['user:write']);
};

/**
 * 高级功能权限中间件
 */
const premiumAuth = () => {
  return authMiddleware(['premium:features']);
};

/**
 * 检查用户是否为资源所有者
 * @param {Function} getUserIdFromEvent 从事件中获取用户ID的函数
 */
const ownershipCheck = (getUserIdFromEvent) => {
  return async (event, context, next) => {
    try {
      if (!event.user) {
        throw new AuthError('UNAUTHORIZED', '需要认证');
      }
      
      const resourceUserId = getUserIdFromEvent(event);
      
      if (!resourceUserId) {
        throw new AuthError('INVALID_RESOURCE', '无效的资源ID');
      }
      
      // 检查是否为资源所有者或管理员
      const isOwner = event.user.id.toString() === resourceUserId.toString();
      const isAdmin = event.scopes.includes('admin:read');
      
      if (!isOwner && !isAdmin) {
        throw new AuthError('ACCESS_DENIED', '访问被拒绝');
      }
      
      event.isOwner = isOwner;
      event.isAdmin = isAdmin;
      
      if (next) {
        return await next();
      }
      
      return { authorized: true };
      
    } catch (error) {
      console.error('Ownership check failed:', error);
      
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: error.code || 'ACCESS_DENIED',
          message: error.message || '访问被拒绝',
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
    }
  };
};

/**
 * 设备验证中间件
 * 验证请求是否来自已授权的设备
 */
const deviceAuth = () => {
  return async (event, context, next) => {
    try {
      const deviceId = event.headers['x-device-id'] || event.headers['X-Device-Id'];
      
      if (!deviceId) {
        throw new AuthError('MISSING_DEVICE_ID', '缺少设备ID');
      }
      
      if (!event.tokenPayload || event.tokenPayload.deviceId !== deviceId) {
        throw new AuthError('DEVICE_MISMATCH', '设备ID不匹配');
      }
      
      event.deviceId = deviceId;
      
      if (next) {
        return await next();
      }
      
      return { authorized: true };
      
    } catch (error) {
      console.error('Device auth failed:', error);
      
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: error.code || 'DEVICE_AUTH_FAILED',
          message: error.message || '设备认证失败',
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
    }
  };
};

/**
 * 会话验证中间件
 * 验证用户会话是否有效
 */
const sessionAuth = () => {
  return async (event, context, next) => {
    try {
      if (!event.tokenPayload || !event.tokenPayload.sessionId) {
        throw new AuthError('MISSING_SESSION', '缺少会话信息');
      }
      
      // 可以在这里添加会话验证逻辑
      // 例如检查会话是否在Redis中存在
      
      event.sessionId = event.tokenPayload.sessionId;
      
      if (next) {
        return await next();
      }
      
      return { authorized: true };
      
    } catch (error) {
      console.error('Session auth failed:', error);
      
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          code: error.code || 'SESSION_AUTH_FAILED',
          message: error.message || '会话认证失败',
          timestamp: new Date().toISOString(),
          requestId: context.requestId
        })
      };
    }
  };
};

/**
 * 组合多个认证中间件
 * @param {Array} middlewares 中间件数组
 */
const combineAuth = (middlewares) => {
  return async (event, context, next) => {
    for (const middleware of middlewares) {
      const result = await middleware(event, context);
      
      if (result.statusCode) {
        // 认证失败，返回错误响应
        return result;
      }
      
      if (!result.authorized) {
        return {
          statusCode: 401,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            code: 'UNAUTHORIZED',
            message: '认证失败',
            timestamp: new Date().toISOString(),
            requestId: context.requestId
          })
        };
      }
    }
    
    if (next) {
      return await next();
    }
    
    return { authorized: true };
  };
};

/**
 * 认证错误类
 */
class AuthError extends Error {
  constructor(code, message) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
  }
}

/**
 * 提取Token信息的工具函数
 */
const extractTokenInfo = (event) => {
  const authHeader = event.headers.authorization || event.headers.Authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  return tokenManager.decodeToken(token);
};

/**
 * 检查Token是否即将过期
 * @param {number} thresholdSeconds 过期阈值(秒)
 */
const checkTokenExpiry = (thresholdSeconds = 300) => {
  return (event, context, next) => {
    const tokenInfo = extractTokenInfo(event);
    
    if (tokenInfo && tokenInfo.exp) {
      const now = Math.floor(Date.now() / 1000);
      const timeToExpiry = tokenInfo.exp - now;
      
      if (timeToExpiry <= thresholdSeconds) {
        // 添加过期警告头
        event.headers['X-Token-Expiry-Warning'] = 'true';
        event.headers['X-Token-TTL'] = timeToExpiry.toString();
      }
    }
    
    if (next) {
      return next();
    }
    
    return { authorized: true };
  };
};

module.exports = {
  authMiddleware,
  optionalAuth,
  adminAuth,
  gameAuth,
  userWriteAuth,
  premiumAuth,
  ownershipCheck,
  deviceAuth,
  sessionAuth,
  combineAuth,
  AuthError,
  extractTokenInfo,
  checkTokenExpiry
};