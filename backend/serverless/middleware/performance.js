/**
 * 性能监控中间件
 * 收集API响应时间、内存使用、数据库查询等性能指标
 * 集成增强的监控和告警系统
 */

const { RedisManager } = require('../utils/redis');
const { performanceMonitor } = require('../utils/monitoring');
const { logger } = require('../utils/logger');

class PerformanceMonitor {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.metrics = new Map();
  }

  /**
   * 增强的性能监控中间件
   */
  middleware() {
    return async (event, context, next) => {
      const startTime = Date.now();
      const startMemory = process.memoryUsage();

      // 生成请求ID
      const requestId = context.requestId || this.generateRequestId();

      // 创建性能日志器
      const perfLogger = logger.child({
        requestId,
        performance: true
      });

      try {
        perfLogger.debug('Request started', {
          path: event.path,
          method: event.httpMethod,
          userId: event.user?.id
        });

        // 执行请求
        const result = await next();

        const duration = Date.now() - startTime;
        const memoryUsage = this.calculateMemoryUsage(startMemory);

        // 记录成功指标到本地
        await this.recordMetrics({
          requestId,
          path: event.path,
          method: event.httpMethod,
          userId: event.user?.id,
          duration,
          memory: memoryUsage,
          status: 'success',
          statusCode: result?.statusCode || 200
        });

        // 记录到全局监控系统
        await performanceMonitor.recordApiMetrics(event, context, result || { statusCode: 200 }, startTime);

        perfLogger.info('Request completed', {
          duration: `${duration}ms`,
          memoryDelta: `${memoryUsage.delta}MB`,
          statusCode: result?.statusCode || 200
        });

        return result;

      } catch (error) {
        const duration = Date.now() - startTime;
        const memoryUsage = this.calculateMemoryUsage(startMemory);

        // 记录错误指标到本地
        await this.recordMetrics({
          requestId,
          path: event.path,
          method: event.httpMethod,
          userId: event.user?.id,
          duration,
          memory: memoryUsage,
          status: 'error',
          error: error.message,
          statusCode: error.statusCode || 500
        });

        // 记录到全局监控系统
        await performanceMonitor.recordApiMetrics(
          event,
          context,
          { statusCode: error.statusCode || 500 },
          startTime
        );

        perfLogger.error('Request failed', {
          duration: `${duration}ms`,
          memoryDelta: `${memoryUsage.delta}MB`,
          error: error.message,
          statusCode: error.statusCode || 500
        });

        throw error;
      }
    };
  }

  /**
   * 记录性能指标
   */
  async recordMetrics(metrics) {
    try {
      // 存储到Redis用于实时监控
      const key = `metrics:${Date.now()}:${metrics.requestId}`;
      await this.redis.setex(key, 3600, JSON.stringify(metrics));
      
      // 更新聚合指标
      await this.updateAggregateMetrics(metrics);
      
      // 检查性能阈值
      await this.checkPerformanceThresholds(metrics);
      
    } catch (error) {
      console.error('Failed to record metrics:', error);
    }
  }

  /**
   * 更新聚合指标
   */
  async updateAggregateMetrics(metrics) {
    const minute = Math.floor(Date.now() / 60000) * 60000;
    const aggregateKey = `aggregate:${minute}:${metrics.path}`;
    
    const current = await this.redis.get(aggregateKey);
    const aggregate = current ? JSON.parse(current) : {
      count: 0,
      totalDuration: 0,
      errorCount: 0,
      maxDuration: 0,
      minDuration: Infinity
    };
    
    aggregate.count++;
    aggregate.totalDuration += metrics.duration;
    aggregate.maxDuration = Math.max(aggregate.maxDuration, metrics.duration);
    aggregate.minDuration = Math.min(aggregate.minDuration, metrics.duration);
    
    if (metrics.status === 'error') {
      aggregate.errorCount++;
    }
    
    await this.redis.setex(aggregateKey, 3600, JSON.stringify(aggregate));
  }

  /**
   * 检查性能阈值
   */
  async checkPerformanceThresholds(metrics) {
    const thresholds = {
      responseTime: 1000, // 1秒
      memoryUsage: 100 * 1024 * 1024, // 100MB
      errorRate: 0.05 // 5%
    };
    
    // 响应时间告警
    if (metrics.duration > thresholds.responseTime) {
      await this.sendAlert('SLOW_RESPONSE', {
        path: metrics.path,
        duration: metrics.duration,
        threshold: thresholds.responseTime
      });
    }
    
    // 内存使用告警
    if (metrics.memory.heapUsed > thresholds.memoryUsage) {
      await this.sendAlert('HIGH_MEMORY', {
        path: metrics.path,
        memory: metrics.memory.heapUsed,
        threshold: thresholds.memoryUsage
      });
    }
  }

  /**
   * 发送告警
   */
  async sendAlert(type, data) {
    const alert = {
      type,
      timestamp: new Date().toISOString(),
      data,
      severity: this.getAlertSeverity(type)
    };
    
    // 存储告警记录
    const alertKey = `alert:${Date.now()}:${type}`;
    await this.redis.setex(alertKey, 86400, JSON.stringify(alert));
    
    // 这里可以集成钉钉、企业微信等告警通知
    console.warn('Performance Alert:', alert);
  }

  /**
   * 计算内存使用
   */
  calculateMemoryUsage(startMemory) {
    const currentMemory = process.memoryUsage();
    return {
      heapUsed: currentMemory.heapUsed,
      heapTotal: currentMemory.heapTotal,
      external: currentMemory.external,
      rss: currentMemory.rss,
      heapDelta: currentMemory.heapUsed - startMemory.heapUsed
    };
  }

  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取告警严重程度
   */
  getAlertSeverity(type) {
    const severityMap = {
      'SLOW_RESPONSE': 'warning',
      'HIGH_MEMORY': 'warning',
      'HIGH_ERROR_RATE': 'critical',
      'DATABASE_SLOW': 'warning'
    };
    
    return severityMap[type] || 'info';
  }

  /**
   * 获取性能报告
   */
  async getPerformanceReport(timeRange = 3600) {
    const endTime = Date.now();
    const startTime = endTime - (timeRange * 1000);
    
    // 获取聚合数据
    const keys = await this.redis.keys(`aggregate:*`);
    const aggregateData = [];
    
    for (const key of keys) {
      const data = await this.redis.get(key);
      if (data) {
        const parsed = JSON.parse(data);
        const timestamp = parseInt(key.split(':')[1]);
        
        if (timestamp >= startTime && timestamp <= endTime) {
          aggregateData.push({
            timestamp,
            path: key.split(':')[2],
            ...parsed,
            avgDuration: parsed.totalDuration / parsed.count,
            errorRate: parsed.errorCount / parsed.count
          });
        }
      }
    }
    
    return {
      timeRange: { startTime, endTime },
      summary: this.calculateSummary(aggregateData),
      details: aggregateData
    };
  }

  /**
   * 计算汇总统计
   */
  calculateSummary(data) {
    if (data.length === 0) return {};
    
    const totalRequests = data.reduce((sum, item) => sum + item.count, 0);
    const totalErrors = data.reduce((sum, item) => sum + item.errorCount, 0);
    const avgResponseTime = data.reduce((sum, item) => sum + item.avgDuration, 0) / data.length;
    
    return {
      totalRequests,
      totalErrors,
      errorRate: totalErrors / totalRequests,
      avgResponseTime,
      maxResponseTime: Math.max(...data.map(item => item.maxDuration)),
      minResponseTime: Math.min(...data.map(item => item.minDuration))
    };
  }
}

module.exports = { PerformanceMonitor };
