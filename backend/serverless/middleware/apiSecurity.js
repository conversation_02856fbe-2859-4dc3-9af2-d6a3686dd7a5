/**
 * API安全防护中间件
 * 集成多层安全防护机制
 */

const { logger } = require('../utils/logger');
const { SecurityMiddleware } = require('./security');
const { rateLimitMiddleware, combineRateLimit } = require('./rateLimit');
const { validateRequest } = require('./validation');
const { APIError, createError } = require('../utils/errors');
const crypto = require('crypto');

class APISecurityManager {
  constructor() {
    this.security = new SecurityMiddleware();
    this.suspiciousPatterns = [
      /(<script|javascript:|data:)/i,
      /(union|select|insert|update|delete|drop|create|alter)/i,
      /(eval|exec|system|shell_exec)/i,
      /(\.\.|\/etc\/|\/proc\/|\/sys\/)/i
    ];
    this.blockedUserAgents = [
      /bot|crawler|spider|scraper/i,
      /curl|wget|python-requests/i
    ];
  }

  /**
   * 综合安全防护中间件
   */
  securityProtection(options = {}) {
    return async (event, context) => {
      const securityLogger = logger.child({
        requestId: context.requestId,
        security: true
      });

      try {
        // 1. 基础安全检查
        await this.basicSecurityCheck(event, securityLogger);

        // 2. 输入安全验证
        await this.inputSecurityValidation(event, securityLogger);

        // 3. 请求频率限制
        if (options.rateLimit !== false) {
          await this.applyRateLimit(event, context, options.rateLimit);
        }

        // 4. 权限验证
        if (options.requireAuth !== false) {
          await this.authenticationCheck(event, securityLogger);
        }

        // 5. 业务权限检查
        if (options.permissions) {
          await this.permissionCheck(event, options.permissions, securityLogger);
        }

        // 6. 数据验证
        if (options.validation) {
          const validatedData = validateRequest(options.validation)(event);
          event.validatedData = validatedData;
        }

        // 7. 安全审计日志
        securityLogger.info('Security check passed', {
          path: event.path,
          method: event.httpMethod,
          userId: event.user?.id,
          ip: this.security.getClientIP(event)
        });

        return { allowed: true };

      } catch (error) {
        securityLogger.warn('Security check failed', {
          error: error.message,
          path: event.path,
          method: event.httpMethod,
          ip: this.security.getClientIP(event)
        });

        throw error;
      }
    };
  }

  /**
   * 基础安全检查
   */
  async basicSecurityCheck(event, logger) {
    const clientIP = this.security.getClientIP(event);
    const userAgent = event.headers['user-agent'] || '';

    // 检查User-Agent黑名单
    if (this.blockedUserAgents.some(pattern => pattern.test(userAgent))) {
      logger.logSecurity('Blocked User-Agent detected', 'medium', {
        userAgent,
        ip: clientIP
      });
      throw createError.forbidden('访问被拒绝');
    }

    // 检查请求头异常
    if (!userAgent || userAgent.length < 10) {
      logger.logSecurity('Suspicious User-Agent', 'low', {
        userAgent,
        ip: clientIP
      });
    }

    // 检查请求大小
    const bodySize = event.body ? Buffer.byteLength(event.body, 'utf8') : 0;
    if (bodySize > 10 * 1024 * 1024) { // 10MB限制
      throw createError.invalidParams('请求体过大');
    }

    // 检查请求头数量
    const headerCount = Object.keys(event.headers || {}).length;
    if (headerCount > 50) {
      logger.logSecurity('Too many headers', 'medium', {
        headerCount,
        ip: clientIP
      });
      throw createError.invalidParams('请求头过多');
    }
  }

  /**
   * 输入安全验证
   */
  async inputSecurityValidation(event, logger) {
    const clientIP = this.security.getClientIP(event);

    // 检查URL路径
    if (this.containsSuspiciousPattern(event.path)) {
      logger.logSecurity('Suspicious URL pattern', 'high', {
        path: event.path,
        ip: clientIP
      });
      throw createError.forbidden('非法请求路径');
    }

    // 检查查询参数
    if (event.queryStringParameters) {
      for (const [key, value] of Object.entries(event.queryStringParameters)) {
        if (this.containsSuspiciousPattern(key) || this.containsSuspiciousPattern(value)) {
          logger.logSecurity('Suspicious query parameter', 'high', {
            key,
            value: this.security.maskValue(value),
            ip: clientIP
          });
          throw createError.invalidParams('非法查询参数');
        }
      }
    }

    // 检查请求体
    if (event.body) {
      const bodyStr = typeof event.body === 'string' ? event.body : JSON.stringify(event.body);
      if (this.containsSuspiciousPattern(bodyStr)) {
        logger.logSecurity('Suspicious request body', 'high', {
          bodyPreview: bodyStr.substring(0, 100),
          ip: clientIP
        });
        throw createError.invalidParams('非法请求内容');
      }
    }
  }

  /**
   * 应用限流策略
   */
  async applyRateLimit(event, context, rateLimitConfig) {
    const defaultConfig = {
      types: [
        { type: 'ip', options: {} },
        { type: 'user', options: {} }
      ]
    };

    const config = { ...defaultConfig, ...rateLimitConfig };
    const rateLimitCheck = combineRateLimit(config.types);
    
    const result = await rateLimitCheck(event, context);
    if (!result.allowed) {
      throw new APIError('RATE_LIMIT_EXCEEDED', '请求频率过高', null, 429);
    }
  }

  /**
   * 身份验证检查
   */
  async authenticationCheck(event, logger) {
    if (!event.user) {
      logger.logSecurity('Unauthenticated request', 'medium', {
        path: event.path,
        ip: this.security.getClientIP(event)
      });
      throw createError.unauthorized('需要登录');
    }

    // 检查用户状态
    if (event.user.status !== 'active') {
      logger.logSecurity('Inactive user access attempt', 'medium', {
        userId: event.user.id,
        status: event.user.status,
        ip: this.security.getClientIP(event)
      });
      throw createError.forbidden('账户已被禁用');
    }
  }

  /**
   * 权限检查
   */
  async permissionCheck(event, requiredPermissions, logger) {
    const userPermissions = event.user?.permissions || [];
    
    const hasPermission = requiredPermissions.every(permission => {
      return userPermissions.includes(permission) || userPermissions.includes('admin');
    });

    if (!hasPermission) {
      logger.logSecurity('Insufficient permissions', 'medium', {
        userId: event.user?.id,
        required: requiredPermissions,
        actual: userPermissions,
        ip: this.security.getClientIP(event)
      });
      throw createError.forbidden('权限不足');
    }
  }

  /**
   * 检查是否包含可疑模式
   */
  containsSuspiciousPattern(input) {
    if (typeof input !== 'string') return false;
    return this.suspiciousPatterns.some(pattern => pattern.test(input));
  }

  /**
   * 生成请求签名
   */
  generateRequestSignature(event) {
    const data = {
      method: event.httpMethod,
      path: event.path,
      timestamp: Date.now(),
      body: event.body || ''
    };

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
  }

  /**
   * CSRF防护
   */
  csrfProtection() {
    return (event, context) => {
      if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(event.httpMethod)) {
        const csrfToken = event.headers['x-csrf-token'] || event.headers['X-CSRF-Token'];
        const sessionToken = event.tokenPayload?.sessionId;

        if (!csrfToken || !sessionToken) {
          throw createError.forbidden('CSRF token missing');
        }

        // 验证CSRF token
        const expectedToken = crypto
          .createHash('sha256')
          .update(`${sessionToken}:${process.env.CSRF_SECRET}`)
          .digest('hex');

        if (csrfToken !== expectedToken) {
          throw createError.forbidden('Invalid CSRF token');
        }
      }

      return { allowed: true };
    };
  }

  /**
   * 请求重放攻击防护
   */
  replayAttackProtection(windowMs = 5 * 60 * 1000) {
    return async (event, context) => {
      const timestamp = event.headers['x-timestamp'];
      const signature = event.headers['x-signature'];

      if (!timestamp || !signature) {
        throw createError.invalidParams('Missing timestamp or signature');
      }

      const requestTime = parseInt(timestamp);
      const now = Date.now();

      // 检查时间窗口
      if (Math.abs(now - requestTime) > windowMs) {
        throw createError.invalidParams('Request timestamp expired');
      }

      // 检查签名是否已使用
      const signatureKey = `replay:${signature}`;
      const redis = require('../utils/redis');
      
      const exists = await redis.get(signatureKey);
      if (exists) {
        throw createError.forbidden('Request replay detected');
      }

      // 记录签名
      await redis.setex(signatureKey, Math.ceil(windowMs / 1000), '1');

      return { allowed: true };
    };
  }
}

// 创建全局实例
const apiSecurity = new APISecurityManager();

module.exports = {
  APISecurityManager,
  apiSecurity
};
