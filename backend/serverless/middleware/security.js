/**
 * 安全加固中间件
 * 提供API安全头、输入过滤、审计日志等安全功能
 */

const crypto = require('crypto');
const { RedisManager } = require('../utils/redis');

class SecurityMiddleware {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.sensitiveFields = ['password', 'token', 'secret', 'key'];
  }

  /**
   * 安全头中间件
   */
  securityHeaders() {
    return (event, context, next) => {
      // 设置安全响应头
      const securityHeaders = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
      };

      // 将安全头添加到响应中
      event.securityHeaders = securityHeaders;
      
      return next ? next() : { headers: securityHeaders };
    };
  }

  /**
   * 输入过滤中间件
   */
  inputSanitization() {
    return (event, context, next) => {
      try {
        // 过滤请求体
        if (event.body) {
          const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
          event.body = JSON.stringify(this.sanitizeObject(body));
        }

        // 过滤查询参数
        if (event.queryStringParameters) {
          event.queryStringParameters = this.sanitizeObject(event.queryStringParameters);
        }

        // 过滤路径参数
        if (event.pathParameters) {
          event.pathParameters = this.sanitizeObject(event.pathParameters);
        }

        return next ? next() : { sanitized: true };
        
      } catch (error) {
        console.error('Input sanitization error:', error);
        throw new Error('Invalid input format');
      }
    };
  }

  /**
   * 审计日志中间件
   */
  auditLog() {
    return async (event, context, next) => {
      const startTime = Date.now();
      const auditData = {
        requestId: context.requestId,
        timestamp: new Date().toISOString(),
        method: event.httpMethod,
        path: event.path,
        userAgent: event.headers['user-agent'],
        sourceIP: this.getClientIP(event),
        userId: event.user?.id,
        sessionId: event.tokenPayload?.sessionId
      };

      try {
        const result = await (next ? next() : Promise.resolve());
        
        // 记录成功的审计日志
        await this.recordAuditLog({
          ...auditData,
          status: 'success',
          duration: Date.now() - startTime,
          responseSize: JSON.stringify(result).length
        });

        return result;
        
      } catch (error) {
        // 记录失败的审计日志
        await this.recordAuditLog({
          ...auditData,
          status: 'error',
          error: error.message,
          duration: Date.now() - startTime
        });

        throw error;
      }
    };
  }

  /**
   * 防暴力破解中间件
   */
  bruteForceProtection(options = {}) {
    const {
      maxAttempts = 5,
      windowMs = 15 * 60 * 1000, // 15分钟
      blockDurationMs = 60 * 60 * 1000 // 1小时
    } = options;

    return async (event, context, next) => {
      const clientIP = this.getClientIP(event);
      const key = `bruteforce:${clientIP}`;
      
      try {
        // 检查是否被封禁
        const blockKey = `blocked:${clientIP}`;
        const isBlocked = await this.redis.get(blockKey);
        
        if (isBlocked) {
          throw new Error('IP temporarily blocked due to suspicious activity');
        }

        // 获取当前尝试次数
        const attempts = await this.redis.get(key);
        const currentAttempts = attempts ? parseInt(attempts) : 0;

        if (currentAttempts >= maxAttempts) {
          // 封禁IP
          await this.redis.setex(blockKey, Math.floor(blockDurationMs / 1000), '1');
          await this.redis.del(key);
          
          throw new Error('Too many failed attempts. IP blocked.');
        }

        const result = await (next ? next() : Promise.resolve());
        
        // 成功则清除计数
        await this.redis.del(key);
        
        return result;
        
      } catch (error) {
        // 失败则增加计数
        const newAttempts = await this.redis.incr(key);
        if (newAttempts === 1) {
          await this.redis.expire(key, Math.floor(windowMs / 1000));
        }
        
        throw error;
      }
    };
  }

  /**
   * 数据脱敏
   */
  dataMasking(data) {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const masked = Array.isArray(data) ? [] : {};
    
    for (const [key, value] of Object.entries(data)) {
      if (this.isSensitiveField(key)) {
        masked[key] = this.maskValue(value);
      } else if (typeof value === 'object') {
        masked[key] = this.dataMasking(value);
      } else {
        masked[key] = value;
      }
    }
    
    return masked;
  }

  /**
   * 对象清理
   */
  sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return this.sanitizeValue(obj);
    }

    const sanitized = Array.isArray(obj) ? [] : {};
    
    for (const [key, value] of Object.entries(obj)) {
      const cleanKey = this.sanitizeValue(key);
      if (typeof value === 'object') {
        sanitized[cleanKey] = this.sanitizeObject(value);
      } else {
        sanitized[cleanKey] = this.sanitizeValue(value);
      }
    }
    
    return sanitized;
  }

  /**
   * 值清理
   */
  sanitizeValue(value) {
    if (typeof value !== 'string') {
      return value;
    }

    return value
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
      .replace(/javascript:/gi, '') // 移除javascript协议
      .replace(/on\w+\s*=/gi, '') // 移除事件处理器
      .replace(/[<>'"]/g, '') // 移除危险字符
      .trim();
  }

  /**
   * 检查是否为敏感字段
   */
  isSensitiveField(fieldName) {
    const lowerField = fieldName.toLowerCase();
    return this.sensitiveFields.some(sensitive => lowerField.includes(sensitive));
  }

  /**
   * 掩码处理
   */
  maskValue(value) {
    if (typeof value !== 'string') {
      return '***';
    }
    
    if (value.length <= 4) {
      return '*'.repeat(value.length);
    }
    
    return value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2);
  }

  /**
   * 获取客户端IP
   */
  getClientIP(event) {
    return event.headers['x-forwarded-for'] ||
           event.headers['x-real-ip'] ||
           event.requestContext?.identity?.sourceIp ||
           'unknown';
  }

  /**
   * 记录审计日志
   */
  async recordAuditLog(auditData) {
    try {
      // 脱敏处理
      const maskedData = this.dataMasking(auditData);
      
      // 存储到Redis (保留7天)
      const key = `audit:${auditData.timestamp}:${auditData.requestId}`;
      await this.redis.setex(key, 7 * 24 * 3600, JSON.stringify(maskedData));
      
      // 异步写入到持久化存储
      this.persistAuditLog(maskedData).catch(error => {
        console.error('Failed to persist audit log:', error);
      });
      
    } catch (error) {
      console.error('Failed to record audit log:', error);
    }
  }

  /**
   * 持久化审计日志
   */
  async persistAuditLog(auditData) {
    // 这里可以集成到数据库或日志服务
    // 例如：写入到专门的审计日志表
    console.log('Audit Log:', JSON.stringify(auditData));
  }

  /**
   * 生成安全令牌
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 加密敏感数据
   */
  encryptSensitiveData(data, key) {
    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, key);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  /**
   * 解密敏感数据
   */
  decryptSensitiveData(encryptedData, key) {
    const algorithm = 'aes-256-gcm';
    const decipher = crypto.createDecipher(algorithm, key);
    
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

module.exports = { SecurityMiddleware };
