/**
 * 错误处理中间件
 * 统一处理和格式化错误响应
 * 集成结构化日志系统
 */

const { APIError, errorUtils } = require('../utils/errors');
const { logger } = require('../utils/logger');

/**
 * 增强的错误处理中间件
 * 包装处理器函数，自动捕获和处理错误，集成结构化日志
 */
const errorHandler = (handler) => {
  return async (event, context) => {
    const startTime = Date.now();
    const requestLogger = logger.child({
      requestId: context.requestId,
      functionName: context.functionName,
      path: event.path,
      method: event.httpMethod,
      userId: event.user?.id
    });

    try {
      requestLogger.info('Request started');
      const result = await handler(event, context);

      // 记录成功响应
      const duration = Date.now() - startTime;
      requestLogger.info('Request completed successfully', {
        duration: `${duration}ms`,
        statusCode: result.statusCode || 200
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // 使用结构化日志记录错误
      requestLogger.error('Request failed', {
        error: error.message,
        errorCode: error.code,
        statusCode: error.statusCode,
        duration: `${duration}ms`,
        stack: error.stack,
        errorType: error.constructor.name
      });

      // 上报错误到监控系统
      await errorMonitor.report(error, {
        requestId: context.requestId,
        path: event.path,
        method: event.httpMethod,
        userId: event.user?.id,
        duration
      });

      // 记录错误统计
      errorMonitor.track(error, {
        path: event.path,
        method: event.httpMethod
      });

      // 格式化错误响应
      const errorResponse = errorUtils.formatErrorResponse(error, context.requestId);

      return {
        statusCode: errorResponse.statusCode,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'X-Request-ID': context.requestId
        },
        body: JSON.stringify(errorResponse.body)
      };
    }
  };
};

/**
 * 全局错误处理器
 * 处理未被捕获的错误
 */
const globalErrorHandler = (error, event, context) => {
  console.error('Global error handler:', {
    error: error.message,
    stack: error.stack,
    event: {
      path: event.path,
      method: event.httpMethod
    },
    context: {
      requestId: context.requestId
    }
  });
  
  let statusCode = 500;
  let errorCode = 'INTERNAL_ERROR';
  let message = '服务器内部错误';
  let details = null;
  
  if (error instanceof APIError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    message = error.message;
    details = error.details;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 'INVALID_PARAMS';
    message = error.message;
    details = error.details;
  } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
    statusCode = 400;
    errorCode = 'INVALID_JSON';
    message = '请求体JSON格式错误';
  } else if (error.code === 'ECONNREFUSED') {
    statusCode = 503;
    errorCode = 'SERVICE_UNAVAILABLE';
    message = '服务暂时不可用';
  } else if (error.code === 'ETIMEDOUT') {
    statusCode = 504;
    errorCode = 'GATEWAY_TIMEOUT';
    message = '请求超时';
  }
  
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      code: errorCode,
      message,
      details,
      timestamp: new Date().toISOString(),
      requestId: context.requestId
    })
  };
};

/**
 * 异步错误包装器
 * 自动捕获异步函数中的错误
 */
const asyncErrorWrapper = (fn) => {
  return async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw error;
    }
  };
};

/**
 * 数据库错误处理器
 * 处理数据库相关错误
 */
const dbErrorHandler = (error) => {
  console.error('Database error:', error);
  
  if (error.code === 'ER_DUP_ENTRY') {
    throw new APIError('DUPLICATE_ENTRY', '数据已存在', null, 400);
  } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
    throw new APIError('FOREIGN_KEY_CONSTRAINT', '关联数据不存在', null, 400);
  } else if (error.code === 'ER_DATA_TOO_LONG') {
    throw new APIError('DATA_TOO_LONG', '数据长度超出限制', null, 400);
  } else if (error.code === 'ECONNREFUSED') {
    throw new APIError('DATABASE_UNAVAILABLE', '数据库连接失败', null, 503);
  } else if (error.code === 'PROTOCOL_CONNECTION_LOST') {
    throw new APIError('DATABASE_CONNECTION_LOST', '数据库连接丢失', null, 503);
  }
  
  throw new APIError('DATABASE_ERROR', '数据库操作失败', null, 500);
};

/**
 * Redis错误处理器
 * 处理Redis相关错误
 */
const redisErrorHandler = (error) => {
  console.error('Redis error:', error);
  
  if (error.code === 'ECONNREFUSED') {
    throw new APIError('CACHE_UNAVAILABLE', '缓存服务不可用', null, 503);
  } else if (error.code === 'NOAUTH') {
    throw new APIError('CACHE_AUTH_FAILED', '缓存服务认证失败', null, 503);
  }
  
  throw new APIError('CACHE_ERROR', '缓存服务异常', null, 500);
};

/**
 * 第三方API错误处理器
 * 处理外部API调用错误
 */
const externalApiErrorHandler = (error, serviceName = '外部服务') => {
  console.error(`${serviceName} API error:`, error);
  
  if (error.response) {
    const status = error.response.status;
    const data = error.response.data;
    
    if (status === 400) {
      throw new APIError('EXTERNAL_BAD_REQUEST', `${serviceName}请求参数错误`, data, 400);
    } else if (status === 401) {
      throw new APIError('EXTERNAL_UNAUTHORIZED', `${serviceName}认证失败`, data, 401);
    } else if (status === 403) {
      throw new APIError('EXTERNAL_FORBIDDEN', `${serviceName}访问被拒绝`, data, 403);
    } else if (status === 404) {
      throw new APIError('EXTERNAL_NOT_FOUND', `${serviceName}资源不存在`, data, 404);
    } else if (status === 429) {
      throw new APIError('EXTERNAL_RATE_LIMIT', `${serviceName}请求频率限制`, data, 429);
    } else if (status >= 500) {
      throw new APIError('EXTERNAL_SERVER_ERROR', `${serviceName}服务器错误`, data, 502);
    }
  } else if (error.code === 'ECONNABORTED') {
    throw new APIError('EXTERNAL_TIMEOUT', `${serviceName}请求超时`, null, 504);
  } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
    throw new APIError('EXTERNAL_UNAVAILABLE', `${serviceName}不可用`, null, 503);
  }
  
  throw new APIError('EXTERNAL_ERROR', `${serviceName}异常`, null, 502);
};

/**
 * 业务逻辑错误处理器
 * 处理特定业务场景的错误
 */
const businessErrorHandler = {
  /**
   * 用户相关错误
   */
  user: (error) => {
    if (error.message.includes('not found')) {
      throw new APIError('USER_NOT_FOUND', '用户不存在', null, 404);
    } else if (error.message.includes('inactive')) {
      throw new APIError('USER_INACTIVE', '用户已被禁用', null, 403);
    } else if (error.message.includes('duplicate')) {
      throw new APIError('USER_EXISTS', '用户已存在', null, 400);
    }
    
    throw error;
  },
  
  /**
   * 游戏相关错误
   */
  game: (error) => {
    if (error.message.includes('session not found')) {
      throw new APIError('GAME_SESSION_NOT_FOUND', '游戏会话不存在', null, 404);
    } else if (error.message.includes('session expired')) {
      throw new APIError('GAME_SESSION_EXPIRED', '游戏会话已过期', null, 400);
    } else if (error.message.includes('question not found')) {
      throw new APIError('QUESTION_NOT_FOUND', '题目不存在', null, 404);
    } else if (error.message.includes('invalid answer')) {
      throw new APIError('INVALID_ANSWER', '答案格式错误', null, 400);
    }
    
    throw error;
  },
  
  /**
   * 认证相关错误
   */
  auth: (error) => {
    if (error.message.includes('token expired')) {
      throw new APIError('TOKEN_EXPIRED', '访问令牌已过期', null, 401);
    } else if (error.message.includes('invalid token')) {
      throw new APIError('INVALID_TOKEN', '无效的访问令牌', null, 401);
    } else if (error.message.includes('insufficient permissions')) {
      throw new APIError('INSUFFICIENT_PERMISSIONS', '权限不足', null, 403);
    }
    
    throw error;
  }
};

/**
 * 错误监控和上报
 */
const errorMonitor = {
  /**
   * 上报错误到监控系统
   */
  report: async (error, context = {}) => {
    try {
      // 这里可以集成错误监控服务，如Sentry、腾讯云监控等
      const errorReport = {
        timestamp: new Date().toISOString(),
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
          code: error.code
        },
        context,
        severity: error.statusCode >= 500 ? 'error' : 'warning'
      };
      
      // TODO: 发送到监控系统
      console.log('Error report:', JSON.stringify(errorReport));
      
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  },
  
  /**
   * 记录错误统计
   */
  track: (error, context = {}) => {
    try {
      // 这里可以记录错误统计信息
      const errorStat = {
        code: error.code || 'UNKNOWN',
        count: 1,
        timestamp: new Date().toISOString(),
        context
      };
      
      // TODO: 发送到统计系统
      console.log('Error stat:', JSON.stringify(errorStat));
      
    } catch (trackError) {
      console.error('Failed to track error:', trackError);
    }
  }
};

/**
 * 错误恢复策略
 */
const errorRecovery = {
  /**
   * 重试机制
   */
  retry: async (fn, maxRetries = 3, delay = 1000) => {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // 不重试客户端错误
        if (error instanceof APIError && error.statusCode < 500) {
          throw error;
        }
        
        if (i === maxRetries) {
          break;
        }
        
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
    
    throw lastError;
  },
  
  /**
   * 断路器模式
   */
  circuitBreaker: (fn, threshold = 5, timeout = 60000) => {
    let failures = 0;
    let lastFailTime = 0;
    let state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    
    return async (...args) => {
      const now = Date.now();
      
      if (state === 'OPEN') {
        if (now - lastFailTime > timeout) {
          state = 'HALF_OPEN';
        } else {
          throw new APIError('SERVICE_UNAVAILABLE', '服务暂时不可用', null, 503);
        }
      }
      
      try {
        const result = await fn(...args);
        
        if (state === 'HALF_OPEN') {
          state = 'CLOSED';
          failures = 0;
        }
        
        return result;
        
      } catch (error) {
        failures++;
        lastFailTime = now;
        
        if (failures >= threshold) {
          state = 'OPEN';
        }
        
        throw error;
      }
    };
  }
};

module.exports = {
  errorHandler,
  globalErrorHandler,
  asyncErrorWrapper,
  dbErrorHandler,
  redisErrorHandler,
  externalApiErrorHandler,
  businessErrorHandler,
  errorMonitor,
  errorRecovery
};