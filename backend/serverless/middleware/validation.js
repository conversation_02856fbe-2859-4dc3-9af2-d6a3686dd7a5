/**
 * 请求参数验证中间件
 * 基于JSON Schema进行参数验证
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const { ValidationError } = require('../utils/errors');

/**
 * 参数验证器类
 */
class RequestValidator {
  constructor() {
    this.ajv = new Ajv({ 
      allErrors: true,
      removeAdditional: true,
      useDefaults: true,
      coerceTypes: true
    });
    addFormats(this.ajv);
    
    // 添加自定义验证格式
    this.addCustomFormats();
  }

  /**
   * 添加自定义验证格式
   */
  addCustomFormats() {
    // 手机号验证
    this.ajv.addFormat('mobile', {
      type: 'string',
      validate: (data) => /^1[3-9]\d{9}$/.test(data)
    });
    
    // 微信OpenID验证
    this.ajv.addFormat('openid', {
      type: 'string',
      validate: (data) => /^[a-zA-Z0-9_-]{28}$/.test(data)
    });
    
    // 设备ID验证
    this.ajv.addFormat('deviceId', {
      type: 'string',
      validate: (data) => /^[a-fA-F0-9-]{36}$/.test(data)
    });
    
    // 游戏会话ID验证
    this.ajv.addFormat('sessionId', {
      type: 'string',
      validate: (data) => /^[a-fA-F0-9]{32}$/.test(data)
    });
  }

  /**
   * 验证数据
   * @param {Object} schema JSON Schema
   * @param {any} data 待验证数据
   * @returns {any} 验证后的数据
   */
  validate(schema, data) {
    const validate = this.ajv.compile(schema);
    const valid = validate(data);
    
    if (!valid) {
      const errors = validate.errors.map(error => ({
        field: error.instancePath.slice(1) || error.params?.missingProperty || error.propertyName,
        message: this.getErrorMessage(error),
        value: error.data,
        allowedValues: error.params?.allowedValues
      }));
      
      throw new ValidationError(errors);
    }
    
    return data;
  }

  /**
   * 获取友好的错误消息
   */
  getErrorMessage(error) {
    const messages = {
      required: '此字段为必填项',
      type: `字段类型应为 ${error.params?.type}`,
      format: `字段格式不正确`,
      minimum: `数值不能小于 ${error.params?.limit}`,
      maximum: `数值不能大于 ${error.params?.limit}`,
      minLength: `长度不能少于 ${error.params?.limit} 个字符`,
      maxLength: `长度不能超过 ${error.params?.limit} 个字符`,
      pattern: '字段格式不符合要求',
      enum: `值必须是以下之一: ${error.params?.allowedValues?.join(', ')}`,
      minItems: `数组长度不能少于 ${error.params?.limit} 项`,
      maxItems: `数组长度不能超过 ${error.params?.limit} 项`,
      uniqueItems: '数组中的项必须唯一'
    };
    
    return messages[error.keyword] || error.message || '字段验证失败';
  }
}

// 全局验证器实例
const validator = new RequestValidator();

/**
 * 验证中间件工厂
 * @param {Object} schema JSON Schema
 * @returns {Function} 验证函数
 */
const validateRequest = (schema) => {
  return (event) => {
    let data = {};
    
    // 解析请求体
    if (event.body) {
      try {
        if (typeof event.body === 'string') {
          data = JSON.parse(event.body);
        } else {
          data = event.body;
        }
      } catch (error) {
        throw new ValidationError([{
          field: 'body',
          message: '无效的JSON格式',
          value: event.body
        }]);
      }
    }
    
    // 合并查询参数
    if (event.queryStringParameters) {
      data = { ...data, ...event.queryStringParameters };
    }
    
    // 合并路径参数
    if (event.pathParameters) {
      data = { ...data, ...event.pathParameters };
    }
    
    // 合并头部参数（如果需要）
    if (schema.properties) {
      Object.keys(schema.properties).forEach(key => {
        if (key.startsWith('header_')) {
          const headerName = key.replace('header_', '').toLowerCase();
          const headerValue = event.headers[headerName] || event.headers[headerName.toUpperCase()];
          if (headerValue) {
            data[key] = headerValue;
          }
        }
      });
    }
    
    return validator.validate(schema, data);
  };
};

/**
 * 常用验证Schema
 */
const VALIDATION_SCHEMAS = {
  // 微信登录
  wechatLogin: {
    type: 'object',
    required: ['code'],
    properties: {
      code: { 
        type: 'string', 
        minLength: 32,
        maxLength: 32,
        description: '微信授权码'
      },
      encryptedData: { 
        type: 'string',
        description: '加密用户数据'
      },
      iv: { 
        type: 'string',
        description: '初始化向量'
      }
    },
    additionalProperties: false
  },
  
  // 刷新Token
  refreshToken: {
    type: 'object',
    required: ['refreshToken'],
    properties: {
      refreshToken: { 
        type: 'string', 
        minLength: 1,
        description: '刷新令牌'
      }
    },
    additionalProperties: false
  },
  
  // 创建游戏会话
  createGameSession: {
    type: 'object',
    required: ['category', 'difficulty'],
    properties: {
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'],
        description: '方言类别'
      },
      difficulty: { 
        type: 'integer', 
        minimum: 1, 
        maximum: 5,
        description: '难度等级'
      },
      questionCount: { 
        type: 'integer', 
        minimum: 5, 
        maximum: 50, 
        default: 10,
        description: '题目数量'
      },
      gameMode: { 
        type: 'string', 
        enum: ['standard', 'challenge', 'practice'], 
        default: 'standard',
        description: '游戏模式'
      }
    },
    additionalProperties: false
  },
  
  // 提交答案
  submitAnswer: {
    type: 'object',
    required: ['questionId', 'answer'],
    properties: {
      questionId: { 
        type: 'integer', 
        minimum: 1,
        description: '题目ID'
      },
      answer: { 
        type: 'string', 
        minLength: 1, 
        maxLength: 200,
        description: '用户答案'
      },
      answerTime: { 
        type: 'number', 
        minimum: 0,
        maximum: 300,
        description: '答题用时(秒)'
      },
      hintUsed: { 
        type: 'boolean', 
        default: false,
        description: '是否使用提示'
      }
    },
    additionalProperties: false
  },
  
  // 更新用户资料
  updateProfile: {
    type: 'object',
    properties: {
      nickname: { 
        type: 'string', 
        minLength: 1, 
        maxLength: 30,
        description: '用户昵称'
      },
      avatar: { 
        type: 'string', 
        format: 'uri',
        description: '头像URL'
      },
      province: { 
        type: 'string', 
        maxLength: 50,
        description: '省份'
      },
      city: { 
        type: 'string', 
        maxLength: 50,
        description: '城市'
      },
      language: { 
        type: 'string', 
        enum: ['zh_CN', 'zh_TW', 'en_US'],
        default: 'zh_CN',
        description: '语言设置'
      }
    },
    additionalProperties: false,
    minProperties: 1
  },
  
  // 分页参数
  pagination: {
    type: 'object',
    properties: {
      page: { 
        type: 'integer', 
        minimum: 1, 
        default: 1,
        description: '页码'
      },
      size: { 
        type: 'integer', 
        minimum: 1, 
        maximum: 100, 
        default: 20,
        description: '每页数量'
      }
    },
    additionalProperties: true
  },
  
  // 获取题目
  getQuestions: {
    type: 'object',
    properties: {
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'],
        description: '方言类别'
      },
      difficulty: { 
        type: 'integer', 
        minimum: 1, 
        maximum: 5,
        description: '难度等级'
      },
      limit: { 
        type: 'integer', 
        minimum: 1, 
        maximum: 50, 
        default: 10,
        description: '题目数量'
      },
      excludeIds: {
        type: 'array',
        items: { type: 'integer' },
        maxItems: 1000,
        description: '排除的题目ID'
      }
    },
    additionalProperties: false
  },
  
  // 获取排行榜
  getLeaderboard: {
    type: 'object',
    properties: {
      type: { 
        type: 'string', 
        enum: ['overall', 'weekly', 'monthly', 'friends'],
        default: 'overall',
        description: '排行榜类型'
      },
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'],
        description: '方言类别'
      },
      period: { 
        type: 'string',
        pattern: '^(current|\\d{4}-\\d{2}|\\d{4}-W\\d{2})$',
        default: 'current',
        description: '时间周期'
      },
      page: { 
        type: 'integer', 
        minimum: 1, 
        default: 1,
        description: '页码'
      },
      size: { 
        type: 'integer', 
        minimum: 1, 
        maximum: 100, 
        default: 50,
        description: '每页数量'
      }
    },
    additionalProperties: false
  },
  
  // 创建分享
  createShare: {
    type: 'object',
    required: ['type', 'content'],
    properties: {
      type: { 
        type: 'string', 
        enum: ['score', 'invite', 'achievement'],
        description: '分享类型'
      },
      content: { 
        type: 'object',
        description: '分享内容'
      },
      platform: { 
        type: 'string', 
        enum: ['wechat', 'weibo', 'qq'], 
        default: 'wechat',
        description: '分享平台'
      }
    },
    additionalProperties: false
  },
  
  // 路径参数验证
  pathParams: {
    userId: {
      type: 'object',
      required: ['userId'],
      properties: {
        userId: { 
          type: 'string',
          pattern: '^\\d+$',
          description: '用户ID'
        }
      }
    },
    
    sessionId: {
      type: 'object',
      required: ['sessionId'],
      properties: {
        sessionId: { 
          type: 'string',
          format: 'sessionId',
          description: '游戏会话ID'
        }
      }
    },
    
    questionId: {
      type: 'object',
      required: ['questionId'],
      properties: {
        questionId: { 
          type: 'string',
          pattern: '^\\d+$',
          description: '题目ID'
        }
      }
    }
  }
};

/**
 * 批量验证中间件
 * @param {Object} schemas 多个schema对象
 */
const validateMultiple = (schemas) => {
  return (event) => {
    const results = {};
    
    Object.keys(schemas).forEach(key => {
      try {
        results[key] = validateRequest(schemas[key])(event);
      } catch (error) {
        throw error;
      }
    });
    
    return results;
  };
};

/**
 * 条件验证中间件
 * @param {Function} condition 条件函数
 * @param {Object} schema 验证schema
 */
const validateIf = (condition, schema) => {
  return (event) => {
    if (condition(event)) {
      return validateRequest(schema)(event);
    }
    return {};
  };
};

/**
 * 自定义验证器
 */
const customValidators = {
  /**
   * 验证文件上传
   */
  validateFileUpload: (maxSize = 10 * 1024 * 1024, allowedTypes = []) => {
    return (file) => {
      if (!file) {
        throw new ValidationError([{
          field: 'file',
          message: '文件不能为空'
        }]);
      }
      
      if (file.size > maxSize) {
        throw new ValidationError([{
          field: 'file',
          message: `文件大小不能超过 ${maxSize / 1024 / 1024}MB`
        }]);
      }
      
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        throw new ValidationError([{
          field: 'file',
          message: `文件类型必须是: ${allowedTypes.join(', ')}`
        }]);
      }
      
      return file;
    };
  },
  
  /**
   * 验证时间范围
   */
  validateDateRange: (startDate, endDate) => {
    if (new Date(startDate) >= new Date(endDate)) {
      throw new ValidationError([{
        field: 'dateRange',
        message: '开始时间必须早于结束时间'
      }]);
    }
    return true;
  },
  
  /**
   * 验证唯一性
   */
  validateUnique: async (Model, field, value, excludeId = null) => {
    const existing = await Model.findOne({ [field]: value });
    
    if (existing && (!excludeId || existing.id !== excludeId)) {
      throw new ValidationError([{
        field,
        message: `${field}已存在`
      }]);
    }
    
    return true;
  }
};

module.exports = {
  RequestValidator,
  validateRequest,
  validateMultiple,
  validateIf,
  customValidators,
  VALIDATION_SCHEMAS
};