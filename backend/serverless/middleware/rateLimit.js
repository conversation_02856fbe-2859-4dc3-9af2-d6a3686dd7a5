/**
 * 限流中间件
 * 实现令牌桶和滑动窗口限流算法
 */

const redis = require('../utils/redis');
const config = require('../../config');
const { RateLimitError } = require('../utils/errors');

/**
 * 令牌桶限流算法
 */
class TokenBucket {
  constructor(capacity, refillRate, redisClient) {
    this.capacity = capacity;        // 桶容量
    this.refillRate = refillRate;    // 令牌补充速率（个/秒）
    this.redis = redisClient;
  }

  /**
   * 消费令牌
   * @param {string} key 限流键
   * @param {number} tokens 消费令牌数
   * @returns {Object} 消费结果
   */
  async consume(key, tokens = 1) {
    const now = Date.now();
    const bucketKey = `bucket:${key}`;
    
    // Lua脚本实现原子操作
    const script = `
      local bucket_key = KEYS[1]
      local capacity = tonumber(ARGV[1])
      local refill_rate = tonumber(ARGV[2])
      local tokens_requested = tonumber(ARGV[3])
      local now = tonumber(ARGV[4])
      
      -- 获取当前状态
      local bucket_data = redis.call('HMGET', bucket_key, 'tokens', 'last_refill')
      local current_tokens = tonumber(bucket_data[1]) or capacity
      local last_refill = tonumber(bucket_data[2]) or now
      
      -- 计算补充的令牌数
      local time_passed = (now - last_refill) / 1000
      local tokens_to_add = math.floor(time_passed * refill_rate)
      current_tokens = math.min(capacity, current_tokens + tokens_to_add)
      
      -- 检查是否有足够令牌
      if current_tokens >= tokens_requested then
        current_tokens = current_tokens - tokens_requested
        
        -- 更新状态
        redis.call('HMSET', bucket_key, 
          'tokens', current_tokens, 
          'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600)
        
        return {1, current_tokens, capacity}
      else
        -- 更新最后补充时间但不消费令牌
        redis.call('HMSET', bucket_key, 
          'tokens', current_tokens, 
          'last_refill', now)
        redis.call('EXPIRE', bucket_key, 3600)
        
        return {0, current_tokens, capacity}
      end
    `;
    
    const result = await this.redis.eval(
      script, 
      1, 
      [bucketKey], 
      [this.capacity, this.refillRate, tokens, now]
    );
    
    return {
      allowed: result[0] === 1,
      remaining: result[1],
      capacity: result[2],
      resetTime: now + ((this.capacity - result[1]) / this.refillRate) * 1000
    };
  }
}

/**
 * 滑动窗口限流算法
 */
class SlidingWindowRateLimit {
  constructor(windowSize, maxRequests, redisClient) {
    this.windowSize = windowSize * 1000; // 转换为毫秒
    this.maxRequests = maxRequests;
    this.redis = redisClient;
  }

  /**
   * 检查限流
   * @param {string} key 限流键
   * @returns {Object} 检查结果
   */
  async checkLimit(key) {
    const now = Date.now();
    const windowStart = now - this.windowSize;
    const limitKey = `sliding:${key}`;
    
    const script = `
      local key = KEYS[1]
      local window_start = tonumber(ARGV[1])
      local now = tonumber(ARGV[2])
      local max_requests = tonumber(ARGV[3])
      local window_size = tonumber(ARGV[4])
      
      -- 清理过期记录
      redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
      
      -- 获取当前窗口内的请求数
      local current_requests = redis.call('ZCARD', key)
      
      if current_requests < max_requests then
        -- 添加当前请求
        redis.call('ZADD', key, now, now .. ':' .. math.random())
        redis.call('EXPIRE', key, math.ceil(window_size / 1000))
        return {1, max_requests - current_requests - 1, max_requests}
      else
        return {0, 0, max_requests}
      end
    `;
    
    const result = await this.redis.eval(
      script,
      1,
      [limitKey],
      [windowStart, now, this.maxRequests, this.windowSize]
    );
    
    return {
      allowed: result[0] === 1,
      remaining: result[1],
      limit: result[2],
      resetTime: now + this.windowSize
    };
  }
}

/**
 * 分布式限流器
 */
class DistributedRateLimiter {
  constructor(redisClient) {
    this.redis = redisClient;
    this.buckets = new Map();
    this.windows = new Map();
  }

  /**
   * 获取令牌桶限流器
   */
  getTokenBucket(capacity, refillRate) {
    const key = `tb_${capacity}_${refillRate}`;
    if (!this.buckets.has(key)) {
      this.buckets.set(key, new TokenBucket(capacity, refillRate, this.redis));
    }
    return this.buckets.get(key);
  }

  /**
   * 获取滑动窗口限流器
   */
  getSlidingWindow(windowSize, maxRequests) {
    const key = `sw_${windowSize}_${maxRequests}`;
    if (!this.windows.has(key)) {
      this.windows.set(key, new SlidingWindowRateLimit(windowSize, maxRequests, this.redis));
    }
    return this.windows.get(key);
  }
}

// 全局限流器实例
const rateLimiter = new DistributedRateLimiter(redis);

/**
 * 限流中间件工厂
 * @param {string} limitType 限流类型
 * @param {Object} options 配置选项
 */
const rateLimitMiddleware = (limitType = 'user', options = {}) => {
  return async (event, context) => {
    try {
      let limitKey;
      let limiter;
      let limitConfig;
      
      // 根据限流类型生成key和配置
      switch (limitType) {
        case 'global':
          limitKey = 'global';
          limitConfig = options.config || config.rateLimit.global;
          limiter = rateLimiter.getTokenBucket(
            limitConfig.capacity,
            limitConfig.refillRate
          );
          break;
          
        case 'user':
          if (!event.user) {
            // 如果没有用户信息，使用IP限流
            return await rateLimitMiddleware('ip', options)(event, context);
          }
          limitKey = `user:${event.user.id}`;
          limitConfig = options.config || config.rateLimit.user;
          limiter = rateLimiter.getTokenBucket(
            limitConfig.capacity,
            limitConfig.refillRate
          );
          break;
          
        case 'api':
          const apiPath = event.path || event.requestContext?.path;
          const normalizedPath = normalizeApiPath(apiPath);
          limitConfig = options.config || config.rateLimit.api[normalizedPath];
          
          if (!limitConfig) {
            return { allowed: true }; // 没有配置限流则允许
          }
          
          limitKey = `api:${normalizedPath}:${event.user?.id || getClientIP(event)}`;
          limiter = rateLimiter.getTokenBucket(
            limitConfig.capacity,
            limitConfig.refillRate
          );
          break;
          
        case 'ip':
          const clientIP = getClientIP(event);
          limitKey = `ip:${clientIP}`;
          limitConfig = options.config || config.rateLimit.ip;
          limiter = rateLimiter.getSlidingWindow(
            limitConfig.windowSize,
            limitConfig.maxRequests
          );
          break;
          
        case 'device':
          const deviceId = event.headers['x-device-id'] || event.headers['X-Device-Id'];
          if (!deviceId) {
            throw new RateLimitError('设备ID不能为空');
          }
          limitKey = `device:${deviceId}`;
          limitConfig = options.config || config.rateLimit.user;
          limiter = rateLimiter.getTokenBucket(
            limitConfig.capacity,
            limitConfig.refillRate
          );
          break;
          
        default:
          return { allowed: true };
      }
      
      // 执行限流检查
      const result = limiter.consume ? 
        await limiter.consume(limitKey, options.tokens || 1) :
        await limiter.checkLimit(limitKey);
      
      if (!result.allowed) {
        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
        
        throw new RateLimitError(
          `请求频率过高，请 ${retryAfter} 秒后重试`,
          retryAfter
        );
      }
      
      // 添加限流信息到事件对象
      event.rateLimitInfo = {
        type: limitType,
        remaining: result.remaining,
        limit: result.capacity || result.limit,
        resetTime: result.resetTime
      };
      
      return { allowed: true };
      
    } catch (error) {
      if (error instanceof RateLimitError) {
        throw error;
      }
      
      console.error('Rate limiting error:', error);
      // 限流失败时允许请求通过，避免影响正常业务
      return { allowed: true };
    }
  };
};

/**
 * 组合限流中间件
 * @param {Array} limitTypes 限流类型数组
 */
const combineRateLimit = (limitTypes) => {
  return async (event, context) => {
    for (const { type, options } of limitTypes) {
      const result = await rateLimitMiddleware(type, options)(event, context);
      
      if (!result.allowed) {
        return result;
      }
    }
    
    return { allowed: true };
  };
};

/**
 * 动态限流中间件
 * 根据系统负载动态调整限流策略
 */
const dynamicRateLimit = (baseConfig, loadThresholds = []) => {
  return async (event, context) => {
    try {
      // 获取系统负载指标
      const systemLoad = await getSystemLoad();
      
      // 根据负载调整限流配置
      let adjustedConfig = { ...baseConfig };
      
      for (const threshold of loadThresholds) {
        if (systemLoad >= threshold.load) {
          adjustedConfig = {
            ...adjustedConfig,
            capacity: Math.floor(adjustedConfig.capacity * threshold.factor),
            refillRate: adjustedConfig.refillRate * threshold.factor
          };
        }
      }
      
      return await rateLimitMiddleware('user', { config: adjustedConfig })(event, context);
      
    } catch (error) {
      console.error('Dynamic rate limit error:', error);
      return await rateLimitMiddleware('user', { config: baseConfig })(event, context);
    }
  };
};

/**
 * 白名单限流中间件
 * 为特定用户或IP提供更高的限流阈值
 */
const whitelistRateLimit = (whitelist = [], whitelistConfig = {}) => {
  return async (event, context) => {
    const userId = event.user?.id;
    const clientIP = getClientIP(event);
    
    // 检查是否在白名单中
    const isWhitelisted = whitelist.some(item => {
      if (typeof item === 'object') {
        return (item.userId && item.userId === userId) || 
               (item.ip && item.ip === clientIP);
      }
      return item === userId || item === clientIP;
    });
    
    if (isWhitelisted) {
      return await rateLimitMiddleware('user', { config: whitelistConfig })(event, context);
    }
    
    return await rateLimitMiddleware('user')(event, context);
  };
};

/**
 * 获取客户端IP地址
 */
function getClientIP(event) {
  return event.headers['x-forwarded-for'] ||
         event.headers['X-Forwarded-For'] ||
         event.requestContext?.identity?.sourceIp ||
         event.sourceIP ||
         '127.0.0.1';
}

/**
 * 标准化API路径
 */
function normalizeApiPath(path) {
  if (!path) return '/';
  
  // 移除查询参数
  const cleanPath = path.split('?')[0];
  
  // 替换路径参数为通配符
  return cleanPath.replace(/\/\d+/g, '/{id}')
                  .replace(/\/[a-fA-F0-9-]{36}/g, '/{uuid}')
                  .replace(/\/[a-fA-F0-9]{32}/g, '/{hash}');
}

/**
 * 获取系统负载（模拟）
 */
async function getSystemLoad() {
  try {
    // 这里可以集成真实的系统监控指标
    // 例如CPU使用率、内存使用率、请求队列长度等
    return Math.random(); // 模拟0-1之间的负载值
  } catch (error) {
    console.error('Failed to get system load:', error);
    return 0.5; // 默认中等负载
  }
}

/**
 * 限流统计和监控
 */
const rateLimitMonitor = {
  /**
   * 记录限流事件
   */
  recordEvent: async (event, limitType, allowed, remaining) => {
    try {
      const eventData = {
        timestamp: new Date().toISOString(),
        limitType,
        allowed,
        remaining,
        userId: event.user?.id,
        ip: getClientIP(event),
        path: event.path,
        userAgent: event.headers['user-agent']
      };
      
      // 这里可以发送到监控系统
      console.log('Rate limit event:', JSON.stringify(eventData));
      
    } catch (error) {
      console.error('Failed to record rate limit event:', error);
    }
  },
  
  /**
   * 获取限流统计
   */
  getStats: async (limitType, timeRange = '1h') => {
    try {
      // 这里可以从监控系统获取统计数据
      return {
        totalRequests: 1000,
        blockedRequests: 50,
        blockRate: 0.05,
        topUsers: [],
        topIPs: []
      };
    } catch (error) {
      console.error('Failed to get rate limit stats:', error);
      return null;
    }
  }
};

module.exports = {
  rateLimitMiddleware,
  combineRateLimit,
  dynamicRateLimit,
  whitelistRateLimit,
  TokenBucket,
  SlidingWindowRateLimit,
  DistributedRateLimiter,
  rateLimitMonitor,
  getClientIP,
  normalizeApiPath
};