/**
 * 游戏结果数据模型
 * 对应 game_records 表
 */

const { DatabaseManager } = require('../utils/database');

class GameResult {
  constructor(data = {}) {
    this.id = data.id || null;
    this.userId = data.user_id || data.userId || null;
    this.gameSessionId = data.game_session_id || data.gameSessionId || null;
    this.questionId = data.question_id || data.questionId || null;
    this.userAnswer = data.user_answer || data.userAnswer || null;
    this.isCorrect = data.is_correct || data.isCorrect || 0;
    this.answerTime = data.answer_time || data.answerTime || 0;
    this.scoreEarned = data.score_earned || data.scoreEarned || 0;
    this.streakCount = data.streak_count || data.streakCount || 0;
    this.hintUsed = data.hint_used || data.hintUsed || 0;
    this.createdAt = data.created_at || data.createdAt || null;
  }

  /**
   * 记录游戏结果
   */
  static async create(data) {
    const db = DatabaseManager.getInstance();
    
    const result = new GameResult(data);
    
    const [insertResult] = await db.query(
      `INSERT INTO game_records 
       (user_id, game_session_id, question_id, user_answer, is_correct,
        answer_time, score_earned, streak_count, hint_used)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        result.userId, result.gameSessionId, result.questionId,
        result.userAnswer, result.isCorrect ? 1 : 0, result.answerTime,
        result.scoreEarned, result.streakCount, result.hintUsed ? 1 : 0
      ]
    );

    result.id = insertResult.insertId;
    return result;
  }

  /**
   * 根据游戏会话ID获取所有结果
   */
  static async findBySessionId(sessionId) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      `SELECT gr.*, dq.question_text, dq.standard_answer, dq.explanation
       FROM game_records gr
       LEFT JOIN dialect_questions dq ON gr.question_id = dq.id
       WHERE gr.game_session_id = ?
       ORDER BY gr.created_at ASC`,
      [sessionId]
    );
    
    return rows.map(row => ({
      ...new GameResult(row),
      question: {
        id: row.question_id,
        text: row.question_text,
        correctAnswer: row.standard_answer,
        explanation: row.explanation
      }
    }));
  }

  /**
   * 根据用户ID获取游戏历史
   */
  static async findByUserId(userId, limit = 100) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      `SELECT gr.*, dq.question_text, dq.standard_answer, dq.category, dq.difficulty_level
       FROM game_records gr
       LEFT JOIN dialect_questions dq ON gr.question_id = dq.id
       WHERE gr.user_id = ?
       ORDER BY gr.created_at DESC
       LIMIT ?`,
      [userId, limit]
    );
    
    return rows.map(row => new GameResult(row));
  }

  /**
   * 获取用户答题统计
   */
  static async getUserAnswerStats(userId, days = 30) {
    const db = DatabaseManager.getInstance();
    
    const [stats] = await db.query(
      `SELECT 
         COUNT(*) as totalAnswers,
         COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correctAnswers,
         AVG(CASE WHEN is_correct = 1 THEN 1.0 ELSE 0.0 END) * 100 as accuracy,
         AVG(answer_time) as avgAnswerTime,
         SUM(score_earned) as totalScore,
         MAX(streak_count) as maxStreak,
         COUNT(CASE WHEN hint_used = 1 THEN 1 END) as hintsUsed
       FROM game_records 
       WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)`,
      [userId, days]
    );

    return stats[0] || {};
  }

  /**
   * 获取题目答题统计
   */
  static async getQuestionStats(questionId) {
    const db = DatabaseManager.getInstance();
    
    const [stats] = await db.query(
      `SELECT 
         COUNT(*) as totalAttempts,
         COUNT(CASE WHEN is_correct = 1 THEN 1 END) as correctAttempts,
         AVG(CASE WHEN is_correct = 1 THEN 1.0 ELSE 0.0 END) * 100 as correctRate,
         AVG(answer_time) as avgAnswerTime,
         AVG(score_earned) as avgScore
       FROM game_records 
       WHERE question_id = ?`,
      [questionId]
    );

    return stats[0] || {};
  }

  /**
   * 获取用户按类别的答题统计
   */
  static async getUserCategoryStats(userId) {
    const db = DatabaseManager.getInstance();
    
    const [stats] = await db.query(
      `SELECT 
         dq.category,
         COUNT(*) as totalAnswers,
         COUNT(CASE WHEN gr.is_correct = 1 THEN 1 END) as correctAnswers,
         AVG(CASE WHEN gr.is_correct = 1 THEN 1.0 ELSE 0.0 END) * 100 as accuracy,
         AVG(gr.answer_time) as avgAnswerTime,
         SUM(gr.score_earned) as totalScore
       FROM game_records gr
       LEFT JOIN dialect_questions dq ON gr.question_id = dq.id
       WHERE gr.user_id = ?
       GROUP BY dq.category
       ORDER BY totalAnswers DESC`,
      [userId]
    );

    return stats || [];
  }

  /**
   * 获取用户按难度的答题统计
   */
  static async getUserDifficultyStats(userId) {
    const db = DatabaseManager.getInstance();
    
    const [stats] = await db.query(
      `SELECT 
         dq.difficulty_level as difficulty,
         COUNT(*) as totalAnswers,
         COUNT(CASE WHEN gr.is_correct = 1 THEN 1 END) as correctAnswers,
         AVG(CASE WHEN gr.is_correct = 1 THEN 1.0 ELSE 0.0 END) * 100 as accuracy,
         AVG(gr.answer_time) as avgAnswerTime,
         SUM(gr.score_earned) as totalScore
       FROM game_records gr
       LEFT JOIN dialect_questions dq ON gr.question_id = dq.id
       WHERE gr.user_id = ?
       GROUP BY dq.difficulty_level
       ORDER BY dq.difficulty_level ASC`,
      [userId]
    );

    return stats || [];
  }

  /**
   * 获取用户最近错误题目
   */
  static async getUserRecentErrors(userId, limit = 10) {
    const db = DatabaseManager.getInstance();
    
    const [errors] = await db.query(
      `SELECT 
         gr.*,
         dq.question_text,
         dq.standard_answer,
         dq.explanation,
         dq.category,
         dq.difficulty_level
       FROM game_records gr
       LEFT JOIN dialect_questions dq ON gr.question_id = dq.id
       WHERE gr.user_id = ? AND gr.is_correct = 0
       ORDER BY gr.created_at DESC
       LIMIT ?`,
      [userId, limit]
    );

    return errors.map(row => ({
      ...new GameResult(row),
      question: {
        id: row.question_id,
        text: row.question_text,
        correctAnswer: row.standard_answer,
        explanation: row.explanation,
        category: row.category,
        difficulty: row.difficulty_level
      }
    }));
  }

  /**
   * 获取全局答题统计（排行榜用）
   */
  static async getGlobalStats(timeRange = '7d') {
    const db = DatabaseManager.getInstance();
    
    let dateCondition = '';
    switch (timeRange) {
      case '1d':
        dateCondition = 'AND gr.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)';
        break;
      case '7d':
        dateCondition = 'AND gr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateCondition = 'AND gr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        dateCondition = '';
    }

    const [stats] = await db.query(
      `SELECT 
         u.id as userId,
         u.nickname,
         u.avatar_url,
         COUNT(*) as totalAnswers,
         COUNT(CASE WHEN gr.is_correct = 1 THEN 1 END) as correctAnswers,
         AVG(CASE WHEN gr.is_correct = 1 THEN 1.0 ELSE 0.0 END) * 100 as accuracy,
         SUM(gr.score_earned) as totalScore,
         MAX(gr.streak_count) as maxStreak
       FROM game_records gr
       LEFT JOIN users u ON gr.user_id = u.id
       WHERE 1=1 ${dateCondition}
       GROUP BY gr.user_id
       HAVING totalAnswers >= 10
       ORDER BY totalScore DESC, accuracy DESC
       LIMIT 100`,
      []
    );

    return stats || [];
  }

  /**
   * 转换为API响应格式
   */
  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      gameSessionId: this.gameSessionId,
      questionId: this.questionId,
      userAnswer: this.userAnswer,
      isCorrect: this.isCorrect === 1,
      answerTime: this.answerTime,
      scoreEarned: this.scoreEarned,
      streakCount: this.streakCount,
      hintUsed: this.hintUsed === 1,
      createdAt: this.createdAt
    };
  }
}

module.exports = GameResult;