/**
 * 游戏会话数据模型
 * 对应 game_sessions 表
 */

const { DatabaseManager } = require('../utils/database');
const { v4: uuidv4 } = require('uuid');

class GameSession {
  constructor(data = {}) {
    this.id = data.id || null;
    this.sessionId = data.session_id || data.sessionId || null;
    this.userId = data.user_id || data.userId || null;
    this.category = data.category || '';
    this.difficulty = data.difficulty || 1;
    this.questionCount = data.question_count || data.questionCount || 10;
    this.currentQuestion = data.current_question || data.currentQuestion || 0;
    this.correctCount = data.correct_count || data.correctCount || 0;
    this.totalScore = data.total_score || data.totalScore || 0;
    this.totalTime = data.total_time || data.totalTime || 0;
    this.gameMode = data.game_mode || data.gameMode || 'standard';
    this.status = data.status || 1; // 1: 进行中, 2: 已完成, 3: 已取消
    this.startedAt = data.started_at || data.startedAt || null;
    this.finishedAt = data.finished_at || data.finishedAt || null;
    this.expiresAt = data.expires_at || data.expiresAt || null;
    this.createdAt = data.created_at || data.createdAt || null;
    this.updatedAt = data.updated_at || data.updatedAt || null;
    
    // 运行时数据
    this.questions = data.questions || [];
    this.currentQuestionIndex = data.currentQuestionIndex || 0;
    this.streakCount = data.streakCount || 0;
  }

  /**
   * 创建新的游戏会话
   */
  static async create(userId, options = {}) {
    const db = DatabaseManager.getInstance();
    
    const sessionId = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2小时过期
    
    const gameSession = new GameSession({
      sessionId,
      userId,
      category: options.category || 'general',
      difficulty: options.difficulty || 1,
      questionCount: options.questionCount || 10,
      gameMode: options.gameMode || 'standard',
      startedAt: now,
      expiresAt
    });

    const [result] = await db.query(
      `INSERT INTO game_sessions 
       (session_id, user_id, category, difficulty, question_count, 
        game_mode, status, started_at, expires_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        gameSession.sessionId, gameSession.userId, gameSession.category,
        gameSession.difficulty, gameSession.questionCount, gameSession.gameMode,
        gameSession.status, gameSession.startedAt, gameSession.expiresAt
      ]
    );

    gameSession.id = result.insertId;
    return gameSession;
  }

  /**
   * 根据会话ID查找
   */
  static async findBySessionId(sessionId) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      'SELECT * FROM game_sessions WHERE session_id = ?',
      [sessionId]
    );
    
    return rows.length > 0 ? new GameSession(rows[0]) : null;
  }

  /**
   * 根据用户ID查找活跃会话
   */
  static async findActiveByUserId(userId) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      `SELECT * FROM game_sessions 
       WHERE user_id = ? AND status = 1 AND expires_at > NOW()
       ORDER BY created_at DESC
       LIMIT 1`,
      [userId]
    );
    
    return rows.length > 0 ? new GameSession(rows[0]) : null;
  }

  /**
   * 根据用户ID获取历史会话
   */
  static async findHistoryByUserId(userId, limit = 20) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      `SELECT * FROM game_sessions 
       WHERE user_id = ? AND status IN (2, 3)
       ORDER BY finished_at DESC
       LIMIT ?`,
      [userId, limit]
    );
    
    return rows.map(row => new GameSession(row));
  }

  /**
   * 检查会话是否有效
   */
  isValid() {
    if (this.status !== 1) return false;
    if (new Date() > new Date(this.expiresAt)) return false;
    return true;
  }

  /**
   * 检查游戏是否完成
   */
  isCompleted() {
    return this.currentQuestion >= this.questionCount;
  }

  /**
   * 更新游戏进度
   */
  async updateProgress(isCorrect, score, answerTime) {
    const db = DatabaseManager.getInstance();
    
    this.currentQuestion += 1;
    if (isCorrect) {
      this.correctCount += 1;
      this.streakCount += 1;
    } else {
      this.streakCount = 0;
    }
    
    this.totalScore += score;
    this.totalTime += answerTime;

    // 检查是否完成
    if (this.isCompleted()) {
      this.status = 2; // 已完成
      this.finishedAt = new Date();
    }

    await db.query(
      `UPDATE game_sessions 
       SET current_question = ?, correct_count = ?, total_score = ?, 
           total_time = ?, status = ?, finished_at = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        this.currentQuestion, this.correctCount, this.totalScore,
        this.totalTime, this.status, this.finishedAt, this.id
      ]
    );

    return this.isCompleted();
  }

  /**
   * 取消游戏会话
   */
  async cancel() {
    const db = DatabaseManager.getInstance();
    
    this.status = 3; // 已取消
    this.finishedAt = new Date();

    await db.query(
      `UPDATE game_sessions 
       SET status = ?, finished_at = ?, updated_at = NOW()
       WHERE id = ?`,
      [this.status, this.finishedAt, this.id]
    );
  }

  /**
   * 获取游戏统计
   */
  getStats() {
    const accuracy = this.currentQuestion > 0 ? 
      (this.correctCount / this.currentQuestion * 100).toFixed(2) : 0;
    
    const avgTimePerQuestion = this.currentQuestion > 0 ? 
      (this.totalTime / this.currentQuestion).toFixed(2) : 0;

    return {
      totalQuestions: this.questionCount,
      answeredQuestions: this.currentQuestion,
      correctAnswers: this.correctCount,
      accuracy: parseFloat(accuracy),
      totalScore: this.totalScore,
      totalTime: this.totalTime,
      avgTimePerQuestion: parseFloat(avgTimePerQuestion),
      streakCount: this.streakCount
    };
  }

  /**
   * 转换为API响应格式
   */
  toJSON(includeStats = true) {
    const result = {
      sessionId: this.sessionId,
      userId: this.userId,
      category: this.category,
      difficulty: this.difficulty,
      gameMode: this.gameMode,
      status: this.status,
      startedAt: this.startedAt,
      finishedAt: this.finishedAt,
      expiresAt: this.expiresAt
    };

    if (includeStats) {
      result.stats = this.getStats();
    }

    // 添加状态描述
    const statusMap = {
      1: 'playing',
      2: 'completed',
      3: 'cancelled'
    };
    result.statusText = statusMap[this.status] || 'unknown';

    return result;
  }

  /**
   * 清理过期会话
   */
  static async cleanupExpiredSessions() {
    const db = DatabaseManager.getInstance();
    
    // 将过期的进行中会话设为已取消
    const [result] = await db.query(
      `UPDATE game_sessions 
       SET status = 3, finished_at = NOW(), updated_at = NOW()
       WHERE status = 1 AND expires_at <= NOW()`
    );

    return result.affectedRows;
  }

  /**
   * 获取用户游戏统计
   */
  static async getUserGameStats(userId) {
    const db = DatabaseManager.getInstance();
    
    const [stats] = await db.query(
      `SELECT 
         COUNT(*) as totalGames,
         COUNT(CASE WHEN status = 2 THEN 1 END) as completedGames,
         COUNT(CASE WHEN status = 3 THEN 1 END) as cancelledGames,
         AVG(CASE WHEN status = 2 THEN total_score END) as avgScore,
         MAX(total_score) as bestScore,
         AVG(CASE WHEN status = 2 THEN (correct_count / question_count * 100) END) as avgAccuracy,
         AVG(CASE WHEN status = 2 THEN total_time END) as avgTotalTime
       FROM game_sessions 
       WHERE user_id = ?`,
      [userId]
    );

    return stats[0] || {};
  }
}

module.exports = GameSession;