/**
 * 用户模型
 * 提供用户相关的数据访问方法
 */

const db = require('../utils/database');

class User {
  constructor(data = {}) {
    this.id = data.id;
    this.openid = data.openid;
    this.unionid = data.unionid;
    this.nickname = data.nickname;
    this.avatar_url = data.avatar_url;
    this.gender = data.gender;
    this.province = data.province;
    this.city = data.city;
    this.country = data.country;
    this.language = data.language;
    this.total_score = data.total_score || 0;
    this.total_games = data.total_games || 0;
    this.win_games = data.win_games || 0;
    this.max_streak = data.max_streak || 0;
    this.current_level = data.current_level || 1;
    this.status = data.status || 1;
    this.last_login_at = data.last_login_at;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  /**
   * 根据openid查找用户
   * @param {string} openid 微信openid
   * @returns {Promise<User|null>}
   */
  static async findByOpenId(openid) {
    const row = await db.findOne('users', { openid, status: 1 });
    return row ? new User(row) : null;
  }

  /**
   * 根据用户ID查找用户
   * @param {number} id 用户ID
   * @returns {Promise<User|null>}
   */
  static async findById(id) {
    const row = await db.findOne('users', { id, status: 1 });
    return row ? new User(row) : null;
  }

  /**
   * 根据unionid查找用户
   * @param {string} unionid 微信unionid
   * @returns {Promise<User|null>}
   */
  static async findByUnionId(unionid) {
    if (!unionid) return null;
    const row = await db.findOne('users', { unionid, status: 1 });
    return row ? new User(row) : null;
  }

  /**
   * 创建新用户
   * @param {Object} userData 用户数据
   * @returns {Promise<User>}
   */
  static async create(userData) {
    const now = new Date();
    const data = {
      openid: userData.openid,
      unionid: userData.unionid || null,
      nickname: userData.nickname || '',
      avatar_url: userData.avatar_url || null,
      gender: userData.gender || 0,
      province: userData.province || null,
      city: userData.city || null,
      country: userData.country || null,
      language: userData.language || 'zh_CN',
      last_login_at: now,
      created_at: now,
      updated_at: now
    };

    const result = await db.insert('users', data);
    const user = await User.findById(result.insertId);
    
    return user;
  }

  /**
   * 更新用户信息
   * @param {Object} updates 更新数据
   * @returns {Promise<boolean>}
   */
  async update(updates) {
    const allowedFields = [
      'nickname', 'avatar_url', 'gender', 'province', 
      'city', 'country', 'language', 'last_login_at'
    ];
    
    const updateData = {};
    allowedFields.forEach(field => {
      if (field in updates) {
        updateData[field] = updates[field];
      }
    });

    if (Object.keys(updateData).length === 0) {
      return false;
    }

    updateData.updated_at = new Date();
    
    const result = await db.update('users', updateData, { id: this.id });
    
    if (result.affectedRows > 0) {
      // 更新当前对象的属性
      Object.assign(this, updateData);
      return true;
    }
    
    return false;
  }

  /**
   * 更新用户积分和统计
   * @param {number} scoreChange 积分变化
   * @param {boolean} isWin 是否胜利
   * @returns {Promise<boolean>}
   */
  async updateGameStats(scoreChange, isWin = false) {
    const updates = {
      total_score: this.total_score + scoreChange,
      total_games: this.total_games + 1,
      updated_at: new Date()
    };

    if (isWin) {
      updates.win_games = this.win_games + 1;
    }

    const result = await db.update('users', updates, { id: this.id });
    
    if (result.affectedRows > 0) {
      Object.assign(this, updates);
      return true;
    }
    
    return false;
  }

  /**
   * 更新最后登录时间
   * @returns {Promise<boolean>}
   */
  async updateLastLogin() {
    const result = await db.update(
      'users', 
      { last_login_at: new Date(), updated_at: new Date() },
      { id: this.id }
    );
    
    return result.affectedRows > 0;
  }

  /**
   * 获取用户游戏统计
   * @param {string} category 方言类别(可选)
   * @returns {Promise<Array>}
   */
  async getGameStats(category = null) {
    const where = { user_id: this.id };
    if (category) {
      where.dialect_category = category;
    }

    return await db.find('user_game_stats', {
      where,
      orderBy: 'accuracy_rate DESC'
    });
  }

  /**
   * 获取用户游戏历史
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 分页结果
   */
  async getGameHistory(options = {}) {
    const {
      category = null,
      page = 1,
      size = 20
    } = options;

    const sql = `
      SELECT 
        gr.id,
        gr.game_session_id,
        gr.is_correct,
        gr.score_earned,
        gr.answer_time,
        gr.streak_count,
        gr.hint_used,
        gr.created_at,
        dq.category,
        dq.region,
        dq.question_text,
        dq.difficulty_level
      FROM game_records gr
      JOIN dialect_questions dq ON gr.question_id = dq.id
      WHERE gr.user_id = ? ${category ? 'AND dq.category = ?' : ''}
      ORDER BY gr.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const params = [this.id];
    if (category) params.push(category);
    params.push(size, (page - 1) * size);

    const items = await db.query(sql, params);

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM game_records gr
      JOIN dialect_questions dq ON gr.question_id = dq.id
      WHERE gr.user_id = ? ${category ? 'AND dq.category = ?' : ''}
    `;
    
    const countParams = [this.id];
    if (category) countParams.push(category);
    
    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;

    return {
      items,
      pagination: {
        page,
        size,
        total,
        totalPages: Math.ceil(total / size),
        hasNext: page * size < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * 获取用户排名
   * @param {string} type 排行榜类型
   * @param {string} category 方言类别
   * @returns {Promise<Object|null>}
   */
  async getRanking(type = 'overall', category = null) {
    let sql = `
      SELECT rank, score
      FROM leaderboards
      WHERE type = ? AND user_id = ?
    `;
    const params = [type, this.id];

    if (category) {
      sql += ' AND category = ?';
      params.push(category);
    } else {
      sql += ' AND category IS NULL';
    }

    sql += ' ORDER BY created_at DESC LIMIT 1';

    const rows = await db.query(sql, params);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 获取好友列表
   * @returns {Promise<Array>}
   */
  async getFriends() {
    const sql = `
      SELECT u.id, u.nickname, u.avatar_url, u.total_score, u.current_level
      FROM users u
      JOIN user_relationships ur ON u.id = ur.friend_id
      WHERE ur.user_id = ? AND ur.relationship_type = 1 AND u.status = 1
      ORDER BY u.total_score DESC
    `;

    return await db.query(sql, [this.id]);
  }

  /**
   * 添加好友
   * @param {number} friendId 好友ID
   * @returns {Promise<boolean>}
   */
  async addFriend(friendId) {
    if (friendId === this.id) {
      throw new Error('Cannot add yourself as friend');
    }

    // 检查是否已经是好友
    const exists = await db.exists('user_relationships', {
      user_id: this.id,
      friend_id: friendId
    });

    if (exists) {
      return false;
    }

    // 双向添加好友关系
    await db.transaction(async (tx) => {
      await tx.query(
        'INSERT INTO user_relationships (user_id, friend_id, relationship_type) VALUES (?, ?, 1)',
        [this.id, friendId]
      );
      await tx.query(
        'INSERT INTO user_relationships (user_id, friend_id, relationship_type) VALUES (?, ?, 1)',
        [friendId, this.id]
      );
    });

    return true;
  }

  /**
   * 删除好友
   * @param {number} friendId 好友ID
   * @returns {Promise<boolean>}
   */
  async removeFriend(friendId) {
    const result = await db.transaction(async (tx) => {
      const result1 = await tx.query(
        'DELETE FROM user_relationships WHERE user_id = ? AND friend_id = ?',
        [this.id, friendId]
      );
      const result2 = await tx.query(
        'DELETE FROM user_relationships WHERE user_id = ? AND friend_id = ?',
        [friendId, this.id]
      );
      
      return result1.affectedRows + result2.affectedRows;
    });

    return result > 0;
  }

  /**
   * 检查是否为好友
   * @param {number} friendId 好友ID
   * @returns {Promise<boolean>}
   */
  async isFriend(friendId) {
    return await db.exists('user_relationships', {
      user_id: this.id,
      friend_id: friendId,
      relationship_type: 1
    });
  }

  /**
   * 转换为API响应格式
   * @param {boolean} includePrivate 是否包含私有信息
   * @returns {Object}
   */
  toJSON(includePrivate = false) {
    const publicData = {
      id: this.id,
      nickname: this.nickname,
      avatar_url: this.avatar_url,
      total_score: this.total_score,
      current_level: this.current_level,
      created_at: this.created_at
    };

    if (includePrivate) {
      return {
        ...publicData,
        gender: this.gender,
        province: this.province,
        city: this.city,
        country: this.country,
        language: this.language,
        total_games: this.total_games,
        win_games: this.win_games,
        max_streak: this.max_streak,
        last_login_at: this.last_login_at,
        updated_at: this.updated_at
      };
    }

    return publicData;
  }

  /**
   * 获取用户等级信息
   * @returns {Object}
   */
  getLevelInfo() {
    const levels = [
      { level: 1, name: '方言新手', minScore: 0 },
      { level: 2, name: '方言学徒', minScore: 1000 },
      { level: 3, name: '方言达人', minScore: 5000 },
      { level: 4, name: '方言专家', minScore: 15000 },
      { level: 5, name: '方言大师', minScore: 50000 }
    ];

    const currentLevel = levels.reverse().find(l => this.total_score >= l.minScore);
    const nextLevel = levels.find(l => l.level > currentLevel.level);

    return {
      current: currentLevel,
      next: nextLevel,
      progress: nextLevel ? 
        (this.total_score - currentLevel.minScore) / (nextLevel.minScore - currentLevel.minScore) : 1
    };
  }

  /**
   * 批量查找用户
   * @param {Array} ids 用户ID数组
   * @returns {Promise<Array>}
   */
  static async findByIds(ids) {
    if (!ids || ids.length === 0) return [];
    
    const placeholders = ids.map(() => '?').join(',');
    const sql = `SELECT * FROM users WHERE id IN (${placeholders}) AND status = 1`;
    
    const rows = await db.query(sql, ids);
    return rows.map(row => new User(row));
  }

  /**
   * 搜索用户
   * @param {string} keyword 关键词
   * @param {Object} options 搜索选项
   * @returns {Promise<Object>}
   */
  static async search(keyword, options = {}) {
    const { page = 1, size = 20 } = options;
    
    const sql = `
      SELECT id, nickname, avatar_url, total_score, current_level
      FROM users
      WHERE status = 1 AND nickname LIKE ?
      ORDER BY total_score DESC
      LIMIT ? OFFSET ?
    `;
    
    const searchTerm = `%${keyword}%`;
    const items = await db.query(sql, [searchTerm, size, (page - 1) * size]);
    
    // 获取总数
    const countSql = 'SELECT COUNT(*) as total FROM users WHERE status = 1 AND nickname LIKE ?';
    const countResult = await db.query(countSql, [searchTerm]);
    const total = countResult[0].total;
    
    return {
      items: items.map(item => new User(item)),
      pagination: {
        page,
        size,
        total,
        totalPages: Math.ceil(total / size),
        hasNext: page * size < total,
        hasPrev: page > 1
      }
    };
  }
}

module.exports = User;