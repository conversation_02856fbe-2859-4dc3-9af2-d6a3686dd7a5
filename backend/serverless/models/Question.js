/**
 * 题目数据模型
 * 对应 dialect_questions 表
 */

const { DatabaseManager } = require('../utils/database');

class Question {
  constructor(data = {}) {
    this.id = data.id || null;
    this.category = data.category || '';
    this.region = data.region || '';
    this.questionText = data.question_text || data.questionText || '';
    this.questionType = data.question_type || data.questionType || 1;
    this.audioUrl = data.audio_url || data.audioUrl || null;
    this.audioDuration = data.audio_duration || data.audioDuration || 0;
    this.difficultyLevel = data.difficulty_level || data.difficultyLevel || 1;
    this.standardAnswer = data.standard_answer || data.standardAnswer || '';
    this.answerOptions = data.answer_options || data.answerOptions || null;
    this.explanation = data.explanation || null;
    this.usageCount = data.usage_count || data.usageCount || 0;
    this.correctRate = data.correct_rate || data.correctRate || 0.0000;
    this.avgAnswerTime = data.avg_answer_time || data.avgAnswerTime || 0.0000;
    this.status = data.status || 1;
    this.createdBy = data.created_by || data.createdBy || null;
    this.createdAt = data.created_at || data.createdAt || null;
    this.updatedAt = data.updated_at || data.updatedAt || null;
  }

  /**
   * 根据ID查找题目
   */
  static async findById(id) {
    const db = DatabaseManager.getInstance();
    const [rows] = await db.query(
      'SELECT * FROM dialect_questions WHERE id = ? AND status = 1',
      [id]
    );
    
    return rows.length > 0 ? new Question(rows[0]) : null;
  }

  /**
   * 根据条件查找题目列表
   */
  static async findByConditions(conditions = {}) {
    const db = DatabaseManager.getInstance();
    let sql = 'SELECT * FROM dialect_questions WHERE status = 1';
    const params = [];

    // 构建查询条件
    if (conditions.category) {
      sql += ' AND category = ?';
      params.push(conditions.category);
    }

    if (conditions.region) {
      sql += ' AND region = ?';
      params.push(conditions.region);
    }

    if (conditions.difficulty) {
      sql += ' AND difficulty_level = ?';
      params.push(conditions.difficulty);
    }

    if (conditions.questionType) {
      sql += ' AND question_type = ?';
      params.push(conditions.questionType);
    }

    // 排序
    if (conditions.random) {
      sql += ' ORDER BY RAND()';
    } else {
      sql += ' ORDER BY usage_count ASC, correct_rate DESC';
    }

    // 限制数量
    if (conditions.limit) {
      sql += ' LIMIT ?';
      params.push(parseInt(conditions.limit));
    }

    const [rows] = await db.query(sql, params);
    return rows.map(row => new Question(row));
  }

  /**
   * 获取随机题目
   */
  static async getRandomQuestions(count = 10, difficulty = null, category = null) {
    const conditions = {
      random: true,
      limit: count
    };

    if (difficulty) {
      conditions.difficulty = difficulty;
    }

    if (category) {
      conditions.category = category;
    }

    return await this.findByConditions(conditions);
  }

  /**
   * 更新题目使用统计
   */
  static async updateUsageStats(questionId, isCorrect, answerTime) {
    const db = DatabaseManager.getInstance();
    
    // 使用事务更新统计数据
    await db.transaction(async (connection) => {
      // 获取当前统计数据
      const [current] = await connection.query(
        'SELECT usage_count, correct_rate, avg_answer_time FROM dialect_questions WHERE id = ?',
        [questionId]
      );

      if (current.length === 0) return;

      const currentStats = current[0];
      const newUsageCount = currentStats.usage_count + 1;
      
      // 计算新的正确率
      const totalCorrect = Math.round(currentStats.correct_rate * currentStats.usage_count);
      const newTotalCorrect = totalCorrect + (isCorrect ? 1 : 0);
      const newCorrectRate = newTotalCorrect / newUsageCount;
      
      // 计算新的平均答题时间
      const totalTime = currentStats.avg_answer_time * currentStats.usage_count;
      const newTotalTime = totalTime + answerTime;
      const newAvgAnswerTime = newTotalTime / newUsageCount;

      // 更新统计数据
      await connection.query(
        `UPDATE dialect_questions 
         SET usage_count = ?, correct_rate = ?, avg_answer_time = ?, updated_at = NOW()
         WHERE id = ?`,
        [newUsageCount, newCorrectRate, newAvgAnswerTime, questionId]
      );
    });
  }

  /**
   * 创建新题目
   */
  async save() {
    const db = DatabaseManager.getInstance();
    
    if (this.id) {
      // 更新现有题目
      const [result] = await db.query(
        `UPDATE dialect_questions 
         SET category = ?, region = ?, question_text = ?, question_type = ?,
             audio_url = ?, audio_duration = ?, difficulty_level = ?, 
             standard_answer = ?, answer_options = ?, explanation = ?,
             status = ?, updated_at = NOW()
         WHERE id = ?`,
        [
          this.category, this.region, this.questionText, this.questionType,
          this.audioUrl, this.audioDuration, this.difficultyLevel,
          this.standardAnswer, JSON.stringify(this.answerOptions), this.explanation,
          this.status, this.id
        ]
      );
      return result.affectedRows > 0;
    } else {
      // 创建新题目
      const [result] = await db.query(
        `INSERT INTO dialect_questions 
         (category, region, question_text, question_type, audio_url, audio_duration,
          difficulty_level, standard_answer, answer_options, explanation, status, created_by)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          this.category, this.region, this.questionText, this.questionType,
          this.audioUrl, this.audioDuration, this.difficultyLevel,
          this.standardAnswer, JSON.stringify(this.answerOptions), this.explanation,
          this.status, this.createdBy
        ]
      );
      
      this.id = result.insertId;
      return true;
    }
  }

  /**
   * 转换为API响应格式
   */
  toJSON(includeAnswer = false) {
    const result = {
      id: this.id,
      category: this.category,
      region: this.region,
      content: this.questionText,
      type: this.questionType,
      audioUrl: this.audioUrl,
      audioDuration: this.audioDuration,
      difficulty: this.difficultyLevel,
      options: this.answerOptions,
      explanation: includeAnswer ? this.explanation : undefined,
      usageCount: this.usageCount,
      correctRate: this.correctRate,
      avgAnswerTime: this.avgAnswerTime
    };

    // 只在需要时包含答案
    if (includeAnswer) {
      result.correctAnswer = this.standardAnswer;
    }

    return result;
  }

  /**
   * 验证用户答案
   */
  checkAnswer(userAnswer) {
    if (!userAnswer) return false;
    
    // 标准化答案（去除空格，转小写）
    const normalizedUserAnswer = userAnswer.trim().toLowerCase();
    const normalizedCorrectAnswer = this.standardAnswer.trim().toLowerCase();
    
    return normalizedUserAnswer === normalizedCorrectAnswer;
  }

  /**
   * 计算得分
   */
  calculateScore(isCorrect, answerTime, streakCount = 0) {
    if (!isCorrect) return 0;
    
    let baseScore = 10; // 基础分数
    
    // 难度加成
    baseScore += (this.difficultyLevel - 1) * 5;
    
    // 时间加成（答题时间越短，加成越多）
    if (answerTime <= 5) {
      baseScore += 10; // 5秒内完成
    } else if (answerTime <= 10) {
      baseScore += 5;  // 10秒内完成
    }
    
    // 连击加成
    baseScore += Math.min(streakCount, 10) * 2;
    
    return baseScore;
  }
}

module.exports = Question;