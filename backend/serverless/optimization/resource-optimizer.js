/**
 * 资源优化与回收管理器
 * 
 * 实现资源的智能分配、自动回收、闲置资源清理等功能
 * 包括内存管理、连接池优化、缓存清理等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');

class ResourceOptimizer extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 内存管理配置
            memory: {
                maxUsage: options.maxMemoryUsage || 0.85,    // 最大内存使用率85%
                gcThreshold: options.gcThreshold || 0.80,    // GC触发阈值80%
                cleanupInterval: 300000,                     // 5分钟清理一次
                objectPoolSize: 1000                         // 对象池大小
            },
            
            // 连接池配置
            connectionPool: {
                maxConnections: options.maxConnections || 5000,
                minConnections: options.minConnections || 100,
                idleTimeout: options.idleTimeout || 300000,   // 5分钟空闲超时
                maxIdleTime: options.maxIdleTime || 600000,   // 10分钟最大空闲时间
                cleanupInterval: 60000                        // 1分钟清理一次
            },
            
            // 缓存管理配置
            cache: {
                maxSize: options.maxCacheSize || 1000,        // 最大缓存条目数
                ttlCleanupInterval: 120000,                   // 2分钟TTL清理
                memoryThreshold: 0.75,                        // 内存阈值75%
                lruEvictionSize: 100                          // LRU淘汰数量
            },
            
            // 资源监控配置
            monitoring: {
                interval: 30000,                              // 30秒监控间隔
                historySize: 100,                             // 历史记录数量
                alertThreshold: 0.90                          // 预警阈值90%
            }
        };
        
        // 资源状态
        this.resources = {
            memory: {
                used: 0,
                total: 0,
                usage: 0,
                objects: new Map(),
                pools: new Map()
            },
            connections: {
                active: new Map(),
                idle: new Map(),
                total: 0,
                usage: 0
            },
            cache: {
                entries: new Map(),
                size: 0,
                hitRate: 0,
                lastCleanup: Date.now()
            }
        };
        
        // 对象池管理
        this.objectPools = new Map([
            ['websocket_message', { pool: [], maxSize: 1000, created: 0, reused: 0 }],
            ['user_session', { pool: [], maxSize: 500, created: 0, reused: 0 }],
            ['room_state', { pool: [], maxSize: 200, created: 0, reused: 0 }],
            ['barrage_item', { pool: [], maxSize: 2000, created: 0, reused: 0 }]
        ]);
        
        // 监控历史
        this.monitoringHistory = [];
        
        // 统计信息
        this.stats = {
            memoryCleanups: 0,
            connectionCleanups: 0,
            cacheCleanups: 0,
            objectsRecycled: 0,
            bytesReclaimed: 0
        };
        
        // 定时器
        this.timers = {
            monitoring: null,
            memoryCleanup: null,
            connectionCleanup: null,
            cacheCleanup: null
        };
        
        this.initialize();
    }
    
    /**
     * 初始化资源优化器
     */
    initialize() {
        console.log('初始化资源优化与回收管理器...');
        
        // 启动监控
        this.startMonitoring();
        
        // 启动清理任务
        this.startCleanupTasks();
        
        // 初始化对象池
        this.initializeObjectPools();
        
        console.log('资源优化器初始化完成');
    }
    
    /**
     * 启动监控
     */
    startMonitoring() {
        this.timers.monitoring = setInterval(() => {
            this.collectResourceMetrics();
            this.analyzeResourceUsage();
            this.checkResourceAlerts();
        }, this.config.monitoring.interval);
    }
    
    /**
     * 启动清理任务
     */
    startCleanupTasks() {
        // 内存清理
        this.timers.memoryCleanup = setInterval(() => {
            this.performMemoryCleanup();
        }, this.config.memory.cleanupInterval);
        
        // 连接清理
        this.timers.connectionCleanup = setInterval(() => {
            this.performConnectionCleanup();
        }, this.config.connectionPool.cleanupInterval);
        
        // 缓存清理
        this.timers.cacheCleanup = setInterval(() => {
            this.performCacheCleanup();
        }, this.config.cache.ttlCleanupInterval);
    }
    
    /**
     * 初始化对象池
     */
    initializeObjectPools() {
        for (const [poolName, poolConfig] of this.objectPools) {
            // 预创建一些对象
            const preCreateCount = Math.min(10, poolConfig.maxSize);
            for (let i = 0; i < preCreateCount; i++) {
                const obj = this.createPoolObject(poolName);
                if (obj) {
                    poolConfig.pool.push(obj);
                }
            }
        }
    }
    
    /**
     * 收集资源指标
     */
    collectResourceMetrics() {
        // 收集内存指标
        const memUsage = process.memoryUsage();
        this.resources.memory = {
            used: memUsage.heapUsed,
            total: memUsage.heapTotal,
            usage: memUsage.heapUsed / memUsage.heapTotal,
            rss: memUsage.rss,
            external: memUsage.external
        };
        
        // 收集连接指标
        this.resources.connections.total = 
            this.resources.connections.active.size + 
            this.resources.connections.idle.size;
        this.resources.connections.usage = 
            this.resources.connections.total / this.config.connectionPool.maxConnections;
        
        // 收集缓存指标
        this.resources.cache.size = this.resources.cache.entries.size;
        
        // 记录历史
        const snapshot = {
            timestamp: Date.now(),
            memory: { ...this.resources.memory },
            connections: {
                active: this.resources.connections.active.size,
                idle: this.resources.connections.idle.size,
                total: this.resources.connections.total,
                usage: this.resources.connections.usage
            },
            cache: {
                size: this.resources.cache.size,
                hitRate: this.resources.cache.hitRate
            }
        };
        
        this.monitoringHistory.push(snapshot);
        
        // 保持历史记录限制
        if (this.monitoringHistory.length > this.config.monitoring.historySize) {
            this.monitoringHistory.shift();
        }
        
        // 触发监控事件
        this.emit('resourceMetrics', snapshot);
    }
    
    /**
     * 分析资源使用情况
     */
    analyzeResourceUsage() {
        const memory = this.resources.memory;
        const connections = this.resources.connections;
        const cache = this.resources.cache;
        
        // 内存使用分析
        if (memory.usage > this.config.memory.gcThreshold) {
            this.triggerGarbageCollection();
        }
        
        // 连接池分析
        if (connections.usage > 0.9) {
            this.optimizeConnectionPool();
        }
        
        // 缓存分析
        if (cache.size > this.config.cache.maxSize * 0.9) {
            this.optimizeCacheUsage();
        }
        
        // 对象池分析
        this.optimizeObjectPools();
    }
    
    /**
     * 检查资源预警
     */
    checkResourceAlerts() {
        const threshold = this.config.monitoring.alertThreshold;
        
        // 内存预警
        if (this.resources.memory.usage > threshold) {
            this.emit('resourceAlert', {
                type: 'memory',
                usage: this.resources.memory.usage,
                threshold,
                message: `内存使用率过高: ${(this.resources.memory.usage * 100).toFixed(1)}%`
            });
        }
        
        // 连接预警
        if (this.resources.connections.usage > threshold) {
            this.emit('resourceAlert', {
                type: 'connections',
                usage: this.resources.connections.usage,
                threshold,
                message: `连接使用率过高: ${(this.resources.connections.usage * 100).toFixed(1)}%`
            });
        }
    }
    
    /**
     * 执行内存清理
     */
    performMemoryCleanup() {
        console.log('执行内存清理...');
        
        const beforeUsage = this.resources.memory.usage;
        let reclaimedBytes = 0;
        
        // 清理对象池中的过期对象
        for (const [poolName, poolConfig] of this.objectPools) {
            const beforeSize = poolConfig.pool.length;
            poolConfig.pool = poolConfig.pool.slice(0, Math.floor(poolConfig.maxSize * 0.5));
            const cleaned = beforeSize - poolConfig.pool.length;
            reclaimedBytes += cleaned * 1024; // 估算每个对象1KB
        }
        
        // 清理闲置的资源引用
        this.cleanupIdleReferences();
        
        // 触发垃圾回收
        if (global.gc) {
            global.gc();
        }
        
        this.stats.memoryCleanups++;
        this.stats.bytesReclaimed += reclaimedBytes;
        
        const afterUsage = this.resources.memory.usage;
        const improvement = beforeUsage - afterUsage;
        
        console.log(`内存清理完成，回收 ${(reclaimedBytes / 1024 / 1024).toFixed(2)}MB，使用率降低 ${(improvement * 100).toFixed(1)}%`);
        
        this.emit('memoryCleanup', {
            beforeUsage,
            afterUsage,
            improvement,
            reclaimedBytes
        });
    }
    
    /**
     * 执行连接清理
     */
    performConnectionCleanup() {
        console.log('执行连接清理...');
        
        const now = Date.now();
        let cleanedConnections = 0;
        
        // 清理空闲连接
        for (const [connectionId, connection] of this.resources.connections.idle) {
            const idleTime = now - connection.lastActivity;
            
            if (idleTime > this.config.connectionPool.maxIdleTime) {
                // 关闭长时间空闲的连接
                this.closeConnection(connectionId, '长时间空闲');
                cleanedConnections++;
            } else if (idleTime > this.config.connectionPool.idleTimeout && 
                      this.resources.connections.idle.size > this.config.connectionPool.minConnections) {
                // 关闭超过空闲超时的连接（保持最小连接数）
                this.closeConnection(connectionId, '空闲超时');
                cleanedConnections++;
            }
        }
        
        this.stats.connectionCleanups++;
        
        console.log(`连接清理完成，清理了 ${cleanedConnections} 个空闲连接`);
        
        this.emit('connectionCleanup', {
            cleanedConnections,
            activeConnections: this.resources.connections.active.size,
            idleConnections: this.resources.connections.idle.size
        });
    }
    
    /**
     * 执行缓存清理
     */
    performCacheCleanup() {
        console.log('执行缓存清理...');
        
        const now = Date.now();
        let cleanedEntries = 0;
        let reclaimedMemory = 0;
        
        // 清理过期缓存
        for (const [key, entry] of this.resources.cache.entries) {
            if (entry.expireTime && now > entry.expireTime) {
                const size = this.estimateCacheEntrySize(entry);
                this.resources.cache.entries.delete(key);
                cleanedEntries++;
                reclaimedMemory += size;
            }
        }
        
        // 如果缓存仍然过大，执行LRU淘汰
        if (this.resources.cache.entries.size > this.config.cache.maxSize) {
            const entriesToEvict = this.resources.cache.entries.size - this.config.cache.maxSize + this.config.cache.lruEvictionSize;
            const sortedEntries = Array.from(this.resources.cache.entries.entries())
                .sort((a, b) => a[1].lastAccess - b[1].lastAccess);
            
            for (let i = 0; i < entriesToEvict && i < sortedEntries.length; i++) {
                const [key, entry] = sortedEntries[i];
                const size = this.estimateCacheEntrySize(entry);
                this.resources.cache.entries.delete(key);
                cleanedEntries++;
                reclaimedMemory += size;
            }
        }
        
        this.stats.cacheCleanups++;
        this.resources.cache.lastCleanup = now;
        
        console.log(`缓存清理完成，清理了 ${cleanedEntries} 个条目，回收 ${(reclaimedMemory / 1024).toFixed(2)}KB`);
        
        this.emit('cacheCleanup', {
            cleanedEntries,
            reclaimedMemory,
            remainingEntries: this.resources.cache.entries.size
        });
    }
    
    /**
     * 触发垃圾回收
     */
    triggerGarbageCollection() {
        if (global.gc) {
            const beforeUsage = this.resources.memory.usage;
            global.gc();
            
            // 等待一小段时间让GC完成
            setTimeout(() => {
                this.collectResourceMetrics();
                const afterUsage = this.resources.memory.usage;
                const improvement = beforeUsage - afterUsage;
                
                console.log(`垃圾回收完成，内存使用率从 ${(beforeUsage * 100).toFixed(1)}% 降低到 ${(afterUsage * 100).toFixed(1)}%`);
                
                this.emit('garbageCollection', {
                    beforeUsage,
                    afterUsage,
                    improvement
                });
            }, 100);
        }
    }
    
    /**
     * 优化连接池
     */
    optimizeConnectionPool() {
        console.log('优化连接池...');
        
        // 将一些活跃连接移到空闲池
        const activeConnections = Array.from(this.resources.connections.active.entries());
        const now = Date.now();
        
        for (const [connectionId, connection] of activeConnections) {
            const inactiveTime = now - connection.lastActivity;
            
            if (inactiveTime > this.config.connectionPool.idleTimeout) {
                this.resources.connections.active.delete(connectionId);
                this.resources.connections.idle.set(connectionId, connection);
            }
        }
    }
    
    /**
     * 优化缓存使用
     */
    optimizeCacheUsage() {
        console.log('优化缓存使用...');
        
        // 提前触发缓存清理
        this.performCacheCleanup();
        
        // 调整缓存策略
        this.emit('cacheOptimization', {
            action: 'reduce_ttl',
            factor: 0.8
        });
    }
    
    /**
     * 优化对象池
     */
    optimizeObjectPools() {
        for (const [poolName, poolConfig] of this.objectPools) {
            const poolSize = poolConfig.pool.length;
            const maxSize = poolConfig.maxSize;
            const usage = poolSize / maxSize;
            
            // 如果池使用率过低，减少池大小
            if (usage < 0.1 && poolSize > 10) {
                const targetSize = Math.max(10, Math.floor(poolSize * 0.5));
                poolConfig.pool = poolConfig.pool.slice(0, targetSize);
            }
            
            // 如果池使用率过高，考虑扩展
            if (usage > 0.9 && poolSize < maxSize) {
                const expandSize = Math.min(10, maxSize - poolSize);
                for (let i = 0; i < expandSize; i++) {
                    const obj = this.createPoolObject(poolName);
                    if (obj) {
                        poolConfig.pool.push(obj);
                    }
                }
            }
        }
    }
    
    /**
     * 从对象池获取对象
     */
    getFromPool(poolName) {
        const poolConfig = this.objectPools.get(poolName);
        if (!poolConfig) {
            return null;
        }
        
        if (poolConfig.pool.length > 0) {
            poolConfig.reused++;
            return poolConfig.pool.pop();
        } else {
            poolConfig.created++;
            return this.createPoolObject(poolName);
        }
    }
    
    /**
     * 归还对象到池
     */
    returnToPool(poolName, obj) {
        const poolConfig = this.objectPools.get(poolName);
        if (!poolConfig || poolConfig.pool.length >= poolConfig.maxSize) {
            return false;
        }
        
        // 重置对象状态
        this.resetPoolObject(poolName, obj);
        
        poolConfig.pool.push(obj);
        this.stats.objectsRecycled++;
        
        return true;
    }
    
    /**
     * 创建池对象
     */
    createPoolObject(poolName) {
        switch (poolName) {
            case 'websocket_message':
                return {
                    id: null,
                    type: null,
                    data: null,
                    timestamp: null,
                    roomId: null,
                    userId: null
                };
                
            case 'user_session':
                return {
                    userId: null,
                    connectionId: null,
                    roomId: null,
                    joinTime: null,
                    lastActivity: null,
                    metadata: {}
                };
                
            case 'room_state':
                return {
                    roomId: null,
                    playerCount: 0,
                    viewerCount: 0,
                    status: null,
                    lastUpdate: null
                };
                
            case 'barrage_item':
                return {
                    id: null,
                    content: null,
                    userId: null,
                    timestamp: null,
                    style: {}
                };
                
            default:
                return {};
        }
    }
    
    /**
     * 重置池对象
     */
    resetPoolObject(poolName, obj) {
        switch (poolName) {
            case 'websocket_message':
                obj.id = null;
                obj.type = null;
                obj.data = null;
                obj.timestamp = null;
                obj.roomId = null;
                obj.userId = null;
                break;
                
            case 'user_session':
                obj.userId = null;
                obj.connectionId = null;
                obj.roomId = null;
                obj.joinTime = null;
                obj.lastActivity = null;
                obj.metadata = {};
                break;
                
            case 'room_state':
                obj.roomId = null;
                obj.playerCount = 0;
                obj.viewerCount = 0;
                obj.status = null;
                obj.lastUpdate = null;
                break;
                
            case 'barrage_item':
                obj.id = null;
                obj.content = null;
                obj.userId = null;
                obj.timestamp = null;
                obj.style = {};
                break;
        }
    }
    
    /**
     * 清理闲置引用
     */
    cleanupIdleReferences() {
        // 清理可能的循环引用和闲置对象
        for (const [key, value] of this.resources.memory.objects) {
            if (value && typeof value === 'object' && value._lastAccess) {
                const idleTime = Date.now() - value._lastAccess;
                if (idleTime > 600000) { // 10分钟未访问
                    this.resources.memory.objects.delete(key);
                }
            }
        }
    }
    
    /**
     * 关闭连接
     */
    closeConnection(connectionId, reason) {
        const connection = this.resources.connections.idle.get(connectionId) || 
                          this.resources.connections.active.get(connectionId);
        
        if (connection) {
            try {
                if (connection.ws && connection.ws.close) {
                    connection.ws.close();
                }
            } catch (error) {
                console.error(`关闭连接失败 ${connectionId}:`, error);
            }
            
            this.resources.connections.idle.delete(connectionId);
            this.resources.connections.active.delete(connectionId);
            
            console.log(`连接 ${connectionId} 已关闭: ${reason}`);
        }
    }
    
    /**
     * 估算缓存条目大小
     */
    estimateCacheEntrySize(entry) {
        try {
            return JSON.stringify(entry).length * 2; // 粗略估算，UTF-16编码
        } catch (error) {
            return 1024; // 默认1KB
        }
    }
    
    /**
     * 获取资源统计
     */
    getResourceStats() {
        return {
            memory: {
                ...this.resources.memory,
                usagePercent: (this.resources.memory.usage * 100).toFixed(1)
            },
            connections: {
                active: this.resources.connections.active.size,
                idle: this.resources.connections.idle.size,
                total: this.resources.connections.total,
                usagePercent: (this.resources.connections.usage * 100).toFixed(1)
            },
            cache: {
                size: this.resources.cache.size,
                hitRate: this.resources.cache.hitRate,
                lastCleanup: this.resources.cache.lastCleanup
            },
            objectPools: Object.fromEntries(
                Array.from(this.objectPools.entries()).map(([name, config]) => [
                    name,
                    {
                        poolSize: config.pool.length,
                        maxSize: config.maxSize,
                        created: config.created,
                        reused: config.reused,
                        reuseRate: config.created > 0 ? (config.reused / (config.created + config.reused) * 100).toFixed(1) : 0
                    }
                ])
            ),
            stats: { ...this.stats }
        };
    }
    
    /**
     * 获取监控历史
     */
    getMonitoringHistory() {
        return this.monitoringHistory.slice();
    }
    
    /**
     * 关闭资源优化器
     */
    close() {
        console.log('关闭资源优化与回收管理器...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理资源
        this.resources.memory.objects.clear();
        this.resources.connections.active.clear();
        this.resources.connections.idle.clear();
        this.resources.cache.entries.clear();
        
        // 清理对象池
        for (const poolConfig of this.objectPools.values()) {
            poolConfig.pool = [];
        }
        
        console.log('资源优化器已关闭');
    }
}

module.exports = ResourceOptimizer;
