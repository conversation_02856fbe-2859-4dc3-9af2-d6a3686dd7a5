/**
 * 智能降级策略管理器
 * 
 * 实现智能降级策略，包括高负载时的功能降级、消息限流、连接数控制等
 * 确保在成本压力下系统稳定运行
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');

class DegradationManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 降级阈值
            thresholds: {
                cpu: options.cpuThreshold || 80,           // CPU使用率80%
                memory: options.memoryThreshold || 85,     // 内存使用率85%
                connections: options.connectionThreshold || 4000, // 连接数4000
                cost: options.costThreshold || 0.9,        // 成本预算90%
                errorRate: options.errorRateThreshold || 0.05, // 错误率5%
                responseTime: options.responseTimeThreshold || 1000 // 响应时间1秒
            },
            
            // 降级级别
            levels: {
                0: 'normal',      // 正常运行
                1: 'light',       // 轻度降级
                2: 'moderate',    // 中度降级
                3: 'heavy',       // 重度降级
                4: 'emergency'    // 紧急降级
            },
            
            // 恢复策略
            recovery: {
                checkInterval: 30000,     // 30秒检查一次恢复条件
                stabilityPeriod: 300000,  // 5分钟稳定期
                gradualRecovery: true     // 渐进式恢复
            },
            
            // 功能开关
            features: {
                barrage: true,        // 弹幕功能
                gifts: true,          // 礼物功能
                predictions: true,    // 预测功能
                likes: true,          // 点赞功能
                notifications: true,  // 通知功能
                analytics: true       // 分析功能
            }
        };
        
        // 当前状态
        this.state = {
            level: 0,                    // 当前降级级别
            activeStrategies: new Set(), // 激活的降级策略
            lastLevelChange: Date.now(), // 上次级别变更时间
            metrics: {                   // 当前指标
                cpu: 0,
                memory: 0,
                connections: 0,
                cost: 0,
                errorRate: 0,
                responseTime: 0
            }
        };
        
        // 降级策略定义
        this.strategies = new Map([
            // 轻度降级策略
            ['reduce_barrage_rate', {
                level: 1,
                description: '降低弹幕发送频率',
                action: this.reduceBarrageRate.bind(this),
                revert: this.restoreBarrageRate.bind(this)
            }],
            
            ['increase_cache_ttl', {
                level: 1,
                description: '增加缓存TTL',
                action: this.increaseCacheTTL.bind(this),
                revert: this.restoreCacheTTL.bind(this)
            }],
            
            // 中度降级策略
            ['limit_new_connections', {
                level: 2,
                description: '限制新连接',
                action: this.limitNewConnections.bind(this),
                revert: this.restoreConnectionLimit.bind(this)
            }],
            
            ['disable_notifications', {
                level: 2,
                description: '禁用通知功能',
                action: this.disableNotifications.bind(this),
                revert: this.enableNotifications.bind(this)
            }],
            
            ['reduce_prediction_frequency', {
                level: 2,
                description: '降低预测更新频率',
                action: this.reducePredictionFrequency.bind(this),
                revert: this.restorePredictionFrequency.bind(this)
            }],
            
            // 重度降级策略
            ['disable_gifts', {
                level: 3,
                description: '禁用礼物功能',
                action: this.disableGifts.bind(this),
                revert: this.enableGifts.bind(this)
            }],
            
            ['batch_messages', {
                level: 3,
                description: '批量处理消息',
                action: this.enableMessageBatching.bind(this),
                revert: this.disableMessageBatching.bind(this)
            }],
            
            ['reduce_room_capacity', {
                level: 3,
                description: '降低房间容量',
                action: this.reduceRoomCapacity.bind(this),
                revert: this.restoreRoomCapacity.bind(this)
            }],
            
            // 紧急降级策略
            ['disable_predictions', {
                level: 4,
                description: '禁用预测功能',
                action: this.disablePredictions.bind(this),
                revert: this.enablePredictions.bind(this)
            }],
            
            ['emergency_connection_limit', {
                level: 4,
                description: '紧急连接限制',
                action: this.emergencyConnectionLimit.bind(this),
                revert: this.restoreEmergencyLimit.bind(this)
            }],
            
            ['minimal_features_only', {
                level: 4,
                description: '仅保留核心功能',
                action: this.enableMinimalFeatures.bind(this),
                revert: this.restoreAllFeatures.bind(this)
            }]
        ]);
        
        // 监控定时器
        this.monitorTimer = null;
        this.recoveryTimer = null;
        
        // 统计信息
        this.stats = {
            degradationCount: 0,
            recoveryCount: 0,
            totalDowntime: 0,
            strategiesUsed: new Map()
        };
        
        this.initialize();
    }
    
    /**
     * 初始化降级管理器
     */
    initialize() {
        console.log('初始化智能降级策略管理器...');
        
        // 启动监控
        this.startMonitoring();
        
        // 启动恢复检查
        this.startRecoveryCheck();
        
        console.log('降级策略管理器初始化完成');
    }
    
    /**
     * 启动监控
     */
    startMonitoring() {
        this.monitorTimer = setInterval(() => {
            this.checkDegradationConditions();
        }, 10000); // 每10秒检查一次
    }
    
    /**
     * 启动恢复检查
     */
    startRecoveryCheck() {
        this.recoveryTimer = setInterval(() => {
            this.checkRecoveryConditions();
        }, this.config.recovery.checkInterval);
    }
    
    /**
     * 更新系统指标
     */
    updateMetrics(metrics) {
        this.state.metrics = {
            ...this.state.metrics,
            ...metrics
        };
        
        // 触发指标更新事件
        this.emit('metricsUpdate', this.state.metrics);
    }
    
    /**
     * 检查降级条件
     */
    checkDegradationConditions() {
        const metrics = this.state.metrics;
        const thresholds = this.config.thresholds;
        
        let requiredLevel = 0;
        
        // 检查各项指标
        if (metrics.cpu > thresholds.cpu || 
            metrics.memory > thresholds.memory ||
            metrics.responseTime > thresholds.responseTime) {
            requiredLevel = Math.max(requiredLevel, 1);
        }
        
        if (metrics.connections > thresholds.connections ||
            metrics.errorRate > thresholds.errorRate) {
            requiredLevel = Math.max(requiredLevel, 2);
        }
        
        if (metrics.cost > thresholds.cost) {
            requiredLevel = Math.max(requiredLevel, 3);
        }
        
        // 紧急情况检查
        if (metrics.cpu > 95 || 
            metrics.memory > 95 || 
            metrics.errorRate > 0.2 ||
            metrics.responseTime > 5000) {
            requiredLevel = 4;
        }
        
        // 如果需要更高级别的降级
        if (requiredLevel > this.state.level) {
            this.degradeToLevel(requiredLevel);
        }
    }
    
    /**
     * 降级到指定级别
     */
    async degradeToLevel(targetLevel) {
        if (targetLevel <= this.state.level) {
            return;
        }
        
        console.log(`开始降级到级别 ${targetLevel} (${this.config.levels[targetLevel]})`);
        
        // 执行降级策略
        for (const [strategyName, strategy] of this.strategies) {
            if (strategy.level <= targetLevel && !this.state.activeStrategies.has(strategyName)) {
                try {
                    await strategy.action();
                    this.state.activeStrategies.add(strategyName);
                    
                    // 更新统计
                    const count = this.stats.strategiesUsed.get(strategyName) || 0;
                    this.stats.strategiesUsed.set(strategyName, count + 1);
                    
                    console.log(`激活降级策略: ${strategy.description}`);
                    
                } catch (error) {
                    console.error(`执行降级策略失败 ${strategyName}:`, error);
                }
            }
        }
        
        // 更新状态
        const previousLevel = this.state.level;
        this.state.level = targetLevel;
        this.state.lastLevelChange = Date.now();
        this.stats.degradationCount++;
        
        // 触发降级事件
        this.emit('degradation', {
            previousLevel,
            currentLevel: targetLevel,
            activeStrategies: Array.from(this.state.activeStrategies),
            metrics: { ...this.state.metrics }
        });
    }
    
    /**
     * 检查恢复条件
     */
    checkRecoveryConditions() {
        if (this.state.level === 0) {
            return; // 已经是正常级别
        }
        
        const metrics = this.state.metrics;
        const thresholds = this.config.thresholds;
        const stabilityPeriod = this.config.recovery.stabilityPeriod;
        
        // 检查是否满足恢复条件
        const canRecover = 
            metrics.cpu < thresholds.cpu * 0.7 &&
            metrics.memory < thresholds.memory * 0.7 &&
            metrics.connections < thresholds.connections * 0.8 &&
            metrics.cost < thresholds.cost * 0.8 &&
            metrics.errorRate < thresholds.errorRate * 0.5 &&
            metrics.responseTime < thresholds.responseTime * 0.7;
        
        // 检查稳定期
        const timeSinceLastChange = Date.now() - this.state.lastLevelChange;
        const isStable = timeSinceLastChange >= stabilityPeriod;
        
        if (canRecover && isStable) {
            if (this.config.recovery.gradualRecovery) {
                this.gradualRecover();
            } else {
                this.fullRecover();
            }
        }
    }
    
    /**
     * 渐进式恢复
     */
    async gradualRecover() {
        const targetLevel = Math.max(0, this.state.level - 1);
        await this.recoverToLevel(targetLevel);
    }
    
    /**
     * 完全恢复
     */
    async fullRecover() {
        await this.recoverToLevel(0);
    }
    
    /**
     * 恢复到指定级别
     */
    async recoverToLevel(targetLevel) {
        if (targetLevel >= this.state.level) {
            return;
        }
        
        console.log(`开始恢复到级别 ${targetLevel} (${this.config.levels[targetLevel]})`);
        
        // 恢复不需要的策略
        for (const [strategyName, strategy] of this.strategies) {
            if (strategy.level > targetLevel && this.state.activeStrategies.has(strategyName)) {
                try {
                    await strategy.revert();
                    this.state.activeStrategies.delete(strategyName);
                    
                    console.log(`恢复策略: ${strategy.description}`);
                    
                } catch (error) {
                    console.error(`恢复策略失败 ${strategyName}:`, error);
                }
            }
        }
        
        // 更新状态
        const previousLevel = this.state.level;
        this.state.level = targetLevel;
        this.state.lastLevelChange = Date.now();
        this.stats.recoveryCount++;
        
        // 触发恢复事件
        this.emit('recovery', {
            previousLevel,
            currentLevel: targetLevel,
            activeStrategies: Array.from(this.state.activeStrategies),
            metrics: { ...this.state.metrics }
        });
    }
    
    // ==================== 降级策略实现 ====================
    
    /**
     * 降低弹幕发送频率
     */
    async reduceBarrageRate() {
        this.emit('configChange', {
            type: 'barrage_rate',
            action: 'reduce',
            value: 0.5 // 降低到50%
        });
    }
    
    async restoreBarrageRate() {
        this.emit('configChange', {
            type: 'barrage_rate',
            action: 'restore',
            value: 1.0
        });
    }
    
    /**
     * 增加缓存TTL
     */
    async increaseCacheTTL() {
        this.emit('configChange', {
            type: 'cache_ttl',
            action: 'increase',
            multiplier: 2
        });
    }
    
    async restoreCacheTTL() {
        this.emit('configChange', {
            type: 'cache_ttl',
            action: 'restore',
            multiplier: 1
        });
    }
    
    /**
     * 限制新连接
     */
    async limitNewConnections() {
        this.emit('configChange', {
            type: 'connection_limit',
            action: 'set',
            value: Math.floor(this.config.thresholds.connections * 0.8)
        });
    }
    
    async restoreConnectionLimit() {
        this.emit('configChange', {
            type: 'connection_limit',
            action: 'restore',
            value: this.config.thresholds.connections
        });
    }
    
    /**
     * 禁用通知功能
     */
    async disableNotifications() {
        this.config.features.notifications = false;
        this.emit('featureToggle', {
            feature: 'notifications',
            enabled: false
        });
    }
    
    async enableNotifications() {
        this.config.features.notifications = true;
        this.emit('featureToggle', {
            feature: 'notifications',
            enabled: true
        });
    }
    
    /**
     * 降低预测更新频率
     */
    async reducePredictionFrequency() {
        this.emit('configChange', {
            type: 'prediction_frequency',
            action: 'reduce',
            value: 0.5
        });
    }
    
    async restorePredictionFrequency() {
        this.emit('configChange', {
            type: 'prediction_frequency',
            action: 'restore',
            value: 1.0
        });
    }
    
    /**
     * 禁用礼物功能
     */
    async disableGifts() {
        this.config.features.gifts = false;
        this.emit('featureToggle', {
            feature: 'gifts',
            enabled: false
        });
    }
    
    async enableGifts() {
        this.config.features.gifts = true;
        this.emit('featureToggle', {
            feature: 'gifts',
            enabled: true
        });
    }
    
    /**
     * 启用消息批处理
     */
    async enableMessageBatching() {
        this.emit('configChange', {
            type: 'message_batching',
            action: 'enable',
            batchSize: 100,
            batchTimeout: 500
        });
    }
    
    async disableMessageBatching() {
        this.emit('configChange', {
            type: 'message_batching',
            action: 'disable'
        });
    }
    
    /**
     * 降低房间容量
     */
    async reduceRoomCapacity() {
        this.emit('configChange', {
            type: 'room_capacity',
            action: 'reduce',
            value: 500 // 降低到500人
        });
    }
    
    async restoreRoomCapacity() {
        this.emit('configChange', {
            type: 'room_capacity',
            action: 'restore',
            value: 1000
        });
    }
    
    /**
     * 禁用预测功能
     */
    async disablePredictions() {
        this.config.features.predictions = false;
        this.emit('featureToggle', {
            feature: 'predictions',
            enabled: false
        });
    }
    
    async enablePredictions() {
        this.config.features.predictions = true;
        this.emit('featureToggle', {
            feature: 'predictions',
            enabled: true
        });
    }
    
    /**
     * 紧急连接限制
     */
    async emergencyConnectionLimit() {
        this.emit('configChange', {
            type: 'connection_limit',
            action: 'emergency',
            value: 1000 // 紧急限制到1000
        });
    }
    
    async restoreEmergencyLimit() {
        this.emit('configChange', {
            type: 'connection_limit',
            action: 'restore',
            value: this.config.thresholds.connections
        });
    }
    
    /**
     * 启用最小功能集
     */
    async enableMinimalFeatures() {
        this.config.features = {
            barrage: true,
            gifts: false,
            predictions: false,
            likes: false,
            notifications: false,
            analytics: false
        };
        
        this.emit('featureToggle', {
            feature: 'minimal_mode',
            enabled: true,
            features: this.config.features
        });
    }
    
    async restoreAllFeatures() {
        this.config.features = {
            barrage: true,
            gifts: true,
            predictions: true,
            likes: true,
            notifications: true,
            analytics: true
        };
        
        this.emit('featureToggle', {
            feature: 'minimal_mode',
            enabled: false,
            features: this.config.features
        });
    }
    
    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            level: this.state.level,
            levelName: this.config.levels[this.state.level],
            activeStrategies: Array.from(this.state.activeStrategies).map(name => ({
                name,
                description: this.strategies.get(name).description
            })),
            metrics: { ...this.state.metrics },
            features: { ...this.config.features },
            stats: {
                ...this.stats,
                strategiesUsed: Object.fromEntries(this.stats.strategiesUsed)
            }
        };
    }
    
    /**
     * 手动触发降级
     */
    async manualDegrade(level, reason = '手动触发') {
        console.log(`手动降级到级别 ${level}: ${reason}`);
        await this.degradeToLevel(level);
    }
    
    /**
     * 手动触发恢复
     */
    async manualRecover(level = 0, reason = '手动恢复') {
        console.log(`手动恢复到级别 ${level}: ${reason}`);
        await this.recoverToLevel(level);
    }
    
    /**
     * 关闭降级管理器
     */
    close() {
        console.log('关闭智能降级策略管理器...');
        
        // 清理定时器
        if (this.monitorTimer) {
            clearInterval(this.monitorTimer);
        }
        
        if (this.recoveryTimer) {
            clearInterval(this.recoveryTimer);
        }
        
        // 恢复所有策略
        this.fullRecover();
        
        console.log('降级策略管理器已关闭');
    }
}

module.exports = DegradationManager;
