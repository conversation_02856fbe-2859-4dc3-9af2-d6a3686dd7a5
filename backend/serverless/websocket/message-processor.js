/**
 * 消息处理器
 * 
 * 优化弹幕消息处理性能，实现消息批量处理、异步广播、消息队列优化等功能
 * 目标：单房间支持1000+消息/分钟
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');

class MessageProcessor extends EventEmitter {
    constructor(connectionPool, options = {}) {
        super();
        
        this.connectionPool = connectionPool;
        
        // 配置参数
        this.config = {
            batchSize: options.batchSize || 50,
            batchTimeout: options.batchTimeout || 100, // ms
            maxQueueSize: options.maxQueueSize || 10000,
            rateLimitPerUser: options.rateLimitPerUser || 10, // 每分钟
            rateLimitPerRoom: options.rateLimitPerRoom || 1000, // 每分钟
            contentFilterEnabled: options.contentFilterEnabled || true,
            priorityLevels: options.priorityLevels || 3,
            ...options
        };
        
        // 消息队列
        this.messageQueue = [];
        this.priorityQueues = new Map(); // priority -> queue
        this.processingQueue = false;
        
        // 批处理
        this.batchBuffer = [];
        this.batchTimer = null;
        
        // 速率限制
        this.userRateLimit = new Map(); // userId -> { count, resetTime }
        this.roomRateLimit = new Map(); // roomId -> { count, resetTime }
        
        // 内容过滤
        this.contentFilter = {
            sensitiveWords: ['敏感词1', '敏感词2'], // 实际项目中从配置加载
            bannedPatterns: [/广告/, /违规/], // 正则表达式
            maxLength: 200
        };
        
        // 统计信息
        this.stats = {
            totalMessages: 0,
            processedMessages: 0,
            filteredMessages: 0,
            rateLimitedMessages: 0,
            averageProcessingTime: 0,
            messagesPerSecond: 0,
            lastProcessTime: Date.now()
        };
        
        // 性能监控
        this.performanceMonitor = {
            processingTimes: [],
            maxSamples: 1000
        };
        
        this.initialize();
    }
    
    /**
     * 初始化消息处理器
     */
    initialize() {
        console.log('初始化消息处理器...');
        
        // 初始化优先级队列
        for (let i = 0; i < this.config.priorityLevels; i++) {
            this.priorityQueues.set(i, []);
        }
        
        // 启动消息处理循环
        this.startProcessingLoop();
        
        // 启动速率限制清理
        this.startRateLimitCleanup();
        
        // 启动统计更新
        this.startStatsUpdate();
        
        console.log('消息处理器初始化完成');
    }
    
    /**
     * 处理消息
     */
    async processMessage(connectionId, message) {
        const startTime = Date.now();
        
        try {
            // 获取连接信息
            const connection = this.connectionPool.getConnection(connectionId);
            if (!connection) {
                throw new Error('连接不存在');
            }
            
            // 创建消息对象
            const messageObj = {
                id: uuidv4(),
                connectionId,
                userId: connection.userId,
                roomId: connection.roomId,
                type: message.type,
                content: message.content,
                timestamp: Date.now(),
                priority: this.getMessagePriority(message.type),
                metadata: message.metadata || {}
            };
            
            // 速率限制检查
            if (!this.checkRateLimit(messageObj)) {
                this.stats.rateLimitedMessages++;
                this.emit('rateLimited', messageObj);
                return false;
            }
            
            // 内容过滤
            if (this.config.contentFilterEnabled && !this.filterContent(messageObj)) {
                this.stats.filteredMessages++;
                this.emit('contentFiltered', messageObj);
                return false;
            }
            
            // 添加到队列
            this.addToQueue(messageObj);
            
            // 更新统计
            this.stats.totalMessages++;
            this.updateProcessingTime(Date.now() - startTime);
            
            return true;
            
        } catch (error) {
            console.error('处理消息失败:', error);
            this.emit('processingError', { connectionId, message, error });
            return false;
        }
    }
    
    /**
     * 获取消息优先级
     */
    getMessagePriority(messageType) {
        const priorityMap = {
            'system': 0,      // 最高优先级
            'admin': 0,
            'gift': 1,        // 高优先级
            'like': 1,
            'barrage': 2,     // 普通优先级
            'prediction': 2,
            'chat': 2
        };
        
        return priorityMap[messageType] || 2;
    }
    
    /**
     * 检查速率限制
     */
    checkRateLimit(message) {
        const now = Date.now();
        const minute = 60 * 1000;
        
        // 用户速率限制
        if (message.userId) {
            const userLimit = this.userRateLimit.get(message.userId);
            if (userLimit) {
                if (now - userLimit.resetTime < minute) {
                    if (userLimit.count >= this.config.rateLimitPerUser) {
                        return false;
                    }
                    userLimit.count++;
                } else {
                    userLimit.count = 1;
                    userLimit.resetTime = now;
                }
            } else {
                this.userRateLimit.set(message.userId, {
                    count: 1,
                    resetTime: now
                });
            }
        }
        
        // 房间速率限制
        if (message.roomId) {
            const roomLimit = this.roomRateLimit.get(message.roomId);
            if (roomLimit) {
                if (now - roomLimit.resetTime < minute) {
                    if (roomLimit.count >= this.config.rateLimitPerRoom) {
                        return false;
                    }
                    roomLimit.count++;
                } else {
                    roomLimit.count = 1;
                    roomLimit.resetTime = now;
                }
            } else {
                this.roomRateLimit.set(message.roomId, {
                    count: 1,
                    resetTime: now
                });
            }
        }
        
        return true;
    }
    
    /**
     * 内容过滤
     */
    filterContent(message) {
        if (message.type !== 'barrage' && message.type !== 'chat') {
            return true; // 非文本消息不过滤
        }
        
        const content = message.content;
        
        // 长度检查
        if (content.length > this.contentFilter.maxLength) {
            return false;
        }
        
        // 敏感词检查
        for (const word of this.contentFilter.sensitiveWords) {
            if (content.includes(word)) {
                return false;
            }
        }
        
        // 正则表达式检查
        for (const pattern of this.contentFilter.bannedPatterns) {
            if (pattern.test(content)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 添加消息到队列
     */
    addToQueue(message) {
        // 检查队列大小
        if (this.messageQueue.length >= this.config.maxQueueSize) {
            console.warn('消息队列已满，丢弃最旧的消息');
            this.messageQueue.shift();
        }
        
        // 根据优先级添加到对应队列
        const priorityQueue = this.priorityQueues.get(message.priority);
        if (priorityQueue) {
            priorityQueue.push(message);
        } else {
            this.messageQueue.push(message);
        }
        
        // 触发处理
        this.triggerProcessing();
    }
    
    /**
     * 触发消息处理
     */
    triggerProcessing() {
        if (!this.processingQueue) {
            setImmediate(() => this.processQueue());
        }
    }
    
    /**
     * 启动消息处理循环
     */
    startProcessingLoop() {
        setInterval(() => {
            if (!this.processingQueue) {
                this.processQueue();
            }
        }, 10); // 每10ms检查一次
    }
    
    /**
     * 处理消息队列
     */
    async processQueue() {
        if (this.processingQueue) {
            return;
        }
        
        this.processingQueue = true;
        
        try {
            // 按优先级处理消息
            for (let priority = 0; priority < this.config.priorityLevels; priority++) {
                const queue = this.priorityQueues.get(priority);
                if (queue && queue.length > 0) {
                    await this.processBatch(queue);
                }
            }
            
            // 处理普通队列
            if (this.messageQueue.length > 0) {
                await this.processBatch(this.messageQueue);
            }
            
        } catch (error) {
            console.error('处理队列失败:', error);
        } finally {
            this.processingQueue = false;
        }
    }
    
    /**
     * 批量处理消息
     */
    async processBatch(queue) {
        const batchSize = Math.min(this.config.batchSize, queue.length);
        if (batchSize === 0) {
            return;
        }
        
        const batch = queue.splice(0, batchSize);
        const startTime = Date.now();
        
        try {
            // 按房间分组
            const roomGroups = new Map();
            for (const message of batch) {
                if (!roomGroups.has(message.roomId)) {
                    roomGroups.set(message.roomId, []);
                }
                roomGroups.get(message.roomId).push(message);
            }
            
            // 并行处理各房间的消息
            const promises = [];
            for (const [roomId, messages] of roomGroups) {
                promises.push(this.processRoomMessages(roomId, messages));
            }
            
            await Promise.all(promises);
            
            // 更新统计
            this.stats.processedMessages += batch.length;
            this.updateProcessingTime(Date.now() - startTime);
            
        } catch (error) {
            console.error('批量处理失败:', error);
            // 将失败的消息重新加入队列
            queue.unshift(...batch);
        }
    }
    
    /**
     * 处理房间消息
     */
    async processRoomMessages(roomId, messages) {
        try {
            // 按消息类型分组处理
            const typeGroups = new Map();
            for (const message of messages) {
                if (!typeGroups.has(message.type)) {
                    typeGroups.set(message.type, []);
                }
                typeGroups.get(message.type).push(message);
            }
            
            // 处理不同类型的消息
            for (const [type, typeMessages] of typeGroups) {
                await this.processMessagesByType(roomId, type, typeMessages);
            }
            
        } catch (error) {
            console.error(`处理房间 ${roomId} 消息失败:`, error);
            throw error;
        }
    }
    
    /**
     * 按类型处理消息
     */
    async processMessagesByType(roomId, type, messages) {
        switch (type) {
            case 'barrage':
                await this.processBarrageMessages(roomId, messages);
                break;
                
            case 'like':
                await this.processLikeMessages(roomId, messages);
                break;
                
            case 'gift':
                await this.processGiftMessages(roomId, messages);
                break;
                
            case 'prediction':
                await this.processPredictionMessages(roomId, messages);
                break;
                
            case 'system':
                await this.processSystemMessages(roomId, messages);
                break;
                
            default:
                await this.processGenericMessages(roomId, messages);
        }
    }
    
    /**
     * 处理弹幕消息
     */
    async processBarrageMessages(roomId, messages) {
        // 批量广播弹幕消息
        for (const message of messages) {
            const broadcastMessage = {
                type: 'barrage',
                id: message.id,
                userId: message.userId,
                content: message.content,
                timestamp: message.timestamp,
                metadata: message.metadata
            };
            
            this.connectionPool.broadcastToRoom(roomId, broadcastMessage, message.connectionId);
        }
        
        // 触发事件
        this.emit('barrageProcessed', { roomId, messages });
    }
    
    /**
     * 处理点赞消息
     */
    async processLikeMessages(roomId, messages) {
        // 合并点赞数量
        const likeCount = messages.length;
        
        const broadcastMessage = {
            type: 'like_batch',
            count: likeCount,
            timestamp: Date.now()
        };
        
        this.connectionPool.broadcastToRoom(roomId, broadcastMessage);
        
        this.emit('likesProcessed', { roomId, count: likeCount });
    }
    
    /**
     * 处理礼物消息
     */
    async processGiftMessages(roomId, messages) {
        for (const message of messages) {
            const broadcastMessage = {
                type: 'gift',
                id: message.id,
                userId: message.userId,
                giftType: message.metadata.giftType,
                giftCount: message.metadata.giftCount,
                timestamp: message.timestamp
            };
            
            this.connectionPool.broadcastToRoom(roomId, broadcastMessage, message.connectionId);
        }
        
        this.emit('giftsProcessed', { roomId, messages });
    }
    
    /**
     * 处理预测消息
     */
    async processPredictionMessages(roomId, messages) {
        for (const message of messages) {
            const broadcastMessage = {
                type: 'prediction',
                id: message.id,
                userId: message.userId,
                prediction: message.metadata.prediction,
                timestamp: message.timestamp
            };
            
            this.connectionPool.broadcastToRoom(roomId, broadcastMessage, message.connectionId);
        }
        
        this.emit('predictionsProcessed', { roomId, messages });
    }
    
    /**
     * 处理系统消息
     */
    async processSystemMessages(roomId, messages) {
        for (const message of messages) {
            const broadcastMessage = {
                type: 'system',
                id: message.id,
                content: message.content,
                timestamp: message.timestamp,
                metadata: message.metadata
            };
            
            this.connectionPool.broadcastToRoom(roomId, broadcastMessage);
        }
        
        this.emit('systemMessagesProcessed', { roomId, messages });
    }
    
    /**
     * 处理通用消息
     */
    async processGenericMessages(roomId, messages) {
        for (const message of messages) {
            this.connectionPool.broadcastToRoom(roomId, message, message.connectionId);
        }
        
        this.emit('genericMessagesProcessed', { roomId, messages });
    }
    
    /**
     * 启动速率限制清理
     */
    startRateLimitCleanup() {
        setInterval(() => {
            this.cleanupRateLimit();
        }, 60000); // 每分钟清理一次
    }
    
    /**
     * 清理过期的速率限制记录
     */
    cleanupRateLimit() {
        const now = Date.now();
        const minute = 60 * 1000;
        
        // 清理用户速率限制
        for (const [userId, limit] of this.userRateLimit) {
            if (now - limit.resetTime > minute) {
                this.userRateLimit.delete(userId);
            }
        }
        
        // 清理房间速率限制
        for (const [roomId, limit] of this.roomRateLimit) {
            if (now - limit.resetTime > minute) {
                this.roomRateLimit.delete(roomId);
            }
        }
    }
    
    /**
     * 启动统计更新
     */
    startStatsUpdate() {
        setInterval(() => {
            this.updateStats();
        }, 5000); // 每5秒更新一次统计
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        const now = Date.now();
        const timeDiff = (now - this.stats.lastProcessTime) / 1000;
        
        if (timeDiff > 0) {
            this.stats.messagesPerSecond = Math.round(this.stats.processedMessages / timeDiff);
        }
        
        // 计算平均处理时间
        if (this.performanceMonitor.processingTimes.length > 0) {
            const sum = this.performanceMonitor.processingTimes.reduce((a, b) => a + b, 0);
            this.stats.averageProcessingTime = sum / this.performanceMonitor.processingTimes.length;
        }
        
        this.emit('statsUpdate', { ...this.stats });
    }
    
    /**
     * 更新处理时间
     */
    updateProcessingTime(time) {
        this.performanceMonitor.processingTimes.push(time);
        
        // 保持样本数量限制
        if (this.performanceMonitor.processingTimes.length > this.performanceMonitor.maxSamples) {
            this.performanceMonitor.processingTimes.shift();
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            queueSize: this.messageQueue.length,
            priorityQueueSizes: Array.from(this.priorityQueues.entries()).map(([priority, queue]) => ({
                priority,
                size: queue.length
            })),
            rateLimitCacheSize: {
                users: this.userRateLimit.size,
                rooms: this.roomRateLimit.size
            }
        };
    }
    
    /**
     * 关闭消息处理器
     */
    close() {
        console.log('关闭消息处理器...');
        
        // 清理定时器
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
        }
        
        // 清理队列
        this.messageQueue = [];
        this.priorityQueues.clear();
        this.batchBuffer = [];
        
        // 清理缓存
        this.userRateLimit.clear();
        this.roomRateLimit.clear();
        
        console.log('消息处理器已关闭');
    }
}

module.exports = MessageProcessor;
