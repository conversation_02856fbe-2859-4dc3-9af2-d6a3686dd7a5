/**
 * WebSocket连接池管理器
 * 
 * 实现连接复用、负载均衡、连接数限制等功能，支持5000+并发连接
 * 包括连接状态监控和自动扩缩容
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const WebSocket = require('ws');

class ConnectionPool extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            maxConnections: options.maxConnections || 5000,
            maxConnectionsPerRoom: options.maxConnectionsPerRoom || 1000,
            connectionTimeout: options.connectionTimeout || 30000,
            heartbeatInterval: options.heartbeatInterval || 30000,
            cleanupInterval: options.cleanupInterval || 60000,
            loadBalanceThreshold: options.loadBalanceThreshold || 0.8,
            autoScale: options.autoScale || true,
            ...options
        };
        
        // 连接存储
        this.connections = new Map(); // connectionId -> connection info
        this.roomConnections = new Map(); // roomId -> Set of connectionIds
        this.userConnections = new Map(); // userId -> Set of connectionIds
        
        // 连接池状态
        this.stats = {
            totalConnections: 0,
            activeConnections: 0,
            roomCount: 0,
            messagesPerSecond: 0,
            lastMessageTime: Date.now()
        };
        
        // 负载均衡
        this.loadBalancer = {
            servers: new Map(), // serverId -> server info
            currentServer: 0,
            roundRobinIndex: 0
        };
        
        // 定时器
        this.heartbeatTimer = null;
        this.cleanupTimer = null;
        this.statsTimer = null;
        
        this.initialize();
    }
    
    /**
     * 初始化连接池
     */
    initialize() {
        console.log('初始化WebSocket连接池...');
        
        // 启动心跳检测
        this.startHeartbeat();
        
        // 启动清理任务
        this.startCleanup();
        
        // 启动统计监控
        this.startStatsMonitoring();
        
        console.log(`连接池初始化完成，最大连接数: ${this.config.maxConnections}`);
    }
    
    /**
     * 添加连接到池中
     */
    addConnection(connectionId, ws, metadata = {}) {
        try {
            // 检查连接数限制
            if (this.stats.totalConnections >= this.config.maxConnections) {
                throw new Error('连接数已达上限');
            }
            
            // 检查房间连接数限制
            const roomId = metadata.roomId;
            if (roomId && this.getRoomConnectionCount(roomId) >= this.config.maxConnectionsPerRoom) {
                throw new Error('房间连接数已达上限');
            }
            
            // 创建连接信息
            const connectionInfo = {
                id: connectionId,
                ws: ws,
                userId: metadata.userId,
                roomId: roomId,
                joinTime: Date.now(),
                lastHeartbeat: Date.now(),
                isActive: true,
                messageCount: 0,
                metadata: metadata
            };
            
            // 存储连接
            this.connections.set(connectionId, connectionInfo);
            
            // 更新房间连接映射
            if (roomId) {
                if (!this.roomConnections.has(roomId)) {
                    this.roomConnections.set(roomId, new Set());
                }
                this.roomConnections.get(roomId).add(connectionId);
            }
            
            // 更新用户连接映射
            if (metadata.userId) {
                if (!this.userConnections.has(metadata.userId)) {
                    this.userConnections.set(metadata.userId, new Set());
                }
                this.userConnections.get(metadata.userId).add(connectionId);
            }
            
            // 设置WebSocket事件监听
            this.setupWebSocketEvents(connectionId, ws);
            
            // 更新统计
            this.stats.totalConnections++;
            this.stats.activeConnections++;
            
            // 触发事件
            this.emit('connectionAdded', connectionInfo);
            
            console.log(`连接已添加: ${connectionId}, 当前连接数: ${this.stats.totalConnections}`);
            
            return true;
            
        } catch (error) {
            console.error('添加连接失败:', error);
            return false;
        }
    }
    
    /**
     * 设置WebSocket事件监听
     */
    setupWebSocketEvents(connectionId, ws) {
        // 消息处理
        ws.on('message', (data) => {
            this.handleMessage(connectionId, data);
        });
        
        // 连接关闭
        ws.on('close', (code, reason) => {
            this.removeConnection(connectionId, `连接关闭: ${code} ${reason}`);
        });
        
        // 连接错误
        ws.on('error', (error) => {
            console.error(`连接错误 ${connectionId}:`, error);
            this.removeConnection(connectionId, `连接错误: ${error.message}`);
        });
        
        // Pong响应（心跳）
        ws.on('pong', () => {
            const connection = this.connections.get(connectionId);
            if (connection) {
                connection.lastHeartbeat = Date.now();
            }
        });
    }
    
    /**
     * 处理WebSocket消息
     */
    handleMessage(connectionId, data) {
        try {
            const connection = this.connections.get(connectionId);
            if (!connection) {
                return;
            }
            
            // 更新消息计数
            connection.messageCount++;
            connection.lastHeartbeat = Date.now();
            
            // 解析消息
            const message = JSON.parse(data.toString());
            
            // 更新统计
            this.updateMessageStats();
            
            // 触发消息事件
            this.emit('message', {
                connectionId,
                connection,
                message
            });
            
        } catch (error) {
            console.error(`处理消息失败 ${connectionId}:`, error);
        }
    }
    
    /**
     * 移除连接
     */
    removeConnection(connectionId, reason = '未知原因') {
        const connection = this.connections.get(connectionId);
        if (!connection) {
            return false;
        }
        
        try {
            // 关闭WebSocket连接
            if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
                connection.ws.close();
            }
            
            // 从房间连接映射中移除
            if (connection.roomId) {
                const roomConnections = this.roomConnections.get(connection.roomId);
                if (roomConnections) {
                    roomConnections.delete(connectionId);
                    if (roomConnections.size === 0) {
                        this.roomConnections.delete(connection.roomId);
                    }
                }
            }
            
            // 从用户连接映射中移除
            if (connection.userId) {
                const userConnections = this.userConnections.get(connection.userId);
                if (userConnections) {
                    userConnections.delete(connectionId);
                    if (userConnections.size === 0) {
                        this.userConnections.delete(connection.userId);
                    }
                }
            }
            
            // 从连接池中移除
            this.connections.delete(connectionId);
            
            // 更新统计
            this.stats.totalConnections--;
            if (connection.isActive) {
                this.stats.activeConnections--;
            }
            
            // 触发事件
            this.emit('connectionRemoved', {
                connectionId,
                connection,
                reason
            });
            
            console.log(`连接已移除: ${connectionId}, 原因: ${reason}, 当前连接数: ${this.stats.totalConnections}`);
            
            return true;
            
        } catch (error) {
            console.error('移除连接失败:', error);
            return false;
        }
    }
    
    /**
     * 广播消息到房间
     */
    broadcastToRoom(roomId, message, excludeConnectionId = null) {
        const roomConnections = this.roomConnections.get(roomId);
        if (!roomConnections) {
            return 0;
        }
        
        let sentCount = 0;
        const messageStr = JSON.stringify(message);
        
        for (const connectionId of roomConnections) {
            if (connectionId === excludeConnectionId) {
                continue;
            }
            
            const connection = this.connections.get(connectionId);
            if (connection && connection.isActive && connection.ws.readyState === WebSocket.OPEN) {
                try {
                    connection.ws.send(messageStr);
                    sentCount++;
                } catch (error) {
                    console.error(`发送消息失败 ${connectionId}:`, error);
                    this.removeConnection(connectionId, '发送消息失败');
                }
            }
        }
        
        return sentCount;
    }
    
    /**
     * 发送消息给特定用户
     */
    sendToUser(userId, message) {
        const userConnections = this.userConnections.get(userId);
        if (!userConnections) {
            return 0;
        }
        
        let sentCount = 0;
        const messageStr = JSON.stringify(message);
        
        for (const connectionId of userConnections) {
            const connection = this.connections.get(connectionId);
            if (connection && connection.isActive && connection.ws.readyState === WebSocket.OPEN) {
                try {
                    connection.ws.send(messageStr);
                    sentCount++;
                } catch (error) {
                    console.error(`发送消息失败 ${connectionId}:`, error);
                    this.removeConnection(connectionId, '发送消息失败');
                }
            }
        }
        
        return sentCount;
    }
    
    /**
     * 发送消息给特定连接
     */
    sendToConnection(connectionId, message) {
        const connection = this.connections.get(connectionId);
        if (!connection || !connection.isActive || connection.ws.readyState !== WebSocket.OPEN) {
            return false;
        }
        
        try {
            connection.ws.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error(`发送消息失败 ${connectionId}:`, error);
            this.removeConnection(connectionId, '发送消息失败');
            return false;
        }
    }
    
    /**
     * 获取房间连接数
     */
    getRoomConnectionCount(roomId) {
        const roomConnections = this.roomConnections.get(roomId);
        return roomConnections ? roomConnections.size : 0;
    }
    
    /**
     * 获取用户连接数
     */
    getUserConnectionCount(userId) {
        const userConnections = this.userConnections.get(userId);
        return userConnections ? userConnections.size : 0;
    }
    
    /**
     * 获取连接信息
     */
    getConnection(connectionId) {
        return this.connections.get(connectionId);
    }
    
    /**
     * 获取房间所有连接
     */
    getRoomConnections(roomId) {
        const roomConnections = this.roomConnections.get(roomId);
        if (!roomConnections) {
            return [];
        }
        
        return Array.from(roomConnections).map(connectionId => 
            this.connections.get(connectionId)
        ).filter(Boolean);
    }
    
    /**
     * 启动心跳检测
     */
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.performHeartbeat();
        }, this.config.heartbeatInterval);
    }
    
    /**
     * 执行心跳检测
     */
    performHeartbeat() {
        const now = Date.now();
        const timeout = this.config.connectionTimeout;
        const deadConnections = [];
        
        for (const [connectionId, connection] of this.connections) {
            if (now - connection.lastHeartbeat > timeout) {
                deadConnections.push(connectionId);
            } else if (connection.ws.readyState === WebSocket.OPEN) {
                // 发送ping
                try {
                    connection.ws.ping();
                } catch (error) {
                    deadConnections.push(connectionId);
                }
            }
        }
        
        // 清理死连接
        deadConnections.forEach(connectionId => {
            this.removeConnection(connectionId, '心跳超时');
        });
        
        if (deadConnections.length > 0) {
            console.log(`心跳检测清理了 ${deadConnections.length} 个死连接`);
        }
    }
    
    /**
     * 启动清理任务
     */
    startCleanup() {
        this.cleanupTimer = setInterval(() => {
            this.performCleanup();
        }, this.config.cleanupInterval);
    }
    
    /**
     * 执行清理任务
     */
    performCleanup() {
        // 清理空房间
        for (const [roomId, connections] of this.roomConnections) {
            if (connections.size === 0) {
                this.roomConnections.delete(roomId);
            }
        }
        
        // 清理空用户映射
        for (const [userId, connections] of this.userConnections) {
            if (connections.size === 0) {
                this.userConnections.delete(userId);
            }
        }
        
        // 更新房间统计
        this.stats.roomCount = this.roomConnections.size;
        
        // 检查是否需要扩缩容
        if (this.config.autoScale) {
            this.checkAutoScale();
        }
    }
    
    /**
     * 检查自动扩缩容
     */
    checkAutoScale() {
        const loadRatio = this.stats.totalConnections / this.config.maxConnections;
        
        if (loadRatio > this.config.loadBalanceThreshold) {
            console.log(`连接负载过高 (${(loadRatio * 100).toFixed(1)}%), 建议扩容`);
            this.emit('scaleUp', {
                currentLoad: loadRatio,
                connections: this.stats.totalConnections,
                maxConnections: this.config.maxConnections
            });
        } else if (loadRatio < 0.3 && this.stats.totalConnections > 100) {
            console.log(`连接负载较低 (${(loadRatio * 100).toFixed(1)}%), 可以考虑缩容`);
            this.emit('scaleDown', {
                currentLoad: loadRatio,
                connections: this.stats.totalConnections,
                maxConnections: this.config.maxConnections
            });
        }
    }
    
    /**
     * 启动统计监控
     */
    startStatsMonitoring() {
        this.statsTimer = setInterval(() => {
            this.updateStats();
        }, 10000); // 每10秒更新一次统计
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        // 计算活跃连接数
        let activeCount = 0;
        for (const connection of this.connections.values()) {
            if (connection.isActive && connection.ws.readyState === WebSocket.OPEN) {
                activeCount++;
            }
        }
        
        this.stats.activeConnections = activeCount;
        this.stats.roomCount = this.roomConnections.size;
        
        // 触发统计事件
        this.emit('statsUpdate', { ...this.stats });
    }
    
    /**
     * 更新消息统计
     */
    updateMessageStats() {
        const now = Date.now();
        const timeDiff = (now - this.stats.lastMessageTime) / 1000;
        
        if (timeDiff >= 1) {
            // 简单的消息速率计算
            this.stats.messagesPerSecond = Math.round(1 / timeDiff);
            this.stats.lastMessageTime = now;
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime()
        };
    }
    
    /**
     * 关闭连接池
     */
    close() {
        console.log('关闭WebSocket连接池...');
        
        // 清理定时器
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
        }
        
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        if (this.statsTimer) {
            clearInterval(this.statsTimer);
        }
        
        // 关闭所有连接
        for (const [connectionId, connection] of this.connections) {
            if (connection.ws && connection.ws.readyState === WebSocket.OPEN) {
                connection.ws.close();
            }
        }
        
        // 清理数据
        this.connections.clear();
        this.roomConnections.clear();
        this.userConnections.clear();
        
        console.log('WebSocket连接池已关闭');
    }
}

module.exports = ConnectionPool;
