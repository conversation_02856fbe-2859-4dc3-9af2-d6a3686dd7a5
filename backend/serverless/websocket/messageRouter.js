/**
 * WebSocket 消息路由器
 * 处理不同类型的WebSocket消息并路由到相应的处理器
 */

const { logger } = require('../utils/logger');
const { performanceMonitor } = require('../utils/monitoring');

class MessageRouter {
  constructor() {
    this.routes = new Map();
    this.middleware = [];
    this.rateLimiter = new Map(); // 简单的内存限流器
  }

  /**
   * 添加消息路由
   */
  addRoute(messageType, handler) {
    if (typeof handler !== 'function') {
      throw new Error('Handler must be a function');
    }

    this.routes.set(messageType, handler);
    logger.debug('Message route added', { messageType });
  }

  /**
   * 移除消息路由
   */
  removeRoute(messageType) {
    this.routes.delete(messageType);
    logger.debug('Message route removed', { messageType });
  }

  /**
   * 添加中间件
   */
  addMiddleware(middleware) {
    if (typeof middleware !== 'function') {
      throw new Error('Middleware must be a function');
    }

    this.middleware.push(middleware);
    logger.debug('Message middleware added');
  }

  /**
   * 路由消息到相应的处理器
   */
  async routeMessage(message, connection, requestContext) {
    const startTime = Date.now();
    const messageLogger = logger.child({
      connectionId: connection.id,
      messageType: message.type,
      userId: connection.userId
    });

    try {
      // 验证消息格式
      this.validateMessage(message);

      // 检查限流
      await this.checkRateLimit(connection.id, message.type);

      // 执行中间件
      for (const middleware of this.middleware) {
        await middleware(message, connection, requestContext);
      }

      // 查找路由处理器
      const handler = this.routes.get(message.type);
      if (!handler) {
        throw new Error(`No handler found for message type: ${message.type}`);
      }

      messageLogger.debug('Processing message');

      // 执行处理器
      const result = await handler(message, connection, requestContext);

      // 记录性能指标
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'websocket_message',
        message.type,
        duration,
        true
      );

      messageLogger.info('Message processed successfully', {
        duration: `${duration}ms`,
        hasResponse: !!result
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 记录错误指标
      await performanceMonitor.recordDatabaseMetrics(
        'websocket_message',
        message.type,
        duration,
        false,
        error
      );

      messageLogger.error('Message processing failed', {
        error: error.message,
        duration: `${duration}ms`
      });

      // 返回错误响应
      return {
        type: 'error',
        data: {
          originalType: message.type,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * 验证消息格式
   */
  validateMessage(message) {
    if (!message || typeof message !== 'object') {
      throw new Error('Message must be an object');
    }

    if (!message.type || typeof message.type !== 'string') {
      throw new Error('Message must have a valid type');
    }

    if (message.type.length > 50) {
      throw new Error('Message type too long');
    }

    // 检查消息大小
    const messageSize = JSON.stringify(message).length;
    if (messageSize > 64 * 1024) { // 64KB限制
      throw new Error('Message too large');
    }

    // 验证数据字段
    if (message.data && typeof message.data !== 'object') {
      throw new Error('Message data must be an object');
    }
  }

  /**
   * 检查消息限流
   */
  async checkRateLimit(connectionId, messageType) {
    const key = `${connectionId}:${messageType}`;
    const now = Date.now();
    const windowSize = 60 * 1000; // 1分钟窗口
    const maxMessages = this.getMessageLimit(messageType);

    if (!this.rateLimiter.has(key)) {
      this.rateLimiter.set(key, {
        count: 1,
        windowStart: now
      });
      return;
    }

    const limiter = this.rateLimiter.get(key);
    
    // 检查是否需要重置窗口
    if (now - limiter.windowStart > windowSize) {
      limiter.count = 1;
      limiter.windowStart = now;
      return;
    }

    // 检查是否超过限制
    if (limiter.count >= maxMessages) {
      throw new Error(`Rate limit exceeded for message type: ${messageType}`);
    }

    limiter.count++;
  }

  /**
   * 获取消息类型的限流配置
   */
  getMessageLimit(messageType) {
    const limits = {
      'ping': 60,              // 心跳消息：每分钟60次
      'danmaku:send': 10,      // 弹幕消息：每分钟10次
      'prediction:submit': 5,   // 预测提交：每分钟5次
      'room:join': 5,          // 加入房间：每分钟5次
      'room:leave': 5,         // 离开房间：每分钟5次
      'game:sync': 30,         // 游戏同步：每分钟30次
      'auth': 3                // 认证：每分钟3次
    };

    return limits[messageType] || 20; // 默认限制：每分钟20次
  }

  /**
   * 清理过期的限流记录
   */
  cleanupRateLimiter() {
    const now = Date.now();
    const windowSize = 60 * 1000;

    for (const [key, limiter] of this.rateLimiter.entries()) {
      if (now - limiter.windowStart > windowSize) {
        this.rateLimiter.delete(key);
      }
    }
  }

  /**
   * 获取路由统计信息
   */
  getRouteStats() {
    return {
      totalRoutes: this.routes.size,
      routes: Array.from(this.routes.keys()),
      middlewareCount: this.middleware.length,
      rateLimiterEntries: this.rateLimiter.size
    };
  }

  /**
   * 批量添加路由
   */
  addRoutes(routes) {
    for (const [messageType, handler] of Object.entries(routes)) {
      this.addRoute(messageType, handler);
    }
  }

  /**
   * 创建路由组
   */
  createRouteGroup(prefix, routes) {
    for (const [suffix, handler] of Object.entries(routes)) {
      const messageType = `${prefix}:${suffix}`;
      this.addRoute(messageType, handler);
    }
  }

  /**
   * 消息验证中间件
   */
  static createValidationMiddleware(schema) {
    return async (message, connection, requestContext) => {
      // 这里可以集成JSON Schema验证
      // 简单示例：检查必需字段
      if (schema.required) {
        for (const field of schema.required) {
          if (!message.data || !(field in message.data)) {
            throw new Error(`Missing required field: ${field}`);
          }
        }
      }
    };
  }

  /**
   * 认证中间件
   */
  static createAuthMiddleware(requiredAuth = true) {
    return async (message, connection, requestContext) => {
      if (requiredAuth && !connection.authenticated) {
        throw new Error('Authentication required');
      }
    };
  }

  /**
   * 日志中间件
   */
  static createLoggingMiddleware() {
    return async (message, connection, requestContext) => {
      logger.debug('Message middleware: logging', {
        messageType: message.type,
        connectionId: connection.id,
        userId: connection.userId,
        dataSize: message.data ? JSON.stringify(message.data).length : 0
      });
    };
  }

  /**
   * 性能监控中间件
   */
  static createPerformanceMiddleware() {
    return async (message, connection, requestContext) => {
      // 记录消息处理开始时间
      requestContext.messageStartTime = Date.now();
    };
  }

  /**
   * 安全检查中间件
   */
  static createSecurityMiddleware() {
    return async (message, connection, requestContext) => {
      // 检查消息内容是否包含恶意代码
      const messageStr = JSON.stringify(message);
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /eval\(/i,
        /function\(/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(messageStr)) {
          logger.warn('Suspicious message content detected', {
            connectionId: connection.id,
            messageType: message.type,
            pattern: pattern.toString()
          });
          throw new Error('Suspicious message content');
        }
      }
    };
  }
}

// 启动清理任务
setInterval(() => {
  // 这里需要访问全局实例，实际使用时需要适当调整
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = { MessageRouter };
