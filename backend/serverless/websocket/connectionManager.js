/**
 * WebSocket 连接管理器
 * 管理WebSocket连接的生命周期、状态和元数据
 */

const { RedisManager } = require('../utils/redis');
const { logger } = require('../utils/logger');

class ConnectionManager {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.connectionPrefix = 'ws:connection:';
    this.userConnectionsPrefix = 'ws:user:';
    this.ipConnectionsPrefix = 'ws:ip:';
    
    // 启动清理任务
    this.startCleanupTask();
  }

  /**
   * 创建新连接
   */
  async createConnection(connectionId, sourceIP, headers = {}) {
    const connection = {
      id: connectionId,
      sourceIP,
      userAgent: headers['User-Agent'] || '',
      origin: headers['Origin'] || '',
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      authenticated: false,
      userId: null,
      rooms: [],
      metadata: {}
    };

    try {
      // 存储连接信息
      await this.redis.setex(
        `${this.connectionPrefix}${connectionId}`,
        24 * 3600, // 24小时过期
        JSON.stringify(connection)
      );

      // 记录IP连接数
      await this.redis.sadd(`${this.ipConnectionsPrefix}${sourceIP}`, connectionId);
      await this.redis.expire(`${this.ipConnectionsPrefix}${sourceIP}`, 24 * 3600);

      logger.info('WebSocket connection created', {
        connectionId,
        sourceIP,
        userAgent: connection.userAgent
      });

      return connection;

    } catch (error) {
      logger.error('Failed to create WebSocket connection', {
        connectionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取连接信息
   */
  async getConnection(connectionId) {
    try {
      const data = await this.redis.get(`${this.connectionPrefix}${connectionId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Failed to get WebSocket connection', {
        connectionId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 更新连接信息
   */
  async updateConnection(connectionId, updates) {
    try {
      const connection = await this.getConnection(connectionId);
      if (!connection) {
        throw new Error('Connection not found');
      }

      const updatedConnection = { ...connection, ...updates };
      
      await this.redis.setex(
        `${this.connectionPrefix}${connectionId}`,
        24 * 3600,
        JSON.stringify(updatedConnection)
      );

      // 如果更新了用户ID，建立用户连接映射
      if (updates.userId && updates.userId !== connection.userId) {
        await this.addUserConnection(updates.userId, connectionId);
        
        // 移除旧的用户连接映射
        if (connection.userId) {
          await this.removeUserConnection(connection.userId, connectionId);
        }
      }

      logger.debug('WebSocket connection updated', {
        connectionId,
        updates: Object.keys(updates)
      });

      return updatedConnection;

    } catch (error) {
      logger.error('Failed to update WebSocket connection', {
        connectionId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新最后活跃时间
   */
  async updateLastActivity(connectionId) {
    try {
      const connection = await this.getConnection(connectionId);
      if (connection) {
        connection.lastActivity = new Date().toISOString();
        await this.redis.setex(
          `${this.connectionPrefix}${connectionId}`,
          24 * 3600,
          JSON.stringify(connection)
        );
      }
    } catch (error) {
      logger.error('Failed to update last activity', {
        connectionId,
        error: error.message
      });
    }
  }

  /**
   * 移除连接
   */
  async removeConnection(connectionId) {
    try {
      const connection = await this.getConnection(connectionId);
      if (!connection) {
        return;
      }

      // 移除连接记录
      await this.redis.del(`${this.connectionPrefix}${connectionId}`);

      // 移除IP连接记录
      await this.redis.srem(`${this.ipConnectionsPrefix}${connection.sourceIP}`, connectionId);

      // 移除用户连接映射
      if (connection.userId) {
        await this.removeUserConnection(connection.userId, connectionId);
      }

      logger.info('WebSocket connection removed', {
        connectionId,
        userId: connection.userId,
        duration: this.calculateConnectionDuration(connection.createdAt)
      });

    } catch (error) {
      logger.error('Failed to remove WebSocket connection', {
        connectionId,
        error: error.message
      });
    }
  }

  /**
   * 添加用户连接映射
   */
  async addUserConnection(userId, connectionId) {
    try {
      await this.redis.sadd(`${this.userConnectionsPrefix}${userId}`, connectionId);
      await this.redis.expire(`${this.userConnectionsPrefix}${userId}`, 24 * 3600);
    } catch (error) {
      logger.error('Failed to add user connection mapping', {
        userId,
        connectionId,
        error: error.message
      });
    }
  }

  /**
   * 移除用户连接映射
   */
  async removeUserConnection(userId, connectionId) {
    try {
      await this.redis.srem(`${this.userConnectionsPrefix}${userId}`, connectionId);
    } catch (error) {
      logger.error('Failed to remove user connection mapping', {
        userId,
        connectionId,
        error: error.message
      });
    }
  }

  /**
   * 获取用户的所有连接
   */
  async getUserConnections(userId) {
    try {
      const connectionIds = await this.redis.smembers(`${this.userConnectionsPrefix}${userId}`);
      const connections = [];

      for (const connectionId of connectionIds) {
        const connection = await this.getConnection(connectionId);
        if (connection) {
          connections.push(connection);
        } else {
          // 清理无效的连接ID
          await this.removeUserConnection(userId, connectionId);
        }
      }

      return connections;
    } catch (error) {
      logger.error('Failed to get user connections', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 获取IP的连接数
   */
  async getConnectionCountByIP(sourceIP) {
    try {
      return await this.redis.scard(`${this.ipConnectionsPrefix}${sourceIP}`);
    } catch (error) {
      logger.error('Failed to get connection count by IP', {
        sourceIP,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * 获取所有活跃连接
   */
  async getActiveConnections() {
    try {
      const pattern = `${this.connectionPrefix}*`;
      const keys = await this.redis.keys(pattern);
      const connections = [];

      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          connections.push(JSON.parse(data));
        }
      }

      return connections;
    } catch (error) {
      logger.error('Failed to get active connections', {
        error: error.message
      });
      return [];
    }
  }

  /**
   * 获取连接统计信息
   */
  async getConnectionStats() {
    try {
      const connections = await this.getActiveConnections();
      const now = new Date();
      
      const stats = {
        total: connections.length,
        authenticated: 0,
        anonymous: 0,
        byHour: {},
        averageDuration: 0,
        uniqueUsers: new Set(),
        uniqueIPs: new Set()
      };

      let totalDuration = 0;

      connections.forEach(connection => {
        if (connection.authenticated) {
          stats.authenticated++;
          if (connection.userId) {
            stats.uniqueUsers.add(connection.userId);
          }
        } else {
          stats.anonymous++;
        }

        stats.uniqueIPs.add(connection.sourceIP);

        // 计算连接时长
        const duration = now - new Date(connection.createdAt);
        totalDuration += duration;

        // 按小时统计
        const hour = new Date(connection.createdAt).getHours();
        stats.byHour[hour] = (stats.byHour[hour] || 0) + 1;
      });

      stats.averageDuration = connections.length > 0 ? totalDuration / connections.length : 0;
      stats.uniqueUsers = stats.uniqueUsers.size;
      stats.uniqueIPs = stats.uniqueIPs.size;

      return stats;
    } catch (error) {
      logger.error('Failed to get connection stats', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * 启动清理任务
   */
  startCleanupTask() {
    // 每5分钟清理一次过期连接
    setInterval(async () => {
      try {
        await this.cleanupExpiredConnections();
      } catch (error) {
        logger.error('Connection cleanup task failed', {
          error: error.message
        });
      }
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期连接
   */
  async cleanupExpiredConnections() {
    try {
      const connections = await this.getActiveConnections();
      const now = new Date();
      const maxIdleTime = 30 * 60 * 1000; // 30分钟无活动则清理
      
      let cleanedCount = 0;

      for (const connection of connections) {
        const lastActivity = new Date(connection.lastActivity);
        const idleTime = now - lastActivity;

        if (idleTime > maxIdleTime) {
          await this.removeConnection(connection.id);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logger.info('Cleaned up expired WebSocket connections', {
          cleanedCount,
          totalConnections: connections.length
        });
      }

    } catch (error) {
      logger.error('Failed to cleanup expired connections', {
        error: error.message
      });
    }
  }

  /**
   * 计算连接持续时间
   */
  calculateConnectionDuration(createdAt) {
    const duration = new Date() - new Date(createdAt);
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }

  /**
   * 强制断开用户的所有连接
   */
  async disconnectUser(userId) {
    try {
      const connections = await this.getUserConnections(userId);
      
      for (const connection of connections) {
        await this.removeConnection(connection.id);
      }

      logger.info('Disconnected all user connections', {
        userId,
        connectionCount: connections.length
      });

    } catch (error) {
      logger.error('Failed to disconnect user connections', {
        userId,
        error: error.message
      });
    }
  }
}

module.exports = { ConnectionManager };
