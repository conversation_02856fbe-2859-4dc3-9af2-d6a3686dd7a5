/**
 * WebSocket 处理器
 * 实现实时通信功能，支持围观、弹幕、预测游戏等功能
 */

const { logger } = require('../utils/logger');
const { performanceMonitor } = require('../utils/monitoring');
const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { ConnectionManager } = require('./connectionManager');
const { MessageRouter } = require('./messageRouter');
const { RoomManager } = require('./roomManager');

class WebSocketHandler {
  constructor() {
    this.connectionManager = new ConnectionManager();
    this.messageRouter = new MessageRouter();
    this.roomManager = new RoomManager();
    
    // 初始化消息路由
    this.setupMessageRoutes();
  }

  /**
   * WebSocket 连接处理
   */
  async handleConnect(event, context) {
    const startTime = Date.now();
    const wsLogger = logger.child({
      requestId: context.requestId,
      connectionId: event.requestContext.connectionId,
      action: 'connect'
    });

    try {
      wsLogger.info('WebSocket connection attempt');

      // 安全检查
      await this.performSecurityCheck(event, context);

      // 创建连接
      const connection = await this.connectionManager.createConnection(
        event.requestContext.connectionId,
        event.requestContext.identity.sourceIp,
        event.headers
      );

      wsLogger.info('WebSocket connection established', {
        connectionId: connection.id,
        userId: connection.userId,
        duration: `${Date.now() - startTime}ms`
      });

      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Connected' })
      };

    } catch (error) {
      wsLogger.error('WebSocket connection failed', {
        error: error.message,
        duration: `${Date.now() - startTime}ms`
      });

      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Connection failed' })
      };
    }
  }

  /**
   * WebSocket 断开处理
   */
  async handleDisconnect(event, context) {
    const startTime = Date.now();
    const wsLogger = logger.child({
      requestId: context.requestId,
      connectionId: event.requestContext.connectionId,
      action: 'disconnect'
    });

    try {
      wsLogger.info('WebSocket disconnection');

      // 清理连接
      await this.connectionManager.removeConnection(
        event.requestContext.connectionId
      );

      // 清理房间状态
      await this.roomManager.handleUserDisconnect(
        event.requestContext.connectionId
      );

      wsLogger.info('WebSocket disconnection completed', {
        duration: `${Date.now() - startTime}ms`
      });

      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Disconnected' })
      };

    } catch (error) {
      wsLogger.error('WebSocket disconnection error', {
        error: error.message,
        duration: `${Date.now() - startTime}ms`
      });

      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Disconnection failed' })
      };
    }
  }

  /**
   * WebSocket 消息处理
   */
  async handleMessage(event, context) {
    const startTime = Date.now();
    const wsLogger = logger.child({
      requestId: context.requestId,
      connectionId: event.requestContext.connectionId,
      action: 'message'
    });

    try {
      // 解析消息
      const message = JSON.parse(event.body);
      wsLogger.debug('WebSocket message received', {
        messageType: message.type,
        messageSize: event.body.length
      });

      // 验证连接
      const connection = await this.connectionManager.getConnection(
        event.requestContext.connectionId
      );

      if (!connection) {
        throw new Error('Connection not found');
      }

      // 更新连接活跃时间
      await this.connectionManager.updateLastActivity(connection.id);

      // 路由消息
      const response = await this.messageRouter.routeMessage(
        message,
        connection,
        event.requestContext
      );

      // 发送响应（如果需要）
      if (response) {
        await this.sendMessage(
          event.requestContext.connectionId,
          response
        );
      }

      wsLogger.info('WebSocket message processed', {
        messageType: message.type,
        duration: `${Date.now() - startTime}ms`
      });

      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Message processed' })
      };

    } catch (error) {
      wsLogger.error('WebSocket message processing failed', {
        error: error.message,
        body: event.body,
        duration: `${Date.now() - startTime}ms`
      });

      // 发送错误响应
      try {
        await this.sendMessage(
          event.requestContext.connectionId,
          {
            type: 'error',
            message: error.message,
            timestamp: new Date().toISOString()
          }
        );
      } catch (sendError) {
        wsLogger.error('Failed to send error message', {
          error: sendError.message
        });
      }

      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Message processing failed' })
      };
    }
  }

  /**
   * 设置消息路由
   */
  setupMessageRoutes() {
    // 心跳检测
    this.messageRouter.addRoute('ping', async (message, connection) => {
      return {
        type: 'pong',
        timestamp: new Date().toISOString()
      };
    });

    // 用户认证
    this.messageRouter.addRoute('auth', async (message, connection) => {
      return await this.handleAuthentication(message, connection);
    });

    // 房间操作
    this.messageRouter.addRoute('room:join', async (message, connection) => {
      return await this.roomManager.joinRoom(
        connection,
        message.data.roomId,
        message.data.roomType
      );
    });

    this.messageRouter.addRoute('room:leave', async (message, connection) => {
      return await this.roomManager.leaveRoom(connection, message.data.roomId);
    });

    // 弹幕消息
    this.messageRouter.addRoute('danmaku:send', async (message, connection) => {
      return await this.handleDanmaku(message, connection);
    });

    // 预测游戏
    this.messageRouter.addRoute('prediction:submit', async (message, connection) => {
      return await this.handlePrediction(message, connection);
    });

    // 游戏状态同步
    this.messageRouter.addRoute('game:sync', async (message, connection) => {
      return await this.handleGameSync(message, connection);
    });
  }

  /**
   * 安全检查
   */
  async performSecurityCheck(event, context) {
    // 检查连接频率
    const clientIP = event.requestContext.identity.sourceIp;
    const connectionCount = await this.connectionManager.getConnectionCountByIP(clientIP);
    
    if (connectionCount > 10) { // 每个IP最多10个连接
      throw new Error('Too many connections from this IP');
    }

    // 检查User-Agent
    const userAgent = event.headers['User-Agent'] || '';
    if (!userAgent || userAgent.length < 10) {
      throw new Error('Invalid User-Agent');
    }
  }

  /**
   * 用户认证处理
   */
  async handleAuthentication(message, connection) {
    try {
      const { token } = message.data;
      
      // 验证JWT token
      const jwt = require('jsonwebtoken');
      const config = require('../../config');
      const payload = jwt.verify(token, config.jwt.accessSecret);

      // 更新连接信息
      await this.connectionManager.updateConnection(connection.id, {
        userId: payload.userId,
        authenticated: true,
        authTime: new Date().toISOString()
      });

      logger.info('WebSocket user authenticated', {
        connectionId: connection.id,
        userId: payload.userId
      });

      return {
        type: 'auth:success',
        data: {
          userId: payload.userId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      logger.warn('WebSocket authentication failed', {
        connectionId: connection.id,
        error: error.message
      });

      return {
        type: 'auth:failed',
        message: 'Authentication failed'
      };
    }
  }

  /**
   * 弹幕处理
   */
  async handleDanmaku(message, connection) {
    if (!connection.authenticated) {
      throw new Error('Authentication required');
    }

    const { roomId, content, color } = message.data;
    
    // 内容过滤
    const filteredContent = await this.filterContent(content);
    
    // 广播弹幕
    const danmakuMessage = {
      type: 'danmaku:message',
      data: {
        id: this.generateId(),
        userId: connection.userId,
        content: filteredContent,
        color: color || '#FFFFFF',
        timestamp: new Date().toISOString()
      }
    };

    await this.roomManager.broadcastToRoom(roomId, danmakuMessage, connection.id);

    return {
      type: 'danmaku:sent',
      data: { messageId: danmakuMessage.data.id }
    };
  }

  /**
   * 预测游戏处理
   */
  async handlePrediction(message, connection) {
    if (!connection.authenticated) {
      throw new Error('Authentication required');
    }

    const { roomId, questionId, prediction } = message.data;
    
    // 记录预测
    const predictionData = {
      userId: connection.userId,
      questionId,
      prediction,
      timestamp: new Date().toISOString()
    };

    await this.roomManager.recordPrediction(roomId, predictionData);

    return {
      type: 'prediction:recorded',
      data: { questionId, prediction }
    };
  }

  /**
   * 游戏状态同步处理
   */
  async handleGameSync(message, connection) {
    const { roomId, gameState } = message.data;
    
    // 广播游戏状态
    const syncMessage = {
      type: 'game:state',
      data: {
        ...gameState,
        timestamp: new Date().toISOString()
      }
    };

    await this.roomManager.broadcastToRoom(roomId, syncMessage, connection.id);

    return null; // 不需要响应
  }

  /**
   * 发送消息到指定连接
   */
  async sendMessage(connectionId, message) {
    const AWS = require('aws-sdk');
    const apiGateway = new AWS.ApiGatewayManagementApi({
      endpoint: process.env.WEBSOCKET_ENDPOINT
    });

    try {
      await apiGateway.postToConnection({
        ConnectionId: connectionId,
        Data: JSON.stringify(message)
      }).promise();

    } catch (error) {
      if (error.statusCode === 410) {
        // 连接已断开，清理连接
        await this.connectionManager.removeConnection(connectionId);
      }
      throw error;
    }
  }

  /**
   * 内容过滤
   */
  async filterContent(content) {
    // 简单的内容过滤，实际项目中可以集成更复杂的过滤系统
    const bannedWords = ['垃圾', '傻逼', '操你妈'];
    let filtered = content;
    
    bannedWords.forEach(word => {
      filtered = filtered.replace(new RegExp(word, 'gi'), '*'.repeat(word.length));
    });

    return filtered;
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// 创建处理器实例
const wsHandler = new WebSocketHandler();

// 导出Lambda处理函数
module.exports = {
  connect: errorHandler(wsHandler.handleConnect.bind(wsHandler)),
  disconnect: errorHandler(wsHandler.handleDisconnect.bind(wsHandler)),
  message: errorHandler(wsHandler.handleMessage.bind(wsHandler)),
  WebSocketHandler
};
