/**
 * WebSocket 房间管理器
 * 管理围观房间、弹幕、预测游戏等功能
 */

const { RedisManager } = require('../utils/redis');
const { logger } = require('../utils/logger');
const AWS = require('aws-sdk');

class RoomManager {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.roomPrefix = 'ws:room:';
    this.userRoomPrefix = 'ws:user_room:';
    this.predictionPrefix = 'ws:prediction:';
    
    // API Gateway管理API实例
    this.apiGateway = new AWS.ApiGatewayManagementApi({
      endpoint: process.env.WEBSOCKET_ENDPOINT
    });
  }

  /**
   * 创建房间
   */
  async createRoom(roomId, roomType, creatorId, gameData = {}) {
    const room = {
      id: roomId,
      type: roomType, // 'spectator', 'multiplayer', 'tournament'
      creatorId,
      createdAt: new Date().toISOString(),
      status: 'active',
      maxUsers: this.getMaxUsers(roomType),
      currentUsers: 0,
      gameData,
      settings: {
        allowDanmaku: true,
        allowPrediction: true,
        moderationEnabled: true
      },
      metadata: {}
    };

    try {
      await this.redis.setex(
        `${this.roomPrefix}${roomId}`,
        24 * 3600, // 24小时过期
        JSON.stringify(room)
      );

      logger.info('Room created', {
        roomId,
        roomType,
        creatorId
      });

      return room;

    } catch (error) {
      logger.error('Failed to create room', {
        roomId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取房间信息
   */
  async getRoom(roomId) {
    try {
      const data = await this.redis.get(`${this.roomPrefix}${roomId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Failed to get room', {
        roomId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 用户加入房间
   */
  async joinRoom(connection, roomId, roomType = 'spectator') {
    try {
      // 检查房间是否存在，不存在则创建
      let room = await this.getRoom(roomId);
      if (!room) {
        room = await this.createRoom(roomId, roomType, connection.userId);
      }

      // 检查房间容量
      if (room.currentUsers >= room.maxUsers) {
        throw new Error('Room is full');
      }

      // 检查用户是否已在房间中
      const isInRoom = await this.redis.sismember(
        `${this.roomPrefix}${roomId}:users`,
        connection.id
      );

      if (isInRoom) {
        return {
          type: 'room:already_joined',
          data: { roomId, roomInfo: room }
        };
      }

      // 添加用户到房间
      await this.redis.sadd(`${this.roomPrefix}${roomId}:users`, connection.id);
      await this.redis.expire(`${this.roomPrefix}${roomId}:users`, 24 * 3600);

      // 更新用户房间映射
      await this.redis.sadd(`${this.userRoomPrefix}${connection.id}`, roomId);
      await this.redis.expire(`${this.userRoomPrefix}${connection.id}`, 24 * 3600);

      // 更新房间用户数
      room.currentUsers++;
      await this.redis.setex(
        `${this.roomPrefix}${roomId}`,
        24 * 3600,
        JSON.stringify(room)
      );

      // 通知房间内其他用户
      await this.broadcastToRoom(roomId, {
        type: 'room:user_joined',
        data: {
          userId: connection.userId,
          connectionId: connection.id,
          timestamp: new Date().toISOString()
        }
      }, connection.id);

      logger.info('User joined room', {
        roomId,
        userId: connection.userId,
        connectionId: connection.id,
        currentUsers: room.currentUsers
      });

      return {
        type: 'room:joined',
        data: {
          roomId,
          roomInfo: room,
          userCount: room.currentUsers
        }
      };

    } catch (error) {
      logger.error('Failed to join room', {
        roomId,
        userId: connection.userId,
        error: error.message
      });

      return {
        type: 'room:join_failed',
        data: {
          roomId,
          error: error.message
        }
      };
    }
  }

  /**
   * 用户离开房间
   */
  async leaveRoom(connection, roomId) {
    try {
      const room = await this.getRoom(roomId);
      if (!room) {
        return {
          type: 'room:not_found',
          data: { roomId }
        };
      }

      // 从房间移除用户
      await this.redis.srem(`${this.roomPrefix}${roomId}:users`, connection.id);
      await this.redis.srem(`${this.userRoomPrefix}${connection.id}`, roomId);

      // 更新房间用户数
      room.currentUsers = Math.max(0, room.currentUsers - 1);
      await this.redis.setex(
        `${this.roomPrefix}${roomId}`,
        24 * 3600,
        JSON.stringify(room)
      );

      // 通知房间内其他用户
      await this.broadcastToRoom(roomId, {
        type: 'room:user_left',
        data: {
          userId: connection.userId,
          connectionId: connection.id,
          timestamp: new Date().toISOString()
        }
      }, connection.id);

      // 如果房间为空且不是持久房间，则删除房间
      if (room.currentUsers === 0 && room.type !== 'persistent') {
        await this.deleteRoom(roomId);
      }

      logger.info('User left room', {
        roomId,
        userId: connection.userId,
        connectionId: connection.id,
        remainingUsers: room.currentUsers
      });

      return {
        type: 'room:left',
        data: {
          roomId,
          userCount: room.currentUsers
        }
      };

    } catch (error) {
      logger.error('Failed to leave room', {
        roomId,
        userId: connection.userId,
        error: error.message
      });

      return {
        type: 'room:leave_failed',
        data: {
          roomId,
          error: error.message
        }
      };
    }
  }

  /**
   * 处理用户断开连接
   */
  async handleUserDisconnect(connectionId) {
    try {
      // 获取用户所在的所有房间
      const roomIds = await this.redis.smembers(`${this.userRoomPrefix}${connectionId}`);

      for (const roomId of roomIds) {
        // 从房间移除用户
        await this.redis.srem(`${this.roomPrefix}${roomId}:users`, connectionId);

        // 更新房间用户数
        const room = await this.getRoom(roomId);
        if (room) {
          room.currentUsers = Math.max(0, room.currentUsers - 1);
          await this.redis.setex(
            `${this.roomPrefix}${roomId}`,
            24 * 3600,
            JSON.stringify(room)
          );

          // 通知房间内其他用户
          await this.broadcastToRoom(roomId, {
            type: 'room:user_disconnected',
            data: {
              connectionId,
              timestamp: new Date().toISOString()
            }
          }, connectionId);

          // 如果房间为空，删除房间
          if (room.currentUsers === 0 && room.type !== 'persistent') {
            await this.deleteRoom(roomId);
          }
        }
      }

      // 清理用户房间映射
      await this.redis.del(`${this.userRoomPrefix}${connectionId}`);

      logger.info('User disconnected from all rooms', {
        connectionId,
        roomCount: roomIds.length
      });

    } catch (error) {
      logger.error('Failed to handle user disconnect', {
        connectionId,
        error: error.message
      });
    }
  }

  /**
   * 向房间广播消息
   */
  async broadcastToRoom(roomId, message, excludeConnectionId = null) {
    try {
      const connectionIds = await this.redis.smembers(`${this.roomPrefix}${roomId}:users`);
      const filteredConnectionIds = excludeConnectionId 
        ? connectionIds.filter(id => id !== excludeConnectionId)
        : connectionIds;

      const messageData = JSON.stringify(message);
      const promises = filteredConnectionIds.map(connectionId => 
        this.sendMessageToConnection(connectionId, messageData)
      );

      const results = await Promise.allSettled(promises);
      
      // 统计发送结果
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      logger.debug('Broadcast message to room', {
        roomId,
        messageType: message.type,
        totalConnections: filteredConnectionIds.length,
        successful,
        failed
      });

      // 清理失效的连接
      if (failed > 0) {
        await this.cleanupInvalidConnections(roomId, results, filteredConnectionIds);
      }

    } catch (error) {
      logger.error('Failed to broadcast to room', {
        roomId,
        messageType: message.type,
        error: error.message
      });
    }
  }

  /**
   * 发送消息到指定连接
   */
  async sendMessageToConnection(connectionId, messageData) {
    try {
      await this.apiGateway.postToConnection({
        ConnectionId: connectionId,
        Data: messageData
      }).promise();

    } catch (error) {
      if (error.statusCode === 410) {
        // 连接已断开，需要清理
        throw new Error(`Connection ${connectionId} is stale`);
      }
      throw error;
    }
  }

  /**
   * 清理无效连接
   */
  async cleanupInvalidConnections(roomId, results, connectionIds) {
    const invalidConnections = [];
    
    results.forEach((result, index) => {
      if (result.status === 'rejected' && 
          result.reason.message.includes('is stale')) {
        invalidConnections.push(connectionIds[index]);
      }
    });

    for (const connectionId of invalidConnections) {
      await this.redis.srem(`${this.roomPrefix}${roomId}:users`, connectionId);
      await this.redis.srem(`${this.userRoomPrefix}${connectionId}`, roomId);
    }

    if (invalidConnections.length > 0) {
      // 更新房间用户数
      const room = await this.getRoom(roomId);
      if (room) {
        room.currentUsers = Math.max(0, room.currentUsers - invalidConnections.length);
        await this.redis.setex(
          `${this.roomPrefix}${roomId}`,
          24 * 3600,
          JSON.stringify(room)
        );
      }

      logger.info('Cleaned up invalid connections', {
        roomId,
        cleanedCount: invalidConnections.length
      });
    }
  }

  /**
   * 记录预测
   */
  async recordPrediction(roomId, predictionData) {
    try {
      const key = `${this.predictionPrefix}${roomId}:${predictionData.questionId}:${predictionData.userId}`;
      
      await this.redis.setex(
        key,
        3600, // 1小时过期
        JSON.stringify(predictionData)
      );

      logger.debug('Prediction recorded', {
        roomId,
        userId: predictionData.userId,
        questionId: predictionData.questionId
      });

    } catch (error) {
      logger.error('Failed to record prediction', {
        roomId,
        predictionData,
        error: error.message
      });
    }
  }

  /**
   * 获取房间预测统计
   */
  async getRoomPredictionStats(roomId, questionId) {
    try {
      const pattern = `${this.predictionPrefix}${roomId}:${questionId}:*`;
      const keys = await this.redis.keys(pattern);
      
      const predictions = [];
      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          predictions.push(JSON.parse(data));
        }
      }

      // 统计预测结果
      const stats = {};
      predictions.forEach(prediction => {
        const answer = prediction.prediction;
        stats[answer] = (stats[answer] || 0) + 1;
      });

      return {
        total: predictions.length,
        distribution: stats,
        predictions
      };

    } catch (error) {
      logger.error('Failed to get prediction stats', {
        roomId,
        questionId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 删除房间
   */
  async deleteRoom(roomId) {
    try {
      await this.redis.del(`${this.roomPrefix}${roomId}`);
      await this.redis.del(`${this.roomPrefix}${roomId}:users`);
      
      logger.info('Room deleted', { roomId });

    } catch (error) {
      logger.error('Failed to delete room', {
        roomId,
        error: error.message
      });
    }
  }

  /**
   * 获取房间类型的最大用户数
   */
  getMaxUsers(roomType) {
    const limits = {
      'spectator': 1000,    // 围观房间
      'multiplayer': 10,    // 多人游戏
      'tournament': 100,    // 锦标赛
      'persistent': 5000    // 持久房间
    };

    return limits[roomType] || 100;
  }

  /**
   * 获取房间列表
   */
  async getRoomList(roomType = null, limit = 50) {
    try {
      const pattern = `${this.roomPrefix}*`;
      const keys = await this.redis.keys(pattern);
      const rooms = [];

      for (const key of keys.slice(0, limit)) {
        const data = await this.redis.get(key);
        if (data) {
          const room = JSON.parse(data);
          if (!roomType || room.type === roomType) {
            rooms.push(room);
          }
        }
      }

      return rooms.sort((a, b) => b.currentUsers - a.currentUsers);

    } catch (error) {
      logger.error('Failed to get room list', {
        error: error.message
      });
      return [];
    }
  }
}

module.exports = { RoomManager };
