/**
 * 性能监控和告警系统
 * 实时监控系统性能指标并触发告警
 */

const { logger } = require('./logger');
const { RedisManager } = require('./redis');
const config = require('../../config');

class PerformanceMonitor {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.metrics = new Map();
    this.alerts = new Map();
    this.thresholds = {
      responseTime: 2000,      // 2秒响应时间阈值
      errorRate: 0.05,         // 5%错误率阈值
      memoryUsage: 0.8,        // 80%内存使用率阈值
      cpuUsage: 0.8,           // 80%CPU使用率阈值
      dbConnectionPool: 0.9,   // 90%数据库连接池使用率阈值
      queueLength: 100         // 队列长度阈值
    };
    
    // 启动监控
    this.startMonitoring();
  }

  /**
   * 启动性能监控
   */
  startMonitoring() {
    // 每30秒收集一次系统指标
    setInterval(() => {
      this.collectSystemMetrics().catch(error => {
        logger.error('Failed to collect system metrics', { error: error.message });
      });
    }, 30000);

    // 每分钟分析性能趋势
    setInterval(() => {
      this.analyzePerformanceTrends().catch(error => {
        logger.error('Failed to analyze performance trends', { error: error.message });
      });
    }, 60000);

    // 每5分钟清理过期指标
    setInterval(() => {
      this.cleanupExpiredMetrics();
    }, 5 * 60000);
  }

  /**
   * 记录API请求指标
   */
  async recordApiMetrics(event, context, response, startTime) {
    const duration = Date.now() - startTime;
    const timestamp = new Date().toISOString();
    
    const metrics = {
      timestamp,
      requestId: context.requestId,
      method: event.httpMethod,
      path: this.normalizePath(event.path),
      statusCode: response.statusCode,
      duration,
      userId: event.user?.id,
      ip: this.getClientIP(event),
      userAgent: event.headers['user-agent'],
      responseSize: response.body ? Buffer.byteLength(response.body, 'utf8') : 0
    };

    // 存储到Redis
    await this.storeMetrics('api_requests', metrics);

    // 实时性能分析
    await this.analyzeRealTimePerformance(metrics);

    // 记录到日志
    logger.logMetrics(metrics);
  }

  /**
   * 记录数据库操作指标
   */
  async recordDatabaseMetrics(operation, table, duration, success = true, error = null) {
    const metrics = {
      timestamp: new Date().toISOString(),
      operation,
      table,
      duration,
      success,
      error: error?.message
    };

    await this.storeMetrics('db_operations', metrics);

    // 检查慢查询
    if (duration > this.thresholds.responseTime) {
      await this.triggerAlert('slow_query', {
        operation,
        table,
        duration,
        threshold: this.thresholds.responseTime
      });
    }
  }

  /**
   * 记录缓存操作指标
   */
  async recordCacheMetrics(operation, key, hit = null, duration = null) {
    const metrics = {
      timestamp: new Date().toISOString(),
      operation,
      key: this.maskSensitiveKey(key),
      hit,
      duration
    };

    await this.storeMetrics('cache_operations', metrics);
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics() {
    const metrics = {
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };

    // 添加自定义指标
    metrics.custom = await this.collectCustomMetrics();

    await this.storeMetrics('system_metrics', metrics);

    // 检查系统资源使用率
    await this.checkSystemThresholds(metrics);
  }

  /**
   * 收集自定义指标
   */
  async collectCustomMetrics() {
    try {
      const custom = {};

      // 数据库连接池状态
      const dbManager = require('./database');
      if (dbManager.pool) {
        const poolInfo = dbManager.getPoolInfo();
        custom.dbPool = poolInfo;
        
        // 检查连接池使用率
        if (poolInfo.activeConnections && poolInfo.connectionLimit) {
          const usage = poolInfo.activeConnections / poolInfo.connectionLimit;
          if (usage > this.thresholds.dbConnectionPool) {
            await this.triggerAlert('high_db_pool_usage', {
              usage: `${(usage * 100).toFixed(2)}%`,
              active: poolInfo.activeConnections,
              limit: poolInfo.connectionLimit
            });
          }
        }
      }

      // Redis连接状态
      if (this.redis.client) {
        custom.redis = {
          status: this.redis.client.status,
          commandsProcessed: this.redis.client.commandQueueLength || 0
        };
      }

      // 应用级指标
      custom.app = {
        activeRequests: this.getActiveRequestCount(),
        totalRequests: this.getTotalRequestCount(),
        errorCount: this.getErrorCount()
      };

      return custom;
    } catch (error) {
      logger.error('Failed to collect custom metrics', { error: error.message });
      return {};
    }
  }

  /**
   * 实时性能分析
   */
  async analyzeRealTimePerformance(metrics) {
    // 检查响应时间
    if (metrics.duration > this.thresholds.responseTime) {
      await this.triggerAlert('slow_response', {
        path: metrics.path,
        duration: metrics.duration,
        threshold: this.thresholds.responseTime,
        requestId: metrics.requestId
      });
    }

    // 检查错误率
    if (metrics.statusCode >= 500) {
      await this.incrementErrorCount();
      
      const errorRate = await this.calculateErrorRate();
      if (errorRate > this.thresholds.errorRate) {
        await this.triggerAlert('high_error_rate', {
          errorRate: `${(errorRate * 100).toFixed(2)}%`,
          threshold: `${(this.thresholds.errorRate * 100).toFixed(2)}%`
        });
      }
    }
  }

  /**
   * 分析性能趋势
   */
  async analyzePerformanceTrends() {
    try {
      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;

      // 获取最近一小时的指标
      const metrics = await this.getMetricsInRange('api_requests', oneHourAgo, now);
      
      if (metrics.length === 0) return;

      // 计算平均响应时间
      const avgResponseTime = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
      
      // 计算错误率
      const errorCount = metrics.filter(m => m.statusCode >= 400).length;
      const errorRate = errorCount / metrics.length;

      // 计算QPS
      const qps = metrics.length / 3600; // 每秒请求数

      const trendData = {
        timestamp: new Date().toISOString(),
        avgResponseTime,
        errorRate,
        qps,
        totalRequests: metrics.length,
        errorCount
      };

      await this.storeMetrics('performance_trends', trendData);

      // 检查趋势告警
      await this.checkTrendAlerts(trendData);

    } catch (error) {
      logger.error('Failed to analyze performance trends', { error: error.message });
    }
  }

  /**
   * 检查趋势告警
   */
  async checkTrendAlerts(trendData) {
    // 响应时间趋势告警
    if (trendData.avgResponseTime > this.thresholds.responseTime * 0.8) {
      await this.triggerAlert('response_time_trend', {
        avgResponseTime: trendData.avgResponseTime,
        threshold: this.thresholds.responseTime
      });
    }

    // 错误率趋势告警
    if (trendData.errorRate > this.thresholds.errorRate * 0.8) {
      await this.triggerAlert('error_rate_trend', {
        errorRate: `${(trendData.errorRate * 100).toFixed(2)}%`,
        threshold: `${(this.thresholds.errorRate * 100).toFixed(2)}%`
      });
    }

    // QPS异常告警
    const expectedQps = await this.getExpectedQps();
    if (trendData.qps > expectedQps * 1.5 || trendData.qps < expectedQps * 0.5) {
      await this.triggerAlert('qps_anomaly', {
        currentQps: trendData.qps.toFixed(2),
        expectedQps: expectedQps.toFixed(2)
      });
    }
  }

  /**
   * 触发告警
   */
  async triggerAlert(alertType, data) {
    const alertKey = `alert:${alertType}`;
    const lastAlert = await this.redis.get(alertKey);
    
    // 防止重复告警（5分钟内不重复）
    if (lastAlert && Date.now() - parseInt(lastAlert) < 5 * 60 * 1000) {
      return;
    }

    const alert = {
      type: alertType,
      timestamp: new Date().toISOString(),
      data,
      severity: this.getAlertSeverity(alertType),
      environment: config.env
    };

    // 记录告警
    await this.redis.setex(alertKey, 300, Date.now().toString()); // 5分钟
    await this.storeMetrics('alerts', alert);

    // 发送告警通知
    await this.sendAlertNotification(alert);

    logger.warn('Performance alert triggered', alert);
  }

  /**
   * 发送告警通知
   */
  async sendAlertNotification(alert) {
    try {
      // 这里可以集成各种通知渠道
      // 例如：企业微信、钉钉、邮件、短信等
      
      if (config.alerts?.webhook) {
        // 发送到Webhook
        await this.sendWebhookAlert(alert);
      }

      if (config.alerts?.email && alert.severity === 'critical') {
        // 发送邮件告警
        await this.sendEmailAlert(alert);
      }

    } catch (error) {
      logger.error('Failed to send alert notification', { 
        error: error.message,
        alert 
      });
    }
  }

  /**
   * 存储指标数据
   */
  async storeMetrics(type, data) {
    try {
      const key = `metrics:${type}:${Date.now()}`;
      await this.redis.setex(key, 24 * 3600, JSON.stringify(data)); // 保留24小时
    } catch (error) {
      logger.error('Failed to store metrics', { error: error.message, type });
    }
  }

  /**
   * 获取指定时间范围内的指标
   */
  async getMetricsInRange(type, startTime, endTime) {
    try {
      const pattern = `metrics:${type}:*`;
      const keys = await this.redis.keys(pattern);
      
      const metrics = [];
      for (const key of keys) {
        const timestamp = parseInt(key.split(':')[2]);
        if (timestamp >= startTime && timestamp <= endTime) {
          const data = await this.redis.get(key);
          if (data) {
            metrics.push(JSON.parse(data));
          }
        }
      }
      
      return metrics.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    } catch (error) {
      logger.error('Failed to get metrics in range', { error: error.message, type });
      return [];
    }
  }

  /**
   * 工具方法
   */
  normalizePath(path) {
    return path?.replace(/\/\d+/g, '/{id}')
               .replace(/\/[a-fA-F0-9-]{36}/g, '/{uuid}') || '/';
  }

  getClientIP(event) {
    return event.headers['x-forwarded-for'] ||
           event.requestContext?.identity?.sourceIp ||
           'unknown';
  }

  maskSensitiveKey(key) {
    const sensitivePatterns = ['token', 'secret', 'password', 'key'];
    if (sensitivePatterns.some(pattern => key.toLowerCase().includes(pattern))) {
      return key.substring(0, 8) + '***';
    }
    return key;
  }

  getAlertSeverity(alertType) {
    const severityMap = {
      slow_response: 'warning',
      slow_query: 'warning',
      high_error_rate: 'critical',
      high_db_pool_usage: 'warning',
      response_time_trend: 'warning',
      error_rate_trend: 'critical',
      qps_anomaly: 'info'
    };
    return severityMap[alertType] || 'info';
  }

  // 模拟方法，实际实现需要根据具体需求
  getActiveRequestCount() { return 0; }
  getTotalRequestCount() { return 0; }
  getErrorCount() { return 0; }
  async incrementErrorCount() { }
  async calculateErrorRate() { return 0; }
  async getExpectedQps() { return 10; }
  async sendWebhookAlert(alert) { }
  async sendEmailAlert(alert) { }
  
  cleanupExpiredMetrics() {
    // 清理过期指标的实现
  }

  async checkSystemThresholds(metrics) {
    // 检查系统阈值的实现
  }
}

// 创建全局监控实例
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};
