/**
 * 错误定义和处理工具
 * 统一错误码和错误处理机制
 */

// 错误码定义
const ERROR_CODES = {
  // 通用错误 (1000-1099)
  INTERNAL_ERROR: { code: 1000, message: '服务器内部错误' },
  INVALID_PARAMS: { code: 1001, message: '参数验证失败' },
  RESOURCE_NOT_FOUND: { code: 1004, message: '资源不存在' },
  
  // 认证错误 (2000-2099)
  UNAUTHORIZED: { code: 2001, message: '未授权访问' },
  INVALID_TOKEN: { code: 2002, message: '无效的访问令牌' },
  TOKEN_EXPIRED: { code: 2003, message: '访问令牌已过期' },
  INSUFFICIENT_PERMISSIONS: { code: 2004, message: '权限不足' },
  INVALID_WECHAT_CODE: { code: 2005, message: '微信授权码无效' },
  WECHAT_API_ERROR: { code: 2006, message: '微信接口调用失败' },
  
  // 业务错误 (3000-3099)
  USER_NOT_FOUND: { code: 3001, message: '用户不存在' },
  GAME_SESSION_EXPIRED: { code: 3002, message: '游戏会话已过期' },
  QUESTION_NOT_AVAILABLE: { code: 3003, message: '题目不可用' },
  INVALID_ANSWER: { code: 3004, message: '无效的答案格式' },
  GAME_SESSION_NOT_FOUND: { code: 3005, message: '游戏会话不存在' },
  
  // 限流错误 (4000-4099)
  RATE_LIMIT_EXCEEDED: { code: 4001, message: '请求频率过高' },
  DAILY_LIMIT_EXCEEDED: { code: 4002, message: '今日游戏次数已达上限' },
  
  // 第三方服务错误 (5000-5099)
  WECHAT_LOGIN_FAILED: { code: 5001, message: '微信登录失败' },
  PAYMENT_ERROR: { code: 5002, message: '支付处理失败' },
  COS_UPLOAD_ERROR: { code: 5003, message: '文件上传失败' }
};

/**
 * 自定义API错误类
 */
class APIError extends Error {
  constructor(errorCode, message = null, details = null, statusCode = 400) {
    const errorInfo = ERROR_CODES[errorCode] || ERROR_CODES.INTERNAL_ERROR;
    super(message || errorInfo.message);
    
    this.name = 'APIError';
    this.code = errorCode;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }

  /**
   * 转换为API响应格式
   */
  toResponse(requestId = null) {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      requestId
    };
  }

  /**
   * 是否为客户端错误
   */
  isClientError() {
    return this.statusCode >= 400 && this.statusCode < 500;
  }

  /**
   * 是否为服务器错误
   */
  isServerError() {
    return this.statusCode >= 500;
  }
}

/**
 * 验证错误类
 */
class ValidationError extends APIError {
  constructor(details) {
    super('INVALID_PARAMS', '参数验证失败', details, 400);
    this.name = 'ValidationError';
  }
}

/**
 * 认证错误类
 */
class AuthError extends APIError {
  constructor(code, message) {
    super(code, message, null, 401);
    this.name = 'AuthError';
  }
}

/**
 * 权限错误类
 */
class ForbiddenError extends APIError {
  constructor(message = '访问被拒绝') {
    super('ACCESS_DENIED', message, null, 403);
    this.name = 'ForbiddenError';
  }
}

/**
 * 资源不存在错误类
 */
class NotFoundError extends APIError {
  constructor(resource = '资源') {
    super('RESOURCE_NOT_FOUND', `${resource}不存在`, null, 404);
    this.name = 'NotFoundError';
  }
}

/**
 * 限流错误类
 */
class RateLimitError extends APIError {
  constructor(message = '请求频率过高', retryAfter = 60) {
    super('RATE_LIMIT_EXCEEDED', message, { retryAfter }, 429);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

/**
 * 业务逻辑错误类
 */
class BusinessError extends APIError {
  constructor(code, message, details = null) {
    super(code, message, details, 400);
    this.name = 'BusinessError';
  }
}

/**
 * 第三方服务错误类
 */
class ExternalServiceError extends APIError {
  constructor(service, message, statusCode = 500) {
    super(`${service.toUpperCase()}_ERROR`, message, null, statusCode);
    this.name = 'ExternalServiceError';
    this.service = service;
  }
}

/**
 * 错误工厂函数
 */
const createError = {
  // 通用错误
  internal: (message = null) => new APIError('INTERNAL_ERROR', message, null, 500),
  invalidParams: (details = null) => new ValidationError(details),
  notFound: (resource = '资源') => new NotFoundError(resource),
  
  // 认证错误
  unauthorized: (message = null) => new AuthError('UNAUTHORIZED', message),
  invalidToken: (message = null) => new AuthError('INVALID_TOKEN', message),
  tokenExpired: (message = null) => new AuthError('TOKEN_EXPIRED', message),
  forbidden: (message = null) => new ForbiddenError(message),
  
  // 限流错误
  rateLimit: (message = null, retryAfter = 60) => new RateLimitError(message, retryAfter),
  dailyLimit: (message = null) => new APIError('DAILY_LIMIT_EXCEEDED', message, null, 429),
  
  // 业务错误
  business: (code, message, details = null) => new BusinessError(code, message, details),
  
  // 第三方服务错误
  wechat: (message = null) => new ExternalServiceError('wechat', message || '微信服务异常'),
  cos: (message = null) => new ExternalServiceError('cos', message || '文件服务异常')
};

/**
 * 错误处理工具函数
 */
const errorUtils = {
  /**
   * 判断错误类型
   */
  isAPIError: (error) => error instanceof APIError,
  isValidationError: (error) => error instanceof ValidationError,
  isAuthError: (error) => error instanceof AuthError,
  isRateLimitError: (error) => error instanceof RateLimitError,
  
  /**
   * 格式化错误响应
   */
  formatErrorResponse: (error, requestId = null) => {
    if (error instanceof APIError) {
      return {
        statusCode: error.statusCode,
        body: error.toResponse(requestId)
      };
    }
    
    // 处理其他类型的错误
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error.name === 'ValidationError') {
      statusCode = 400;
      errorCode = 'INVALID_PARAMS';
      message = error.message;
    } else if (error.message) {
      message = error.message;
    }
    
    return {
      statusCode,
      body: {
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId
      }
    };
  },
  
  /**
   * 记录错误日志
   */
  logError: (error, context = {}) => {
    const errorInfo = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode,
      timestamp: new Date().toISOString(),
      context
    };
    
    if (error.statusCode >= 500) {
      console.error('Server Error:', errorInfo);
    } else {
      console.warn('Client Error:', errorInfo);
    }
  },
  
  /**
   * 包装异步函数，自动处理错误
   */
  asyncWrapper: (fn) => {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        errorUtils.logError(error, { args });
        throw error;
      }
    };
  },
  
  /**
   * 重试机制
   */
  withRetry: async (fn, maxRetries = 3, delay = 1000) => {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // 不重试客户端错误
        if (error instanceof APIError && error.isClientError()) {
          throw error;
        }
        
        // 最后一次重试失败
        if (i === maxRetries) {
          break;
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
    
    throw lastError;
  }
};

module.exports = {
  ERROR_CODES,
  APIError,
  ValidationError,
  AuthError,
  ForbiddenError,
  NotFoundError,
  RateLimitError,
  BusinessError,
  ExternalServiceError,
  createError,
  errorUtils
};