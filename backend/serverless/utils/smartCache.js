/**
 * 智能缓存管理服务
 * 实现多层缓存、智能预加载、缓存预热、自适应TTL等高级缓存策略
 */

const { RedisManager } = require('./redis');
const { logger } = require('./logger');
const { performanceMonitor } = require('./monitoring');

class SmartCacheManager {
  constructor() {
    this.redis = RedisManager.getInstance();
    
    // 缓存层级配置
    this.cacheLayers = {
      L1: { // 内存缓存 - 最快
        maxSize: 1000,
        ttl: 300, // 5分钟
        storage: new Map()
      },
      L2: { // Redis缓存 - 中等速度
        ttl: 3600, // 1小时
        prefix: 'l2:'
      },
      L3: { // 持久化缓存 - 较慢但持久
        ttl: 86400, // 24小时
        prefix: 'l3:'
      }
    };

    // 缓存策略配置
    this.strategies = {
      // 热点数据策略
      hotData: {
        layers: ['L1', 'L2'],
        preloadThreshold: 10, // 访问次数阈值
        ttlMultiplier: 2
      },
      // 用户数据策略
      userData: {
        layers: ['L1', 'L2'],
        personalizedTTL: true,
        invalidateOnUpdate: true
      },
      // 静态内容策略
      staticContent: {
        layers: ['L2', 'L3'],
        longTTL: true,
        compressionEnabled: true
      },
      // 实时数据策略
      realTimeData: {
        layers: ['L1'],
        shortTTL: true,
        autoRefresh: true
      }
    };

    // 预加载配置
    this.preloadConfig = {
      enabled: true,
      batchSize: 50,
      concurrency: 5,
      scheduleInterval: 300000, // 5分钟
      patterns: [
        'popular_content',
        'user_preferences',
        'game_questions',
        'dialect_regions'
      ]
    };

    // 统计信息
    this.stats = {
      hits: { L1: 0, L2: 0, L3: 0 },
      misses: { L1: 0, L2: 0, L3: 0 },
      preloads: 0,
      evictions: 0,
      errors: 0
    };

    // 访问频率统计
    this.accessFrequency = new Map();
    
    // 启动预加载调度器
    this.startPreloadScheduler();
  }

  /**
   * 智能获取缓存数据
   */
  async smartGet(key, strategy = 'default', options = {}) {
    const startTime = Date.now();
    
    try {
      // 记录访问频率
      this.recordAccess(key);
      
      // 根据策略选择缓存层
      const layers = this.getCacheLayers(strategy);
      
      // 逐层查找
      for (const layer of layers) {
        const result = await this.getFromLayer(key, layer);
        if (result !== null) {
          this.stats.hits[layer]++;
          
          // 如果是从L2或L3获取，考虑提升到L1
          if (layer !== 'L1' && this.shouldPromoteToL1(key)) {
            await this.setToLayer(key, result, 'L1');
          }
          
          await this.recordCacheHit(key, layer, Date.now() - startTime);
          return result;
        }
        this.stats.misses[layer]++;
      }

      return null;

    } catch (error) {
      this.stats.errors++;
      logger.error('Smart cache get failed', {
        key,
        strategy,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 智能设置缓存数据
   */
  async smartSet(key, value, strategy = 'default', options = {}) {
    try {
      const layers = this.getCacheLayers(strategy);
      const ttl = this.calculateTTL(key, strategy, options);
      
      // 并行写入多个缓存层
      const setPromises = layers.map(layer => 
        this.setToLayer(key, value, layer, ttl[layer])
      );
      
      await Promise.allSettled(setPromises);
      
      // 记录缓存设置
      await this.recordCacheSet(key, strategy, layers);
      
      return true;

    } catch (error) {
      this.stats.errors++;
      logger.error('Smart cache set failed', {
        key,
        strategy,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 智能删除缓存
   */
  async smartDelete(key, strategy = 'default') {
    try {
      const layers = this.getCacheLayers(strategy);
      
      // 从所有层删除
      const deletePromises = layers.map(layer => 
        this.deleteFromLayer(key, layer)
      );
      
      await Promise.allSettled(deletePromises);
      
      // 清除访问频率记录
      this.accessFrequency.delete(key);
      
      return true;

    } catch (error) {
      this.stats.errors++;
      logger.error('Smart cache delete failed', {
        key,
        strategy,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 批量预加载
   */
  async batchPreload(keys, dataLoader, strategy = 'default') {
    const startTime = Date.now();
    
    try {
      // 检查哪些键需要预加载
      const keysToLoad = [];
      for (const key of keys) {
        const cached = await this.smartGet(key, strategy);
        if (cached === null) {
          keysToLoad.push(key);
        }
      }

      if (keysToLoad.length === 0) {
        return { loaded: 0, cached: keys.length };
      }

      // 分批加载数据
      const batches = this.chunkArray(keysToLoad, this.preloadConfig.batchSize);
      let loadedCount = 0;

      for (const batch of batches) {
        const batchPromises = batch.map(async (key) => {
          try {
            const data = await dataLoader(key);
            if (data !== null) {
              await this.smartSet(key, data, strategy);
              loadedCount++;
            }
          } catch (error) {
            logger.error('Preload failed for key', {
              key,
              error: error.message
            });
          }
        });

        await Promise.allSettled(batchPromises);
      }

      this.stats.preloads += loadedCount;
      
      logger.info('Batch preload completed', {
        totalKeys: keys.length,
        loadedKeys: loadedCount,
        duration: Date.now() - startTime
      });

      return { loaded: loadedCount, cached: keys.length - keysToLoad.length };

    } catch (error) {
      logger.error('Batch preload failed', {
        error: error.message
      });
      return { loaded: 0, cached: 0 };
    }
  }

  /**
   * 智能缓存预热
   */
  async warmupCache() {
    logger.info('Starting intelligent cache warmup');
    
    const warmupTasks = [
      this.warmupPopularContent(),
      this.warmupUserPreferences(),
      this.warmupGameQuestions(),
      this.warmupDialectRegions(),
      this.warmupStaticAssets()
    ];

    const results = await Promise.allSettled(warmupTasks);
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    logger.info('Cache warmup completed', {
      successful,
      failed,
      total: warmupTasks.length
    });

    return { successful, failed };
  }

  /**
   * 预热热门内容
   */
  async warmupPopularContent() {
    const popularKeys = await this.getPopularContentKeys();
    
    return await this.batchPreload(
      popularKeys,
      async (key) => {
        // 这里应该调用实际的数据加载函数
        return await this.loadContentData(key);
      },
      'hotData'
    );
  }

  /**
   * 预热用户偏好
   */
  async warmupUserPreferences() {
    const activeUserIds = await this.getActiveUserIds();
    const preferenceKeys = activeUserIds.map(id => `user_preferences:${id}`);
    
    return await this.batchPreload(
      preferenceKeys,
      async (key) => {
        const userId = key.split(':')[1];
        return await this.loadUserPreferences(userId);
      },
      'userData'
    );
  }

  /**
   * 预热游戏问题
   */
  async warmupGameQuestions() {
    const questionKeys = await this.getPopularQuestionKeys();
    
    return await this.batchPreload(
      questionKeys,
      async (key) => {
        return await this.loadQuestionData(key);
      },
      'staticContent'
    );
  }

  /**
   * 预热方言地区数据
   */
  async warmupDialectRegions() {
    const regionKeys = await this.getDialectRegionKeys();
    
    return await this.batchPreload(
      regionKeys,
      async (key) => {
        return await this.loadDialectRegionData(key);
      },
      'staticContent'
    );
  }

  /**
   * 预热静态资源
   */
  async warmupStaticAssets() {
    const assetKeys = [
      'app_config',
      'language_list',
      'difficulty_levels',
      'game_categories'
    ];
    
    return await this.batchPreload(
      assetKeys,
      async (key) => {
        return await this.loadStaticAsset(key);
      },
      'staticContent'
    );
  }

  /**
   * 自适应TTL计算
   */
  calculateTTL(key, strategy, options = {}) {
    const baseConfig = this.strategies[strategy] || this.strategies.default;
    const accessCount = this.accessFrequency.get(key) || 0;
    
    const ttls = {};
    
    for (const layer of this.getCacheLayers(strategy)) {
      let baseTTL = this.cacheLayers[layer].ttl;
      
      // 根据访问频率调整TTL
      if (accessCount > 10) {
        baseTTL *= (baseConfig.ttlMultiplier || 1.5);
      }
      
      // 个性化TTL
      if (baseConfig.personalizedTTL && key.includes('user:')) {
        baseTTL *= 0.8; // 用户数据TTL稍短
      }
      
      // 长TTL策略
      if (baseConfig.longTTL) {
        baseTTL *= 2;
      }
      
      // 短TTL策略
      if (baseConfig.shortTTL) {
        baseTTL *= 0.5;
      }
      
      // 自定义TTL
      if (options.customTTL) {
        baseTTL = options.customTTL;
      }
      
      ttls[layer] = Math.max(60, Math.min(baseTTL, 86400)); // 1分钟到24小时
    }
    
    return ttls;
  }

  /**
   * 从指定层获取数据
   */
  async getFromLayer(key, layer) {
    try {
      switch (layer) {
        case 'L1':
          return this.cacheLayers.L1.storage.get(key) || null;
          
        case 'L2':
          const l2Key = this.cacheLayers.L2.prefix + key;
          const l2Data = await this.redis.get(l2Key);
          return l2Data ? JSON.parse(l2Data) : null;
          
        case 'L3':
          const l3Key = this.cacheLayers.L3.prefix + key;
          const l3Data = await this.redis.get(l3Key);
          return l3Data ? JSON.parse(l3Data) : null;
          
        default:
          return null;
      }
    } catch (error) {
      logger.error(`Failed to get from layer ${layer}`, {
        key,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 向指定层设置数据
   */
  async setToLayer(key, value, layer, ttl) {
    try {
      switch (layer) {
        case 'L1':
          // L1缓存大小限制
          if (this.cacheLayers.L1.storage.size >= this.cacheLayers.L1.maxSize) {
            this.evictFromL1();
          }
          this.cacheLayers.L1.storage.set(key, value);
          // L1缓存过期处理
          setTimeout(() => {
            this.cacheLayers.L1.storage.delete(key);
          }, (ttl || this.cacheLayers.L1.ttl) * 1000);
          break;
          
        case 'L2':
          const l2Key = this.cacheLayers.L2.prefix + key;
          await this.redis.setex(l2Key, ttl || this.cacheLayers.L2.ttl, JSON.stringify(value));
          break;
          
        case 'L3':
          const l3Key = this.cacheLayers.L3.prefix + key;
          await this.redis.setex(l3Key, ttl || this.cacheLayers.L3.ttl, JSON.stringify(value));
          break;
      }
    } catch (error) {
      logger.error(`Failed to set to layer ${layer}`, {
        key,
        error: error.message
      });
    }
  }

  /**
   * 从指定层删除数据
   */
  async deleteFromLayer(key, layer) {
    try {
      switch (layer) {
        case 'L1':
          this.cacheLayers.L1.storage.delete(key);
          break;
          
        case 'L2':
          const l2Key = this.cacheLayers.L2.prefix + key;
          await this.redis.del(l2Key);
          break;
          
        case 'L3':
          const l3Key = this.cacheLayers.L3.prefix + key;
          await this.redis.del(l3Key);
          break;
      }
    } catch (error) {
      logger.error(`Failed to delete from layer ${layer}`, {
        key,
        error: error.message
      });
    }
  }

  /**
   * 获取缓存层列表
   */
  getCacheLayers(strategy) {
    const config = this.strategies[strategy] || this.strategies.hotData;
    return config.layers || ['L1', 'L2'];
  }

  /**
   * 记录访问频率
   */
  recordAccess(key) {
    const current = this.accessFrequency.get(key) || 0;
    this.accessFrequency.set(key, current + 1);
  }

  /**
   * 判断是否应该提升到L1
   */
  shouldPromoteToL1(key) {
    const accessCount = this.accessFrequency.get(key) || 0;
    return accessCount >= this.strategies.hotData.preloadThreshold;
  }

  /**
   * L1缓存淘汰策略（LRU）
   */
  evictFromL1() {
    const entries = Array.from(this.cacheLayers.L1.storage.entries());
    if (entries.length > 0) {
      const keyToEvict = entries[0][0]; // 简化的LRU，实际应该跟踪访问时间
      this.cacheLayers.L1.storage.delete(keyToEvict);
      this.stats.evictions++;
    }
  }

  /**
   * 启动预加载调度器
   */
  startPreloadScheduler() {
    if (!this.preloadConfig.enabled) return;
    
    setInterval(async () => {
      try {
        await this.scheduledPreload();
      } catch (error) {
        logger.error('Scheduled preload failed', {
          error: error.message
        });
      }
    }, this.preloadConfig.scheduleInterval);
  }

  /**
   * 定时预加载任务
   */
  async scheduledPreload() {
    logger.debug('Running scheduled preload');
    
    // 预加载热点数据
    const hotKeys = this.getHotKeys();
    if (hotKeys.length > 0) {
      await this.batchPreload(
        hotKeys,
        async (key) => await this.loadDataByKey(key),
        'hotData'
      );
    }
  }

  /**
   * 获取热点键列表
   */
  getHotKeys() {
    return Array.from(this.accessFrequency.entries())
      .filter(([key, count]) => count >= this.strategies.hotData.preloadThreshold)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([key]) => key);
  }

  /**
   * 数组分块
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 记录缓存命中
   */
  async recordCacheHit(key, layer, duration) {
    await performanceMonitor.recordCacheMetrics(
      'cache_hit',
      layer,
      duration,
      { key }
    );
  }

  /**
   * 记录缓存设置
   */
  async recordCacheSet(key, strategy, layers) {
    await performanceMonitor.recordCacheMetrics(
      'cache_set',
      strategy,
      0,
      { key, layers: layers.join(',') }
    );
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    const totalHits = Object.values(this.stats.hits).reduce((a, b) => a + b, 0);
    const totalMisses = Object.values(this.stats.misses).reduce((a, b) => a + b, 0);
    const hitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0;

    return {
      hitRate: Math.round(hitRate * 100) / 100,
      hits: this.stats.hits,
      misses: this.stats.misses,
      preloads: this.stats.preloads,
      evictions: this.stats.evictions,
      errors: this.stats.errors,
      l1Size: this.cacheLayers.L1.storage.size,
      accessFrequencySize: this.accessFrequency.size
    };
  }

  // 以下是数据加载函数的占位符，实际实现时需要连接到具体的数据源
  async loadContentData(key) { return null; }
  async loadUserPreferences(userId) { return null; }
  async loadQuestionData(key) { return null; }
  async loadDialectRegionData(key) { return null; }
  async loadStaticAsset(key) { return null; }
  async loadDataByKey(key) { return null; }
  async getPopularContentKeys() { return []; }
  async getActiveUserIds() { return []; }
  async getPopularQuestionKeys() { return []; }
  async getDialectRegionKeys() { return []; }
}

// 单例模式
let instance = null;

class SmartCache {
  static getInstance() {
    if (!instance) {
      instance = new SmartCacheManager();
    }
    return instance;
  }
}

module.exports = { SmartCache };
