/**
 * 开发环境数据库Mock服务
 * 提供内存数据存储用于开发和测试
 */

class DevDatabase {
  constructor() {
    this.users = new Map();
    this.questions = this.generateMockQuestions();
    this.gameSessions = new Map();
    this.gameRecords = new Map();
    this.connected = true;
  }

  // 生成Mock题目数据
  generateMockQuestions() {
    const questions = [];
    const dialects = ['四川话', '广东话', '上海话', '北京话', '东北话'];
    const difficulties = [1, 2, 3, 4, 5];
    
    for (let i = 1; i <= 50; i++) {
      const dialect = dialects[Math.floor(Math.random() * dialects.length)];
      const difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
      
      questions.push({
        question_id: i,
        dialect_region: dialect,
        difficulty_level: difficulty,
        question_text: `这句${dialect}是什么意思？`,
        audio_url: `https://dev-audio.example.com/audio_${i}.mp3`,
        correct_answer: `答案${i}`,
        options: [
          `答案${i}`,
          `错误选项${i}A`,
          `错误选项${i}B`,
          `错误选项${i}C`
        ],
        hint: `提示：这是关于${dialect}的表达`,
        explanation: `这句话在${dialect}中表示...`,
        tags: [dialect, `难度${difficulty}`],
        play_count: Math.floor(Math.random() * 1000),
        correct_rate: Math.random(),
        created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        updated_at: new Date()
      });
    }
    
    return questions;
  }

  // 健康检查
  async healthCheck() {
    return {
      status: 'healthy',
      type: 'mock',
      message: '开发环境Mock数据库',
      timestamp: new Date().toISOString(),
      stats: {
        users: this.users.size,
        questions: this.questions.length,
        gameSessions: this.gameSessions.size,
        gameRecords: this.gameRecords.size
      }
    };
  }

  // 查询题目
  async queryQuestions(filters = {}) {
    let filteredQuestions = [...this.questions];

    // 按方言过滤
    if (filters.dialect) {
      filteredQuestions = filteredQuestions.filter(q => 
        q.dialect_region === filters.dialect
      );
    }

    // 按难度过滤
    if (filters.difficulty) {
      filteredQuestions = filteredQuestions.filter(q => 
        q.difficulty_level === parseInt(filters.difficulty)
      );
    }

    // 随机排序
    if (filters.random) {
      filteredQuestions.sort(() => Math.random() - 0.5);
    }

    // 限制数量
    if (filters.limit) {
      filteredQuestions = filteredQuestions.slice(0, parseInt(filters.limit));
    }

    return filteredQuestions;
  }

  // 获取单个题目
  async getQuestion(questionId) {
    return this.questions.find(q => q.question_id === parseInt(questionId));
  }

  // 创建用户
  async createUser(userData) {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const user = {
      user_id: userId,
      openid: userData.openid,
      nickname: userData.nickname || '游客',
      avatar_url: userData.avatar_url || '',
      created_at: new Date(),
      updated_at: new Date(),
      last_login: new Date(),
      game_count: 0,
      total_score: 0,
      best_score: 0,
      accuracy_rate: 0
    };
    
    this.users.set(userId, user);
    return user;
  }

  // 获取用户
  async getUser(userId) {
    return this.users.get(userId);
  }

  // 根据openid获取用户
  async getUserByOpenid(openid) {
    for (const [id, user] of this.users) {
      if (user.openid === openid) {
        return user;
      }
    }
    return null;
  }

  // 创建游戏会话
  async createGameSession(sessionData) {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session = {
      session_id: sessionId,
      user_id: sessionData.user_id,
      status: 'active',
      current_question: 0,
      total_questions: sessionData.question_count || 10,
      score: 0,
      start_time: new Date(),
      questions: sessionData.questions || [],
      answers: [],
      created_at: new Date(),
      updated_at: new Date()
    };
    
    this.gameSessions.set(sessionId, session);
    return session;
  }

  // 获取游戏会话
  async getGameSession(sessionId) {
    return this.gameSessions.get(sessionId);
  }

  // 更新游戏会话
  async updateGameSession(sessionId, updates) {
    const session = this.gameSessions.get(sessionId);
    if (session) {
      Object.assign(session, updates, { updated_at: new Date() });
      this.gameSessions.set(sessionId, session);
      return session;
    }
    return null;
  }

  // 记录游戏结果
  async recordGameResult(resultData) {
    const recordId = `record_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const record = {
      record_id: recordId,
      user_id: resultData.user_id,
      session_id: resultData.session_id,
      total_questions: resultData.total_questions,
      correct_answers: resultData.correct_answers,
      score: resultData.score,
      duration: resultData.duration,
      accuracy_rate: resultData.accuracy_rate,
      difficulty_distribution: resultData.difficulty_distribution,
      dialect_distribution: resultData.dialect_distribution,
      created_at: new Date()
    };
    
    this.gameRecords.set(recordId, record);
    
    // 更新用户统计
    const user = this.users.get(resultData.user_id);
    if (user) {
      user.game_count += 1;
      user.total_score += resultData.score;
      user.best_score = Math.max(user.best_score, resultData.score);
      user.accuracy_rate = (user.accuracy_rate * (user.game_count - 1) + resultData.accuracy_rate) / user.game_count;
      user.updated_at = new Date();
      this.users.set(resultData.user_id, user);
    }
    
    return record;
  }

  // 获取用户游戏统计
  async getUserStats(userId) {
    const user = this.users.get(userId);
    if (!user) return null;

    const userRecords = Array.from(this.gameRecords.values())
      .filter(record => record.user_id === userId);

    return {
      user_id: userId,
      total_games: user.game_count,
      total_score: user.total_score,
      best_score: user.best_score,
      average_score: user.game_count > 0 ? Math.round(user.total_score / user.game_count) : 0,
      accuracy_rate: user.accuracy_rate,
      recent_games: userRecords.slice(-10),
      favorite_dialect: this.getMostPlayedDialect(userRecords),
      strength_level: this.calculateStrengthLevel(user.accuracy_rate, user.game_count)
    };
  }

  // 计算最常玩的方言
  getMostPlayedDialect(records) {
    const dialectCounts = {};
    records.forEach(record => {
      if (record.dialect_distribution) {
        Object.keys(record.dialect_distribution).forEach(dialect => {
          dialectCounts[dialect] = (dialectCounts[dialect] || 0) + record.dialect_distribution[dialect];
        });
      }
    });
    
    return Object.keys(dialectCounts).reduce((a, b) => 
      dialectCounts[a] > dialectCounts[b] ? a : b, '四川话'
    );
  }

  // 计算实力等级
  calculateStrengthLevel(accuracy, gameCount) {
    if (gameCount < 5) return '新手';
    if (accuracy < 0.6) return '初学者';
    if (accuracy < 0.75) return '进阶者';
    if (accuracy < 0.85) return '熟练者';
    if (accuracy < 0.95) return '专家';
    return '大师';
  }

  // 关闭连接
  async close() {
    this.connected = false;
    console.log('Mock数据库连接已关闭');
  }
}

// 单例模式
let instance = null;

module.exports = {
  getInstance() {
    if (!instance) {
      instance = new DevDatabase();
    }
    return instance;
  },
  
  healthCheck: async () => {
    const db = module.exports.getInstance();
    return await db.healthCheck();
  },
  
  close: async () => {
    if (instance) {
      await instance.close();
      instance = null;
    }
  }
};