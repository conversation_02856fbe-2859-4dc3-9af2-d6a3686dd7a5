/**
 * 开发环境Redis Mock服务
 * 提供内存缓存存储用于开发和测试
 */

class DevRedis {
  constructor() {
    this.cache = new Map();
    this.ttls = new Map();
    this.connected = true;
    
    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次
  }

  // 清理过期缓存
  cleanup() {
    const now = Date.now();
    for (const [key, expireTime] of this.ttls) {
      if (expireTime <= now) {
        this.cache.delete(key);
        this.ttls.delete(key);
      }
    }
  }

  // 健康检查
  async healthCheck() {
    return {
      status: 'healthy',
      type: 'mock',
      message: '开发环境Mock Redis',
      timestamp: new Date().toISOString(),
      stats: {
        keys: this.cache.size,
        memory: this.getMemoryUsage()
      }
    };
  }

  // 获取内存使用量估算
  getMemoryUsage() {
    let size = 0;
    for (const [key, value] of this.cache) {
      size += key.length + JSON.stringify(value).length;
    }
    return `${Math.round(size / 1024)}KB`;
  }

  // 设置缓存
  async set(key, value, ttl = null) {
    this.cache.set(key, value);
    
    if (ttl && ttl > 0) {
      this.ttls.set(key, Date.now() + ttl * 1000);
    }
    
    return 'OK';
  }

  // 获取缓存
  async get(key) {
    // 检查是否过期
    const expireTime = this.ttls.get(key);
    if (expireTime && expireTime <= Date.now()) {
      this.cache.delete(key);
      this.ttls.delete(key);
      return null;
    }
    
    return this.cache.get(key) || null;
  }

  // 删除缓存
  async del(key) {
    const existed = this.cache.has(key);
    this.cache.delete(key);
    this.ttls.delete(key);
    return existed ? 1 : 0;
  }

  // 检查key是否存在
  async exists(key) {
    const expireTime = this.ttls.get(key);
    if (expireTime && expireTime <= Date.now()) {
      this.cache.delete(key);
      this.ttls.delete(key);
      return 0;
    }
    
    return this.cache.has(key) ? 1 : 0;
  }

  // 设置过期时间
  async expire(key, ttl) {
    if (this.cache.has(key)) {
      this.ttls.set(key, Date.now() + ttl * 1000);
      return 1;
    }
    return 0;
  }

  // 获取剩余过期时间
  async ttl(key) {
    const expireTime = this.ttls.get(key);
    if (!expireTime) {
      return this.cache.has(key) ? -1 : -2; // -1: 永不过期, -2: 不存在
    }
    
    const remaining = Math.max(0, Math.ceil((expireTime - Date.now()) / 1000));
    return remaining;
  }

  // 原子递增
  async incr(key) {
    const current = parseInt(this.cache.get(key) || 0);
    const newValue = current + 1;
    this.cache.set(key, newValue.toString());
    return newValue;
  }

  // 原子递减
  async decr(key) {
    const current = parseInt(this.cache.get(key) || 0);
    const newValue = current - 1;
    this.cache.set(key, newValue.toString());
    return newValue;
  }

  // 列表左推
  async lpush(key, ...values) {
    const list = this.cache.get(key) || [];
    if (!Array.isArray(list)) {
      throw new Error('WRONGTYPE Operation against a key holding the wrong kind of value');
    }
    
    list.unshift(...values);
    this.cache.set(key, list);
    return list.length;
  }

  // 列表右推
  async rpush(key, ...values) {
    const list = this.cache.get(key) || [];
    if (!Array.isArray(list)) {
      throw new Error('WRONGTYPE Operation against a key holding the wrong kind of value');
    }
    
    list.push(...values);
    this.cache.set(key, list);
    return list.length;
  }

  // 列表左弹
  async lpop(key) {
    const list = this.cache.get(key);
    if (!list || !Array.isArray(list)) return null;
    
    const value = list.shift();
    if (list.length === 0) {
      this.cache.delete(key);
    } else {
      this.cache.set(key, list);
    }
    
    return value;
  }

  // 列表右弹
  async rpop(key) {
    const list = this.cache.get(key);
    if (!list || !Array.isArray(list)) return null;
    
    const value = list.pop();
    if (list.length === 0) {
      this.cache.delete(key);
    } else {
      this.cache.set(key, list);
    }
    
    return value;
  }

  // 获取列表长度
  async llen(key) {
    const list = this.cache.get(key);
    return Array.isArray(list) ? list.length : 0;
  }

  // 获取列表范围
  async lrange(key, start, stop) {
    const list = this.cache.get(key);
    if (!Array.isArray(list)) return [];
    
    return list.slice(start, stop === -1 ? undefined : stop + 1);
  }

  // 哈希表设置
  async hset(key, field, value) {
    const hash = this.cache.get(key) || {};
    if (typeof hash !== 'object' || Array.isArray(hash)) {
      throw new Error('WRONGTYPE Operation against a key holding the wrong kind of value');
    }
    
    const isNew = !(field in hash);
    hash[field] = value;
    this.cache.set(key, hash);
    return isNew ? 1 : 0;
  }

  // 哈希表获取
  async hget(key, field) {
    const hash = this.cache.get(key);
    if (!hash || typeof hash !== 'object' || Array.isArray(hash)) return null;
    
    return hash[field] || null;
  }

  // 哈希表获取所有
  async hgetall(key) {
    const hash = this.cache.get(key);
    if (!hash || typeof hash !== 'object' || Array.isArray(hash)) return {};
    
    return { ...hash };
  }

  // 哈希表删除字段
  async hdel(key, ...fields) {
    const hash = this.cache.get(key);
    if (!hash || typeof hash !== 'object' || Array.isArray(hash)) return 0;
    
    let deletedCount = 0;
    fields.forEach(field => {
      if (field in hash) {
        delete hash[field];
        deletedCount++;
      }
    });
    
    if (Object.keys(hash).length === 0) {
      this.cache.delete(key);
    } else {
      this.cache.set(key, hash);
    }
    
    return deletedCount;
  }

  // 模糊匹配key
  async keys(pattern) {
    const keys = Array.from(this.cache.keys());
    
    if (pattern === '*') {
      return keys;
    }
    
    // 简单的通配符匹配
    const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
    return keys.filter(key => regex.test(key));
  }

  // 清空所有缓存
  async flushall() {
    this.cache.clear();
    this.ttls.clear();
    return 'OK';
  }

  // 关闭连接
  async close() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.connected = false;
    console.log('Mock Redis连接已关闭');
  }
}

// 单例模式
let instance = null;

module.exports = {
  getInstance() {
    if (!instance) {
      instance = new DevRedis();
    }
    return instance;
  },
  
  healthCheck: async () => {
    const redis = module.exports.getInstance();
    return await redis.healthCheck();
  },
  
  close: async () => {
    if (instance) {
      await instance.close();
      instance = null;
    }
  }
};