/**
 * Redis连接和操作工具
 * 提供缓存、限流、会话管理等功能
 */

const Redis = require('redis');
const config = require('../../config');

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  /**
   * 连接Redis
   */
  async connect() {
    if (this.client && this.isConnected) {
      return this.client;
    }

    try {
      this.client = Redis.createClient({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        db: config.redis.db,
        keyPrefix: config.redis.keyPrefix,
        retryDelayOnFailover: config.redis.retryDelayOnFailover,
        maxRetriesPerRequest: config.redis.maxRetriesPerRequest,
        lazyConnect: config.redis.lazyConnect,
        connectTimeout: config.redis.connectTimeout,
        commandTimeout: config.redis.commandTimeout
      });

      // 连接事件监听
      this.client.on('connect', () => {
        console.log('Redis connected');
        this.isConnected = true;
      });

      this.client.on('error', (error) => {
        console.error('Redis connection error:', error);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('Redis connection ended');
        this.isConnected = false;
      });

      await this.client.connect();
      return this.client;
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  /**
   * 获取Redis客户端
   */
  async getClient() {
    if (!this.client || !this.isConnected) {
      await this.connect();
    }
    return this.client;
  }

  /**
   * 设置缓存
   * @param {string} key 键
   * @param {any} value 值
   * @param {number} ttl TTL(秒)
   */
  async set(key, value, ttl = config.cache.ttl) {
    const client = await this.getClient();
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    
    if (ttl > 0) {
      return await client.setEx(key, ttl, stringValue);
    } else {
      return await client.set(key, stringValue);
    }
  }

  /**
   * 获取缓存
   * @param {string} key 键
   * @param {boolean} parseJSON 是否解析JSON
   */
  async get(key, parseJSON = true) {
    const client = await this.getClient();
    const value = await client.get(key);
    
    if (value === null) return null;
    
    if (parseJSON) {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    }
    
    return value;
  }

  /**
   * 删除缓存
   * @param {string|Array} keys 键或键数组
   */
  async del(keys) {
    const client = await this.getClient();
    return await client.del(keys);
  }

  /**
   * 检查键是否存在
   * @param {string} key 键
   */
  async exists(key) {
    const client = await this.getClient();
    return await client.exists(key);
  }

  /**
   * 设置过期时间
   * @param {string} key 键
   * @param {number} ttl TTL(秒)
   */
  async expire(key, ttl) {
    const client = await this.getClient();
    return await client.expire(key, ttl);
  }

  /**
   * 获取TTL
   * @param {string} key 键
   */
  async ttl(key) {
    const client = await this.getClient();
    return await client.ttl(key);
  }

  /**
   * 原子递增
   * @param {string} key 键
   * @param {number} increment 递增值
   */
  async incr(key, increment = 1) {
    const client = await this.getClient();
    return increment === 1 ? 
      await client.incr(key) : 
      await client.incrBy(key, increment);
  }

  /**
   * 原子递减
   * @param {string} key 键
   * @param {number} decrement 递减值
   */
  async decr(key, decrement = 1) {
    const client = await this.getClient();
    return decrement === 1 ? 
      await client.decr(key) : 
      await client.decrBy(key, decrement);
  }

  /**
   * 哈希表操作 - 设置字段
   * @param {string} key 键
   * @param {string} field 字段
   * @param {any} value 值
   */
  async hset(key, field, value) {
    const client = await this.getClient();
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    return await client.hSet(key, field, stringValue);
  }

  /**
   * 哈希表操作 - 获取字段
   * @param {string} key 键
   * @param {string} field 字段
   * @param {boolean} parseJSON 是否解析JSON
   */
  async hget(key, field, parseJSON = true) {
    const client = await this.getClient();
    const value = await client.hGet(key, field);
    
    if (value === null) return null;
    
    if (parseJSON) {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    }
    
    return value;
  }

  /**
   * 哈希表操作 - 批量设置
   * @param {string} key 键
   * @param {Object} hash 哈希对象
   */
  async hmset(key, hash) {
    const client = await this.getClient();
    const stringHash = {};
    
    for (const [field, value] of Object.entries(hash)) {
      stringHash[field] = typeof value === 'string' ? value : JSON.stringify(value);
    }
    
    return await client.hMSet(key, stringHash);
  }

  /**
   * 哈希表操作 - 批量获取
   * @param {string} key 键
   * @param {Array} fields 字段数组
   * @param {boolean} parseJSON 是否解析JSON
   */
  async hmget(key, fields, parseJSON = true) {
    const client = await this.getClient();
    const values = await client.hmGet(key, fields);
    
    if (!parseJSON) return values;
    
    return values.map(value => {
      if (value === null) return null;
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    });
  }

  /**
   * 哈希表操作 - 获取所有字段
   * @param {string} key 键
   * @param {boolean} parseJSON 是否解析JSON
   */
  async hgetall(key, parseJSON = true) {
    const client = await this.getClient();
    const hash = await client.hGetAll(key);
    
    if (!parseJSON) return hash;
    
    const result = {};
    for (const [field, value] of Object.entries(hash)) {
      try {
        result[field] = JSON.parse(value);
      } catch (error) {
        result[field] = value;
      }
    }
    
    return result;
  }

  /**
   * 列表操作 - 左推入
   * @param {string} key 键
   * @param {any} value 值
   */
  async lpush(key, value) {
    const client = await this.getClient();
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    return await client.lPush(key, stringValue);
  }

  /**
   * 列表操作 - 右推入
   * @param {string} key 键
   * @param {any} value 值
   */
  async rpush(key, value) {
    const client = await this.getClient();
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    return await client.rPush(key, stringValue);
  }

  /**
   * 列表操作 - 左弹出
   * @param {string} key 键
   * @param {boolean} parseJSON 是否解析JSON
   */
  async lpop(key, parseJSON = true) {
    const client = await this.getClient();
    const value = await client.lPop(key);
    
    if (value === null) return null;
    
    if (parseJSON) {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    }
    
    return value;
  }

  /**
   * 列表操作 - 右弹出
   * @param {string} key 键
   * @param {boolean} parseJSON 是否解析JSON
   */
  async rpop(key, parseJSON = true) {
    const client = await this.getClient();
    const value = await client.rPop(key);
    
    if (value === null) return null;
    
    if (parseJSON) {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    }
    
    return value;
  }

  /**
   * 有序集合操作 - 添加成员
   * @param {string} key 键
   * @param {number} score 分数
   * @param {string} member 成员
   */
  async zadd(key, score, member) {
    const client = await this.getClient();
    return await client.zAdd(key, { score, value: member });
  }

  /**
   * 有序集合操作 - 按分数范围获取成员
   * @param {string} key 键
   * @param {number} min 最小分数
   * @param {number} max 最大分数
   * @param {boolean} withScores 是否返回分数
   */
  async zrangebyscore(key, min, max, withScores = false) {
    const client = await this.getClient();
    return withScores ? 
      await client.zRangeByScoreWithScores(key, min, max) :
      await client.zRangeByScore(key, min, max);
  }

  /**
   * 有序集合操作 - 删除按分数范围的成员
   * @param {string} key 键
   * @param {number} min 最小分数
   * @param {number} max 最大分数
   */
  async zremrangebyscore(key, min, max) {
    const client = await this.getClient();
    return await client.zRemRangeByScore(key, min, max);
  }

  /**
   * 有序集合操作 - 获取成员数量
   * @param {string} key 键
   */
  async zcard(key) {
    const client = await this.getClient();
    return await client.zCard(key);
  }

  /**
   * 执行Lua脚本
   * @param {string} script Lua脚本
   * @param {number} numKeys 键的数量
   * @param {Array} keys 键数组
   * @param {Array} args 参数数组
   */
  async eval(script, numKeys, keys = [], args = []) {
    const client = await this.getClient();
    return await client.eval(script, {
      keys,
      arguments: args
    });
  }

  /**
   * 管道操作
   * @param {Function} callback 管道操作回调
   */
  async pipeline(callback) {
    const client = await this.getClient();
    const pipeline = client.multi();
    
    await callback(pipeline);
    
    return await pipeline.exec();
  }

  /**
   * 模糊匹配键
   * @param {string} pattern 匹配模式
   */
  async keys(pattern) {
    const client = await this.getClient();
    return await client.keys(pattern);
  }

  /**
   * 扫描键
   * @param {number} cursor 游标
   * @param {string} pattern 匹配模式
   * @param {number} count 数量
   */
  async scan(cursor = 0, pattern = '*', count = 10) {
    const client = await this.getClient();
    return await client.scan(cursor, {
      MATCH: pattern,
      COUNT: count
    });
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const client = await this.getClient();
      const result = await client.ping();
      return {
        status: 'healthy',
        response: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 关闭连接
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      console.log('Redis connection closed');
    }
  }

  /**
   * 缓存装饰器工厂
   * @param {string} keyPrefix 键前缀
   * @param {number} ttl TTL(秒)
   */
  cacheDecorator(keyPrefix, ttl = config.cache.ttl) {
    return (target, propertyName, descriptor) => {
      const method = descriptor.value;
      
      descriptor.value = async function(...args) {
        const cacheKey = `${keyPrefix}:${JSON.stringify(args)}`;
        
        // 尝试从缓存获取
        const cached = await this.get(cacheKey);
        if (cached !== null) {
          return cached;
        }
        
        // 执行原方法
        const result = await method.apply(this, args);
        
        // 缓存结果
        await this.set(cacheKey, result, ttl);
        
        return result;
      };
      
      return descriptor;
    };
  }
}

// 全局Redis客户端实例
const redisClient = new RedisClient();

module.exports = redisClient;