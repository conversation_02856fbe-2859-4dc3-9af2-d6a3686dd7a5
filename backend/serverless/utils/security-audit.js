/**
 * 安全审计日志工具
 * 记录和分析安全相关事件
 */

const { RedisManager } = require('./redis');
const securityConfig = require('../config/security');

class SecurityAudit {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.config = securityConfig;
  }

  /**
   * 记录安全事件
   */
  async logSecurityEvent(event) {
    const auditLog = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type: event.type,
      severity: event.severity || 'info',
      userId: event.userId || null,
      ip: event.ip || null,
      userAgent: event.userAgent || null,
      path: event.path || null,
      method: event.method || null,
      details: event.details || {},
      risk: this.calculateRiskScore(event)
    };

    // 存储到Redis
    await this.storeAuditLog(auditLog);

    // 检查是否需要告警
    if (auditLog.severity === 'critical' || auditLog.risk > 80) {
      await this.triggerAlert(auditLog);
    }

    return auditLog;
  }

  /**
   * 记录登录事件
   */
  async logLoginEvent(userId, ip, userAgent, success, details = {}) {
    return await this.logSecurityEvent({
      type: success ? 'login_success' : 'login_failure',
      severity: success ? 'info' : 'warning',
      userId,
      ip,
      userAgent,
      details: {
        ...details,
        loginMethod: details.loginMethod || 'wechat'
      }
    });
  }

  /**
   * 记录数据访问事件
   */
  async logDataAccess(userId, resource, action, ip, success = true) {
    return await this.logSecurityEvent({
      type: 'data_access',
      severity: success ? 'info' : 'warning',
      userId,
      ip,
      details: {
        resource,
        action,
        success
      }
    });
  }

  /**
   * 记录安全违规事件
   */
  async logSecurityViolation(type, ip, userAgent, details = {}) {
    return await this.logSecurityEvent({
      type: 'security_violation',
      severity: 'critical',
      ip,
      userAgent,
      details: {
        violationType: type,
        ...details
      }
    });
  }

  /**
   * 记录限流事件
   */
  async logRateLimitExceeded(ip, path, method, limit, current) {
    return await this.logSecurityEvent({
      type: 'rate_limit_exceeded',
      severity: 'warning',
      ip,
      path,
      method,
      details: {
        limit,
        current,
        exceeded: current - limit
      }
    });
  }

  /**
   * 获取安全事件统计
   */
  async getSecurityStats(timeRange = 3600) {
    const now = Date.now();
    const startTime = now - (timeRange * 1000);
    
    const events = await this.redis.zrangebyscore(
      'security:audit:events',
      startTime,
      now
    );

    const stats = {
      total: events.length,
      byType: {},
      bySeverity: {},
      byRisk: { low: 0, medium: 0, high: 0, critical: 0 },
      topIPs: {},
      recentViolations: []
    };

    events.forEach(eventStr => {
      try {
        const event = JSON.parse(eventStr);
        
        // 按类型统计
        stats.byType[event.type] = (stats.byType[event.type] || 0) + 1;
        
        // 按严重程度统计
        stats.bySeverity[event.severity] = (stats.bySeverity[event.severity] || 0) + 1;
        
        // 按风险等级统计
        if (event.risk < 25) stats.byRisk.low++;
        else if (event.risk < 50) stats.byRisk.medium++;
        else if (event.risk < 75) stats.byRisk.high++;
        else stats.byRisk.critical++;
        
        // IP统计
        if (event.ip) {
          stats.topIPs[event.ip] = (stats.topIPs[event.ip] || 0) + 1;
        }
        
        // 最近违规
        if (event.type === 'security_violation') {
          stats.recentViolations.push(event);
        }
      } catch (error) {
        console.error('Failed to parse audit event:', error);
      }
    });

    // 排序Top IPs
    stats.topIPs = Object.entries(stats.topIPs)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .reduce((obj, [ip, count]) => ({ ...obj, [ip]: count }), {});

    return stats;
  }

  /**
   * 检测异常行为
   */
  async detectAnomalies(timeRange = 3600) {
    const stats = await this.getSecurityStats(timeRange);
    const anomalies = [];

    // 检测异常登录失败率
    const loginFailures = stats.byType.login_failure || 0;
    const loginSuccesses = stats.byType.login_success || 0;
    const totalLogins = loginFailures + loginSuccesses;
    
    if (totalLogins > 10 && (loginFailures / totalLogins) > 0.5) {
      anomalies.push({
        type: 'high_login_failure_rate',
        severity: 'warning',
        details: {
          failureRate: (loginFailures / totalLogins) * 100,
          totalAttempts: totalLogins
        }
      });
    }

    // 检测频繁的安全违规
    const violations = stats.byType.security_violation || 0;
    if (violations > 10) {
      anomalies.push({
        type: 'frequent_security_violations',
        severity: 'critical',
        details: {
          violationCount: violations,
          timeRange
        }
      });
    }

    // 检测可疑IP
    Object.entries(stats.topIPs).forEach(([ip, count]) => {
      if (count > 100) {
        anomalies.push({
          type: 'suspicious_ip_activity',
          severity: 'warning',
          details: {
            ip,
            requestCount: count,
            timeRange
          }
        });
      }
    });

    return anomalies;
  }

  /**
   * 生成安全报告
   */
  async generateSecurityReport(timeRange = 86400) {
    const [stats, anomalies] = await Promise.all([
      this.getSecurityStats(timeRange),
      this.detectAnomalies(timeRange)
    ]);

    return {
      timestamp: new Date().toISOString(),
      timeRange,
      summary: {
        totalEvents: stats.total,
        criticalEvents: stats.bySeverity.critical || 0,
        securityViolations: stats.byType.security_violation || 0,
        anomaliesDetected: anomalies.length
      },
      statistics: stats,
      anomalies,
      recommendations: this.generateRecommendations(stats, anomalies)
    };
  }

  /**
   * 存储审计日志
   */
  async storeAuditLog(auditLog) {
    const key = 'security:audit:events';
    const score = Date.parse(auditLog.timestamp);
    
    await this.redis.zadd(key, score, JSON.stringify(auditLog));
    
    // 清理过期数据
    const retentionMs = this.config.audit.retentionDays * 24 * 60 * 60 * 1000;
    const cutoffTime = Date.now() - retentionMs;
    await this.redis.zremrangebyscore(key, 0, cutoffTime);
  }

  /**
   * 触发安全告警
   */
  async triggerAlert(auditLog) {
    const alert = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type: 'security_alert',
      severity: auditLog.severity,
      event: auditLog,
      message: this.generateAlertMessage(auditLog)
    };

    // 存储告警
    await this.redis.lpush('security:alerts', JSON.stringify(alert));
    
    // 控制台输出
    console.warn('🚨 Security Alert:', alert.message);
    
    return alert;
  }

  /**
   * 计算风险评分
   */
  calculateRiskScore(event) {
    let score = 0;
    
    // 基础分数
    switch (event.type) {
      case 'login_failure': score += 20; break;
      case 'security_violation': score += 60; break;
      case 'rate_limit_exceeded': score += 30; break;
      case 'data_access': score += 10; break;
      default: score += 5;
    }
    
    // 严重程度加权
    switch (event.severity) {
      case 'critical': score *= 2; break;
      case 'warning': score *= 1.5; break;
      case 'info': score *= 1; break;
    }
    
    return Math.min(100, score);
  }

  /**
   * 生成事件ID
   */
  generateEventId() {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成告警消息
   */
  generateAlertMessage(auditLog) {
    const messages = {
      login_failure: `登录失败: IP ${auditLog.ip}`,
      security_violation: `安全违规: ${auditLog.details.violationType}`,
      rate_limit_exceeded: `限流触发: ${auditLog.path}`,
      data_access: `数据访问: ${auditLog.details.resource}`
    };
    
    return messages[auditLog.type] || `安全事件: ${auditLog.type}`;
  }

  /**
   * 生成安全建议
   */
  generateRecommendations(stats, anomalies) {
    const recommendations = [];
    
    if (anomalies.some(a => a.type === 'high_login_failure_rate')) {
      recommendations.push('考虑加强登录验证机制，如验证码或多因素认证');
    }
    
    if (anomalies.some(a => a.type === 'frequent_security_violations')) {
      recommendations.push('检查安全规则配置，可能需要调整防护策略');
    }
    
    if (anomalies.some(a => a.type === 'suspicious_ip_activity')) {
      recommendations.push('考虑对可疑IP进行临时封禁或加强监控');
    }
    
    return recommendations;
  }
}

module.exports = { SecurityAudit };
