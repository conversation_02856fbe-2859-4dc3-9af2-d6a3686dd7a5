/**
 * 数据库性能优化工具
 * 提供查询优化、连接池管理、慢查询监控等功能
 */

const mysql = require('mysql2/promise');
const { RedisManager } = require('./redis');

class DatabaseOptimizer {
  constructor(config) {
    this.config = config;
    this.redis = RedisManager.getInstance();
    this.slowQueryThreshold = 1000; // 1秒
    this.queryCache = new Map();
  }

  /**
   * 创建优化的连接池
   */
  createOptimizedPool() {
    return mysql.createPool({
      ...this.config,
      // 优化连接池配置
      connectionLimit: 20, // 增加连接数
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true,
      // 启用查询缓存
      queryCache: true,
      // 启用压缩
      compress: true,
      // 优化字符集
      charset: 'utf8mb4',
      // 启用多语句查询
      multipleStatements: false,
      // 连接保活
      keepAliveInitialDelay: 0,
      enableKeepAlive: true,
      // SSL配置
      ssl: this.config.ssl || false
    });
  }

  /**
   * 查询包装器 - 添加性能监控
   */
  async executeQuery(pool, sql, params = []) {
    const startTime = Date.now();
    const queryId = this.generateQueryId(sql, params);
    
    try {
      // 检查查询缓存
      const cachedResult = await this.getCachedQuery(queryId);
      if (cachedResult) {
        return {
          ...cachedResult,
          fromCache: true,
          duration: Date.now() - startTime
        };
      }

      // 执行查询
      const [rows, fields] = await pool.execute(sql, params);
      const duration = Date.now() - startTime;

      // 记录慢查询
      if (duration > this.slowQueryThreshold) {
        await this.recordSlowQuery({
          sql,
          params,
          duration,
          timestamp: new Date().toISOString()
        });
      }

      // 缓存查询结果（仅对SELECT查询）
      if (sql.trim().toLowerCase().startsWith('select')) {
        await this.cacheQuery(queryId, { rows, fields }, duration);
      }

      return {
        rows,
        fields,
        duration,
        fromCache: false
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 记录查询错误
      await this.recordQueryError({
        sql,
        params,
        error: error.message,
        duration,
        timestamp: new Date().toISOString()
      });

      throw error;
    }
  }

  /**
   * 批量插入优化
   */
  async batchInsert(pool, table, data, batchSize = 1000) {
    if (!Array.isArray(data) || data.length === 0) {
      return { affectedRows: 0, insertId: null };
    }

    const fields = Object.keys(data[0]);
    const placeholders = fields.map(() => '?').join(', ');
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES `;

    let totalAffectedRows = 0;
    let firstInsertId = null;

    // 分批处理
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      const values = [];
      const batchPlaceholders = [];

      batch.forEach(row => {
        batchPlaceholders.push(`(${placeholders})`);
        fields.forEach(field => {
          values.push(row[field]);
        });
      });

      const batchSql = sql + batchPlaceholders.join(', ');
      const result = await this.executeQuery(pool, batchSql, values);
      
      totalAffectedRows += result.rows.affectedRows;
      if (firstInsertId === null && result.rows.insertId) {
        firstInsertId = result.rows.insertId;
      }
    }

    return {
      affectedRows: totalAffectedRows,
      insertId: firstInsertId
    };
  }

  /**
   * 批量更新优化
   */
  async batchUpdate(pool, table, updates, keyField = 'id') {
    if (!Array.isArray(updates) || updates.length === 0) {
      return { affectedRows: 0 };
    }

    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      let totalAffectedRows = 0;
      
      for (const update of updates) {
        const { [keyField]: keyValue, ...fields } = update;
        const setClause = Object.keys(fields).map(field => `${field} = ?`).join(', ');
        const values = [...Object.values(fields), keyValue];
        
        const sql = `UPDATE ${table} SET ${setClause} WHERE ${keyField} = ?`;
        const result = await this.executeQuery(connection, sql, values);
        totalAffectedRows += result.rows.affectedRows;
      }
      
      await connection.commit();
      return { affectedRows: totalAffectedRows };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 查询结果缓存
   */
  async cacheQuery(queryId, result, duration) {
    try {
      // 只缓存快速查询的结果
      if (duration < this.slowQueryThreshold) {
        const cacheKey = `query_cache:${queryId}`;
        const ttl = this.calculateCacheTTL(duration);
        
        await this.redis.setex(
          cacheKey, 
          ttl, 
          JSON.stringify({
            ...result,
            cachedAt: Date.now()
          })
        );
      }
    } catch (error) {
      console.error('Failed to cache query:', error);
    }
  }

  /**
   * 获取缓存的查询结果
   */
  async getCachedQuery(queryId) {
    try {
      const cacheKey = `query_cache:${queryId}`;
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        const result = JSON.parse(cached);
        
        // 检查缓存是否过期
        const age = Date.now() - result.cachedAt;
        if (age < 300000) { // 5分钟内的缓存有效
          return {
            rows: result.rows,
            fields: result.fields
          };
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get cached query:', error);
      return null;
    }
  }

  /**
   * 记录慢查询
   */
  async recordSlowQuery(queryData) {
    try {
      const key = `slow_query:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
      await this.redis.setex(key, 86400, JSON.stringify(queryData)); // 保留24小时
      
      // 发送告警
      if (queryData.duration > 5000) { // 超过5秒的查询
        console.warn('Critical slow query detected:', {
          duration: queryData.duration,
          sql: queryData.sql.substring(0, 100) + '...'
        });
      }
    } catch (error) {
      console.error('Failed to record slow query:', error);
    }
  }

  /**
   * 记录查询错误
   */
  async recordQueryError(errorData) {
    try {
      const key = `query_error:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
      await this.redis.setex(key, 86400, JSON.stringify(errorData));
    } catch (error) {
      console.error('Failed to record query error:', error);
    }
  }

  /**
   * 生成查询ID
   */
  generateQueryId(sql, params) {
    const crypto = require('crypto');
    const queryString = sql + JSON.stringify(params);
    return crypto.createHash('md5').update(queryString).digest('hex');
  }

  /**
   * 计算缓存TTL
   */
  calculateCacheTTL(queryDuration) {
    // 查询越快，缓存时间越长
    if (queryDuration < 100) return 3600; // 1小时
    if (queryDuration < 500) return 1800; // 30分钟
    return 600; // 10分钟
  }

  /**
   * 获取数据库性能报告
   */
  async getPerformanceReport() {
    try {
      // 获取慢查询统计
      const slowQueries = await this.getSlowQueryStats();
      
      // 获取查询错误统计
      const queryErrors = await this.getQueryErrorStats();
      
      // 获取缓存命中率
      const cacheStats = await this.getCacheStats();
      
      return {
        slowQueries,
        queryErrors,
        cacheStats,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to generate performance report:', error);
      return null;
    }
  }

  /**
   * 获取慢查询统计
   */
  async getSlowQueryStats() {
    const keys = await this.redis.keys('slow_query:*');
    const queries = [];
    
    for (const key of keys.slice(0, 100)) { // 限制数量
      const data = await this.redis.get(key);
      if (data) {
        queries.push(JSON.parse(data));
      }
    }
    
    return {
      total: queries.length,
      avgDuration: queries.reduce((sum, q) => sum + q.duration, 0) / queries.length || 0,
      maxDuration: Math.max(...queries.map(q => q.duration), 0),
      recentQueries: queries.slice(0, 10)
    };
  }

  /**
   * 获取查询错误统计
   */
  async getQueryErrorStats() {
    const keys = await this.redis.keys('query_error:*');
    const errors = [];
    
    for (const key of keys.slice(0, 50)) {
      const data = await this.redis.get(key);
      if (data) {
        errors.push(JSON.parse(data));
      }
    }
    
    return {
      total: errors.length,
      recentErrors: errors.slice(0, 10)
    };
  }

  /**
   * 获取缓存统计
   */
  async getCacheStats() {
    const cacheKeys = await this.redis.keys('query_cache:*');
    
    return {
      totalCachedQueries: cacheKeys.length,
      estimatedMemoryUsage: cacheKeys.length * 1024 // 粗略估算
    };
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache() {
    try {
      const keys = await this.redis.keys('query_cache:*');
      let cleanedCount = 0;
      
      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl <= 0) {
          await this.redis.del(key);
          cleanedCount++;
        }
      }
      
      console.log(`Cleaned up ${cleanedCount} expired cache entries`);
      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup expired cache:', error);
      return 0;
    }
  }
}

module.exports = { DatabaseOptimizer };
