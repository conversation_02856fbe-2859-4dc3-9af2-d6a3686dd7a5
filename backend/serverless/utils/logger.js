/**
 * 结构化日志系统
 * 支持多级别日志、结构化输出、性能监控
 */

const config = require('../../config');

class Logger {
  constructor(options = {}) {
    this.level = options.level || config.logging?.level || 'info';
    this.service = options.service || 'hometown-dialect-game';
    this.version = options.version || '1.0.0';
    this.environment = config.env;
    
    // 日志级别映射
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };
    
    this.currentLevel = this.levels[this.level] || 2;
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      service: this.service,
      version: this.version,
      environment: this.environment,
      message,
      ...meta
    };

    // 在开发环境下使用更友好的格式
    if (config.isDev) {
      return `[${timestamp}] ${level.toUpperCase()}: ${message} ${Object.keys(meta).length > 0 ? JSON.stringify(meta, null, 2) : ''}`;
    }

    // 生产环境使用JSON格式
    return JSON.stringify(logEntry);
  }

  /**
   * 检查是否应该记录此级别的日志
   */
  shouldLog(level) {
    return this.levels[level] <= this.currentLevel;
  }

  /**
   * 错误日志
   */
  error(message, meta = {}) {
    if (!this.shouldLog('error')) return;
    
    const logMessage = this.formatMessage('error', message, {
      ...meta,
      stack: meta.error?.stack || meta.stack
    });
    
    console.error(logMessage);
  }

  /**
   * 警告日志
   */
  warn(message, meta = {}) {
    if (!this.shouldLog('warn')) return;
    
    const logMessage = this.formatMessage('warn', message, meta);
    console.warn(logMessage);
  }

  /**
   * 信息日志
   */
  info(message, meta = {}) {
    if (!this.shouldLog('info')) return;
    
    const logMessage = this.formatMessage('info', message, meta);
    console.log(logMessage);
  }

  /**
   * 调试日志
   */
  debug(message, meta = {}) {
    if (!this.shouldLog('debug')) return;
    
    const logMessage = this.formatMessage('debug', message, meta);
    console.log(logMessage);
  }

  /**
   * 跟踪日志
   */
  trace(message, meta = {}) {
    if (!this.shouldLog('trace')) return;
    
    const logMessage = this.formatMessage('trace', message, meta);
    console.log(logMessage);
  }

  /**
   * 记录API请求
   */
  logRequest(event, context, startTime) {
    const duration = Date.now() - startTime;
    const requestInfo = {
      requestId: context.requestId,
      method: event.httpMethod,
      path: event.path,
      userAgent: event.headers['User-Agent'],
      ip: event.headers['X-Forwarded-For'] || event.requestContext?.identity?.sourceIp,
      duration: `${duration}ms`,
      userId: event.user?.id || null
    };

    this.info('API Request', requestInfo);
  }

  /**
   * 记录API响应
   */
  logResponse(event, context, response, startTime) {
    const duration = Date.now() - startTime;
    const responseInfo = {
      requestId: context.requestId,
      statusCode: response.statusCode,
      duration: `${duration}ms`,
      responseSize: response.body ? Buffer.byteLength(response.body, 'utf8') : 0
    };

    if (response.statusCode >= 400) {
      this.warn('API Error Response', responseInfo);
    } else {
      this.info('API Response', responseInfo);
    }
  }

  /**
   * 记录数据库操作
   */
  logDatabase(operation, table, duration, meta = {}) {
    const dbInfo = {
      operation,
      table,
      duration: `${duration.toFixed(2)}ms`,
      ...meta
    };

    if (duration > 1000) {
      this.warn('Slow Database Query', dbInfo);
    } else {
      this.debug('Database Operation', dbInfo);
    }
  }

  /**
   * 记录缓存操作
   */
  logCache(operation, key, hit = null, duration = null) {
    const cacheInfo = {
      operation,
      key,
      hit: hit !== null ? (hit ? 'HIT' : 'MISS') : null,
      duration: duration ? `${duration.toFixed(2)}ms` : null
    };

    this.debug('Cache Operation', cacheInfo);
  }

  /**
   * 记录业务事件
   */
  logEvent(eventName, data = {}) {
    const eventInfo = {
      event: eventName,
      ...data
    };

    this.info('Business Event', eventInfo);
  }

  /**
   * 记录性能指标
   */
  logMetrics(metrics) {
    this.info('Performance Metrics', metrics);
  }

  /**
   * 记录安全事件
   */
  logSecurity(event, severity = 'medium', meta = {}) {
    const securityInfo = {
      securityEvent: event,
      severity,
      ...meta
    };

    if (severity === 'high' || severity === 'critical') {
      this.error('Security Alert', securityInfo);
    } else {
      this.warn('Security Event', securityInfo);
    }
  }

  /**
   * 创建子日志器
   */
  child(meta = {}) {
    const childLogger = new Logger({
      level: this.level,
      service: this.service,
      version: this.version
    });
    
    // 重写格式化方法以包含子日志器的元数据
    const originalFormat = childLogger.formatMessage.bind(childLogger);
    childLogger.formatMessage = (level, message, additionalMeta = {}) => {
      return originalFormat(level, message, { ...meta, ...additionalMeta });
    };
    
    return childLogger;
  }
}

/**
 * 性能监控装饰器
 */
function withPerformanceLogging(logger, operation) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const startTime = Date.now();
      const operationLogger = logger.child({ operation, method: propertyKey });
      
      try {
        operationLogger.debug('Operation started');
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;
        
        operationLogger.info('Operation completed', { duration: `${duration}ms` });
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        operationLogger.error('Operation failed', { 
          duration: `${duration}ms`,
          error: error.message,
          stack: error.stack
        });
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * 创建默认日志器实例
 */
const defaultLogger = new Logger();

module.exports = {
  Logger,
  withPerformanceLogging,
  logger: defaultLogger
};
