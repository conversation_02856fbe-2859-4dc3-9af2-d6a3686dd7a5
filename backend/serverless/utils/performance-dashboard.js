/**
 * 性能监控仪表板
 * 提供性能数据查询和可视化接口
 */

const { RedisManager } = require('./redis');
const performanceConfig = require('../config/performance');

class PerformanceDashboard {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.config = performanceConfig;
  }

  /**
   * 获取实时性能指标
   */
  async getRealTimeMetrics(timeRange = 3600) {
    const now = Date.now();
    const startTime = now - (timeRange * 1000);
    
    const metrics = await this.redis.zrangebyscore(
      `${this.config.redis.prefix}${this.config.redis.keys.metrics}`,
      startTime,
      now
    );
    
    return this.processMetrics(metrics);
  }

  /**
   * 获取聚合性能数据
   */
  async getAggregatedMetrics(period = 'hour', count = 24) {
    const key = `${this.config.redis.prefix}${this.config.redis.keys.aggregated}:${period}`;
    const data = await this.redis.lrange(key, 0, count - 1);
    
    return data.map(item => JSON.parse(item));
  }

  /**
   * 获取性能趋势
   */
  async getPerformanceTrends(metric = 'responseTime', period = 'hour', count = 24) {
    const aggregatedData = await this.getAggregatedMetrics(period, count);
    
    return aggregatedData.map(data => ({
      timestamp: data.timestamp,
      value: data[metric] || 0,
      trend: this.calculateTrend(data[metric], data.previous?.[metric])
    }));
  }

  /**
   * 获取错误率统计
   */
  async getErrorRateStats(timeRange = 3600) {
    const metrics = await this.getRealTimeMetrics(timeRange);
    const totalRequests = metrics.length;
    const errorRequests = metrics.filter(m => m.status === 'error').length;
    
    return {
      totalRequests,
      errorRequests,
      errorRate: totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0,
      successRate: totalRequests > 0 ? ((totalRequests - errorRequests) / totalRequests) * 100 : 0
    };
  }

  /**
   * 获取慢查询统计
   */
  async getSlowQueryStats(timeRange = 3600) {
    const metrics = await this.getRealTimeMetrics(timeRange);
    const slowQueries = metrics.filter(m => 
      m.database && m.database.duration > this.config.thresholds.database.warning
    );
    
    return {
      totalQueries: metrics.filter(m => m.database).length,
      slowQueries: slowQueries.length,
      slowQueryRate: slowQueries.length > 0 ? (slowQueries.length / metrics.length) * 100 : 0,
      avgQueryTime: this.calculateAverage(metrics.map(m => m.database?.duration).filter(Boolean)),
      slowestQuery: Math.max(...slowQueries.map(q => q.database.duration), 0)
    };
  }

  /**
   * 获取内存使用统计
   */
  async getMemoryStats(timeRange = 3600) {
    const metrics = await this.getRealTimeMetrics(timeRange);
    const memoryUsages = metrics.map(m => m.memory?.heapUsed || 0).filter(Boolean);
    
    return {
      current: memoryUsages[memoryUsages.length - 1] || 0,
      average: this.calculateAverage(memoryUsages),
      peak: Math.max(...memoryUsages, 0),
      trend: this.calculateTrend(
        memoryUsages.slice(-10),
        memoryUsages.slice(-20, -10)
      )
    };
  }

  /**
   * 获取吞吐量统计
   */
  async getThroughputStats(timeRange = 3600) {
    const metrics = await this.getRealTimeMetrics(timeRange);
    const timeSlots = this.groupByTimeSlots(metrics, 60); // 按分钟分组
    
    return {
      current: timeSlots[timeSlots.length - 1]?.count || 0,
      average: this.calculateAverage(timeSlots.map(slot => slot.count)),
      peak: Math.max(...timeSlots.map(slot => slot.count), 0),
      total: metrics.length
    };
  }

  /**
   * 获取告警历史
   */
  async getAlertHistory(count = 50) {
    const alerts = await this.redis.lrange(
      `${this.config.redis.prefix}${this.config.redis.keys.alerts}`,
      0,
      count - 1
    );
    
    return alerts.map(alert => JSON.parse(alert));
  }

  /**
   * 生成性能报告
   */
  async generateReport(timeRange = 3600) {
    const [
      realTimeMetrics,
      errorStats,
      slowQueryStats,
      memoryStats,
      throughputStats,
      alertHistory
    ] = await Promise.all([
      this.getRealTimeMetrics(timeRange),
      this.getErrorRateStats(timeRange),
      this.getSlowQueryStats(timeRange),
      this.getMemoryStats(timeRange),
      this.getThroughputStats(timeRange),
      this.getAlertHistory(10)
    ]);

    return {
      timestamp: new Date().toISOString(),
      timeRange,
      summary: {
        totalRequests: realTimeMetrics.length,
        averageResponseTime: this.calculateAverage(realTimeMetrics.map(m => m.duration)),
        errorRate: errorStats.errorRate,
        memoryUsage: memoryStats.current,
        throughput: throughputStats.average
      },
      details: {
        errorStats,
        slowQueryStats,
        memoryStats,
        throughputStats
      },
      alerts: alertHistory,
      health: this.calculateHealthScore({
        errorRate: errorStats.errorRate,
        avgResponseTime: this.calculateAverage(realTimeMetrics.map(m => m.duration)),
        memoryUsage: memoryStats.current,
        slowQueryRate: slowQueryStats.slowQueryRate
      })
    };
  }

  /**
   * 处理原始指标数据
   */
  processMetrics(rawMetrics) {
    return rawMetrics.map(metric => {
      try {
        return JSON.parse(metric);
      } catch (error) {
        console.error('Failed to parse metric:', error);
        return null;
      }
    }).filter(Boolean);
  }

  /**
   * 计算平均值
   */
  calculateAverage(values) {
    if (!values || values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * 计算趋势
   */
  calculateTrend(current, previous) {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * 按时间段分组
   */
  groupByTimeSlots(metrics, slotSize) {
    const slots = new Map();
    
    metrics.forEach(metric => {
      const slot = Math.floor(metric.timestamp / (slotSize * 1000)) * slotSize;
      if (!slots.has(slot)) {
        slots.set(slot, { timestamp: slot, count: 0, metrics: [] });
      }
      slots.get(slot).count++;
      slots.get(slot).metrics.push(metric);
    });
    
    return Array.from(slots.values()).sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * 计算健康评分
   */
  calculateHealthScore(metrics) {
    let score = 100;
    
    // 错误率影响
    if (metrics.errorRate > this.config.thresholds.errorRate.critical) {
      score -= 30;
    } else if (metrics.errorRate > this.config.thresholds.errorRate.warning) {
      score -= 15;
    }
    
    // 响应时间影响
    if (metrics.avgResponseTime > this.config.thresholds.responseTime.critical) {
      score -= 25;
    } else if (metrics.avgResponseTime > this.config.thresholds.responseTime.warning) {
      score -= 10;
    }
    
    // 内存使用影响
    if (metrics.memoryUsage > this.config.thresholds.memory.critical) {
      score -= 20;
    } else if (metrics.memoryUsage > this.config.thresholds.memory.warning) {
      score -= 10;
    }
    
    // 慢查询影响
    if (metrics.slowQueryRate > 20) {
      score -= 15;
    } else if (metrics.slowQueryRate > 10) {
      score -= 8;
    }
    
    return Math.max(0, Math.min(100, score));
  }
}

module.exports = { PerformanceDashboard };
