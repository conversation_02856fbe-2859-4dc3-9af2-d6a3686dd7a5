/**
 * 消息队列管理器
 * 基于Redis实现的高性能消息队列系统
 * 支持异步消息处理、优先级队列、延迟消息等功能
 */

const { RedisManager } = require('./redis');
const { logger } = require('./logger');
const { performanceMonitor } = require('./monitoring');

class MessageQueue {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.queuePrefix = 'mq:';
    this.processingPrefix = 'mq:processing:';
    this.deadLetterPrefix = 'mq:dead:';
    this.delayedPrefix = 'mq:delayed:';
    
    // 队列配置
    this.queues = new Map();
    this.processors = new Map();
    this.isProcessing = false;
    
    // 默认配置
    this.defaultConfig = {
      maxRetries: 3,
      retryDelay: 5000, // 5秒
      visibilityTimeout: 30000, // 30秒
      maxConcurrency: 10,
      batchSize: 1
    };

    // 启动处理器
    this.startProcessing();
  }

  /**
   * 注册队列
   */
  registerQueue(queueName, config = {}) {
    const queueConfig = {
      ...this.defaultConfig,
      ...config
    };

    this.queues.set(queueName, queueConfig);
    
    logger.info('Message queue registered', {
      queueName,
      config: queueConfig
    });
  }

  /**
   * 注册消息处理器
   */
  registerProcessor(queueName, processor) {
    if (typeof processor !== 'function') {
      throw new Error('Processor must be a function');
    }

    this.processors.set(queueName, processor);
    
    logger.info('Message processor registered', {
      queueName
    });
  }

  /**
   * 发送消息到队列
   */
  async sendMessage(queueName, message, options = {}) {
    const startTime = Date.now();
    
    try {
      const messageData = {
        id: this.generateMessageId(),
        queueName,
        payload: message,
        priority: options.priority || 0,
        delay: options.delay || 0,
        maxRetries: options.maxRetries || this.defaultConfig.maxRetries,
        retryCount: 0,
        createdAt: new Date().toISOString(),
        scheduledAt: options.delay ? 
          new Date(Date.now() + options.delay).toISOString() : 
          new Date().toISOString()
      };

      // 如果有延迟，添加到延迟队列
      if (options.delay && options.delay > 0) {
        await this.addToDelayedQueue(messageData);
      } else {
        await this.addToQueue(queueName, messageData);
      }

      // 记录性能指标
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_send',
        queueName,
        duration,
        true
      );

      logger.debug('Message sent to queue', {
        messageId: messageData.id,
        queueName,
        priority: messageData.priority,
        delay: options.delay
      });

      return messageData.id;

    } catch (error) {
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_send',
        queueName,
        duration,
        false,
        error
      );

      logger.error('Failed to send message to queue', {
        queueName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 批量发送消息
   */
  async sendBatchMessages(queueName, messages, options = {}) {
    const startTime = Date.now();
    
    try {
      const messageDataList = messages.map(message => ({
        id: this.generateMessageId(),
        queueName,
        payload: message,
        priority: options.priority || 0,
        delay: options.delay || 0,
        maxRetries: options.maxRetries || this.defaultConfig.maxRetries,
        retryCount: 0,
        createdAt: new Date().toISOString(),
        scheduledAt: options.delay ? 
          new Date(Date.now() + options.delay).toISOString() : 
          new Date().toISOString()
      }));

      // 批量添加到队列
      const pipeline = this.redis.pipeline();
      
      for (const messageData of messageDataList) {
        if (options.delay && options.delay > 0) {
          pipeline.zadd(
            `${this.delayedPrefix}${queueName}`,
            Date.now() + options.delay,
            JSON.stringify(messageData)
          );
        } else {
          // 使用优先级队列
          pipeline.zadd(
            `${this.queuePrefix}${queueName}`,
            messageData.priority,
            JSON.stringify(messageData)
          );
        }
      }

      await pipeline.exec();

      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_batch_send',
        queueName,
        duration,
        true
      );

      logger.info('Batch messages sent to queue', {
        queueName,
        messageCount: messages.length,
        priority: options.priority
      });

      return messageDataList.map(msg => msg.id);

    } catch (error) {
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_batch_send',
        queueName,
        duration,
        false,
        error
      );

      logger.error('Failed to send batch messages to queue', {
        queueName,
        messageCount: messages.length,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 添加消息到队列
   */
  async addToQueue(queueName, messageData) {
    // 使用有序集合实现优先级队列
    await this.redis.zadd(
      `${this.queuePrefix}${queueName}`,
      messageData.priority,
      JSON.stringify(messageData)
    );
  }

  /**
   * 添加消息到延迟队列
   */
  async addToDelayedQueue(messageData) {
    const executeAt = new Date(messageData.scheduledAt).getTime();
    await this.redis.zadd(
      `${this.delayedPrefix}${messageData.queueName}`,
      executeAt,
      JSON.stringify(messageData)
    );
  }

  /**
   * 从队列获取消息
   */
  async getMessage(queueName, count = 1) {
    try {
      // 从优先级队列获取消息（分数最高的优先）
      const messages = await this.redis.zrevrange(
        `${this.queuePrefix}${queueName}`,
        0,
        count - 1
      );

      if (messages.length === 0) {
        return [];
      }

      // 移除已获取的消息
      const pipeline = this.redis.pipeline();
      messages.forEach(message => {
        pipeline.zrem(`${this.queuePrefix}${queueName}`, message);
      });
      await pipeline.exec();

      // 解析消息数据
      const messageDataList = messages.map(message => JSON.parse(message));

      // 添加到处理中队列
      const processingPipeline = this.redis.pipeline();
      messageDataList.forEach(messageData => {
        const processingKey = `${this.processingPrefix}${queueName}:${messageData.id}`;
        processingPipeline.setex(
          processingKey,
          Math.ceil(this.getQueueConfig(queueName).visibilityTimeout / 1000),
          JSON.stringify(messageData)
        );
      });
      await processingPipeline.exec();

      return messageDataList;

    } catch (error) {
      logger.error('Failed to get message from queue', {
        queueName,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 确认消息处理完成
   */
  async ackMessage(queueName, messageId) {
    try {
      const processingKey = `${this.processingPrefix}${queueName}:${messageId}`;
      await this.redis.del(processingKey);

      logger.debug('Message acknowledged', {
        queueName,
        messageId
      });

    } catch (error) {
      logger.error('Failed to acknowledge message', {
        queueName,
        messageId,
        error: error.message
      });
    }
  }

  /**
   * 拒绝消息（重新入队或进入死信队列）
   */
  async nackMessage(queueName, messageId, requeue = true) {
    try {
      const processingKey = `${this.processingPrefix}${queueName}:${messageId}`;
      const messageData = await this.redis.get(processingKey);

      if (!messageData) {
        logger.warn('Message not found in processing queue', {
          queueName,
          messageId
        });
        return;
      }

      const message = JSON.parse(messageData);
      await this.redis.del(processingKey);

      if (requeue && message.retryCount < message.maxRetries) {
        // 增加重试次数并重新入队
        message.retryCount++;
        message.retryAt = new Date(Date.now() + this.getQueueConfig(queueName).retryDelay).toISOString();
        
        // 添加到延迟队列等待重试
        await this.redis.zadd(
          `${this.delayedPrefix}${queueName}`,
          Date.now() + this.getQueueConfig(queueName).retryDelay,
          JSON.stringify(message)
        );

        logger.info('Message requeued for retry', {
          queueName,
          messageId,
          retryCount: message.retryCount,
          maxRetries: message.maxRetries
        });
      } else {
        // 进入死信队列
        await this.addToDeadLetterQueue(queueName, message);

        logger.warn('Message moved to dead letter queue', {
          queueName,
          messageId,
          retryCount: message.retryCount
        });
      }

    } catch (error) {
      logger.error('Failed to nack message', {
        queueName,
        messageId,
        error: error.message
      });
    }
  }

  /**
   * 添加到死信队列
   */
  async addToDeadLetterQueue(queueName, messageData) {
    const deadLetterKey = `${this.deadLetterPrefix}${queueName}`;
    await this.redis.lpush(deadLetterKey, JSON.stringify({
      ...messageData,
      deadAt: new Date().toISOString()
    }));
  }

  /**
   * 处理延迟消息
   */
  async processDelayedMessages() {
    try {
      const queueNames = Array.from(this.queues.keys());
      const now = Date.now();

      for (const queueName of queueNames) {
        const delayedKey = `${this.delayedPrefix}${queueName}`;
        
        // 获取到期的延迟消息
        const messages = await this.redis.zrangebyscore(
          delayedKey,
          0,
          now,
          'LIMIT',
          0,
          100
        );

        if (messages.length > 0) {
          // 移动到正常队列
          const pipeline = this.redis.pipeline();
          
          messages.forEach(message => {
            const messageData = JSON.parse(message);
            pipeline.zrem(delayedKey, message);
            pipeline.zadd(
              `${this.queuePrefix}${queueName}`,
              messageData.priority,
              message
            );
          });

          await pipeline.exec();

          logger.debug('Delayed messages processed', {
            queueName,
            messageCount: messages.length
          });
        }
      }

    } catch (error) {
      logger.error('Failed to process delayed messages', {
        error: error.message
      });
    }
  }

  /**
   * 启动消息处理
   */
  startProcessing() {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    // 处理延迟消息
    setInterval(() => {
      this.processDelayedMessages();
    }, 5000); // 每5秒检查一次

    // 处理队列消息
    setInterval(() => {
      this.processQueues();
    }, 1000); // 每秒处理一次

    // 处理超时消息
    setInterval(() => {
      this.processTimeoutMessages();
    }, 30000); // 每30秒检查一次

    logger.info('Message queue processing started');
  }

  /**
   * 处理队列消息
   */
  async processQueues() {
    try {
      for (const [queueName, config] of this.queues) {
        const processor = this.processors.get(queueName);
        if (!processor) {
          continue;
        }

        // 检查当前处理中的消息数量
        const processingCount = await this.getProcessingCount(queueName);
        if (processingCount >= config.maxConcurrency) {
          continue;
        }

        // 获取待处理消息
        const availableSlots = config.maxConcurrency - processingCount;
        const batchSize = Math.min(config.batchSize, availableSlots);
        
        const messages = await this.getMessage(queueName, batchSize);
        
        if (messages.length > 0) {
          // 并发处理消息
          const processingPromises = messages.map(message => 
            this.processMessage(queueName, message, processor)
          );

          // 不等待处理完成，继续处理其他队列
          Promise.allSettled(processingPromises);
        }
      }

    } catch (error) {
      logger.error('Failed to process queues', {
        error: error.message
      });
    }
  }

  /**
   * 处理单个消息
   */
  async processMessage(queueName, messageData, processor) {
    const startTime = Date.now();
    
    try {
      logger.debug('Processing message', {
        queueName,
        messageId: messageData.id
      });

      // 执行处理器
      await processor(messageData.payload, messageData);

      // 确认消息处理完成
      await this.ackMessage(queueName, messageData.id);

      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_process',
        queueName,
        duration,
        true
      );

      logger.debug('Message processed successfully', {
        queueName,
        messageId: messageData.id,
        duration: `${duration}ms`
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'message_queue_process',
        queueName,
        duration,
        false,
        error
      );

      logger.error('Message processing failed', {
        queueName,
        messageId: messageData.id,
        error: error.message,
        duration: `${duration}ms`
      });

      // 拒绝消息，触发重试或进入死信队列
      await this.nackMessage(queueName, messageData.id, true);
    }
  }

  /**
   * 处理超时消息
   */
  async processTimeoutMessages() {
    try {
      const queueNames = Array.from(this.queues.keys());

      for (const queueName of queueNames) {
        const pattern = `${this.processingPrefix}${queueName}:*`;
        const keys = await this.redis.keys(pattern);

        for (const key of keys) {
          const ttl = await this.redis.ttl(key);
          if (ttl <= 0) {
            // 消息已超时，重新处理
            const messageData = await this.redis.get(key);
            if (messageData) {
              const message = JSON.parse(messageData);
              const messageId = message.id;
              
              await this.redis.del(key);
              await this.nackMessage(queueName, messageId, true);

              logger.warn('Timeout message reprocessed', {
                queueName,
                messageId
              });
            }
          }
        }
      }

    } catch (error) {
      logger.error('Failed to process timeout messages', {
        error: error.message
      });
    }
  }

  /**
   * 获取处理中的消息数量
   */
  async getProcessingCount(queueName) {
    try {
      const pattern = `${this.processingPrefix}${queueName}:*`;
      const keys = await this.redis.keys(pattern);
      return keys.length;
    } catch (error) {
      logger.error('Failed to get processing count', {
        queueName,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(queueName) {
    try {
      const queueKey = `${this.queuePrefix}${queueName}`;
      const delayedKey = `${this.delayedPrefix}${queueName}`;
      const deadLetterKey = `${this.deadLetterPrefix}${queueName}`;

      const [
        pendingCount,
        delayedCount,
        deadLetterCount,
        processingCount
      ] = await Promise.all([
        this.redis.zcard(queueKey),
        this.redis.zcard(delayedKey),
        this.redis.llen(deadLetterKey),
        this.getProcessingCount(queueName)
      ]);

      return {
        queueName,
        pendingCount,
        delayedCount,
        processingCount,
        deadLetterCount,
        totalCount: pendingCount + delayedCount + processingCount
      };

    } catch (error) {
      logger.error('Failed to get queue stats', {
        queueName,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 获取队列配置
   */
  getQueueConfig(queueName) {
    return this.queues.get(queueName) || this.defaultConfig;
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 停止处理
   */
  stopProcessing() {
    this.isProcessing = false;
    logger.info('Message queue processing stopped');
  }
}

// 创建单例实例
let messageQueueInstance = null;

class MessageQueueManager {
  static getInstance() {
    if (!messageQueueInstance) {
      messageQueueInstance = new MessageQueue();
    }
    return messageQueueInstance;
  }
}

module.exports = { MessageQueue, MessageQueueManager };
