/**
 * 成本控制优化服务
 * 监控和优化云资源使用，确保月成本控制在$300以内
 */

const { logger } = require('./logger');
const { RedisManager } = require('./redis');
const { DatabaseManager } = require('./database');
const { performanceMonitor } = require('./monitoring');

class CostOptimizer {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.db = DatabaseManager.getInstance();
    
    // 成本预算配置
    this.budgetConfig = {
      monthlyBudget: 300, // $300/月
      dailyBudget: 10,    // $10/天
      hourlyBudget: 0.42, // $0.42/小时
      alertThresholds: {
        warning: 0.8,   // 80%预算时警告
        critical: 0.9,  // 90%预算时严重警告
        emergency: 0.95 // 95%预算时紧急停止
      }
    };

    // 资源成本配置（基于腾讯云定价）
    this.resourceCosts = {
      // 云函数 SCF
      scf: {
        invocation: 0.0000002, // $0.0000002/次调用
        duration: 0.0000166667, // $0.0000166667/GB-秒
        traffic: 0.12 // $0.12/GB流量
      },
      
      // 数据库 MySQL
      mysql: {
        storage: 0.101, // $0.101/GB/月
        backup: 0.101,  // $0.101/GB/月
        connection: 0.02 // $0.02/连接/小时
      },
      
      // Redis缓存
      redis: {
        memory: 0.05, // $0.05/GB/小时
        traffic: 0.12 // $0.12/GB流量
      },
      
      // 对象存储 COS
      cos: {
        storage: 0.024, // $0.024/GB/月
        request: 0.002, // $0.002/万次请求
        traffic: 0.12   // $0.12/GB流量
      },
      
      // API网关
      apiGateway: {
        request: 0.06 // $0.06/万次调用
      },
      
      // AI服务
      ai: {
        textModeration: 0.0015, // $0.0015/千次
        audioModeration: 0.003, // $0.003/分钟
        translation: 0.02       // $0.02/千字符
      }
    };

    // 成本统计
    this.costStats = {
      current: {
        hourly: 0,
        daily: 0,
        monthly: 0
      },
      projected: {
        daily: 0,
        monthly: 0
      },
      breakdown: {
        scf: 0,
        mysql: 0,
        redis: 0,
        cos: 0,
        apiGateway: 0,
        ai: 0
      }
    };

    // 优化策略
    this.optimizationStrategies = {
      // 函数冷启动优化
      coldStart: {
        enabled: true,
        warmupInterval: 300000, // 5分钟
        concurrency: 5
      },
      
      // 缓存优化
      cache: {
        enabled: true,
        ttlOptimization: true,
        compressionThreshold: 1024
      },
      
      // 数据库连接池优化
      database: {
        enabled: true,
        maxConnections: 20,
        idleTimeout: 300000
      },
      
      // 流量优化
      traffic: {
        enabled: true,
        compression: true,
        cdn: true
      }
    };

    // 启动成本监控
    this.startCostMonitoring();
  }

  /**
   * 记录资源使用
   */
  async recordResourceUsage(resourceType, usage, metadata = {}) {
    try {
      const cost = this.calculateResourceCost(resourceType, usage);
      const timestamp = new Date();
      
      // 更新成本统计
      this.updateCostStats(resourceType, cost);
      
      // 记录到Redis
      const key = `cost:usage:${resourceType}:${timestamp.getTime()}`;
      await this.redis.setex(key, 86400, JSON.stringify({
        resourceType,
        usage,
        cost,
        metadata,
        timestamp
      }));
      
      // 检查预算警告
      await this.checkBudgetAlerts();
      
      logger.debug('Resource usage recorded', {
        resourceType,
        usage,
        cost,
        metadata
      });

    } catch (error) {
      logger.error('Failed to record resource usage', {
        resourceType,
        usage,
        error: error.message
      });
    }
  }

  /**
   * 计算资源成本
   */
  calculateResourceCost(resourceType, usage) {
    const costs = this.resourceCosts[resourceType];
    if (!costs) return 0;

    let totalCost = 0;

    switch (resourceType) {
      case 'scf':
        totalCost = (usage.invocations || 0) * costs.invocation +
                   (usage.duration || 0) * costs.duration +
                   (usage.traffic || 0) * costs.traffic;
        break;
        
      case 'mysql':
        totalCost = (usage.storage || 0) * costs.storage / 30 / 24 + // 按小时计算
                   (usage.backup || 0) * costs.backup / 30 / 24 +
                   (usage.connections || 0) * costs.connection;
        break;
        
      case 'redis':
        totalCost = (usage.memory || 0) * costs.memory +
                   (usage.traffic || 0) * costs.traffic;
        break;
        
      case 'cos':
        totalCost = (usage.storage || 0) * costs.storage / 30 / 24 +
                   (usage.requests || 0) * costs.request / 10000 +
                   (usage.traffic || 0) * costs.traffic;
        break;
        
      case 'apiGateway':
        totalCost = (usage.requests || 0) * costs.request / 10000;
        break;
        
      case 'ai':
        totalCost = (usage.textModeration || 0) * costs.textModeration / 1000 +
                   (usage.audioModeration || 0) * costs.audioModeration +
                   (usage.translation || 0) * costs.translation / 1000;
        break;
    }

    return totalCost;
  }

  /**
   * 更新成本统计
   */
  updateCostStats(resourceType, cost) {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDate();
    
    // 更新当前成本
    this.costStats.current.hourly += cost;
    this.costStats.current.daily += cost;
    this.costStats.current.monthly += cost;
    
    // 更新分类成本
    this.costStats.breakdown[resourceType] = (this.costStats.breakdown[resourceType] || 0) + cost;
    
    // 计算预测成本
    this.costStats.projected.daily = this.costStats.current.hourly * 24;
    this.costStats.projected.monthly = this.costStats.current.daily * 30;
  }

  /**
   * 检查预算警告
   */
  async checkBudgetAlerts() {
    const { current, projected } = this.costStats;
    const { alertThresholds, monthlyBudget, dailyBudget, hourlyBudget } = this.budgetConfig;

    // 检查小时预算
    if (current.hourly > hourlyBudget * alertThresholds.emergency) {
      await this.triggerEmergencyStop('hourly budget exceeded');
    } else if (current.hourly > hourlyBudget * alertThresholds.critical) {
      await this.sendCostAlert('critical', 'hourly', current.hourly, hourlyBudget);
    } else if (current.hourly > hourlyBudget * alertThresholds.warning) {
      await this.sendCostAlert('warning', 'hourly', current.hourly, hourlyBudget);
    }

    // 检查日预算
    if (projected.daily > dailyBudget * alertThresholds.emergency) {
      await this.triggerEmergencyStop('daily budget projection exceeded');
    } else if (projected.daily > dailyBudget * alertThresholds.critical) {
      await this.sendCostAlert('critical', 'daily', projected.daily, dailyBudget);
    }

    // 检查月预算
    if (projected.monthly > monthlyBudget * alertThresholds.emergency) {
      await this.triggerEmergencyStop('monthly budget projection exceeded');
    } else if (projected.monthly > monthlyBudget * alertThresholds.critical) {
      await this.sendCostAlert('critical', 'monthly', projected.monthly, monthlyBudget);
    }
  }

  /**
   * 发送成本警告
   */
  async sendCostAlert(level, period, currentCost, budget) {
    const percentage = (currentCost / budget * 100).toFixed(1);
    
    logger.warn(`Cost alert: ${level}`, {
      level,
      period,
      currentCost: currentCost.toFixed(4),
      budget,
      percentage: `${percentage}%`,
      breakdown: this.costStats.breakdown
    });

    // 记录警告到监控系统
    await performanceMonitor.recordCostAlert(level, period, currentCost, budget);

    // 如果是严重警告，启动优化措施
    if (level === 'critical') {
      await this.enableEmergencyOptimizations();
    }
  }

  /**
   * 触发紧急停止
   */
  async triggerEmergencyStop(reason) {
    logger.error('Emergency cost stop triggered', {
      reason,
      currentCosts: this.costStats.current,
      projectedCosts: this.costStats.projected,
      breakdown: this.costStats.breakdown
    });

    // 启用最严格的优化措施
    await this.enableEmergencyOptimizations();
    
    // 限制非关键功能
    await this.disableNonCriticalFeatures();
    
    // 发送紧急通知
    await this.sendEmergencyNotification(reason);
  }

  /**
   * 启用紧急优化措施
   */
  async enableEmergencyOptimizations() {
    try {
      // 1. 减少数据库连接池大小
      this.optimizationStrategies.database.maxConnections = 10;
      
      // 2. 增加缓存TTL
      this.optimizationStrategies.cache.ttlOptimization = true;
      
      // 3. 启用压缩
      this.optimizationStrategies.traffic.compression = true;
      
      // 4. 减少函数并发
      this.optimizationStrategies.coldStart.concurrency = 2;
      
      // 5. 清理过期缓存
      await this.cleanupExpiredCache();
      
      // 6. 优化数据库查询
      await this.optimizeDatabaseQueries();
      
      logger.info('Emergency optimizations enabled');

    } catch (error) {
      logger.error('Failed to enable emergency optimizations', {
        error: error.message
      });
    }
  }

  /**
   * 禁用非关键功能
   */
  async disableNonCriticalFeatures() {
    try {
      // 设置功能开关
      await this.redis.setex('feature:ai_moderation', 3600, 'disabled');
      await this.redis.setex('feature:auto_translation', 3600, 'disabled');
      await this.redis.setex('feature:advanced_analytics', 3600, 'disabled');
      await this.redis.setex('feature:content_recommendations', 3600, 'disabled');
      
      logger.info('Non-critical features disabled');

    } catch (error) {
      logger.error('Failed to disable non-critical features', {
        error: error.message
      });
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanupExpiredCache() {
    try {
      const keys = await this.redis.keys('*');
      let cleanedCount = 0;
      
      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl === -1) { // 没有过期时间的键
          await this.redis.expire(key, 3600); // 设置1小时过期
          cleanedCount++;
        }
      }
      
      logger.info('Cache cleanup completed', { cleanedCount });

    } catch (error) {
      logger.error('Cache cleanup failed', {
        error: error.message
      });
    }
  }

  /**
   * 优化数据库查询
   */
  async optimizeDatabaseQueries() {
    try {
      const connection = await this.db.getConnection();
      
      try {
        // 分析慢查询
        const [slowQueries] = await connection.execute(`
          SELECT query_time, sql_text 
          FROM mysql.slow_log 
          WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
          ORDER BY query_time DESC 
          LIMIT 10
        `);
        
        if (slowQueries.length > 0) {
          logger.warn('Slow queries detected', {
            count: slowQueries.length,
            queries: slowQueries.map(q => ({
              time: q.query_time,
              sql: q.sql_text.substring(0, 100)
            }))
          });
        }
        
        // 优化表
        await connection.execute('OPTIMIZE TABLE users, game_sessions, game_questions');
        
      } finally {
        connection.release();
      }
      
      logger.info('Database optimization completed');

    } catch (error) {
      logger.error('Database optimization failed', {
        error: error.message
      });
    }
  }

  /**
   * 获取成本报告
   */
  async getCostReport(period = 'daily') {
    try {
      const now = new Date();
      let startTime, endTime;
      
      switch (period) {
        case 'hourly':
          startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
          endTime = new Date(startTime.getTime() + 3600000);
          break;
        case 'daily':
          startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          endTime = new Date(startTime.getTime() + 86400000);
          break;
        case 'monthly':
          startTime = new Date(now.getFullYear(), now.getMonth(), 1);
          endTime = new Date(now.getFullYear(), now.getMonth() + 1, 1);
          break;
      }
      
      // 获取使用记录
      const keys = await this.redis.keys(`cost:usage:*`);
      const usageRecords = [];
      
      for (const key of keys) {
        const record = await this.redis.get(key);
        if (record) {
          const parsed = JSON.parse(record);
          const recordTime = new Date(parsed.timestamp);
          
          if (recordTime >= startTime && recordTime < endTime) {
            usageRecords.push(parsed);
          }
        }
      }
      
      // 计算总成本
      const totalCost = usageRecords.reduce((sum, record) => sum + record.cost, 0);
      
      // 按资源类型分组
      const breakdown = usageRecords.reduce((acc, record) => {
        acc[record.resourceType] = (acc[record.resourceType] || 0) + record.cost;
        return acc;
      }, {});
      
      // 计算预算使用率
      const budget = period === 'hourly' ? this.budgetConfig.hourlyBudget :
                    period === 'daily' ? this.budgetConfig.dailyBudget :
                    this.budgetConfig.monthlyBudget;
      
      const budgetUsage = (totalCost / budget * 100).toFixed(1);
      
      return {
        period,
        startTime,
        endTime,
        totalCost: totalCost.toFixed(4),
        budget,
        budgetUsage: `${budgetUsage}%`,
        breakdown,
        recordCount: usageRecords.length,
        projectedMonthlyCost: period === 'daily' ? (totalCost * 30).toFixed(2) : 
                             period === 'hourly' ? (totalCost * 24 * 30).toFixed(2) : 
                             totalCost.toFixed(2)
      };

    } catch (error) {
      logger.error('Failed to generate cost report', {
        period,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 启动成本监控
   */
  startCostMonitoring() {
    // 每小时重置小时成本
    setInterval(() => {
      this.costStats.current.hourly = 0;
    }, 3600000);
    
    // 每天重置日成本
    setInterval(() => {
      this.costStats.current.daily = 0;
    }, 86400000);
    
    // 每月重置月成本
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const timeToNextMonth = nextMonth.getTime() - now.getTime();
    
    setTimeout(() => {
      this.costStats.current.monthly = 0;
      this.costStats.breakdown = {
        scf: 0, mysql: 0, redis: 0, cos: 0, apiGateway: 0, ai: 0
      };
      
      // 设置每月重置
      setInterval(() => {
        this.costStats.current.monthly = 0;
        this.costStats.breakdown = {
          scf: 0, mysql: 0, redis: 0, cos: 0, apiGateway: 0, ai: 0
        };
      }, 30 * 24 * 3600000); // 30天
    }, timeToNextMonth);
    
    logger.info('Cost monitoring started');
  }

  /**
   * 发送紧急通知
   */
  async sendEmergencyNotification(reason) {
    // 这里应该集成实际的通知服务（邮件、短信、钉钉等）
    logger.error('EMERGENCY COST NOTIFICATION', {
      reason,
      timestamp: new Date().toISOString(),
      costs: this.costStats,
      action: 'Emergency optimizations enabled'
    });
  }
}

// 单例模式
let instance = null;

class CostOptimizerSingleton {
  static getInstance() {
    if (!instance) {
      instance = new CostOptimizer();
    }
    return instance;
  }
}

module.exports = { CostOptimizer: CostOptimizerSingleton };
