/**
 * 数据库连接和查询工具
 * 基于MySQL2实现连接池管理和查询封装
 * 优化版本：支持高并发、连接监控、自动重连
 */

const mysql = require('mysql2/promise');
const config = require('../../config');
const { performance } = require('perf_hooks');

class DatabaseManager {
  constructor() {
    this.pool = null;
    this.isConnected = false;
    this.connectionStats = {
      totalQueries: 0,
      slowQueries: 0,
      errors: 0,
      avgResponseTime: 0,
      lastHealthCheck: null
    };
    this.slowQueryThreshold = 1000; // 1秒
    this.healthCheckInterval = null;
  }

  /**
   * 初始化数据库连接池
   */
  async connect() {
    if (this.pool) {
      return this.pool;
    }

    try {
      // 优化的连接池配置
      const poolConfig = {
        host: config.database.host,
        port: config.database.port,
        user: config.database.user,
        password: config.database.password,
        database: config.database.database,
        charset: config.database.charset,
        timezone: config.database.timezone,
        // 优化连接池参数
        connectionLimit: Math.max(config.database.connectionLimit || 20, 20), // 最少20个连接
        acquireTimeout: config.database.acquireTimeout || 60000,
        timeout: config.database.timeout || 60000,
        idleTimeout: 300000, // 5分钟空闲超时
        queueLimit: 0, // 无限队列
        reconnect: true,
        ssl: config.database.ssl,
        supportBigNumbers: true,
        bigNumberStrings: true,
        // 新增优化参数
        multipleStatements: false, // 安全考虑
        namedPlaceholders: true,
        dateStrings: false,
        debug: config.isDev,
        trace: config.isDev
      };

      this.pool = mysql.createPool(poolConfig);

      // 监听连接池事件
      this.pool.on('connection', (connection) => {
        console.log(`New database connection established as id ${connection.threadId}`);
      });

      this.pool.on('error', (err) => {
        console.error('Database pool error:', err);
        this.connectionStats.errors++;
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          this.handleDisconnect();
        }
      });

      // 测试连接
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      this.isConnected = true;
      console.log('Database connected successfully with optimized pool');

      // 启动健康检查
      this.startHealthCheck();

      return this.pool;
    } catch (error) {
      console.error('Database connection failed:', error);
      this.connectionStats.errors++;
      throw error;
    }
  }

  /**
   * 处理连接断开
   */
  handleDisconnect() {
    console.log('Database connection lost, attempting to reconnect...');
    this.isConnected = false;

    setTimeout(async () => {
      try {
        await this.connect();
        console.log('Database reconnected successfully');
      } catch (error) {
        console.error('Failed to reconnect to database:', error);
        this.handleDisconnect(); // 递归重试
      }
    }, 2000);
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.healthCheck();
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 获取连接池
   */
  async getPool() {
    if (!this.pool || !this.isConnected) {
      await this.connect();
    }
    return this.pool;
  }

  /**
   * 执行查询（带性能监控）
   * @param {string} sql SQL语句
   * @param {Array} params 参数
   * @returns {Promise<Array>} 查询结果
   */
  async query(sql, params = []) {
    const startTime = performance.now();

    try {
      const pool = await this.getPool();
      const [rows] = await pool.execute(sql, params);

      // 更新统计信息
      const duration = performance.now() - startTime;
      this.updateStats(duration);

      // 记录慢查询
      if (duration > this.slowQueryThreshold) {
        this.logSlowQuery(sql, params, duration);
      }

      return rows;
    } catch (error) {
      this.connectionStats.errors++;
      console.error('Database query error:', error);
      console.error('SQL:', sql);
      console.error('Params:', params);

      // 如果是连接错误，尝试重连
      if (this.isConnectionError(error)) {
        this.handleDisconnect();
      }

      throw error;
    }
  }

  /**
   * 更新统计信息
   */
  updateStats(duration) {
    this.connectionStats.totalQueries++;

    // 计算平均响应时间
    const currentAvg = this.connectionStats.avgResponseTime;
    const totalQueries = this.connectionStats.totalQueries;
    this.connectionStats.avgResponseTime =
      (currentAvg * (totalQueries - 1) + duration) / totalQueries;
  }

  /**
   * 记录慢查询
   */
  logSlowQuery(sql, params, duration) {
    this.connectionStats.slowQueries++;
    console.warn(`Slow query detected (${duration.toFixed(2)}ms):`, {
      sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
      params: params.length > 0 ? params : 'none',
      duration: `${duration.toFixed(2)}ms`
    });
  }

  /**
   * 判断是否为连接错误
   */
  isConnectionError(error) {
    const connectionErrors = [
      'PROTOCOL_CONNECTION_LOST',
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNREFUSED'
    ];
    return connectionErrors.includes(error.code);
  }

  /**
   * 执行事务（改进版）
   * @param {Function} callback 事务回调函数
   * @param {Object} options 事务选项
   * @returns {Promise<any>} 事务结果
   */
  async transaction(callback, options = {}) {
    const {
      isolationLevel = 'READ COMMITTED',
      timeout = 30000,
      retryCount = 3
    } = options;

    let attempt = 0;

    while (attempt < retryCount) {
      const pool = await this.getPool();
      const connection = await pool.getConnection();
      const startTime = performance.now();

      try {
        // 设置事务隔离级别
        if (isolationLevel) {
          await connection.execute(`SET TRANSACTION ISOLATION LEVEL ${isolationLevel}`);
        }

        await connection.beginTransaction();

        // 设置事务超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Transaction timeout')), timeout);
        });

        // 创建增强的事务上下文
        const tx = {
          query: async (sql, params = []) => {
            const queryStart = performance.now();
            const [rows] = await connection.execute(sql, params);
            const queryDuration = performance.now() - queryStart;

            // 记录事务内的慢查询
            if (queryDuration > this.slowQueryThreshold) {
              console.warn(`Slow query in transaction (${queryDuration.toFixed(2)}ms):`, sql.substring(0, 100));
            }

            return rows;
          },
          rollback: async () => {
            await connection.rollback();
          },
          savepoint: async (name) => {
            await connection.execute(`SAVEPOINT ${name}`);
          },
          rollbackTo: async (name) => {
            await connection.execute(`ROLLBACK TO SAVEPOINT ${name}`);
          },
          releaseSavepoint: async (name) => {
            await connection.execute(`RELEASE SAVEPOINT ${name}`);
          }
        };

        // 执行事务逻辑
        const result = await Promise.race([
          callback(tx),
          timeoutPromise
        ]);

        await connection.commit();

        const duration = performance.now() - startTime;
        if (duration > 5000) { // 记录长事务
          console.warn(`Long transaction detected: ${duration.toFixed(2)}ms`);
        }

        return result;

      } catch (error) {
        try {
          await connection.rollback();
        } catch (rollbackError) {
          console.error('Rollback failed:', rollbackError);
        }

        // 检查是否需要重试
        if (this.shouldRetryTransaction(error) && attempt < retryCount - 1) {
          attempt++;
          console.warn(`Transaction failed, retrying (${attempt}/${retryCount}):`, error.message);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 指数退避
          continue;
        }

        console.error('Transaction error:', error);
        this.connectionStats.errors++;
        throw error;
      } finally {
        connection.release();
      }
    }
  }

  /**
   * 判断事务是否应该重试
   */
  shouldRetryTransaction(error) {
    const retryableErrors = [
      'ER_LOCK_WAIT_TIMEOUT',
      'ER_LOCK_DEADLOCK',
      'PROTOCOL_CONNECTION_LOST',
      'ECONNRESET'
    ];
    return retryableErrors.includes(error.code);
  }

  /**
   * 插入数据
   * @param {string} table 表名
   * @param {Object} data 数据对象
   * @returns {Promise<Object>} 插入结果
   */
  async insert(table, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    
    try {
      const pool = await this.getPool();
      const [result] = await pool.execute(sql, values);
      
      return {
        insertId: result.insertId,
        affectedRows: result.affectedRows
      };
    } catch (error) {
      console.error('Insert error:', error);
      throw error;
    }
  }

  /**
   * 批量插入数据
   * @param {string} table 表名
   * @param {Array} dataArray 数据数组
   * @returns {Promise<Object>} 插入结果
   */
  async batchInsert(table, dataArray) {
    if (!dataArray || dataArray.length === 0) {
      return { affectedRows: 0 };
    }

    const fields = Object.keys(dataArray[0]);
    const placeholders = '(' + fields.map(() => '?').join(', ') + ')';
    const valuesPlaceholder = dataArray.map(() => placeholders).join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES ${valuesPlaceholder}`;
    
    // 展平所有值
    const values = dataArray.reduce((acc, item) => {
      return acc.concat(Object.values(item));
    }, []);
    
    try {
      const pool = await this.getPool();
      const [result] = await pool.execute(sql, values);
      
      return {
        insertId: result.insertId,
        affectedRows: result.affectedRows
      };
    } catch (error) {
      console.error('Batch insert error:', error);
      throw error;
    }
  }

  /**
   * 更新数据
   * @param {string} table 表名
   * @param {Object} data 更新数据
   * @param {Object} where 条件
   * @returns {Promise<Object>} 更新结果
   */
  async update(table, data, where) {
    const setFields = Object.keys(data).map(field => `${field} = ?`).join(', ');
    const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
    
    const sql = `UPDATE ${table} SET ${setFields} WHERE ${whereFields}`;
    const values = [...Object.values(data), ...Object.values(where)];
    
    try {
      const pool = await this.getPool();
      const [result] = await pool.execute(sql, values);
      
      return {
        affectedRows: result.affectedRows,
        changedRows: result.changedRows
      };
    } catch (error) {
      console.error('Update error:', error);
      throw error;
    }
  }

  /**
   * 删除数据
   * @param {string} table 表名
   * @param {Object} where 条件
   * @returns {Promise<Object>} 删除结果
   */
  async delete(table, where) {
    const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
    const sql = `DELETE FROM ${table} WHERE ${whereFields}`;
    const values = Object.values(where);
    
    try {
      const pool = await this.getPool();
      const [result] = await pool.execute(sql, values);
      
      return {
        affectedRows: result.affectedRows
      };
    } catch (error) {
      console.error('Delete error:', error);
      throw error;
    }
  }

  /**
   * 查找单条记录
   * @param {string} table 表名
   * @param {Object} where 条件
   * @param {string} fields 字段
   * @returns {Promise<Object|null>} 查询结果
   */
  async findOne(table, where = {}, fields = '*') {
    let sql = `SELECT ${fields} FROM ${table}`;
    let values = [];
    
    if (Object.keys(where).length > 0) {
      const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
      sql += ` WHERE ${whereFields}`;
      values = Object.values(where);
    }
    
    sql += ' LIMIT 1';
    
    const rows = await this.query(sql, values);
    return rows.length > 0 ? rows[0] : null;
  }

  /**
   * 查找多条记录
   * @param {string} table 表名
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 查询结果
   */
  async find(table, options = {}) {
    const {
      where = {},
      fields = '*',
      orderBy = '',
      limit = 0,
      offset = 0
    } = options;
    
    let sql = `SELECT ${fields} FROM ${table}`;
    let values = [];
    
    // WHERE条件
    if (Object.keys(where).length > 0) {
      const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
      sql += ` WHERE ${whereFields}`;
      values = Object.values(where);
    }
    
    // ORDER BY
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }
    
    // LIMIT和OFFSET
    if (limit > 0) {
      sql += ` LIMIT ${limit}`;
      if (offset > 0) {
        sql += ` OFFSET ${offset}`;
      }
    }
    
    return await this.query(sql, values);
  }

  /**
   * 计数查询
   * @param {string} table 表名
   * @param {Object} where 条件
   * @returns {Promise<number>} 计数结果
   */
  async count(table, where = {}) {
    let sql = `SELECT COUNT(*) as count FROM ${table}`;
    let values = [];
    
    if (Object.keys(where).length > 0) {
      const whereFields = Object.keys(where).map(field => `${field} = ?`).join(' AND ');
      sql += ` WHERE ${whereFields}`;
      values = Object.values(where);
    }
    
    const rows = await this.query(sql, values);
    return rows[0].count;
  }

  /**
   * 检查记录是否存在
   * @param {string} table 表名
   * @param {Object} where 条件
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(table, where) {
    const count = await this.count(table, where);
    return count > 0;
  }

  /**
   * 分页查询
   * @param {string} table 表名
   * @param {Object} options 查询选项
   * @returns {Promise<Object>} 分页结果
   */
  async paginate(table, options = {}) {
    const {
      where = {},
      fields = '*',
      orderBy = '',
      page = 1,
      size = 20
    } = options;
    
    const offset = (page - 1) * size;
    
    // 获取总数
    const total = await this.count(table, where);
    
    // 获取数据
    const items = await this.find(table, {
      where,
      fields,
      orderBy,
      limit: size,
      offset
    });
    
    return {
      items,
      pagination: {
        page,
        size,
        total,
        totalPages: Math.ceil(total / size),
        hasNext: page * size < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * 关闭连接池
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      this.isConnected = false;
      console.log('Database connection closed');
    }
  }

  /**
   * 增强的健康检查
   */
  async healthCheck() {
    const startTime = performance.now();

    try {
      const pool = await this.getPool();
      const connection = await pool.getConnection();

      // 执行简单查询测试
      await connection.execute('SELECT 1 as test');
      await connection.ping();

      const duration = performance.now() - startTime;
      connection.release();

      const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        responseTime: `${duration.toFixed(2)}ms`,
        stats: this.getConnectionStats(),
        poolInfo: this.getPoolInfo()
      };

      this.connectionStats.lastHealthCheck = healthData;
      return healthData;

    } catch (error) {
      const healthData = {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
        stats: this.getConnectionStats()
      };

      this.connectionStats.lastHealthCheck = healthData;
      return healthData;
    }
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    return {
      totalQueries: this.connectionStats.totalQueries,
      slowQueries: this.connectionStats.slowQueries,
      errors: this.connectionStats.errors,
      avgResponseTime: `${this.connectionStats.avgResponseTime.toFixed(2)}ms`,
      slowQueryRate: this.connectionStats.totalQueries > 0
        ? `${((this.connectionStats.slowQueries / this.connectionStats.totalQueries) * 100).toFixed(2)}%`
        : '0%',
      errorRate: this.connectionStats.totalQueries > 0
        ? `${((this.connectionStats.errors / this.connectionStats.totalQueries) * 100).toFixed(2)}%`
        : '0%'
    };
  }

  /**
   * 获取连接池信息
   */
  getPoolInfo() {
    if (!this.pool) return null;

    return {
      connectionLimit: this.pool.config.connectionLimit,
      acquireTimeout: this.pool.config.acquireTimeout,
      timeout: this.pool.config.timeout,
      // 注意：这些属性可能不存在于所有版本的mysql2中
      activeConnections: this.pool._allConnections ? this.pool._allConnections.length : 'unknown',
      freeConnections: this.pool._freeConnections ? this.pool._freeConnections.length : 'unknown',
      queuedRequests: this.pool._connectionQueue ? this.pool._connectionQueue.length : 'unknown'
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.connectionStats = {
      totalQueries: 0,
      slowQueries: 0,
      errors: 0,
      avgResponseTime: 0,
      lastHealthCheck: null
    };
  }

  /**
   * 关闭连接池
   */
  async close() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      this.isConnected = false;
      console.log('Database connection closed');
    }
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }
}

// 全局数据库实例（单例模式）
const dbInstance = DatabaseManager.getInstance();

module.exports = dbInstance;