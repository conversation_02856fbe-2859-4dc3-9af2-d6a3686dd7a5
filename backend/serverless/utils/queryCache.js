/**
 * 查询缓存优化服务
 * 实现智能查询缓存、分页优化、慢查询监控
 */

const { RedisManager } = require('./redis');
const { DatabaseManager } = require('./database');
const { logger } = require('./logger');
const { performanceMonitor } = require('./monitoring');

class QueryCacheManager {
  constructor() {
    this.redis = RedisManager.getInstance();
    this.db = DatabaseManager.getInstance();
    
    // 缓存配置
    this.cacheConfig = {
      defaultTTL: 3600, // 1小时
      shortTTL: 300,    // 5分钟
      longTTL: 86400,   // 24小时
      maxCacheSize: 1000000, // 1MB
      compressionThreshold: 10000 // 10KB
    };
    
    // 缓存键前缀
    this.prefixes = {
      query: 'query:',
      pagination: 'page:',
      aggregation: 'agg:',
      user: 'user:',
      content: 'content:',
      stats: 'stats:'
    };
    
    // 慢查询阈值（毫秒）
    this.slowQueryThreshold = 1000;
    
    // 缓存命中率统计
    this.hitRateStats = {
      hits: 0,
      misses: 0,
      errors: 0
    };
  }

  /**
   * 执行带缓存的查询
   */
  async executeWithCache(cacheKey, queryFn, options = {}) {
    const startTime = Date.now();
    const {
      ttl = this.cacheConfig.defaultTTL,
      compress = false,
      tags = [],
      skipCache = false
    } = options;

    try {
      // 检查是否跳过缓存
      if (skipCache) {
        const result = await queryFn();
        return this.formatResult(result, false, Date.now() - startTime);
      }

      // 尝试从缓存获取
      const cachedResult = await this.getFromCache(cacheKey, compress);
      if (cachedResult !== null) {
        this.hitRateStats.hits++;
        return this.formatResult(cachedResult, true, Date.now() - startTime);
      }

      // 缓存未命中，执行查询
      this.hitRateStats.misses++;
      const result = await queryFn();
      
      // 存储到缓存
      await this.setToCache(cacheKey, result, ttl, compress, tags);
      
      const duration = Date.now() - startTime;
      
      // 监控慢查询
      if (duration > this.slowQueryThreshold) {
        await this.logSlowQuery(cacheKey, duration, options);
      }

      return this.formatResult(result, false, duration);

    } catch (error) {
      this.hitRateStats.errors++;
      logger.error('Query cache execution failed', {
        cacheKey,
        error: error.message,
        duration: Date.now() - startTime
      });
      throw error;
    }
  }

  /**
   * 分页查询优化
   */
  async executePaginatedQuery(baseQuery, params, paginationOptions = {}) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      cacheKey,
      ttl = this.cacheConfig.shortTTL
    } = paginationOptions;

    const offset = (page - 1) * limit;
    const cacheKeyWithPagination = `${this.prefixes.pagination}${cacheKey}:${page}:${limit}:${sortBy}:${sortOrder}`;

    return await this.executeWithCache(
      cacheKeyWithPagination,
      async () => {
        const connection = await this.db.getConnection();
        
        try {
          // 构建查询
          const countQuery = baseQuery.replace(/SELECT.*?FROM/, 'SELECT COUNT(*) as total FROM');
          const dataQuery = `${baseQuery} ORDER BY ${sortBy} ${sortOrder} LIMIT ? OFFSET ?`;

          // 执行计数查询
          const [countResult] = await connection.execute(countQuery, params);
          const total = countResult[0].total;

          // 执行数据查询
          const [dataResult] = await connection.execute(dataQuery, [...params, limit, offset]);

          return {
            data: dataResult,
            pagination: {
              page,
              limit,
              total,
              totalPages: Math.ceil(total / limit),
              hasNext: page < Math.ceil(total / limit),
              hasPrev: page > 1
            }
          };

        } finally {
          connection.release();
        }
      },
      { ttl, tags: ['pagination'] }
    );
  }

  /**
   * 聚合查询缓存
   */
  async executeAggregationQuery(queryName, queryFn, options = {}) {
    const {
      timeRange = '1h',
      refreshInterval = 300, // 5分钟
      groupBy = []
    } = options;

    const cacheKey = `${this.prefixes.aggregation}${queryName}:${timeRange}:${groupBy.join(',')}`;
    
    return await this.executeWithCache(
      cacheKey,
      queryFn,
      { 
        ttl: refreshInterval,
        tags: ['aggregation', queryName],
        compress: true
      }
    );
  }

  /**
   * 用户相关查询缓存
   */
  async executeUserQuery(userId, queryName, queryFn, options = {}) {
    const cacheKey = `${this.prefixes.user}${userId}:${queryName}`;
    
    return await this.executeWithCache(
      cacheKey,
      queryFn,
      {
        ttl: this.cacheConfig.shortTTL,
        tags: ['user', userId, queryName],
        ...options
      }
    );
  }

  /**
   * 内容相关查询缓存
   */
  async executeContentQuery(contentType, queryName, queryFn, options = {}) {
    const cacheKey = `${this.prefixes.content}${contentType}:${queryName}`;
    
    return await this.executeWithCache(
      cacheKey,
      queryFn,
      {
        ttl: this.cacheConfig.defaultTTL,
        tags: ['content', contentType, queryName],
        ...options
      }
    );
  }

  /**
   * 统计查询缓存
   */
  async executeStatsQuery(statsType, queryFn, options = {}) {
    const cacheKey = `${this.prefixes.stats}${statsType}:${Date.now() - Date.now() % 300000}`; // 5分钟对齐
    
    return await this.executeWithCache(
      cacheKey,
      queryFn,
      {
        ttl: this.cacheConfig.shortTTL,
        tags: ['stats', statsType],
        compress: true,
        ...options
      }
    );
  }

  /**
   * 从缓存获取数据
   */
  async getFromCache(cacheKey, compress = false) {
    try {
      const cached = await this.redis.get(cacheKey);
      if (!cached) return null;

      let data = JSON.parse(cached);
      
      // 解压缩
      if (compress && data.compressed) {
        data = await this.decompress(data.data);
      }

      return data;

    } catch (error) {
      logger.error('Failed to get from cache', {
        cacheKey,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 存储数据到缓存
   */
  async setToCache(cacheKey, data, ttl, compress = false, tags = []) {
    try {
      let cacheData = data;
      
      // 检查数据大小并压缩
      const dataSize = JSON.stringify(data).length;
      if (compress || dataSize > this.cacheConfig.compressionThreshold) {
        cacheData = {
          compressed: true,
          data: await this.compress(data)
        };
      }

      // 检查缓存大小限制
      if (JSON.stringify(cacheData).length > this.cacheConfig.maxCacheSize) {
        logger.warn('Cache data too large, skipping cache', {
          cacheKey,
          size: JSON.stringify(cacheData).length
        });
        return;
      }

      await this.redis.setex(cacheKey, ttl, JSON.stringify(cacheData));
      
      // 添加标签索引
      if (tags.length > 0) {
        await this.addCacheTagIndex(cacheKey, tags);
      }

    } catch (error) {
      logger.error('Failed to set cache', {
        cacheKey,
        error: error.message
      });
    }
  }

  /**
   * 按标签清除缓存
   */
  async clearCacheByTags(tags) {
    try {
      const cacheKeys = new Set();
      
      for (const tag of tags) {
        const tagKeys = await this.redis.smembers(`tag:${tag}`);
        tagKeys.forEach(key => cacheKeys.add(key));
      }

      if (cacheKeys.size > 0) {
        const keysArray = Array.from(cacheKeys);
        await this.redis.del(...keysArray);
        
        // 清除标签索引
        for (const tag of tags) {
          await this.redis.del(`tag:${tag}`);
        }

        logger.info('Cache cleared by tags', {
          tags,
          clearedKeys: keysArray.length
        });
      }

    } catch (error) {
      logger.error('Failed to clear cache by tags', {
        tags,
        error: error.message
      });
    }
  }

  /**
   * 清除用户相关缓存
   */
  async clearUserCache(userId) {
    await this.clearCacheByTags(['user', userId]);
  }

  /**
   * 清除内容相关缓存
   */
  async clearContentCache(contentType) {
    await this.clearCacheByTags(['content', contentType]);
  }

  /**
   * 预热缓存
   */
  async warmupCache(warmupQueries) {
    logger.info('Starting cache warmup', {
      queryCount: warmupQueries.length
    });

    const results = await Promise.allSettled(
      warmupQueries.map(async ({ cacheKey, queryFn, options }) => {
        try {
          await this.executeWithCache(cacheKey, queryFn, options);
          return { cacheKey, status: 'success' };
        } catch (error) {
          return { cacheKey, status: 'error', error: error.message };
        }
      })
    );

    const successful = results.filter(r => r.value?.status === 'success').length;
    const failed = results.filter(r => r.value?.status === 'error').length;

    logger.info('Cache warmup completed', {
      successful,
      failed,
      total: warmupQueries.length
    });

    return { successful, failed, total: warmupQueries.length };
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    try {
      const info = await this.redis.info('memory');
      const keyCount = await this.redis.dbsize();
      
      const hitRate = this.hitRateStats.hits + this.hitRateStats.misses > 0 
        ? (this.hitRateStats.hits / (this.hitRateStats.hits + this.hitRateStats.misses)) * 100 
        : 0;

      return {
        hitRate: Math.round(hitRate * 100) / 100,
        hits: this.hitRateStats.hits,
        misses: this.hitRateStats.misses,
        errors: this.hitRateStats.errors,
        keyCount,
        memoryUsage: this.parseMemoryInfo(info)
      };

    } catch (error) {
      logger.error('Failed to get cache stats', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * 添加缓存标签索引
   */
  async addCacheTagIndex(cacheKey, tags) {
    try {
      for (const tag of tags) {
        await this.redis.sadd(`tag:${tag}`, cacheKey);
        await this.redis.expire(`tag:${tag}`, this.cacheConfig.longTTL);
      }
    } catch (error) {
      logger.error('Failed to add cache tag index', {
        cacheKey,
        tags,
        error: error.message
      });
    }
  }

  /**
   * 记录慢查询
   */
  async logSlowQuery(cacheKey, duration, options) {
    const slowQueryLog = {
      cacheKey,
      duration,
      options,
      timestamp: new Date().toISOString(),
      threshold: this.slowQueryThreshold
    };

    logger.warn('Slow query detected', slowQueryLog);

    // 记录到性能监控
    await performanceMonitor.recordDatabaseMetrics(
      'slow_query',
      cacheKey,
      duration,
      true
    );
  }

  /**
   * 压缩数据
   */
  async compress(data) {
    // 简化实现，实际应该使用 zlib 或其他压缩库
    return Buffer.from(JSON.stringify(data)).toString('base64');
  }

  /**
   * 解压缩数据
   */
  async decompress(compressedData) {
    // 简化实现，实际应该使用 zlib 或其他压缩库
    return JSON.parse(Buffer.from(compressedData, 'base64').toString());
  }

  /**
   * 格式化查询结果
   */
  formatResult(data, fromCache, duration) {
    return {
      data,
      meta: {
        fromCache,
        duration,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 解析内存信息
   */
  parseMemoryInfo(info) {
    const lines = info.split('\r\n');
    const memoryInfo = {};
    
    lines.forEach(line => {
      if (line.includes('used_memory:')) {
        memoryInfo.used = parseInt(line.split(':')[1]);
      } else if (line.includes('used_memory_human:')) {
        memoryInfo.usedHuman = line.split(':')[1];
      } else if (line.includes('maxmemory:')) {
        memoryInfo.max = parseInt(line.split(':')[1]);
      }
    });

    return memoryInfo;
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.hitRateStats = {
      hits: 0,
      misses: 0,
      errors: 0
    };
  }
}

// 单例模式
let instance = null;

class QueryCache {
  static getInstance() {
    if (!instance) {
      instance = new QueryCacheManager();
    }
    return instance;
  }
}

module.exports = { QueryCache };
