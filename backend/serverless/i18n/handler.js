/**
 * 国际化服务处理器
 * 处理多语言内容管理、翻译任务、语言切换等功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { I18nService } = require('./i18nService');
const { TranslationService } = require('./translationService');
const { logger } = require('../utils/logger');

class I18nHandler {
  constructor() {
    this.i18nService = new I18nService();
    this.translationService = new TranslationService();
  }

  /**
   * 获取支持的语言列表
   */
  async getLanguages(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getLanguages'
    });

    try {
      const { active = true } = event.queryStringParameters || {};

      const languages = await this.i18nService.getLanguages({
        activeOnly: active === 'true'
      });

      i18nLogger.debug('Languages retrieved', {
        languageCount: languages.length,
        activeOnly: active
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: languages
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get languages', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get languages'
        })
      };
    }
  }

  /**
   * 获取翻译内容
   */
  async getTranslations(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getTranslations'
    });

    try {
      const {
        language = 'zh-CN',
        category,
        keys,
        namespace
      } = event.queryStringParameters || {};

      const translations = await this.i18nService.getTranslations({
        language,
        category,
        keys: keys ? keys.split(',') : undefined,
        namespace
      });

      i18nLogger.debug('Translations retrieved', {
        language,
        category,
        translationCount: Object.keys(translations).length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: translations
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get translations', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get translations'
        })
      };
    }
  }

  /**
   * 批量获取翻译内容
   */
  async getBatchTranslations(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getBatchTranslations'
    });

    try {
      const { languages, categories, keys } = JSON.parse(event.body || '{}');

      if (!languages || !Array.isArray(languages)) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Languages array is required'
          })
        };
      }

      const batchTranslations = await this.i18nService.getBatchTranslations({
        languages,
        categories,
        keys
      });

      i18nLogger.debug('Batch translations retrieved', {
        languageCount: languages.length,
        totalTranslations: Object.keys(batchTranslations).reduce(
          (total, lang) => total + Object.keys(batchTranslations[lang]).length, 0
        )
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: batchTranslations
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get batch translations', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get batch translations'
        })
      };
    }
  }

  /**
   * 创建翻译键
   */
  async createTranslationKey(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'createTranslationKey'
    });

    try {
      const {
        keyName,
        category,
        description,
        defaultValue
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;
      const userRole = event.user?.role;

      if (!keyName || !category || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Key name, category, and user authentication are required'
          })
        };
      }

      // 检查权限
      if (userRole !== 'admin' && userRole !== 'translator') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const translationKey = await this.i18nService.createTranslationKey({
        keyName,
        category,
        description,
        defaultValue,
        createdBy: userId
      });

      i18nLogger.info('Translation key created', {
        keyId: translationKey.id,
        keyName,
        category,
        createdBy: userId
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: translationKey
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to create translation key', {
        error: error.message
      });

      const statusCode = error.message.includes('already exists') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 更新翻译内容
   */
  async updateTranslation(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'updateTranslation'
    });

    try {
      const { keyId } = event.pathParameters;
      const {
        language,
        value,
        isApproved
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;
      const userRole = event.user?.role;

      if (!keyId || !language || !value || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Key ID, language, value, and user authentication are required'
          })
        };
      }

      // 检查权限
      if (userRole !== 'admin' && userRole !== 'translator' && userRole !== 'reviewer') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const translation = await this.i18nService.updateTranslation({
        keyId,
        language,
        value,
        isApproved: isApproved && (userRole === 'admin' || userRole === 'reviewer'),
        translatorId: userId
      });

      i18nLogger.info('Translation updated', {
        keyId,
        language,
        translatorId: userId,
        isApproved
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: translation
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to update translation', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update translation'
        })
      };
    }
  }

  /**
   * 创建翻译任务
   */
  async createTranslationTask(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'createTranslationTask'
    });

    try {
      const {
        title,
        description,
        sourceLanguage,
        targetLanguage,
        contentType,
        priority,
        deadline,
        keyIds
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;
      const userRole = event.user?.role;

      if (!title || !sourceLanguage || !targetLanguage || !contentType || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Title, source language, target language, content type, and user authentication are required'
          })
        };
      }

      // 检查权限
      if (userRole !== 'admin' && userRole !== 'project_manager') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const task = await this.translationService.createTask({
        title,
        description,
        sourceLanguage,
        targetLanguage,
        contentType,
        priority: priority || 'medium',
        deadline,
        keyIds: keyIds || [],
        createdBy: userId
      });

      i18nLogger.info('Translation task created', {
        taskId: task.id,
        title,
        sourceLanguage,
        targetLanguage,
        createdBy: userId
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: task
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to create translation task', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to create translation task'
        })
      };
    }
  }

  /**
   * 获取翻译任务列表
   */
  async getTranslationTasks(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getTranslationTasks'
    });

    try {
      const {
        page = 1,
        limit = 20,
        status,
        assigneeId,
        sourceLanguage,
        targetLanguage,
        contentType,
        priority
      } = event.queryStringParameters || {};

      const userId = event.user?.id;
      const userRole = event.user?.role;

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const tasks = await this.translationService.getTasks({
        page: parseInt(page),
        limit: parseInt(limit),
        status,
        assigneeId: assigneeId || (userRole === 'translator' ? userId : undefined),
        sourceLanguage,
        targetLanguage,
        contentType,
        priority,
        userId,
        userRole
      });

      i18nLogger.debug('Translation tasks retrieved', {
        taskCount: tasks.tasks.length,
        page,
        limit,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: tasks
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get translation tasks', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get translation tasks'
        })
      };
    }
  }

  /**
   * 分配翻译任务
   */
  async assignTranslationTask(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'assignTranslationTask'
    });

    try {
      const { taskId } = event.pathParameters;
      const { assigneeId, reviewerId } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;
      const userRole = event.user?.role;

      if (!taskId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Task ID and user authentication are required'
          })
        };
      }

      // 检查权限
      if (userRole !== 'admin' && userRole !== 'project_manager') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const task = await this.translationService.assignTask({
        taskId,
        assigneeId,
        reviewerId,
        assignedBy: userId
      });

      i18nLogger.info('Translation task assigned', {
        taskId,
        assigneeId,
        reviewerId,
        assignedBy: userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: task
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to assign translation task', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to assign translation task'
        })
      };
    }
  }

  /**
   * 更新翻译任务进度
   */
  async updateTaskProgress(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'updateTaskProgress'
    });

    try {
      const { taskId } = event.pathParameters;
      const { progress, status, translatedItems } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;

      if (!taskId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Task ID and user authentication are required'
          })
        };
      }

      const task = await this.translationService.updateTaskProgress({
        taskId,
        progress,
        status,
        translatedItems,
        updatedBy: userId
      });

      i18nLogger.info('Translation task progress updated', {
        taskId,
        progress,
        status,
        updatedBy: userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: task
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to update task progress', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update task progress'
        })
      };
    }
  }

  /**
   * 获取用户语言偏好
   */
  async getUserLanguagePreference(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getUserLanguagePreference'
    });

    try {
      const userId = event.user?.id;

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const preference = await this.i18nService.getUserLanguagePreference(userId);

      i18nLogger.debug('User language preference retrieved', {
        userId,
        primaryLanguage: preference?.primaryLanguage
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: preference
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get user language preference', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get user language preference'
        })
      };
    }
  }

  /**
   * 更新用户语言偏好
   */
  async updateUserLanguagePreference(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'updateUserLanguagePreference'
    });

    try {
      const {
        primaryLanguage,
        secondaryLanguages,
        autoTranslate,
        translationQuality
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;

      if (!primaryLanguage || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Primary language and user authentication are required'
          })
        };
      }

      const preference = await this.i18nService.updateUserLanguagePreference({
        userId,
        primaryLanguage,
        secondaryLanguages: secondaryLanguages || [],
        autoTranslate: autoTranslate !== undefined ? autoTranslate : true,
        translationQuality: translationQuality || 'balanced'
      });

      i18nLogger.info('User language preference updated', {
        userId,
        primaryLanguage,
        autoTranslate
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: preference
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to update user language preference', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update user language preference'
        })
      };
    }
  }

  /**
   * 获取翻译统计信息
   */
  async getTranslationStats(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'getTranslationStats'
    });

    try {
      const {
        type = 'completion',
        language,
        timeRange = '30d'
      } = event.queryStringParameters || {};

      const userRole = event.user?.role;

      // 检查权限
      if (userRole !== 'admin' && userRole !== 'project_manager' && userRole !== 'reviewer') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const stats = await this.i18nService.getTranslationStats({
        type,
        language,
        timeRange
      });

      i18nLogger.debug('Translation stats retrieved', {
        type,
        language,
        timeRange,
        statsCount: Object.keys(stats).length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      i18nLogger.error('Failed to get translation stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get translation stats'
        })
      };
    }
  }

  /**
   * 自动翻译内容
   */
  async autoTranslate(event, context) {
    const i18nLogger = logger.child({
      requestId: context.requestId,
      action: 'autoTranslate'
    });

    try {
      const {
        text,
        sourceLanguage,
        targetLanguage,
        quality = 'balanced'
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;

      if (!text || !sourceLanguage || !targetLanguage || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Text, source language, target language, and user authentication are required'
          })
        };
      }

      const translation = await this.translationService.autoTranslate({
        text,
        sourceLanguage,
        targetLanguage,
        quality,
        userId
      });

      i18nLogger.info('Auto translation completed', {
        sourceLanguage,
        targetLanguage,
        textLength: text.length,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: translation
        })
      };

    } catch (error) {
      i18nLogger.error('Auto translation failed', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Auto translation failed'
        })
      };
    }
  }
}

// 创建处理器实例
const i18nHandler = new I18nHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  getLanguages: withMiddleware(i18nHandler.getLanguages.bind(i18nHandler)),
  getTranslations: withMiddleware(i18nHandler.getTranslations.bind(i18nHandler)),
  getBatchTranslations: withMiddleware(i18nHandler.getBatchTranslations.bind(i18nHandler)),
  createTranslationKey: withMiddleware(i18nHandler.createTranslationKey.bind(i18nHandler)),
  updateTranslation: withMiddleware(i18nHandler.updateTranslation.bind(i18nHandler)),
  createTranslationTask: withMiddleware(i18nHandler.createTranslationTask.bind(i18nHandler)),
  getTranslationTasks: withMiddleware(i18nHandler.getTranslationTasks.bind(i18nHandler)),
  assignTranslationTask: withMiddleware(i18nHandler.assignTranslationTask.bind(i18nHandler)),
  updateTaskProgress: withMiddleware(i18nHandler.updateTaskProgress.bind(i18nHandler)),
  getUserLanguagePreference: withMiddleware(i18nHandler.getUserLanguagePreference.bind(i18nHandler)),
  updateUserLanguagePreference: withMiddleware(i18nHandler.updateUserLanguagePreference.bind(i18nHandler)),
  getTranslationStats: withMiddleware(i18nHandler.getTranslationStats.bind(i18nHandler)),
  autoTranslate: withMiddleware(i18nHandler.autoTranslate.bind(i18nHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'GET /v1/i18n/languages': i18nHandler.getLanguages.bind(i18nHandler),
      'GET /v1/i18n/translations': i18nHandler.getTranslations.bind(i18nHandler),
      'POST /v1/i18n/translations/batch': i18nHandler.getBatchTranslations.bind(i18nHandler),
      'POST /v1/i18n/keys': i18nHandler.createTranslationKey.bind(i18nHandler),
      'PUT /v1/i18n/translations/{keyId}': i18nHandler.updateTranslation.bind(i18nHandler),
      'POST /v1/i18n/tasks': i18nHandler.createTranslationTask.bind(i18nHandler),
      'GET /v1/i18n/tasks': i18nHandler.getTranslationTasks.bind(i18nHandler),
      'PUT /v1/i18n/tasks/{taskId}/assign': i18nHandler.assignTranslationTask.bind(i18nHandler),
      'PUT /v1/i18n/tasks/{taskId}/progress': i18nHandler.updateTaskProgress.bind(i18nHandler),
      'GET /v1/i18n/users/preferences': i18nHandler.getUserLanguagePreference.bind(i18nHandler),
      'PUT /v1/i18n/users/preferences': i18nHandler.updateUserLanguagePreference.bind(i18nHandler),
      'GET /v1/i18n/stats': i18nHandler.getTranslationStats.bind(i18nHandler),
      'POST /v1/i18n/translate': i18nHandler.autoTranslate.bind(i18nHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
