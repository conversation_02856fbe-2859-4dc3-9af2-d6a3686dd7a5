/**
 * 学习模式处理器
 * 处理课程管理、进度追踪、训练营等学习相关功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { LearningService } = require('./learningService');
const { logger } = require('../utils/logger');

class LearningHandler {
  constructor() {
    this.learningService = new LearningService();
  }

  /**
   * 获取课程列表
   */
  async getCourses(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getCourses'
    });

    try {
      const {
        page = 1,
        limit = 20,
        category,
        dialectRegion,
        difficulty,
        featured
      } = event.queryStringParameters || {};

      const courses = await this.learningService.getCourses({
        page: parseInt(page),
        limit: parseInt(limit),
        category,
        dialectRegion,
        difficulty,
        featured: featured === 'true'
      });

      learningLogger.debug('Courses retrieved', {
        courseCount: courses.courses.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: courses
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get courses', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get courses'
        })
      };
    }
  }

  /**
   * 获取课程详情
   */
  async getCourseDetail(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getCourseDetail'
    });

    try {
      const { courseId } = event.pathParameters;
      const userId = event.user?.id;

      if (!courseId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Course ID is required'
          })
        };
      }

      const courseDetail = await this.learningService.getCourseDetail(courseId, userId);

      if (!courseDetail) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Course not found'
          })
        };
      }

      learningLogger.debug('Course detail retrieved', {
        courseId,
        userId,
        lessonCount: courseDetail.lessons?.length || 0
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: courseDetail
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get course detail', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get course detail'
        })
      };
    }
  }

  /**
   * 开始学习课程
   */
  async startCourse(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'startCourse'
    });

    try {
      const { courseId } = event.pathParameters;
      const userId = event.user?.id;

      if (!courseId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Course ID and user authentication are required'
          })
        };
      }

      const progress = await this.learningService.startCourse(userId, courseId);

      learningLogger.info('Course started', {
        courseId,
        userId,
        progressId: progress.id
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: progress
        })
      };

    } catch (error) {
      learningLogger.error('Failed to start course', {
        error: error.message
      });

      const statusCode = error.message.includes('already enrolled') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 更新课程进度
   */
  async updateCourseProgress(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'updateCourseProgress'
    });

    try {
      const { courseId } = event.pathParameters;
      const { lessonId, progress, studyTime, score } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!courseId || !lessonId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Course ID, lesson ID, and user authentication are required'
          })
        };
      }

      const updatedProgress = await this.learningService.updateLessonProgress({
        userId,
        courseId,
        lessonId,
        progress: progress || 100,
        studyTime: studyTime || 0,
        score
      });

      learningLogger.info('Course progress updated', {
        courseId,
        lessonId,
        userId,
        progress,
        studyTime
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: updatedProgress
        })
      };

    } catch (error) {
      learningLogger.error('Failed to update course progress', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update course progress'
        })
      };
    }
  }

  /**
   * 获取用户学习进度
   */
  async getUserProgress(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getUserProgress'
    });

    try {
      const userId = event.user?.id;
      const {
        page = 1,
        limit = 20,
        status = 'all'
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const progress = await this.learningService.getUserProgress({
        userId,
        page: parseInt(page),
        limit: parseInt(limit),
        status
      });

      learningLogger.debug('User progress retrieved', {
        userId,
        courseCount: progress.courses.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: progress
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get user progress', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get user progress'
        })
      };
    }
  }

  /**
   * 获取训练营列表
   */
  async getTrainingCamps(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getTrainingCamps'
    });

    try {
      const {
        page = 1,
        limit = 20,
        type,
        dialect,
        difficulty,
        status = 'active'
      } = event.queryStringParameters || {};

      const camps = await this.learningService.getTrainingCamps({
        page: parseInt(page),
        limit: parseInt(limit),
        type,
        dialect,
        difficulty,
        status
      });

      learningLogger.debug('Training camps retrieved', {
        campCount: camps.camps.length,
        page,
        limit,
        status
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: camps
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get training camps', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get training camps'
        })
      };
    }
  }

  /**
   * 加入训练营
   */
  async joinTrainingCamp(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'joinTrainingCamp'
    });

    try {
      const { campId } = event.pathParameters;
      const userId = event.user?.id;

      if (!campId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Camp ID and user authentication are required'
          })
        };
      }

      const participation = await this.learningService.joinTrainingCamp(userId, campId);

      learningLogger.info('Training camp joined', {
        campId,
        userId,
        participationId: participation.id
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: participation
        })
      };

    } catch (error) {
      learningLogger.error('Failed to join training camp', {
        error: error.message
      });

      const statusCode = error.message.includes('already joined') ? 409 :
                        error.message.includes('full') ? 409 :
                        error.message.includes('not active') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 获取训练营进度
   */
  async getTrainingCampProgress(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getTrainingCampProgress'
    });

    try {
      const { campId } = event.pathParameters;
      const userId = event.user?.id;

      if (!campId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Camp ID and user authentication are required'
          })
        };
      }

      const progress = await this.learningService.getTrainingCampProgress(userId, campId);

      if (!progress) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Training camp participation not found'
          })
        };
      }

      learningLogger.debug('Training camp progress retrieved', {
        campId,
        userId,
        currentDay: progress.currentDay,
        completedDays: progress.completedDays
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: progress
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get training camp progress', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get training camp progress'
        })
      };
    }
  }

  /**
   * 更新训练营每日进度
   */
  async updateDailyProgress(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'updateDailyProgress'
    });

    try {
      const { campId } = event.pathParameters;
      const { dayNumber, questionsAnswered, correctAnswers, studyTime } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!campId || !userId || dayNumber === undefined) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Camp ID, day number, and user authentication are required'
          })
        };
      }

      const progress = await this.learningService.updateDailyProgress({
        userId,
        campId,
        dayNumber,
        questionsAnswered: questionsAnswered || 0,
        correctAnswers: correctAnswers || 0,
        studyTime: studyTime || 0
      });

      learningLogger.info('Daily progress updated', {
        campId,
        userId,
        dayNumber,
        questionsAnswered,
        correctAnswers
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: progress
        })
      };

    } catch (error) {
      learningLogger.error('Failed to update daily progress', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update daily progress'
        })
      };
    }
  }

  /**
   * 获取学习路径列表
   */
  async getLearningPaths(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getLearningPaths'
    });

    try {
      const {
        page = 1,
        limit = 20,
        dialect,
        difficulty,
        featured
      } = event.queryStringParameters || {};

      const paths = await this.learningService.getLearningPaths({
        page: parseInt(page),
        limit: parseInt(limit),
        dialect,
        difficulty,
        featured: featured === 'true'
      });

      learningLogger.debug('Learning paths retrieved', {
        pathCount: paths.paths.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: paths
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get learning paths', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get learning paths'
        })
      };
    }
  }

  /**
   * 获取用户成就
   */
  async getUserAchievements(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getUserAchievements'
    });

    try {
      const userId = event.user?.id;
      const {
        page = 1,
        limit = 50,
        category
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const achievements = await this.learningService.getUserAchievements({
        userId,
        page: parseInt(page),
        limit: parseInt(limit),
        category
      });

      learningLogger.debug('User achievements retrieved', {
        userId,
        achievementCount: achievements.achievements.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: achievements
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get user achievements', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get user achievements'
        })
      };
    }
  }

  /**
   * 获取学习统计
   */
  async getLearningStats(event, context) {
    const learningLogger = logger.child({
      requestId: context.requestId,
      action: 'getLearningStats'
    });

    try {
      const userId = event.user?.id;
      const {
        timeRange = '30d'
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const stats = await this.learningService.getLearningStats(userId, timeRange);

      learningLogger.debug('Learning stats retrieved', {
        userId,
        timeRange,
        totalStudyTime: stats.totalStudyTime
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      learningLogger.error('Failed to get learning stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get learning stats'
        })
      };
    }
  }
}

// 创建处理器实例
const learningHandler = new LearningHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  getCourses: withMiddleware(learningHandler.getCourses.bind(learningHandler)),
  getCourseDetail: withMiddleware(learningHandler.getCourseDetail.bind(learningHandler)),
  startCourse: withMiddleware(learningHandler.startCourse.bind(learningHandler)),
  updateCourseProgress: withMiddleware(learningHandler.updateCourseProgress.bind(learningHandler)),
  getUserProgress: withMiddleware(learningHandler.getUserProgress.bind(learningHandler)),
  getTrainingCamps: withMiddleware(learningHandler.getTrainingCamps.bind(learningHandler)),
  joinTrainingCamp: withMiddleware(learningHandler.joinTrainingCamp.bind(learningHandler)),
  getTrainingCampProgress: withMiddleware(learningHandler.getTrainingCampProgress.bind(learningHandler)),
  updateDailyProgress: withMiddleware(learningHandler.updateDailyProgress.bind(learningHandler)),
  getLearningPaths: withMiddleware(learningHandler.getLearningPaths.bind(learningHandler)),
  getUserAchievements: withMiddleware(learningHandler.getUserAchievements.bind(learningHandler)),
  getLearningStats: withMiddleware(learningHandler.getLearningStats.bind(learningHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'GET /v1/learning/courses': learningHandler.getCourses.bind(learningHandler),
      'GET /v1/learning/courses/{courseId}': learningHandler.getCourseDetail.bind(learningHandler),
      'POST /v1/learning/courses/{courseId}/start': learningHandler.startCourse.bind(learningHandler),
      'PUT /v1/learning/courses/{courseId}/progress': learningHandler.updateCourseProgress.bind(learningHandler),
      'GET /v1/learning/users/progress': learningHandler.getUserProgress.bind(learningHandler),
      'GET /v1/learning/training-camps': learningHandler.getTrainingCamps.bind(learningHandler),
      'POST /v1/learning/training-camps/{campId}/join': learningHandler.joinTrainingCamp.bind(learningHandler),
      'GET /v1/learning/training-camps/{campId}/progress': learningHandler.getTrainingCampProgress.bind(learningHandler),
      'PUT /v1/learning/training-camps/{campId}/daily-progress': learningHandler.updateDailyProgress.bind(learningHandler),
      'GET /v1/learning/paths': learningHandler.getLearningPaths.bind(learningHandler),
      'GET /v1/learning/users/achievements': learningHandler.getUserAchievements.bind(learningHandler),
      'GET /v1/learning/users/stats': learningHandler.getLearningStats.bind(learningHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
