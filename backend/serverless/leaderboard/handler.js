/**
 * 排行榜服务处理器
 * 处理全球排行榜、地区排行榜、好友排行榜等API请求
 */

const User = require('../models/User');
const GameResult = require('../models/GameResult');
const { authMiddleware, optionalAuth } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const { errorHandler } = require('../middleware/error');
const { rateLimitMiddleware } = require('../middleware/rateLimit');
const { APIError } = require('../utils/errors');
const db = require('../utils/database');

/**
 * 获取全球排行榜
 * GET /v1/leaderboard/global
 */
const getGlobalLeaderboard = errorHandler(async (event, context) => {
  // 可选认证
  await optionalAuth()(event, context);

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('api')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const queryParams = event.queryStringParameters || {};
  const {
    type = 'total_score',
    period = 'all_time',
    page = 1,
    size = 50
  } = queryParams;

  // 验证参数
  const validTypes = ['total_score', 'total_games', 'win_rate', 'max_streak'];
  const validPeriods = ['all_time', 'weekly', 'monthly', 'daily'];
  
  if (!validTypes.includes(type)) {
    throw new APIError('INVALID_TYPE', '无效的排行榜类型', null, 400);
  }
  
  if (!validPeriods.includes(period)) {
    throw new APIError('INVALID_PERIOD', '无效的时间周期', null, 400);
  }

  const pageNum = Math.max(parseInt(page), 1);
  const pageSize = Math.min(Math.max(parseInt(size), 1), 100);

  try {
    let orderBy;
    let whereClause = "WHERE u.status = 1"; // 只显示活跃用户
    
    // 设置排序字段
    switch (type) {
      case 'total_score':
        orderBy = 'u.total_score DESC';
        break;
      case 'total_games':
        orderBy = 'u.total_games DESC';
        break;
      case 'win_rate':
        orderBy = '(CASE WHEN u.total_games > 0 THEN u.win_games / u.total_games ELSE 0 END) DESC';
        break;
      case 'max_streak':
        orderBy = 'u.max_streak DESC';
        break;
    }

    // 根据时间周期添加条件
    if (period !== 'all_time') {
      let dateCondition;
      switch (period) {
        case 'daily':
          dateCondition = "AND DATE(u.updated_at) = CURDATE()";
          break;
        case 'weekly':
          dateCondition = "AND YEARWEEK(u.updated_at) = YEARWEEK(NOW())";
          break;
        case 'monthly':
          dateCondition = "AND YEAR(u.updated_at) = YEAR(NOW()) AND MONTH(u.updated_at) = MONTH(NOW())";
          break;
      }
      whereClause += ` ${dateCondition}`;
    }

    // 构建查询
    const sql = `
      SELECT 
        u.id,
        u.nickname,
        u.avatar_url,
        u.total_score,
        u.total_games,
        u.win_games,
        u.max_streak,
        u.current_level,
        (CASE WHEN u.total_games > 0 THEN ROUND(u.win_games / u.total_games, 4) ELSE 0 END) as win_rate,
        ROW_NUMBER() OVER (ORDER BY ${orderBy}) as rank_position
      FROM users u
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ? OFFSET ?
    `;

    const offset = (pageNum - 1) * pageSize;
    const leaderboardData = await db.query(sql, [pageSize, offset]);

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM users u ${whereClause}`;
    const countResult = await db.query(countSql);
    const total = countResult[0].total;

    // 如果用户已登录，获取用户自己的排名
    let currentUserRank = null;
    if (event.user) {
      const userRankSql = `
        SELECT rank_position FROM (
          SELECT 
            u.id,
            ROW_NUMBER() OVER (ORDER BY ${orderBy}) as rank_position
          FROM users u
          ${whereClause}
        ) ranked
        WHERE id = ?
      `;
      const userRankResult = await db.query(userRankSql, [event.user.id]);
      if (userRankResult.length > 0) {
        currentUserRank = userRankResult[0].rank_position;
      }
    }

    return {
      leaderboard: leaderboardData.map(user => ({
        rank: user.rank_position,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          level: user.current_level
        },
        score: user.total_score,
        totalGames: user.total_games,
        winGames: user.win_games,
        winRate: parseFloat(user.win_rate),
        maxStreak: user.max_streak
      })),
      pagination: {
        page: pageNum,
        size: pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasNext: pageNum * pageSize < total,
        hasPrev: pageNum > 1
      },
      type,
      period,
      currentUserRank
    };

  } catch (error) {
    console.error('Get global leaderboard error:', error);
    throw new APIError('GET_LEADERBOARD_FAILED', '获取排行榜失败', null, 500);
  }
});

/**
 * 获取地区排行榜
 * GET /v1/leaderboard/region
 */
const getRegionLeaderboard = errorHandler(async (event, context) => {
  // 可选认证
  await optionalAuth()(event, context);

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('api')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const queryParams = event.queryStringParameters || {};
  const {
    region,
    category = 'all',
    page = 1,
    size = 50
  } = queryParams;

  if (!region) {
    throw new APIError('MISSING_REGION', '地区参数不能为空', null, 400);
  }

  const pageNum = Math.max(parseInt(page), 1);
  const pageSize = Math.min(Math.max(parseInt(size), 1), 100);

  try {
    let whereClause = "WHERE u.status = 1";
    let params = [];

    // 根据地区筛选
    if (region !== 'all') {
      whereClause += " AND u.region = ?";
      params.push(region);
    }

    // 根据方言类别筛选游戏记录
    let joinClause = "";
    if (category !== 'all') {
      joinClause = `
        INNER JOIN (
          SELECT gr.user_id, SUM(gr.score_earned) as category_score
          FROM game_records gr
          INNER JOIN dialect_questions dq ON gr.question_id = dq.id
          WHERE dq.category = ?
          GROUP BY gr.user_id
        ) cs ON u.id = cs.user_id
      `;
      params.push(category);
    }

    const sql = `
      SELECT 
        u.id,
        u.nickname,
        u.avatar_url,
        u.region,
        u.total_score,
        u.total_games,
        u.win_games,
        u.max_streak,
        u.current_level,
        ${category !== 'all' ? 'cs.category_score' : 'u.total_score'} as display_score,
        ROW_NUMBER() OVER (ORDER BY ${category !== 'all' ? 'cs.category_score' : 'u.total_score'} DESC) as rank_position
      FROM users u
      ${joinClause}
      ${whereClause}
      ORDER BY display_score DESC
      LIMIT ? OFFSET ?
    `;

    const offset = (pageNum - 1) * pageSize;
    params.push(pageSize, offset);
    const leaderboardData = await db.query(sql, params);

    // 获取总数
    const countParams = params.slice(0, -2); // 移除 LIMIT 和 OFFSET 参数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM users u
      ${joinClause}
      ${whereClause}
    `;
    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;

    return {
      leaderboard: leaderboardData.map(user => ({
        rank: user.rank_position,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          region: user.region,
          level: user.current_level
        },
        score: user.display_score,
        totalScore: user.total_score,
        totalGames: user.total_games,
        winGames: user.win_games,
        maxStreak: user.max_streak
      })),
      pagination: {
        page: pageNum,
        size: pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasNext: pageNum * pageSize < total,
        hasPrev: pageNum > 1
      },
      region,
      category
    };

  } catch (error) {
    console.error('Get region leaderboard error:', error);
    throw new APIError('GET_REGION_LEADERBOARD_FAILED', '获取地区排行榜失败', null, 500);
  }
});

/**
 * 获取好友排行榜
 * GET /v1/leaderboard/friends
 */
const getFriendsLeaderboard = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('user')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  const userId = event.user.id;

  try {
    // 获取用户好友排行榜
    const sql = `
      SELECT 
        u.id,
        u.nickname,
        u.avatar_url,
        u.total_score,
        u.total_games,
        u.win_games,
        u.max_streak,
        u.current_level,
        (CASE WHEN u.total_games > 0 THEN ROUND(u.win_games / u.total_games, 4) ELSE 0 END) as win_rate,
        ROW_NUMBER() OVER (ORDER BY u.total_score DESC) as rank_position
      FROM users u
      WHERE u.id IN (
        SELECT friend_id FROM user_relationships 
        WHERE user_id = ? AND status = 1
        UNION
        SELECT ? as friend_id
      )
      AND u.status = 1
      ORDER BY u.total_score DESC
      LIMIT 100
    `;

    const friendsData = await db.query(sql, [userId, userId]);

    return {
      leaderboard: friendsData.map(user => ({
        rank: user.rank_position,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          level: user.current_level
        },
        score: user.total_score,
        totalGames: user.total_games,
        winGames: user.win_games,
        winRate: parseFloat(user.win_rate),
        maxStreak: user.max_streak,
        isCurrentUser: user.id === userId
      })),
      total: friendsData.length
    };

  } catch (error) {
    console.error('Get friends leaderboard error:', error);
    throw new APIError('GET_FRIENDS_LEADERBOARD_FAILED', '获取好友排行榜失败', null, 500);
  }
});

/**
 * 获取用户排名信息
 * GET /v1/leaderboard/user-rank/{userId}
 */
const getUserRank = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const targetUserId = event.pathParameters?.userId;
  
  if (!targetUserId) {
    throw new APIError('MISSING_USER_ID', '用户ID不能为空', null, 400);
  }

  try {
    // 获取用户基本信息
    const user = await User.findById(targetUserId);
    if (!user) {
      throw new APIError('USER_NOT_FOUND', '用户不存在', null, 404);
    }

    // 获取全球排名
    const globalRankSql = `
      SELECT COUNT(*) + 1 as rank
      FROM users
      WHERE total_score > ? AND status = 1
    `;
    const globalRankResult = await db.query(globalRankSql, [user.total_score]);
    const globalRank = globalRankResult[0].rank;

    // 获取地区排名（如果有地区信息）
    let regionRank = null;
    if (user.region) {
      const regionRankSql = `
        SELECT COUNT(*) + 1 as rank
        FROM users
        WHERE total_score > ? AND region = ? AND status = 1
      `;
      const regionRankResult = await db.query(regionRankSql, [user.total_score, user.region]);
      regionRank = regionRankResult[0].rank;
    }

    // 获取好友排名
    let friendsRank = null;
    const friendsRankSql = `
      SELECT COUNT(*) + 1 as rank
      FROM users u
      WHERE u.total_score > ?
      AND u.status = 1
      AND u.id IN (
        SELECT friend_id FROM user_relationships 
        WHERE user_id = ? AND status = 1
        UNION
        SELECT ? as friend_id
      )
    `;
    const friendsRankResult = await db.query(friendsRankSql, [user.total_score, targetUserId, targetUserId]);
    friendsRank = friendsRankResult[0].rank;

    return {
      user: {
        id: user.id,
        nickname: user.nickname,
        avatarUrl: user.avatar_url,
        region: user.region,
        level: user.current_level
      },
      score: user.total_score,
      totalGames: user.total_games,
      winGames: user.win_games,
      winRate: user.total_games > 0 ? user.win_games / user.total_games : 0,
      maxStreak: user.max_streak,
      ranking: {
        global: globalRank,
        region: regionRank,
        friends: friendsRank
      }
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Get user rank error:', error);
    throw new APIError('GET_USER_RANK_FAILED', '获取用户排名失败', null, 500);
  }
});

/**
 * 路由处理器
 */
const routes = {
  'GET /v1/leaderboard/global': getGlobalLeaderboard,
  'GET /v1/leaderboard/region': getRegionLeaderboard,
  'GET /v1/leaderboard/friends': getFriendsLeaderboard,
  'GET /v1/leaderboard/user-rank/{userId}': getUserRank
};

/**
 * 主处理器
 */
const main = async (event, context) => {
  const method = event.httpMethod || event.requestContext?.httpMethod;
  const path = event.path || event.requestContext?.path;
  
  // 处理路径参数
  let routeKey = `${method} ${path}`;
  
  // 匹配动态路由
  for (const route of Object.keys(routes)) {
    const routePattern = route.replace(/\{[^}]+\}/g, '[^/]+');
    const regex = new RegExp(`^${routePattern}$`);
    
    if (regex.test(routeKey)) {
      routeKey = route;
      break;
    }
  }
  
  console.log(`Leaderboard Service - ${routeKey}`);
  
  // 添加CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Device-Id',
    'Access-Control-Max-Age': '86400'
  };
  
  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  
  // 查找对应的路由处理器
  const handler = routes[routeKey];
  
  if (!handler) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: '接口不存在',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
  
  try {
    const result = await handler(event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 0,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
    
  } catch (error) {
    console.error('Leaderboard handler error:', error);
    
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error instanceof APIError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      message = error.message;
    }
    
    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
};

module.exports = {
  main,
  getGlobalLeaderboard,
  getRegionLeaderboard,
  getFriendsLeaderboard,
  getUserRank
};