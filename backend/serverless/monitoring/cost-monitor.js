/**
 * 实时成本监控系统
 * 
 * 实现实时成本监控系统，包括资源使用统计、成本计算、预算预警等功能
 * 目标：围观功能成本控制在$80/月以内
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const { CloudWatchClient, GetMetricStatisticsCommand } = require('@aws-sdk/client-cloudwatch');
const { CostExplorerClient, GetCostAndUsageCommand } = require('@aws-sdk/client-cost-explorer');

class CostMonitor extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 预算配置
            budget: {
                monthly: options.monthlyBudget || 80,        // 月度预算$80
                daily: options.dailyBudget || 2.67,         // 日预算$2.67
                hourly: options.hourlyBudget || 0.11,       // 小时预算$0.11
                warningThreshold: 0.8,                      // 80%预警
                criticalThreshold: 0.95                     // 95%严重预警
            },
            
            // 监控间隔
            intervals: {
                realtime: 60000,        // 1分钟实时监控
                hourly: 3600000,        // 1小时统计
                daily: 86400000,        // 1天统计
                monthly: 2592000000     // 30天统计
            },
            
            // 成本计算配置
            pricing: {
                // Serverless函数成本（每百万次请求）
                scf: {
                    requests: 0.0000002,    // $0.0000002 per request
                    duration: 0.0000166667, // $0.0000166667 per GB-second
                    memory: 128             // 默认内存128MB
                },
                
                // 数据库成本
                database: {
                    connections: 0.01,      // $0.01 per connection hour
                    storage: 0.10,          // $0.10 per GB per month
                    io: 0.10                // $0.10 per million I/O requests
                },
                
                // Redis缓存成本
                redis: {
                    memory: 0.05,           // $0.05 per GB per hour
                    operations: 0.000001    // $0.000001 per operation
                },
                
                // CDN成本
                cdn: {
                    requests: 0.0075,       // $0.0075 per 10,000 requests
                    dataTransfer: 0.08      // $0.08 per GB
                },
                
                // WebSocket连接成本
                websocket: {
                    connections: 0.25,      // $0.25 per million connection minutes
                    messages: 1.00          // $1.00 per million messages
                }
            }
        };
        
        // AWS客户端
        this.cloudWatchClient = new CloudWatchClient({ region: 'ap-southeast-1' });
        this.costExplorerClient = new CostExplorerClient({ region: 'us-east-1' });
        
        // 成本统计
        this.costs = {
            current: {
                hourly: 0,
                daily: 0,
                monthly: 0
            },
            breakdown: {
                serverless: 0,
                database: 0,
                redis: 0,
                cdn: 0,
                websocket: 0
            },
            usage: {
                requests: 0,
                connections: 0,
                messages: 0,
                storage: 0,
                bandwidth: 0
            }
        };
        
        // 预警状态
        this.alerts = {
            active: new Set(),
            history: []
        };
        
        // 监控定时器
        this.timers = {
            realtime: null,
            hourly: null,
            daily: null,
            monthly: null
        };
        
        // 资源使用追踪
        this.resourceTracker = {
            startTime: Date.now(),
            metrics: new Map(),
            samples: []
        };
        
        this.initialize();
    }
    
    /**
     * 初始化成本监控系统
     */
    initialize() {
        console.log('初始化成本监控系统...');
        
        // 启动各种监控定时器
        this.startRealtimeMonitoring();
        this.startHourlyMonitoring();
        this.startDailyMonitoring();
        this.startMonthlyMonitoring();
        
        // 初始化资源追踪
        this.initializeResourceTracking();
        
        console.log(`成本监控系统初始化完成，月度预算: $${this.config.budget.monthly}`);
    }
    
    /**
     * 启动实时监控
     */
    startRealtimeMonitoring() {
        this.timers.realtime = setInterval(async () => {
            await this.collectRealtimeMetrics();
            await this.calculateCurrentCosts();
            await this.checkBudgetAlerts();
        }, this.config.intervals.realtime);
    }
    
    /**
     * 启动小时监控
     */
    startHourlyMonitoring() {
        this.timers.hourly = setInterval(async () => {
            await this.generateHourlyReport();
            await this.optimizeResourceUsage();
        }, this.config.intervals.hourly);
    }
    
    /**
     * 启动日监控
     */
    startDailyMonitoring() {
        this.timers.daily = setInterval(async () => {
            await this.generateDailyReport();
            await this.forecastMonthlyCost();
        }, this.config.intervals.daily);
    }
    
    /**
     * 启动月监控
     */
    startMonthlyMonitoring() {
        this.timers.monthly = setInterval(async () => {
            await this.generateMonthlyReport();
            await this.adjustBudgetStrategy();
        }, this.config.intervals.monthly);
    }
    
    /**
     * 收集实时指标
     */
    async collectRealtimeMetrics() {
        try {
            // 收集Serverless函数指标
            const scfMetrics = await this.getServerlessMetrics();
            
            // 收集数据库指标
            const dbMetrics = await this.getDatabaseMetrics();
            
            // 收集Redis指标
            const redisMetrics = await this.getRedisMetrics();
            
            // 收集CDN指标
            const cdnMetrics = await this.getCDNMetrics();
            
            // 收集WebSocket指标
            const wsMetrics = await this.getWebSocketMetrics();
            
            // 更新使用统计
            this.updateUsageStats({
                ...scfMetrics,
                ...dbMetrics,
                ...redisMetrics,
                ...cdnMetrics,
                ...wsMetrics
            });
            
        } catch (error) {
            console.error('收集实时指标失败:', error);
        }
    }
    
    /**
     * 获取Serverless函数指标
     */
    async getServerlessMetrics() {
        // 模拟获取Serverless函数指标
        // 实际项目中应该调用腾讯云API
        return {
            requests: Math.floor(Math.random() * 1000) + 500,
            duration: Math.floor(Math.random() * 5000) + 1000,
            memory: 128,
            errors: Math.floor(Math.random() * 10)
        };
    }
    
    /**
     * 获取数据库指标
     */
    async getDatabaseMetrics() {
        // 模拟获取数据库指标
        return {
            connections: Math.floor(Math.random() * 50) + 20,
            queries: Math.floor(Math.random() * 2000) + 1000,
            storage: 2.5, // GB
            ioOperations: Math.floor(Math.random() * 10000) + 5000
        };
    }
    
    /**
     * 获取Redis指标
     */
    async getRedisMetrics() {
        // 模拟获取Redis指标
        return {
            memoryUsage: 0.5, // GB
            operations: Math.floor(Math.random() * 5000) + 2000,
            hitRate: 0.85,
            connections: Math.floor(Math.random() * 20) + 10
        };
    }
    
    /**
     * 获取CDN指标
     */
    async getCDNMetrics() {
        // 模拟获取CDN指标
        return {
            requests: Math.floor(Math.random() * 10000) + 5000,
            dataTransfer: Math.random() * 10 + 5, // GB
            hitRate: 0.90
        };
    }
    
    /**
     * 获取WebSocket指标
     */
    async getWebSocketMetrics() {
        // 模拟获取WebSocket指标
        return {
            activeConnections: Math.floor(Math.random() * 1000) + 500,
            messages: Math.floor(Math.random() * 50000) + 20000,
            connectionMinutes: Math.floor(Math.random() * 100000) + 50000
        };
    }
    
    /**
     * 更新使用统计
     */
    updateUsageStats(metrics) {
        this.costs.usage = {
            requests: (this.costs.usage.requests || 0) + (metrics.requests || 0),
            connections: metrics.activeConnections || 0,
            messages: (this.costs.usage.messages || 0) + (metrics.messages || 0),
            storage: metrics.storage || 0,
            bandwidth: (this.costs.usage.bandwidth || 0) + (metrics.dataTransfer || 0)
        };
        
        // 记录样本
        this.resourceTracker.samples.push({
            timestamp: Date.now(),
            metrics: { ...metrics }
        });
        
        // 保持样本数量限制
        if (this.resourceTracker.samples.length > 1440) { // 24小时的分钟数
            this.resourceTracker.samples.shift();
        }
    }
    
    /**
     * 计算当前成本
     */
    async calculateCurrentCosts() {
        const now = Date.now();
        const hoursSinceStart = (now - this.resourceTracker.startTime) / (1000 * 60 * 60);
        const daysSinceStart = hoursSinceStart / 24;
        
        // 计算各组件成本
        const serverlessCost = this.calculateServerlessCost();
        const databaseCost = this.calculateDatabaseCost();
        const redisCost = this.calculateRedisCost();
        const cdnCost = this.calculateCDNCost();
        const websocketCost = this.calculateWebSocketCost();
        
        // 更新成本分解
        this.costs.breakdown = {
            serverless: serverlessCost,
            database: databaseCost,
            redis: redisCost,
            cdn: cdnCost,
            websocket: websocketCost
        };
        
        // 计算总成本
        const totalCost = Object.values(this.costs.breakdown).reduce((sum, cost) => sum + cost, 0);
        
        // 更新当前成本
        this.costs.current = {
            hourly: totalCost / Math.max(hoursSinceStart, 1),
            daily: totalCost / Math.max(daysSinceStart, 1),
            monthly: totalCost / Math.max(daysSinceStart / 30, 1)
        };
        
        // 触发成本更新事件
        this.emit('costUpdate', {
            current: this.costs.current,
            breakdown: this.costs.breakdown,
            usage: this.costs.usage
        });
    }
    
    /**
     * 计算Serverless成本
     */
    calculateServerlessCost() {
        const usage = this.costs.usage;
        const pricing = this.config.pricing.scf;
        
        const requestCost = (usage.requests || 0) * pricing.requests;
        const durationCost = (usage.requests || 0) * (pricing.memory / 1024) * 
                           (2000 / 1000) * pricing.duration; // 假设平均2秒执行时间
        
        return requestCost + durationCost;
    }
    
    /**
     * 计算数据库成本
     */
    calculateDatabaseCost() {
        const usage = this.costs.usage;
        const pricing = this.config.pricing.database;
        
        const connectionCost = (usage.connections || 0) * pricing.connections * 
                              (Date.now() - this.resourceTracker.startTime) / (1000 * 60 * 60);
        const storageCost = (usage.storage || 0) * pricing.storage / 30; // 日成本
        const ioCost = (usage.requests || 0) * pricing.io / 1000000;
        
        return connectionCost + storageCost + ioCost;
    }
    
    /**
     * 计算Redis成本
     */
    calculateRedisCost() {
        const usage = this.costs.usage;
        const pricing = this.config.pricing.redis;
        
        const memoryCost = 0.5 * pricing.memory * 
                          (Date.now() - this.resourceTracker.startTime) / (1000 * 60 * 60);
        const operationCost = (usage.messages || 0) * pricing.operations;
        
        return memoryCost + operationCost;
    }
    
    /**
     * 计算CDN成本
     */
    calculateCDNCost() {
        const usage = this.costs.usage;
        const pricing = this.config.pricing.cdn;
        
        const requestCost = (usage.requests || 0) * pricing.requests / 10000;
        const transferCost = (usage.bandwidth || 0) * pricing.dataTransfer;
        
        return requestCost + transferCost;
    }
    
    /**
     * 计算WebSocket成本
     */
    calculateWebSocketCost() {
        const usage = this.costs.usage;
        const pricing = this.config.pricing.websocket;
        
        const connectionCost = (usage.connections || 0) * 
                              (Date.now() - this.resourceTracker.startTime) / (1000 * 60) * 
                              pricing.connections / 1000000;
        const messageCost = (usage.messages || 0) * pricing.messages / 1000000;
        
        return connectionCost + messageCost;
    }
    
    /**
     * 检查预算预警
     */
    async checkBudgetAlerts() {
        const current = this.costs.current;
        const budget = this.config.budget;
        
        // 检查小时预算
        await this.checkBudgetThreshold('hourly', current.hourly, budget.hourly);
        
        // 检查日预算
        await this.checkBudgetThreshold('daily', current.daily, budget.daily);
        
        // 检查月预算
        await this.checkBudgetThreshold('monthly', current.monthly, budget.monthly);
    }
    
    /**
     * 检查预算阈值
     */
    async checkBudgetThreshold(period, currentCost, budgetLimit) {
        const usage = currentCost / budgetLimit;
        const warningThreshold = this.config.budget.warningThreshold;
        const criticalThreshold = this.config.budget.criticalThreshold;
        
        if (usage >= criticalThreshold) {
            await this.triggerAlert('critical', period, usage, currentCost, budgetLimit);
        } else if (usage >= warningThreshold) {
            await this.triggerAlert('warning', period, usage, currentCost, budgetLimit);
        }
    }
    
    /**
     * 触发预警
     */
    async triggerAlert(level, period, usage, currentCost, budgetLimit) {
        const alertKey = `${level}_${period}`;
        
        // 避免重复预警
        if (this.alerts.active.has(alertKey)) {
            return;
        }
        
        const alert = {
            id: `${alertKey}_${Date.now()}`,
            level,
            period,
            usage: Math.round(usage * 100),
            currentCost: currentCost.toFixed(4),
            budgetLimit: budgetLimit.toFixed(2),
            timestamp: Date.now(),
            message: `${period}成本预警: 已使用${Math.round(usage * 100)}%预算 ($${currentCost.toFixed(4)}/$${budgetLimit.toFixed(2)})`
        };
        
        // 记录预警
        this.alerts.active.add(alertKey);
        this.alerts.history.push(alert);
        
        // 保持历史记录限制
        if (this.alerts.history.length > 100) {
            this.alerts.history.shift();
        }
        
        // 触发预警事件
        this.emit('budgetAlert', alert);
        
        console.warn(`成本预警: ${alert.message}`);
        
        // 如果是严重预警，触发自动优化
        if (level === 'critical') {
            await this.triggerEmergencyOptimization();
        }
        
        // 设置预警清除定时器
        setTimeout(() => {
            this.alerts.active.delete(alertKey);
        }, 3600000); // 1小时后清除预警状态
    }
    
    /**
     * 触发紧急优化
     */
    async triggerEmergencyOptimization() {
        console.log('触发紧急成本优化...');
        
        // 降低WebSocket连接数
        this.emit('optimizationRequired', {
            type: 'websocket',
            action: 'reduce_connections',
            target: 0.8 // 减少到80%
        });
        
        // 增加缓存TTL
        this.emit('optimizationRequired', {
            type: 'cache',
            action: 'increase_ttl',
            multiplier: 2
        });
        
        // 降低数据库查询频率
        this.emit('optimizationRequired', {
            type: 'database',
            action: 'reduce_queries',
            target: 0.7 // 减少到70%
        });
    }
    
    /**
     * 生成小时报告
     */
    async generateHourlyReport() {
        const report = {
            timestamp: Date.now(),
            period: 'hourly',
            costs: { ...this.costs.current },
            breakdown: { ...this.costs.breakdown },
            usage: { ...this.costs.usage },
            budgetUsage: {
                hourly: (this.costs.current.hourly / this.config.budget.hourly * 100).toFixed(1),
                daily: (this.costs.current.daily / this.config.budget.daily * 100).toFixed(1),
                monthly: (this.costs.current.monthly / this.config.budget.monthly * 100).toFixed(1)
            }
        };
        
        this.emit('hourlyReport', report);
        console.log(`小时成本报告: $${this.costs.current.hourly.toFixed(4)}/小时`);
    }
    
    /**
     * 预测月度成本
     */
    async forecastMonthlyCost() {
        const currentDaily = this.costs.current.daily;
        const daysInMonth = 30;
        const forecastedMonthlyCost = currentDaily * daysInMonth;
        
        const forecast = {
            current: forecastedMonthlyCost,
            budget: this.config.budget.monthly,
            usage: (forecastedMonthlyCost / this.config.budget.monthly * 100).toFixed(1),
            trend: this.calculateCostTrend(),
            recommendations: this.generateCostRecommendations(forecastedMonthlyCost)
        };
        
        this.emit('monthlyForecast', forecast);
        
        if (forecastedMonthlyCost > this.config.budget.monthly) {
            console.warn(`月度成本预测超预算: $${forecastedMonthlyCost.toFixed(2)} > $${this.config.budget.monthly}`);
        }
    }
    
    /**
     * 计算成本趋势
     */
    calculateCostTrend() {
        const samples = this.resourceTracker.samples;
        if (samples.length < 2) {
            return 'stable';
        }
        
        const recent = samples.slice(-60); // 最近1小时
        const older = samples.slice(-120, -60); // 前1小时
        
        const recentAvg = recent.reduce((sum, s) => sum + (s.metrics.requests || 0), 0) / recent.length;
        const olderAvg = older.reduce((sum, s) => sum + (s.metrics.requests || 0), 0) / older.length;
        
        const change = (recentAvg - olderAvg) / olderAvg;
        
        if (change > 0.1) return 'increasing';
        if (change < -0.1) return 'decreasing';
        return 'stable';
    }
    
    /**
     * 生成成本建议
     */
    generateCostRecommendations(forecastedCost) {
        const recommendations = [];
        const budget = this.config.budget.monthly;
        
        if (forecastedCost > budget) {
            const overage = forecastedCost - budget;
            const overagePercent = (overage / budget * 100).toFixed(1);
            
            recommendations.push(`预测超预算${overagePercent}%，建议优化资源使用`);
            
            // 具体建议
            if (this.costs.breakdown.websocket > budget * 0.3) {
                recommendations.push('WebSocket成本较高，建议优化连接管理');
            }
            
            if (this.costs.breakdown.database > budget * 0.25) {
                recommendations.push('数据库成本较高，建议优化查询和缓存');
            }
            
            if (this.costs.breakdown.serverless > budget * 0.3) {
                recommendations.push('Serverless成本较高，建议优化函数执行时间');
            }
        } else {
            recommendations.push('成本控制良好，继续保持');
        }
        
        return recommendations;
    }
    
    /**
     * 初始化资源追踪
     */
    initializeResourceTracking() {
        this.resourceTracker.startTime = Date.now();
        this.resourceTracker.metrics.clear();
        this.resourceTracker.samples = [];
    }
    
    /**
     * 获取成本统计
     */
    getCostStats() {
        return {
            current: { ...this.costs.current },
            breakdown: { ...this.costs.breakdown },
            usage: { ...this.costs.usage },
            budget: { ...this.config.budget },
            alerts: {
                active: Array.from(this.alerts.active),
                history: this.alerts.history.slice(-10) // 最近10条
            }
        };
    }
    
    /**
     * 获取成本趋势数据
     */
    getCostTrends() {
        const samples = this.resourceTracker.samples;
        const trends = {
            hourly: [],
            daily: []
        };
        
        // 按小时聚合
        const hourlyGroups = new Map();
        samples.forEach(sample => {
            const hour = Math.floor(sample.timestamp / (1000 * 60 * 60));
            if (!hourlyGroups.has(hour)) {
                hourlyGroups.set(hour, []);
            }
            hourlyGroups.get(hour).push(sample);
        });
        
        // 计算小时趋势
        for (const [hour, hourSamples] of hourlyGroups) {
            const avgRequests = hourSamples.reduce((sum, s) => sum + (s.metrics.requests || 0), 0) / hourSamples.length;
            trends.hourly.push({
                timestamp: hour * 1000 * 60 * 60,
                requests: avgRequests,
                estimatedCost: avgRequests * this.config.pricing.scf.requests
            });
        }
        
        return trends;
    }
    
    /**
     * 关闭成本监控系统
     */
    close() {
        console.log('关闭成本监控系统...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.resourceTracker.samples = [];
        this.alerts.active.clear();
        
        console.log('成本监控系统已关闭');
    }
}

module.exports = CostMonitor;
