/**
 * 实时性能监控系统
 * 
 * 实现围观功能的实时性能监控，包括连接数、消息量、响应时间、资源使用率等指标的实时监控
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const os = require('os');

class PerformanceMonitor extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 监控间隔
            intervals: {
                realtime: 1000,      // 1秒实时监控
                metrics: 5000,       // 5秒指标收集
                analysis: 30000,     // 30秒性能分析
                cleanup: 300000      // 5分钟数据清理
            },
            
            // 性能阈值
            thresholds: {
                // 连接相关
                maxConnections: 5000,
                connectionGrowthRate: 100,    // 每分钟最大增长
                
                // 消息相关
                maxMessagesPerSecond: 1000,
                messageQueueSize: 10000,
                
                // 响应时间
                maxResponseTime: 200,         // 200ms
                avgResponseTimeThreshold: 100, // 100ms平均响应时间
                
                // 资源使用
                maxCpuUsage: 80,             // 80% CPU使用率
                maxMemoryUsage: 85,          // 85% 内存使用率
                maxDiskUsage: 90,            // 90% 磁盘使用率
                
                // 错误率
                maxErrorRate: 5,             // 5% 错误率
                maxTimeoutRate: 2            // 2% 超时率
            },
            
            // 数据保留
            retention: {
                realtime: 300,               // 5分钟实时数据
                hourly: 24,                  // 24小时数据
                daily: 30                    // 30天数据
            }
        };
        
        // 实时监控数据
        this.realtimeMetrics = {
            timestamp: Date.now(),
            connections: {
                total: 0,
                active: 0,
                idle: 0,
                growth: 0
            },
            messages: {
                sent: 0,
                received: 0,
                queued: 0,
                processed: 0,
                failed: 0,
                rate: 0
            },
            performance: {
                responseTime: {
                    min: 0,
                    max: 0,
                    avg: 0,
                    p95: 0,
                    p99: 0
                },
                throughput: 0,
                errorRate: 0,
                timeoutRate: 0
            },
            resources: {
                cpu: {
                    usage: 0,
                    load: [0, 0, 0]
                },
                memory: {
                    used: 0,
                    free: 0,
                    usage: 0,
                    heap: {
                        used: 0,
                        total: 0
                    }
                },
                disk: {
                    usage: 0,
                    read: 0,
                    write: 0
                },
                network: {
                    bytesIn: 0,
                    bytesOut: 0,
                    packetsIn: 0,
                    packetsOut: 0
                }
            }
        };
        
        // 历史数据存储
        this.historicalData = {
            realtime: [],
            hourly: [],
            daily: []
        };
        
        // 响应时间追踪
        this.responseTimeTracker = {
            samples: [],
            maxSamples: 1000
        };
        
        // 连接追踪
        this.connectionTracker = {
            history: [],
            current: new Map()
        };
        
        // 消息追踪
        this.messageTracker = {
            history: [],
            queues: new Map(),
            processing: new Map()
        };
        
        // 性能警报
        this.alerts = {
            active: new Map(),
            history: []
        };
        
        // 定时器
        this.timers = {
            realtime: null,
            metrics: null,
            analysis: null,
            cleanup: null
        };
        
        // 统计计数器
        this.counters = {
            totalRequests: 0,
            totalErrors: 0,
            totalTimeouts: 0,
            totalConnections: 0,
            totalMessages: 0
        };
        
        this.initialize();
    }
    
    /**
     * 初始化性能监控系统
     */
    initialize() {
        console.log('初始化实时性能监控系统...');
        
        // 启动监控定时器
        this.startMonitoring();
        
        console.log('性能监控系统初始化完成');
    }
    
    /**
     * 启动监控
     */
    startMonitoring() {
        // 实时监控
        this.timers.realtime = setInterval(() => {
            this.collectRealtimeMetrics();
        }, this.config.intervals.realtime);
        
        // 指标收集
        this.timers.metrics = setInterval(() => {
            this.collectMetrics();
        }, this.config.intervals.metrics);
        
        // 性能分析
        this.timers.analysis = setInterval(() => {
            this.performAnalysis();
        }, this.config.intervals.analysis);
        
        // 数据清理
        this.timers.cleanup = setInterval(() => {
            this.cleanupData();
        }, this.config.intervals.cleanup);
    }
    
    /**
     * 收集实时指标
     */
    collectRealtimeMetrics() {
        const now = Date.now();
        
        // 更新时间戳
        this.realtimeMetrics.timestamp = now;
        
        // 收集系统资源指标
        this.collectSystemMetrics();
        
        // 收集应用指标
        this.collectApplicationMetrics();
        
        // 触发实时数据事件
        this.emit('realtimeMetrics', { ...this.realtimeMetrics });
        
        // 检查阈值
        this.checkThresholds();
    }
    
    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        // CPU使用率
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        
        cpus.forEach(cpu => {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });
        
        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        const usage = 100 - ~~(100 * idle / total);
        
        this.realtimeMetrics.resources.cpu.usage = usage;
        this.realtimeMetrics.resources.cpu.load = os.loadavg();
        
        // 内存使用率
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        
        this.realtimeMetrics.resources.memory.used = usedMem;
        this.realtimeMetrics.resources.memory.free = freeMem;
        this.realtimeMetrics.resources.memory.usage = (usedMem / totalMem) * 100;
        
        // Node.js 堆内存
        const memUsage = process.memoryUsage();
        this.realtimeMetrics.resources.memory.heap.used = memUsage.heapUsed;
        this.realtimeMetrics.resources.memory.heap.total = memUsage.heapTotal;
    }
    
    /**
     * 收集应用指标
     */
    collectApplicationMetrics() {
        // 连接指标
        this.updateConnectionMetrics();
        
        // 消息指标
        this.updateMessageMetrics();
        
        // 性能指标
        this.updatePerformanceMetrics();
    }
    
    /**
     * 更新连接指标
     */
    updateConnectionMetrics() {
        const connections = this.connectionTracker.current;
        let activeCount = 0;
        let idleCount = 0;
        
        for (const [connectionId, connection] of connections) {
            if (Date.now() - connection.lastActivity < 30000) { // 30秒内活跃
                activeCount++;
            } else {
                idleCount++;
            }
        }
        
        const totalConnections = connections.size;
        const previousTotal = this.realtimeMetrics.connections.total;
        const growth = totalConnections - previousTotal;
        
        this.realtimeMetrics.connections.total = totalConnections;
        this.realtimeMetrics.connections.active = activeCount;
        this.realtimeMetrics.connections.idle = idleCount;
        this.realtimeMetrics.connections.growth = growth;
    }
    
    /**
     * 更新消息指标
     */
    updateMessageMetrics() {
        // 计算消息处理速率
        const now = Date.now();
        const timeWindow = 60000; // 1分钟窗口
        
        const recentMessages = this.messageTracker.history.filter(msg => 
            now - msg.timestamp < timeWindow
        );
        
        const messageRate = recentMessages.length / (timeWindow / 1000);
        
        this.realtimeMetrics.messages.rate = messageRate;
        
        // 队列状态
        let totalQueued = 0;
        for (const [queueName, queue] of this.messageTracker.queues) {
            totalQueued += queue.length;
        }
        
        this.realtimeMetrics.messages.queued = totalQueued;
    }
    
    /**
     * 更新性能指标
     */
    updatePerformanceMetrics() {
        const samples = this.responseTimeTracker.samples;
        
        if (samples.length > 0) {
            const sortedSamples = [...samples].sort((a, b) => a - b);
            const len = sortedSamples.length;
            
            this.realtimeMetrics.performance.responseTime.min = sortedSamples[0];
            this.realtimeMetrics.performance.responseTime.max = sortedSamples[len - 1];
            this.realtimeMetrics.performance.responseTime.avg = 
                sortedSamples.reduce((sum, time) => sum + time, 0) / len;
            this.realtimeMetrics.performance.responseTime.p95 = 
                sortedSamples[Math.floor(len * 0.95)];
            this.realtimeMetrics.performance.responseTime.p99 = 
                sortedSamples[Math.floor(len * 0.99)];
        }
        
        // 错误率和超时率
        const totalRequests = this.counters.totalRequests;
        if (totalRequests > 0) {
            this.realtimeMetrics.performance.errorRate = 
                (this.counters.totalErrors / totalRequests) * 100;
            this.realtimeMetrics.performance.timeoutRate = 
                (this.counters.totalTimeouts / totalRequests) * 100;
        }
    }
    
    /**
     * 收集指标数据
     */
    collectMetrics() {
        // 保存当前指标到历史数据
        const snapshot = {
            timestamp: Date.now(),
            metrics: JSON.parse(JSON.stringify(this.realtimeMetrics))
        };
        
        this.historicalData.realtime.push(snapshot);
        
        // 保持数据量限制
        if (this.historicalData.realtime.length > this.config.retention.realtime) {
            this.historicalData.realtime.shift();
        }
        
        // 触发指标收集事件
        this.emit('metricsCollected', snapshot);
    }
    
    /**
     * 执行性能分析
     */
    performAnalysis() {
        // 分析趋势
        this.analyzeTrends();
        
        // 分析异常
        this.analyzeAnomalies();
        
        // 生成性能报告
        this.generatePerformanceReport();
    }
    
    /**
     * 分析趋势
     */
    analyzeTrends() {
        const recentData = this.historicalData.realtime.slice(-10); // 最近10个数据点
        
        if (recentData.length < 2) return;
        
        // 连接数趋势
        const connectionTrend = this.calculateTrend(
            recentData.map(d => d.metrics.connections.total)
        );
        
        // 响应时间趋势
        const responseTimeTrend = this.calculateTrend(
            recentData.map(d => d.metrics.performance.responseTime.avg)
        );
        
        // 错误率趋势
        const errorRateTrend = this.calculateTrend(
            recentData.map(d => d.metrics.performance.errorRate)
        );
        
        const trends = {
            connections: connectionTrend,
            responseTime: responseTimeTrend,
            errorRate: errorRateTrend
        };
        
        this.emit('trendsAnalyzed', trends);
    }
    
    /**
     * 计算趋势
     */
    calculateTrend(values) {
        if (values.length < 2) return 'stable';
        
        const first = values[0];
        const last = values[values.length - 1];
        const change = ((last - first) / first) * 100;
        
        if (Math.abs(change) < 5) return 'stable';
        return change > 0 ? 'increasing' : 'decreasing';
    }
    
    /**
     * 分析异常
     */
    analyzeAnomalies() {
        const current = this.realtimeMetrics;
        const anomalies = [];
        
        // 检查响应时间异常
        if (current.performance.responseTime.avg > this.config.thresholds.avgResponseTimeThreshold) {
            anomalies.push({
                type: 'high_response_time',
                value: current.performance.responseTime.avg,
                threshold: this.config.thresholds.avgResponseTimeThreshold
            });
        }
        
        // 检查错误率异常
        if (current.performance.errorRate > this.config.thresholds.maxErrorRate) {
            anomalies.push({
                type: 'high_error_rate',
                value: current.performance.errorRate,
                threshold: this.config.thresholds.maxErrorRate
            });
        }
        
        // 检查资源使用异常
        if (current.resources.cpu.usage > this.config.thresholds.maxCpuUsage) {
            anomalies.push({
                type: 'high_cpu_usage',
                value: current.resources.cpu.usage,
                threshold: this.config.thresholds.maxCpuUsage
            });
        }
        
        if (current.resources.memory.usage > this.config.thresholds.maxMemoryUsage) {
            anomalies.push({
                type: 'high_memory_usage',
                value: current.resources.memory.usage,
                threshold: this.config.thresholds.maxMemoryUsage
            });
        }
        
        if (anomalies.length > 0) {
            this.emit('anomaliesDetected', anomalies);
        }
    }
    
    /**
     * 生成性能报告
     */
    generatePerformanceReport() {
        const report = {
            timestamp: Date.now(),
            summary: {
                totalConnections: this.realtimeMetrics.connections.total,
                activeConnections: this.realtimeMetrics.connections.active,
                messageRate: this.realtimeMetrics.messages.rate,
                avgResponseTime: this.realtimeMetrics.performance.responseTime.avg,
                errorRate: this.realtimeMetrics.performance.errorRate,
                cpuUsage: this.realtimeMetrics.resources.cpu.usage,
                memoryUsage: this.realtimeMetrics.resources.memory.usage
            },
            health: this.calculateHealthScore(),
            recommendations: this.generateRecommendations()
        };
        
        this.emit('performanceReport', report);
    }
    
    /**
     * 计算健康分数
     */
    calculateHealthScore() {
        let score = 100;
        const current = this.realtimeMetrics;
        
        // 响应时间影响
        if (current.performance.responseTime.avg > this.config.thresholds.avgResponseTimeThreshold) {
            score -= 20;
        }
        
        // 错误率影响
        if (current.performance.errorRate > this.config.thresholds.maxErrorRate) {
            score -= 25;
        }
        
        // CPU使用率影响
        if (current.resources.cpu.usage > this.config.thresholds.maxCpuUsage) {
            score -= 15;
        }
        
        // 内存使用率影响
        if (current.resources.memory.usage > this.config.thresholds.maxMemoryUsage) {
            score -= 15;
        }
        
        // 连接数影响
        if (current.connections.total > this.config.thresholds.maxConnections * 0.9) {
            score -= 10;
        }
        
        return Math.max(0, score);
    }
    
    /**
     * 生成建议
     */
    generateRecommendations() {
        const recommendations = [];
        const current = this.realtimeMetrics;
        
        if (current.performance.responseTime.avg > this.config.thresholds.avgResponseTimeThreshold) {
            recommendations.push({
                type: 'performance',
                priority: 'high',
                message: '响应时间过高，建议优化数据库查询或增加缓存'
            });
        }
        
        if (current.resources.cpu.usage > this.config.thresholds.maxCpuUsage) {
            recommendations.push({
                type: 'resource',
                priority: 'high',
                message: 'CPU使用率过高，建议优化算法或增加服务器资源'
            });
        }
        
        if (current.resources.memory.usage > this.config.thresholds.maxMemoryUsage) {
            recommendations.push({
                type: 'resource',
                priority: 'medium',
                message: '内存使用率过高，建议检查内存泄漏或增加内存'
            });
        }
        
        if (current.connections.total > this.config.thresholds.maxConnections * 0.8) {
            recommendations.push({
                type: 'capacity',
                priority: 'medium',
                message: '连接数接近上限，建议准备扩容'
            });
        }
        
        return recommendations;
    }
    
    /**
     * 检查阈值
     */
    checkThresholds() {
        const current = this.realtimeMetrics;
        const alerts = [];
        
        // 检查各项指标
        if (current.connections.total > this.config.thresholds.maxConnections) {
            alerts.push(this.createAlert('max_connections_exceeded', current.connections.total));
        }
        
        if (current.messages.rate > this.config.thresholds.maxMessagesPerSecond) {
            alerts.push(this.createAlert('max_message_rate_exceeded', current.messages.rate));
        }
        
        if (current.performance.responseTime.avg > this.config.thresholds.maxResponseTime) {
            alerts.push(this.createAlert('max_response_time_exceeded', current.performance.responseTime.avg));
        }
        
        if (current.resources.cpu.usage > this.config.thresholds.maxCpuUsage) {
            alerts.push(this.createAlert('max_cpu_usage_exceeded', current.resources.cpu.usage));
        }
        
        if (current.resources.memory.usage > this.config.thresholds.maxMemoryUsage) {
            alerts.push(this.createAlert('max_memory_usage_exceeded', current.resources.memory.usage));
        }
        
        // 处理警报
        alerts.forEach(alert => this.handleAlert(alert));
    }
    
    /**
     * 创建警报
     */
    createAlert(type, value) {
        return {
            id: `${type}_${Date.now()}`,
            type,
            value,
            threshold: this.config.thresholds[type.replace('max_', '').replace('_exceeded', '')],
            timestamp: Date.now(),
            severity: this.getAlertSeverity(type, value)
        };
    }
    
    /**
     * 获取警报严重程度
     */
    getAlertSeverity(type, value) {
        // 根据超出阈值的程度确定严重程度
        const threshold = this.config.thresholds[type.replace('max_', '').replace('_exceeded', '')];
        const ratio = value / threshold;
        
        if (ratio > 1.5) return 'critical';
        if (ratio > 1.2) return 'high';
        if (ratio > 1.1) return 'medium';
        return 'low';
    }
    
    /**
     * 处理警报
     */
    handleAlert(alert) {
        // 检查是否已存在相同类型的活跃警报
        if (this.alerts.active.has(alert.type)) {
            return; // 避免重复警报
        }
        
        // 添加到活跃警报
        this.alerts.active.set(alert.type, alert);
        
        // 添加到历史记录
        this.alerts.history.push(alert);
        
        // 保持历史记录限制
        if (this.alerts.history.length > 1000) {
            this.alerts.history.shift();
        }
        
        // 触发警报事件
        this.emit('alert', alert);
        
        // 根据严重程度采取行动
        if (alert.severity === 'critical') {
            this.emit('criticalAlert', alert);
        }
    }
    
    /**
     * 记录连接事件
     */
    recordConnection(connectionId, event, data = {}) {
        const record = {
            connectionId,
            event,
            timestamp: Date.now(),
            data
        };
        
        if (event === 'connected') {
            this.connectionTracker.current.set(connectionId, {
                id: connectionId,
                connectedAt: record.timestamp,
                lastActivity: record.timestamp,
                ...data
            });
            this.counters.totalConnections++;
        } else if (event === 'disconnected') {
            this.connectionTracker.current.delete(connectionId);
        } else if (event === 'activity') {
            const connection = this.connectionTracker.current.get(connectionId);
            if (connection) {
                connection.lastActivity = record.timestamp;
            }
        }
        
        this.connectionTracker.history.push(record);
        
        // 保持历史记录限制
        if (this.connectionTracker.history.length > 10000) {
            this.connectionTracker.history.shift();
        }
    }
    
    /**
     * 记录消息事件
     */
    recordMessage(messageId, event, data = {}) {
        const record = {
            messageId,
            event,
            timestamp: Date.now(),
            data
        };
        
        this.messageTracker.history.push(record);
        this.counters.totalMessages++;
        
        if (event === 'failed') {
            this.counters.totalErrors++;
        }
        
        // 保持历史记录限制
        if (this.messageTracker.history.length > 10000) {
            this.messageTracker.history.shift();
        }
    }
    
    /**
     * 记录响应时间
     */
    recordResponseTime(responseTime) {
        this.responseTimeTracker.samples.push(responseTime);
        this.counters.totalRequests++;
        
        // 保持样本数量限制
        if (this.responseTimeTracker.samples.length > this.responseTimeTracker.maxSamples) {
            this.responseTimeTracker.samples.shift();
        }
    }
    
    /**
     * 记录错误
     */
    recordError(error) {
        this.counters.totalErrors++;
    }
    
    /**
     * 记录超时
     */
    recordTimeout() {
        this.counters.totalTimeouts++;
    }
    
    /**
     * 清理数据
     */
    cleanupData() {
        const now = Date.now();
        
        // 清理连接历史
        const connectionCutoff = now - (24 * 60 * 60 * 1000); // 24小时
        this.connectionTracker.history = this.connectionTracker.history.filter(
            record => record.timestamp > connectionCutoff
        );
        
        // 清理消息历史
        const messageCutoff = now - (24 * 60 * 60 * 1000); // 24小时
        this.messageTracker.history = this.messageTracker.history.filter(
            record => record.timestamp > messageCutoff
        );
        
        // 清理过期警报
        const alertCutoff = now - (7 * 24 * 60 * 60 * 1000); // 7天
        this.alerts.history = this.alerts.history.filter(
            alert => alert.timestamp > alertCutoff
        );
        
        // 清理已解决的活跃警报
        for (const [type, alert] of this.alerts.active) {
            if (now - alert.timestamp > 300000) { // 5分钟后自动清理
                this.alerts.active.delete(type);
            }
        }
    }
    
    /**
     * 获取当前指标
     */
    getCurrentMetrics() {
        return { ...this.realtimeMetrics };
    }
    
    /**
     * 获取历史数据
     */
    getHistoricalData(timeRange = 'realtime') {
        return this.historicalData[timeRange] || [];
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            counters: { ...this.counters },
            connections: {
                total: this.connectionTracker.current.size,
                history: this.connectionTracker.history.length
            },
            messages: {
                history: this.messageTracker.history.length,
                queues: this.messageTracker.queues.size
            },
            alerts: {
                active: this.alerts.active.size,
                history: this.alerts.history.length
            },
            health: this.calculateHealthScore()
        };
    }
    
    /**
     * 关闭性能监控
     */
    close() {
        console.log('关闭实时性能监控系统...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.connectionTracker.current.clear();
        this.connectionTracker.history = [];
        this.messageTracker.history = [];
        this.messageTracker.queues.clear();
        this.alerts.active.clear();
        this.alerts.history = [];
        this.historicalData.realtime = [];
        this.historicalData.hourly = [];
        this.historicalData.daily = [];
        
        console.log('性能监控系统已关闭');
    }
}

module.exports = PerformanceMonitor;
