/**
 * 异常告警系统
 * 
 * 实现异常情况的自动告警，包括性能异常、连接异常、成本异常等的实时告警和通知
 * 支持多种通知方式
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class AlertSystem extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 告警规则
            rules: {
                // 性能告警
                performance: {
                    highResponseTime: {
                        threshold: 200,
                        duration: 60000,        // 持续1分钟
                        severity: 'high',
                        enabled: true
                    },
                    highErrorRate: {
                        threshold: 5,           // 5%
                        duration: 30000,        // 持续30秒
                        severity: 'critical',
                        enabled: true
                    },
                    lowThroughput: {
                        threshold: 100,         // 每秒100请求以下
                        duration: 120000,       // 持续2分钟
                        severity: 'medium',
                        enabled: true
                    }
                },
                
                // 资源告警
                resource: {
                    highCpuUsage: {
                        threshold: 80,          // 80%
                        duration: 180000,       // 持续3分钟
                        severity: 'high',
                        enabled: true
                    },
                    highMemoryUsage: {
                        threshold: 85,          // 85%
                        duration: 300000,       // 持续5分钟
                        severity: 'high',
                        enabled: true
                    },
                    diskSpaceLow: {
                        threshold: 90,          // 90%
                        duration: 60000,        // 持续1分钟
                        severity: 'critical',
                        enabled: true
                    }
                },
                
                // 连接告警
                connection: {
                    tooManyConnections: {
                        threshold: 4500,        // 接近5000上限
                        duration: 30000,        // 持续30秒
                        severity: 'high',
                        enabled: true
                    },
                    connectionDropRate: {
                        threshold: 10,          // 10%连接掉线率
                        duration: 60000,        // 持续1分钟
                        severity: 'medium',
                        enabled: true
                    },
                    slowConnectionGrowth: {
                        threshold: -50,         // 连接数下降50以上
                        duration: 300000,       // 持续5分钟
                        severity: 'low',
                        enabled: true
                    }
                },
                
                // 成本告警
                cost: {
                    budgetExceeded: {
                        threshold: 80,          // 预算的80%
                        duration: 0,            // 立即告警
                        severity: 'critical',
                        enabled: true
                    },
                    unusualSpending: {
                        threshold: 150,         // 比平均高50%
                        duration: 3600000,      // 持续1小时
                        severity: 'medium',
                        enabled: true
                    }
                },
                
                // 业务告警
                business: {
                    lowUserActivity: {
                        threshold: 100,         // 活跃用户少于100
                        duration: 1800000,      // 持续30分钟
                        severity: 'low',
                        enabled: true
                    },
                    highSpamRate: {
                        threshold: 20,          // 20%垃圾消息率
                        duration: 60000,        // 持续1分钟
                        severity: 'high',
                        enabled: true
                    }
                }
            },
            
            // 通知渠道
            channels: {
                email: {
                    enabled: true,
                    recipients: ['<EMAIL>'],
                    severityFilter: ['critical', 'high']
                },
                sms: {
                    enabled: true,
                    recipients: ['+1234567890'],
                    severityFilter: ['critical']
                },
                webhook: {
                    enabled: true,
                    url: 'https://hooks.slack.com/services/xxx',
                    severityFilter: ['critical', 'high', 'medium']
                },
                dingtalk: {
                    enabled: true,
                    webhook: 'https://oapi.dingtalk.com/robot/send?access_token=xxx',
                    severityFilter: ['critical', 'high']
                }
            },
            
            // 告警抑制
            suppression: {
                enabled: true,
                cooldownPeriod: 300000,     // 5分钟冷却期
                maxAlertsPerHour: 10,       // 每小时最多10个告警
                duplicateWindow: 600000     // 10分钟内相同告警去重
            },
            
            // 告警升级
            escalation: {
                enabled: true,
                levels: [
                    { delay: 900000, channels: ['email'] },        // 15分钟后邮件
                    { delay: 1800000, channels: ['sms'] },         // 30分钟后短信
                    { delay: 3600000, channels: ['webhook'] }      // 1小时后webhook
                ]
            }
        };
        
        // 活跃告警
        this.activeAlerts = new Map();
        
        // 告警历史
        this.alertHistory = [];
        
        // 告警统计
        this.alertStats = {
            total: 0,
            bySeverity: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            byCategory: {
                performance: 0,
                resource: 0,
                connection: 0,
                cost: 0,
                business: 0
            },
            resolved: 0,
            suppressed: 0
        };
        
        // 规则状态追踪
        this.ruleStates = new Map();
        
        // 通知队列
        this.notificationQueue = [];
        
        // 定时器
        this.timers = {
            ruleCheck: null,
            notification: null,
            cleanup: null,
            escalation: null
        };
        
        this.initialize();
    }
    
    /**
     * 初始化告警系统
     */
    initialize() {
        console.log('初始化异常告警系统...');
        
        // 初始化规则状态
        this.initializeRuleStates();
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        console.log('告警系统初始化完成');
    }
    
    /**
     * 初始化规则状态
     */
    initializeRuleStates() {
        for (const [category, rules] of Object.entries(this.config.rules)) {
            for (const [ruleName, rule] of Object.entries(rules)) {
                const ruleId = `${category}.${ruleName}`;
                this.ruleStates.set(ruleId, {
                    id: ruleId,
                    category,
                    name: ruleName,
                    rule,
                    triggered: false,
                    triggerTime: null,
                    lastCheck: null,
                    consecutiveViolations: 0,
                    history: []
                });
            }
        }
    }
    
    /**
     * 启动定期任务
     */
    startPeriodicTasks() {
        // 规则检查
        this.timers.ruleCheck = setInterval(() => {
            this.checkRules();
        }, 10000); // 10秒检查一次
        
        // 通知处理
        this.timers.notification = setInterval(() => {
            this.processNotificationQueue();
        }, 5000); // 5秒处理一次通知
        
        // 数据清理
        this.timers.cleanup = setInterval(() => {
            this.cleanupData();
        }, 300000); // 5分钟清理一次
        
        // 告警升级
        this.timers.escalation = setInterval(() => {
            this.processEscalation();
        }, 60000); // 1分钟检查一次升级
    }
    
    /**
     * 检查告警规则
     */
    checkRules() {
        for (const [ruleId, ruleState] of this.ruleStates) {
            if (!ruleState.rule.enabled) continue;
            
            this.checkRule(ruleState);
        }
    }
    
    /**
     * 检查单个规则
     */
    checkRule(ruleState) {
        const now = Date.now();
        ruleState.lastCheck = now;
        
        // 获取当前指标值
        const currentValue = this.getCurrentMetricValue(ruleState.category, ruleState.name);
        if (currentValue === null) return;
        
        // 检查是否违反阈值
        const violated = this.isThresholdViolated(currentValue, ruleState.rule.threshold, ruleState.name);
        
        if (violated) {
            ruleState.consecutiveViolations++;
            
            if (!ruleState.triggered) {
                // 首次触发
                ruleState.triggered = true;
                ruleState.triggerTime = now;
            } else {
                // 检查持续时间
                const duration = now - ruleState.triggerTime;
                if (duration >= ruleState.rule.duration) {
                    // 满足持续时间条件，触发告警
                    this.triggerAlert(ruleState, currentValue);
                }
            }
        } else {
            // 恢复正常
            if (ruleState.triggered) {
                this.resolveAlert(ruleState, currentValue);
            }
            
            ruleState.triggered = false;
            ruleState.triggerTime = null;
            ruleState.consecutiveViolations = 0;
        }
        
        // 记录历史
        ruleState.history.push({
            timestamp: now,
            value: currentValue,
            violated,
            triggered: ruleState.triggered
        });
        
        // 保持历史记录限制
        if (ruleState.history.length > 1000) {
            ruleState.history.shift();
        }
    }
    
    /**
     * 获取当前指标值
     */
    getCurrentMetricValue(category, name) {
        // 这里应该从性能监控系统获取实际数据
        // 为了演示，返回模拟数据
        const mockData = {
            performance: {
                highResponseTime: Math.random() * 300,
                highErrorRate: Math.random() * 10,
                lowThroughput: Math.random() * 200
            },
            resource: {
                highCpuUsage: Math.random() * 100,
                highMemoryUsage: Math.random() * 100,
                diskSpaceLow: Math.random() * 100
            },
            connection: {
                tooManyConnections: Math.random() * 5000,
                connectionDropRate: Math.random() * 20,
                slowConnectionGrowth: (Math.random() - 0.5) * 200
            },
            cost: {
                budgetExceeded: Math.random() * 100,
                unusualSpending: Math.random() * 200
            },
            business: {
                lowUserActivity: Math.random() * 200,
                highSpamRate: Math.random() * 30
            }
        };
        
        return mockData[category]?.[name] || null;
    }
    
    /**
     * 检查是否违反阈值
     */
    isThresholdViolated(value, threshold, ruleName) {
        // 根据规则名称确定比较方式
        const lowerIsBetter = [
            'highResponseTime', 'highErrorRate', 'highCpuUsage', 'highMemoryUsage',
            'diskSpaceLow', 'tooManyConnections', 'connectionDropRate', 'budgetExceeded',
            'unusualSpending', 'highSpamRate'
        ];
        
        const higherIsBetter = [
            'lowThroughput', 'lowUserActivity'
        ];
        
        if (lowerIsBetter.includes(ruleName)) {
            return value > threshold;
        } else if (higherIsBetter.includes(ruleName)) {
            return value < threshold;
        } else if (ruleName === 'slowConnectionGrowth') {
            return value < threshold; // 负增长
        }
        
        return false;
    }
    
    /**
     * 触发告警
     */
    triggerAlert(ruleState, currentValue) {
        const alertId = crypto.randomUUID();
        const now = Date.now();
        
        const alert = {
            id: alertId,
            ruleId: ruleState.id,
            category: ruleState.category,
            name: ruleState.name,
            severity: ruleState.rule.severity,
            threshold: ruleState.rule.threshold,
            currentValue,
            triggeredAt: now,
            resolvedAt: null,
            status: 'active',
            notificationsSent: [],
            escalationLevel: 0,
            description: this.generateAlertDescription(ruleState, currentValue),
            metadata: {
                consecutiveViolations: ruleState.consecutiveViolations,
                duration: now - ruleState.triggerTime
            }
        };
        
        // 检查是否应该抑制
        if (this.shouldSuppressAlert(alert)) {
            this.alertStats.suppressed++;
            return;
        }
        
        // 添加到活跃告警
        this.activeAlerts.set(alertId, alert);
        
        // 添加到历史记录
        this.alertHistory.push(alert);
        
        // 更新统计
        this.alertStats.total++;
        this.alertStats.bySeverity[alert.severity]++;
        this.alertStats.byCategory[alert.category]++;
        
        // 发送通知
        this.scheduleNotification(alert);
        
        // 触发事件
        this.emit('alertTriggered', alert);
        
        console.log(`告警触发: ${alert.description}`);
    }
    
    /**
     * 解决告警
     */
    resolveAlert(ruleState, currentValue) {
        // 查找对应的活跃告警
        for (const [alertId, alert] of this.activeAlerts) {
            if (alert.ruleId === ruleState.id && alert.status === 'active') {
                alert.status = 'resolved';
                alert.resolvedAt = Date.now();
                alert.resolvedValue = currentValue;
                
                // 从活跃告警中移除
                this.activeAlerts.delete(alertId);
                
                // 更新统计
                this.alertStats.resolved++;
                
                // 发送解决通知
                this.scheduleResolutionNotification(alert);
                
                // 触发事件
                this.emit('alertResolved', alert);
                
                console.log(`告警解决: ${alert.description}`);
                break;
            }
        }
    }
    
    /**
     * 生成告警描述
     */
    generateAlertDescription(ruleState, currentValue) {
        const descriptions = {
            'performance.highResponseTime': `响应时间过高: ${currentValue.toFixed(2)}ms (阈值: ${ruleState.rule.threshold}ms)`,
            'performance.highErrorRate': `错误率过高: ${currentValue.toFixed(2)}% (阈值: ${ruleState.rule.threshold}%)`,
            'performance.lowThroughput': `吞吐量过低: ${currentValue.toFixed(0)}请求/秒 (阈值: ${ruleState.rule.threshold}请求/秒)`,
            'resource.highCpuUsage': `CPU使用率过高: ${currentValue.toFixed(1)}% (阈值: ${ruleState.rule.threshold}%)`,
            'resource.highMemoryUsage': `内存使用率过高: ${currentValue.toFixed(1)}% (阈值: ${ruleState.rule.threshold}%)`,
            'resource.diskSpaceLow': `磁盘空间不足: ${currentValue.toFixed(1)}% (阈值: ${ruleState.rule.threshold}%)`,
            'connection.tooManyConnections': `连接数过多: ${currentValue.toFixed(0)} (阈值: ${ruleState.rule.threshold})`,
            'connection.connectionDropRate': `连接掉线率过高: ${currentValue.toFixed(2)}% (阈值: ${ruleState.rule.threshold}%)`,
            'connection.slowConnectionGrowth': `连接数下降: ${currentValue.toFixed(0)} (阈值: ${ruleState.rule.threshold})`,
            'cost.budgetExceeded': `预算超支: ${currentValue.toFixed(1)}% (阈值: ${ruleState.rule.threshold}%)`,
            'cost.unusualSpending': `异常支出: ${currentValue.toFixed(1)}% (阈值: ${ruleState.rule.threshold}%)`,
            'business.lowUserActivity': `用户活跃度低: ${currentValue.toFixed(0)}人 (阈值: ${ruleState.rule.threshold}人)`,
            'business.highSpamRate': `垃圾消息率过高: ${currentValue.toFixed(2)}% (阈值: ${ruleState.rule.threshold}%)`
        };
        
        return descriptions[ruleState.id] || `${ruleState.id}: ${currentValue} (阈值: ${ruleState.rule.threshold})`;
    }
    
    /**
     * 检查是否应该抑制告警
     */
    shouldSuppressAlert(alert) {
        if (!this.config.suppression.enabled) return false;
        
        const now = Date.now();
        
        // 检查冷却期
        const recentAlerts = this.alertHistory.filter(a => 
            a.ruleId === alert.ruleId && 
            now - a.triggeredAt < this.config.suppression.cooldownPeriod
        );
        
        if (recentAlerts.length > 0) return true;
        
        // 检查每小时告警数量限制
        const hourlyAlerts = this.alertHistory.filter(a => 
            now - a.triggeredAt < 3600000
        );
        
        if (hourlyAlerts.length >= this.config.suppression.maxAlertsPerHour) return true;
        
        // 检查重复告警窗口
        const duplicateAlerts = this.alertHistory.filter(a => 
            a.ruleId === alert.ruleId && 
            a.severity === alert.severity &&
            now - a.triggeredAt < this.config.suppression.duplicateWindow
        );
        
        if (duplicateAlerts.length > 0) return true;
        
        return false;
    }
    
    /**
     * 安排通知
     */
    scheduleNotification(alert) {
        const notification = {
            id: crypto.randomUUID(),
            alertId: alert.id,
            type: 'alert',
            alert,
            scheduledAt: Date.now(),
            attempts: 0,
            maxAttempts: 3,
            channels: this.getNotificationChannels(alert.severity)
        };
        
        this.notificationQueue.push(notification);
    }
    
    /**
     * 安排解决通知
     */
    scheduleResolutionNotification(alert) {
        const notification = {
            id: crypto.randomUUID(),
            alertId: alert.id,
            type: 'resolution',
            alert,
            scheduledAt: Date.now(),
            attempts: 0,
            maxAttempts: 3,
            channels: this.getNotificationChannels(alert.severity)
        };
        
        this.notificationQueue.push(notification);
    }
    
    /**
     * 获取通知渠道
     */
    getNotificationChannels(severity) {
        const channels = [];
        
        for (const [channelName, config] of Object.entries(this.config.channels)) {
            if (config.enabled && config.severityFilter.includes(severity)) {
                channels.push(channelName);
            }
        }
        
        return channels;
    }
    
    /**
     * 处理通知队列
     */
    processNotificationQueue() {
        const now = Date.now();
        
        // 处理待发送的通知
        const pendingNotifications = this.notificationQueue.filter(n => 
            n.scheduledAt <= now && n.attempts < n.maxAttempts
        );
        
        pendingNotifications.forEach(notification => {
            this.sendNotification(notification);
        });
        
        // 清理已完成或失败的通知
        this.notificationQueue = this.notificationQueue.filter(n => 
            n.attempts < n.maxAttempts && n.scheduledAt > now - 3600000 // 保留1小时内的记录
        );
    }
    
    /**
     * 发送通知
     */
    async sendNotification(notification) {
        notification.attempts++;
        
        try {
            for (const channelName of notification.channels) {
                await this.sendToChannel(channelName, notification);
            }
            
            // 记录成功发送
            const alert = this.activeAlerts.get(notification.alertId) || 
                         this.alertHistory.find(a => a.id === notification.alertId);
            
            if (alert) {
                alert.notificationsSent.push({
                    timestamp: Date.now(),
                    channels: notification.channels,
                    type: notification.type
                });
            }
            
            console.log(`通知发送成功: ${notification.type} for alert ${notification.alertId}`);
            
        } catch (error) {
            console.error(`通知发送失败: ${error.message}`);
            
            // 如果还有重试机会，重新安排
            if (notification.attempts < notification.maxAttempts) {
                notification.scheduledAt = Date.now() + 60000; // 1分钟后重试
            }
        }
    }
    
    /**
     * 发送到指定渠道
     */
    async sendToChannel(channelName, notification) {
        const config = this.config.channels[channelName];
        if (!config || !config.enabled) return;
        
        const message = this.formatNotificationMessage(notification, channelName);
        
        switch (channelName) {
            case 'email':
                await this.sendEmail(config.recipients, message);
                break;
            case 'sms':
                await this.sendSMS(config.recipients, message);
                break;
            case 'webhook':
                await this.sendWebhook(config.url, message);
                break;
            case 'dingtalk':
                await this.sendDingTalk(config.webhook, message);
                break;
        }
    }
    
    /**
     * 格式化通知消息
     */
    formatNotificationMessage(notification, channelName) {
        const alert = notification.alert;
        const isResolution = notification.type === 'resolution';
        
        const baseMessage = {
            title: isResolution ? '告警解决' : '系统告警',
            severity: alert.severity,
            category: alert.category,
            description: alert.description,
            triggeredAt: new Date(alert.triggeredAt).toLocaleString(),
            resolvedAt: isResolution ? new Date(alert.resolvedAt).toLocaleString() : null
        };
        
        // 根据渠道格式化消息
        switch (channelName) {
            case 'email':
                return this.formatEmailMessage(baseMessage);
            case 'sms':
                return this.formatSMSMessage(baseMessage);
            case 'webhook':
            case 'dingtalk':
                return this.formatWebhookMessage(baseMessage);
            default:
                return baseMessage;
        }
    }
    
    /**
     * 格式化邮件消息
     */
    formatEmailMessage(message) {
        return {
            subject: `[${message.severity.toUpperCase()}] ${message.title}`,
            body: `
告警详情:
- 类别: ${message.category}
- 严重程度: ${message.severity}
- 描述: ${message.description}
- 触发时间: ${message.triggeredAt}
${message.resolvedAt ? `- 解决时间: ${message.resolvedAt}` : ''}

请及时处理相关问题。
            `.trim()
        };
    }
    
    /**
     * 格式化短信消息
     */
    formatSMSMessage(message) {
        return `[${message.severity.toUpperCase()}] ${message.description} - ${message.triggeredAt}`;
    }
    
    /**
     * 格式化Webhook消息
     */
    formatWebhookMessage(message) {
        return {
            text: `${message.title}: ${message.description}`,
            attachments: [{
                color: this.getSeverityColor(message.severity),
                fields: [
                    { title: '类别', value: message.category, short: true },
                    { title: '严重程度', value: message.severity, short: true },
                    { title: '触发时间', value: message.triggeredAt, short: false }
                ]
            }]
        };
    }
    
    /**
     * 获取严重程度颜色
     */
    getSeverityColor(severity) {
        const colors = {
            critical: '#ff0000',
            high: '#ff8800',
            medium: '#ffaa00',
            low: '#00aa00'
        };
        return colors[severity] || '#888888';
    }
    
    /**
     * 模拟发送邮件
     */
    async sendEmail(recipients, message) {
        console.log(`发送邮件到: ${recipients.join(', ')}`);
        console.log(`主题: ${message.subject}`);
        console.log(`内容: ${message.body}`);
        // 实际实现中应该使用邮件服务
    }
    
    /**
     * 模拟发送短信
     */
    async sendSMS(recipients, message) {
        console.log(`发送短信到: ${recipients.join(', ')}`);
        console.log(`内容: ${message}`);
        // 实际实现中应该使用短信服务
    }
    
    /**
     * 模拟发送Webhook
     */
    async sendWebhook(url, message) {
        console.log(`发送Webhook到: ${url}`);
        console.log(`内容: ${JSON.stringify(message, null, 2)}`);
        // 实际实现中应该使用HTTP请求
    }
    
    /**
     * 模拟发送钉钉消息
     */
    async sendDingTalk(webhook, message) {
        console.log(`发送钉钉消息到: ${webhook}`);
        console.log(`内容: ${JSON.stringify(message, null, 2)}`);
        // 实际实现中应该使用钉钉API
    }
    
    /**
     * 处理告警升级
     */
    processEscalation() {
        if (!this.config.escalation.enabled) return;
        
        const now = Date.now();
        
        for (const [alertId, alert] of this.activeAlerts) {
            const alertAge = now - alert.triggeredAt;
            const currentLevel = alert.escalationLevel;
            
            // 检查是否需要升级
            for (let i = currentLevel; i < this.config.escalation.levels.length; i++) {
                const level = this.config.escalation.levels[i];
                
                if (alertAge >= level.delay) {
                    // 执行升级
                    this.escalateAlert(alert, i + 1, level.channels);
                    alert.escalationLevel = i + 1;
                }
            }
        }
    }
    
    /**
     * 升级告警
     */
    escalateAlert(alert, level, channels) {
        console.log(`告警升级到级别 ${level}: ${alert.description}`);
        
        // 发送升级通知
        const notification = {
            id: crypto.randomUUID(),
            alertId: alert.id,
            type: 'escalation',
            alert: { ...alert, escalationLevel: level },
            scheduledAt: Date.now(),
            attempts: 0,
            maxAttempts: 3,
            channels
        };
        
        this.notificationQueue.push(notification);
        
        this.emit('alertEscalated', { alert, level });
    }
    
    /**
     * 清理数据
     */
    cleanupData() {
        const now = Date.now();
        const retentionPeriod = 7 * 24 * 60 * 60 * 1000; // 7天
        
        // 清理告警历史
        const originalLength = this.alertHistory.length;
        this.alertHistory = this.alertHistory.filter(alert => 
            now - alert.triggeredAt < retentionPeriod
        );
        
        const cleanedCount = originalLength - this.alertHistory.length;
        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 条过期告警记录`);
        }
        
        // 清理规则状态历史
        for (const [ruleId, ruleState] of this.ruleStates) {
            ruleState.history = ruleState.history.filter(record => 
                now - record.timestamp < retentionPeriod
            );
        }
    }
    
    /**
     * 获取活跃告警
     */
    getActiveAlerts() {
        return Array.from(this.activeAlerts.values());
    }
    
    /**
     * 获取告警历史
     */
    getAlertHistory(filters = {}) {
        let history = this.alertHistory;
        
        if (filters.severity) {
            history = history.filter(alert => alert.severity === filters.severity);
        }
        
        if (filters.category) {
            history = history.filter(alert => alert.category === filters.category);
        }
        
        if (filters.startTime) {
            history = history.filter(alert => alert.triggeredAt >= filters.startTime);
        }
        
        if (filters.endTime) {
            history = history.filter(alert => alert.triggeredAt <= filters.endTime);
        }
        
        return history.sort((a, b) => b.triggeredAt - a.triggeredAt);
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.alertStats,
            active: this.activeAlerts.size,
            rules: {
                total: this.ruleStates.size,
                enabled: Array.from(this.ruleStates.values()).filter(r => r.rule.enabled).length,
                triggered: Array.from(this.ruleStates.values()).filter(r => r.triggered).length
            },
            notifications: {
                queued: this.notificationQueue.length,
                pending: this.notificationQueue.filter(n => n.attempts < n.maxAttempts).length
            }
        };
    }
    
    /**
     * 手动触发告警测试
     */
    testAlert(ruleId, testValue) {
        const ruleState = this.ruleStates.get(ruleId);
        if (!ruleState) {
            throw new Error(`规则不存在: ${ruleId}`);
        }
        
        // 创建测试告警
        const alert = {
            id: `test_${crypto.randomUUID()}`,
            ruleId,
            category: ruleState.category,
            name: ruleState.name,
            severity: ruleState.rule.severity,
            threshold: ruleState.rule.threshold,
            currentValue: testValue,
            triggeredAt: Date.now(),
            resolvedAt: null,
            status: 'test',
            notificationsSent: [],
            escalationLevel: 0,
            description: `[测试] ${this.generateAlertDescription(ruleState, testValue)}`,
            metadata: {
                test: true
            }
        };
        
        // 发送测试通知
        this.scheduleNotification(alert);
        
        console.log(`测试告警已触发: ${alert.description}`);
        return alert;
    }
    
    /**
     * 关闭告警系统
     */
    close() {
        console.log('关闭异常告警系统...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.activeAlerts.clear();
        this.alertHistory = [];
        this.ruleStates.clear();
        this.notificationQueue = [];
        
        console.log('告警系统已关闭');
    }
}

module.exports = AlertSystem;
