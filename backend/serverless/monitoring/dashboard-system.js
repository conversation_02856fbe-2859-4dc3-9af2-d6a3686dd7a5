/**
 * 运维仪表盘与报表系统
 * 
 * 实现围观功能的运维仪表盘，包括实时数据展示、历史趋势分析、性能报表生成等
 * 支持多维度数据分析
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class DashboardSystem extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.config = {
            // 仪表盘配置
            dashboard: {
                refreshInterval: 5000,          // 5秒刷新间隔
                dataRetention: {
                    realtime: 3600000,          // 1小时实时数据
                    hourly: 86400000 * 7,       // 7天小时数据
                    daily: 86400000 * 30,       // 30天日数据
                    monthly: 86400000 * 365     // 1年月数据
                },
                widgets: [
                    'system_overview',
                    'performance_metrics',
                    'connection_stats',
                    'message_stats',
                    'resource_usage',
                    'alert_summary',
                    'cost_tracking',
                    'user_activity'
                ]
            },
            
            // 报表配置
            reports: {
                types: [
                    'daily_summary',
                    'weekly_performance',
                    'monthly_overview',
                    'cost_analysis',
                    'user_behavior',
                    'system_health'
                ],
                formats: ['json', 'csv', 'pdf'],
                schedule: {
                    daily: '00:00',
                    weekly: 'MON 00:00',
                    monthly: '1 00:00'
                }
            },
            
            // 数据聚合配置
            aggregation: {
                intervals: {
                    minute: 60000,
                    hour: 3600000,
                    day: 86400000,
                    week: 604800000,
                    month: **********
                },
                metrics: [
                    'connections',
                    'messages',
                    'response_time',
                    'error_rate',
                    'cpu_usage',
                    'memory_usage',
                    'cost',
                    'user_count'
                ]
            }
        };
        
        // 数据存储
        this.dataStore = {
            realtime: new Map(),
            hourly: new Map(),
            daily: new Map(),
            monthly: new Map()
        };
        
        // 仪表盘状态
        this.dashboardState = {
            lastUpdate: null,
            connectedClients: new Set(),
            widgets: new Map(),
            alerts: [],
            systemHealth: 100
        };
        
        // 报表缓存
        this.reportCache = new Map();
        
        // 数据聚合器
        this.aggregators = new Map();
        
        // 定时器
        this.timers = {
            dataCollection: null,
            aggregation: null,
            reportGeneration: null,
            cleanup: null
        };
        
        // 统计信息
        this.stats = {
            totalDataPoints: 0,
            reportsGenerated: 0,
            dashboardViews: 0,
            apiCalls: 0
        };
        
        this.initialize();
    }
    
    /**
     * 初始化仪表盘系统
     */
    initialize() {
        console.log('初始化运维仪表盘与报表系统...');
        
        // 初始化小组件
        this.initializeWidgets();
        
        // 初始化数据聚合器
        this.initializeAggregators();
        
        // 启动定时任务
        this.startPeriodicTasks();
        
        console.log('仪表盘系统初始化完成');
    }
    
    /**
     * 初始化小组件
     */
    initializeWidgets() {
        this.config.dashboard.widgets.forEach(widgetName => {
            this.dashboardState.widgets.set(widgetName, {
                name: widgetName,
                enabled: true,
                lastUpdate: null,
                data: {},
                config: this.getWidgetConfig(widgetName)
            });
        });
    }
    
    /**
     * 获取小组件配置
     */
    getWidgetConfig(widgetName) {
        const configs = {
            system_overview: {
                title: '系统概览',
                type: 'overview',
                metrics: ['uptime', 'version', 'environment', 'status'],
                refreshInterval: 30000
            },
            performance_metrics: {
                title: '性能指标',
                type: 'chart',
                metrics: ['response_time', 'throughput', 'error_rate'],
                chartType: 'line',
                timeRange: 3600000
            },
            connection_stats: {
                title: '连接统计',
                type: 'gauge',
                metrics: ['total_connections', 'active_connections', 'connection_rate'],
                maxValue: 5000
            },
            message_stats: {
                title: '消息统计',
                type: 'counter',
                metrics: ['messages_sent', 'messages_received', 'message_rate'],
                timeRange: 3600000
            },
            resource_usage: {
                title: '资源使用',
                type: 'progress',
                metrics: ['cpu_usage', 'memory_usage', 'disk_usage'],
                thresholds: { warning: 70, critical: 90 }
            },
            alert_summary: {
                title: '告警摘要',
                type: 'list',
                metrics: ['active_alerts', 'recent_alerts', 'alert_trends'],
                maxItems: 10
            },
            cost_tracking: {
                title: '成本追踪',
                type: 'chart',
                metrics: ['daily_cost', 'monthly_cost', 'cost_trend'],
                chartType: 'bar',
                timeRange: **********
            },
            user_activity: {
                title: '用户活动',
                type: 'heatmap',
                metrics: ['active_users', 'new_users', 'user_retention'],
                timeRange: 86400000
            }
        };
        
        return configs[widgetName] || { title: widgetName, type: 'default' };
    }
    
    /**
     * 初始化数据聚合器
     */
    initializeAggregators() {
        this.config.aggregation.metrics.forEach(metric => {
            this.aggregators.set(metric, {
                name: metric,
                rawData: [],
                aggregatedData: {
                    minute: [],
                    hour: [],
                    day: [],
                    week: [],
                    month: []
                },
                lastAggregation: {
                    minute: 0,
                    hour: 0,
                    day: 0,
                    week: 0,
                    month: 0
                }
            });
        });
    }
    
    /**
     * 启动定期任务
     */
    startPeriodicTasks() {
        // 数据收集
        this.timers.dataCollection = setInterval(() => {
            this.collectDashboardData();
        }, this.config.dashboard.refreshInterval);
        
        // 数据聚合
        this.timers.aggregation = setInterval(() => {
            this.performDataAggregation();
        }, 60000); // 1分钟聚合一次
        
        // 报表生成
        this.timers.reportGeneration = setInterval(() => {
            this.checkScheduledReports();
        }, 300000); // 5分钟检查一次
        
        // 数据清理
        this.timers.cleanup = setInterval(() => {
            this.cleanupData();
        }, 3600000); // 1小时清理一次
    }
    
    /**
     * 收集仪表盘数据
     */
    collectDashboardData() {
        const now = Date.now();
        
        // 更新各个小组件的数据
        for (const [widgetName, widget] of this.dashboardState.widgets) {
            if (widget.enabled) {
                this.updateWidgetData(widget);
            }
        }
        
        // 更新仪表盘状态
        this.dashboardState.lastUpdate = now;
        
        // 计算系统健康度
        this.updateSystemHealth();
        
        // 广播更新给连接的客户端
        this.broadcastUpdate();
        
        this.stats.totalDataPoints++;
    }
    
    /**
     * 更新小组件数据
     */
    updateWidgetData(widget) {
        const now = Date.now();
        
        switch (widget.name) {
            case 'system_overview':
                widget.data = this.getSystemOverviewData();
                break;
            case 'performance_metrics':
                widget.data = this.getPerformanceMetricsData();
                break;
            case 'connection_stats':
                widget.data = this.getConnectionStatsData();
                break;
            case 'message_stats':
                widget.data = this.getMessageStatsData();
                break;
            case 'resource_usage':
                widget.data = this.getResourceUsageData();
                break;
            case 'alert_summary':
                widget.data = this.getAlertSummaryData();
                break;
            case 'cost_tracking':
                widget.data = this.getCostTrackingData();
                break;
            case 'user_activity':
                widget.data = this.getUserActivityData();
                break;
        }
        
        widget.lastUpdate = now;
    }
    
    /**
     * 获取系统概览数据
     */
    getSystemOverviewData() {
        return {
            uptime: process.uptime() * 1000,
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            status: 'running',
            startTime: Date.now() - (process.uptime() * 1000),
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch
        };
    }
    
    /**
     * 获取性能指标数据
     */
    getPerformanceMetricsData() {
        // 模拟性能数据
        const now = Date.now();
        const timeRange = 3600000; // 1小时
        const dataPoints = [];
        
        for (let i = 0; i < 60; i++) {
            const timestamp = now - (timeRange - (i * 60000));
            dataPoints.push({
                timestamp,
                response_time: 50 + Math.random() * 100,
                throughput: 800 + Math.random() * 400,
                error_rate: Math.random() * 5
            });
        }
        
        return {
            current: {
                response_time: dataPoints[dataPoints.length - 1].response_time,
                throughput: dataPoints[dataPoints.length - 1].throughput,
                error_rate: dataPoints[dataPoints.length - 1].error_rate
            },
            history: dataPoints,
            trends: this.calculateTrends(dataPoints)
        };
    }
    
    /**
     * 获取连接统计数据
     */
    getConnectionStatsData() {
        const totalConnections = Math.floor(Math.random() * 4000) + 1000;
        const activeConnections = Math.floor(totalConnections * (0.7 + Math.random() * 0.3));
        
        return {
            total_connections: totalConnections,
            active_connections: activeConnections,
            idle_connections: totalConnections - activeConnections,
            connection_rate: Math.floor(Math.random() * 50) + 10,
            max_connections: 5000,
            utilization: (totalConnections / 5000) * 100
        };
    }
    
    /**
     * 获取消息统计数据
     */
    getMessageStatsData() {
        return {
            messages_sent: Math.floor(Math.random() * 10000) + 5000,
            messages_received: Math.floor(Math.random() * 10000) + 5000,
            messages_queued: Math.floor(Math.random() * 100),
            message_rate: Math.floor(Math.random() * 500) + 200,
            failed_messages: Math.floor(Math.random() * 50),
            success_rate: 95 + Math.random() * 5
        };
    }
    
    /**
     * 获取资源使用数据
     */
    getResourceUsageData() {
        return {
            cpu_usage: Math.random() * 100,
            memory_usage: 60 + Math.random() * 30,
            disk_usage: 40 + Math.random() * 20,
            network_in: Math.random() * 1000,
            network_out: Math.random() * 1000,
            load_average: [
                Math.random() * 2,
                Math.random() * 2,
                Math.random() * 2
            ]
        };
    }
    
    /**
     * 获取告警摘要数据
     */
    getAlertSummaryData() {
        const alerts = [
            { id: '1', severity: 'high', message: 'CPU使用率过高', time: Date.now() - 300000 },
            { id: '2', severity: 'medium', message: '连接数接近上限', time: Date.now() - 600000 },
            { id: '3', severity: 'low', message: '磁盘空间不足', time: Date.now() - 900000 }
        ];
        
        return {
            active_alerts: alerts.length,
            critical_alerts: alerts.filter(a => a.severity === 'critical').length,
            high_alerts: alerts.filter(a => a.severity === 'high').length,
            medium_alerts: alerts.filter(a => a.severity === 'medium').length,
            low_alerts: alerts.filter(a => a.severity === 'low').length,
            recent_alerts: alerts,
            alert_trends: this.calculateAlertTrends()
        };
    }
    
    /**
     * 获取成本追踪数据
     */
    getCostTrackingData() {
        const dailyCosts = [];
        const now = Date.now();
        
        for (let i = 0; i < 30; i++) {
            const date = new Date(now - (29 - i) * 86400000);
            dailyCosts.push({
                date: date.toISOString().split('T')[0],
                cost: 8 + Math.random() * 4,
                budget: 10
            });
        }
        
        const totalCost = dailyCosts.reduce((sum, day) => sum + day.cost, 0);
        
        return {
            daily_cost: dailyCosts[dailyCosts.length - 1].cost,
            monthly_cost: totalCost,
            monthly_budget: 300,
            cost_trend: dailyCosts,
            utilization: (totalCost / 300) * 100,
            projected_cost: totalCost * (30 / dailyCosts.length)
        };
    }
    
    /**
     * 获取用户活动数据
     */
    getUserActivityData() {
        const hours = [];
        const now = new Date();
        
        for (let i = 0; i < 24; i++) {
            hours.push({
                hour: i,
                active_users: Math.floor(Math.random() * 1000) + 100,
                new_users: Math.floor(Math.random() * 50),
                messages: Math.floor(Math.random() * 5000) + 1000
            });
        }
        
        return {
            current_active: hours[now.getHours()].active_users,
            peak_active: Math.max(...hours.map(h => h.active_users)),
            total_new: hours.reduce((sum, h) => sum + h.new_users, 0),
            hourly_activity: hours,
            user_retention: 85 + Math.random() * 10
        };
    }
    
    /**
     * 计算趋势
     */
    calculateTrends(dataPoints) {
        if (dataPoints.length < 2) return {};
        
        const metrics = Object.keys(dataPoints[0]).filter(key => key !== 'timestamp');
        const trends = {};
        
        metrics.forEach(metric => {
            const values = dataPoints.map(point => point[metric]);
            const first = values[0];
            const last = values[values.length - 1];
            const change = ((last - first) / first) * 100;
            
            trends[metric] = {
                direction: change > 5 ? 'up' : change < -5 ? 'down' : 'stable',
                change: change.toFixed(2),
                trend: this.calculateLinearTrend(values)
            };
        });
        
        return trends;
    }
    
    /**
     * 计算线性趋势
     */
    calculateLinearTrend(values) {
        const n = values.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = values.reduce((sum, val) => sum + val, 0);
        const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0);
        const sumXX = (n * (n - 1) * (2 * n - 1)) / 6;
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        return slope;
    }
    
    /**
     * 计算告警趋势
     */
    calculateAlertTrends() {
        // 模拟告警趋势数据
        const trends = [];
        const now = Date.now();
        
        for (let i = 0; i < 7; i++) {
            const date = new Date(now - (6 - i) * 86400000);
            trends.push({
                date: date.toISOString().split('T')[0],
                critical: Math.floor(Math.random() * 3),
                high: Math.floor(Math.random() * 5),
                medium: Math.floor(Math.random() * 10),
                low: Math.floor(Math.random() * 15)
            });
        }
        
        return trends;
    }
    
    /**
     * 更新系统健康度
     */
    updateSystemHealth() {
        let health = 100;
        
        // 根据各种指标计算健康度
        const resourceData = this.getResourceUsageData();
        const performanceData = this.getPerformanceMetricsData();
        const alertData = this.getAlertSummaryData();
        
        // CPU使用率影响
        if (resourceData.cpu_usage > 80) health -= 15;
        else if (resourceData.cpu_usage > 60) health -= 5;
        
        // 内存使用率影响
        if (resourceData.memory_usage > 85) health -= 15;
        else if (resourceData.memory_usage > 70) health -= 5;
        
        // 响应时间影响
        if (performanceData.current.response_time > 200) health -= 10;
        else if (performanceData.current.response_time > 100) health -= 5;
        
        // 错误率影响
        if (performanceData.current.error_rate > 5) health -= 20;
        else if (performanceData.current.error_rate > 2) health -= 10;
        
        // 告警影响
        health -= alertData.critical_alerts * 10;
        health -= alertData.high_alerts * 5;
        health -= alertData.medium_alerts * 2;
        
        this.dashboardState.systemHealth = Math.max(0, health);
    }
    
    /**
     * 广播更新给客户端
     */
    broadcastUpdate() {
        const updateData = {
            timestamp: Date.now(),
            systemHealth: this.dashboardState.systemHealth,
            widgets: Object.fromEntries(this.dashboardState.widgets),
            stats: this.stats
        };
        
        this.emit('dashboardUpdate', updateData);
        
        // 模拟WebSocket广播
        this.dashboardState.connectedClients.forEach(clientId => {
            console.log(`广播更新到客户端: ${clientId}`);
        });
    }
    
    /**
     * 执行数据聚合
     */
    performDataAggregation() {
        const now = Date.now();
        
        for (const [metricName, aggregator] of this.aggregators) {
            this.aggregateMetricData(aggregator, now);
        }
    }
    
    /**
     * 聚合指标数据
     */
    aggregateMetricData(aggregator, now) {
        const intervals = this.config.aggregation.intervals;
        
        // 分钟级聚合
        if (now - aggregator.lastAggregation.minute >= intervals.minute) {
            this.aggregateInterval(aggregator, 'minute', intervals.minute, now);
            aggregator.lastAggregation.minute = now;
        }
        
        // 小时级聚合
        if (now - aggregator.lastAggregation.hour >= intervals.hour) {
            this.aggregateInterval(aggregator, 'hour', intervals.hour, now);
            aggregator.lastAggregation.hour = now;
        }
        
        // 日级聚合
        if (now - aggregator.lastAggregation.day >= intervals.day) {
            this.aggregateInterval(aggregator, 'day', intervals.day, now);
            aggregator.lastAggregation.day = now;
        }
    }
    
    /**
     * 聚合时间间隔数据
     */
    aggregateInterval(aggregator, interval, duration, now) {
        const startTime = now - duration;
        const relevantData = aggregator.rawData.filter(point => 
            point.timestamp >= startTime && point.timestamp < now
        );
        
        if (relevantData.length === 0) return;
        
        const aggregated = {
            timestamp: now,
            interval,
            count: relevantData.length,
            min: Math.min(...relevantData.map(d => d.value)),
            max: Math.max(...relevantData.map(d => d.value)),
            avg: relevantData.reduce((sum, d) => sum + d.value, 0) / relevantData.length,
            sum: relevantData.reduce((sum, d) => sum + d.value, 0)
        };
        
        aggregator.aggregatedData[interval].push(aggregated);
        
        // 保持数据量限制
        const maxPoints = { minute: 1440, hour: 168, day: 30, week: 52, month: 12 };
        if (aggregator.aggregatedData[interval].length > maxPoints[interval]) {
            aggregator.aggregatedData[interval].shift();
        }
    }
    
    /**
     * 检查计划报表
     */
    checkScheduledReports() {
        const now = new Date();
        
        // 检查是否需要生成日报
        if (this.shouldGenerateReport('daily', now)) {
            this.generateReport('daily_summary');
        }
        
        // 检查是否需要生成周报
        if (this.shouldGenerateReport('weekly', now)) {
            this.generateReport('weekly_performance');
        }
        
        // 检查是否需要生成月报
        if (this.shouldGenerateReport('monthly', now)) {
            this.generateReport('monthly_overview');
        }
    }
    
    /**
     * 检查是否应该生成报表
     */
    shouldGenerateReport(type, now) {
        // 简化的调度逻辑
        switch (type) {
            case 'daily':
                return now.getHours() === 0 && now.getMinutes() < 5;
            case 'weekly':
                return now.getDay() === 1 && now.getHours() === 0 && now.getMinutes() < 5;
            case 'monthly':
                return now.getDate() === 1 && now.getHours() === 0 && now.getMinutes() < 5;
            default:
                return false;
        }
    }
    
    /**
     * 生成报表
     */
    async generateReport(reportType, options = {}) {
        console.log(`开始生成报表: ${reportType}`);
        
        const reportId = crypto.randomUUID();
        const startTime = Date.now();
        
        try {
            let reportData;
            
            switch (reportType) {
                case 'daily_summary':
                    reportData = await this.generateDailySummaryReport();
                    break;
                case 'weekly_performance':
                    reportData = await this.generateWeeklyPerformanceReport();
                    break;
                case 'monthly_overview':
                    reportData = await this.generateMonthlyOverviewReport();
                    break;
                case 'cost_analysis':
                    reportData = await this.generateCostAnalysisReport();
                    break;
                case 'user_behavior':
                    reportData = await this.generateUserBehaviorReport();
                    break;
                case 'system_health':
                    reportData = await this.generateSystemHealthReport();
                    break;
                default:
                    throw new Error(`未知的报表类型: ${reportType}`);
            }
            
            const report = {
                id: reportId,
                type: reportType,
                generatedAt: Date.now(),
                generationTime: Date.now() - startTime,
                data: reportData,
                format: options.format || 'json',
                size: JSON.stringify(reportData).length
            };
            
            // 缓存报表
            this.reportCache.set(reportId, report);
            
            // 更新统计
            this.stats.reportsGenerated++;
            
            // 触发事件
            this.emit('reportGenerated', report);
            
            console.log(`报表生成完成: ${reportType} (${report.generationTime}ms)`);
            
            return report;
            
        } catch (error) {
            console.error(`报表生成失败: ${reportType}`, error);
            throw error;
        }
    }
    
    /**
     * 生成日报
     */
    async generateDailySummaryReport() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        
        return {
            date: yesterday.toISOString().split('T')[0],
            summary: {
                totalConnections: Math.floor(Math.random() * 5000) + 2000,
                totalMessages: Math.floor(Math.random() * 100000) + 50000,
                avgResponseTime: 80 + Math.random() * 40,
                errorRate: Math.random() * 3,
                uptime: 99.9,
                cost: 8 + Math.random() * 4
            },
            performance: {
                peakConnections: Math.floor(Math.random() * 1000) + 4000,
                peakTime: '20:30',
                slowestEndpoint: '/api/messages',
                slowestResponseTime: 250 + Math.random() * 100
            },
            alerts: {
                total: Math.floor(Math.random() * 10),
                critical: Math.floor(Math.random() * 2),
                resolved: Math.floor(Math.random() * 8)
            },
            recommendations: [
                '考虑在高峰时段增加服务器资源',
                '优化消息处理算法以提高性能',
                '监控磁盘空间使用情况'
            ]
        };
    }
    
    /**
     * 生成周报
     */
    async generateWeeklyPerformanceReport() {
        return {
            week: this.getWeekNumber(new Date()),
            year: new Date().getFullYear(),
            performance: {
                avgResponseTime: 85 + Math.random() * 30,
                avgThroughput: 850 + Math.random() * 300,
                avgErrorRate: Math.random() * 2,
                uptime: 99.8 + Math.random() * 0.2
            },
            trends: {
                responseTime: 'improving',
                throughput: 'stable',
                errorRate: 'improving',
                userGrowth: 'increasing'
            },
            topIssues: [
                { issue: '高峰时段响应延迟', frequency: 15, impact: 'medium' },
                { issue: '内存使用率偏高', frequency: 8, impact: 'low' },
                { issue: '连接超时', frequency: 5, impact: 'high' }
            ]
        };
    }
    
    /**
     * 生成月报
     */
    async generateMonthlyOverviewReport() {
        return {
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
            overview: {
                totalUsers: Math.floor(Math.random() * 10000) + 50000,
                newUsers: Math.floor(Math.random() * 5000) + 2000,
                totalMessages: Math.floor(Math.random() * 1000000) + 2000000,
                totalCost: 240 + Math.random() * 60,
                budgetUtilization: 80 + Math.random() * 15
            },
            growth: {
                userGrowth: 15 + Math.random() * 10,
                messageGrowth: 25 + Math.random() * 15,
                costGrowth: 5 + Math.random() * 10
            },
            achievements: [
                '系统稳定性达到99.9%',
                '响应时间优化20%',
                '成本控制在预算范围内'
            ]
        };
    }
    
    /**
     * 获取周数
     */
    getWeekNumber(date) {
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    }
    
    /**
     * 清理数据
     */
    cleanupData() {
        const now = Date.now();
        
        // 清理报表缓存
        for (const [reportId, report] of this.reportCache) {
            if (now - report.generatedAt > 86400000 * 7) { // 7天
                this.reportCache.delete(reportId);
            }
        }
        
        // 清理聚合器原始数据
        for (const [metricName, aggregator] of this.aggregators) {
            aggregator.rawData = aggregator.rawData.filter(point => 
                now - point.timestamp < 86400000 // 保留1天原始数据
            );
        }
        
        console.log('数据清理完成');
    }
    
    /**
     * 添加客户端连接
     */
    addClient(clientId) {
        this.dashboardState.connectedClients.add(clientId);
        this.stats.dashboardViews++;
        this.emit('clientConnected', { clientId, totalClients: this.dashboardState.connectedClients.size });
    }
    
    /**
     * 移除客户端连接
     */
    removeClient(clientId) {
        this.dashboardState.connectedClients.delete(clientId);
        this.emit('clientDisconnected', { clientId, totalClients: this.dashboardState.connectedClients.size });
    }
    
    /**
     * 获取仪表盘数据
     */
    getDashboardData() {
        this.stats.apiCalls++;
        return {
            timestamp: this.dashboardState.lastUpdate,
            systemHealth: this.dashboardState.systemHealth,
            widgets: Object.fromEntries(this.dashboardState.widgets),
            connectedClients: this.dashboardState.connectedClients.size
        };
    }
    
    /**
     * 获取报表列表
     */
    getReports(filters = {}) {
        let reports = Array.from(this.reportCache.values());
        
        if (filters.type) {
            reports = reports.filter(report => report.type === filters.type);
        }
        
        if (filters.startDate) {
            reports = reports.filter(report => report.generatedAt >= new Date(filters.startDate).getTime());
        }
        
        if (filters.endDate) {
            reports = reports.filter(report => report.generatedAt <= new Date(filters.endDate).getTime());
        }
        
        return reports.sort((a, b) => b.generatedAt - a.generatedAt);
    }
    
    /**
     * 获取报表详情
     */
    getReport(reportId) {
        return this.reportCache.get(reportId);
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            connectedClients: this.dashboardState.connectedClients.size,
            cachedReports: this.reportCache.size,
            systemHealth: this.dashboardState.systemHealth,
            uptime: process.uptime() * 1000
        };
    }
    
    /**
     * 关闭仪表盘系统
     */
    close() {
        console.log('关闭运维仪表盘与报表系统...');
        
        // 清理定时器
        Object.values(this.timers).forEach(timer => {
            if (timer) {
                clearInterval(timer);
            }
        });
        
        // 清理数据
        this.dataStore.realtime.clear();
        this.dataStore.hourly.clear();
        this.dataStore.daily.clear();
        this.dataStore.monthly.clear();
        this.reportCache.clear();
        this.aggregators.clear();
        this.dashboardState.connectedClients.clear();
        this.dashboardState.widgets.clear();
        
        console.log('仪表盘系统已关闭');
    }
}

module.exports = DashboardSystem;
