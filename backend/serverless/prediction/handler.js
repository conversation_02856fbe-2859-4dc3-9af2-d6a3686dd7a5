/**
 * 预测游戏处理器
 * 处理围观者预测答案、积分计算、排行榜等功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { PredictionService } = require('./predictionService');
const { logger } = require('../utils/logger');

class PredictionHandler {
  constructor() {
    this.predictionService = new PredictionService();
  }

  /**
   * 创建预测游戏
   */
  async createPredictionGame(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'createPredictionGame'
    });

    try {
      const { roomId, questionId, questionText, options } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!roomId || !questionId || !questionText || !options) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID, question ID, question text, and options are required'
          })
        };
      }

      const game = await this.predictionService.createPredictionGame({
        roomId,
        questionId,
        questionText,
        options,
        creatorId: userId
      });

      predictionLogger.info('Prediction game created', {
        gameId: game.id,
        roomId,
        questionId
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: game
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to create prediction game', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to create prediction game'
        })
      };
    }
  }

  /**
   * 提交预测
   */
  async submitPrediction(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'submitPrediction'
    });

    try {
      const { gameId } = event.pathParameters;
      const { predictedAnswer, confidenceLevel } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!gameId || !predictedAnswer) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Game ID and predicted answer are required'
          })
        };
      }

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const prediction = await this.predictionService.submitPrediction({
        gameId,
        userId,
        predictedAnswer,
        confidenceLevel: confidenceLevel || 50
      });

      predictionLogger.info('Prediction submitted', {
        gameId,
        userId,
        predictedAnswer,
        confidenceLevel
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: prediction
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to submit prediction', {
        error: error.message
      });

      const statusCode = error.message.includes('Game not active') ? 409 :
                        error.message.includes('already submitted') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 获取预测游戏信息
   */
  async getPredictionGame(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'getPredictionGame'
    });

    try {
      const { gameId } = event.pathParameters;

      if (!gameId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Game ID is required'
          })
        };
      }

      const game = await this.predictionService.getPredictionGame(gameId);

      if (!game) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Prediction game not found'
          })
        };
      }

      predictionLogger.debug('Prediction game retrieved', {
        gameId,
        status: game.status
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: game
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to get prediction game', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get prediction game'
        })
      };
    }
  }

  /**
   * 获取房间的预测游戏列表
   */
  async getRoomPredictionGames(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'getRoomPredictionGames'
    });

    try {
      const { roomId } = event.pathParameters;
      const {
        page = 1,
        limit = 20,
        status = 'all'
      } = event.queryStringParameters || {};

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const games = await this.predictionService.getRoomPredictionGames({
        roomId,
        page: parseInt(page),
        limit: parseInt(limit),
        status
      });

      predictionLogger.debug('Room prediction games retrieved', {
        roomId,
        gameCount: games.games.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: games
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to get room prediction games', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get room prediction games'
        })
      };
    }
  }

  /**
   * 结算预测游戏
   */
  async settlePredictionGame(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'settlePredictionGame'
    });

    try {
      const { gameId } = event.pathParameters;
      const { correctAnswer } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!gameId || !correctAnswer) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Game ID and correct answer are required'
          })
        };
      }

      const result = await this.predictionService.settlePredictionGame({
        gameId,
        correctAnswer,
        settledBy: userId
      });

      predictionLogger.info('Prediction game settled', {
        gameId,
        correctAnswer,
        winnersCount: result.winnersCount,
        totalPoints: result.totalPointsAwarded
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to settle prediction game', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 :
                        error.message.includes('already settled') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 获取预测统计
   */
  async getPredictionStats(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'getPredictionStats'
    });

    try {
      const { gameId } = event.pathParameters;

      if (!gameId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Game ID is required'
          })
        };
      }

      const stats = await this.predictionService.getPredictionStats(gameId);

      if (!stats) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Prediction game not found'
          })
        };
      }

      predictionLogger.debug('Prediction stats retrieved', {
        gameId,
        totalPredictions: stats.totalPredictions
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to get prediction stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get prediction stats'
        })
      };
    }
  }

  /**
   * 获取用户预测历史
   */
  async getUserPredictionHistory(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'getUserPredictionHistory'
    });

    try {
      const userId = event.user?.id;
      const {
        page = 1,
        limit = 20,
        roomId
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const history = await this.predictionService.getUserPredictionHistory({
        userId,
        page: parseInt(page),
        limit: parseInt(limit),
        roomId
      });

      predictionLogger.debug('User prediction history retrieved', {
        userId,
        predictionCount: history.predictions.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: history
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to get user prediction history', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get user prediction history'
        })
      };
    }
  }

  /**
   * 获取预测排行榜
   */
  async getPredictionLeaderboard(event, context) {
    const predictionLogger = logger.child({
      requestId: context.requestId,
      action: 'getPredictionLeaderboard'
    });

    try {
      const {
        roomId,
        timeRange = '24h',
        limit = 50
      } = event.queryStringParameters || {};

      const leaderboard = await this.predictionService.getPredictionLeaderboard({
        roomId,
        timeRange,
        limit: parseInt(limit)
      });

      predictionLogger.debug('Prediction leaderboard retrieved', {
        roomId,
        timeRange,
        userCount: leaderboard.users.length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: leaderboard
        })
      };

    } catch (error) {
      predictionLogger.error('Failed to get prediction leaderboard', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get prediction leaderboard'
        })
      };
    }
  }
}

// 创建处理器实例
const predictionHandler = new PredictionHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  createPredictionGame: withMiddleware(predictionHandler.createPredictionGame.bind(predictionHandler)),
  submitPrediction: withMiddleware(predictionHandler.submitPrediction.bind(predictionHandler)),
  getPredictionGame: withMiddleware(predictionHandler.getPredictionGame.bind(predictionHandler)),
  getRoomPredictionGames: withMiddleware(predictionHandler.getRoomPredictionGames.bind(predictionHandler)),
  settlePredictionGame: withMiddleware(predictionHandler.settlePredictionGame.bind(predictionHandler)),
  getPredictionStats: withMiddleware(predictionHandler.getPredictionStats.bind(predictionHandler)),
  getUserPredictionHistory: withMiddleware(predictionHandler.getUserPredictionHistory.bind(predictionHandler)),
  getPredictionLeaderboard: withMiddleware(predictionHandler.getPredictionLeaderboard.bind(predictionHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'POST /v1/prediction/games': predictionHandler.createPredictionGame.bind(predictionHandler),
      'GET /v1/prediction/games/{gameId}': predictionHandler.getPredictionGame.bind(predictionHandler),
      'POST /v1/prediction/games/{gameId}/predictions': predictionHandler.submitPrediction.bind(predictionHandler),
      'POST /v1/prediction/games/{gameId}/settle': predictionHandler.settlePredictionGame.bind(predictionHandler),
      'GET /v1/prediction/games/{gameId}/stats': predictionHandler.getPredictionStats.bind(predictionHandler),
      'GET /v1/prediction/rooms/{roomId}/games': predictionHandler.getRoomPredictionGames.bind(predictionHandler),
      'GET /v1/prediction/users/history': predictionHandler.getUserPredictionHistory.bind(predictionHandler),
      'GET /v1/prediction/leaderboard': predictionHandler.getPredictionLeaderboard.bind(predictionHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
