/**
 * 弹幕系统处理器
 * 处理弹幕发送、接收、过滤等功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { DanmakuService } = require('./danmakuService');
const { logger } = require('../utils/logger');

class DanmakuHandler {
  constructor() {
    this.danmakuService = new DanmakuService();
  }

  /**
   * 发送弹幕
   */
  async sendDanmaku(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'sendDanmaku'
    });

    try {
      const { roomId, content, color, positionType, fontSize } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!roomId || !content) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID and content are required'
          })
        };
      }

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const result = await this.danmakuService.sendDanmaku({
        roomId,
        userId,
        content,
        color: color || '#FFFFFF',
        positionType: positionType || 'scroll',
        fontSize: fontSize || 'medium'
      });

      danmakuLogger.info('Danmaku sent', {
        roomId,
        userId,
        messageId: result.id,
        contentLength: content.length
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to send danmaku', {
        error: error.message
      });

      const statusCode = error.message.includes('Rate limit') ? 429 :
                        error.message.includes('blocked') ? 403 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 获取弹幕历史
   */
  async getDanmakuHistory(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'getDanmakuHistory'
    });

    try {
      const { roomId } = event.pathParameters;
      const {
        page = 1,
        limit = 50,
        since,
        until
      } = event.queryStringParameters || {};

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const history = await this.danmakuService.getDanmakuHistory({
        roomId,
        page: parseInt(page),
        limit: parseInt(limit),
        since,
        until
      });

      danmakuLogger.debug('Danmaku history retrieved', {
        roomId,
        messageCount: history.messages.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: history
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to get danmaku history', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get danmaku history'
        })
      };
    }
  }

  /**
   * 删除弹幕
   */
  async deleteDanmaku(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'deleteDanmaku'
    });

    try {
      const { messageId } = event.pathParameters;
      const userId = event.user?.id;

      if (!messageId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Message ID is required'
          })
        };
      }

      await this.danmakuService.deleteDanmaku(messageId, userId);

      danmakuLogger.info('Danmaku deleted', {
        messageId,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Danmaku deleted successfully'
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to delete danmaku', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 :
                        error.message.includes('not found') ? 404 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 举报弹幕
   */
  async reportDanmaku(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'reportDanmaku'
    });

    try {
      const { messageId } = event.pathParameters;
      const { reason } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!messageId || !reason) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Message ID and reason are required'
          })
        };
      }

      await this.danmakuService.reportDanmaku(messageId, userId, reason);

      danmakuLogger.info('Danmaku reported', {
        messageId,
        reporterId: userId,
        reason
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Report submitted successfully'
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to report danmaku', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to submit report'
        })
      };
    }
  }

  /**
   * 获取弹幕统计
   */
  async getDanmakuStats(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'getDanmakuStats'
    });

    try {
      const { roomId } = event.pathParameters;
      const { timeRange = '1h' } = event.queryStringParameters || {};

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const stats = await this.danmakuService.getDanmakuStats(roomId, timeRange);

      danmakuLogger.debug('Danmaku stats retrieved', {
        roomId,
        timeRange,
        totalMessages: stats.totalMessages
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to get danmaku stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get danmaku stats'
        })
      };
    }
  }

  /**
   * 管理弹幕过滤规则
   */
  async manageFilterRules(event, context) {
    const danmakuLogger = logger.child({
      requestId: context.requestId,
      action: 'manageFilterRules'
    });

    try {
      const method = event.httpMethod;
      const userId = event.user?.id;

      // 检查管理员权限
      if (!await this.danmakuService.isAdmin(userId)) {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Admin permission required'
          })
        };
      }

      let result;
      switch (method) {
        case 'GET':
          result = await this.danmakuService.getFilterRules();
          break;
        case 'POST':
          const newRule = JSON.parse(event.body || '{}');
          result = await this.danmakuService.addFilterRule(newRule);
          break;
        case 'PUT':
          const { ruleId } = event.pathParameters;
          const updates = JSON.parse(event.body || '{}');
          result = await this.danmakuService.updateFilterRule(ruleId, updates);
          break;
        case 'DELETE':
          const { ruleId: deleteRuleId } = event.pathParameters;
          await this.danmakuService.deleteFilterRule(deleteRuleId);
          result = { message: 'Rule deleted successfully' };
          break;
        default:
          return {
            statusCode: 405,
            body: JSON.stringify({
              error: 'Method not allowed'
            })
          };
      }

      danmakuLogger.info('Filter rules managed', {
        method,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      danmakuLogger.error('Failed to manage filter rules', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to manage filter rules'
        })
      };
    }
  }
}

// 创建处理器实例
const danmakuHandler = new DanmakuHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  sendDanmaku: withMiddleware(danmakuHandler.sendDanmaku.bind(danmakuHandler)),
  getDanmakuHistory: withMiddleware(danmakuHandler.getDanmakuHistory.bind(danmakuHandler)),
  deleteDanmaku: withMiddleware(danmakuHandler.deleteDanmaku.bind(danmakuHandler)),
  reportDanmaku: withMiddleware(danmakuHandler.reportDanmaku.bind(danmakuHandler)),
  getDanmakuStats: withMiddleware(danmakuHandler.getDanmakuStats.bind(danmakuHandler)),
  manageFilterRules: withMiddleware(danmakuHandler.manageFilterRules.bind(danmakuHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'POST /v1/danmaku/send': danmakuHandler.sendDanmaku.bind(danmakuHandler),
      'GET /v1/danmaku/rooms/{roomId}/history': danmakuHandler.getDanmakuHistory.bind(danmakuHandler),
      'DELETE /v1/danmaku/messages/{messageId}': danmakuHandler.deleteDanmaku.bind(danmakuHandler),
      'POST /v1/danmaku/messages/{messageId}/report': danmakuHandler.reportDanmaku.bind(danmakuHandler),
      'GET /v1/danmaku/rooms/{roomId}/stats': danmakuHandler.getDanmakuStats.bind(danmakuHandler),
      'GET /v1/danmaku/filter-rules': danmakuHandler.manageFilterRules.bind(danmakuHandler),
      'POST /v1/danmaku/filter-rules': danmakuHandler.manageFilterRules.bind(danmakuHandler),
      'PUT /v1/danmaku/filter-rules/{ruleId}': danmakuHandler.manageFilterRules.bind(danmakuHandler),
      'DELETE /v1/danmaku/filter-rules/{ruleId}': danmakuHandler.manageFilterRules.bind(danmakuHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
