/**
 * 弹幕系统服务
 * 处理弹幕的业务逻辑、过滤、存储等
 */

const { DatabaseManager } = require('../utils/database');
const { RedisManager } = require('../utils/redis');
const { logger } = require('../utils/logger');
const { RoomManager } = require('../websocket/roomManager');

class DanmakuService {
  constructor() {
    this.db = DatabaseManager.getInstance();
    this.redis = RedisManager.getInstance();
    this.roomManager = new RoomManager();
    this.rateLimitPrefix = 'danmaku:rate:';
    this.filterCachePrefix = 'danmaku:filter:';
  }

  /**
   * 发送弹幕
   */
  async sendDanmaku({ roomId, userId, content, color, positionType, fontSize }) {
    try {
      // 检查发送频率限制
      await this.checkRateLimit(userId, roomId);

      // 内容过滤
      const filteredContent = await this.filterContent(content);
      if (!filteredContent) {
        throw new Error('Message content is blocked');
      }

      // 检查房间是否允许弹幕
      const room = await this.roomManager.getRoom(roomId);
      if (!room || !room.settings.allowDanmaku) {
        throw new Error('Danmaku is not allowed in this room');
      }

      const connection = await this.db.getConnection();
      try {
        await connection.beginTransaction();

        // 插入弹幕记录
        const [result] = await connection.execute(`
          INSERT INTO danmaku_messages (
            room_id, user_id, content, color, position_type, font_size, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, [roomId, userId, filteredContent, color, positionType, fontSize]);

        const messageId = result.insertId;

        // 获取用户信息
        const [users] = await connection.execute(
          'SELECT nickname, avatar_url FROM users WHERE id = ?',
          [userId]
        );

        const user = users[0] || {};

        await connection.commit();

        // 构建弹幕消息
        const danmakuMessage = {
          id: messageId,
          roomId,
          userId,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          content: filteredContent,
          color,
          positionType,
          fontSize,
          timestamp: new Date().toISOString()
        };

        // 广播弹幕到房间
        await this.roomManager.broadcastToRoom(roomId, {
          type: 'danmaku:message',
          data: danmakuMessage
        });

        // 更新发送频率记录
        await this.updateRateLimit(userId, roomId);

        logger.info('Danmaku sent successfully', {
          messageId,
          roomId,
          userId,
          contentLength: filteredContent.length
        });

        return danmakuMessage;

      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }

    } catch (error) {
      logger.error('Failed to send danmaku', {
        roomId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取弹幕历史
   */
  async getDanmakuHistory({ roomId, page = 1, limit = 50, since, until }) {
    const connection = await this.db.getConnection();
    
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'dm.room_id = ? AND dm.status = "normal"';
      let params = [roomId];

      if (since) {
        whereClause += ' AND dm.created_at >= ?';
        params.push(since);
      }

      if (until) {
        whereClause += ' AND dm.created_at <= ?';
        params.push(until);
      }

      // 获取总数
      const [countResult] = await connection.execute(`
        SELECT COUNT(*) as total
        FROM danmaku_messages dm
        WHERE ${whereClause}
      `, params);

      const total = countResult[0].total;

      // 获取弹幕列表
      const [messages] = await connection.execute(`
        SELECT 
          dm.*,
          u.nickname,
          u.avatar_url
        FROM danmaku_messages dm
        LEFT JOIN users u ON dm.user_id = u.id
        WHERE ${whereClause}
        ORDER BY dm.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, limit, offset]);

      return {
        messages: messages.map(msg => ({
          id: msg.id,
          roomId: msg.room_id,
          userId: msg.user_id,
          nickname: msg.nickname,
          avatarUrl: msg.avatar_url,
          content: msg.content,
          color: msg.color,
          positionType: msg.position_type,
          fontSize: msg.font_size,
          timestamp: msg.created_at
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      logger.error('Failed to get danmaku history', {
        roomId,
        page,
        limit,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 删除弹幕
   */
  async deleteDanmaku(messageId, userId) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.beginTransaction();

      // 检查权限
      const [messages] = await connection.execute(
        'SELECT user_id, room_id FROM danmaku_messages WHERE id = ?',
        [messageId]
      );

      if (!messages.length) {
        throw new Error('Message not found');
      }

      const message = messages[0];
      const isOwner = message.user_id === userId;
      const isAdmin = await this.isAdmin(userId);
      const isModerator = await this.isModerator(userId, message.room_id);

      if (!isOwner && !isAdmin && !isModerator) {
        throw new Error('Permission denied');
      }

      // 标记为删除
      await connection.execute(
        'UPDATE danmaku_messages SET status = "deleted" WHERE id = ?',
        [messageId]
      );

      await connection.commit();

      // 广播删除消息
      await this.roomManager.broadcastToRoom(message.room_id, {
        type: 'danmaku:deleted',
        data: { messageId }
      });

      logger.info('Danmaku deleted', {
        messageId,
        deletedBy: userId,
        originalSender: message.user_id
      });

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to delete danmaku', {
        messageId,
        userId,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 举报弹幕
   */
  async reportDanmaku(messageId, reporterId, reason) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.beginTransaction();

      // 检查消息是否存在
      const [messages] = await connection.execute(
        'SELECT id, user_id, room_id FROM danmaku_messages WHERE id = ?',
        [messageId]
      );

      if (!messages.length) {
        throw new Error('Message not found');
      }

      // 插入举报记录
      await connection.execute(`
        INSERT INTO danmaku_reports (
          message_id, reporter_id, reason, created_at
        ) VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        reason = VALUES(reason), created_at = NOW()
      `, [messageId, reporterId, reason]);

      await connection.commit();

      logger.info('Danmaku reported', {
        messageId,
        reporterId,
        reason
      });

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to report danmaku', {
        messageId,
        reporterId,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取弹幕统计
   */
  async getDanmakuStats(roomId, timeRange = '1h') {
    const connection = await this.db.getConnection();
    
    try {
      const timeCondition = this.getTimeCondition(timeRange);
      
      const [stats] = await connection.execute(`
        SELECT 
          COUNT(*) as totalMessages,
          COUNT(DISTINCT user_id) as uniqueSenders,
          AVG(CHAR_LENGTH(content)) as avgLength,
          COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blockedMessages
        FROM danmaku_messages
        WHERE room_id = ? AND created_at >= ${timeCondition}
      `, [roomId]);

      // 获取热门词汇
      const [words] = await connection.execute(`
        SELECT 
          SUBSTRING_INDEX(SUBSTRING_INDEX(content, ' ', numbers.n), ' ', -1) as word,
          COUNT(*) as frequency
        FROM danmaku_messages
        JOIN (
          SELECT 1 n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5
        ) numbers ON CHAR_LENGTH(content) - CHAR_LENGTH(REPLACE(content, ' ', '')) >= numbers.n - 1
        WHERE room_id = ? AND created_at >= ${timeCondition} AND status = 'normal'
        GROUP BY word
        HAVING CHAR_LENGTH(word) > 1
        ORDER BY frequency DESC
        LIMIT 10
      `, [roomId]);

      // 获取时间分布
      const [timeDistribution] = await connection.execute(`
        SELECT 
          DATE_FORMAT(created_at, '%H:%i') as time_slot,
          COUNT(*) as message_count
        FROM danmaku_messages
        WHERE room_id = ? AND created_at >= ${timeCondition}
        GROUP BY time_slot
        ORDER BY time_slot
      `, [roomId]);

      return {
        totalMessages: stats[0].totalMessages,
        uniqueSenders: stats[0].uniqueSenders,
        avgLength: Math.round(stats[0].avgLength || 0),
        blockedMessages: stats[0].blockedMessages,
        hotWords: words,
        timeDistribution,
        timeRange
      };

    } catch (error) {
      logger.error('Failed to get danmaku stats', {
        roomId,
        timeRange,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 内容过滤
   */
  async filterContent(content) {
    try {
      // 从缓存获取过滤规则
      let rules = await this.redis.get(`${this.filterCachePrefix}rules`);
      if (!rules) {
        rules = await this.loadFilterRules();
        await this.redis.setex(`${this.filterCachePrefix}rules`, 3600, JSON.stringify(rules));
      } else {
        rules = JSON.parse(rules);
      }

      let filteredContent = content;

      for (const rule of rules) {
        if (!rule.is_active) continue;

        switch (rule.rule_type) {
          case 'keyword':
            filteredContent = this.filterKeywords(filteredContent, rule);
            break;
          case 'regex':
            filteredContent = this.filterRegex(filteredContent, rule);
            break;
          case 'length':
            if (filteredContent.length > parseInt(rule.rule_content)) {
              if (rule.action === 'block') return null;
            }
            break;
        }

        if (!filteredContent) break;
      }

      return filteredContent;

    } catch (error) {
      logger.error('Failed to filter content', {
        content: content.substring(0, 50),
        error: error.message
      });
      return content; // 过滤失败时返回原内容
    }
  }

  /**
   * 关键词过滤
   */
  filterKeywords(content, rule) {
    const keywords = rule.rule_content.split(',');
    let filtered = content;

    for (const keyword of keywords) {
      const regex = new RegExp(keyword.trim(), 'gi');
      if (rule.action === 'block' && regex.test(filtered)) {
        return null;
      } else if (rule.action === 'replace') {
        const replacement = rule.replacement || '*'.repeat(keyword.length);
        filtered = filtered.replace(regex, replacement);
      }
    }

    return filtered;
  }

  /**
   * 正则表达式过滤
   */
  filterRegex(content, rule) {
    try {
      const regex = new RegExp(rule.rule_content, 'gi');
      if (rule.action === 'block' && regex.test(content)) {
        return null;
      } else if (rule.action === 'replace') {
        const replacement = rule.replacement || '***';
        return content.replace(regex, replacement);
      }
    } catch (error) {
      logger.warn('Invalid regex in filter rule', {
        ruleId: rule.id,
        regex: rule.rule_content
      });
    }
    return content;
  }

  /**
   * 加载过滤规则
   */
  async loadFilterRules() {
    const connection = await this.db.getConnection();
    try {
      const [rules] = await connection.execute(
        'SELECT * FROM danmaku_filter_rules WHERE is_active = TRUE ORDER BY priority DESC'
      );
      return rules;
    } finally {
      connection.release();
    }
  }

  /**
   * 检查发送频率限制
   */
  async checkRateLimit(userId, roomId) {
    const key = `${this.rateLimitPrefix}${userId}:${roomId}`;
    const now = Date.now();
    const windowSize = 60 * 1000; // 1分钟窗口
    const maxMessages = 10; // 每分钟最多10条

    const count = await this.redis.get(key);
    if (count && parseInt(count) >= maxMessages) {
      throw new Error('Rate limit exceeded: too many messages');
    }
  }

  /**
   * 更新发送频率记录
   */
  async updateRateLimit(userId, roomId) {
    const key = `${this.rateLimitPrefix}${userId}:${roomId}`;
    const current = await this.redis.get(key);
    
    if (current) {
      await this.redis.incr(key);
    } else {
      await this.redis.setex(key, 60, 1); // 1分钟过期
    }
  }

  /**
   * 检查是否为管理员
   */
  async isAdmin(userId) {
    const connection = await this.db.getConnection();
    try {
      const [users] = await connection.execute(
        'SELECT role FROM users WHERE id = ?',
        [userId]
      );
      return users.length > 0 && users[0].role === 'admin';
    } finally {
      connection.release();
    }
  }

  /**
   * 检查是否为房间管理员
   */
  async isModerator(userId, roomId) {
    const connection = await this.db.getConnection();
    try {
      const [rooms] = await connection.execute(
        'SELECT creator_id FROM spectator_rooms WHERE id = ?',
        [roomId]
      );
      return rooms.length > 0 && rooms[0].creator_id === userId;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取时间条件
   */
  getTimeCondition(timeRange) {
    const conditions = {
      '1h': 'NOW() - INTERVAL 1 HOUR',
      '6h': 'NOW() - INTERVAL 6 HOUR',
      '24h': 'NOW() - INTERVAL 24 HOUR',
      '7d': 'NOW() - INTERVAL 7 DAY'
    };
    return conditions[timeRange] || conditions['1h'];
  }

  /**
   * 获取过滤规则
   */
  async getFilterRules() {
    return await this.loadFilterRules();
  }

  /**
   * 添加过滤规则
   */
  async addFilterRule(rule) {
    const connection = await this.db.getConnection();
    try {
      const [result] = await connection.execute(`
        INSERT INTO danmaku_filter_rules (
          rule_type, rule_content, action, replacement, priority
        ) VALUES (?, ?, ?, ?, ?)
      `, [
        rule.ruleType,
        rule.ruleContent,
        rule.action,
        rule.replacement || null,
        rule.priority || 0
      ]);

      // 清除缓存
      await this.redis.del(`${this.filterCachePrefix}rules`);

      return { id: result.insertId, ...rule };
    } finally {
      connection.release();
    }
  }

  /**
   * 更新过滤规则
   */
  async updateFilterRule(ruleId, updates) {
    const connection = await this.db.getConnection();
    try {
      const fields = [];
      const values = [];

      Object.entries(updates).forEach(([key, value]) => {
        fields.push(`${key} = ?`);
        values.push(value);
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      values.push(ruleId);

      await connection.execute(
        `UPDATE danmaku_filter_rules SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      // 清除缓存
      await this.redis.del(`${this.filterCachePrefix}rules`);

      return { id: ruleId, ...updates };
    } finally {
      connection.release();
    }
  }

  /**
   * 删除过滤规则
   */
  async deleteFilterRule(ruleId) {
    const connection = await this.db.getConnection();
    try {
      await connection.execute(
        'DELETE FROM danmaku_filter_rules WHERE id = ?',
        [ruleId]
      );

      // 清除缓存
      await this.redis.del(`${this.filterCachePrefix}rules`);
    } finally {
      connection.release();
    }
  }
}

module.exports = { DanmakuService };
