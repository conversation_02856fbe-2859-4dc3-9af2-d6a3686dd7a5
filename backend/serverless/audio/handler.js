/**
 * 音频资源服务处理器
 * 处理音频资源的获取、管理等API请求
 */

const { getCosService } = require('../services/CosService');
const { authMiddleware, optionalAuth } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const { errorHandler } = require('../middleware/error');
const { rateLimitMiddleware } = require('../middleware/rateLimit');
const { APIError } = require('../utils/errors');
const { RedisManager } = require('../utils/redis');

/**
 * 获取音频资源信息
 * GET /v1/audio/{resourceId}
 */
const getAudioResource = errorHandler(async (event, context) => {
  // 限流检查 (音频资源访问频率较高，使用较宽松的限流)
  const rateLimitResult = await rateLimitMiddleware('media')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  const resourceId = event.pathParameters?.resourceId;
  
  if (!resourceId) {
    throw new APIError('MISSING_RESOURCE_ID', '音频资源ID不能为空', null, 400);
  }

  // 验证resourceId格式
  if (!/^[a-zA-Z0-9_-]+$/.test(resourceId)) {
    throw new APIError('INVALID_RESOURCE_ID', '音频资源ID格式无效', null, 400);
  }

  try {
    const cosService = getCosService();
    const redis = RedisManager.getInstance();
    
    // 尝试从缓存获取
    const cacheKey = `audio:resource:${resourceId}`;
    const cachedResource = await redis.get(cacheKey);
    
    if (cachedResource) {
      const resource = JSON.parse(cachedResource);
      
      // 检查缓存是否过期 (24小时)
      const cacheTime = new Date(resource.cachedAt);
      const now = new Date();
      const hoursDiff = (now - cacheTime) / (1000 * 60 * 60);
      
      if (hoursDiff < 24) {
        return {
          resource: resource,
          cached: true,
          cacheAge: Math.round(hoursDiff * 100) / 100
        };
      }
    }

    // 从COS获取资源信息
    const resource = await cosService.getAudioResource(resourceId);
    
    // 缓存结果 (24小时)
    const cacheData = {
      ...resource,
      cachedAt: new Date().toISOString()
    };
    await redis.setex(cacheKey, 24 * 60 * 60, JSON.stringify(cacheData));

    return {
      resource: resource,
      cached: false
    };

  } catch (error) {
    console.error('Get audio resource error:', error);
    
    if (error.message === 'AUDIO_NOT_FOUND') {
      throw new APIError('AUDIO_NOT_FOUND', '音频资源不存在', null, 404);
    } else if (error.message === 'AUDIO_ACCESS_FAILED') {
      throw new APIError('AUDIO_ACCESS_FAILED', '音频资源访问失败', null, 500);
    }
    
    throw new APIError('GET_AUDIO_FAILED', '获取音频资源失败', null, 500);
  }
});

/**
 * 获取音频资源列表
 * GET /v1/audio
 */
const getAudioResources = errorHandler(async (event, context) => {
  // 可选认证 (游客也可以访问部分资源)
  const authResult = await optionalAuth()(event, context);
  
  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('api')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const queryParams = event.queryStringParameters || {};
  const {
    category,
    limit = '50'
  } = queryParams;

  const resourceLimit = Math.min(Math.max(parseInt(limit), 1), 100);

  try {
    const cosService = getCosService();
    const redis = RedisManager.getInstance();
    
    // 构建缓存键
    const cacheKey = `audio:list:${category || 'all'}:${resourceLimit}`;
    const cachedList = await redis.get(cacheKey);
    
    if (cachedList) {
      const data = JSON.parse(cachedList);
      const cacheTime = new Date(data.cachedAt);
      const now = new Date();
      const minutesDiff = (now - cacheTime) / (1000 * 60);
      
      // 缓存1小时
      if (minutesDiff < 60) {
        return {
          resources: data.resources,
          total: data.total,
          category: category || null,
          cached: true,
          cacheAge: Math.round(minutesDiff * 100) / 100
        };
      }
    }

    // 从COS获取资源列表
    const resources = await cosService.listAudioResources(category, resourceLimit);

    // 缓存结果
    const cacheData = {
      resources: resources,
      total: resources.length,
      cachedAt: new Date().toISOString()
    };
    await redis.setex(cacheKey, 60 * 60, JSON.stringify(cacheData)); // 1小时缓存

    return {
      resources: resources,
      total: resources.length,
      category: category || null,
      cached: false
    };

  } catch (error) {
    console.error('Get audio resources error:', error);
    throw new APIError('GET_AUDIO_LIST_FAILED', '获取音频资源列表失败', null, 500);
  }
});

/**
 * 获取音频资源统计
 * GET /v1/audio/stats
 */
const getAudioStats = errorHandler(async (event, context) => {
  // 需要管理员权限
  const authResult = await authMiddleware(['admin:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  try {
    const cosService = getCosService();
    const redis = RedisManager.getInstance();
    
    // 尝试从缓存获取
    const cacheKey = 'audio:stats:global';
    const cachedStats = await redis.get(cacheKey);
    
    if (cachedStats) {
      const stats = JSON.parse(cachedStats);
      const cacheTime = new Date(stats.cachedAt);
      const now = new Date();
      const hoursDiff = (now - cacheTime) / (1000 * 60 * 60);
      
      // 缓存6小时
      if (hoursDiff < 6) {
        return {
          stats: stats,
          cached: true,
          cacheAge: Math.round(hoursDiff * 100) / 100
        };
      }
    }

    // 获取存储统计
    const stats = await cosService.getStorageStats();
    
    // 缓存结果
    const cacheData = {
      ...stats,
      cachedAt: new Date().toISOString()
    };
    await redis.setex(cacheKey, 6 * 60 * 60, JSON.stringify(cacheData)); // 6小时缓存

    return {
      stats: stats,
      cached: false
    };

  } catch (error) {
    console.error('Get audio stats error:', error);
    throw new APIError('GET_AUDIO_STATS_FAILED', '获取音频统计失败', null, 500);
  }
});

/**
 * 上传音频资源 (管理员功能)
 * POST /v1/audio/{resourceId}
 */
const uploadAudioResource = errorHandler(async (event, context) => {
  // 需要管理员权限
  const authResult = await authMiddleware(['admin:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const resourceId = event.pathParameters?.resourceId;
  
  if (!resourceId) {
    throw new APIError('MISSING_RESOURCE_ID', '音频资源ID不能为空', null, 400);
  }

  // 验证resourceId格式
  if (!/^[a-zA-Z0-9_-]+$/.test(resourceId)) {
    throw new APIError('INVALID_RESOURCE_ID', '音频资源ID格式无效', null, 400);
  }

  try {
    // 解析上传的文件数据
    const contentType = event.headers['content-type'] || event.headers['Content-Type'];
    
    if (!contentType || !contentType.startsWith('audio/')) {
      throw new APIError('INVALID_CONTENT_TYPE', '文件类型必须是音频格式', null, 400);
    }

    // 获取文件数据
    let audioBuffer;
    if (event.isBase64Encoded) {
      audioBuffer = Buffer.from(event.body, 'base64');
    } else {
      audioBuffer = Buffer.from(event.body, 'binary');
    }

    // 检查文件大小 (最大10MB)
    if (audioBuffer.length > 10 * 1024 * 1024) {
      throw new APIError('FILE_TOO_LARGE', '音频文件大小不能超过10MB', null, 400);
    }

    const cosService = getCosService();
    
    // 上传到COS
    const result = await cosService.uploadAudio(resourceId, audioBuffer, contentType);
    
    // 清除相关缓存
    const redis = RedisManager.getInstance();
    await redis.del(`audio:resource:${resourceId}`);
    
    // 清除列表缓存
    const category = resourceId.split('_')[0];
    await redis.del(`audio:list:${category}:*`);
    await redis.del('audio:list:all:*');
    await redis.del('audio:stats:global');

    return {
      message: '音频资源上传成功',
      resource: result
    };

  } catch (error) {
    if (error instanceof APIError) throw error;
    
    console.error('Upload audio resource error:', error);
    throw new APIError('UPLOAD_AUDIO_FAILED', '上传音频资源失败', null, 500);
  }
});

/**
 * 删除音频资源 (管理员功能)
 * DELETE /v1/audio/{resourceId}
 */
const deleteAudioResource = errorHandler(async (event, context) => {
  // 需要管理员权限
  const authResult = await authMiddleware(['admin:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const resourceId = event.pathParameters?.resourceId;
  
  if (!resourceId) {
    throw new APIError('MISSING_RESOURCE_ID', '音频资源ID不能为空', null, 400);
  }

  try {
    const cosService = getCosService();
    
    // 删除COS中的文件
    const result = await cosService.deleteAudio(resourceId);
    
    // 清除相关缓存
    const redis = RedisManager.getInstance();
    await redis.del(`audio:resource:${resourceId}`);
    
    // 清除列表缓存
    const category = resourceId.split('_')[0];
    await redis.del(`audio:list:${category}:*`);
    await redis.del('audio:list:all:*');
    await redis.del('audio:stats:global');

    return {
      message: '音频资源删除成功',
      resource: result
    };

  } catch (error) {
    console.error('Delete audio resource error:', error);
    throw new APIError('DELETE_AUDIO_FAILED', '删除音频资源失败', null, 500);
  }
});

/**
 * 路由处理器
 */
const routes = {
  'GET /v1/audio': getAudioResources,
  'GET /v1/audio/stats': getAudioStats,
  'GET /v1/audio/{resourceId}': getAudioResource,
  'POST /v1/audio/{resourceId}': uploadAudioResource,
  'DELETE /v1/audio/{resourceId}': deleteAudioResource
};

/**
 * 主处理器
 */
const main = async (event, context) => {
  const method = event.httpMethod || event.requestContext?.httpMethod;
  const path = event.path || event.requestContext?.path;
  
  // 处理路径参数
  let routeKey = `${method} ${path}`;
  
  // 匹配动态路由
  for (const route of Object.keys(routes)) {
    const routePattern = route.replace(/\{[^}]+\}/g, '[^/]+');
    const regex = new RegExp(`^${routePattern}$`);
    
    if (regex.test(routeKey)) {
      routeKey = route;
      break;
    }
  }
  
  console.log(`Audio Service - ${routeKey}`);
  
  // 添加CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Device-Id',
    'Access-Control-Max-Age': '86400'
  };
  
  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  
  // 查找对应的路由处理器
  const handler = routes[routeKey];
  
  if (!handler) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: '接口不存在',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
  
  try {
    const result = await handler(event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 0,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
    
  } catch (error) {
    console.error('Audio handler error:', error);
    
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error instanceof APIError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      message = error.message;
    }
    
    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
};

module.exports = {
  main,
  getAudioResource,
  getAudioResources,
  getAudioStats,
  uploadAudioResource,
  deleteAudioResource
};