/**
 * 音频服务开发处理器
 * 使用Mock数据服务的简化版本
 */

/**
 * 主处理器 - 路由分发
 */
const main = async (event, context) => {
  const { httpMethod, path } = event;

  console.log(`Audio Handler: ${httpMethod} ${path}`);

  try {
    // 路由分发
    if (httpMethod === 'GET' && path === '/v1/audio') {
      return await getAudioList(event, context);
    } else if (httpMethod === 'GET' && path.includes('/v1/audio/') && path.includes('/stats')) {
      return await getAudioStats(event, context);
    } else if (httpMethod === 'GET' && path.includes('/v1/audio/')) {
      return await getAudioResource(event, context);
    } else if (httpMethod === 'POST' && path.includes('/v1/audio/')) {
      return await uploadAudioResource(event, context);
    } else if (httpMethod === 'DELETE' && path.includes('/v1/audio/')) {
      return await deleteAudioResource(event, context);
    }

    // 404处理
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: `音频服务不支持 ${httpMethod} ${path}`,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Audio Handler Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INTERNAL_ERROR',
        message: error.message || '服务器内部错误',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 获取音频资源列表
 * GET /v1/audio
 */
const getAudioList = async (event, context) => {
  const queryParams = event.queryStringParameters || {};
  const { dialect, limit = 20, offset = 0 } = queryParams;

  // Mock音频资源数据
  const mockAudioResources = [
    {
      id: 'audio_1',
      filename: '四川话_问候语_1.mp3',
      dialect: '四川话',
      category: '问候语',
      duration: 3.5,
      size: 56000,
      url: 'https://dev-audio.example.com/sichuan_greeting_1.mp3',
      uploadTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      playCount: 125
    },
    {
      id: 'audio_2',
      filename: '广东话_日常用语_1.mp3',
      dialect: '广东话',
      category: '日常用语',
      duration: 4.2,
      size: 67000,
      url: 'https://dev-audio.example.com/cantonese_daily_1.mp3',
      uploadTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      playCount: 89
    },
    {
      id: 'audio_3',
      filename: '上海话_数字_1.mp3',
      dialect: '上海话',
      category: '数字',
      duration: 2.8,
      size: 45000,
      url: 'https://dev-audio.example.com/shanghai_number_1.mp3',
      uploadTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      playCount: 203
    },
    {
      id: 'audio_4',
      filename: '北京话_特色词汇_1.mp3',
      dialect: '北京话',
      category: '特色词汇',
      duration: 5.1,
      size: 82000,
      url: 'https://dev-audio.example.com/beijing_special_1.mp3',
      uploadTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      playCount: 156
    },
    {
      id: 'audio_5',
      filename: '东北话_俚语_1.mp3',
      dialect: '东北话',
      category: '俚语',
      duration: 3.9,
      size: 62000,
      url: 'https://dev-audio.example.com/dongbei_slang_1.mp3',
      uploadTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      playCount: 78
    }
  ];

  // 根据方言过滤
  let filteredResources = dialect 
    ? mockAudioResources.filter(r => r.dialect === dialect)
    : mockAudioResources;

  // 分页
  const startIndex = parseInt(offset);
  const endIndex = startIndex + parseInt(limit);
  const paginatedResources = filteredResources.slice(startIndex, endIndex);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        resources: paginatedResources,
        total: filteredResources.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        filters: {
          dialect: dialect || 'all'
        }
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 获取音频资源信息
 * GET /v1/audio/{resourceId}
 */
const getAudioResource = async (event, context) => {
  const resourceId = event.path.split('/').pop();

  // Mock特定音频资源
  const mockResource = {
    id: resourceId,
    filename: `${resourceId}.mp3`,
    dialect: '四川话',
    category: '问候语',
    duration: 3.5,
    size: 56000,
    format: 'mp3',
    bitrate: '128kbps',
    sampleRate: '44.1kHz',
    url: `https://dev-audio.example.com/${resourceId}.mp3`,
    cdnUrl: `https://cdn.example.com/audio/${resourceId}.mp3`,
    uploadTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    playCount: Math.floor(Math.random() * 500),
    downloadCount: Math.floor(Math.random() * 100),
    metadata: {
      title: '四川话问候语示例',
      description: '展示四川方言中常用的问候语表达方式',
      tags: ['四川话', '问候语', '日常用语'],
      speaker: '成都本地人',
      recordingDate: '2023-10-15',
      location: '成都'
    },
    status: 'active',
    lastAccessed: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
  };

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: mockResource,
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 获取音频统计信息 (管理员)
 * GET /v1/audio/stats
 */
const getAudioStats = async (event, context) => {
  // Mock统计数据
  const mockStats = {
    total: {
      resources: 1250,
      dialects: 25,
      categories: 12,
      totalSize: '2.3GB',
      totalDuration: '8.5小时'
    },
    dialects: {
      '四川话': { count: 285, size: '450MB', avgDuration: 3.2 },
      '广东话': { count: 195, size: '320MB', avgDuration: 3.8 },
      '上海话': { count: 180, size: '280MB', avgDuration: 2.9 },
      '北京话': { count: 165, size: '270MB', avgDuration: 3.5 },
      '东北话': { count: 145, size: '230MB', avgDuration: 4.1 },
      '其他': { count: 280, size: '750MB', avgDuration: 3.4 }
    },
    categories: {
      '问候语': 125,
      '日常用语': 320,
      '数字': 89,
      '颜色': 67,
      '食物': 156,
      '家庭': 134,
      '职业': 98,
      '交通': 87,
      '天气': 76,
      '时间': 68,
      '特色词汇': 45,
      '俚语': 35
    },
    recent: {
      uploadsToday: 12,
      uploadsThisWeek: 67,
      uploadsThisMonth: 234,
      popularResources: [
        { id: 'audio_1', dialect: '四川话', playCount: 1205 },
        { id: 'audio_2', dialect: '广东话', playCount: 989 },
        { id: 'audio_3', dialect: '上海话', playCount: 856 }
      ]
    },
    storage: {
      used: '2.3GB',
      limit: '10GB',
      usagePercent: 23,
      avgFileSize: '1.8MB'
    }
  };

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: mockStats,
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 上传音频资源 (管理员)
 * POST /v1/audio/{resourceId}
 */
const uploadAudioResource = async (event, context) => {
  return {
    statusCode: 501,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'NOT_IMPLEMENTED',
      message: '开发环境暂不支持音频上传功能',
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 删除音频资源 (管理员)
 * DELETE /v1/audio/{resourceId}
 */
const deleteAudioResource = async (event, context) => {
  return {
    statusCode: 501,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'NOT_IMPLEMENTED',
      message: '开发环境暂不支持音频删除功能',
      timestamp: new Date().toISOString()
    })
  };
};

module.exports = { main };