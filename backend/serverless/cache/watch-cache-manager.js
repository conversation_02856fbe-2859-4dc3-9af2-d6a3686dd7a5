/**
 * 围观功能缓存管理器
 * 
 * 优化围观功能的缓存策略，包括热点数据缓存、预加载机制、缓存失效策略等
 * 提高缓存命中率到80%+
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const Redis = require('redis');
const EventEmitter = require('events');

class WatchCacheManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // Redis配置
        this.redisConfig = {
            host: options.redisHost || 'localhost',
            port: options.redisPort || 6379,
            password: options.redisPassword || '',
            db: options.redisDb || 0,
            keyPrefix: 'watch:',
            maxRetries: 3,
            retryDelayOnFailover: 100
        };
        
        // 缓存策略配置
        this.cacheConfig = {
            // TTL配置（秒）
            ttl: {
                hotRooms: 60,           // 热门房间1分钟
                roomDetails: 30,        // 房间详情30秒
                roomViewers: 20,        // 房间观众20秒
                barrageMessages: 10,    // 弹幕消息10秒
                userProfile: 300,       // 用户资料5分钟
                roomStats: 15,          // 房间统计15秒
                predictions: 60,        // 预测数据1分钟
                gifts: 30               // 礼物数据30秒
            },
            
            // 预加载配置
            preload: {
                enabled: true,
                hotRoomThreshold: 50,   // 观众数超过50的房间
                preloadInterval: 30000, // 30秒预加载一次
                maxPreloadRooms: 20     // 最多预加载20个房间
            },
            
            // 热点数据配置
            hotData: {
                trackingWindow: 300000, // 5分钟统计窗口
                hotThreshold: 10,       // 访问次数阈值
                maxHotKeys: 1000        // 最大热点key数量
            }
        };
        
        // Redis客户端
        this.redisClient = null;
        this.pubClient = null;
        this.subClient = null;
        
        // 缓存统计
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            errors: 0,
            hitRate: 0
        };
        
        // 热点数据追踪
        this.hotDataTracker = {
            accessCounts: new Map(),    // key -> count
            accessTimes: new Map(),     // key -> timestamps[]
            hotKeys: new Set()          // 热点key集合
        };
        
        // 预加载管理
        this.preloadManager = {
            timer: null,
            isRunning: false,
            lastPreloadTime: 0
        };
        
        // 缓存失效管理
        this.invalidationManager = {
            patterns: new Map(),        // pattern -> keys[]
            dependencies: new Map()     // key -> dependent keys[]
        };
        
        this.initialize();
    }
    
    /**
     * 初始化缓存管理器
     */
    async initialize() {
        console.log('初始化缓存管理器...');
        
        try {
            // 创建Redis连接
            this.redisClient = Redis.createClient(this.redisConfig);
            this.pubClient = this.redisClient.duplicate();
            this.subClient = this.redisClient.duplicate();
            
            // 连接Redis
            await Promise.all([
                this.redisClient.connect(),
                this.pubClient.connect(),
                this.subClient.connect()
            ]);
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 启动预加载
            if (this.cacheConfig.preload.enabled) {
                this.startPreloading();
            }
            
            // 启动统计更新
            this.startStatsUpdate();
            
            // 启动热点数据追踪
            this.startHotDataTracking();
            
            console.log('缓存管理器初始化完成');
            
        } catch (error) {
            console.error('初始化缓存管理器失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // Redis错误处理
        this.redisClient.on('error', (error) => {
            console.error('Redis客户端错误:', error);
            this.stats.errors++;
        });
        
        // 订阅缓存失效事件
        this.subClient.subscribe('cache:invalidate', (message) => {
            this.handleCacheInvalidation(JSON.parse(message));
        });
    }
    
    /**
     * 获取缓存数据
     */
    async get(key, options = {}) {
        const fullKey = this.getFullKey(key);
        const startTime = Date.now();
        
        try {
            // 追踪热点数据
            this.trackHotData(key);
            
            // 从Redis获取数据
            const data = await this.redisClient.get(fullKey);
            
            if (data !== null) {
                this.stats.hits++;
                this.emit('cacheHit', { key, time: Date.now() - startTime });
                
                // 解析数据
                const parsed = JSON.parse(data);
                
                // 检查是否需要刷新
                if (options.refreshThreshold && this.shouldRefresh(parsed, options.refreshThreshold)) {
                    this.emit('refreshNeeded', { key, data: parsed });
                }
                
                return parsed.value;
            } else {
                this.stats.misses++;
                this.emit('cacheMiss', { key, time: Date.now() - startTime });
                return null;
            }
            
        } catch (error) {
            console.error(`缓存获取失败 ${key}:`, error);
            this.stats.errors++;
            return null;
        }
    }
    
    /**
     * 设置缓存数据
     */
    async set(key, value, ttl = null, options = {}) {
        const fullKey = this.getFullKey(key);
        const startTime = Date.now();
        
        try {
            // 构造缓存对象
            const cacheObject = {
                value: value,
                timestamp: Date.now(),
                ttl: ttl || this.getTTL(key),
                metadata: options.metadata || {}
            };
            
            const serialized = JSON.stringify(cacheObject);
            const expiry = cacheObject.ttl;
            
            // 设置到Redis
            if (expiry > 0) {
                await this.redisClient.setEx(fullKey, expiry, serialized);
            } else {
                await this.redisClient.set(fullKey, serialized);
            }
            
            this.stats.sets++;
            this.emit('cacheSet', { key, ttl: expiry, time: Date.now() - startTime });
            
            // 设置依赖关系
            if (options.dependencies) {
                this.setDependencies(key, options.dependencies);
            }
            
            // 添加到失效模式
            if (options.invalidationPattern) {
                this.addToInvalidationPattern(options.invalidationPattern, key);
            }
            
            return true;
            
        } catch (error) {
            console.error(`缓存设置失败 ${key}:`, error);
            this.stats.errors++;
            return false;
        }
    }
    
    /**
     * 删除缓存数据
     */
    async delete(key) {
        const fullKey = this.getFullKey(key);
        
        try {
            const result = await this.redisClient.del(fullKey);
            
            if (result > 0) {
                this.stats.deletes++;
                this.emit('cacheDelete', { key });
                
                // 删除依赖的缓存
                await this.deleteDependentCaches(key);
            }
            
            return result > 0;
            
        } catch (error) {
            console.error(`缓存删除失败 ${key}:`, error);
            this.stats.errors++;
            return false;
        }
    }
    
    /**
     * 批量删除缓存
     */
    async deletePattern(pattern) {
        const fullPattern = this.getFullKey(pattern);
        
        try {
            const keys = await this.redisClient.keys(fullPattern);
            
            if (keys.length > 0) {
                const result = await this.redisClient.del(keys);
                this.stats.deletes += result;
                this.emit('cachePatternDelete', { pattern, count: result });
                return result;
            }
            
            return 0;
            
        } catch (error) {
            console.error(`批量删除缓存失败 ${pattern}:`, error);
            this.stats.errors++;
            return 0;
        }
    }
    
    /**
     * 获取或设置缓存（缓存穿透保护）
     */
    async getOrSet(key, fetchFunction, ttl = null, options = {}) {
        // 先尝试获取缓存
        let data = await this.get(key, options);
        
        if (data !== null) {
            return data;
        }
        
        // 缓存未命中，获取数据
        try {
            data = await fetchFunction();
            
            if (data !== null && data !== undefined) {
                // 设置缓存
                await this.set(key, data, ttl, options);
            }
            
            return data;
            
        } catch (error) {
            console.error(`获取数据失败 ${key}:`, error);
            throw error;
        }
    }
    
    /**
     * 预加载热门房间数据
     */
    async preloadHotRooms() {
        if (this.preloadManager.isRunning) {
            return;
        }
        
        this.preloadManager.isRunning = true;
        
        try {
            console.log('开始预加载热门房间数据...');
            
            // 获取热门房间列表
            const hotRooms = await this.getHotRooms();
            
            // 并行预加载房间数据
            const preloadPromises = hotRooms.map(room => 
                this.preloadRoomData(room.id)
            );
            
            await Promise.all(preloadPromises);
            
            this.preloadManager.lastPreloadTime = Date.now();
            console.log(`预加载完成，处理了 ${hotRooms.length} 个热门房间`);
            
        } catch (error) {
            console.error('预加载失败:', error);
        } finally {
            this.preloadManager.isRunning = false;
        }
    }
    
    /**
     * 获取热门房间列表
     */
    async getHotRooms() {
        // 这里应该调用数据库获取热门房间
        // 为了演示，返回模拟数据
        return [
            { id: 1, viewer_count: 100 },
            { id: 2, viewer_count: 80 },
            { id: 3, viewer_count: 60 }
        ].filter(room => room.viewer_count >= this.cacheConfig.preload.hotRoomThreshold)
         .slice(0, this.cacheConfig.preload.maxPreloadRooms);
    }
    
    /**
     * 预加载房间数据
     */
    async preloadRoomData(roomId) {
        const preloadTasks = [
            // 预加载房间详情
            this.preloadIfNotExists(`room:details:${roomId}`, () => this.fetchRoomDetails(roomId)),
            
            // 预加载房间观众
            this.preloadIfNotExists(`room:viewers:${roomId}`, () => this.fetchRoomViewers(roomId)),
            
            // 预加载房间统计
            this.preloadIfNotExists(`room:stats:${roomId}`, () => this.fetchRoomStats(roomId)),
            
            // 预加载最新弹幕
            this.preloadIfNotExists(`room:barrage:${roomId}`, () => this.fetchRecentBarrage(roomId))
        ];
        
        await Promise.all(preloadTasks);
    }
    
    /**
     * 如果不存在则预加载
     */
    async preloadIfNotExists(key, fetchFunction) {
        const exists = await this.exists(key);
        if (!exists) {
            try {
                const data = await fetchFunction();
                if (data) {
                    await this.set(key, data);
                }
            } catch (error) {
                console.error(`预加载失败 ${key}:`, error);
            }
        }
    }
    
    /**
     * 检查缓存是否存在
     */
    async exists(key) {
        const fullKey = this.getFullKey(key);
        try {
            const result = await this.redisClient.exists(fullKey);
            return result === 1;
        } catch (error) {
            console.error(`检查缓存存在性失败 ${key}:`, error);
            return false;
        }
    }
    
    /**
     * 启动预加载
     */
    startPreloading() {
        this.preloadManager.timer = setInterval(() => {
            this.preloadHotRooms();
        }, this.cacheConfig.preload.preloadInterval);
        
        // 立即执行一次预加载
        setTimeout(() => this.preloadHotRooms(), 1000);
    }
    
    /**
     * 启动热点数据追踪
     */
    startHotDataTracking() {
        setInterval(() => {
            this.updateHotDataStats();
        }, 60000); // 每分钟更新一次
    }
    
    /**
     * 追踪热点数据
     */
    trackHotData(key) {
        const now = Date.now();
        const window = this.cacheConfig.hotData.trackingWindow;
        
        // 更新访问计数
        const currentCount = this.hotDataTracker.accessCounts.get(key) || 0;
        this.hotDataTracker.accessCounts.set(key, currentCount + 1);
        
        // 更新访问时间
        if (!this.hotDataTracker.accessTimes.has(key)) {
            this.hotDataTracker.accessTimes.set(key, []);
        }
        
        const times = this.hotDataTracker.accessTimes.get(key);
        times.push(now);
        
        // 清理过期的访问时间
        const validTimes = times.filter(time => now - time <= window);
        this.hotDataTracker.accessTimes.set(key, validTimes);
        
        // 判断是否为热点数据
        if (validTimes.length >= this.cacheConfig.hotData.hotThreshold) {
            this.hotDataTracker.hotKeys.add(key);
        }
    }
    
    /**
     * 更新热点数据统计
     */
    updateHotDataStats() {
        const now = Date.now();
        const window = this.cacheConfig.hotData.trackingWindow;
        
        // 清理过期数据
        for (const [key, times] of this.hotDataTracker.accessTimes) {
            const validTimes = times.filter(time => now - time <= window);
            
            if (validTimes.length === 0) {
                this.hotDataTracker.accessTimes.delete(key);
                this.hotDataTracker.accessCounts.delete(key);
                this.hotDataTracker.hotKeys.delete(key);
            } else {
                this.hotDataTracker.accessTimes.set(key, validTimes);
                this.hotDataTracker.accessCounts.set(key, validTimes.length);
                
                // 更新热点状态
                if (validTimes.length >= this.cacheConfig.hotData.hotThreshold) {
                    this.hotDataTracker.hotKeys.add(key);
                } else {
                    this.hotDataTracker.hotKeys.delete(key);
                }
            }
        }
        
        // 限制热点key数量
        if (this.hotDataTracker.hotKeys.size > this.cacheConfig.hotData.maxHotKeys) {
            const sortedKeys = Array.from(this.hotDataTracker.hotKeys)
                .sort((a, b) => 
                    (this.hotDataTracker.accessCounts.get(b) || 0) - 
                    (this.hotDataTracker.accessCounts.get(a) || 0)
                )
                .slice(0, this.cacheConfig.hotData.maxHotKeys);
            
            this.hotDataTracker.hotKeys = new Set(sortedKeys);
        }
        
        this.emit('hotDataUpdate', {
            hotKeyCount: this.hotDataTracker.hotKeys.size,
            totalTrackedKeys: this.hotDataTracker.accessCounts.size
        });
    }
    
    /**
     * 设置依赖关系
     */
    setDependencies(key, dependencies) {
        for (const dep of dependencies) {
            if (!this.invalidationManager.dependencies.has(dep)) {
                this.invalidationManager.dependencies.set(dep, new Set());
            }
            this.invalidationManager.dependencies.get(dep).add(key);
        }
    }
    
    /**
     * 删除依赖的缓存
     */
    async deleteDependentCaches(key) {
        const dependents = this.invalidationManager.dependencies.get(key);
        if (dependents) {
            const deletePromises = Array.from(dependents).map(depKey => 
                this.delete(depKey)
            );
            await Promise.all(deletePromises);
            this.invalidationManager.dependencies.delete(key);
        }
    }
    
    /**
     * 添加到失效模式
     */
    addToInvalidationPattern(pattern, key) {
        if (!this.invalidationManager.patterns.has(pattern)) {
            this.invalidationManager.patterns.set(pattern, new Set());
        }
        this.invalidationManager.patterns.get(pattern).add(key);
    }
    
    /**
     * 处理缓存失效
     */
    async handleCacheInvalidation(message) {
        const { type, data } = message;
        
        switch (type) {
            case 'room_update':
                await this.invalidateRoomCache(data.roomId);
                break;
                
            case 'user_update':
                await this.invalidateUserCache(data.userId);
                break;
                
            case 'pattern':
                await this.deletePattern(data.pattern);
                break;
                
            default:
                console.warn('未知的缓存失效类型:', type);
        }
    }
    
    /**
     * 失效房间缓存
     */
    async invalidateRoomCache(roomId) {
        const patterns = [
            `room:details:${roomId}`,
            `room:viewers:${roomId}*`,
            `room:stats:${roomId}`,
            `room:barrage:${roomId}*`,
            `room:predictions:${roomId}*`
        ];
        
        for (const pattern of patterns) {
            await this.deletePattern(pattern);
        }
    }
    
    /**
     * 失效用户缓存
     */
    async invalidateUserCache(userId) {
        const patterns = [
            `user:profile:${userId}`,
            `user:rooms:${userId}*`,
            `user:predictions:${userId}*`
        ];
        
        for (const pattern of patterns) {
            await this.deletePattern(pattern);
        }
    }
    
    /**
     * 获取完整key
     */
    getFullKey(key) {
        return `${this.redisConfig.keyPrefix}${key}`;
    }
    
    /**
     * 获取TTL
     */
    getTTL(key) {
        // 根据key类型返回对应的TTL
        for (const [type, ttl] of Object.entries(this.cacheConfig.ttl)) {
            if (key.includes(type)) {
                return ttl;
            }
        }
        return 60; // 默认1分钟
    }
    
    /**
     * 判断是否需要刷新
     */
    shouldRefresh(cacheObject, threshold) {
        const age = Date.now() - cacheObject.timestamp;
        const refreshTime = cacheObject.ttl * threshold * 1000;
        return age >= refreshTime;
    }
    
    /**
     * 启动统计更新
     */
    startStatsUpdate() {
        setInterval(() => {
            this.updateStats();
        }, 30000); // 每30秒更新一次
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? (this.stats.hits / total * 100) : 0;
        
        this.emit('statsUpdate', { ...this.stats });
        
        // 如果命中率低于目标，发出警告
        if (this.stats.hitRate < 80 && total > 100) {
            console.warn(`缓存命中率偏低: ${this.stats.hitRate.toFixed(1)}%`);
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            hotKeyCount: this.hotDataTracker.hotKeys.size,
            trackedKeyCount: this.hotDataTracker.accessCounts.size,
            preloadStatus: {
                isRunning: this.preloadManager.isRunning,
                lastPreloadTime: this.preloadManager.lastPreloadTime
            }
        };
    }
    
    /**
     * 模拟数据获取方法（实际项目中应该调用数据库）
     */
    async fetchRoomDetails(roomId) {
        // 模拟数据库查询
        return { id: roomId, name: `房间${roomId}`, status: 'active' };
    }
    
    async fetchRoomViewers(roomId) {
        // 模拟数据库查询
        return [{ id: 1, name: '观众1' }, { id: 2, name: '观众2' }];
    }
    
    async fetchRoomStats(roomId) {
        // 模拟数据库查询
        return { viewerCount: 50, messageCount: 100 };
    }
    
    async fetchRecentBarrage(roomId) {
        // 模拟数据库查询
        return [{ id: 1, content: '弹幕1' }, { id: 2, content: '弹幕2' }];
    }
    
    /**
     * 关闭缓存管理器
     */
    async close() {
        console.log('关闭缓存管理器...');
        
        try {
            // 清理定时器
            if (this.preloadManager.timer) {
                clearInterval(this.preloadManager.timer);
            }
            
            // 关闭Redis连接
            await Promise.all([
                this.redisClient.quit(),
                this.pubClient.quit(),
                this.subClient.quit()
            ]);
            
            console.log('缓存管理器已关闭');
            
        } catch (error) {
            console.error('关闭缓存管理器失败:', error);
        }
    }
}

module.exports = WatchCacheManager;
