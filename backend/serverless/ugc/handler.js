/**
 * UGC内容管理处理器
 * 处理用户生成内容的上传、管理、审核等功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { UGCService } = require('./ugcService');
const { logger } = require('../utils/logger');

class UGCHandler {
  constructor() {
    this.ugcService = new UGCService();
  }

  /**
   * 创建UGC内容
   */
  async createContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'createContent'
    });

    try {
      const {
        title,
        description,
        contentType,
        dialectRegion,
        difficultyLevel,
        tags,
        categoryIds,
        primaryFileUrl,
        additionalFiles
      } = JSON.parse(event.body || '{}');

      const userId = event.user?.id;

      if (!title || !contentType || !dialectRegion || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Title, content type, dialect region, and user authentication are required'
          })
        };
      }

      const content = await this.ugcService.createContent({
        title,
        description,
        contentType,
        dialectRegion,
        difficultyLevel: difficultyLevel || 'beginner',
        tags: tags || [],
        categoryIds: categoryIds || [],
        primaryFileUrl,
        additionalFiles: additionalFiles || [],
        creatorId: userId
      });

      ugcLogger.info('UGC content created', {
        contentId: content.id,
        title,
        contentType,
        creatorId: userId
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: content
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to create UGC content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to create content'
        })
      };
    }
  }

  /**
   * 获取UGC内容列表
   */
  async getContentList(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getContentList'
    });

    try {
      const {
        page = 1,
        limit = 20,
        contentType,
        dialectRegion,
        difficulty,
        category,
        tags,
        sortBy = 'created_at',
        sortOrder = 'desc',
        featured,
        creatorId
      } = event.queryStringParameters || {};

      const contentList = await this.ugcService.getContentList({
        page: parseInt(page),
        limit: parseInt(limit),
        contentType,
        dialectRegion,
        difficulty,
        category,
        tags: tags ? tags.split(',') : undefined,
        sortBy,
        sortOrder,
        featured: featured === 'true',
        creatorId
      });

      ugcLogger.debug('UGC content list retrieved', {
        contentCount: contentList.content.length,
        page,
        limit,
        filters: { contentType, dialectRegion, difficulty, category }
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: contentList
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get UGC content list', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get content list'
        })
      };
    }
  }

  /**
   * 获取UGC内容详情
   */
  async getContentDetail(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getContentDetail'
    });

    try {
      const { contentId } = event.pathParameters;
      const userId = event.user?.id;

      if (!contentId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID is required'
          })
        };
      }

      const content = await this.ugcService.getContentDetail(contentId, userId);

      if (!content) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Content not found'
          })
        };
      }

      ugcLogger.debug('UGC content detail retrieved', {
        contentId,
        userId,
        contentType: content.contentType
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: content
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get UGC content detail', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get content detail'
        })
      };
    }
  }

  /**
   * 更新UGC内容
   */
  async updateContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'updateContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const updates = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!contentId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID and user authentication are required'
          })
        };
      }

      const updatedContent = await this.ugcService.updateContent(contentId, updates, userId);

      ugcLogger.info('UGC content updated', {
        contentId,
        userId,
        updatedFields: Object.keys(updates)
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: updatedContent
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to update UGC content', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 :
                        error.message.includes('not found') ? 404 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 删除UGC内容
   */
  async deleteContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'deleteContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const userId = event.user?.id;

      if (!contentId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID and user authentication are required'
          })
        };
      }

      await this.ugcService.deleteContent(contentId, userId);

      ugcLogger.info('UGC content deleted', {
        contentId,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Content deleted successfully'
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to delete UGC content', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 :
                        error.message.includes('not found') ? 404 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 评价UGC内容
   */
  async rateContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'rateContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const { ratingType, ratingValue, comment } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!contentId || !ratingType || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID, rating type, and user authentication are required'
          })
        };
      }

      const rating = await this.ugcService.rateContent({
        contentId,
        userId,
        ratingType,
        ratingValue,
        comment
      });

      ugcLogger.info('UGC content rated', {
        contentId,
        userId,
        ratingType,
        ratingValue
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: rating
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to rate UGC content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to rate content'
        })
      };
    }
  }

  /**
   * 举报UGC内容
   */
  async reportContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'reportContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const { reportType, reason, evidenceUrls } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!contentId || !reportType || !reason || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID, report type, reason, and user authentication are required'
          })
        };
      }

      const report = await this.ugcService.reportContent({
        contentId,
        reporterId: userId,
        reportType,
        reason,
        evidenceUrls: evidenceUrls || []
      });

      ugcLogger.info('UGC content reported', {
        contentId,
        reporterId: userId,
        reportType,
        reportId: report.id
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: report
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to report UGC content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to report content'
        })
      };
    }
  }

  /**
   * 收藏UGC内容
   */
  async favoriteContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'favoriteContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const { folderName } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!contentId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID and user authentication are required'
          })
        };
      }

      const favorite = await this.ugcService.favoriteContent({
        contentId,
        userId,
        folderName: folderName || 'default'
      });

      ugcLogger.info('UGC content favorited', {
        contentId,
        userId,
        folderName
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: favorite
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to favorite UGC content', {
        error: error.message
      });

      const statusCode = error.message.includes('already favorited') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 取消收藏UGC内容
   */
  async unfavoriteContent(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'unfavoriteContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const userId = event.user?.id;

      if (!contentId || !userId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID and user authentication are required'
          })
        };
      }

      await this.ugcService.unfavoriteContent(contentId, userId);

      ugcLogger.info('UGC content unfavorited', {
        contentId,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Content unfavorited successfully'
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to unfavorite UGC content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to unfavorite content'
        })
      };
    }
  }

  /**
   * 获取用户收藏列表
   */
  async getUserFavorites(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getUserFavorites'
    });

    try {
      const userId = event.user?.id;
      const {
        page = 1,
        limit = 20,
        folderName
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const favorites = await this.ugcService.getUserFavorites({
        userId,
        page: parseInt(page),
        limit: parseInt(limit),
        folderName
      });

      ugcLogger.debug('User favorites retrieved', {
        userId,
        favoriteCount: favorites.favorites.length,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: favorites
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get user favorites', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get user favorites'
        })
      };
    }
  }

  /**
   * 获取UGC分类列表
   */
  async getCategories(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getCategories'
    });

    try {
      const { parentId } = event.queryStringParameters || {};

      const categories = await this.ugcService.getCategories(parentId);

      ugcLogger.debug('UGC categories retrieved', {
        categoryCount: categories.length,
        parentId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: categories
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get UGC categories', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get categories'
        })
      };
    }
  }

  /**
   * 获取UGC标签列表
   */
  async getTags(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getTags'
    });

    try {
      const {
        limit = 50,
        official,
        popular
      } = event.queryStringParameters || {};

      const tags = await this.ugcService.getTags({
        limit: parseInt(limit),
        official: official === 'true',
        popular: popular === 'true'
      });

      ugcLogger.debug('UGC tags retrieved', {
        tagCount: tags.length,
        limit,
        official,
        popular
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: tags
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get UGC tags', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get tags'
        })
      };
    }
  }

  /**
   * 获取UGC统计信息
   */
  async getContentStats(event, context) {
    const ugcLogger = logger.child({
      requestId: context.requestId,
      action: 'getContentStats'
    });

    try {
      const { contentId } = event.pathParameters;
      const { timeRange = '30d' } = event.queryStringParameters || {};

      if (!contentId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID is required'
          })
        };
      }

      const stats = await this.ugcService.getContentStats(contentId, timeRange);

      ugcLogger.debug('UGC content stats retrieved', {
        contentId,
        timeRange,
        totalViews: stats.totalViews
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      ugcLogger.error('Failed to get UGC content stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get content stats'
        })
      };
    }
  }
}

// 创建处理器实例
const ugcHandler = new UGCHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  createContent: withMiddleware(ugcHandler.createContent.bind(ugcHandler)),
  getContentList: withMiddleware(ugcHandler.getContentList.bind(ugcHandler)),
  getContentDetail: withMiddleware(ugcHandler.getContentDetail.bind(ugcHandler)),
  updateContent: withMiddleware(ugcHandler.updateContent.bind(ugcHandler)),
  deleteContent: withMiddleware(ugcHandler.deleteContent.bind(ugcHandler)),
  rateContent: withMiddleware(ugcHandler.rateContent.bind(ugcHandler)),
  reportContent: withMiddleware(ugcHandler.reportContent.bind(ugcHandler)),
  favoriteContent: withMiddleware(ugcHandler.favoriteContent.bind(ugcHandler)),
  unfavoriteContent: withMiddleware(ugcHandler.unfavoriteContent.bind(ugcHandler)),
  getUserFavorites: withMiddleware(ugcHandler.getUserFavorites.bind(ugcHandler)),
  getCategories: withMiddleware(ugcHandler.getCategories.bind(ugcHandler)),
  getTags: withMiddleware(ugcHandler.getTags.bind(ugcHandler)),
  getContentStats: withMiddleware(ugcHandler.getContentStats.bind(ugcHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'POST /v1/ugc/content': ugcHandler.createContent.bind(ugcHandler),
      'GET /v1/ugc/content': ugcHandler.getContentList.bind(ugcHandler),
      'GET /v1/ugc/content/{contentId}': ugcHandler.getContentDetail.bind(ugcHandler),
      'PUT /v1/ugc/content/{contentId}': ugcHandler.updateContent.bind(ugcHandler),
      'DELETE /v1/ugc/content/{contentId}': ugcHandler.deleteContent.bind(ugcHandler),
      'POST /v1/ugc/content/{contentId}/rate': ugcHandler.rateContent.bind(ugcHandler),
      'POST /v1/ugc/content/{contentId}/report': ugcHandler.reportContent.bind(ugcHandler),
      'POST /v1/ugc/content/{contentId}/favorite': ugcHandler.favoriteContent.bind(ugcHandler),
      'DELETE /v1/ugc/content/{contentId}/favorite': ugcHandler.unfavoriteContent.bind(ugcHandler),
      'GET /v1/ugc/content/{contentId}/stats': ugcHandler.getContentStats.bind(ugcHandler),
      'GET /v1/ugc/users/favorites': ugcHandler.getUserFavorites.bind(ugcHandler),
      'GET /v1/ugc/categories': ugcHandler.getCategories.bind(ugcHandler),
      'GET /v1/ugc/tags': ugcHandler.getTags.bind(ugcHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
