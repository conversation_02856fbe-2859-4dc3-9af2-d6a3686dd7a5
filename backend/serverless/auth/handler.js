/**
 * 认证服务处理器
 * 处理用户登录、注册、Token刷新等认证相关的API请求
 */

const wechatAuthService = require('../services/WechatAuthService');
const { authMiddleware, optionalAuth } = require('../middleware/auth');
const { validateRequest, VALIDATION_SCHEMAS } = require('../middleware/validation');
const { errorHandler } = require('../middleware/error');
const { rateLimitMiddleware } = require('../middleware/rateLimit');
const { PerformanceMonitor } = require('../middleware/performance');
const { SecurityMiddleware } = require('../middleware/security');
const { APIError } = require('../utils/errors');

// 初始化中间件
const performanceMonitor = new PerformanceMonitor();
const securityMiddleware = new SecurityMiddleware();

/**
 * 微信登录
 * POST /v1/auth/wechat/login
 */
const wechatLogin = errorHandler(async (event, context) => {
  return await performanceMonitor.middleware()(event, context, async () => {
    // 安全检查
    securityMiddleware.securityHeaders()(event, context);
    securityMiddleware.inputSanitization()(event, context);

    // 参数验证
    const data = validateRequest(VALIDATION_SCHEMAS.wechatLogin)(event);

    // 限流检查
    const rateLimitResult = await rateLimitMiddleware('api')(event, context);
    if (rateLimitResult.statusCode) {
      return rateLimitResult;
    }

  const { code, encryptedData, iv } = data;
  const deviceId = event.headers['x-device-id'] || event.headers['X-Device-Id'];
  
  try {
    const result = await wechatAuthService.login(code, encryptedData, iv, deviceId);
    
    return {
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      expiresIn: result.expiresIn,
      tokenType: 'Bearer',
      user: result.user,
      isNewUser: result.isNewUser
    };
    
  } catch (error) {
    console.error('WeChat login error:', error);
    
    if (error.message.includes('无效的code')) {
      throw new APIError('INVALID_WECHAT_CODE', '微信授权码无效或已过期', null, 400);
    } else if (error.message.includes('API调用太频繁')) {
      throw new APIError('WECHAT_API_RATE_LIMIT', '微信API调用频率限制', null, 429);
    }

    throw new APIError('WECHAT_LOGIN_FAILED', '微信登录失败，请重试', null, 500);
  }
  });
});

/**
 * 刷新访问令牌
 * POST /v1/auth/refresh
 */
const refreshToken = errorHandler(async (event, context) => {
  return await performanceMonitor.middleware()(event, context, async () => {
    // 参数验证
    const data = validateRequest({
      type: 'object',
      required: ['refreshToken'],
      properties: {
        refreshToken: { type: 'string', minLength: 1 }
      }
    })(event);

  const { refreshToken: token } = data;
  
  try {
    const result = await wechatAuthService.refreshToken(token);
    
    return {
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      expiresIn: result.expiresIn,
      tokenType: 'Bearer',
      user: result.user
    };
    
  } catch (error) {
    console.error('Refresh token error:', error);
    
    if (error.message.includes('expired')) {
      throw new APIError('REFRESH_TOKEN_EXPIRED', '刷新令牌已过期，请重新登录', null, 401);
    } else if (error.message.includes('invalid')) {
      throw new APIError('INVALID_REFRESH_TOKEN', '无效的刷新令牌', null, 400);
    }

    throw new APIError('TOKEN_REFRESH_FAILED', '令牌刷新失败', null, 500);
  }
  });
});

/**
 * 退出登录
 * POST /v1/auth/logout
 */
const logout = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const userId = event.user.id;
  const deviceId = event.headers['x-device-id'] || event.headers['X-Device-Id'];
  
  try {
    await wechatAuthService.logout(userId, deviceId);
    
    return {
      message: '退出登录成功'
    };
    
  } catch (error) {
    console.error('Logout error:', error);
    throw new APIError('LOGOUT_FAILED', '退出登录失败', null, 500);
  }
});

/**
 * 退出所有设备
 * POST /v1/auth/logout-all
 */
const logoutAll = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const userId = event.user.id;
  
  try {
    await wechatAuthService.logoutAll(userId);
    
    return {
      message: '已退出所有设备'
    };
    
  } catch (error) {
    console.error('Logout all error:', error);
    throw new APIError('LOGOUT_ALL_FAILED', '退出所有设备失败', null, 500);
  }
});

/**
 * 获取当前用户信息
 * GET /v1/auth/me
 */
const getCurrentUser = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const user = event.user;
  
  return {
    user: user.toJSON(true), // 包含私有信息
    scopes: event.scopes,
    sessionId: event.tokenPayload.sessionId
  };
});

/**
 * 验证Token有效性
 * GET /v1/auth/verify
 */
const verifyToken = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware()(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const tokenPayload = event.tokenPayload;
  const now = Math.floor(Date.now() / 1000);
  
  return {
    valid: true,
    userId: tokenPayload.sub,
    expiresAt: tokenPayload.exp,
    issuedAt: tokenPayload.iat,
    ttl: tokenPayload.exp - now,
    scopes: tokenPayload.scope ? tokenPayload.scope.split(' ') : []
  };
});

/**
 * 获取登录统计
 * GET /v1/auth/stats
 */
const getLoginStats = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  const userId = event.user.id;
  
  try {
    const stats = await wechatAuthService.getLoginStats(userId);
    
    return stats;
    
  } catch (error) {
    console.error('Get login stats error:', error);
    throw new APIError('GET_STATS_FAILED', '获取登录统计失败', null, 500);
  }
});

/**
 * 获取用户手机号（需要用户授权）
 * POST /v1/auth/phone
 */
const getPhoneNumber = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const data = validateRequest({
    type: 'object',
    required: ['code'],
    properties: {
      code: { type: 'string', minLength: 1 }
    }
  })(event);

  const { code } = data;
  
  try {
    const phoneInfo = await wechatAuthService.getPhoneNumber(code);
    
    return {
      phoneNumber: phoneInfo.phoneNumber,
      purePhoneNumber: phoneInfo.purePhoneNumber,
      countryCode: phoneInfo.countryCode
    };
    
  } catch (error) {
    console.error('Get phone number error:', error);
    throw new APIError('GET_PHONE_FAILED', '获取手机号失败', null, 500);
  }
});

/**
 * 路由处理器
 */
const routes = {
  'POST /v1/auth/wechat/login': wechatLogin,
  'POST /v1/auth/refresh': refreshToken,
  'POST /v1/auth/logout': logout,
  'POST /v1/auth/logout-all': logoutAll,
  'GET /v1/auth/me': getCurrentUser,
  'GET /v1/auth/verify': verifyToken,
  'GET /v1/auth/stats': getLoginStats,
  'POST /v1/auth/phone': getPhoneNumber
};

/**
 * 主处理器
 */
const main = async (event, context) => {
  const method = event.httpMethod || event.requestContext?.httpMethod;
  const path = event.path || event.requestContext?.path;
  const routeKey = `${method} ${path}`;
  
  console.log(`Auth Service - ${routeKey}`);
  
  // 添加CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Device-Id',
    'Access-Control-Max-Age': '86400'
  };
  
  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  
  // 查找对应的路由处理器
  const handler = routes[routeKey];
  
  if (!handler) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: '接口不存在',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
  
  try {
    const result = await handler(event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 0,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
    
  } catch (error) {
    console.error('Auth handler error:', error);
    
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error instanceof APIError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      message = error.message;
    }
    
    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
};

module.exports = {
  main,
  wechatLogin,
  refreshToken,
  logout,
  logoutAll,
  getCurrentUser,
  verifyToken,
  getLoginStats,
  getPhoneNumber
};