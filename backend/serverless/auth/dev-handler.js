/**
 * 认证服务开发处理器
 * 使用Mock数据服务的简化版本
 */

const jwt = require('jsonwebtoken');
const devDb = require('../utils/dev-database').getInstance();
const devRedis = require('../utils/dev-redis').getInstance();

// 开发环境JWT密钥
const JWT_SECRET = 'dev_jwt_secret_key_for_testing_only';
const ACCESS_TOKEN_TTL = 24 * 60 * 60; // 24小时
const REFRESH_TOKEN_TTL = 30 * 24 * 60 * 60; // 30天

/**
 * 主处理器 - 路由分发
 */
const main = async (event, context) => {
  const { httpMethod, path } = event;

  console.log(`Auth Handler: ${httpMethod} ${path}`);

  try {
    // 路由分发
    if (path.includes('/wechat/login') && httpMethod === 'POST') {
      return await wechatLogin(event, context);
    } else if (path.includes('/refresh') && httpMethod === 'POST') {
      return await refreshToken(event, context);
    } else if (path.includes('/logout') && httpMethod === 'POST') {
      return await logout(event, context);
    } else if (path.includes('/me') && httpMethod === 'GET') {
      return await getCurrentUser(event, context);
    } else if (path.includes('/verify') && httpMethod === 'GET') {
      return await verifyToken(event, context);
    }

    // 404处理
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: `认证服务不支持 ${httpMethod} ${path}`,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Auth Handler Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INTERNAL_ERROR',
        message: error.message || '服务器内部错误',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 微信登录
 * POST /v1/auth/wechat/login
 */
const wechatLogin = async (event, context) => {
  const body = JSON.parse(event.body || '{}');
  const { code, userInfo } = body;

  if (!code) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'MISSING_CODE',
        message: '缺少微信授权码',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 模拟微信登录 - 开发环境
  const mockOpenid = `dev_openid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // 查找或创建用户
  let user = await devDb.getUserByOpenid(mockOpenid);
  
  if (!user) {
    user = await devDb.createUser({
      openid: mockOpenid,
      nickname: userInfo?.nickname || '开发用户',
      avatar_url: userInfo?.avatarUrl || ''
    });
  } else {
    // 更新最后登录时间
    user.last_login = new Date();
  }

  // 生成Token
  const accessToken = jwt.sign(
    {
      userId: user.user_id,
      openid: user.openid,
      type: 'access'
    },
    JWT_SECRET,
    { expiresIn: ACCESS_TOKEN_TTL }
  );

  const refreshToken = jwt.sign(
    {
      userId: user.user_id,
      openid: user.openid,
      type: 'refresh'
    },
    JWT_SECRET,
    { expiresIn: REFRESH_TOKEN_TTL }
  );

  // 缓存Token
  await devRedis.set(`access_token:${user.user_id}`, accessToken, ACCESS_TOKEN_TTL);
  await devRedis.set(`refresh_token:${user.user_id}`, refreshToken, REFRESH_TOKEN_TTL);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        accessToken,
        refreshToken,
        user: {
          id: user.user_id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          gameCount: user.game_count,
          totalScore: user.total_score,
          bestScore: user.best_score,
          accuracyRate: Math.round(user.accuracy_rate * 100)
        },
        expiresIn: ACCESS_TOKEN_TTL
      },
      timestamp: new Date().toISOString()
    })
  };
};

/**
 * 刷新Token
 * POST /v1/auth/refresh
 */
const refreshToken = async (event, context) => {
  const body = JSON.parse(event.body || '{}');
  const { refreshToken } = body;

  if (!refreshToken) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'MISSING_REFRESH_TOKEN',
        message: '缺少刷新Token',
        timestamp: new Date().toISOString()
      })
    };
  }

  try {
    // 验证刷新Token
    const decoded = jwt.verify(refreshToken, JWT_SECRET);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }

    const user = await devDb.getUser(decoded.userId);
    if (!user) {
      throw new Error('User not found');
    }

    // 生成新的访问Token
    const newAccessToken = jwt.sign(
      {
        userId: user.user_id,
        openid: user.openid,
        type: 'access'
      },
      JWT_SECRET,
      { expiresIn: ACCESS_TOKEN_TTL }
    );

    // 更新缓存
    await devRedis.set(`access_token:${user.user_id}`, newAccessToken, ACCESS_TOKEN_TTL);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: {
          accessToken: newAccessToken,
          expiresIn: ACCESS_TOKEN_TTL
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INVALID_REFRESH_TOKEN',
        message: '刷新Token无效或已过期',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 退出登录
 * POST /v1/auth/logout
 */
const logout = async (event, context) => {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'UNAUTHORIZED',
        message: '缺少认证信息',
        timestamp: new Date().toISOString()
      })
    };
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 删除缓存的Token
    await devRedis.del(`access_token:${decoded.userId}`);
    await devRedis.del(`refresh_token:${decoded.userId}`);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        message: '退出登录成功',
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    return {
      statusCode: 200, // 即使Token无效也返回成功，因为目标达到了
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        message: '退出登录成功',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 获取当前用户信息
 * GET /v1/auth/me
 */
const getCurrentUser = async (event, context) => {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'UNAUTHORIZED',
        message: '缺少认证信息',
        timestamp: new Date().toISOString()
      })
    };
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = await devDb.getUser(decoded.userId);
    
    if (!user) {
      return {
        statusCode: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'USER_NOT_FOUND',
          message: '用户不存在',
          timestamp: new Date().toISOString()
        })
      };
    }

    // 获取用户统计信息
    const userStats = await devDb.getUserStats(user.user_id);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: {
          user: {
            id: user.user_id,
            nickname: user.nickname,
            avatarUrl: user.avatar_url,
            createdAt: user.created_at,
            lastLogin: user.last_login
          },
          stats: userStats
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INVALID_TOKEN',
        message: 'Token无效或已过期',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 验证Token
 * GET /v1/auth/verify
 */
const verifyToken = async (event, context) => {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'UNAUTHORIZED',
        message: '缺少认证信息',
        valid: false,
        timestamp: new Date().toISOString()
      })
    };
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        valid: true,
        data: {
          userId: decoded.userId,
          openid: decoded.openid,
          exp: decoded.exp,
          iat: decoded.iat
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    return {
      statusCode: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INVALID_TOKEN',
        message: 'Token无效或已过期',
        valid: false,
        timestamp: new Date().toISOString()
      })
    };
  }
};

module.exports = { main };