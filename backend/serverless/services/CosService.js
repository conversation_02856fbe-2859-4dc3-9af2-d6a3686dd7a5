/**
 * 腾讯云COS对象存储服务
 * 用于管理音频资源的上传、下载和CDN加速
 */

const COS = require('cos-nodejs-sdk-v5');
const config = require('../../config');

class CosService {
  constructor() {
    this.cos = new COS({
      SecretId: config.cos.secretId,
      SecretKey: config.cos.secretKey,
      Domain: `${config.cos.bucket}.cos.${config.cos.region}.myqcloud.com`
    });
    
    this.bucket = config.cos.bucket;
    this.region = config.cos.region;
    this.cdnDomain = config.cos.cdnDomain; // CDN加速域名
    
    // 音频资源缓存时间 (7天)
    this.cacheMaxAge = 7 * 24 * 60 * 60;
  }

  /**
   * 获取音频资源信息
   */
  async getAudioResource(resourceId) {
    try {
      // 构建音频文件路径
      const audioPath = this.buildAudioPath(resourceId);
      
      // 检查文件是否存在
      const headResult = await this.cos.headObject({
        Bucket: this.bucket,
        Region: this.region,
        Key: audioPath
      });

      if (!headResult) {
        throw new Error('Audio resource not found');
      }

      // 获取文件信息
      const fileInfo = {
        resourceId: resourceId,
        path: audioPath,
        size: headResult.headers['content-length'],
        contentType: headResult.headers['content-type'] || 'audio/mpeg',
        lastModified: headResult.headers['last-modified'],
        etag: headResult.headers.etag
      };

      // 生成访问URL
      const accessUrl = await this.generateAccessUrl(audioPath);
      const cdnUrl = this.generateCdnUrl(audioPath);

      return {
        ...fileInfo,
        url: cdnUrl || accessUrl, // 优先使用CDN地址
        directUrl: accessUrl,
        cdnUrl: cdnUrl,
        cacheMaxAge: this.cacheMaxAge
      };

    } catch (error) {
      console.error('Get audio resource error:', error);
      
      if (error.statusCode === 404) {
        throw new Error('AUDIO_NOT_FOUND');
      }
      
      throw new Error('AUDIO_ACCESS_FAILED');
    }
  }

  /**
   * 生成带签名的访问URL
   */
  async generateAccessUrl(filePath, expires = 3600) {
    try {
      const url = this.cos.getObjectUrl({
        Bucket: this.bucket,
        Region: this.region,
        Key: filePath,
        Sign: true,
        Expires: expires // 1小时有效期
      });

      return url;
    } catch (error) {
      console.error('Generate access URL error:', error);
      throw new Error('GENERATE_URL_FAILED');
    }
  }

  /**
   * 生成CDN加速URL (无需签名)
   */
  generateCdnUrl(filePath) {
    if (!this.cdnDomain) {
      return null;
    }

    // 确保文件路径以 / 开头
    const normalizedPath = filePath.startsWith('/') ? filePath : `/${filePath}`;
    
    return `https://${this.cdnDomain}${normalizedPath}`;
  }

  /**
   * 构建音频文件路径
   */
  buildAudioPath(resourceId) {
    // 根据resourceId构建文件路径
    // 格式: audio/{category}/{region}/{filename}
    
    // 解析resourceId (例如: sichuan_001, guangdong_hello_world)
    const parts = resourceId.split('_');
    
    if (parts.length < 2) {
      throw new Error('Invalid resource ID format');
    }

    const category = parts[0]; // 方言类别
    const filename = parts.slice(1).join('_'); // 文件名
    
    return `audio/${category}/${filename}.mp3`;
  }

  /**
   * 获取音频文件列表
   */
  async listAudioResources(category = null, limit = 100) {
    try {
      const prefix = category ? `audio/${category}/` : 'audio/';
      
      const result = await this.cos.getBucket({
        Bucket: this.bucket,
        Region: this.region,
        Prefix: prefix,
        MaxKeys: limit
      });

      if (!result.Contents) {
        return [];
      }

      const resources = result.Contents
        .filter(item => item.Key.endsWith('.mp3'))
        .map(item => {
          const resourceId = this.extractResourceId(item.Key);
          return {
            resourceId: resourceId,
            path: item.Key,
            size: parseInt(item.Size),
            lastModified: item.LastModified,
            etag: item.ETag,
            url: this.generateCdnUrl(item.Key)
          };
        });

      return resources;
    } catch (error) {
      console.error('List audio resources error:', error);
      throw new Error('LIST_AUDIO_FAILED');
    }
  }

  /**
   * 从文件路径提取resourceId
   */
  extractResourceId(filePath) {
    // 例如: audio/sichuan/001.mp3 -> sichuan_001
    const parts = filePath.split('/');
    
    if (parts.length < 3) {
      return null;
    }

    const category = parts[1];
    const filename = parts[2].replace('.mp3', '');
    
    return `${category}_${filename}`;
  }

  /**
   * 上传音频文件
   */
  async uploadAudio(resourceId, audioBuffer, contentType = 'audio/mpeg') {
    try {
      const filePath = this.buildAudioPath(resourceId);
      
      const result = await this.cos.putObject({
        Bucket: this.bucket,
        Region: this.region,
        Key: filePath,
        Body: audioBuffer,
        ContentType: contentType,
        CacheControl: `max-age=${this.cacheMaxAge}`,
        Metadata: {
          'resource-id': resourceId,
          'upload-time': new Date().toISOString()
        }
      });

      return {
        resourceId: resourceId,
        path: filePath,
        etag: result.ETag,
        url: this.generateCdnUrl(filePath),
        uploadTime: new Date().toISOString()
      };

    } catch (error) {
      console.error('Upload audio error:', error);
      throw new Error('UPLOAD_AUDIO_FAILED');
    }
  }

  /**
   * 删除音频文件
   */
  async deleteAudio(resourceId) {
    try {
      const filePath = this.buildAudioPath(resourceId);
      
      await this.cos.deleteObject({
        Bucket: this.bucket,
        Region: this.region,
        Key: filePath
      });

      return {
        resourceId: resourceId,
        path: filePath,
        deletedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Delete audio error:', error);
      throw new Error('DELETE_AUDIO_FAILED');
    }
  }

  /**
   * 批量删除音频文件
   */
  async batchDeleteAudio(resourceIds) {
    try {
      const objects = resourceIds.map(resourceId => ({
        Key: this.buildAudioPath(resourceId)
      }));

      const result = await this.cos.deleteMultipleObject({
        Bucket: this.bucket,
        Region: this.region,
        Objects: objects
      });

      return {
        deleted: result.Deleted || [],
        errors: result.Error || [],
        deletedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Batch delete audio error:', error);
      throw new Error('BATCH_DELETE_AUDIO_FAILED');
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats() {
    try {
      // 获取音频文件总数和大小
      const audioList = await this.listAudioResources(null, 1000);
      
      const totalFiles = audioList.length;
      const totalSize = audioList.reduce((sum, item) => sum + item.size, 0);
      
      // 按类别分组统计
      const categoryStats = {};
      audioList.forEach(item => {
        const category = item.resourceId.split('_')[0];
        if (!categoryStats[category]) {
          categoryStats[category] = { count: 0, size: 0 };
        }
        categoryStats[category].count += 1;
        categoryStats[category].size += item.size;
      });

      return {
        totalFiles: totalFiles,
        totalSize: totalSize,
        totalSizeMB: Math.round(totalSize / 1024 / 1024 * 100) / 100,
        categoryStats: categoryStats,
        lastUpdate: new Date().toISOString()
      };

    } catch (error) {
      console.error('Get storage stats error:', error);
      throw new Error('GET_STORAGE_STATS_FAILED');
    }
  }
}

// 单例模式
let cosServiceInstance = null;

const getCosService = () => {
  if (!cosServiceInstance) {
    cosServiceInstance = new CosService();
  }
  return cosServiceInstance;
};

module.exports = {
  CosService,
  getCosService
};