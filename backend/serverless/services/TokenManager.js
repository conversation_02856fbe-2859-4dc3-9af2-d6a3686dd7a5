/**
 * JWT Token管理服务
 * 提供Token生成、验证、刷新等功能
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../../config');
const redis = require('../utils/redis');

class TokenManager {
  constructor() {
    this.accessTokenSecret = config.jwt.accessSecret;
    this.refreshTokenSecret = config.jwt.refreshSecret;
    this.accessTokenTTL = config.jwt.accessTokenTTL;
    this.refreshTokenTTL = config.jwt.refreshTokenTTL;
    this.issuer = config.jwt.issuer;
    this.audience = config.jwt.audience;
  }

  /**
   * 生成访问令牌
   * @param {Object} user 用户信息
   * @param {string} sessionId 会话ID
   * @param {string} deviceId 设备ID
   * @returns {string} JWT访问令牌
   */
  generateAccessToken(user, sessionId = null, deviceId = null) {
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      sub: user.id.toString(),
      iat: now,
      exp: now + this.accessTokenTTL,
      aud: this.audience,
      iss: this.issuer,
      scope: this.getUserScopes(user),
      sessionId: sessionId || this.generateSessionId(),
      deviceId: deviceId || null,
      jti: this.generateJti() // JWT ID用于撤销
    };

    return jwt.sign(payload, this.accessTokenSecret, {
      algorithm: 'HS256'
    });
  }

  /**
   * 生成刷新令牌
   * @param {number} userId 用户ID
   * @returns {string} JWT刷新令牌
   */
  generateRefreshToken(userId) {
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      sub: userId.toString(),
      iat: now,
      exp: now + this.refreshTokenTTL,
      aud: this.audience,
      iss: this.issuer,
      type: 'refresh',
      jti: this.generateJti()
    };

    return jwt.sign(payload, this.refreshTokenSecret, {
      algorithm: 'HS256'
    });
  }

  /**
   * 验证访问令牌
   * @param {string} token 访问令牌
   * @returns {Object} 解码后的payload
   */
  async verifyAccessToken(token) {
    try {
      const payload = jwt.verify(token, this.accessTokenSecret, {
        audience: this.audience,
        issuer: this.issuer,
        algorithms: ['HS256']
      });

      // 检查令牌是否被撤销
      const isRevoked = await this.isTokenRevoked(payload.jti);
      if (isRevoked) {
        throw new Error('Token has been revoked');
      }

      return payload;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Access token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid access token');
      } else {
        throw error;
      }
    }
  }

  /**
   * 验证刷新令牌
   * @param {string} token 刷新令牌
   * @returns {Object} 解码后的payload
   */
  async verifyRefreshToken(token) {
    try {
      const payload = jwt.verify(token, this.refreshTokenSecret, {
        audience: this.audience,
        issuer: this.issuer,
        algorithms: ['HS256']
      });

      if (payload.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      // 检查令牌是否被撤销
      const isRevoked = await this.isTokenRevoked(payload.jti);
      if (isRevoked) {
        throw new Error('Refresh token has been revoked');
      }

      return payload;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Refresh token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid refresh token');
      } else {
        throw error;
      }
    }
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken 刷新令牌
   * @param {Object} user 用户信息
   * @returns {Object} 新的令牌对
   */
  async refreshAccessToken(refreshToken, user) {
    // 验证刷新令牌
    const payload = await this.verifyRefreshToken(refreshToken);
    
    if (payload.sub !== user.id.toString()) {
      throw new Error('Refresh token does not match user');
    }

    // 生成新的访问令牌
    const newAccessToken = this.generateAccessToken(user);
    
    // 可选：生成新的刷新令牌（实现令牌轮换）
    const newRefreshToken = this.generateRefreshToken(user.id);
    
    // 撤销旧的刷新令牌
    await this.revokeToken(payload.jti);
    
    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresIn: this.accessTokenTTL
    };
  }

  /**
   * 撤销令牌
   * @param {string} jti JWT ID
   * @param {number} ttl TTL(秒)
   */
  async revokeToken(jti, ttl = null) {
    const key = `revoked_token:${jti}`;
    const expiry = ttl || Math.max(this.accessTokenTTL, this.refreshTokenTTL);
    await redis.set(key, '1', expiry);
  }

  /**
   * 检查令牌是否被撤销
   * @param {string} jti JWT ID
   * @returns {boolean}
   */
  async isTokenRevoked(jti) {
    const key = `revoked_token:${jti}`;
    const value = await redis.get(key, false);
    return value !== null;
  }

  /**
   * 撤销用户的所有令牌
   * @param {number} userId 用户ID
   */
  async revokeAllUserTokens(userId) {
    const key = `user_token_version:${userId}`;
    const version = await redis.incr(key);
    
    // 设置版本号TTL
    await redis.expire(key, Math.max(this.accessTokenTTL, this.refreshTokenTTL));
    
    return version;
  }

  /**
   * 验证用户令牌版本
   * @param {number} userId 用户ID
   * @param {number} tokenVersion 令牌版本
   * @returns {boolean}
   */
  async validateTokenVersion(userId, tokenVersion) {
    const key = `user_token_version:${userId}`;
    const currentVersion = await redis.get(key, false);
    
    if (currentVersion === null) {
      return true; // 没有版本记录，令牌有效
    }
    
    return parseInt(currentVersion) === tokenVersion;
  }

  /**
   * 保存刷新令牌
   * @param {number} userId 用户ID
   * @param {string} refreshToken 刷新令牌
   * @param {string} deviceId 设备ID
   */
  async saveRefreshToken(userId, refreshToken, deviceId = null) {
    const payload = jwt.decode(refreshToken);
    const key = `refresh_token:${userId}:${deviceId || 'default'}`;
    
    const tokenData = {
      token: refreshToken,
      jti: payload.jti,
      deviceId: deviceId,
      createdAt: new Date().toISOString()
    };
    
    await redis.set(key, tokenData, this.refreshTokenTTL);
  }

  /**
   * 获取用户的刷新令牌
   * @param {number} userId 用户ID
   * @param {string} deviceId 设备ID
   * @returns {Object|null}
   */
  async getRefreshToken(userId, deviceId = null) {
    const key = `refresh_token:${userId}:${deviceId || 'default'}`;
    return await redis.get(key);
  }

  /**
   * 删除刷新令牌
   * @param {number} userId 用户ID
   * @param {string} deviceId 设备ID
   */
  async removeRefreshToken(userId, deviceId = null) {
    const key = `refresh_token:${userId}:${deviceId || 'default'}`;
    await redis.del(key);
  }

  /**
   * 获取用户权限范围
   * @param {Object} user 用户信息
   * @returns {string} 权限范围字符串
   */
  getUserScopes(user) {
    const baseScopes = ['user:read', 'game:play'];
    
    // 根据用户角色或状态添加权限
    if (user.status === 1) {
      baseScopes.push('user:write');
    }
    
    if (user.total_score > 10000) {
      baseScopes.push('premium:features');
    }
    
    // 管理员权限（如果有相关字段）
    if (user.role === 'admin') {
      baseScopes.push('admin:read', 'admin:write');
    }
    
    return baseScopes.join(' ');
  }

  /**
   * 检查权限
   * @param {string} userScopes 用户权限
   * @param {Array} requiredScopes 需要的权限
   * @returns {boolean}
   */
  hasRequiredScopes(userScopes, requiredScopes) {
    if (!requiredScopes || requiredScopes.length === 0) {
      return true;
    }
    
    const scopes = userScopes.split(' ');
    return requiredScopes.some(scope => scopes.includes(scope));
  }

  /**
   * 生成会话ID
   * @returns {string}
   */
  generateSessionId() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 生成JWT ID
   * @returns {string}
   */
  generateJti() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 解码令牌（不验证）
   * @param {string} token 令牌
   * @returns {Object|null}
   */
  decodeToken(token) {
    try {
      return jwt.decode(token);
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取令牌剩余时间
   * @param {string} token 令牌
   * @returns {number} 剩余秒数，-1表示已过期
   */
  getTokenTTL(token) {
    const payload = this.decodeToken(token);
    if (!payload || !payload.exp) {
      return -1;
    }
    
    const now = Math.floor(Date.now() / 1000);
    const remaining = payload.exp - now;
    
    return remaining > 0 ? remaining : -1;
  }

  /**
   * 创建令牌对
   * @param {Object} user 用户信息
   * @param {string} deviceId 设备ID
   * @returns {Object} 令牌对
   */
  async createTokenPair(user, deviceId = null) {
    const sessionId = this.generateSessionId();
    
    const accessToken = this.generateAccessToken(user, sessionId, deviceId);
    const refreshToken = this.generateRefreshToken(user.id);
    
    // 保存刷新令牌
    await this.saveRefreshToken(user.id, refreshToken, deviceId);
    
    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenTTL,
      tokenType: 'Bearer',
      scope: this.getUserScopes(user)
    };
  }

  /**
   * 清理过期的令牌记录
   */
  async cleanupExpiredTokens() {
    try {
      // 清理撤销令牌记录
      const revokedPattern = 'revoked_token:*';
      let cursor = 0;
      
      do {
        const result = await redis.scan(cursor, revokedPattern, 100);
        cursor = result.cursor;
        
        for (const key of result.keys) {
          const ttl = await redis.ttl(key);
          if (ttl <= 0) {
            await redis.del(key);
          }
        }
      } while (cursor !== 0);
      
      console.log('Token cleanup completed');
    } catch (error) {
      console.error('Token cleanup failed:', error);
    }
  }

  /**
   * 获取活跃会话统计
   * @param {number} userId 用户ID
   * @returns {Object}
   */
  async getActiveSessionStats(userId) {
    const pattern = `refresh_token:${userId}:*`;
    let cursor = 0;
    let activeTokens = 0;
    const devices = [];
    
    do {
      const result = await redis.scan(cursor, pattern, 100);
      cursor = result.cursor;
      
      for (const key of result.keys) {
        const tokenData = await redis.get(key);
        if (tokenData) {
          activeTokens++;
          if (tokenData.deviceId) {
            devices.push(tokenData.deviceId);
          }
        }
      }
    } while (cursor !== 0);
    
    return {
      activeTokens,
      devices: [...new Set(devices)],
      userId
    };
  }
}

// 全局Token管理器实例
const tokenManager = new TokenManager();

module.exports = tokenManager;