/**
 * 微信认证服务
 * 处理微信小程序登录和用户信息获取
 */

const axios = require('axios');
const crypto = require('crypto');
const config = require('../../config');
const User = require('../models/User');
const tokenManager = require('./TokenManager');

class WechatAuthService {
  constructor() {
    this.appId = config.wechat.appId;
    this.appSecret = config.wechat.appSecret;
    this.apiUrl = config.wechat.apiUrl;
  }

  /**
   * 微信小程序登录
   * @param {string} code 微信授权码
   * @param {string} encryptedData 加密用户数据(可选)
   * @param {string} iv 初始化向量(可选)
   * @param {string} deviceId 设备ID(可选)
   * @returns {Object} 登录结果
   */
  async login(code, encryptedData = null, iv = null, deviceId = null) {
    try {
      // 1. 通过code获取session_key和openid
      const sessionData = await this.getSessionKey(code);
      
      // 2. 解密用户信息（如果提供了加密数据）
      let userInfo = null;
      if (encryptedData && iv && sessionData.session_key) {
        try {
          userInfo = this.decryptUserInfo(
            sessionData.session_key, 
            encryptedData, 
            iv
          );
        } catch (decryptError) {
          console.warn('Failed to decrypt user info:', decryptError.message);
          // 解密失败不影响登录流程
        }
      }
      
      // 3. 查找或创建用户
      let user = await User.findByOpenId(sessionData.openid);
      
      if (!user) {
        // 创建新用户
        const userData = {
          openid: sessionData.openid,
          unionid: sessionData.unionid || null
        };
        
        // 如果有解密的用户信息，添加到用户数据中
        if (userInfo) {
          userData.nickname = userInfo.nickName || '';
          userData.avatar_url = userInfo.avatarUrl || null;
          userData.gender = userInfo.gender || 0;
          userData.province = userInfo.province || null;
          userData.city = userInfo.city || null;
          userData.country = userInfo.country || null;
          userData.language = userInfo.language || 'zh_CN';
        }
        
        user = await User.create(userData);
      } else {
        // 更新现有用户信息
        const updates = { last_login_at: new Date() };
        
        if (userInfo) {
          // 只更新非空的字段
          if (userInfo.nickName) updates.nickname = userInfo.nickName;
          if (userInfo.avatarUrl) updates.avatar_url = userInfo.avatarUrl;
          if (userInfo.gender !== undefined) updates.gender = userInfo.gender;
          if (userInfo.province) updates.province = userInfo.province;
          if (userInfo.city) updates.city = userInfo.city;
          if (userInfo.country) updates.country = userInfo.country;
          if (userInfo.language) updates.language = userInfo.language;
        }
        
        await user.update(updates);
      }
      
      // 4. 生成Token对
      const tokens = await tokenManager.createTokenPair(user, deviceId);
      
      return {
        ...tokens,
        user: user.toJSON(true), // 包含私有信息
        isNewUser: !await User.findByOpenId(sessionData.openid) // 是否为新用户
      };
      
    } catch (error) {
      console.error('WeChat login failed:', error);
      
      // 根据错误类型抛出不同的错误
      if (error.response && error.response.data) {
        const { errcode, errmsg } = error.response.data;
        throw new Error(`微信API错误 (${errcode}): ${errmsg}`);
      }
      
      throw new Error('微信登录失败，请重试');
    }
  }

  /**
   * 获取微信session_key
   * @param {string} code 授权码
   * @returns {Object} 会话数据
   */
  async getSessionKey(code) {
    const url = `${this.apiUrl}/sns/jscode2session`;
    
    const params = {
      appid: this.appId,
      secret: this.appSecret,
      js_code: code,
      grant_type: 'authorization_code'
    };
    
    try {
      const response = await axios.get(url, { 
        params,
        timeout: 10000 // 10秒超时
      });
      
      const data = response.data;
      
      // 检查微信API返回的错误
      if (data.errcode) {
        const errorMessages = {
          40029: '无效的code',
          45011: 'API调用太频繁',
          40013: '无效的AppID',
          40125: '无效的密钥'
        };
        
        const message = errorMessages[data.errcode] || data.errmsg || '未知错误';
        throw new Error(`微信API错误 (${data.errcode}): ${message}`);
      }
      
      // 验证必需字段
      if (!data.openid) {
        throw new Error('微信API返回数据异常：缺少openid');
      }
      
      return {
        openid: data.openid,
        session_key: data.session_key,
        unionid: data.unionid || null
      };
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('微信服务请求超时，请重试');
      }
      
      if (error.response) {
        throw new Error(`微信服务异常 (${error.response.status})`);
      }
      
      throw error;
    }
  }

  /**
   * 解密微信用户信息
   * @param {string} sessionKey 会话密钥
   * @param {string} encryptedData 加密数据
   * @param {string} iv 初始化向量
   * @returns {Object} 解密后的用户信息
   */
  decryptUserInfo(sessionKey, encryptedData, iv) {
    try {
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      
      // 使用AES-128-CBC解密
      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);
      
      let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
      decrypted += decipher.final('utf8');
      
      const userInfo = JSON.parse(decrypted);
      
      // 验证水印
      if (userInfo.watermark) {
        if (userInfo.watermark.appid !== this.appId) {
          throw new Error('用户信息水印验证失败');
        }
      }
      
      return userInfo;
      
    } catch (error) {
      console.error('Decrypt user info failed:', error);
      throw new Error('用户信息解密失败');
    }
  }

  /**
   * 获取微信Access Token（服务端调用）
   * @returns {string} Access Token
   */
  async getAccessToken() {
    const url = `${this.apiUrl}/cgi-bin/token`;
    
    const params = {
      grant_type: 'client_credential',
      appid: this.appId,
      secret: this.appSecret
    };
    
    try {
      const response = await axios.get(url, { params });
      const data = response.data;
      
      if (data.errcode) {
        throw new Error(`获取Access Token失败: ${data.errmsg}`);
      }
      
      return data.access_token;
      
    } catch (error) {
      console.error('Get access token failed:', error);
      throw error;
    }
  }

  /**
   * 验证用户手机号（需要用户授权）
   * @param {string} code 手机号授权码
   * @returns {Object} 手机号信息
   */
  async getPhoneNumber(code) {
    try {
      const accessToken = await this.getAccessToken();
      const url = `${this.apiUrl}/wxa/business/getuserphonenumber?access_token=${accessToken}`;
      
      const response = await axios.post(url, { code });
      const data = response.data;
      
      if (data.errcode !== 0) {
        throw new Error(`获取手机号失败: ${data.errmsg}`);
      }
      
      return data.phone_info;
      
    } catch (error) {
      console.error('Get phone number failed:', error);
      throw error;
    }
  }

  /**
   * 刷新Token
   * @param {string} refreshToken 刷新令牌
   * @returns {Object} 新的Token对
   */
  async refreshToken(refreshToken) {
    try {
      // 验证刷新令牌
      const payload = await tokenManager.verifyRefreshToken(refreshToken);
      
      // 获取用户信息
      const user = await User.findById(payload.sub);
      if (!user) {
        throw new Error('用户不存在');
      }
      
      // 生成新的Token对
      const tokens = await tokenManager.refreshAccessToken(refreshToken, user);
      
      return {
        ...tokens,
        user: user.toJSON(false) // 不包含私有信息
      };
      
    } catch (error) {
      console.error('Refresh token failed:', error);
      throw new Error('Token刷新失败');
    }
  }

  /**
   * 退出登录
   * @param {number} userId 用户ID
   * @param {string} deviceId 设备ID
   * @returns {boolean} 是否成功
   */
  async logout(userId, deviceId = null) {
    try {
      // 移除刷新令牌
      await tokenManager.removeRefreshToken(userId, deviceId);
      
      return true;
    } catch (error) {
      console.error('Logout failed:', error);
      return false;
    }
  }

  /**
   * 撤销所有会话
   * @param {number} userId 用户ID
   * @returns {boolean} 是否成功
   */
  async logoutAll(userId) {
    try {
      // 撤销用户的所有令牌
      await tokenManager.revokeAllUserTokens(userId);
      
      return true;
    } catch (error) {
      console.error('Logout all failed:', error);
      return false;
    }
  }

  /**
   * 验证微信签名（用于消息推送等）
   * @param {string} signature 微信签名
   * @param {string} timestamp 时间戳
   * @param {string} nonce 随机数
   * @param {string} token 验证令牌
   * @returns {boolean} 验证结果
   */
  verifySignature(signature, timestamp, nonce, token) {
    const tmpArr = [token, timestamp, nonce].sort();
    const tmpStr = tmpArr.join('');
    const hash = crypto.createHash('sha1').update(tmpStr).digest('hex');
    
    return hash === signature;
  }

  /**
   * 获取用户信息（通过UnionID查询）
   * @param {string} unionid UnionID
   * @returns {User|null} 用户信息
   */
  async getUserByUnionId(unionid) {
    if (!unionid) return null;
    return await User.findByUnionId(unionid);
  }

  /**
   * 绑定UnionID
   * @param {number} userId 用户ID
   * @param {string} unionid UnionID
   * @returns {boolean} 是否成功
   */
  async bindUnionId(userId, unionid) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }
      
      // 检查UnionID是否已被其他用户使用
      const existingUser = await User.findByUnionId(unionid);
      if (existingUser && existingUser.id !== userId) {
        throw new Error('UnionID已被其他用户使用');
      }
      
      await user.update({ unionid });
      return true;
      
    } catch (error) {
      console.error('Bind UnionID failed:', error);
      throw error;
    }
  }

  /**
   * 获取登录统计信息
   * @param {number} userId 用户ID
   * @returns {Object} 统计信息
   */
  async getLoginStats(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }
      
      const sessionStats = await tokenManager.getActiveSessionStats(userId);
      
      return {
        userId,
        lastLoginAt: user.last_login_at,
        createdAt: user.created_at,
        activeSessions: sessionStats.activeTokens,
        devices: sessionStats.devices
      };
      
    } catch (error) {
      console.error('Get login stats failed:', error);
      throw error;
    }
  }
}

// 全局微信认证服务实例
const wechatAuthService = new WechatAuthService();

module.exports = wechatAuthService;