/**
 * AI服务处理器
 * 处理内容审核、质量评估、智能推荐等AI功能
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { AIModerationService } = require('./aiModerationService');
const { AIRecommendationService } = require('./aiRecommendationService');
const { logger } = require('../utils/logger');

class AIHandler {
  constructor() {
    this.moderationService = new AIModerationService();
    this.recommendationService = new AIRecommendationService();
  }

  /**
   * 审核单个内容
   */
  async moderateContent(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'moderateContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const { force = false } = event.queryStringParameters || {};

      if (!contentId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID is required'
          })
        };
      }

      // 检查权限（只有管理员或内容创建者可以触发审核）
      const userId = event.user?.id;
      const userRole = event.user?.role;
      
      if (!userId || (userRole !== 'admin' && userRole !== 'moderator')) {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const result = await this.moderationService.moderateContent(contentId, {
        force: force === 'true'
      });

      aiLogger.info('Content moderation completed', {
        contentId,
        status: result.overallStatus,
        confidence: result.confidence
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      aiLogger.error('Content moderation failed', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Content moderation failed'
        })
      };
    }
  }

  /**
   * 批量审核内容
   */
  async batchModerateContent(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'batchModerateContent'
    });

    try {
      const { contentIds } = JSON.parse(event.body || '{}');

      if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content IDs array is required'
          })
        };
      }

      // 检查权限
      const userRole = event.user?.role;
      if (userRole !== 'admin' && userRole !== 'moderator') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      // 限制批量处理数量
      if (contentIds.length > 50) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Maximum 50 contents can be processed at once'
          })
        };
      }

      const results = await this.moderationService.batchModerateContent(contentIds);

      aiLogger.info('Batch content moderation completed', {
        totalContent: contentIds.length,
        successCount: results.filter(r => r.overallStatus !== 'error').length,
        errorCount: results.filter(r => r.overallStatus === 'error').length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: {
            results,
            summary: {
              total: contentIds.length,
              approved: results.filter(r => r.overallStatus === 'approved').length,
              rejected: results.filter(r => r.overallStatus === 'rejected').length,
              flagged: results.filter(r => r.overallStatus === 'flagged').length,
              errors: results.filter(r => r.overallStatus === 'error').length
            }
          }
        })
      };

    } catch (error) {
      aiLogger.error('Batch content moderation failed', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Batch content moderation failed'
        })
      };
    }
  }

  /**
   * 获取内容推荐
   */
  async getContentRecommendations(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'getContentRecommendations'
    });

    try {
      const userId = event.user?.id;
      const {
        limit = 20,
        type = 'personalized',
        dialectRegion,
        contentType,
        excludeViewed = true
      } = event.queryStringParameters || {};

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const recommendations = await this.recommendationService.getRecommendations({
        userId,
        limit: parseInt(limit),
        type,
        dialectRegion,
        contentType,
        excludeViewed: excludeViewed === 'true'
      });

      aiLogger.debug('Content recommendations generated', {
        userId,
        recommendationCount: recommendations.length,
        type,
        dialectRegion
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: recommendations
        })
      };

    } catch (error) {
      aiLogger.error('Failed to get content recommendations', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get content recommendations'
        })
      };
    }
  }

  /**
   * 获取相似内容
   */
  async getSimilarContent(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'getSimilarContent'
    });

    try {
      const { contentId } = event.pathParameters;
      const { limit = 10 } = event.queryStringParameters || {};

      if (!contentId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Content ID is required'
          })
        };
      }

      const similarContent = await this.recommendationService.getSimilarContent(
        contentId,
        parseInt(limit)
      );

      aiLogger.debug('Similar content retrieved', {
        contentId,
        similarCount: similarContent.length,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: similarContent
        })
      };

    } catch (error) {
      aiLogger.error('Failed to get similar content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get similar content'
        })
      };
    }
  }

  /**
   * 获取热门内容
   */
  async getTrendingContent(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'getTrendingContent'
    });

    try {
      const {
        limit = 20,
        timeRange = '7d',
        dialectRegion,
        contentType
      } = event.queryStringParameters || {};

      const trendingContent = await this.recommendationService.getTrendingContent({
        limit: parseInt(limit),
        timeRange,
        dialectRegion,
        contentType
      });

      aiLogger.debug('Trending content retrieved', {
        trendingCount: trendingContent.length,
        timeRange,
        dialectRegion,
        contentType
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: trendingContent
        })
      };

    } catch (error) {
      aiLogger.error('Failed to get trending content', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get trending content'
        })
      };
    }
  }

  /**
   * 分析用户兴趣
   */
  async analyzeUserInterests(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'analyzeUserInterests'
    });

    try {
      const userId = event.user?.id;

      if (!userId) {
        return {
          statusCode: 401,
          body: JSON.stringify({
            error: 'Authentication required'
          })
        };
      }

      const interests = await this.recommendationService.analyzeUserInterests(userId);

      aiLogger.debug('User interests analyzed', {
        userId,
        interestCount: interests.length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: interests
        })
      };

    } catch (error) {
      aiLogger.error('Failed to analyze user interests', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to analyze user interests'
        })
      };
    }
  }

  /**
   * 更新推荐模型
   */
  async updateRecommendationModel(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'updateRecommendationModel'
    });

    try {
      // 检查权限
      const userRole = event.user?.role;
      if (userRole !== 'admin') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const { modelType = 'all' } = JSON.parse(event.body || '{}');

      const result = await this.recommendationService.updateModel(modelType);

      aiLogger.info('Recommendation model updated', {
        modelType,
        result
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      aiLogger.error('Failed to update recommendation model', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to update recommendation model'
        })
      };
    }
  }

  /**
   * 获取AI服务统计
   */
  async getAIStats(event, context) {
    const aiLogger = logger.child({
      requestId: context.requestId,
      action: 'getAIStats'
    });

    try {
      // 检查权限
      const userRole = event.user?.role;
      if (userRole !== 'admin' && userRole !== 'moderator') {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied'
          })
        };
      }

      const {
        timeRange = '7d',
        service = 'all'
      } = event.queryStringParameters || {};

      const stats = await this.getServiceStats(timeRange, service);

      aiLogger.debug('AI service stats retrieved', {
        timeRange,
        service,
        statsCount: Object.keys(stats).length
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      aiLogger.error('Failed to get AI service stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get AI service stats'
        })
      };
    }
  }

  /**
   * 获取服务统计数据
   */
  async getServiceStats(timeRange, service) {
    // 这里应该从监控系统或数据库获取统计数据
    // 简化实现，返回模拟数据
    const stats = {
      moderation: {
        totalRequests: 1250,
        approvedCount: 1000,
        rejectedCount: 150,
        flaggedCount: 100,
        averageProcessingTime: 2.5,
        accuracyRate: 0.95
      },
      recommendation: {
        totalRequests: 5000,
        clickThroughRate: 0.15,
        averageRecommendations: 18,
        userSatisfactionScore: 4.2,
        modelAccuracy: 0.78
      },
      transcription: {
        totalRequests: 800,
        successRate: 0.92,
        averageAccuracy: 0.88,
        averageProcessingTime: 15.2
      },
      sentiment: {
        totalRequests: 2000,
        positiveRatio: 0.65,
        negativeRatio: 0.20,
        neutralRatio: 0.15,
        averageConfidence: 0.82
      }
    };

    if (service !== 'all') {
      return { [service]: stats[service] };
    }

    return stats;
  }
}

// 创建处理器实例
const aiHandler = new AIHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  moderateContent: withMiddleware(aiHandler.moderateContent.bind(aiHandler)),
  batchModerateContent: withMiddleware(aiHandler.batchModerateContent.bind(aiHandler)),
  getContentRecommendations: withMiddleware(aiHandler.getContentRecommendations.bind(aiHandler)),
  getSimilarContent: withMiddleware(aiHandler.getSimilarContent.bind(aiHandler)),
  getTrendingContent: withMiddleware(aiHandler.getTrendingContent.bind(aiHandler)),
  analyzeUserInterests: withMiddleware(aiHandler.analyzeUserInterests.bind(aiHandler)),
  updateRecommendationModel: withMiddleware(aiHandler.updateRecommendationModel.bind(aiHandler)),
  getAIStats: withMiddleware(aiHandler.getAIStats.bind(aiHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'POST /v1/ai/moderation/{contentId}': aiHandler.moderateContent.bind(aiHandler),
      'POST /v1/ai/moderation/batch': aiHandler.batchModerateContent.bind(aiHandler),
      'GET /v1/ai/recommendations': aiHandler.getContentRecommendations.bind(aiHandler),
      'GET /v1/ai/content/{contentId}/similar': aiHandler.getSimilarContent.bind(aiHandler),
      'GET /v1/ai/trending': aiHandler.getTrendingContent.bind(aiHandler),
      'GET /v1/ai/users/interests': aiHandler.analyzeUserInterests.bind(aiHandler),
      'POST /v1/ai/models/update': aiHandler.updateRecommendationModel.bind(aiHandler),
      'GET /v1/ai/stats': aiHandler.getAIStats.bind(aiHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
