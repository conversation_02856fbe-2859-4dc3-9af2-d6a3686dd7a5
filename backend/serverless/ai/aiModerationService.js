/**
 * AI内容审核服务
 * 集成腾讯云AI服务，实现内容审核、质量评估、智能推荐
 */

const { logger } = require('../utils/logger');
const { DatabaseManager } = require('../utils/database');
const { RedisManager } = require('../utils/redis');
const { performanceMonitor } = require('../utils/monitoring');

// 腾讯云AI服务SDK
const tencentcloud = require('tencentcloud-sdk-nodejs');

class AIModerationService {
  constructor() {
    this.db = DatabaseManager.getInstance();
    this.redis = RedisManager.getInstance();
    
    // 初始化腾讯云AI客户端
    this.initTencentCloudClients();
    
    // 缓存配置
    this.cachePrefix = 'ai:moderation:';
    this.cacheTTL = 3600; // 1小时
    
    // 审核阈值配置
    this.moderationThresholds = {
      text: {
        porn: 0.8,
        terrorism: 0.8,
        politics: 0.8,
        ads: 0.7,
        abuse: 0.7
      },
      audio: {
        porn: 0.8,
        terrorism: 0.8,
        politics: 0.8,
        ads: 0.7
      },
      image: {
        porn: 0.8,
        terrorism: 0.8,
        politics: 0.8,
        ads: 0.7
      }
    };
  }

  /**
   * 初始化腾讯云AI客户端
   */
  initTencentCloudClients() {
    const clientConfig = {
      credential: {
        secretId: process.env.TENCENT_SECRET_ID,
        secretKey: process.env.TENCENT_SECRET_KEY,
      },
      region: process.env.TENCENT_REGION || 'ap-beijing',
      profile: {
        httpProfile: {
          endpoint: 'tms.tencentcloudapi.com',
        },
      },
    };

    // 文本内容安全客户端
    const TmsClient = tencentcloud.tms.v20201229.Client;
    this.tmsClient = new TmsClient(clientConfig);

    // 音频内容安全客户端
    const AmsClient = tencentcloud.ams.v20201229.Client;
    this.amsClient = new AmsClient(clientConfig);

    // 图像内容安全客户端
    const ImsClient = tencentcloud.ims.v20201229.Client;
    this.imsClient = new ImsClient(clientConfig);

    // 语音识别客户端
    const AsrClient = tencentcloud.asr.v20190614.Client;
    this.asrClient = new AsrClient(clientConfig);

    // 自然语言处理客户端
    const NlpClient = tencentcloud.nlp.v20190408.Client;
    this.nlpClient = new NlpClient(clientConfig);

    logger.info('Tencent Cloud AI clients initialized');
  }

  /**
   * 审核UGC内容
   */
  async moderateContent(contentId, contentData) {
    const startTime = Date.now();
    
    try {
      logger.info('Starting content moderation', {
        contentId,
        contentType: contentData.contentType
      });

      // 检查缓存
      const cacheKey = `${this.cachePrefix}${contentId}`;
      const cachedResult = await this.redis.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      let moderationResult = {
        contentId,
        contentType: contentData.contentType,
        overallStatus: 'approved',
        confidence: 1.0,
        details: {},
        aiScores: {},
        recommendations: [],
        processedAt: new Date().toISOString()
      };

      // 根据内容类型进行不同的审核
      switch (contentData.contentType) {
        case 'text':
          moderationResult = await this.moderateTextContent(contentData, moderationResult);
          break;
        case 'audio':
          moderationResult = await this.moderateAudioContent(contentData, moderationResult);
          break;
        case 'image':
          moderationResult = await this.moderateImageContent(contentData, moderationResult);
          break;
        case 'video':
          moderationResult = await this.moderateVideoContent(contentData, moderationResult);
          break;
        case 'mixed':
          moderationResult = await this.moderateMixedContent(contentData, moderationResult);
          break;
        default:
          throw new Error(`Unsupported content type: ${contentData.contentType}`);
      }

      // 计算质量评分
      moderationResult.qualityScore = await this.calculateQualityScore(contentData, moderationResult);

      // 生成智能推荐
      moderationResult.recommendations = await this.generateRecommendations(contentData, moderationResult);

      // 缓存结果
      await this.redis.setex(cacheKey, this.cacheTTL, JSON.stringify(moderationResult));

      // 保存审核历史
      await this.saveModerationHistory(contentId, moderationResult);

      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'ai_content_moderation',
        contentData.contentType,
        duration,
        true
      );

      logger.info('Content moderation completed', {
        contentId,
        status: moderationResult.overallStatus,
        confidence: moderationResult.confidence,
        duration: `${duration}ms`
      });

      return moderationResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      await performanceMonitor.recordDatabaseMetrics(
        'ai_content_moderation',
        contentData.contentType,
        duration,
        false,
        error
      );

      logger.error('Content moderation failed', {
        contentId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 审核文本内容
   */
  async moderateTextContent(contentData, moderationResult) {
    try {
      const textContent = contentData.title + ' ' + (contentData.description || '');
      
      // 腾讯云文本内容安全检测
      const params = {
        Content: textContent,
        BizType: 'dialect_ugc',
        DataId: contentData.id
      };

      const response = await this.tmsClient.TextModeration(params);
      const result = response.Data;

      moderationResult.details.textModeration = {
        suggestion: result.Suggestion,
        label: result.Label,
        score: result.Score,
        keywords: result.Keywords || [],
        sections: result.DetailResults || []
      };

      // 检查是否违规
      if (result.Suggestion === 'Block') {
        moderationResult.overallStatus = 'rejected';
        moderationResult.confidence = result.Score / 100;
      } else if (result.Suggestion === 'Review') {
        moderationResult.overallStatus = 'flagged';
        moderationResult.confidence = result.Score / 100;
      }

      // 情感分析
      const sentimentResult = await this.analyzeSentiment(textContent);
      moderationResult.details.sentiment = sentimentResult;

      // 关键词提取
      const keywordsResult = await this.extractKeywords(textContent);
      moderationResult.details.keywords = keywordsResult;

      return moderationResult;

    } catch (error) {
      logger.error('Text moderation failed', {
        contentId: contentData.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 审核音频内容
   */
  async moderateAudioContent(contentData, moderationResult) {
    try {
      if (!contentData.primaryFileUrl) {
        throw new Error('Audio file URL is required');
      }

      // 音频内容安全检测
      const params = {
        Type: 1, // URL方式
        Url: contentData.primaryFileUrl,
        BizType: 'dialect_ugc',
        DataId: contentData.id
      };

      const response = await this.amsClient.CreateAudioModerationTask(params);
      const taskId = response.Data.TaskId;

      // 轮询获取审核结果
      const audioResult = await this.pollAudioModerationResult(taskId);
      
      moderationResult.details.audioModeration = audioResult;

      if (audioResult.Suggestion === 'Block') {
        moderationResult.overallStatus = 'rejected';
        moderationResult.confidence = audioResult.Score / 100;
      } else if (audioResult.Suggestion === 'Review') {
        moderationResult.overallStatus = 'flagged';
        moderationResult.confidence = audioResult.Score / 100;
      }

      // 语音识别转文本
      const transcriptionResult = await this.transcribeAudio(contentData.primaryFileUrl);
      moderationResult.details.transcription = transcriptionResult;

      // 如果有转录文本，进行文本审核
      if (transcriptionResult.text) {
        const textModerationResult = await this.moderateTextContent({
          ...contentData,
          title: transcriptionResult.text,
          description: ''
        }, { details: {} });
        
        moderationResult.details.transcriptionModeration = textModerationResult.details.textModeration;
        
        // 更新整体状态
        if (textModerationResult.overallStatus === 'rejected') {
          moderationResult.overallStatus = 'rejected';
        } else if (textModerationResult.overallStatus === 'flagged' && moderationResult.overallStatus === 'approved') {
          moderationResult.overallStatus = 'flagged';
        }
      }

      return moderationResult;

    } catch (error) {
      logger.error('Audio moderation failed', {
        contentId: contentData.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 审核图片内容
   */
  async moderateImageContent(contentData, moderationResult) {
    try {
      if (!contentData.primaryFileUrl) {
        throw new Error('Image file URL is required');
      }

      const params = {
        Type: 1, // URL方式
        Url: contentData.primaryFileUrl,
        BizType: 'dialect_ugc',
        DataId: contentData.id
      };

      const response = await this.imsClient.ImageModeration(params);
      const result = response.Data;

      moderationResult.details.imageModeration = {
        suggestion: result.Suggestion,
        label: result.Label,
        score: result.Score,
        subLabel: result.SubLabel,
        ocrResults: result.OcrResults || [],
        objectResults: result.ObjectResults || []
      };

      if (result.Suggestion === 'Block') {
        moderationResult.overallStatus = 'rejected';
        moderationResult.confidence = result.Score / 100;
      } else if (result.Suggestion === 'Review') {
        moderationResult.overallStatus = 'flagged';
        moderationResult.confidence = result.Score / 100;
      }

      // 如果图片中有文字，进行OCR文本审核
      if (result.OcrResults && result.OcrResults.length > 0) {
        const ocrText = result.OcrResults.map(ocr => ocr.Text).join(' ');
        const textModerationResult = await this.moderateTextContent({
          ...contentData,
          title: ocrText,
          description: ''
        }, { details: {} });
        
        moderationResult.details.ocrModeration = textModerationResult.details.textModeration;
      }

      return moderationResult;

    } catch (error) {
      logger.error('Image moderation failed', {
        contentId: contentData.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 审核视频内容
   */
  async moderateVideoContent(contentData, moderationResult) {
    try {
      // 视频审核通常需要先提取关键帧和音频
      // 这里简化处理，实际应该调用视频内容安全API
      
      // 提取视频缩略图进行图片审核
      if (contentData.thumbnailUrl) {
        const imageResult = await this.moderateImageContent({
          ...contentData,
          primaryFileUrl: contentData.thumbnailUrl
        }, { details: {} });
        
        moderationResult.details.thumbnailModeration = imageResult.details.imageModeration;
        
        if (imageResult.overallStatus === 'rejected') {
          moderationResult.overallStatus = 'rejected';
        } else if (imageResult.overallStatus === 'flagged') {
          moderationResult.overallStatus = 'flagged';
        }
      }

      // 如果视频有音频轨道，进行音频审核
      const audioResult = await this.moderateAudioContent(contentData, { details: {} });
      moderationResult.details.audioTrackModeration = audioResult.details.audioModeration;
      
      if (audioResult.overallStatus === 'rejected') {
        moderationResult.overallStatus = 'rejected';
      } else if (audioResult.overallStatus === 'flagged' && moderationResult.overallStatus === 'approved') {
        moderationResult.overallStatus = 'flagged';
      }

      return moderationResult;

    } catch (error) {
      logger.error('Video moderation failed', {
        contentId: contentData.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 审核混合内容
   */
  async moderateMixedContent(contentData, moderationResult) {
    try {
      // 对每种类型的内容分别审核
      const textResult = await this.moderateTextContent(contentData, { details: {} });
      moderationResult.details.textModeration = textResult.details.textModeration;

      if (contentData.primaryFileUrl) {
        // 根据文件扩展名判断类型
        const fileExtension = contentData.primaryFileUrl.split('.').pop().toLowerCase();
        
        if (['mp3', 'wav', 'aac', 'm4a'].includes(fileExtension)) {
          const audioResult = await this.moderateAudioContent(contentData, { details: {} });
          moderationResult.details.audioModeration = audioResult.details.audioModeration;
        } else if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension)) {
          const imageResult = await this.moderateImageContent(contentData, { details: {} });
          moderationResult.details.imageModeration = imageResult.details.imageModeration;
        } else if (['mp4', 'avi', 'mov', 'wmv'].includes(fileExtension)) {
          const videoResult = await this.moderateVideoContent(contentData, { details: {} });
          moderationResult.details.videoModeration = videoResult.details.videoModeration;
        }
      }

      // 综合判断最终状态
      const allResults = Object.values(moderationResult.details);
      const hasRejected = allResults.some(result => 
        result.textModeration?.suggestion === 'Block' ||
        result.audioModeration?.suggestion === 'Block' ||
        result.imageModeration?.suggestion === 'Block'
      );
      
      const hasFlagged = allResults.some(result => 
        result.textModeration?.suggestion === 'Review' ||
        result.audioModeration?.suggestion === 'Review' ||
        result.imageModeration?.suggestion === 'Review'
      );

      if (hasRejected) {
        moderationResult.overallStatus = 'rejected';
      } else if (hasFlagged) {
        moderationResult.overallStatus = 'flagged';
      }

      return moderationResult;

    } catch (error) {
      logger.error('Mixed content moderation failed', {
        contentId: contentData.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 轮询音频审核结果
   */
  async pollAudioModerationResult(taskId, maxAttempts = 30) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const params = { TaskId: taskId };
        const response = await this.amsClient.DescribeTaskDetail(params);
        const result = response.Data;

        if (result.Status === 'FINISH') {
          return result.Result;
        } else if (result.Status === 'ERROR') {
          throw new Error(`Audio moderation task failed: ${result.ErrorInfo}`);
        }

        // 等待2秒后重试
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        if (attempt === maxAttempts - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    throw new Error('Audio moderation task timeout');
  }

  /**
   * 语音转文本
   */
  async transcribeAudio(audioUrl) {
    try {
      const params = {
        EngineModelType: '16k_zh',
        ChannelNum: 1,
        ResTextFormat: 0,
        SourceType: 0,
        Url: audioUrl
      };

      const response = await this.asrClient.CreateRecTask(params);
      const taskId = response.Data.TaskId;

      // 轮询获取识别结果
      return await this.pollTranscriptionResult(taskId);

    } catch (error) {
      logger.error('Audio transcription failed', {
        audioUrl,
        error: error.message
      });
      return { text: '', confidence: 0 };
    }
  }

  /**
   * 轮询语音识别结果
   */
  async pollTranscriptionResult(taskId, maxAttempts = 30) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const params = { TaskId: taskId };
        const response = await this.asrClient.DescribeTaskStatus(params);
        const result = response.Data;

        if (result.StatusStr === 'success') {
          return {
            text: result.Result,
            confidence: 1.0
          };
        } else if (result.StatusStr === 'failed') {
          throw new Error(`Transcription task failed: ${result.ErrorMsg}`);
        }

        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        if (attempt === maxAttempts - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return { text: '', confidence: 0 };
  }

  /**
   * 情感分析
   */
  async analyzeSentiment(text) {
    try {
      const params = { Text: text };
      const response = await this.nlpClient.SentimentAnalysis(params);
      
      return {
        sentiment: response.Sentiment,
        positive: response.Positive,
        negative: response.Negative,
        neutral: response.Neutral
      };

    } catch (error) {
      logger.error('Sentiment analysis failed', {
        error: error.message
      });
      return { sentiment: 'neutral', positive: 0, negative: 0, neutral: 1 };
    }
  }

  /**
   * 关键词提取
   */
  async extractKeywords(text) {
    try {
      const params = { Text: text, Num: 10 };
      const response = await this.nlpClient.KeywordsExtraction(params);
      
      return response.Keywords || [];

    } catch (error) {
      logger.error('Keywords extraction failed', {
        error: error.message
      });
      return [];
    }
  }

  /**
   * 计算内容质量评分
   */
  async calculateQualityScore(contentData, moderationResult) {
    try {
      let qualityScore = 5.0; // 基础分数

      // 根据审核结果调整分数
      if (moderationResult.overallStatus === 'rejected') {
        qualityScore = 1.0;
      } else if (moderationResult.overallStatus === 'flagged') {
        qualityScore = 3.0;
      }

      // 根据内容完整性调整分数
      if (contentData.title && contentData.title.length > 10) {
        qualityScore += 0.5;
      }
      if (contentData.description && contentData.description.length > 50) {
        qualityScore += 0.5;
      }
      if (contentData.tags && contentData.tags.length > 0) {
        qualityScore += 0.3;
      }

      // 根据文件质量调整分数
      if (contentData.primaryFileSize) {
        if (contentData.primaryFileSize > 1024 * 1024) { // 大于1MB
          qualityScore += 0.2;
        }
      }

      // 根据时长调整分数（音频/视频）
      if (contentData.primaryFileDuration) {
        if (contentData.primaryFileDuration >= 30 && contentData.primaryFileDuration <= 300) {
          qualityScore += 0.3; // 30秒到5分钟的内容加分
        }
      }

      // 确保分数在1-10范围内
      return Math.max(1.0, Math.min(10.0, qualityScore));

    } catch (error) {
      logger.error('Quality score calculation failed', {
        contentId: contentData.id,
        error: error.message
      });
      return 5.0; // 默认分数
    }
  }

  /**
   * 生成智能推荐
   */
  async generateRecommendations(contentData, moderationResult) {
    const recommendations = [];

    try {
      // 基于审核结果的推荐
      if (moderationResult.overallStatus === 'flagged') {
        recommendations.push({
          type: 'moderation',
          priority: 'high',
          message: '内容需要人工审核',
          action: 'manual_review'
        });
      }

      // 基于质量评分的推荐
      if (moderationResult.qualityScore < 6.0) {
        recommendations.push({
          type: 'quality',
          priority: 'medium',
          message: '建议完善内容描述和标签',
          action: 'improve_metadata'
        });
      }

      // 基于内容类型的推荐
      if (contentData.contentType === 'audio' && !contentData.description) {
        recommendations.push({
          type: 'content',
          priority: 'low',
          message: '建议添加音频内容描述',
          action: 'add_description'
        });
      }

      // 基于方言地区的推荐
      const dialectStats = await this.getDialectContentStats(contentData.dialectRegion);
      if (dialectStats.contentCount < 10) {
        recommendations.push({
          type: 'promotion',
          priority: 'medium',
          message: `${contentData.dialectRegion}方言内容较少，建议推荐`,
          action: 'promote_content'
        });
      }

      return recommendations;

    } catch (error) {
      logger.error('Recommendations generation failed', {
        contentId: contentData.id,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 获取方言内容统计
   */
  async getDialectContentStats(dialectRegion) {
    const connection = await this.db.getConnection();
    
    try {
      const [stats] = await connection.execute(`
        SELECT 
          COUNT(*) as contentCount,
          AVG(quality_score) as avgQuality,
          SUM(view_count) as totalViews
        FROM ugc_content 
        WHERE dialect_region = ? AND status = 'published'
      `, [dialectRegion]);

      return stats[0] || { contentCount: 0, avgQuality: 0, totalViews: 0 };

    } catch (error) {
      logger.error('Failed to get dialect content stats', {
        dialectRegion,
        error: error.message
      });
      return { contentCount: 0, avgQuality: 0, totalViews: 0 };
    } finally {
      connection.release();
    }
  }

  /**
   * 保存审核历史
   */
  async saveModerationHistory(contentId, moderationResult) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.execute(`
        INSERT INTO ugc_moderation_history (
          content_id, moderator_id, new_status, reason, 
          moderation_data, ai_confidence, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        contentId,
        'ai_system',
        moderationResult.overallStatus,
        'AI自动审核',
        JSON.stringify(moderationResult),
        moderationResult.confidence
      ]);

    } catch (error) {
      logger.error('Failed to save moderation history', {
        contentId,
        error: error.message
      });
    } finally {
      connection.release();
    }
  }

  /**
   * 批量审核内容
   */
  async batchModerateContent(contentIds) {
    const results = [];
    
    for (const contentId of contentIds) {
      try {
        // 获取内容数据
        const contentData = await this.getContentData(contentId);
        if (contentData) {
          const result = await this.moderateContent(contentId, contentData);
          results.push(result);
        }
      } catch (error) {
        logger.error('Batch moderation failed for content', {
          contentId,
          error: error.message
        });
        results.push({
          contentId,
          error: error.message,
          overallStatus: 'error'
        });
      }
    }

    return results;
  }

  /**
   * 获取内容数据
   */
  async getContentData(contentId) {
    const connection = await this.db.getConnection();
    
    try {
      const [contents] = await connection.execute(
        'SELECT * FROM ugc_content WHERE id = ?',
        [contentId]
      );

      return contents[0] || null;

    } catch (error) {
      logger.error('Failed to get content data', {
        contentId,
        error: error.message
      });
      return null;
    } finally {
      connection.release();
    }
  }
}

module.exports = { AIModerationService };
