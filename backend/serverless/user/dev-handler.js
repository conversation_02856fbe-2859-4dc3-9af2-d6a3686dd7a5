/**
 * 用户服务开发处理器
 * 使用Mock数据服务的简化版本
 */

const jwt = require('jsonwebtoken');
const devDb = require('../utils/dev-database').getInstance();

const JWT_SECRET = 'dev_jwt_secret_key_for_testing_only';

/**
 * 主处理器 - 路由分发
 */
const main = async (event, context) => {
  const { httpMethod, path } = event;
  const pathSegments = path.split('/').filter(Boolean);

  console.log(`User Handler: ${httpMethod} ${path}`);

  try {
    if (pathSegments.length >= 3) {
      const userId = pathSegments[2]; // /v1/users/{userId}
      
      if (httpMethod === 'GET' && pathSegments.length === 3) {
        return await getUserInfo(event, context);
      } else if (httpMethod === 'PUT' && path.includes('/profile')) {
        return await updateUserProfile(event, context);
      } else if (httpMethod === 'GET' && path.includes('/stats')) {
        return await getUserStats(event, context);
      } else if (httpMethod === 'GET' && path.includes('/game-history')) {
        return await getUserGameHistory(event, context);
      }
    } else if (path.includes('/search') && httpMethod === 'GET') {
      return await searchUsers(event, context);
    }

    // 404处理
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: `用户服务不支持 ${httpMethod} ${path}`,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('User Handler Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'INTERNAL_ERROR',
        message: error.message || '服务器内部错误',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * 验证Token并获取用户
 */
const verifyTokenAndGetUser = async (event) => {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('UNAUTHORIZED');
  }

  const token = authHeader.substring(7);
  const decoded = jwt.verify(token, JWT_SECRET);
  const user = await devDb.getUser(decoded.userId);
  
  if (!user) {
    throw new Error('USER_NOT_FOUND');
  }
  
  return user;
};

/**
 * 获取用户信息
 * GET /v1/users/{userId}
 */
const getUserInfo = async (event, context) => {
  const userId = event.path.split('/')[3];
  
  try {
    // 验证权限（简化版：只能查看自己的信息）
    const currentUser = await verifyTokenAndGetUser(event);
    
    if (currentUser.user_id !== userId) {
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'FORBIDDEN',
          message: '无权访问其他用户信息',
          timestamp: new Date().toISOString()
        })
      };
    }

    const user = await devDb.getUser(userId);
    if (!user) {
      return {
        statusCode: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'USER_NOT_FOUND',
          message: '用户不存在',
          timestamp: new Date().toISOString()
        })
      };
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: {
          id: user.user_id,
          nickname: user.nickname,
          avatarUrl: user.avatar_url,
          createdAt: user.created_at,
          lastLogin: user.last_login,
          gameCount: user.game_count,
          totalScore: user.total_score,
          bestScore: user.best_score,
          accuracyRate: Math.round(user.accuracy_rate * 100)
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    if (error.message === 'UNAUTHORIZED') {
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'UNAUTHORIZED',
          message: '缺少认证信息',
          timestamp: new Date().toISOString()
        })
      };
    }
    
    throw error;
  }
};

/**
 * 更新用户资料
 * PUT /v1/users/{userId}/profile
 */
const updateUserProfile = async (event, context) => {
  const userId = event.path.split('/')[3];
  const body = JSON.parse(event.body || '{}');
  
  try {
    const currentUser = await verifyTokenAndGetUser(event);
    
    if (currentUser.user_id !== userId) {
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'FORBIDDEN',
          message: '无权修改其他用户信息',
          timestamp: new Date().toISOString()
        })
      };
    }

    // 更新允许的字段
    const { nickname, avatarUrl } = body;
    const updates = { updated_at: new Date() };
    
    if (nickname) updates.nickname = nickname;
    if (avatarUrl) updates.avatar_url = avatarUrl;

    Object.assign(currentUser, updates);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: {
          id: currentUser.user_id,
          nickname: currentUser.nickname,
          avatarUrl: currentUser.avatar_url,
          updatedAt: currentUser.updated_at
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    if (error.message === 'UNAUTHORIZED') {
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'UNAUTHORIZED',
          message: '缺少认证信息',
          timestamp: new Date().toISOString()
        })
      };
    }
    
    throw error;
  }
};

/**
 * 获取用户统计
 * GET /v1/users/{userId}/stats
 */
const getUserStats = async (event, context) => {
  const userId = event.path.split('/')[3];
  
  try {
    const currentUser = await verifyTokenAndGetUser(event);
    
    if (currentUser.user_id !== userId) {
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'FORBIDDEN',
          message: '无权访问其他用户统计',
          timestamp: new Date().toISOString()
        })
      };
    }

    const stats = await devDb.getUserStats(userId);
    
    if (!stats) {
      return {
        statusCode: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'USER_NOT_FOUND',
          message: '用户不存在',
          timestamp: new Date().toISOString()
        })
      };
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: stats,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    if (error.message === 'UNAUTHORIZED') {
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'UNAUTHORIZED',
          message: '缺少认证信息',
          timestamp: new Date().toISOString()
        })
      };
    }
    
    throw error;
  }
};

/**
 * 获取用户游戏历史
 * GET /v1/users/{userId}/game-history
 */
const getUserGameHistory = async (event, context) => {
  const userId = event.path.split('/')[3];
  const queryParams = event.queryStringParameters || {};
  const { limit = 20, offset = 0 } = queryParams;
  
  try {
    const currentUser = await verifyTokenAndGetUser(event);
    
    if (currentUser.user_id !== userId) {
      return {
        statusCode: 403,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'FORBIDDEN',
          message: '无权访问其他用户游戏历史',
          timestamp: new Date().toISOString()
        })
      };
    }

    // 简化版：返回最近的游戏记录
    const stats = await devDb.getUserStats(userId);
    const recentGames = stats ? stats.recent_games.slice(parseInt(offset), parseInt(offset) + parseInt(limit)) : [];

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'SUCCESS',
        data: {
          games: recentGames,
          total: stats ? stats.recent_games.length : 0,
          limit: parseInt(limit),
          offset: parseInt(offset)
        },
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    if (error.message === 'UNAUTHORIZED') {
      return {
        statusCode: 401,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          code: 'UNAUTHORIZED',
          message: '缺少认证信息',
          timestamp: new Date().toISOString()
        })
      };
    }
    
    throw error;
  }
};

/**
 * 搜索用户
 * GET /v1/users/search
 */
const searchUsers = async (event, context) => {
  const queryParams = event.queryStringParameters || {};
  const { keyword, limit = 10 } = queryParams;
  
  if (!keyword) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        code: 'MISSING_KEYWORD',
        message: '缺少搜索关键词',
        timestamp: new Date().toISOString()
      })
    };
  }

  // 简化版搜索：在开发环境返回空结果
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      code: 'SUCCESS',
      data: {
        users: [],
        total: 0,
        keyword,
        limit: parseInt(limit)
      },
      timestamp: new Date().toISOString()
    })
  };
};

module.exports = { main };