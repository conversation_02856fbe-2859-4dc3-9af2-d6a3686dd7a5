/**
 * 用户服务处理器
 * 处理用户信息管理、游戏统计等API请求
 */

const User = require('../models/User');
const { authMiddleware, ownershipCheck } = require('../middleware/auth');
const { validateRequest, VALIDATION_SCHEMAS } = require('../middleware/validation');
const { errorHandler } = require('../middleware/error');
const { rateLimitMiddleware } = require('../middleware/rateLimit');
const { APIError, NotFoundError } = require('../utils/errors');

/**
 * 获取用户信息
 * GET /v1/users/{userId}
 */
const getUserProfile = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const data = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const { userId } = data;

  // 权限检查：只能查看自己的详细信息，其他用户只能看公开信息
  const isOwner = event.user.id.toString() === userId;
  const isAdmin = event.scopes.includes('admin:read');

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  return {
    user: user.toJSON(isOwner || isAdmin),
    isOwner
  };
});

/**
 * 更新用户资料
 * PUT /v1/users/{userId}/profile
 */
const updateUserProfile = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('user')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const updateData = validateRequest(VALIDATION_SCHEMAS.updateProfile)(event);
  
  const { userId } = pathData;

  // 所有权检查
  const ownershipResult = await ownershipCheck((event) => userId)(event, context);
  if (ownershipResult.statusCode) {
    return ownershipResult;
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const success = await user.update(updateData);
  if (!success) {
    throw new APIError('UPDATE_FAILED', '更新用户资料失败');
  }

  return {
    user: user.toJSON(true),
    message: '用户资料更新成功'
  };
});

/**
 * 获取用户游戏统计
 * GET /v1/users/{userId}/stats
 */
const getUserGameStats = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const queryData = validateRequest({
    type: 'object',
    properties: {
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'] 
      }
    }
  })(event);

  const { userId } = pathData;
  const { category } = queryData;

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const stats = await user.getGameStats(category);
  const levelInfo = user.getLevelInfo();

  return {
    userId: user.id,
    totalScore: user.total_score,
    totalGames: user.total_games,
    winGames: user.win_games,
    winRate: user.total_games > 0 ? (user.win_games / user.total_games).toFixed(4) : 0,
    maxStreak: user.max_streak,
    currentLevel: user.current_level,
    levelInfo,
    categoryStats: stats
  };
});

/**
 * 获取用户游戏历史
 * GET /v1/users/{userId}/game-history
 */
const getUserGameHistory = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const queryData = validateRequest({
    type: 'object',
    properties: {
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'] 
      },
      page: { type: 'integer', minimum: 1, default: 1 },
      size: { type: 'integer', minimum: 1, maximum: 50, default: 20 }
    }
  })(event);

  const { userId } = pathData;
  const { category, page, size } = queryData;

  // 权限检查：只有用户自己才能查看详细游戏历史
  const ownershipResult = await ownershipCheck((event) => userId)(event, context);
  if (ownershipResult.statusCode) {
    return ownershipResult;
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const history = await user.getGameHistory({
    category,
    page,
    size
  });

  return history;
});

/**
 * 获取用户排名
 * GET /v1/users/{userId}/ranking
 */
const getUserRanking = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const queryData = validateRequest({
    type: 'object',
    properties: {
      type: { 
        type: 'string', 
        enum: ['overall', 'weekly', 'monthly'], 
        default: 'overall' 
      },
      category: { 
        type: 'string', 
        enum: ['beijing', 'shanghai', 'guangdong', 'sichuan', 'henan', 'shandong'] 
      }
    }
  })(event);

  const { userId } = pathData;
  const { type, category } = queryData;

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const ranking = await user.getRanking(type, category);

  return {
    userId: user.id,
    type,
    category,
    ranking: ranking || {
      rank: null,
      score: user.total_score,
      message: '暂未上榜'
    }
  };
});

/**
 * 获取用户好友列表
 * GET /v1/users/{userId}/friends
 */
const getUserFriends = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const { userId } = pathData;

  // 权限检查：只能查看自己的好友列表
  const ownershipResult = await ownershipCheck((event) => userId)(event, context);
  if (ownershipResult.statusCode) {
    return ownershipResult;
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const friends = await user.getFriends();

  return {
    userId: user.id,
    friends,
    total: friends.length
  };
});

/**
 * 添加好友
 * POST /v1/users/{userId}/friends
 */
const addFriend = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware('user')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const bodyData = validateRequest({
    type: 'object',
    required: ['friendId'],
    properties: {
      friendId: { type: 'integer', minimum: 1 }
    }
  })(event);

  const { userId } = pathData;
  const { friendId } = bodyData;

  // 权限检查
  const ownershipResult = await ownershipCheck((event) => userId)(event, context);
  if (ownershipResult.statusCode) {
    return ownershipResult;
  }

  if (userId === friendId.toString()) {
    throw new APIError('CANNOT_ADD_SELF', '不能添加自己为好友');
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const friend = await User.findById(friendId);
  if (!friend) {
    throw new APIError('FRIEND_NOT_FOUND', '要添加的用户不存在');
  }

  const success = await user.addFriend(friendId);
  if (!success) {
    throw new APIError('ALREADY_FRIENDS', '已经是好友关系');
  }

  return {
    message: '添加好友成功',
    friend: friend.toJSON(false)
  };
});

/**
 * 删除好友
 * DELETE /v1/users/{userId}/friends/{friendId}
 */
const removeFriend = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:write'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest({
    type: 'object',
    required: ['userId', 'friendId'],
    properties: {
      userId: { type: 'string', pattern: '^\\d+$' },
      friendId: { type: 'string', pattern: '^\\d+$' }
    }
  })(event);

  const { userId, friendId } = pathData;

  // 权限检查
  const ownershipResult = await ownershipCheck((event) => userId)(event, context);
  if (ownershipResult.statusCode) {
    return ownershipResult;
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  const success = await user.removeFriend(parseInt(friendId));
  if (!success) {
    throw new APIError('NOT_FRIENDS', '不是好友关系');
  }

  return {
    message: '删除好友成功'
  };
});

/**
 * 搜索用户
 * GET /v1/users/search
 */
const searchUsers = errorHandler(async (event, context) => {
  // 可选认证
  const authResult = await authMiddleware()(event, context);
  // 搜索功能允许未认证用户使用，但会有更严格的限流

  // 限流检查
  const rateLimitResult = await rateLimitMiddleware(event.user ? 'user' : 'ip')(event, context);
  if (rateLimitResult.statusCode) {
    return rateLimitResult;
  }

  // 参数验证
  const queryData = validateRequest({
    type: 'object',
    required: ['keyword'],
    properties: {
      keyword: { type: 'string', minLength: 1, maxLength: 30 },
      page: { type: 'integer', minimum: 1, default: 1 },
      size: { type: 'integer', minimum: 1, maximum: 20, default: 10 }
    }
  })(event);

  const { keyword, page, size } = queryData;

  const result = await User.search(keyword, { page, size });

  return {
    keyword,
    users: result.items.map(user => user.toJSON(false)),
    pagination: result.pagination
  };
});

/**
 * 获取用户成就
 * GET /v1/users/{userId}/achievements
 */
const getUserAchievements = errorHandler(async (event, context) => {
  // 认证检查
  const authResult = await authMiddleware(['user:read'])(event, context);
  if (authResult.statusCode) {
    return authResult;
  }

  // 参数验证
  const pathData = validateRequest(VALIDATION_SCHEMAS.pathParams.userId)(event);
  const { userId } = pathData;

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('用户');
  }

  // 计算成就
  const achievements = calculateUserAchievements(user);

  return {
    userId: user.id,
    achievements,
    total: achievements.length,
    unlockedCount: achievements.filter(a => a.unlocked).length
  };
});

/**
 * 计算用户成就
 */
function calculateUserAchievements(user) {
  const achievements = [
    {
      id: 'first_game',
      name: '初来乍到',
      description: '完成第一场游戏',
      icon: '🎮',
      unlocked: user.total_games > 0,
      progress: Math.min(user.total_games, 1),
      target: 1
    },
    {
      id: 'games_10',
      name: '游戏达人',
      description: '完成10场游戏',
      icon: '🎯',
      unlocked: user.total_games >= 10,
      progress: Math.min(user.total_games, 10),
      target: 10
    },
    {
      id: 'games_100',
      name: '方言专家',
      description: '完成100场游戏',
      icon: '🏆',
      unlocked: user.total_games >= 100,
      progress: Math.min(user.total_games, 100),
      target: 100
    },
    {
      id: 'score_1000',
      name: '积分新手',
      description: '获得1000积分',
      icon: '⭐',
      unlocked: user.total_score >= 1000,
      progress: Math.min(user.total_score, 1000),
      target: 1000
    },
    {
      id: 'score_10000',
      name: '积分达人',
      description: '获得10000积分',
      icon: '🌟',
      unlocked: user.total_score >= 10000,
      progress: Math.min(user.total_score, 10000),
      target: 10000
    },
    {
      id: 'streak_5',
      name: '连击高手',
      description: '达成5连击',
      icon: '🔥',
      unlocked: user.max_streak >= 5,
      progress: Math.min(user.max_streak, 5),
      target: 5
    },
    {
      id: 'streak_10',
      name: '超级连击',
      description: '达成10连击',
      icon: '💥',
      unlocked: user.max_streak >= 10,
      progress: Math.min(user.max_streak, 10),
      target: 10
    }
  ];

  return achievements;
}

/**
 * 路由处理器
 */
const routes = {
  'GET /v1/users/{userId}': getUserProfile,
  'PUT /v1/users/{userId}/profile': updateUserProfile,
  'GET /v1/users/{userId}/stats': getUserGameStats,
  'GET /v1/users/{userId}/game-history': getUserGameHistory,
  'GET /v1/users/{userId}/ranking': getUserRanking,
  'GET /v1/users/{userId}/friends': getUserFriends,
  'POST /v1/users/{userId}/friends': addFriend,
  'DELETE /v1/users/{userId}/friends/{friendId}': removeFriend,
  'GET /v1/users/search': searchUsers,
  'GET /v1/users/{userId}/achievements': getUserAchievements
};

/**
 * 主处理器
 */
const main = async (event, context) => {
  const method = event.httpMethod || event.requestContext?.httpMethod;
  const path = event.path || event.requestContext?.path;
  
  // 处理路径参数
  let routeKey = `${method} ${path}`;
  
  // 将具体的ID替换为路径参数占位符
  routeKey = routeKey.replace(/\/\d+/g, '/{userId}')
                   .replace(/\/\d+\/friends\/\d+/, '/{userId}/friends/{friendId}');
  
  console.log(`User Service - ${routeKey}`);
  
  // 添加CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Device-Id',
    'Access-Control-Max-Age': '86400'
  };
  
  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }
  
  // 查找对应的路由处理器
  const handler = routes[routeKey];
  
  if (!handler) {
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 'ROUTE_NOT_FOUND',
        message: '接口不存在',
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
  
  try {
    const result = await handler(event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Remaining': event.rateLimitInfo?.remaining,
        'X-RateLimit-Limit': event.rateLimitInfo?.limit,
        ...corsHeaders
      },
      body: JSON.stringify({
        code: 0,
        message: 'success',
        data: result,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
    
  } catch (error) {
    console.error('User handler error:', error);
    
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let message = '服务器内部错误';
    
    if (error instanceof APIError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      message = error.message;
    }
    
    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      },
      body: JSON.stringify({
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      })
    };
  }
};

module.exports = {
  main,
  getUserProfile,
  updateUserProfile,
  getUserGameStats,
  getUserGameHistory,
  getUserRanking,
  getUserFriends,
  addFriend,
  removeFriend,
  searchUsers,
  getUserAchievements
};