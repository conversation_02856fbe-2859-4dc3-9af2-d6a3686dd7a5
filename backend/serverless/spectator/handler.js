/**
 * 围观系统处理器
 * 管理围观房间、观众状态、实时数据同步
 */

const { errorHandler } = require('../middleware/error');
const { apiSecurity } = require('../middleware/apiSecurity');
const { performance } = require('../middleware/performance');
const { SpectatorService } = require('./spectatorService');
const { logger } = require('../utils/logger');

class SpectatorHandler {
  constructor() {
    this.spectatorService = new SpectatorService();
  }

  /**
   * 创建围观房间
   */
  async createRoom(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'createRoom'
    });

    try {
      const { gameSessionId, settings = {} } = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!gameSessionId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Game session ID is required'
          })
        };
      }

      const room = await this.spectatorService.createSpectatorRoom(
        gameSessionId,
        userId,
        settings
      );

      spectatorLogger.info('Spectator room created', {
        roomId: room.id,
        gameSessionId,
        creatorId: userId
      });

      return {
        statusCode: 201,
        body: JSON.stringify({
          success: true,
          data: room
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to create spectator room', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to create spectator room'
        })
      };
    }
  }

  /**
   * 获取围观房间信息
   */
  async getRoom(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'getRoom'
    });

    try {
      const { roomId } = event.pathParameters;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const room = await this.spectatorService.getSpectatorRoom(roomId);

      if (!room) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Room not found'
          })
        };
      }

      spectatorLogger.debug('Spectator room retrieved', {
        roomId,
        spectatorCount: room.spectatorCount
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: room
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to get spectator room', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get spectator room'
        })
      };
    }
  }

  /**
   * 获取围观房间列表
   */
  async getRoomList(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'getRoomList'
    });

    try {
      const {
        page = 1,
        limit = 20,
        status = 'active',
        sortBy = 'spectatorCount'
      } = event.queryStringParameters || {};

      const rooms = await this.spectatorService.getSpectatorRoomList({
        page: parseInt(page),
        limit: parseInt(limit),
        status,
        sortBy
      });

      spectatorLogger.debug('Spectator room list retrieved', {
        totalRooms: rooms.total,
        page,
        limit
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: rooms
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to get spectator room list', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get spectator room list'
        })
      };
    }
  }

  /**
   * 加入围观房间
   */
  async joinRoom(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'joinRoom'
    });

    try {
      const { roomId } = event.pathParameters;
      const userId = event.user?.id;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const result = await this.spectatorService.joinSpectatorRoom(roomId, userId);

      spectatorLogger.info('User joined spectator room', {
        roomId,
        userId,
        spectatorCount: result.spectatorCount
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to join spectator room', {
        error: error.message
      });

      const statusCode = error.message.includes('Room is full') ? 409 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 离开围观房间
   */
  async leaveRoom(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'leaveRoom'
    });

    try {
      const { roomId } = event.pathParameters;
      const userId = event.user?.id;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const result = await this.spectatorService.leaveSpectatorRoom(roomId, userId);

      spectatorLogger.info('User left spectator room', {
        roomId,
        userId,
        spectatorCount: result.spectatorCount
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: result
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to leave spectator room', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to leave spectator room'
        })
      };
    }
  }

  /**
   * 获取围观房间统计
   */
  async getRoomStats(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'getRoomStats'
    });

    try {
      const { roomId } = event.pathParameters;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const stats = await this.spectatorService.getSpectatorRoomStats(roomId);

      if (!stats) {
        return {
          statusCode: 404,
          body: JSON.stringify({
            error: 'Room not found'
          })
        };
      }

      spectatorLogger.debug('Spectator room stats retrieved', {
        roomId,
        totalSpectators: stats.totalSpectators
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: stats
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to get spectator room stats', {
        error: error.message
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to get spectator room stats'
        })
      };
    }
  }

  /**
   * 更新围观房间设置
   */
  async updateRoomSettings(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'updateRoomSettings'
    });

    try {
      const { roomId } = event.pathParameters;
      const settings = JSON.parse(event.body || '{}');
      const userId = event.user?.id;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      const room = await this.spectatorService.updateSpectatorRoomSettings(
        roomId,
        settings,
        userId
      );

      spectatorLogger.info('Spectator room settings updated', {
        roomId,
        userId,
        settings: Object.keys(settings)
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          data: room
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to update spectator room settings', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }

  /**
   * 关闭围观房间
   */
  async closeRoom(event, context) {
    const spectatorLogger = logger.child({
      requestId: context.requestId,
      action: 'closeRoom'
    });

    try {
      const { roomId } = event.pathParameters;
      const userId = event.user?.id;

      if (!roomId) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            error: 'Room ID is required'
          })
        };
      }

      await this.spectatorService.closeSpectatorRoom(roomId, userId);

      spectatorLogger.info('Spectator room closed', {
        roomId,
        userId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Room closed successfully'
        })
      };

    } catch (error) {
      spectatorLogger.error('Failed to close spectator room', {
        error: error.message
      });

      const statusCode = error.message.includes('Permission denied') ? 403 : 500;

      return {
        statusCode,
        body: JSON.stringify({
          error: error.message
        })
      };
    }
  }
}

// 创建处理器实例
const spectatorHandler = new SpectatorHandler();

// 应用中间件并导出处理函数
const withMiddleware = (handler) => {
  return errorHandler(
    performance.middleware()(
      apiSecurity.securityProtection()(handler)
    )
  );
};

module.exports = {
  createRoom: withMiddleware(spectatorHandler.createRoom.bind(spectatorHandler)),
  getRoom: withMiddleware(spectatorHandler.getRoom.bind(spectatorHandler)),
  getRoomList: withMiddleware(spectatorHandler.getRoomList.bind(spectatorHandler)),
  joinRoom: withMiddleware(spectatorHandler.joinRoom.bind(spectatorHandler)),
  leaveRoom: withMiddleware(spectatorHandler.leaveRoom.bind(spectatorHandler)),
  getRoomStats: withMiddleware(spectatorHandler.getRoomStats.bind(spectatorHandler)),
  updateRoomSettings: withMiddleware(spectatorHandler.updateRoomSettings.bind(spectatorHandler)),
  closeRoom: withMiddleware(spectatorHandler.closeRoom.bind(spectatorHandler)),
  
  // 主路由处理器
  main: async (event, context) => {
    const path = event.path;
    const method = event.httpMethod;

    // 路由映射
    const routes = {
      'POST /v1/spectator/rooms': spectatorHandler.createRoom.bind(spectatorHandler),
      'GET /v1/spectator/rooms': spectatorHandler.getRoomList.bind(spectatorHandler),
      'GET /v1/spectator/rooms/{roomId}': spectatorHandler.getRoom.bind(spectatorHandler),
      'POST /v1/spectator/rooms/{roomId}/join': spectatorHandler.joinRoom.bind(spectatorHandler),
      'POST /v1/spectator/rooms/{roomId}/leave': spectatorHandler.leaveRoom.bind(spectatorHandler),
      'GET /v1/spectator/rooms/{roomId}/stats': spectatorHandler.getRoomStats.bind(spectatorHandler),
      'PUT /v1/spectator/rooms/{roomId}/settings': spectatorHandler.updateRoomSettings.bind(spectatorHandler),
      'DELETE /v1/spectator/rooms/{roomId}': spectatorHandler.closeRoom.bind(spectatorHandler)
    };

    const routeKey = `${method} ${path}`;
    const handler = routes[routeKey];

    if (handler) {
      return await withMiddleware(handler)(event, context);
    }

    return {
      statusCode: 404,
      body: JSON.stringify({
        error: 'Route not found'
      })
    };
  }
};
