/**
 * 围观系统服务
 * 处理围观房间的业务逻辑
 */

const { DatabaseManager } = require('../utils/database');
const { RedisManager } = require('../utils/redis');
const { logger } = require('../utils/logger');
const { RoomManager } = require('../websocket/roomManager');

class SpectatorService {
  constructor() {
    this.db = DatabaseManager.getInstance();
    this.redis = RedisManager.getInstance();
    this.roomManager = new RoomManager();
    this.spectatorPrefix = 'spectator:';
  }

  /**
   * 创建围观房间
   */
  async createSpectatorRoom(gameSessionId, creatorId, settings = {}) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.beginTransaction();

      // 检查游戏会话是否存在
      const [gameSession] = await connection.execute(
        'SELECT id, status, player_id, current_question FROM game_sessions WHERE id = ?',
        [gameSessionId]
      );

      if (!gameSession.length) {
        throw new Error('Game session not found');
      }

      const session = gameSession[0];
      if (session.status !== 'active') {
        throw new Error('Game session is not active');
      }

      // 检查是否已存在围观房间
      const [existingRoom] = await connection.execute(
        'SELECT id FROM spectator_rooms WHERE game_session_id = ? AND status = "active"',
        [gameSessionId]
      );

      if (existingRoom.length > 0) {
        throw new Error('Spectator room already exists for this game session');
      }

      // 创建围观房间记录
      const roomId = this.generateRoomId();
      const defaultSettings = {
        maxSpectators: 1000,
        allowDanmaku: true,
        allowPrediction: true,
        moderationEnabled: true,
        ...settings
      };

      await connection.execute(`
        INSERT INTO spectator_rooms (
          id, game_session_id, creator_id, settings, status, created_at
        ) VALUES (?, ?, ?, ?, 'active', NOW())
      `, [
        roomId,
        gameSessionId,
        creatorId,
        JSON.stringify(defaultSettings)
      ]);

      // 在WebSocket系统中创建房间
      await this.roomManager.createRoom(
        roomId,
        'spectator',
        creatorId,
        {
          gameSessionId,
          currentQuestion: session.current_question,
          playerId: session.player_id
        }
      );

      // 缓存房间信息
      const roomData = {
        id: roomId,
        gameSessionId,
        creatorId,
        settings: defaultSettings,
        status: 'active',
        spectatorCount: 0,
        createdAt: new Date().toISOString()
      };

      await this.redis.setex(
        `${this.spectatorPrefix}room:${roomId}`,
        24 * 3600,
        JSON.stringify(roomData)
      );

      await connection.commit();

      logger.info('Spectator room created', {
        roomId,
        gameSessionId,
        creatorId
      });

      return roomData;

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to create spectator room', {
        gameSessionId,
        creatorId,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取围观房间信息
   */
  async getSpectatorRoom(roomId) {
    try {
      // 先从缓存获取
      const cached = await this.redis.get(`${this.spectatorPrefix}room:${roomId}`);
      if (cached) {
        const roomData = JSON.parse(cached);
        // 获取实时观众数
        roomData.spectatorCount = await this.getSpectatorCount(roomId);
        return roomData;
      }

      // 从数据库获取
      const connection = await this.db.getConnection();
      try {
        const [rooms] = await connection.execute(`
          SELECT 
            sr.*,
            gs.status as game_status,
            gs.current_question,
            gs.player_id,
            u.nickname as creator_nickname
          FROM spectator_rooms sr
          JOIN game_sessions gs ON sr.game_session_id = gs.id
          LEFT JOIN users u ON sr.creator_id = u.id
          WHERE sr.id = ?
        `, [roomId]);

        if (!rooms.length) {
          return null;
        }

        const room = rooms[0];
        const roomData = {
          id: room.id,
          gameSessionId: room.game_session_id,
          creatorId: room.creator_id,
          creatorNickname: room.creator_nickname,
          settings: JSON.parse(room.settings || '{}'),
          status: room.status,
          spectatorCount: await this.getSpectatorCount(roomId),
          gameStatus: room.game_status,
          currentQuestion: room.current_question,
          playerId: room.player_id,
          createdAt: room.created_at
        };

        // 缓存结果
        await this.redis.setex(
          `${this.spectatorPrefix}room:${roomId}`,
          3600, // 1小时
          JSON.stringify(roomData)
        );

        return roomData;

      } finally {
        connection.release();
      }

    } catch (error) {
      logger.error('Failed to get spectator room', {
        roomId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取围观房间列表
   */
  async getSpectatorRoomList({ page = 1, limit = 20, status = 'active', sortBy = 'spectatorCount' }) {
    const connection = await this.db.getConnection();
    
    try {
      const offset = (page - 1) * limit;
      
      // 获取总数
      const [countResult] = await connection.execute(`
        SELECT COUNT(*) as total
        FROM spectator_rooms sr
        JOIN game_sessions gs ON sr.game_session_id = gs.id
        WHERE sr.status = ? AND gs.status = 'active'
      `, [status]);

      const total = countResult[0].total;

      // 获取房间列表
      let orderBy = 'sr.created_at DESC';
      if (sortBy === 'spectatorCount') {
        orderBy = 'sr.created_at DESC'; // 实际排序需要在应用层处理
      }

      const [rooms] = await connection.execute(`
        SELECT 
          sr.*,
          gs.current_question,
          gs.player_id,
          u.nickname as creator_nickname
        FROM spectator_rooms sr
        JOIN game_sessions gs ON sr.game_session_id = gs.id
        LEFT JOIN users u ON sr.creator_id = u.id
        WHERE sr.status = ? AND gs.status = 'active'
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `, [status, limit, offset]);

      // 获取每个房间的观众数并排序
      const roomsWithCount = await Promise.all(
        rooms.map(async (room) => {
          const spectatorCount = await this.getSpectatorCount(room.id);
          return {
            id: room.id,
            gameSessionId: room.game_session_id,
            creatorId: room.creator_id,
            creatorNickname: room.creator_nickname,
            settings: JSON.parse(room.settings || '{}'),
            status: room.status,
            spectatorCount,
            currentQuestion: room.current_question,
            playerId: room.player_id,
            createdAt: room.created_at
          };
        })
      );

      // 按观众数排序
      if (sortBy === 'spectatorCount') {
        roomsWithCount.sort((a, b) => b.spectatorCount - a.spectatorCount);
      }

      return {
        rooms: roomsWithCount,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      logger.error('Failed to get spectator room list', {
        page,
        limit,
        status,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 加入围观房间
   */
  async joinSpectatorRoom(roomId, userId) {
    try {
      const room = await this.getSpectatorRoom(roomId);
      if (!room) {
        throw new Error('Room not found');
      }

      if (room.status !== 'active') {
        throw new Error('Room is not active');
      }

      const currentCount = await this.getSpectatorCount(roomId);
      if (currentCount >= room.settings.maxSpectators) {
        throw new Error('Room is full');
      }

      // 添加到观众列表
      await this.redis.sadd(`${this.spectatorPrefix}room:${roomId}:spectators`, userId);
      await this.redis.expire(`${this.spectatorPrefix}room:${roomId}:spectators`, 24 * 3600);

      // 记录加入时间
      await this.redis.setex(
        `${this.spectatorPrefix}user:${userId}:room:${roomId}`,
        24 * 3600,
        new Date().toISOString()
      );

      const newCount = await this.getSpectatorCount(roomId);

      logger.info('User joined spectator room', {
        roomId,
        userId,
        spectatorCount: newCount
      });

      return {
        roomId,
        spectatorCount: newCount,
        joinedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Failed to join spectator room', {
        roomId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 离开围观房间
   */
  async leaveSpectatorRoom(roomId, userId) {
    try {
      // 从观众列表移除
      await this.redis.srem(`${this.spectatorPrefix}room:${roomId}:spectators`, userId);
      await this.redis.del(`${this.spectatorPrefix}user:${userId}:room:${roomId}`);

      const newCount = await this.getSpectatorCount(roomId);

      logger.info('User left spectator room', {
        roomId,
        userId,
        spectatorCount: newCount
      });

      return {
        roomId,
        spectatorCount: newCount,
        leftAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Failed to leave spectator room', {
        roomId,
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取围观房间统计
   */
  async getSpectatorRoomStats(roomId) {
    try {
      const room = await this.getSpectatorRoom(roomId);
      if (!room) {
        return null;
      }

      const spectators = await this.redis.smembers(`${this.spectatorPrefix}room:${roomId}:spectators`);
      
      // 获取观众详细信息
      const connection = await this.db.getConnection();
      try {
        const spectatorDetails = [];
        if (spectators.length > 0) {
          const placeholders = spectators.map(() => '?').join(',');
          const [users] = await connection.execute(
            `SELECT id, nickname, avatar_url FROM users WHERE id IN (${placeholders})`,
            spectators
          );
          spectatorDetails.push(...users);
        }

        return {
          roomId,
          totalSpectators: spectators.length,
          spectators: spectatorDetails,
          room: room
        };

      } finally {
        connection.release();
      }

    } catch (error) {
      logger.error('Failed to get spectator room stats', {
        roomId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新围观房间设置
   */
  async updateSpectatorRoomSettings(roomId, settings, userId) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.beginTransaction();

      // 检查权限
      const [rooms] = await connection.execute(
        'SELECT creator_id, settings FROM spectator_rooms WHERE id = ?',
        [roomId]
      );

      if (!rooms.length) {
        throw new Error('Room not found');
      }

      if (rooms[0].creator_id !== userId) {
        throw new Error('Permission denied');
      }

      // 合并设置
      const currentSettings = JSON.parse(rooms[0].settings || '{}');
      const newSettings = { ...currentSettings, ...settings };

      // 更新数据库
      await connection.execute(
        'UPDATE spectator_rooms SET settings = ? WHERE id = ?',
        [JSON.stringify(newSettings), roomId]
      );

      // 更新缓存
      await this.redis.del(`${this.spectatorPrefix}room:${roomId}`);

      await connection.commit();

      logger.info('Spectator room settings updated', {
        roomId,
        userId,
        settings: Object.keys(settings)
      });

      return await this.getSpectatorRoom(roomId);

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to update spectator room settings', {
        roomId,
        userId,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 关闭围观房间
   */
  async closeSpectatorRoom(roomId, userId) {
    const connection = await this.db.getConnection();
    
    try {
      await connection.beginTransaction();

      // 检查权限
      const [rooms] = await connection.execute(
        'SELECT creator_id FROM spectator_rooms WHERE id = ?',
        [roomId]
      );

      if (!rooms.length) {
        throw new Error('Room not found');
      }

      if (rooms[0].creator_id !== userId) {
        throw new Error('Permission denied');
      }

      // 更新状态
      await connection.execute(
        'UPDATE spectator_rooms SET status = "closed", closed_at = NOW() WHERE id = ?',
        [roomId]
      );

      // 清理缓存和观众列表
      await this.redis.del(`${this.spectatorPrefix}room:${roomId}`);
      await this.redis.del(`${this.spectatorPrefix}room:${roomId}:spectators`);

      await connection.commit();

      logger.info('Spectator room closed', {
        roomId,
        userId
      });

    } catch (error) {
      await connection.rollback();
      logger.error('Failed to close spectator room', {
        roomId,
        userId,
        error: error.message
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取观众数量
   */
  async getSpectatorCount(roomId) {
    try {
      return await this.redis.scard(`${this.spectatorPrefix}room:${roomId}:spectators`);
    } catch (error) {
      logger.error('Failed to get spectator count', {
        roomId,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * 生成房间ID
   */
  generateRoomId() {
    return 'room_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

module.exports = { SpectatorService };
