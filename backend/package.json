{"name": "hometown-dialect-game-backend", "version": "1.0.0", "description": "家乡话猜猜猜游戏后端API服务", "main": "index.js", "scripts": {"dev": "node scripts/dev-server.js", "setup": "node scripts/setup-dev.js", "db:init": "node scripts/init-db.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":80,\"functions\":80,\"lines\":80,\"statements\":80}}'", "test:integration": "jest tests/integration --runInBand", "test:unit": "jest tests/unit", "test:e2e": "jest tests/e2e --runInBand", "test:performance": "node scripts/performance-test.js", "test:security": "node scripts/security-test.js", "test:load": "node scripts/load-test.js", "test:smoke": "jest tests/smoke --runInBand", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "docs:generate": "node scripts/generate-api-docs.js", "docs:sync": "node scripts/sync-api-docs.js watch", "docs:validate": "node scripts/sync-api-docs.js validate", "docs:stats": "node scripts/sync-api-docs.js stats", "docs:serve": "node scripts/sync-api-docs.js serve", "lint": "eslint serverless/**/*.js tests/**/*.js scripts/**/*.js", "lint:fix": "eslint serverless/**/*.js tests/**/*.js scripts/**/*.js --fix", "security:audit": "npm audit && node scripts/security-audit.js", "security:scan": "node scripts/security-scanner.js", "performance:monitor": "node scripts/performance-monitor.js", "performance:db": "node scripts/database-performance-monitor.js", "performance:report": "node scripts/generate-performance-report.js", "build": "node scripts/build.js", "deploy": "serverless deploy", "deploy:prod": "serverless deploy --stage prod", "logs": "serverless logs -f api --tail", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "db:backup": "node scripts/backup-db.js", "db:restore": "node scripts/restore-db.js"}, "dependencies": {"mysql2": "^3.6.5", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "axios": "^1.6.2", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "crypto": "^1.0.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "cos-nodejs-sdk-v5": "^2.12.4", "tencentcloud-sdk-nodejs": "^4.0.622"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "supertest": "^6.3.3", "nock": "^13.4.0", "js-yaml": "^4.1.0", "@jest/globals": "^29.7.0", "serverless": "^3.38.0", "serverless-webpack": "^5.13.0", "webpack": "^5.89.0", "webpack-node-externals": "^3.0.0", "nodemon": "^3.0.2", "express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "chokidar": "^3.5.3", "mime-types": "^2.1.35", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "cross-env": "^7.0.3", "artillery": "^2.0.3", "newman": "^6.0.0"}, "engines": {"node": ">=18.0.0"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.{json,yaml,yml,md}": ["prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:unit && npm run lint"}}, "keywords": ["dialect", "game", "wechat", "serverless", "tencent-cloud"], "author": "Hometown Dialect Game Team", "license": "MIT"}