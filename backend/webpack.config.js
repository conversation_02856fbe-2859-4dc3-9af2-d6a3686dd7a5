const path = require('path');
const nodeExternals = require('webpack-node-externals');

module.exports = {
  entry: './serverless',
  target: 'node',
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  externals: [nodeExternals()],
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [['@babel/preset-env', { targets: { node: '18' } }]]
          }
        }
      }
    ]
  },
  resolve: {
    extensions: ['.js', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'serverless'),
      '@config': path.resolve(__dirname, 'config'),
      '@utils': path.resolve(__dirname, 'serverless/utils'),
      '@models': path.resolve(__dirname, 'serverless/models'),
      '@services': path.resolve(__dirname, 'serverless/services'),
      '@middleware': path.resolve(__dirname, 'serverless/middleware')
    }
  },
  output: {
    libraryTarget: 'commonjs2',
    path: path.resolve(__dirname, '.webpack'),
    filename: '[name].js'
  },
  optimization: {
    minimize: process.env.NODE_ENV === 'production'
  }
};