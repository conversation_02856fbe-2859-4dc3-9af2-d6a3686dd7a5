/**
 * 认证服务集成测试
 * 测试微信登录、令牌刷新、用户验证等功能
 */

const request = require('supertest');
const jwt = require('jsonwebtoken');
const { createTestApp } = require('../helpers/test-app');
const { createTestUser, cleanupTestData } = require('../helpers/test-data');

describe('认证服务集成测试', () => {
  let app;
  let testUser;
  let validToken;
  let refreshToken;

  beforeAll(async () => {
    app = await createTestApp();
    testUser = await createTestUser();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('POST /v1/auth/wechat/login', () => {
    test('应该成功处理微信登录', async () => {
      const mockWechatCode = 'test_wechat_code_123';
      
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: mockWechatCode,
          userInfo: {
            nickName: '测试用户',
            avatarUrl: 'https://example.com/avatar.jpg',
            gender: 1,
            country: '中国',
            province: '广东',
            city: '深圳'
          }
        })
        .expect(200);

      expect(response.body).toMatchObject({
        code: 0,
        message: 'success',
        data: {
          user: expect.objectContaining({
            id: expect.any(Number),
            openid: expect.any(String),
            nickname: '测试用户',
            avatar_url: 'https://example.com/avatar.jpg'
          }),
          tokens: {
            accessToken: expect.any(String),
            refreshToken: expect.any(String),
            expiresIn: expect.any(Number)
          }
        }
      });

      // 保存令牌用于后续测试
      validToken = response.body.data.tokens.accessToken;
      refreshToken = response.body.data.tokens.refreshToken;

      // 验证JWT令牌格式
      const decoded = jwt.decode(validToken);
      expect(decoded).toMatchObject({
        userId: expect.any(Number),
        openid: expect.any(String),
        sessionId: expect.any(String),
        iat: expect.any(Number),
        exp: expect.any(Number)
      });
    });

    test('应该拒绝无效的微信code', async () => {
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: 'invalid_code',
          userInfo: {
            nickName: '测试用户'
          }
        })
        .expect(400);

      expect(response.body).toMatchObject({
        code: 40001,
        message: expect.stringContaining('微信登录失败')
      });
    });

    test('应该验证必需的用户信息字段', async () => {
      const response = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: 'test_code'
          // 缺少userInfo
        })
        .expect(400);

      expect(response.body).toMatchObject({
        code: 40000,
        message: expect.stringContaining('参数验证失败')
      });
    });
  });

  describe('POST /v1/auth/refresh', () => {
    test('应该成功刷新访问令牌', async () => {
      const response = await request(app)
        .post('/v1/auth/refresh')
        .send({
          refreshToken: refreshToken
        })
        .expect(200);

      expect(response.body).toMatchObject({
        code: 0,
        message: 'success',
        data: {
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
          expiresIn: expect.any(Number)
        }
      });

      // 新令牌应该与旧令牌不同
      expect(response.body.data.accessToken).not.toBe(validToken);
      
      // 更新令牌
      validToken = response.body.data.accessToken;
      refreshToken = response.body.data.refreshToken;
    });

    test('应该拒绝无效的刷新令牌', async () => {
      const response = await request(app)
        .post('/v1/auth/refresh')
        .send({
          refreshToken: 'invalid_refresh_token'
        })
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('刷新令牌无效')
      });
    });

    test('应该拒绝过期的刷新令牌', async () => {
      // 创建一个过期的刷新令牌
      const expiredToken = jwt.sign(
        { userId: testUser.id, type: 'refresh' },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' }
      );

      const response = await request(app)
        .post('/v1/auth/refresh')
        .send({
          refreshToken: expiredToken
        })
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('刷新令牌已过期')
      });
    });
  });

  describe('POST /v1/auth/logout', () => {
    test('应该成功登出用户', async () => {
      const response = await request(app)
        .post('/v1/auth/logout')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 0,
        message: 'success'
      });
    });

    test('应该拒绝未认证的登出请求', async () => {
      const response = await request(app)
        .post('/v1/auth/logout')
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40100,
        message: expect.stringContaining('未提供认证令牌')
      });
    });
  });

  describe('GET /v1/auth/verify', () => {
    beforeEach(async () => {
      // 重新登录获取新令牌
      const loginResponse = await request(app)
        .post('/v1/auth/wechat/login')
        .send({
          code: 'test_wechat_code_456',
          userInfo: {
            nickName: '测试用户2'
          }
        });
      
      validToken = loginResponse.body.data.tokens.accessToken;
    });

    test('应该验证有效的访问令牌', async () => {
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        code: 0,
        message: 'success',
        data: {
          user: expect.objectContaining({
            id: expect.any(Number),
            openid: expect.any(String),
            nickname: expect.any(String)
          }),
          tokenInfo: {
            isValid: true,
            expiresAt: expect.any(String),
            sessionId: expect.any(String)
          }
        }
      });
    });

    test('应该拒绝无效的访问令牌', async () => {
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('访问令牌无效')
      });
    });

    test('应该拒绝过期的访问令牌', async () => {
      // 创建一个过期的访问令牌
      const expiredToken = jwt.sign(
        { userId: testUser.id, type: 'access' },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' }
      );

      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('访问令牌已过期')
      });
    });

    test('应该拒绝缺少Authorization头的请求', async () => {
      const response = await request(app)
        .get('/v1/auth/verify')
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40100,
        message: expect.stringContaining('未提供认证令牌')
      });
    });
  });

  describe('认证中间件测试', () => {
    test('应该正确处理Bearer令牌格式', async () => {
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(response.body.code).toBe(0);
    });

    test('应该拒绝错误的令牌格式', async () => {
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', validToken) // 缺少Bearer前缀
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40100,
        message: expect.stringContaining('认证令牌格式错误')
      });
    });

    test('应该处理令牌黑名单', async () => {
      // 先登出，将令牌加入黑名单
      await request(app)
        .post('/v1/auth/logout')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      // 再次使用该令牌应该被拒绝
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('令牌已失效')
      });
    });
  });

  describe('安全性测试', () => {
    test('应该防止JWT令牌伪造', async () => {
      // 使用错误的密钥签名令牌
      const fakeToken = jwt.sign(
        { userId: testUser.id, type: 'access' },
        'wrong_secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${fakeToken}`)
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('访问令牌无效')
      });
    });

    test('应该限制令牌的使用范围', async () => {
      // 使用刷新令牌访问需要访问令牌的接口
      const response = await request(app)
        .get('/v1/auth/verify')
        .set('Authorization', `Bearer ${refreshToken}`)
        .expect(401);

      expect(response.body).toMatchObject({
        code: 40101,
        message: expect.stringContaining('令牌类型错误')
      });
    });
  });
});
