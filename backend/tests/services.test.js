/**
 * Services 模块单元测试
 */

const { CosService, getCosService } = require('../serverless/services/CosService');
const TokenManager = require('../serverless/services/TokenManager');
const WechatAuthService = require('../serverless/services/WechatAuthService');

// Mock external dependencies
jest.mock('cos-nodejs-sdk-v5');
jest.mock('jsonwebtoken');
jest.mock('crypto');

describe('CosService Tests', () => {
  let cosService, mockCOS;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockCOS = {
      getObject: jest.fn(),
      putObject: jest.fn(),
      deleteObject: jest.fn(),
      headBucket: jest.fn(),
      getBucket: jest.fn()
    };

    const COS = require('cos-nodejs-sdk-v5');
    COS.mockImplementation(() => mockCOS);
    
    // Mock config
    jest.doMock('../config', () => ({
      cos: {
        secretId: 'test-secret-id',
        secretKey: 'test-secret-key',
        bucket: 'test-bucket',
        region: 'test-region',
        cdnDomain: 'test.cdn.com'
      },
      jwt: {
        accessSecret: 'test-access-secret',
        refreshSecret: 'test-refresh-secret',
        accessTokenTTL: 3600,
        refreshTokenTTL: 86400,
        issuer: 'test-issuer',
        audience: 'test-audience'
      },
      wechat: {
        appId: 'test-app-id',
        appSecret: 'test-app-secret',
        apiUrl: 'https://api.weixin.qq.com'
      }
    }));

    cosService = getCosService();
  });

  describe('getAudioResource', () => {
    it('应该成功获取音频资源', async () => {
      // Mock headObject method instead of getObject
      mockCOS.headObject.mockImplementation((params, callback) => {
        callback(null, {
          headers: {
            'content-length': '1024',
            'content-type': 'audio/mpeg',
            'last-modified': '2025-07-31T01:00:00.000Z',
            'etag': '"abc123"'
          }
        });
      });
      
      // Mock getObjectUrl method
      mockCOS.getObjectUrl.mockReturnValue('https://example.com/test-audio-123.mp3');

      const result = await cosService.getAudioResource('test_audio_123');

      expect(result).toEqual({
        resourceId: 'test_audio_123',
        path: expect.stringContaining('test'),
        size: '1024',
        contentType: 'audio/mpeg',
        lastModified: '2025-07-31T01:00:00.000Z',
        etag: '"abc123"',
        url: expect.any(String),
        directUrl: expect.any(String),
        cdnUrl: expect.any(String),
        cacheMaxAge: expect.any(Number)
      });

      expect(mockCOS.headObject).toHaveBeenCalledWith({
        Bucket: expect.any(String),
        Region: expect.any(String),
        Key: expect.stringContaining('test')
      });
    });

    it('应该处理资源不存在', async () => {
      mockCOS.getObject.mockImplementation((params, callback) => {
        callback({ statusCode: 404, code: 'NoSuchKey' });
      });

      await expect(cosService.getAudioResource('nonexistent'))
        .rejects
        .toThrow('AUDIO_NOT_FOUND');
    });

    it('应该处理网络错误', async () => {
      mockCOS.getObject.mockImplementation((params, callback) => {
        callback(new Error('Network error'));
      });

      await expect(cosService.getAudioResource('test-audio'))
        .rejects
        .toThrow('AUDIO_ACCESS_FAILED');
    });
  });

  describe('listAudioResources', () => {
    it('应该成功列出音频资源', async () => {
      const mockResponse = {
        Contents: [
          {
            Key: 'audio/dialect/test1.mp3',
            Size: 1024,
            LastModified: '2025-07-31T01:00:00.000Z'
          },
          {
            Key: 'audio/dialect/test2.mp3',
            Size: 2048,
            LastModified: '2025-07-31T02:00:00.000Z'
          }
        ]
      };

      mockCOS.getBucket.mockImplementation((params, callback) => {
        callback(null, mockResponse);
      });

      const result = await cosService.listAudioResources('dialect', 10);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 'test1',
        url: expect.stringContaining('test1.mp3'),
        size: 1024,
        category: 'dialect',
        lastModified: '2025-07-31T01:00:00.000Z'
      });

      expect(mockCOS.getBucket).toHaveBeenCalledWith({
        Bucket: expect.any(String),
        Region: expect.any(String),
        Prefix: 'audio/dialect/',
        MaxKeys: 10
      }, expect.any(Function));
    });

    it('应该处理空结果', async () => {
      mockCOS.getBucket.mockImplementation((params, callback) => {
        callback(null, { Contents: [] });
      });

      const result = await cosService.listAudioResources('empty', 10);

      expect(result).toEqual([]);
    });
  });

  describe('uploadAudio', () => {
    it('应该成功上传音频文件', async () => {
      const mockResponse = {
        statusCode: 200,
        headers: {
          etag: '"abc123"'
        },
        Location: 'https://example.com/audio/test.mp3'
      };

      mockCOS.putObject.mockImplementation((params, callback) => {
        callback(null, mockResponse);
      });

      const audioBuffer = Buffer.from('fake audio data');
      const result = await cosService.uploadAudio('test-audio', audioBuffer, 'audio/mpeg');

      expect(result).toEqual({
        id: 'test-audio',
        url: expect.stringContaining('test-audio'),
        size: audioBuffer.length,
        contentType: 'audio/mpeg',
        etag: 'abc123'
      });

      expect(mockCOS.putObject).toHaveBeenCalledWith({
        Bucket: expect.any(String),
        Region: expect.any(String),
        Key: expect.stringContaining('test-audio'),
        Body: audioBuffer,
        ContentType: 'audio/mpeg'
      }, expect.any(Function));
    });

    it('应该处理上传失败', async () => {
      mockCOS.putObject.mockImplementation((params, callback) => {
        callback(new Error('Upload failed'));
      });

      const audioBuffer = Buffer.from('fake audio data');
      
      await expect(cosService.uploadAudio('test-audio', audioBuffer, 'audio/mpeg'))
        .rejects
        .toThrow('Upload failed');
    });
  });

  describe('deleteAudio', () => {
    it('应该成功删除音频文件', async () => {
      mockCOS.deleteObject.mockImplementation((params, callback) => {
        callback(null, { statusCode: 204 });
      });

      const result = await cosService.deleteAudio('test-audio');

      expect(result).toEqual({
        id: 'test-audio',
        deleted: true
      });

      expect(mockCOS.deleteObject).toHaveBeenCalledWith({
        Bucket: expect.any(String),
        Region: expect.any(String),
        Key: expect.stringContaining('test-audio')
      }, expect.any(Function));
    });

    it('应该处理删除失败', async () => {
      mockCOS.deleteObject.mockImplementation((params, callback) => {
        callback(new Error('Delete failed'));
      });

      await expect(cosService.deleteAudio('test-audio'))
        .rejects
        .toThrow('Delete failed');
    });
  });

  describe('getStorageStats', () => {
    it('应该成功获取存储统计', async () => {
      const mockResponse = {
        Contents: [
          { Key: 'audio/dialect/test1.mp3', Size: 1024 },
          { Key: 'audio/music/test2.mp3', Size: 2048 },
          { Key: 'audio/sound/test3.mp3', Size: 512 }
        ]
      };

      mockCOS.getBucket.mockImplementation((params, callback) => {
        callback(null, mockResponse);
      });

      const result = await cosService.getStorageStats();

      expect(result).toEqual({
        totalFiles: 3,
        totalSize: 3584,
        categories: {
          dialect: { count: 1, size: 1024 },
          music: { count: 1, size: 2048 },
          sound: { count: 1, size: 512 }
        },
        averageFileSize: expect.any(Number)
      });
    });
  });
});

describe('TokenManager Tests', () => {
  let tokenManager;

  beforeEach(() => {
    jest.clearAllMocks();
    tokenManager = new TokenManager();

    // Mock JWT
    const jwt = require('jsonwebtoken');
    jwt.sign = jest.fn();
    jwt.verify = jest.fn();
    jwt.decode = jest.fn();
  });

  describe('generateToken', () => {
    it('应该成功生成访问令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.sign.mockReturnValue('mock-access-token');

      const payload = { userId: 'user-123', scopes: ['user:read'] };
      const result = tokenManager.generateToken(payload, 'access');

      expect(result).toBe('mock-access-token');
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          ...payload,
          type: 'access',
          iat: expect.any(Number),
          exp: expect.any(Number)
        }),
        expect.any(String),
        { algorithm: 'HS256' }
      );
    });

    it('应该成功生成刷新令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.sign.mockReturnValue('mock-refresh-token');

      const payload = { userId: 'user-123' };
      const result = tokenManager.generateToken(payload, 'refresh');

      expect(result).toBe('mock-refresh-token');
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          ...payload,
          type: 'refresh',
          exp: expect.any(Number)
        }),
        expect.any(String),
        { algorithm: 'HS256' }
      );
    });

    it('应该处理无效的令牌类型', () => {
      expect(() => tokenManager.generateToken({}, 'invalid'))
        .toThrow('Invalid token type');
    });
  });

  describe('verifyToken', () => {
    it('应该成功验证有效令牌', () => {
      const jwt = require('jsonwebtoken');
      const mockPayload = {
        userId: 'user-123',
        type: 'access',
        scopes: ['user:read'],
        iat: 1234567890,
        exp: 1234567890 + 3600
      };

      jwt.verify.mockReturnValue(mockPayload);

      const result = tokenManager.verifyToken('valid-token');

      expect(result).toEqual(mockPayload);
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', expect.any(String));
    });

    it('应该处理过期令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        const error = new Error('jwt expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      expect(() => tokenManager.verifyToken('expired-token'))
        .toThrow('Token expired');
    });

    it('应该处理无效令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        const error = new Error('invalid signature');
        error.name = 'JsonWebTokenError';
        throw error;
      });

      expect(() => tokenManager.verifyToken('invalid-token'))
        .toThrow('Invalid token');
    });
  });

  describe('decodeToken', () => {
    it('应该成功解码令牌', () => {
      const jwt = require('jsonwebtoken');
      const mockPayload = {
        userId: 'user-123',
        type: 'access',
        iat: 1234567890,
        exp: 1234567890 + 3600
      };

      jwt.decode.mockReturnValue(mockPayload);

      const result = tokenManager.decodeToken('some-token');

      expect(result).toEqual(mockPayload);
      expect(jwt.decode).toHaveBeenCalledWith('some-token');
    });

    it('应该处理无效格式的令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.decode.mockReturnValue(null);

      const result = tokenManager.decodeToken('invalid-format');

      expect(result).toBeNull();
    });
  });

  describe('refreshToken', () => {
    it('应该成功刷新令牌', () => {
      const jwt = require('jsonwebtoken');
      
      // Mock verify for refresh token
      jwt.verify.mockReturnValue({
        userId: 'user-123',
        type: 'refresh'
      });
      
      // Mock sign for new access token
      jwt.sign.mockReturnValue('new-access-token');

      const result = tokenManager.refreshToken('valid-refresh-token');

      expect(result.accessToken).toBe('new-access-token');
      expect(result.tokenType).toBe('Bearer');
      expect(result.expiresIn).toBe(3600);
    });

    it('应该拒绝非刷新令牌', () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockReturnValue({
        userId: 'user-123',
        type: 'access' // 不是refresh类型
      });

      expect(() => tokenManager.refreshToken('access-token'))
        .toThrow('Invalid refresh token');
    });
  });

  describe('generateTokenPair', () => {
    it('应该成功生成令牌对', () => {
      const jwt = require('jsonwebtoken');
      jwt.sign.mockReturnValueOnce('access-token')
              .mockReturnValueOnce('refresh-token');

      const payload = { userId: 'user-123', scopes: ['user:read'] };
      const result = tokenManager.generateTokenPair(payload);

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresIn: 3600
      });

      expect(jwt.sign).toHaveBeenCalledTimes(2);
    });
  });
});

describe('WechatAuthService Tests', () => {
  let wechatService;

  beforeEach(() => {
    jest.clearAllMocks();
    wechatService = new WechatAuthService();

    // Mock crypto
    const crypto = require('crypto');
    crypto.createHash = jest.fn().mockReturnValue({
      update: jest.fn().mockReturnThis(),
      digest: jest.fn().mockReturnValue('mocked-signature')
    });

    // Mock fetch
    global.fetch = jest.fn();
  });

  describe('validateSignature', () => {
    it('应该成功验证有效签名', () => {
      const params = {
        signature: 'mocked-signature',
        timestamp: '1640995200',
        nonce: 'test-nonce',
        echostr: 'test-echo'
      };

      const result = wechatAuthService.verifySignature(
        params.signature,
        params.timestamp,
        params.nonce,
        'test-token'
      );

      expect(result).toBe(true);
      expect(require('crypto').createHash).toHaveBeenCalledWith('sha1');
    });

    it('应该拒绝无效签名', () => {
      const params = {
        signature: 'invalid-signature',
        timestamp: '1640995200',
        nonce: 'test-nonce',
        echostr: 'test-echo'
      };

      const result = wechatAuthService.verifySignature(
        params.signature,
        params.timestamp,
        params.nonce,
        'test-token'
      );

      expect(result).toBe(false);
    });

    it('应该处理缺少参数', () => {
      const params = {
        signature: 'test-signature'
        // 缺少其他参数
      };

      expect(() => wechatService.validateSignature(params))
        .toThrow('Missing required parameters');
    });
  });

  describe('getAccessToken', () => {
    it('应该成功获取访问令牌', async () => {
      const mockResponse = {
        access_token: 'ACCESS_TOKEN_123',
        expires_in: 7200
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await wechatService.getAccessToken();

      expect(result).toEqual({
        accessToken: 'ACCESS_TOKEN_123',
        expiresIn: 7200,
        expiresAt: expect.any(Number)
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('access_token'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('应该处理API错误响应', async () => {
      const mockErrorResponse = {
        errcode: 40001,
        errmsg: 'invalid credential'
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockErrorResponse)
      });

      await expect(wechatService.getAccessToken())
        .rejects
        .toThrow('WeChat API Error: invalid credential');
    });

    it('应该处理网络错误', async () => {
      global.fetch.mockRejectedValue(new Error('Network error'));

      await expect(wechatService.getAccessToken())
        .rejects
        .toThrow('Network error');
    });
  });

  describe('getUserInfo', () => {
    it('应该成功获取用户信息', async () => {
      const mockUserInfo = {
        openid: 'OPENID_123',
        nickname: '测试用户',
        headimgurl: 'https://example.com/avatar.jpg',
        sex: 1,
        province: '广东',
        city: '深圳',
        country: '中国'
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockUserInfo)
      });

      const result = await wechatService.getUserInfo('ACCESS_TOKEN', 'OPENID_123');

      expect(result).toEqual({
        openid: 'OPENID_123',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        gender: 1,
        province: '广东',
        city: '深圳',
        country: '中国'
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('userinfo'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('应该处理用户信息获取失败', async () => {
      const mockErrorResponse = {
        errcode: 40003,
        errmsg: 'invalid openid'
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockErrorResponse)
      });

      await expect(wechatService.getUserInfo('ACCESS_TOKEN', 'INVALID_OPENID'))
        .rejects
        .toThrow('WeChat API Error: invalid openid');
    });
  });

  describe('code2Session', () => {
    it('应该成功获取会话信息', async () => {
      const mockResponse = {
        openid: 'OPENID_123',
        session_key: 'SESSION_KEY_123',
        unionid: 'UNIONID_123'
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await wechatService.code2Session('JS_CODE_123');

      expect(result).toEqual({
        openid: 'OPENID_123',
        sessionKey: 'SESSION_KEY_123',
        unionid: 'UNIONID_123'
      });

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('jscode2session'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('应该处理无效的授权码', async () => {
      const mockErrorResponse = {
        errcode: 40029,
        errmsg: 'invalid code'
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockErrorResponse)
      });

      await expect(wechatService.code2Session('INVALID_CODE'))
        .rejects
        .toThrow('WeChat API Error: invalid code');
    });
  });

  describe('缓存机制', () => {
    it('应该缓存访问令牌', async () => {
      const mockResponse = {
        access_token: 'CACHED_TOKEN',
        expires_in: 7200
      };

      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // 第一次调用
      const result1 = await wechatService.getAccessToken();
      
      // 第二次调用应该使用缓存
      const result2 = await wechatService.getAccessToken();

      expect(result1.accessToken).toBe('CACHED_TOKEN');
      expect(result2.accessToken).toBe('CACHED_TOKEN');
      expect(global.fetch).toHaveBeenCalledTimes(1); // 只调用了一次API
    });

    it('应该在令牌过期后重新获取', async () => {
      const mockResponse1 = {
        access_token: 'TOKEN_1',
        expires_in: 1 // 1秒后过期
      };

      const mockResponse2 = {
        access_token: 'TOKEN_2',
        expires_in: 7200
      };

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse1)
      }).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse2)
      });

      // 第一次调用
      const result1 = await wechatService.getAccessToken();
      expect(result1.accessToken).toBe('TOKEN_1');

      // 等待令牌过期
      await new Promise(resolve => setTimeout(resolve, 1100));

      // 第二次调用应该重新获取
      const result2 = await wechatService.getAccessToken();
      expect(result2.accessToken).toBe('TOKEN_2');
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });
});