/**
 * Utils 模块单元测试 - 简化版本
 * 专注于错误处理类的测试
 */

const { APIError, ValidationError, NotFoundError } = require('../../serverless/utils/errors');

describe('Error Utils Tests', () => {
  describe('APIError', () => {
    it('应该正确创建APIError实例', () => {
      const error = new APIError('TEST_CODE', '测试错误', { detail: 'test' }, 400);
      
      expect(error.code).toBe('TEST_CODE');
      expect(error.message).toBe('测试错误');
      expect(error.details).toEqual({ detail: 'test' });
      expect(error.statusCode).toBe(400);
      expect(error.name).toBe('APIError');
    });

    it('应该使用默认状态码400', () => {
      const error = new APIError('TEST_CODE', '测试错误');

      expect(error.statusCode).toBe(400);
      expect(error.details).toBeNull();
    });

    it('应该正确转换为响应格式', () => {
      const error = new APIError('TEST_CODE', '测试错误', { field: 'test' }, 400);
      const response = error.toResponse();

      expect(response).toEqual({
        code: 'TEST_CODE',
        message: '测试错误',
        details: { field: 'test' },
        timestamp: expect.any(String),
        requestId: null
      });
    });
  });

  describe('ValidationError', () => {
    it('应该正确创建ValidationError实例', () => {
      const error = new ValidationError({ field: 'username', message: '用户名无效' });

      expect(error.message).toBe('参数验证失败');
      expect(error.details).toEqual({ field: 'username', message: '用户名无效' });
      expect(error.statusCode).toBe(400);
      expect(error.name).toBe('ValidationError');
    });

    it('应该继承自APIError', () => {
      const error = new ValidationError({ field: 'test' });

      expect(error).toBeInstanceOf(APIError);
      expect(error.code).toBe('INVALID_PARAMS');
    });
  });

  describe('NotFoundError', () => {
    it('应该正确创建NotFoundError实例', () => {
      const error = new NotFoundError('用户');

      expect(error.message).toBe('用户不存在');
      expect(error.statusCode).toBe(404);
      expect(error.code).toBe('RESOURCE_NOT_FOUND');
      expect(error.name).toBe('NotFoundError');
    });

    it('应该继承自APIError', () => {
      const error = new NotFoundError('资源');

      expect(error).toBeInstanceOf(APIError);
    });
  });
});

// 数据库和Redis测试已移至集成测试
// 这里只保留错误处理的单元测试
