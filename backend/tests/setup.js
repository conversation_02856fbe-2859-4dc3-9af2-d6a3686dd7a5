/**
 * Jest测试全局设置
 * 配置测试环境、全局变量、模拟等
 */

const { config } = require('dotenv');
const path = require('path');

// 加载测试环境变量
config({ path: path.join(__dirname, '../.env.test') });

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
process.env.JWT_EXPIRES_IN = '1h';
process.env.JWT_REFRESH_EXPIRES_IN = '7d';

// 数据库配置
process.env.TEST_DB_HOST = 'localhost';
process.env.TEST_DB_PORT = '3306';
process.env.TEST_DB_USER = 'test';
process.env.TEST_DB_PASSWORD = 'test';
process.env.TEST_DB_NAME = 'dialect_game_test';

// Redis配置
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.REDIS_PASSWORD = '';
process.env.TEST_REDIS_DB = '1';

// 微信配置
process.env.WECHAT_APP_ID = 'test_wechat_appid';
process.env.WECHAT_APP_SECRET = 'test_wechat_secret';

// COS配置
process.env.COS_SECRET_ID = 'test_cos_secret_id';
process.env.COS_SECRET_KEY = 'test_cos_secret_key';
process.env.COS_BUCKET = 'test-bucket';
process.env.COS_REGION = 'ap-guangzhou';

// 模拟中间件
jest.mock('../serverless/middleware/auth.js', () => ({
  authMiddleware: jest.fn(() => (req, res, next) => {
    req.user = { userId: 1, openid: 'test_openid_123' };
    next();
  }),
  ownershipCheck: jest.fn(() => (req, res, next) => next()),
  optionalAuth: jest.fn(() => (req, res, next) => next())
}));

jest.mock('../serverless/middleware/validation.js', () => ({
  validateRequest: jest.fn(() => (req, res, next) => next()),
  VALIDATION_SCHEMAS: {
    wechatLogin: {
      type: 'object',
      properties: {
        code: { type: 'string' }
      }
    },
    pathParams: {
      userId: { type: 'string' }
    }
  }
}));

jest.mock('../serverless/middleware/rateLimit.js', () => ({
  rateLimitMiddleware: jest.fn(() => (req, res, next) => next())
}));

// 模拟工具函数
jest.mock('../serverless/utils/database.js', () => ({
  createConnection: jest.fn().mockResolvedValue({
    execute: jest.fn().mockResolvedValue([[], []]),
    query: jest.fn().mockResolvedValue([[], []]),
    release: jest.fn()
  }),
  executeQuery: jest.fn().mockResolvedValue([[], []]),
  withTransaction: jest.fn().mockImplementation(async (connection, callback) => {
    return await callback(connection);
  })
}));

jest.mock('../serverless/utils/redis.js', () => ({
  RedisManager: {
    getInstance: jest.fn(() => ({
      connect: jest.fn().mockResolvedValue(),
      disconnect: jest.fn().mockResolvedValue(),
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue('OK'),
      del: jest.fn().mockResolvedValue(1)
    }))
  }
}));

// 全局测试超时
jest.setTimeout(30000);

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// 模拟数据库连接
jest.mock('../serverless/utils/database.js', () => ({
  DatabaseManager: {
    getInstance: jest.fn(() => ({
      initialize: jest.fn().mockResolvedValue(true),
      getPool: jest.fn(() => ({
        execute: jest.fn().mockResolvedValue([[], []]),
        query: jest.fn().mockResolvedValue([[], []]),
        getConnection: jest.fn().mockResolvedValue({
          execute: jest.fn().mockResolvedValue([[], []]),
          query: jest.fn().mockResolvedValue([[], []]),
          release: jest.fn(),
          beginTransaction: jest.fn().mockResolvedValue(),
          commit: jest.fn().mockResolvedValue(),
          rollback: jest.fn().mockResolvedValue()
        })
      })),
      close: jest.fn().mockResolvedValue(),
      isInitialized: jest.fn().mockReturnValue(true)
    }))
  }
}));

// 模拟Redis连接
jest.mock('../serverless/utils/redis.js', () => ({
  RedisManager: {
    getInstance: jest.fn(() => ({
      initialize: jest.fn().mockResolvedValue(true),
      getClient: jest.fn(() => ({
        get: jest.fn().mockResolvedValue(null),
        set: jest.fn().mockResolvedValue('OK'),
        setex: jest.fn().mockResolvedValue('OK'),
        del: jest.fn().mockResolvedValue(1),
        incr: jest.fn().mockResolvedValue(1),
        expire: jest.fn().mockResolvedValue(1),
        ttl: jest.fn().mockResolvedValue(-1),
        keys: jest.fn().mockResolvedValue([]),
        flushdb: jest.fn().mockResolvedValue('OK'),
        quit: jest.fn().mockResolvedValue('OK')
      })),
      close: jest.fn().mockResolvedValue(),
      isInitialized: jest.fn().mockReturnValue(true)
    }))
  }
}));

// 模拟COS服务
jest.mock('../serverless/services/CosService.js', () => ({
  CosService: jest.fn().mockImplementation(() => ({
    getAudioResource: jest.fn().mockResolvedValue({
      url: 'https://example.com/test.mp3',
      size: 1024,
      lastModified: new Date()
    }),
    listAudioResources: jest.fn().mockResolvedValue([
      { key: 'test1.mp3', size: 1024 },
      { key: 'test2.mp3', size: 2048 }
    ]),
    uploadAudio: jest.fn().mockResolvedValue({
      location: 'https://example.com/uploaded.mp3'
    }),
    deleteAudio: jest.fn().mockResolvedValue(true),
    getStorageStats: jest.fn().mockResolvedValue({
      totalSize: 3072,
      fileCount: 2
    })
  }))
}));

// 模拟微信服务
jest.mock('../serverless/services/WechatAuthService.js', () => ({
  WechatAuthService: jest.fn().mockImplementation(() => ({
    code2Session: jest.fn().mockResolvedValue({
      openid: 'test_openid_123',
      session_key: 'test_session_key',
      unionid: 'test_unionid_123'
    }),
    decryptUserData: jest.fn().mockResolvedValue({
      nickName: '测试用户',
      avatarUrl: 'https://example.com/avatar.jpg',
      gender: 1
    })
  }))
}));

// 模拟令牌服务
jest.mock('../serverless/services/TokenManager.js', () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    generateTokens: jest.fn().mockResolvedValue({
      accessToken: 'test_access_token',
      refreshToken: 'test_refresh_token',
      expiresIn: 3600
    }),
    verifyToken: jest.fn().mockResolvedValue({
      userId: 1,
      openid: 'test_openid_123',
      sessionId: 'test_session_123'
    }),
    refreshTokens: jest.fn().mockResolvedValue({
      accessToken: 'new_access_token',
      refreshToken: 'new_refresh_token',
      expiresIn: 3600
    }),
    revokeToken: jest.fn().mockResolvedValue(true)
  }))
}));

// 模拟console方法以减少测试输出噪音
const originalConsole = { ...console };

beforeAll(() => {
  // 在测试期间静默某些console输出
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  // 保留error输出用于调试
  console.error = originalConsole.error;
});

afterAll(() => {
  // 恢复console方法
  Object.assign(console, originalConsole);
});

// 全局测试工具
global.testUtils = {
  /**
   * 等待指定时间
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * 生成随机字符串
   */
  randomString: (length = 10) => {
    return Math.random().toString(36).substring(2, length + 2);
  },

  /**
   * 生成随机数字
   */
  randomNumber: (min = 1, max = 100) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * 验证API响应格式
   */
  validateApiResponse: (response) => {
    expect(response).toHaveProperty('code');
    expect(response).toHaveProperty('message');
    expect(response).toHaveProperty('timestamp');
    expect(typeof response.code).toBe('number');
    expect(typeof response.message).toBe('string');
    expect(typeof response.timestamp).toBe('string');
  },

  /**
   * 验证成功响应
   */
  validateSuccessResponse: (response, expectedData = null) => {
    global.testUtils.validateApiResponse(response);
    expect(response.code).toBe(0);
    expect(response.message).toBe('success');

    if (expectedData) {
      expect(response).toHaveProperty('data');
      if (typeof expectedData === 'object') {
        expect(response.data).toMatchObject(expectedData);
      }
    }
  },

  /**
   * 验证错误响应
   */
  validateErrorResponse: (response, expectedCode = null, expectedMessage = null) => {
    global.testUtils.validateApiResponse(response);
    expect(response.code).not.toBe(0);

    if (expectedCode) {
      expect(response.code).toBe(expectedCode);
    }

    if (expectedMessage) {
      expect(response.message).toContain(expectedMessage);
    }
  }
};

// 测试数据库连接检查
beforeAll(async () => {
  console.log('🧪 测试环境初始化完成');
});

afterAll(async () => {
  console.log('🧹 测试环境清理完成');
});