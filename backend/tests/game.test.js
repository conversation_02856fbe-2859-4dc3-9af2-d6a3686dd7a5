/**
 * Game 模块单元测试
 */

const gameHandler = require('../serverless/game/handler');
const Question = require('../serverless/models/Question');
const GameSession = require('../serverless/models/GameSession');
const GameResult = require('../serverless/models/GameResult');

// Mock dependencies
jest.mock('../serverless/models/Question');
jest.mock('../serverless/models/GameSession');
jest.mock('../serverless/models/GameResult');
jest.mock('../serverless/middleware/auth');
jest.mock('../serverless/middleware/validation');
jest.mock('../serverless/middleware/rateLimit');
jest.mock('../serverless/utils/errors');

describe('Game Handler Tests', () => {
  let mockEvent, mockContext;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock event and context
    mockEvent = {
      httpMethod: 'GET',
      path: '/v1/questions',
      headers: {
        'content-type': 'application/json'
      },
      queryStringParameters: {
        count: '10',
        difficulty: '1',
        random: 'true'
      }
    };

    mockContext = {
      requestId: 'test-request-123',
      functionName: 'game-handler'
    };

    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { validateRequest } = require('../serverless/middleware/validation');
    const { authMiddleware } = require('../serverless/middleware/auth');
    
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    validateRequest.mockImplementation(() => ({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));
  });

  describe('getQuestions', () => {
    it('应该成功获取随机题目列表', async () => {
      const mockQuestions = [
        {
          id: 1,
          text: '这句四川话是什么意思？',
          audioUrl: 'https://example.com/audio1.mp3',
          options: ['答案1', '错误1', '错误2', '错误3'],
          difficulty: 1,
          dialect: '四川话',
          hint: '提示信息',
          tags: ['四川话', '难度1']
        },
        {
          id: 2,
          text: '这句上海话是什么意思？',
          audioUrl: 'https://example.com/audio2.mp3',
          options: ['答案2', '错误1', '错误2', '错误3'],
          difficulty: 1,
          dialect: '上海话',
          hint: '提示信息',
          tags: ['上海话', '难度1']
        }
      ];

      Question.getRandomQuestions.mockResolvedValue(mockQuestions);

      // 直接调用handler函数进行测试
      const result = await gameHandler.getQuestions(mockEvent, mockContext);

      expect(result.questions).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.params.difficulty).toBe(1);
      expect(result.params.random).toBe(true);
      
      expect(Question.getRandomQuestions).toHaveBeenCalledWith(10, 1, undefined);
    });

    it('应该限制题目数量在1-50范围内', async () => {
      mockEvent.queryStringParameters = { count: '100' };
      
      Question.getRandomQuestions.mockResolvedValue([]);

      await gameHandler.getQuestions(mockEvent, mockContext);

      // 应该被限制为50题
      expect(Question.getRandomQuestions).toHaveBeenCalledWith(50, null, undefined);
    });

    it('应该处理无效的count参数', async () => {
      mockEvent.queryStringParameters = { count: 'invalid' };
      
      Question.getRandomQuestions.mockResolvedValue([]);

      await gameHandler.getQuestions(mockEvent, mockContext);

      // parseInt('invalid') returns NaN, Math.max(NaN, 1) returns NaN, Math.min(NaN, 50) returns NaN
      expect(Question.getRandomQuestions).toHaveBeenCalledWith(NaN, null, undefined);
    });

    it('应该处理数据库查询错误', async () => {
      Question.getRandomQuestions.mockRejectedValue(new Error('数据库连接失败'));

      await expect(gameHandler.getQuestions(mockEvent, mockContext))
        .rejects
        .toThrow('获取题目失败');
    });
  });

  describe('createGameSession', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'POST';
      mockEvent.path = '/v1/game-sessions';
      mockEvent.body = JSON.stringify({
        userId: 'user-123',
        questionCount: 5,
        difficulty: 1
      });

      // Mock validation middleware - returns the data directly
      const { validateRequest } = require('../serverless/middleware/validation');
      validateRequest.mockReturnValue({
        userId: 'user-123',
        questionCount: 5,
        difficulty: 1
      });
    });

    it('应该成功创建游戏会话', async () => {
      const mockQuestions = [
        { id: 1, text: '题目1' },
        { id: 2, text: '题目2' },
        { id: 3, text: '题目3' },
        { id: 4, text: '题目4' },
        { id: 5, text: '题目5' }
      ];

      const mockSession = {
        sessionId: 'session-123',
        userId: 'user-123',
        questionCount: 5,
        difficulty: 1,
        category: 'general',
        gameMode: 'standard',
        questions: mockQuestions,
        toJSON: jest.fn().mockReturnValue({
          sessionId: 'session-123',
          userId: 'user-123',
          questionCount: 5,
          status: 'active'
        })
      };

      // Mock event user
      mockEvent.user = { id: 'user-123' };
      
      Question.getRandomQuestions.mockResolvedValue(mockQuestions);
      GameSession.findActiveByUserId.mockResolvedValue(null);
      GameSession.create.mockResolvedValue(mockSession);

      const result = await gameHandler.createGameSession(mockEvent, mockContext);

      expect(result.gameSession.sessionId).toBe('session-123');
      expect(result.gameSession.questionCount).toBe(5);
      expect(result.firstQuestion).toBeDefined();

      expect(GameSession.create).toHaveBeenCalledWith('user-123', {
        category: 'general',
        difficulty: 1,
        questionCount: 5,
        gameMode: 'standard'
      });
    });

    it('应该处理题目数量不足的情况', async () => {
      mockEvent.body = JSON.stringify({
        userId: 'user-123',
        questionCount: 20
      });

      const { validateRequest } = require('../serverless/middleware/validation');
      validateRequest.mockReturnValue({
        userId: 'user-123',
        questionCount: 20
      });

      // Mock event user
      mockEvent.user = { id: 'user-123' };
      
      GameSession.findActiveByUserId.mockResolvedValue(null);
      GameSession.create.mockResolvedValue({ questionCount: 20 });
      Question.getRandomQuestions.mockResolvedValue([
        { id: 1, text: '题目1' },
        { id: 2, text: '题目2' }
      ]); // 只有2题

      await expect(gameHandler.createGameSession(mockEvent, mockContext))
        .rejects
        .toThrow('可用题目数量不足');
    });
  });

  describe('submitAnswer', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'POST';
      mockEvent.path = '/v1/game-sessions/session-123/answer';
      mockEvent.pathParameters = { sessionId: 'session-123' };
      mockEvent.body = JSON.stringify({
        questionId: 1,
        answer: 0,
        timeSpent: 5.2
      });

      // Mock validation middleware
      const { validateRequest } = require('../serverless/middleware/validation');
      validateRequest.mockReturnValue({
        questionId: 1,
        userAnswer: '0',
        answerTime: 5,
        hintUsed: false
      });
    });

    it('应该成功提交答案', async () => {
      const mockSession = {
        sessionId: 'session-123',
        userId: 'user-123',
        currentQuestion: 0,
        status: 'active',
        questions: [
          { id: 1, text: '题目1', correctAnswer: 0 }
        ]
      };

      const mockResult = {
        correct: true,
        correctAnswer: 0,
        timeSpent: 5.2,
        score: 100,
        progress: {
          current: 1,
          total: 5,
          percentage: 20
        }
      };

      // Mock event user
      mockEvent.user = { id: 'user-123' };
      
      const mockQuestion = {
        id: 1,
        checkAnswer: jest.fn().mockReturnValue(true),
        calculateScore: jest.fn().mockReturnValue(100),
        standardAnswer: '答案1',
        explanation: '这是正确答案的解释'
      };
      
      mockSession.isValid = jest.fn().mockReturnValue(true);
      mockSession.updateProgress = jest.fn().mockResolvedValue(false);
      mockSession.getStats = jest.fn().mockReturnValue({ totalScore: 100 });
      mockSession.toJSON = jest.fn().mockReturnValue({ sessionId: 'session-123' });
      
      GameSession.findBySessionId.mockResolvedValue(mockSession);
      Question.findById.mockResolvedValue(mockQuestion);
      GameResult.create.mockResolvedValue({});
      Question.updateUsageStats.mockResolvedValue({});

      const result = await gameHandler.submitAnswer(mockEvent, mockContext);

      expect(result.result.isCorrect).toBe(true);
      expect(result.result.scoreEarned).toBe(100);
      expect(result.isGameCompleted).toBe(false);

      expect(GameResult.create).toHaveBeenCalledWith({
        userId: 'user-123',
        gameSessionId: 'session-123',
        questionId: 1,
        userAnswer: '0',
        isCorrect: true,
        answerTime: 5,
        scoreEarned: 100,
        streakCount: 1,
        hintUsed: false
      });
    });

    it('应该处理会话不存在的情况', async () => {
      mockEvent.user = { id: 'user-123' };
      GameSession.findBySessionId.mockResolvedValue(null);

      await expect(gameHandler.submitAnswer(mockEvent, mockContext))
        .rejects
        .toThrow('游戏会话不存在');
    });

    it('应该处理会话已结束的情况', async () => {
      const mockSession = {
        sessionId: 'session-123',
        userId: 'user-123',
        isValid: jest.fn().mockReturnValue(false)
      };

      mockEvent.user = { id: 'user-123' };
      GameSession.findBySessionId.mockResolvedValue(mockSession);

      await expect(gameHandler.submitAnswer(mockEvent, mockContext))
        .rejects
        .toThrow('游戏会话已过期或已结束');
    });
  });

  describe('getGameSession', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/game-sessions/session-123';
      mockEvent.pathParameters = { sessionId: 'session-123' };
    });

    it('应该成功获取游戏会话信息', async () => {
      const mockSession = {
        sessionId: 'session-123',
        userId: 'user-123',
        totalQuestions: 5,
        currentQuestion: 2,
        status: 1,
        score: 200,
        createdAt: '2025-07-31T01:00:00.000Z',
        isValid: jest.fn().mockReturnValue(true),
        cancel: jest.fn(),
        toJSON: jest.fn().mockReturnValue({
          sessionId: 'session-123',
          currentQuestion: 2,
          status: 'active',
          score: 200
        })
      };

      mockEvent.user = { id: 'user-123' };
      GameSession.findBySessionId.mockResolvedValue(mockSession);

      const result = await gameHandler.getGameSession(mockEvent, mockContext);

      expect(result.gameSession.sessionId).toBe('session-123');
      expect(result.gameSession.currentQuestion).toBe(2);
      expect(result.gameSession.status).toBe('active');
      expect(result.gameSession.score).toBe(200);

      expect(GameSession.findBySessionId).toHaveBeenCalledWith('session-123');
    });

    it('应该处理会话不存在的情况', async () => {
      mockEvent.user = { id: 'user-123' };
      GameSession.findBySessionId.mockResolvedValue(null);

      await expect(gameHandler.getGameSession(mockEvent, mockContext))
        .rejects
        .toThrow('游戏会话不存在');
    });
  });
});

describe('Game Handler Performance Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock rate limit middleware
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
  });

  it('获取题目列表响应时间应该少于50ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'GET',
      queryStringParameters: { count: '10' }
    };

    Question.getRandomQuestions.mockResolvedValue([
      { id: 1, text: '测试题目' }
    ]);

    await gameHandler.getQuestions(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(50);
  });

  it('创建游戏会话响应时间应该少于100ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'POST',
      body: JSON.stringify({
        userId: 'user-123',
        questionCount: 5
      })
    };

    // Mock validation
    const { validateRequest } = require('../serverless/middleware/validation');
    validateRequest.mockReturnValue({
      userId: 'user-123',
      questionCount: 5
    });

    Question.getRandomQuestions.mockResolvedValue([
      { id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }
    ]);

    GameSession.create.mockResolvedValue({
      sessionId: 'session-123',
      questions: []
    });

    await gameHandler.createGameSession(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(100);
  });
});