/**
 * 错误处理模块单元测试
 * 直接测试 errors.js 中的实际功能
 */

const { APIError, createError, handleError, formatErrorResponse } = require('../serverless/utils/errors');

describe('Errors Module Tests', () => {
  describe('APIError Class', () => {
    it('应该创建基本API错误', () => {
      const error = new APIError('INVALID_PARAMS', '参数错误', { field: 'name' }, 400);
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('APIError');
      expect(error.code).toBe('INVALID_PARAMS');
      expect(error.message).toBe('参数错误');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual({ field: 'name' });
      expect(error.timestamp).toBeDefined();
    });

    it('应该使用默认错误信息', () => {
      const error = new APIError('INTERNAL_ERROR');
      
      expect(error.message).toBe('服务器内部错误');
      expect(error.statusCode).toBe(400);
    });

    it('应该处理未知错误码', () => {
      const error = new APIError('UNKNOWN_ERROR', '自定义消息');
      
      expect(error.message).toBe('自定义消息');
      expect(error.code).toBe('UNKNOWN_ERROR');
    });
  });

  describe('Error Constants', () => {
    it('应该定义所有错误码', () => {
      const errors = require('../serverless/utils/errors');
      
      // 测试一些关键错误码是否存在
      expect(errors.ERROR_CODES).toBeDefined();
      expect(errors.ERROR_CODES.INTERNAL_ERROR).toBeDefined();
      expect(errors.ERROR_CODES.UNAUTHORIZED).toBeDefined();
      expect(errors.ERROR_CODES.USER_NOT_FOUND).toBeDefined();
      expect(errors.ERROR_CODES.RATE_LIMIT_EXCEEDED).toBeDefined();
    });

    it('应该包含正确的错误信息结构', () => {
      const errors = require('../serverless/utils/errors');
      const internalError = errors.ERROR_CODES.INTERNAL_ERROR;
      
      expect(internalError).toHaveProperty('code');
      expect(internalError).toHaveProperty('message');
      expect(typeof internalError.code).toBe('number');
      expect(typeof internalError.message).toBe('string');
    });
  });

  describe('Error Utility Functions', () => {
    it('应该创建格式化的错误响应', () => {
      const error = new APIError('INVALID_PARAMS', '参数验证失败', { field: 'email' }, 400);
      
      // 测试格式化函数（如果存在）
      if (typeof formatErrorResponse === 'function') {
        const response = formatErrorResponse(error);
        
        expect(response).toHaveProperty('error');
        expect(response).toHaveProperty('message');
        expect(response).toHaveProperty('statusCode');
        expect(response.error).toBe('INVALID_PARAMS');
        expect(response.message).toBe('参数验证失败');
        expect(response.statusCode).toBe(400);
      }
    });

    it('应该处理系统错误', () => {
      const systemError = new Error('Database connection failed');
      
      // 测试错误处理函数（如果存在）
      if (typeof handleError === 'function') {
        const apiError = handleError(systemError);
        
        expect(apiError).toBeInstanceOf(APIError);
        expect(apiError.code).toBe('INTERNAL_ERROR');
        expect(apiError.statusCode).toBe(500);
      }
    });

    it('应该创建便捷的错误实例', () => {
      // 测试快捷创建函数（如果存在）
      if (typeof createError === 'function') {
        const error = createError('USER_NOT_FOUND', '用户不存在');
        
        expect(error).toBeInstanceOf(APIError);
        expect(error.code).toBe('USER_NOT_FOUND');
        expect(error.message).toBe('用户不存在');
      }
    });
  });

  describe('HTTP Status Code Mapping', () => {
    it('应该为认证错误返回401', () => {
      const authError = new APIError('UNAUTHORIZED', null, null, 401);
      expect(authError.statusCode).toBe(401);
    });

    it('应该为权限错误返回403', () => {
      const permError = new APIError('INSUFFICIENT_PERMISSIONS', null, null, 403);
      expect(permError.statusCode).toBe(403);
    });

    it('应该为资源不存在返回404', () => {
      const notFoundError = new APIError('RESOURCE_NOT_FOUND', null, null, 404);
      expect(notFoundError.statusCode).toBe(404);
    });

    it('应该为限流错误返回429', () => {
      const rateLimitError = new APIError('RATE_LIMIT_EXCEEDED', null, null, 429);
      expect(rateLimitError.statusCode).toBe(429);
    });

    it('应该为服务器错误返回500', () => {
      const serverError = new APIError('INTERNAL_ERROR', null, null, 500);
      expect(serverError.statusCode).toBe(500);
    });
  });

  describe('Error Details and Context', () => {
    it('应该保存错误详情', () => {
      const details = {
        field: 'email',
        value: 'invalid-email',
        expected: 'valid email format'
      };
      
      const error = new APIError('INVALID_PARAMS', '邮箱格式错误', details);
      
      expect(error.details).toEqual(details);
    });

    it('应该包含时间戳', () => {
      const error = new APIError('INTERNAL_ERROR');
      
      expect(error.timestamp).toBeDefined();
      expect(new Date(error.timestamp)).toBeInstanceOf(Date);
      expect(Date.now() - new Date(error.timestamp).getTime()).toBeLessThan(1000);
    });

    it('应该支持错误链', () => {
      const originalError = new Error('Database timeout');
      const apiError = new APIError('INTERNAL_ERROR', '数据库连接超时', { 
        originalError: originalError.message 
      });
      
      expect(apiError.details.originalError).toBe('Database timeout');
    });
  });

  describe('Error Serialization', () => {
    it('应该能够序列化为JSON', () => {
      const error = new APIError('USER_NOT_FOUND', '用户不存在', { userId: 123 });
      
      const serialized = JSON.stringify(error);
      const parsed = JSON.parse(serialized);
      
      expect(parsed.name).toBe('APIError');
      expect(parsed.code).toBe('USER_NOT_FOUND');
      expect(parsed.message).toBe('用户不存在');
    });

    it('应该保持堆栈信息', () => {
      const error = new APIError('INTERNAL_ERROR');
      
      expect(error.stack).toBeDefined();
      expect(typeof error.stack).toBe('string');
      expect(error.stack).toContain('APIError');
    });
  });

  describe('Business Logic Error Scenarios', () => {
    it('应该处理微信登录错误', () => {
      const error = new APIError('WECHAT_LOGIN_FAILED', '微信授权失败', {
        code: 'invalid_code',
        timestamp: new Date().toISOString()
      }, 401);
      
      expect(error.code).toBe('WECHAT_LOGIN_FAILED');
      expect(error.statusCode).toBe(401);
      expect(error.details.code).toBe('invalid_code');
    });

    it('应该处理游戏会话错误', () => {
      const error = new APIError('GAME_SESSION_EXPIRED', '游戏会话已过期', {
        sessionId: 'session_123',
        expiredAt: new Date().toISOString()
      }, 410);
      
      expect(error.code).toBe('GAME_SESSION_EXPIRED');
      expect(error.statusCode).toBe(410);
      expect(error.details.sessionId).toBe('session_123');
    });

    it('应该处理限流错误', () => {
      const error = new APIError('RATE_LIMIT_EXCEEDED', '请求过于频繁', {
        limit: 100,
        window: 3600,
        retryAfter: 60
      }, 429);
      
      expect(error.code).toBe('RATE_LIMIT_EXCEEDED');
      expect(error.statusCode).toBe(429);
      expect(error.details.retryAfter).toBe(60);
    });
  });

  describe('Integration with Express.js', () => {
    it('应该与Express错误处理兼容', () => {
      const error = new APIError('INVALID_PARAMS', '参数错误');
      
      // 模拟Express错误处理器
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      
      const mockNext = jest.fn();
      
      // 模拟错误处理中间件
      const errorHandler = (err, req, res, next) => {
        if (err instanceof APIError) {
          return res.status(err.statusCode).json({
            error: err.code,
            message: err.message,
            details: err.details,
            timestamp: err.timestamp
          });
        }
        next(err);
      };
      
      errorHandler(error, {}, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'INVALID_PARAMS',
        message: '参数错误',
        details: null,
        timestamp: error.timestamp
      });
    });
  });
});