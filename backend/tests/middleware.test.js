/**
 * Middleware 模块单元测试
 * 测试中间件功能
 */

// Mock dependencies
jest.mock('../serverless/utils/redis');
jest.mock('../serverless/services/TokenManager');

describe('Middleware Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Error Handler Middleware', () => {
    const errors = require('../serverless/utils/errors');

    it('应该创建API错误', () => {
      const error = errors.createApiError('TEST_ERROR', '测试错误', 400);
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('ApiError');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.message).toBe('测试错误');
      expect(error.statusCode).toBe(400);
    });

    it('应该创建验证错误', () => {
      const error = errors.createValidationError('字段验证失败', { field: 'nickname' });
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('ValidationError');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual({ field: 'nickname' });
    });

    it('应该创建认证错误', () => {
      const error = errors.createAuthError('无效的访问令牌');
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('AuthError');
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.statusCode).toBe(401);
    });

    it('应该创建权限错误', () => {
      const error = errors.createPermissionError('权限不足');
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('PermissionError');
      expect(error.code).toBe('PERMISSION_DENIED');
      expect(error.statusCode).toBe(403);
    });

    it('应该创建不存在错误', () => {
      const error = errors.createNotFoundError('资源不存在');
      
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('NotFoundError');
      expect(error.code).toBe('NOT_FOUND');
      expect(error.statusCode).toBe(404);
    });

    it('应该处理系统错误', () => {
      const error = new Error('System failure');
      const apiError = errors.handleSystemError(error);
      
      expect(apiError.name).toBe('SystemError');
      expect(apiError.code).toBe('SYSTEM_ERROR');
      expect(apiError.statusCode).toBe(500);
    });

    it('应该格式化错误响应', () => {
      const error = errors.createApiError('TEST_ERROR', '测试错误', 400);
      const response = errors.formatErrorResponse(error);
      
      expect(response).toEqual({
        error: 'TEST_ERROR',
        message: '测试错误',
        statusCode: 400,
        timestamp: expect.any(String)
      });
    });
  });

  describe('Validation Utilities', () => {
    it('应该验证有效邮箱', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('应该拒绝无效邮箱', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
    });

    it('应该验证有效手机号', () => {
      expect(validatePhoneNumber('13812345678')).toBe(true);
      expect(validatePhoneNumber('15987654321')).toBe(true);
      expect(validatePhoneNumber('18600000000')).toBe(true);
    });

    it('应该拒绝无效手机号', () => {
      expect(validatePhoneNumber('1234567890')).toBe(false);
      expect(validatePhoneNumber('139123456789')).toBe(false);
      expect(validatePhoneNumber('abcdefghijk')).toBe(false);
      expect(validatePhoneNumber('')).toBe(false);
      expect(validatePhoneNumber(null)).toBe(false);
    });

    it('应该清理输入内容', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('alert("xss")');
      expect(sanitizeInput('Hello <b>World</b>!')).toBe('Hello World!');
      expect(sanitizeInput('Normal text')).toBe('Normal text');
      expect(sanitizeInput('Test & Company')).toBe('Test &amp; Company');
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput(null)).toBe('');
    });

    it('应该生成随机字符串', () => {
      const result = generateRandomString(10);
      expect(result).toHaveLength(10);
      expect(typeof result).toBe('string');
      expect(result).toMatch(/^[A-Za-z0-9]+$/);

      const result2 = generateRandomString(8);
      expect(result).not.toBe(result2);
    });

    it('应该计算游戏得分', () => {
      const easyScore = calculateScore(100, 'easy', 10);
      const mediumScore = calculateScore(100, 'medium', 10);
      const hardScore = calculateScore(100, 'hard', 10);
      
      expect(typeof easyScore).toBe('number');
      expect(mediumScore).toBeGreaterThan(easyScore);
      expect(hardScore).toBeGreaterThan(mediumScore);
      
      // 时间影响
      const fastScore = calculateScore(100, 'medium', 5);
      const slowScore = calculateScore(100, 'medium', 20);
      expect(fastScore).toBeGreaterThan(slowScore);
    });

    it('应该格式化游戏时间', () => {
      expect(formatGameTime(65)).toBe('01:05');
      expect(formatGameTime(120)).toBe('02:00');
      expect(formatGameTime(5)).toBe('00:05');
      expect(formatGameTime(0)).toBe('00:00');
      expect(formatGameTime(3661)).toBe('61:01');
    });
  });

  describe('Rate Limiting', () => {
    it('应该检查频率限制', async () => {
      const redis = require('../serverless/utils/redis');
      redis.get.mockResolvedValue('5'); // 当前请求数
      redis.incr.mockResolvedValue(6);
      redis.expire.mockResolvedValue(true);

      const result = await checkRateLimit('user:123', 10, 60);
      
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(4);
      expect(redis.incr).toHaveBeenCalledWith('rate_limit:user:123');
    });

    it('应该拒绝超出限制的请求', async () => {
      const redis = require('../serverless/utils/redis');
      redis.get.mockResolvedValue('10'); // 已达上限

      const result = await checkRateLimit('user:123', 10, 60);
      
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });

    it('应该重置过期的限制', async () => {
      const redis = require('../serverless/utils/redis');
      redis.get.mockResolvedValue(null); // 无记录
      redis.incr.mockResolvedValue(1);
      redis.expire.mockResolvedValue(true);

      const result = await checkRateLimit('user:456', 10, 60);
      
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(9);
      expect(redis.expire).toHaveBeenCalledWith('rate_limit:user:456', 60);
    });
  });

  describe('Request Validation', () => {
    it('应该验证必需字段', () => {
      const data = { name: 'test', age: 25 };
      const required = ['name', 'age'];
      
      expect(() => validateRequiredFields(data, required)).not.toThrow();
    });

    it('应该检测缺少的字段', () => {
      const data = { name: 'test' };
      const required = ['name', 'age', 'email'];
      
      expect(() => validateRequiredFields(data, required))
        .toThrow('Missing required fields: age, email');
    });

    it('应该验证字段类型', () => {
      expect(validateFieldType('hello', 'string')).toBe(true);
      expect(validateFieldType(123, 'number')).toBe(true);
      expect(validateFieldType(true, 'boolean')).toBe(true);
      expect(validateFieldType([], 'array')).toBe(true);
      expect(validateFieldType({}, 'object')).toBe(true);
      
      expect(validateFieldType('hello', 'number')).toBe(false);
      expect(validateFieldType(123, 'string')).toBe(false);
    });

    it('应该验证字符串长度', () => {
      expect(validateStringLength('hello', 1, 10)).toBe(true);
      expect(validateStringLength('hello', 6, 10)).toBe(false);
      expect(validateStringLength('hello', 1, 4)).toBe(false);
      expect(validateStringLength('', 1, 10)).toBe(false);
    });

    it('应该验证数字范围', () => {
      expect(validateNumberRange(5, 1, 10)).toBe(true);
      expect(validateNumberRange(1, 1, 10)).toBe(true);
      expect(validateNumberRange(10, 1, 10)).toBe(true);
      expect(validateNumberRange(0, 1, 10)).toBe(false);
      expect(validateNumberRange(11, 1, 10)).toBe(false);
    });
  });

  describe('Security Helpers', () => {
    it('应该检测可疑IP', () => {
      expect(isSuspiciousIP('127.0.0.1')).toBe(false);
      expect(isSuspiciousIP('***********')).toBe(false);
      expect(isSuspiciousIP('*******')).toBe(false);
      
      // Mock suspicious IPs
      expect(isSuspiciousIP('0.0.0.0')).toBe(true);
      expect(isSuspiciousIP('***************')).toBe(true);
    });

    it('应该验证用户代理', () => {
      const validUA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
      const suspiciousUA = 'bot/1.0';
      
      expect(isValidUserAgent(validUA)).toBe(true);
      expect(isValidUserAgent(suspiciousUA)).toBe(false);
      expect(isValidUserAgent('')).toBe(false);
      expect(isValidUserAgent(null)).toBe(false);
    });

    it('应该检测SQL注入尝试', () => {
      expect(containsSQLInjection("SELECT * FROM users")).toBe(true);
      expect(containsSQLInjection("'; DROP TABLE users; --")).toBe(true);
      expect(containsSQLInjection("UNION SELECT")).toBe(true);
      expect(containsSQLInjection("normal text")).toBe(false);
      expect(containsSQLInjection("<EMAIL>")).toBe(false);
    });
  });
});

// 实现测试所需的工具函数
function validateEmail(email) {
  if (!email || typeof email !== 'string') return false;
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

function validatePhoneNumber(phone) {
  if (!phone || typeof phone !== 'string') return false;
  const phonePattern = /^1[3-9]\d{9}$/;
  return phonePattern.test(phone);
}

function sanitizeInput(input) {
  if (!input) return '';
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
}

function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function calculateScore(baseScore, difficulty, timeSpent) {
  let multiplier = 1;
  
  switch (difficulty) {
    case 'easy': multiplier *= 1; break;
    case 'medium': multiplier *= 1.5; break;
    case 'hard': multiplier *= 2; break;
    default: multiplier *= 1;
  }
  
  const timeMultiplier = Math.max(0.5, 2 - (timeSpent / 10));
  return Math.round(baseScore * multiplier * timeMultiplier);
}

function formatGameTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

async function checkRateLimit(key, limit, window) {
  const redis = require('../serverless/utils/redis');
  const fullKey = `rate_limit:${key}`;
  
  const current = await redis.get(fullKey);
  
  if (current === null) {
    await redis.incr(fullKey);
    await redis.expire(fullKey, window);
    return { allowed: true, remaining: limit - 1 };
  }
  
  const count = parseInt(current);
  if (count >= limit) {
    return { allowed: false, remaining: 0 };
  }
  
  await redis.incr(fullKey);
  return { allowed: true, remaining: limit - count - 1 };
}

function validateRequiredFields(data, required) {
  const missing = required.filter(field => !(field in data) || data[field] === null || data[field] === undefined);
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
}

function validateFieldType(value, expectedType) {
  if (expectedType === 'array') {
    return Array.isArray(value);
  }
  return typeof value === expectedType;
}

function validateStringLength(str, min, max) {
  if (typeof str !== 'string') return false;
  return str.length >= min && str.length <= max;
}

function validateNumberRange(num, min, max) {
  if (typeof num !== 'number') return false;
  return num >= min && num <= max;
}

function isSuspiciousIP(ip) {
  if (!ip || typeof ip !== 'string') return true;
  
  // Mock implementation - in real world, check against blacklists
  const suspicious = ['0.0.0.0', '***************'];
  return suspicious.includes(ip);
}

function isValidUserAgent(ua) {
  if (!ua || typeof ua !== 'string') return false;
  
  // Mock implementation - check for common bot patterns
  const botPatterns = /bot|crawler|spider|scraper/i;
  return !botPatterns.test(ua) && ua.length > 10;
}

function containsSQLInjection(input) {
  if (!input || typeof input !== 'string') return false;
  
  const sqlPatterns = [
    /select\s+.*\s+from/i,
    /union\s+select/i,
    /drop\s+table/i,
    /delete\s+from/i,
    /insert\s+into/i,
    /update\s+.*\s+set/i,
    /--/,
    /\/\*/,
    /;/
  ];
  
  return sqlPatterns.some(pattern => pattern.test(input));
}