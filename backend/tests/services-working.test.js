/**
 * Services 模块单元测试 - 工作版本
 * 重新设计的简化测试，确保可以正常运行并提高覆盖率
 */

// Mock all external dependencies at the top
jest.mock('cos-nodejs-sdk-v5');
jest.mock('jsonwebtoken');
jest.mock('crypto');
jest.mock('axios');

// Mock config module
jest.mock('../config', () => ({
  cos: {
    secretId: 'test-secret-id',
    secretKey: 'test-secret-key',
    bucket: 'test-bucket',
    region: 'test-region',
    cdnDomain: 'test.cdn.com'
  },
  jwt: {
    accessSecret: 'test-access-secret',
    refreshSecret: 'test-refresh-secret',
    accessTokenTTL: 3600,
    refreshTokenTTL: 86400,
    issuer: 'test-issuer',
    audience: 'test-audience'
  },
  wechat: {
    appId: 'test-app-id',
    appSecret: 'test-app-secret',
    apiUrl: 'https://api.weixin.qq.com'
  }
}));

// Mock redis
jest.mock('../serverless/utils/redis', () => ({
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  incr: jest.fn(),
  expire: jest.fn(),
  scan: jest.fn(),
  ttl: jest.fn()
}));

// Mock User model
jest.mock('../serverless/models/User', () => ({
  findByOpenId: jest.fn(),
  findById: jest.fn(),
  findByUnionId: jest.fn(),
  create: jest.fn()
}));

describe('Services Module Tests', () => {
  let cosService, tokenManager, wechatAuthService;
  let mockCOS, mockJWT, mockCrypto, mockAxios, mockRedis;

  beforeAll(() => {
    // Import services after mocks are set up
    const { getCosService } = require('../serverless/services/CosService');
    tokenManager = require('../serverless/services/TokenManager');
    wechatAuthService = require('../serverless/services/WechatAuthService');
    
    cosService = getCosService();
    
    // Get mock instances
    mockCOS = require('cos-nodejs-sdk-v5');
    mockJWT = require('jsonwebtoken');
    mockCrypto = require('crypto');
    mockAxios = require('axios');
    mockRedis = require('../serverless/utils/redis');
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CosService', () => {
    beforeEach(() => {
      // Set up COS mock implementation
      const mockCOSInstance = {
        headObject: jest.fn(),
        getObjectUrl: jest.fn(),
        getBucket: jest.fn(),
        putObject: jest.fn(),
        deleteObject: jest.fn()
      };
      mockCOS.mockImplementation(() => mockCOSInstance);
    });

    describe('buildAudioPath', () => {
      it('应该正确构建音频文件路径', () => {
        const result = cosService.buildAudioPath('sichuan_hello_world');
        expect(result).toBe('audio/sichuan/hello_world.mp3');
      });

      it('应该处理无效的资源ID格式', () => {
        expect(() => cosService.buildAudioPath('invalid')).toThrow('Invalid resource ID format');
      });
    });

    describe('generateCdnUrl', () => {
      it('应该生成CDN URL', () => {
        const result = cosService.generateCdnUrl('audio/test.mp3');
        expect(result).toBe('https://test.cdn.com/audio/test.mp3');
      });

      it('应该处理没有CDN域名的情况', () => {
        // Temporarily change CDN domain to null
        const originalCdnDomain = cosService.cdnDomain;
        cosService.cdnDomain = null;
        
        const result = cosService.generateCdnUrl('audio/test.mp3');
        expect(result).toBeNull();
        
        // Restore CDN domain
        cosService.cdnDomain = originalCdnDomain;
      });
    });

    describe('extractResourceId', () => {
      it('应该从文件路径提取资源ID', () => {
        const result = cosService.extractResourceId('audio/sichuan/001.mp3');
        expect(result).toBe('sichuan_001');
      });

      it('应该处理无效路径', () => {
        const result = cosService.extractResourceId('invalid/path');
        expect(result).toBeNull();
      });
    });
  });

  describe('TokenManager', () => {
    beforeEach(() => {
      // Mock JWT functions
      mockJWT.sign.mockReturnValue('mock-token');
      mockJWT.verify.mockReturnValue({ sub: '123', jti: 'test-jti' });
      mockJWT.decode.mockReturnValue({ sub: '123', exp: Date.now() / 1000 + 3600 });
      
      // Mock crypto functions
      mockCrypto.randomBytes.mockReturnValue(Buffer.from('test-random', 'utf8'));
      
      // Mock redis
      mockRedis.get.mockResolvedValue(null);
      mockRedis.set.mockResolvedValue('OK');
    });

    describe('generateAccessToken', () => {
      it('应该生成访问令牌', () => {
        const user = { id: 123, status: 1, total_score: 5000 };
        const result = tokenManager.generateAccessToken(user, 'session-123', 'device-123');
        
        expect(result).toBe('mock-token');
        expect(mockJWT.sign).toHaveBeenCalledWith(
          expect.objectContaining({
            sub: '123',
            sessionId: 'session-123',
            deviceId: 'device-123'
          }),
          'test-access-secret',
          { algorithm: 'HS256' }
        );
      });
    });

    describe('generateRefreshToken', () => {
      it('应该生成刷新令牌', () => {
        const result = tokenManager.generateRefreshToken(123);
        
        expect(result).toBe('mock-token');
        expect(mockJWT.sign).toHaveBeenCalledWith(
          expect.objectContaining({
            sub: '123',
            type: 'refresh'
          }),
          'test-refresh-secret',
          { algorithm: 'HS256' }
        );
      });
    });

    describe('verifyAccessToken', () => {
      it('应该验证有效的访问令牌', async () => {
        const result = await tokenManager.verifyAccessToken('valid-token');
        
        expect(result).toEqual({ sub: '123', jti: 'test-jti' });
        expect(mockJWT.verify).toHaveBeenCalledWith(
          'valid-token',
          'test-access-secret',
          expect.objectContaining({
            audience: 'test-audience',
            issuer: 'test-issuer',
            algorithms: ['HS256']
          })
        );
      });

      it('应该处理过期令牌', async () => {
        const error = new Error('jwt expired');
        error.name = 'TokenExpiredError';
        mockJWT.verify.mockImplementation(() => { throw error; });

        await expect(tokenManager.verifyAccessToken('expired-token'))
          .rejects
          .toThrow('Access token has expired');
      });
    });

    describe('getUserScopes', () => {
      it('应该返回基础权限', () => {
        const user = { id: 123, status: 1, total_score: 5000 };
        const result = tokenManager.getUserScopes(user);
        
        expect(result).toContain('user:read');
        expect(result).toContain('game:play');
        expect(result).toContain('user:write');
      });

      it('应该为高分用户添加高级权限', () => {
        const user = { id: 123, status: 1, total_score: 15000 };
        const result = tokenManager.getUserScopes(user);
        
        expect(result).toContain('premium:features');
      });
    });

    describe('decodeToken', () => {
      it('应该解码令牌', () => {
        const result = tokenManager.decodeToken('some-token');
        
        expect(result).toEqual({ sub: '123', exp: expect.any(Number) });
        expect(mockJWT.decode).toHaveBeenCalledWith('some-token');
      });

      it('应该处理无效令牌', () => {
        mockJWT.decode.mockReturnValue(null);
        
        const result = tokenManager.decodeToken('invalid-token');
        expect(result).toBeNull();
      });
    });
  });

  describe('WechatAuthService', () => {
    beforeEach(() => {
      // Mock axios
      mockAxios.get.mockResolvedValue({
        data: { access_token: 'mock-access-token', expires_in: 7200 }
      });
      
      // Mock crypto hash
      const mockHash = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('mock-signature')
      };
      mockCrypto.createHash.mockReturnValue(mockHash);
    });

    describe('verifySignature', () => {
      it('应该验证有效签名', () => {
        const result = wechatAuthService.verifySignature(
          'mock-signature',
          '1640995200',
          'test-nonce',
          'test-token'
        );
        
        expect(result).toBe(true);
        expect(mockCrypto.createHash).toHaveBeenCalledWith('sha1');
      });

      it('应该拒绝无效签名', () => {
        const result = wechatAuthService.verifySignature(
          'invalid-signature',
          '1640995200',
          'test-nonce',
          'test-token'
        );
        
        expect(result).toBe(false);
      });
    });

    describe('getAccessToken', () => {
      it('应该获取访问令牌', async () => {
        const result = await wechatAuthService.getAccessToken();
        
        expect(result).toBe('mock-access-token');
        expect(mockAxios.get).toHaveBeenCalledWith(
          expect.stringContaining('token'),
          expect.objectContaining({
            params: {
              grant_type: 'client_credential',
              appid: 'test-app-id',
              secret: 'test-app-secret'
            }
          })
        );
      });

      it('应该处理API错误', async () => {
        mockAxios.get.mockResolvedValue({
          data: { errcode: 40001, errmsg: 'invalid credential' }
        });

        await expect(wechatAuthService.getAccessToken())
          .rejects
          .toThrow('获取Access Token失败: invalid credential');
      });
    });

    describe('getSessionKey', () => {
      it('应该获取会话密钥', async () => {
        const mockSessionData = {
          openid: 'test-openid',
          session_key: 'test-session-key',
          unionid: 'test-unionid'
        };
        
        mockAxios.get.mockResolvedValue({ data: mockSessionData });

        const result = await wechatAuthService.getSessionKey('test-code');

        expect(result).toEqual(mockSessionData);
        expect(mockAxios.get).toHaveBeenCalledWith(
          expect.stringContaining('jscode2session'),
          expect.objectContaining({
            params: {
              appid: 'test-app-id',
              secret: 'test-app-secret',
              js_code: 'test-code',
              grant_type: 'authorization_code'
            }
          })
        );
      });

      it('应该处理无效code', async () => {
        mockAxios.get.mockResolvedValue({
          data: { errcode: 40029, errmsg: 'invalid code' }
        });

        await expect(wechatAuthService.getSessionKey('invalid-code'))
          .rejects
          .toThrow('微信API错误 (40029): 无效的code');
      });
    });

    describe('decryptUserInfo', () => {
      it('应该解密用户信息', () => {
        const mockDecipher = {
          update: jest.fn().mockReturnValue('{"nickName":"测试用户","watermark":{"appid":"test-app-id"}}'),
          final: jest.fn().mockReturnValue(''),
          setAutoPadding: jest.fn()
        };
        
        mockCrypto.createDecipheriv.mockReturnValue(mockDecipher);

        const result = wechatAuthService.decryptUserInfo(
          'test-session-key',
          'encrypted-data',
          'iv-data'
        );

        expect(result.nickName).toBe('测试用户');
        expect(mockCrypto.createDecipheriv).toHaveBeenCalledWith(
          'aes-128-cbc',
          expect.any(Buffer),
          expect.any(Buffer)
        );
      });

      it('应该处理解密失败', () => {
        mockCrypto.createDecipheriv.mockImplementation(() => {
          throw new Error('Decryption failed');
        });

        expect(() => wechatAuthService.decryptUserInfo(
          'invalid-session-key',
          'encrypted-data',
          'iv-data'
        )).toThrow('用户信息解密失败');
      });
    });
  });

  describe('Integration Tests', () => {
    it('应该创建完整的令牌对', async () => {
      const user = { id: 123, status: 1, total_score: 5000 };
      
      mockJWT.sign.mockReturnValueOnce('access-token')
                  .mockReturnValueOnce('refresh-token');
      mockJWT.decode.mockReturnValue({ jti: 'test-jti' });
      mockRedis.set.mockResolvedValue('OK');

      const result = await tokenManager.createTokenPair(user, 'device-123');

      expect(result).toEqual({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 3600,
        tokenType: 'Bearer',
        scope: expect.any(String)
      });
    });

    it('应该验证权限范围', () => {
      const userScopes = 'user:read game:play user:write';
      const requiredScopes = ['user:read'];
      
      const result = tokenManager.hasRequiredScopes(userScopes, requiredScopes);
      expect(result).toBe(true);
    });
  });
});