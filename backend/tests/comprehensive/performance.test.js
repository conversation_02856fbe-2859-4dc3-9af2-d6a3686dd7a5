/**
 * 性能测试套件
 * 测试系统在高负载下的性能表现
 */

const { performance } = require('perf_hooks');
const { DatabaseManager } = require('../../serverless/utils/database');
const { RedisManager } = require('../../serverless/utils/redis');
const { QueryCache } = require('../../serverless/utils/queryCache');
const { SmartCache } = require('../../serverless/utils/smartCache');
const { GameService } = require('../../serverless/game/gameService');
const { UserService } = require('../../serverless/user/userService');

describe('Performance Tests', () => {
  let db, redis, queryCache, smartCache, gameService, userService;
  let testUsers = [];
  let testSessions = [];

  beforeAll(async () => {
    db = DatabaseManager.getInstance();
    redis = RedisManager.getInstance();
    queryCache = QueryCache.getInstance();
    smartCache = SmartCache.getInstance();
    gameService = new GameService();
    userService = new UserService();

    // 创建测试用户
    for (let i = 0; i < 100; i++) {
      const user = global.testUtils.generateTestUser({
        id: `perf_user_${i}`,
        nickname: `PerfUser${i}`
      });
      testUsers.push(user);
    }

    // 批量插入测试用户
    const connection = await db.getConnection();
    try {
      for (const user of testUsers) {
        await connection.execute(
          'INSERT INTO users (id, nickname, avatar_url, region, level, total_score, games_played, accuracy_rate, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [user.id, user.nickname, user.avatar_url, user.region, user.level, user.total_score, user.games_played, user.accuracy_rate, user.status, user.created_at]
        );
      }
    } finally {
      connection.release();
    }
  });

  afterAll(async () => {
    // 清理测试数据
    const connection = await db.getConnection();
    try {
      await connection.execute('DELETE FROM game_answers WHERE user_id LIKE "perf_user_%"');
      await connection.execute('DELETE FROM game_sessions WHERE user_id LIKE "perf_user_%"');
      await connection.execute('DELETE FROM users WHERE id LIKE "perf_user_%"');
    } finally {
      connection.release();
    }
  });

  describe('Database Performance', () => {
    test('should handle concurrent database connections', async () => {
      const startTime = performance.now();
      const concurrentQueries = 50;
      
      const promises = Array.from({ length: concurrentQueries }, async (_, index) => {
        const connection = await db.getConnection();
        try {
          const [result] = await connection.execute(
            'SELECT * FROM users WHERE id = ?',
            [`perf_user_${index % testUsers.length}`]
          );
          return result;
        } finally {
          connection.release();
        }
      });

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(concurrentQueries);
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
      
      console.log(`Concurrent DB queries (${concurrentQueries}): ${duration.toFixed(2)}ms`);
    });

    test('should handle bulk insert operations efficiently', async () => {
      const startTime = performance.now();
      const bulkSize = 1000;
      
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();
        
        for (let i = 0; i < bulkSize; i++) {
          const session = global.testUtils.generateTestGameSession(
            testUsers[i % testUsers.length].id,
            { id: `perf_session_${i}` }
          );
          
          await connection.execute(
            'INSERT INTO game_sessions (id, user_id, dialect_region, difficulty_level, status, current_question, total_questions, final_score, total_time, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [session.id, session.user_id, session.dialect_region, session.difficulty_level, session.status, session.current_question, session.total_questions, session.final_score, session.total_time, session.created_at]
          );
          
          testSessions.push(session);
        }
        
        await connection.commit();
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }

      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(10000); // 应该在10秒内完成
      
      console.log(`Bulk insert (${bulkSize} records): ${duration.toFixed(2)}ms`);
    });

    test('should optimize complex queries with joins', async () => {
      const startTime = performance.now();
      
      const connection = await db.getConnection();
      try {
        const [result] = await connection.execute(`
          SELECT 
            u.id,
            u.nickname,
            COUNT(gs.id) as total_games,
            AVG(gs.final_score) as avg_score,
            MAX(gs.final_score) as best_score
          FROM users u
          LEFT JOIN game_sessions gs ON u.id = gs.user_id
          WHERE u.id LIKE 'perf_user_%'
          GROUP BY u.id, u.nickname
          ORDER BY avg_score DESC
          LIMIT 20
        `);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        expect(result).toBeDefined();
        expect(duration).toBeLessThan(2000); // 应该在2秒内完成
        
        console.log(`Complex query with joins: ${duration.toFixed(2)}ms`);
      } finally {
        connection.release();
      }
    });
  });

  describe('Cache Performance', () => {
    test('should demonstrate cache hit performance improvement', async () => {
      const cacheKey = 'perf_test_cache_key';
      const testData = { message: 'Performance test data', timestamp: Date.now() };
      
      // 第一次访问（缓存未命中）
      const startTime1 = performance.now();
      let cachedData = await queryCache.executeWithCache(
        cacheKey,
        async () => {
          await global.testUtils.sleep(100); // 模拟数据库查询延迟
          return testData;
        }
      );
      const duration1 = performance.now() - startTime1;
      
      expect(cachedData.data).toEqual(testData);
      expect(cachedData.meta.fromCache).toBe(false);
      
      // 第二次访问（缓存命中）
      const startTime2 = performance.now();
      cachedData = await queryCache.executeWithCache(
        cacheKey,
        async () => {
          await global.testUtils.sleep(100);
          return testData;
        }
      );
      const duration2 = performance.now() - startTime2;
      
      expect(cachedData.data).toEqual(testData);
      expect(cachedData.meta.fromCache).toBe(true);
      expect(duration2).toBeLessThan(duration1 * 0.1); // 缓存命中应该快10倍以上
      
      console.log(`Cache miss: ${duration1.toFixed(2)}ms, Cache hit: ${duration2.toFixed(2)}ms`);
    });

    test('should handle high-frequency cache operations', async () => {
      const operations = 1000;
      const startTime = performance.now();
      
      const promises = Array.from({ length: operations }, async (_, index) => {
        const key = `perf_cache_${index % 10}`; // 重复使用10个键
        const data = { index, timestamp: Date.now() };
        
        if (index % 2 === 0) {
          // 写操作
          return await smartCache.smartSet(key, data, 'hotData');
        } else {
          // 读操作
          return await smartCache.smartGet(key, 'hotData');
        }
      });
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(results).toHaveLength(operations);
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
      
      console.log(`High-frequency cache ops (${operations}): ${duration.toFixed(2)}ms`);
    });

    test('should efficiently handle cache warmup', async () => {
      const startTime = performance.now();
      
      const warmupResult = await smartCache.warmupCache();
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(warmupResult).toBeDefined();
      expect(warmupResult.successful).toBeGreaterThanOrEqual(0);
      expect(duration).toBeLessThan(10000); // 应该在10秒内完成
      
      console.log(`Cache warmup: ${duration.toFixed(2)}ms, Success: ${warmupResult.successful}, Failed: ${warmupResult.failed}`);
    });
  });

  describe('Service Performance', () => {
    test('should handle concurrent game session creation', async () => {
      const concurrentSessions = 50;
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentSessions }, async (_, index) => {
        const userId = testUsers[index % testUsers.length].id;
        return await gameService.createGameSession({
          userId,
          dialectRegion: 'test_region',
          difficultyLevel: 'beginner',
          totalQuestions: 10
        });
      });
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(results).toHaveLength(concurrentSessions);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.id).toBeDefined();
      });
      expect(duration).toBeLessThan(8000); // 应该在8秒内完成
      
      console.log(`Concurrent session creation (${concurrentSessions}): ${duration.toFixed(2)}ms`);
    });

    test('should efficiently process user statistics', async () => {
      const startTime = performance.now();
      
      const promises = testUsers.slice(0, 20).map(async (user) => {
        return await userService.getUserStatistics(user.id);
      });
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(results).toHaveLength(20);
      results.forEach(stats => {
        expect(stats).toBeDefined();
        expect(stats.userId).toBeDefined();
      });
      expect(duration).toBeLessThan(3000); // 应该在3秒内完成
      
      console.log(`User statistics processing (20 users): ${duration.toFixed(2)}ms`);
    });

    test('should handle paginated queries efficiently', async () => {
      const pageSize = 20;
      const totalPages = 5;
      const startTime = performance.now();
      
      const promises = Array.from({ length: totalPages }, async (_, page) => {
        return await queryCache.executePaginatedQuery(
          'SELECT * FROM users WHERE id LIKE ?',
          ['perf_user_%'],
          {
            page: page + 1,
            limit: pageSize,
            sortBy: 'created_at',
            sortOrder: 'DESC',
            cacheKey: `perf_users_page_${page + 1}`
          }
        );
      });
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(results).toHaveLength(totalPages);
      results.forEach(result => {
        expect(result.data).toBeDefined();
        expect(result.data.data).toBeInstanceOf(Array);
        expect(result.data.pagination).toBeDefined();
      });
      expect(duration).toBeLessThan(4000); // 应该在4秒内完成
      
      console.log(`Paginated queries (${totalPages} pages): ${duration.toFixed(2)}ms`);
    });
  });

  describe('Memory Performance', () => {
    test('should not have memory leaks in repeated operations', async () => {
      const initialMemory = process.memoryUsage();
      const iterations = 100;
      
      for (let i = 0; i < iterations; i++) {
        // 执行一些内存密集型操作
        const data = Array.from({ length: 1000 }, (_, index) => ({
          id: `temp_${i}_${index}`,
          data: 'x'.repeat(100)
        }));
        
        // 模拟处理数据
        const processed = data.map(item => ({
          ...item,
          processed: true,
          timestamp: Date.now()
        }));
        
        // 清理引用
        data.length = 0;
        processed.length = 0;
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // 内存增长应该在合理范围内（小于50MB）
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      
      console.log(`Memory increase after ${iterations} iterations: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });

    test('should efficiently handle large data sets', async () => {
      const largeDataSize = 10000;
      const startTime = performance.now();
      const initialMemory = process.memoryUsage();
      
      // 创建大数据集
      const largeData = Array.from({ length: largeDataSize }, (_, index) => ({
        id: index,
        name: `Item ${index}`,
        description: 'x'.repeat(100),
        metadata: {
          created: new Date(),
          tags: [`tag${index % 10}`, `category${index % 5}`],
          score: Math.random() * 100
        }
      }));
      
      // 处理数据
      const processed = largeData
        .filter(item => item.metadata.score > 50)
        .map(item => ({
          id: item.id,
          name: item.name,
          score: item.metadata.score
        }))
        .sort((a, b) => b.score - a.score)
        .slice(0, 100);
      
      const endTime = performance.now();
      const finalMemory = process.memoryUsage();
      const duration = endTime - startTime;
      const memoryUsed = finalMemory.heapUsed - initialMemory.heapUsed;
      
      expect(processed).toBeInstanceOf(Array);
      expect(processed.length).toBeLessThanOrEqual(100);
      expect(duration).toBeLessThan(2000); // 应该在2秒内完成
      expect(memoryUsed).toBeLessThan(100 * 1024 * 1024); // 内存使用应该小于100MB
      
      console.log(`Large dataset processing (${largeDataSize} items): ${duration.toFixed(2)}ms, Memory: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`);
    });
  });

  describe('Load Testing', () => {
    test('should handle sustained load', async () => {
      const duration = 10000; // 10秒
      const requestsPerSecond = 10;
      const totalRequests = (duration / 1000) * requestsPerSecond;
      
      const startTime = performance.now();
      let completedRequests = 0;
      let errors = 0;
      
      const interval = setInterval(async () => {
        try {
          const promises = Array.from({ length: requestsPerSecond }, async () => {
            const userId = testUsers[Math.floor(Math.random() * testUsers.length)].id;
            return await userService.getUserProfile(userId);
          });
          
          await Promise.all(promises);
          completedRequests += requestsPerSecond;
        } catch (error) {
          errors++;
        }
      }, 1000);
      
      // 等待测试完成
      await global.testUtils.sleep(duration);
      clearInterval(interval);
      
      const endTime = performance.now();
      const actualDuration = endTime - startTime;
      const successRate = (completedRequests / totalRequests) * 100;
      
      expect(successRate).toBeGreaterThan(95); // 成功率应该大于95%
      expect(errors).toBeLessThan(totalRequests * 0.05); // 错误率应该小于5%
      
      console.log(`Load test: ${completedRequests}/${totalRequests} requests completed, Success rate: ${successRate.toFixed(2)}%, Errors: ${errors}`);
    });
  });
});
