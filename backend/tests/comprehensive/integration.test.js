/**
 * 集成测试套件
 * 测试各个模块之间的集成和端到端功能
 */

const request = require('supertest');
const { DatabaseManager } = require('../../serverless/utils/database');
const { RedisManager } = require('../../serverless/utils/redis');
const { WebSocketManager } = require('../../serverless/websocket/websocketManager');
const { GameService } = require('../../serverless/game/gameService');
const { UserService } = require('../../serverless/user/userService');
const { UGCService } = require('../../serverless/ugc/ugcService');
const { I18nService } = require('../../serverless/i18n/i18nService');

describe('Integration Tests', () => {
  let db, redis, gameService, userService, ugcService, i18nService;
  let testUser, testSession, testQuestion;
  let authToken;

  beforeAll(async () => {
    db = DatabaseManager.getInstance();
    redis = RedisManager.getInstance();
    gameService = new GameService();
    userService = new UserService();
    ugcService = new UGCService();
    i18nService = new I18nService();

    // 创建测试用户
    testUser = global.testUtils.generateTestUser({
      id: 'integration_test_user',
      nickname: 'IntegrationTestUser'
    });

    const connection = await db.getConnection();
    try {
      await connection.execute(
        'INSERT INTO users (id, nickname, avatar_url, region, level, total_score, games_played, accuracy_rate, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [testUser.id, testUser.nickname, testUser.avatar_url, testUser.region, testUser.level, testUser.total_score, testUser.games_played, testUser.accuracy_rate, testUser.status, testUser.created_at]
      );

      // 创建测试问题
      testQuestion = global.testUtils.generateTestQuestion({
        id: 'integration_test_question',
        dialect_region: 'test_region'
      });

      await connection.execute(
        'INSERT INTO game_questions (id, dialect_region, difficulty_level, question_type, question_text, audio_url, correct_answer, options, explanation, is_active, usage_count, accuracy_rate, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [testQuestion.id, testQuestion.dialect_region, testQuestion.difficulty_level, testQuestion.question_type, testQuestion.question_text, testQuestion.audio_url, testQuestion.correct_answer, testQuestion.options, testQuestion.explanation, testQuestion.is_active, testQuestion.usage_count, testQuestion.accuracy_rate, testQuestion.created_at]
      );
    } finally {
      connection.release();
    }

    // 生成测试JWT令牌
    const jwt = require('jsonwebtoken');
    authToken = jwt.sign(
      { userId: testUser.id, role: 'user' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // 清理测试数据
    const connection = await db.getConnection();
    try {
      await connection.execute('DELETE FROM game_answers WHERE user_id = ?', [testUser.id]);
      await connection.execute('DELETE FROM game_sessions WHERE user_id = ?', [testUser.id]);
      await connection.execute('DELETE FROM ugc_content WHERE creator_id = ?', [testUser.id]);
      await connection.execute('DELETE FROM users WHERE id = ?', [testUser.id]);
      await connection.execute('DELETE FROM game_questions WHERE id = ?', [testQuestion.id]);
    } finally {
      connection.release();
    }
  });

  describe('User Authentication Flow', () => {
    test('should complete full authentication flow', async () => {
      // 1. 用户登录
      const loginResult = await userService.authenticateUser({
        openid: 'test_openid',
        nickname: testUser.nickname,
        avatar: testUser.avatar_url
      });

      expect(loginResult).toBeDefined();
      expect(loginResult.user).toBeDefined();
      expect(loginResult.token).toBeDefined();

      // 2. 验证令牌
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(loginResult.token, process.env.JWT_SECRET);
      expect(decoded.userId).toBe(loginResult.user.id);

      // 3. 获取用户信息
      const userProfile = await userService.getUserProfile(loginResult.user.id);
      expect(userProfile).toBeDefined();
      expect(userProfile.id).toBe(loginResult.user.id);
    });

    test('should handle invalid authentication', async () => {
      await expect(userService.authenticateUser({
        openid: '', // 无效的openid
        nickname: 'Test',
        avatar: 'test.jpg'
      })).rejects.toThrow();
    });
  });

  describe('Game Flow Integration', () => {
    test('should complete full game session flow', async () => {
      // 1. 创建游戏会话
      const session = await gameService.createGameSession({
        userId: testUser.id,
        dialectRegion: 'test_region',
        difficultyLevel: 'beginner',
        totalQuestions: 5
      });

      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.userId).toBe(testUser.id);
      testSession = session;

      // 2. 获取第一个问题
      const question = await gameService.getNextQuestion(session.id);
      expect(question).toBeDefined();
      expect(question.id).toBeDefined();

      // 3. 提交答案
      const answerResult = await gameService.submitAnswer({
        sessionId: session.id,
        questionId: question.id,
        userAnswer: 'A',
        timeSpent: 5000
      });

      expect(answerResult).toBeDefined();
      expect(answerResult.isCorrect).toBeDefined();
      expect(answerResult.score).toBeDefined();

      // 4. 获取会话状态
      const sessionStatus = await gameService.getSessionStatus(session.id);
      expect(sessionStatus).toBeDefined();
      expect(sessionStatus.currentQuestion).toBeGreaterThan(1);

      // 5. 完成游戏
      const completionResult = await gameService.completeGameSession(session.id);
      expect(completionResult).toBeDefined();
      expect(completionResult.finalScore).toBeDefined();
    });

    test('should handle concurrent game sessions', async () => {
      const concurrentSessions = 5;
      const promises = Array.from({ length: concurrentSessions }, async (_, index) => {
        return await gameService.createGameSession({
          userId: testUser.id,
          dialectRegion: 'test_region',
          difficultyLevel: 'beginner',
          totalQuestions: 3,
          sessionId: `concurrent_session_${index}`
        });
      });

      const sessions = await Promise.all(promises);
      expect(sessions).toHaveLength(concurrentSessions);
      sessions.forEach(session => {
        expect(session).toBeDefined();
        expect(session.id).toBeDefined();
      });
    });
  });

  describe('UGC Content Integration', () => {
    test('should complete UGC content lifecycle', async () => {
      // 1. 创建UGC内容
      const content = await ugcService.createContent({
        title: 'Integration Test Content',
        description: 'Test description',
        contentType: 'audio',
        dialectRegion: 'test_region',
        difficultyLevel: 'beginner',
        tags: ['test', 'integration'],
        primaryFileUrl: 'https://example.com/test.mp3',
        creatorId: testUser.id
      });

      expect(content).toBeDefined();
      expect(content.id).toBeDefined();
      expect(content.creatorId).toBe(testUser.id);

      // 2. 获取内容详情
      const contentDetails = await ugcService.getContentById(content.id);
      expect(contentDetails).toBeDefined();
      expect(contentDetails.title).toBe('Integration Test Content');

      // 3. 评价内容
      const ratingResult = await ugcService.rateContent({
        contentId: content.id,
        userId: testUser.id,
        ratingType: 'like',
        ratingValue: 5
      });

      expect(ratingResult).toBeDefined();

      // 4. 获取内容统计
      const stats = await ugcService.getContentStats(content.id);
      expect(stats).toBeDefined();
      expect(stats.likeCount).toBeGreaterThan(0);

      // 5. 删除内容
      const deleteResult = await ugcService.deleteContent(content.id, testUser.id);
      expect(deleteResult).toBe(true);
    });

    test('should handle content moderation flow', async () => {
      // 1. 创建需要审核的内容
      const content = await ugcService.createContent({
        title: 'Moderation Test Content',
        description: 'Content for moderation testing',
        contentType: 'text',
        dialectRegion: 'test_region',
        difficultyLevel: 'beginner',
        tags: ['moderation', 'test'],
        creatorId: testUser.id
      });

      expect(content.moderationStatus).toBe('pending');

      // 2. 模拟AI审核
      const moderationResult = await ugcService.moderateContent(content.id);
      expect(moderationResult).toBeDefined();
      expect(moderationResult.suggestion).toBeDefined();

      // 3. 更新审核状态
      const updateResult = await ugcService.updateModerationStatus({
        contentId: content.id,
        status: 'approved',
        moderatorId: 'system',
        reason: 'Automated approval'
      });

      expect(updateResult).toBe(true);

      // 4. 验证状态更新
      const updatedContent = await ugcService.getContentById(content.id);
      expect(updatedContent.moderationStatus).toBe('approved');
    });
  });

  describe('Real-time Communication Integration', () => {
    test('should handle WebSocket connection and messaging', async () => {
      const wsManager = new WebSocketManager();
      
      // 模拟WebSocket连接
      const mockConnection = {
        id: 'test_connection_id',
        userId: testUser.id,
        send: jest.fn(),
        close: jest.fn()
      };

      // 1. 注册连接
      await wsManager.handleConnection(mockConnection);
      expect(wsManager.getConnection(mockConnection.id)).toBeDefined();

      // 2. 加入房间
      const roomId = 'test_room_integration';
      await wsManager.joinRoom(mockConnection.id, roomId);
      
      const room = wsManager.getRoom(roomId);
      expect(room).toBeDefined();
      expect(room.participants.has(mockConnection.id)).toBe(true);

      // 3. 发送消息
      const message = {
        type: 'chat',
        content: 'Integration test message',
        userId: testUser.id
      };

      await wsManager.broadcastToRoom(roomId, message);
      expect(mockConnection.send).toHaveBeenCalled();

      // 4. 离开房间
      await wsManager.leaveRoom(mockConnection.id, roomId);
      const updatedRoom = wsManager.getRoom(roomId);
      expect(updatedRoom.participants.has(mockConnection.id)).toBe(false);

      // 5. 断开连接
      await wsManager.handleDisconnection(mockConnection.id);
      expect(wsManager.getConnection(mockConnection.id)).toBeUndefined();
    });

    test('should handle spectator room integration', async () => {
      // 1. 创建围观房间
      const room = await gameService.createSpectatorRoom({
        gameSessionId: testSession?.id || 'test_session',
        creatorId: testUser.id,
        title: 'Integration Test Room',
        maxSpectators: 10
      });

      expect(room).toBeDefined();
      expect(room.id).toBeDefined();

      // 2. 加入围观
      const joinResult = await gameService.joinSpectatorRoom({
        roomId: room.id,
        userId: testUser.id
      });

      expect(joinResult).toBe(true);

      // 3. 发送弹幕
      const danmakuResult = await gameService.sendDanmaku({
        roomId: room.id,
        userId: testUser.id,
        content: 'Integration test danmaku',
        type: 'text'
      });

      expect(danmakuResult).toBeDefined();
      expect(danmakuResult.id).toBeDefined();

      // 4. 创建预测游戏
      const prediction = await gameService.createPredictionGame({
        roomId: room.id,
        questionId: testQuestion.id,
        options: ['A', 'B', 'C', 'D'],
        duration: 30000
      });

      expect(prediction).toBeDefined();
      expect(prediction.id).toBeDefined();

      // 5. 提交预测
      const predictionResult = await gameService.submitPrediction({
        gameId: prediction.id,
        userId: testUser.id,
        predictedAnswer: 'A'
      });

      expect(predictionResult).toBe(true);
    });
  });

  describe('Internationalization Integration', () => {
    test('should handle multi-language content flow', async () => {
      // 1. 获取支持的语言
      const languages = await i18nService.getLanguages({ activeOnly: true });
      expect(languages).toBeDefined();
      expect(languages.length).toBeGreaterThan(0);

      // 2. 获取翻译内容
      const translations = await i18nService.getTranslations({
        language: 'zh-CN',
        category: 'ui'
      });

      expect(translations).toBeDefined();
      expect(typeof translations).toBe('object');

      // 3. 创建翻译键
      const translationKey = await i18nService.createTranslationKey({
        keyName: 'integration.test.key',
        category: 'test',
        description: 'Integration test key',
        defaultValue: 'Integration test value',
        createdBy: testUser.id
      });

      expect(translationKey).toBeDefined();
      expect(translationKey.id).toBeDefined();

      // 4. 更新翻译
      const translationUpdate = await i18nService.updateTranslation({
        keyId: translationKey.id,
        language: 'en-US',
        value: 'Integration test value in English',
        translatorId: testUser.id
      });

      expect(translationUpdate).toBeDefined();

      // 5. 批量获取翻译
      const batchTranslations = await i18nService.getBatchTranslations({
        languages: ['zh-CN', 'en-US'],
        categories: ['test']
      });

      expect(batchTranslations).toBeDefined();
      expect(batchTranslations['zh-CN']).toBeDefined();
      expect(batchTranslations['en-US']).toBeDefined();
    });
  });

  describe('Cache Integration', () => {
    test('should demonstrate cache consistency across services', async () => {
      const userId = testUser.id;
      
      // 1. 通过UserService获取用户信息（应该缓存）
      const userProfile1 = await userService.getUserProfile(userId);
      expect(userProfile1).toBeDefined();

      // 2. 更新用户信息
      await userService.updateUserProfile(userId, {
        nickname: 'Updated Integration User'
      });

      // 3. 再次获取用户信息（缓存应该已失效）
      const userProfile2 = await userService.getUserProfile(userId);
      expect(userProfile2.nickname).toBe('Updated Integration User');

      // 4. 验证缓存一致性
      const cachedProfile = await redis.get(`user:profile:${userId}`);
      if (cachedProfile) {
        const parsed = JSON.parse(cachedProfile);
        expect(parsed.nickname).toBe('Updated Integration User');
      }
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle cascading errors gracefully', async () => {
      // 1. 尝试访问不存在的资源
      await expect(gameService.getSessionStatus('non_existent_session')).rejects.toThrow();

      // 2. 尝试无效的用户操作
      await expect(ugcService.deleteContent('non_existent_content', testUser.id)).rejects.toThrow();

      // 3. 尝试无效的权限操作
      await expect(userService.updateUserProfile('non_existent_user', { nickname: 'Test' })).rejects.toThrow();

      // 4. 验证系统仍然正常工作
      const userProfile = await userService.getUserProfile(testUser.id);
      expect(userProfile).toBeDefined();
    });
  });

  describe('Performance Integration', () => {
    test('should maintain performance under integrated load', async () => {
      const startTime = performance.now();
      
      // 并发执行多种操作
      const promises = [
        userService.getUserProfile(testUser.id),
        gameService.getQuestionsByRegion('test_region', { limit: 10 }),
        ugcService.getContentList({ limit: 10 }),
        i18nService.getTranslations({ language: 'zh-CN' })
      ];

      const results = await Promise.all(promises);
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(4);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
      expect(duration).toBeLessThan(3000); // 应该在3秒内完成

      console.log(`Integrated operations duration: ${duration.toFixed(2)}ms`);
    });
  });
});
