/**
 * 围观功能性能测试
 * 
 * 测试围观功能在高并发、大数据量、网络异常等极限条件下的性能表现
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-07-31
 */

const WebSocket = require('ws');
const axios = require('axios');
const { performance } = require('perf_hooks');

describe('围观功能性能测试', () => {
    const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3001';
    const WS_URL = process.env.TEST_WS_URL || 'ws://localhost:3001';
    
    let testRoomId;
    let testUserId;
    
    beforeAll(async () => {
        // 创建测试房间
        const roomResponse = await axios.post(`${BASE_URL}/api/watch/rooms`, {
            playerInfo: {
                userId: 'test-player-001',
                nickname: '测试玩家',
                avatar: 'test-avatar.jpg'
            },
            gameConfig: {
                dialect: 'shanghai',
                difficulty: 'medium'
            }
        });
        
        testRoomId = roomResponse.data.roomId;
        testUserId = 'test-user-001';
    });
    
    afterAll(async () => {
        // 清理测试数据
        if (testRoomId) {
            await axios.delete(`${BASE_URL}/api/watch/rooms/${testRoomId}`);
        }
    });

    describe('WebSocket连接性能测试', () => {
        test('应该支持1000个并发WebSocket连接', async () => {
            const connectionCount = 1000;
            const connections = [];
            const connectionTimes = [];
            
            console.log(`开始创建${connectionCount}个WebSocket连接...`);
            
            const startTime = performance.now();
            
            // 创建并发连接
            const connectionPromises = Array.from({ length: connectionCount }, async (_, index) => {
                const connectStart = performance.now();
                
                return new Promise((resolve, reject) => {
                    const ws = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=test-user-${index}`);
                    
                    const timeout = setTimeout(() => {
                        ws.terminate();
                        reject(new Error(`Connection ${index} timeout`));
                    }, 10000);
                    
                    ws.on('open', () => {
                        clearTimeout(timeout);
                        const connectTime = performance.now() - connectStart;
                        connectionTimes.push(connectTime);
                        connections.push(ws);
                        resolve(ws);
                    });
                    
                    ws.on('error', (error) => {
                        clearTimeout(timeout);
                        reject(error);
                    });
                });
            });
            
            try {
                const results = await Promise.allSettled(connectionPromises);
                const successfulConnections = results.filter(r => r.status === 'fulfilled').length;
                const totalTime = performance.now() - startTime;
                
                console.log(`成功连接: ${successfulConnections}/${connectionCount}`);
                console.log(`总耗时: ${totalTime.toFixed(2)}ms`);
                console.log(`平均连接时间: ${(connectionTimes.reduce((a, b) => a + b, 0) / connectionTimes.length).toFixed(2)}ms`);
                
                // 验证连接成功率
                expect(successfulConnections).toBeGreaterThanOrEqual(connectionCount * 0.95); // 95%成功率
                expect(totalTime).toBeLessThan(30000); // 30秒内完成
                
                // 清理连接
                connections.forEach(ws => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                    }
                });
                
            } catch (error) {
                console.error('并发连接测试失败:', error);
                throw error;
            }
        }, 60000);
        
        test('应该在连接断开后自动重连', async () => {
            const ws = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=${testUserId}`);
            
            await new Promise((resolve) => {
                ws.on('open', resolve);
            });
            
            expect(ws.readyState).toBe(WebSocket.OPEN);
            
            // 强制断开连接
            ws.terminate();
            
            // 等待重连
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 验证重连机制（实际项目中需要检查服务端重连逻辑）
            // 这里只是示例，实际需要根据具体实现调整
            expect(true).toBe(true); // 占位符
        });
    });

    describe('弹幕消息性能测试', () => {
        test('应该支持高频率弹幕发送', async () => {
            const messageCount = 1000;
            const concurrentUsers = 50;
            const messagesPerUser = messageCount / concurrentUsers;
            
            console.log(`测试${concurrentUsers}个用户同时发送${messagesPerUser}条弹幕...`);
            
            const startTime = performance.now();
            const messageTimes = [];
            
            const userPromises = Array.from({ length: concurrentUsers }, async (_, userIndex) => {
                const ws = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=test-user-${userIndex}`);
                
                await new Promise(resolve => ws.on('open', resolve));
                
                const messagePromises = Array.from({ length: messagesPerUser }, async (_, msgIndex) => {
                    const messageStart = performance.now();
                    
                    return new Promise((resolve) => {
                        const message = {
                            type: 'barrage',
                            content: `测试弹幕消息 ${userIndex}-${msgIndex}`,
                            timestamp: Date.now()
                        };
                        
                        ws.send(JSON.stringify(message));
                        
                        // 模拟消息发送完成
                        setTimeout(() => {
                            const messageTime = performance.now() - messageStart;
                            messageTimes.push(messageTime);
                            resolve();
                        }, Math.random() * 10);
                    });
                });
                
                await Promise.all(messagePromises);
                ws.close();
            });
            
            await Promise.all(userPromises);
            
            const totalTime = performance.now() - startTime;
            const avgMessageTime = messageTimes.reduce((a, b) => a + b, 0) / messageTimes.length;
            
            console.log(`发送${messageCount}条弹幕耗时: ${totalTime.toFixed(2)}ms`);
            console.log(`平均每条弹幕耗时: ${avgMessageTime.toFixed(2)}ms`);
            console.log(`消息吞吐量: ${(messageCount / (totalTime / 1000)).toFixed(2)} 条/秒`);
            
            // 验证性能指标
            expect(totalTime).toBeLessThan(10000); // 10秒内完成
            expect(avgMessageTime).toBeLessThan(100); // 平均每条消息100ms内
        }, 30000);
        
        test('应该正确处理消息广播', async () => {
            const receiverCount = 100;
            const receivers = [];
            const receivedMessages = [];
            
            // 创建接收者连接
            for (let i = 0; i < receiverCount; i++) {
                const ws = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=receiver-${i}`);
                
                await new Promise(resolve => ws.on('open', resolve));
                
                ws.on('message', (data) => {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'barrage') {
                        receivedMessages.push({
                            receiverId: i,
                            content: message.content,
                            timestamp: Date.now()
                        });
                    }
                });
                
                receivers.push(ws);
            }
            
            // 发送测试消息
            const sender = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=sender`);
            await new Promise(resolve => sender.on('open', resolve));
            
            const testMessage = {
                type: 'barrage',
                content: '广播测试消息',
                timestamp: Date.now()
            };
            
            const broadcastStart = performance.now();
            sender.send(JSON.stringify(testMessage));
            
            // 等待消息传播
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const broadcastTime = performance.now() - broadcastStart;
            
            console.log(`消息广播耗时: ${broadcastTime.toFixed(2)}ms`);
            console.log(`接收到消息的用户数: ${receivedMessages.length}`);
            
            // 验证广播效果
            expect(receivedMessages.length).toBeGreaterThanOrEqual(receiverCount * 0.9); // 90%接收率
            expect(broadcastTime).toBeLessThan(1000); // 1秒内完成广播
            
            // 清理连接
            receivers.forEach(ws => ws.close());
            sender.close();
        });
    });

    describe('数据库性能测试', () => {
        test('应该快速查询房间列表', async () => {
            const queryCount = 100;
            const queryTimes = [];
            
            console.log(`执行${queryCount}次房间列表查询...`);
            
            for (let i = 0; i < queryCount; i++) {
                const queryStart = performance.now();
                
                const response = await axios.get(`${BASE_URL}/api/watch/rooms`, {
                    params: {
                        page: Math.floor(Math.random() * 10) + 1,
                        limit: 20,
                        dialect: Math.random() > 0.5 ? 'shanghai' : 'beijing'
                    }
                });
                
                const queryTime = performance.now() - queryStart;
                queryTimes.push(queryTime);
                
                expect(response.status).toBe(200);
                expect(response.data.rooms).toBeDefined();
            }
            
            const avgQueryTime = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
            const maxQueryTime = Math.max(...queryTimes);
            const minQueryTime = Math.min(...queryTimes);
            
            console.log(`平均查询时间: ${avgQueryTime.toFixed(2)}ms`);
            console.log(`最大查询时间: ${maxQueryTime.toFixed(2)}ms`);
            console.log(`最小查询时间: ${minQueryTime.toFixed(2)}ms`);
            
            // 验证查询性能
            expect(avgQueryTime).toBeLessThan(100); // 平均100ms内
            expect(maxQueryTime).toBeLessThan(500); // 最大500ms内
        });
        
        test('应该高效处理用户积分更新', async () => {
            const updateCount = 500;
            const updateTimes = [];
            
            console.log(`执行${updateCount}次积分更新...`);
            
            const updatePromises = Array.from({ length: updateCount }, async (_, index) => {
                const updateStart = performance.now();
                
                const response = await axios.post(`${BASE_URL}/api/watch/prediction/score`, {
                    userId: `test-user-${index}`,
                    roomId: testRoomId,
                    scoreChange: Math.floor(Math.random() * 100) + 1,
                    reason: 'performance_test'
                });
                
                const updateTime = performance.now() - updateStart;
                updateTimes.push(updateTime);
                
                return response;
            });
            
            const results = await Promise.allSettled(updatePromises);
            const successCount = results.filter(r => r.status === 'fulfilled').length;
            
            const avgUpdateTime = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length;
            
            console.log(`成功更新: ${successCount}/${updateCount}`);
            console.log(`平均更新时间: ${avgUpdateTime.toFixed(2)}ms`);
            
            // 验证更新性能
            expect(successCount).toBeGreaterThanOrEqual(updateCount * 0.95); // 95%成功率
            expect(avgUpdateTime).toBeLessThan(200); // 平均200ms内
        });
    });

    describe('内存压力测试', () => {
        test('应该在高负载下保持内存稳定', async () => {
            const initialMemory = process.memoryUsage();
            console.log('初始内存使用:', formatMemory(initialMemory));
            
            // 创建大量连接和数据
            const connections = [];
            const dataObjects = [];
            
            try {
                // 创建连接
                for (let i = 0; i < 200; i++) {
                    const ws = new WebSocket(`${WS_URL}/watch/${testRoomId}?userId=memory-test-${i}`);
                    await new Promise(resolve => ws.on('open', resolve));
                    connections.push(ws);
                    
                    // 创建内存数据
                    dataObjects.push({
                        id: i,
                        data: new Array(1000).fill(Math.random()),
                        timestamp: Date.now()
                    });
                }
                
                const peakMemory = process.memoryUsage();
                console.log('峰值内存使用:', formatMemory(peakMemory));
                
                // 发送大量消息
                for (let i = 0; i < 100; i++) {
                    const randomConnection = connections[Math.floor(Math.random() * connections.length)];
                    if (randomConnection.readyState === WebSocket.OPEN) {
                        randomConnection.send(JSON.stringify({
                            type: 'barrage',
                            content: `内存测试消息 ${i}`,
                            timestamp: Date.now()
                        }));
                    }
                }
                
                // 等待处理完成
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                const afterTestMemory = process.memoryUsage();
                console.log('测试后内存使用:', formatMemory(afterTestMemory));
                
                // 验证内存增长合理
                const memoryIncrease = afterTestMemory.heapUsed - initialMemory.heapUsed;
                const memoryIncreaseMB = memoryIncrease / 1024 / 1024;
                
                console.log(`内存增长: ${memoryIncreaseMB.toFixed(2)}MB`);
                
                expect(memoryIncreaseMB).toBeLessThan(500); // 内存增长不超过500MB
                
            } finally {
                // 清理资源
                connections.forEach(ws => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                    }
                });
                
                dataObjects.length = 0;
                
                // 强制垃圾回收
                if (global.gc) {
                    global.gc();
                }
                
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const finalMemory = process.memoryUsage();
                console.log('清理后内存使用:', formatMemory(finalMemory));
            }
        }, 60000);
    });

    describe('网络异常测试', () => {
        test('应该处理网络延迟', async () => {
            // 模拟网络延迟
            const delays = [100, 500, 1000, 2000];
            
            for (const delay of delays) {
                console.log(`测试${delay}ms网络延迟...`);
                
                const startTime = performance.now();
                
                // 模拟延迟请求
                await new Promise(resolve => setTimeout(resolve, delay));
                
                const response = await axios.get(`${BASE_URL}/api/watch/rooms/${testRoomId}`);
                
                const totalTime = performance.now() - startTime;
                
                console.log(`${delay}ms延迟下响应时间: ${totalTime.toFixed(2)}ms`);
                
                expect(response.status).toBe(200);
                expect(totalTime).toBeGreaterThan(delay); // 至少包含模拟延迟
            }
        });
        
        test('应该处理请求超时', async () => {
            // 测试超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Request timeout')), 5000);
            });
            
            const requestPromise = axios.get(`${BASE_URL}/api/watch/rooms`, {
                timeout: 3000
            });
            
            try {
                await Promise.race([requestPromise, timeoutPromise]);
                // 如果请求成功完成，验证响应
                expect(true).toBe(true);
            } catch (error) {
                // 验证超时错误处理
                expect(error.message).toMatch(/timeout|Request timeout/i);
            }
        });
    });

    // 工具函数
    function formatMemory(memoryUsage) {
        return {
            rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)}MB`,
            heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
            heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
            external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)}MB`
        };
    }
});
