/**
 * 测试数据管理工具
 * 提供测试数据的创建、清理和管理功能
 */

const { DatabaseManager } = require('../../serverless/utils/database');
const { RedisManager } = require('../../serverless/utils/redis');
const crypto = require('crypto');

class TestDataManager {
  constructor() {
    this.createdUsers = [];
    this.createdSessions = [];
    this.createdGameRecords = [];
    this.dbManager = DatabaseManager.getInstance();
    this.redisManager = RedisManager.getInstance();
  }

  /**
   * 创建测试用户
   */
  async createTestUser(userData = {}) {
    const defaultUser = {
      openid: `test_openid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      unionid: `test_unionid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      nickname: '测试用户',
      avatar_url: 'https://example.com/avatar.jpg',
      gender: 1,
      country: '中国',
      province: '广东',
      city: '深圳'
    };

    const user = { ...defaultUser, ...userData };
    
    const pool = this.dbManager.getPool();
    const [result] = await pool.execute(
      `INSERT INTO users (openid, unionid, nickname, avatar_url, gender, country, province, city) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [user.openid, user.unionid, user.nickname, user.avatar_url, user.gender, user.country, user.province, user.city]
    );

    user.id = result.insertId;
    this.createdUsers.push(user);
    
    return user;
  }

  /**
   * 创建多个测试用户
   */
  async createTestUsers(count = 5, baseData = {}) {
    const users = [];
    
    for (let i = 0; i < count; i++) {
      const userData = {
        ...baseData,
        nickname: `测试用户${i + 1}`,
        openid: `test_openid_${i}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      
      const user = await this.createTestUser(userData);
      users.push(user);
    }
    
    return users;
  }

  /**
   * 创建测试用户会话
   */
  async createTestSession(userId, sessionData = {}) {
    const defaultSession = {
      session_id: `test_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      access_token_hash: crypto.createHash('sha256').update(`test_access_token_${Date.now()}`).digest('hex'),
      refresh_token_hash: crypto.createHash('sha256').update(`test_refresh_token_${Date.now()}`).digest('hex'),
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
    };

    const session = { ...defaultSession, ...sessionData };
    
    const pool = this.dbManager.getPool();
    const [result] = await pool.execute(
      `INSERT INTO user_sessions (user_id, session_id, access_token_hash, refresh_token_hash, expires_at) 
       VALUES (?, ?, ?, ?, ?)`,
      [userId, session.session_id, session.access_token_hash, session.refresh_token_hash, session.expires_at]
    );

    session.id = result.insertId;
    session.user_id = userId;
    this.createdSessions.push(session);
    
    return session;
  }

  /**
   * 创建测试游戏记录
   */
  async createTestGameRecord(userId, gameData = {}) {
    const defaultGame = {
      session_id: `game_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      total_questions: 10,
      correct_answers: Math.floor(Math.random() * 10),
      score: 0,
      time_spent: Math.floor(Math.random() * 300) + 60, // 1-6分钟
      completed_at: new Date()
    };

    const game = { ...defaultGame, ...gameData };
    game.score = game.correct_answers * 10; // 每题10分
    
    const pool = this.dbManager.getPool();
    const [result] = await pool.execute(
      `INSERT INTO game_records (user_id, session_id, total_questions, correct_answers, score, time_spent, completed_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [userId, game.session_id, game.total_questions, game.correct_answers, game.score, game.time_spent, game.completed_at]
    );

    game.id = result.insertId;
    game.user_id = userId;
    this.createdGameRecords.push(game);
    
    return game;
  }

  /**
   * 创建测试方言问题
   */
  async createTestDialectQuestions(count = 10) {
    const questions = [];
    const pool = this.dbManager.getPool();
    
    for (let i = 0; i < count; i++) {
      const question = {
        audio_url: `https://example.com/audio/test_${i}.mp3`,
        correct_region: `测试地区${i + 1}`,
        options: JSON.stringify([
          `测试地区${i + 1}`,
          `测试地区${i + 2}`,
          `测试地区${i + 3}`,
          `测试地区${i + 4}`
        ]),
        difficulty: Math.floor(Math.random() * 3) + 1, // 1-3
        category: '测试分类',
        cultural_context: `这是测试问题${i + 1}的文化背景`,
        is_active: true
      };

      const [result] = await pool.execute(
        `INSERT INTO dialect_questions (audio_url, correct_region, options, difficulty, category, cultural_context, is_active) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [question.audio_url, question.correct_region, question.options, question.difficulty, question.category, question.cultural_context, question.is_active]
      );

      question.id = result.insertId;
      questions.push(question);
    }
    
    return questions;
  }

  /**
   * 创建测试排行榜数据
   */
  async createTestLeaderboard(users, period = 'weekly') {
    const pool = this.dbManager.getPool();
    const leaderboardData = [];
    
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      const data = {
        user_id: user.id,
        period,
        total_score: Math.floor(Math.random() * 1000) + 100,
        games_played: Math.floor(Math.random() * 50) + 10,
        avg_score: 0,
        best_score: 0,
        rank_position: i + 1,
        period_start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 一周前
        period_end: new Date()
      };
      
      data.avg_score = Math.floor(data.total_score / data.games_played);
      data.best_score = data.avg_score + Math.floor(Math.random() * 50);

      const [result] = await pool.execute(
        `INSERT INTO leaderboards (user_id, period, total_score, games_played, avg_score, best_score, rank_position, period_start, period_end) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [data.user_id, data.period, data.total_score, data.games_played, data.avg_score, data.best_score, data.rank_position, data.period_start, data.period_end]
      );

      data.id = result.insertId;
      leaderboardData.push(data);
    }
    
    return leaderboardData;
  }

  /**
   * 创建测试用户关系
   */
  async createTestUserRelationships(users) {
    const pool = this.dbManager.getPool();
    const relationships = [];
    
    // 创建一些好友关系
    for (let i = 0; i < users.length - 1; i++) {
      const relationship = {
        user_id: users[i].id,
        friend_id: users[i + 1].id,
        relationship_type: 'friend',
        status: 'accepted',
        created_at: new Date()
      };

      const [result] = await pool.execute(
        `INSERT INTO user_relationships (user_id, friend_id, relationship_type, status, created_at) 
         VALUES (?, ?, ?, ?, ?)`,
        [relationship.user_id, relationship.friend_id, relationship.relationship_type, relationship.status, relationship.created_at]
      );

      relationship.id = result.insertId;
      relationships.push(relationship);
    }
    
    return relationships;
  }

  /**
   * 创建测试分享记录
   */
  async createTestShareRecords(users, count = 5) {
    const pool = this.dbManager.getPool();
    const shareRecords = [];
    
    for (let i = 0; i < count; i++) {
      const user = users[i % users.length];
      const record = {
        user_id: user.id,
        share_type: ['wechat_friend', 'wechat_moments', 'wechat_group'][Math.floor(Math.random() * 3)],
        content_type: 'game_result',
        content_id: Math.floor(Math.random() * 1000),
        share_data: JSON.stringify({
          score: Math.floor(Math.random() * 100),
          rank: Math.floor(Math.random() * 100) + 1,
          message: '我在家乡话猜猜猜中获得了高分！'
        }),
        created_at: new Date()
      };

      const [result] = await pool.execute(
        `INSERT INTO share_records (user_id, share_type, content_type, content_id, share_data, created_at) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [record.user_id, record.share_type, record.content_type, record.content_id, record.share_data, record.created_at]
      );

      record.id = result.insertId;
      shareRecords.push(record);
    }
    
    return shareRecords;
  }

  /**
   * 设置Redis测试数据
   */
  async setupRedisTestData() {
    const redis = this.redisManager.getClient();
    
    // 设置一些测试缓存数据
    await redis.setex('test:cache:user:1', 3600, JSON.stringify({
      id: 1,
      nickname: '缓存测试用户',
      score: 100
    }));

    // 设置限流测试数据
    await redis.setex('rate_limit:test_ip:global', 3600, '5');
    await redis.setex('rate_limit:user:1:api', 3600, '10');

    // 设置会话测试数据
    await redis.setex('session:test_session_123', 3600, JSON.stringify({
      userId: 1,
      loginTime: Date.now(),
      lastActivity: Date.now()
    }));
  }

  /**
   * 清理所有测试数据
   */
  async cleanupAllTestData() {
    const pool = this.dbManager.getPool();
    
    try {
      // 清理数据库表（按依赖关系顺序）
      const tables = [
        'share_records',
        'user_relationships', 
        'leaderboards',
        'game_records',
        'user_sessions',
        'token_blacklist',
        'dialect_questions',
        'users'
      ];

      for (const table of tables) {
        await pool.execute(`DELETE FROM ${table} WHERE 1=1`);
        await pool.execute(`ALTER TABLE ${table} AUTO_INCREMENT = 1`);
      }

      // 清理Redis测试数据
      const redis = this.redisManager.getClient();
      const keys = await redis.keys('test:*');
      if (keys.length > 0) {
        await redis.del(...keys);
      }

      // 清理限流数据
      const rateLimitKeys = await redis.keys('rate_limit:*');
      if (rateLimitKeys.length > 0) {
        await redis.del(...rateLimitKeys);
      }

      // 清理会话数据
      const sessionKeys = await redis.keys('session:*');
      if (sessionKeys.length > 0) {
        await redis.del(...sessionKeys);
      }

      // 重置内部状态
      this.createdUsers = [];
      this.createdSessions = [];
      this.createdGameRecords = [];

      console.log('✅ 测试数据清理完成');
      
    } catch (error) {
      console.error('❌ 测试数据清理失败:', error);
      throw error;
    }
  }

  /**
   * 获取随机测试用户
   */
  getRandomTestUser() {
    if (this.createdUsers.length === 0) {
      throw new Error('没有可用的测试用户，请先创建测试用户');
    }
    
    const randomIndex = Math.floor(Math.random() * this.createdUsers.length);
    return this.createdUsers[randomIndex];
  }

  /**
   * 获取所有创建的测试用户
   */
  getAllTestUsers() {
    return [...this.createdUsers];
  }

  /**
   * 获取所有创建的测试会话
   */
  getAllTestSessions() {
    return [...this.createdSessions];
  }

  /**
   * 获取所有创建的游戏记录
   */
  getAllTestGameRecords() {
    return [...this.createdGameRecords];
  }
}

// 创建全局实例
const testDataManager = new TestDataManager();

// 导出便捷函数
module.exports = {
  TestDataManager,
  createTestUser: (userData) => testDataManager.createTestUser(userData),
  createTestUsers: (count, baseData) => testDataManager.createTestUsers(count, baseData),
  createTestSession: (userId, sessionData) => testDataManager.createTestSession(userId, sessionData),
  createTestGameRecord: (userId, gameData) => testDataManager.createTestGameRecord(userId, gameData),
  createTestDialectQuestions: (count) => testDataManager.createTestDialectQuestions(count),
  createTestLeaderboard: (users, period) => testDataManager.createTestLeaderboard(users, period),
  createTestUserRelationships: (users) => testDataManager.createTestUserRelationships(users),
  createTestShareRecords: (users, count) => testDataManager.createTestShareRecords(users, count),
  setupRedisTestData: () => testDataManager.setupRedisTestData(),
  cleanupTestData: () => testDataManager.cleanupAllTestData(),
  getRandomTestUser: () => testDataManager.getRandomTestUser(),
  getAllTestUsers: () => testDataManager.getAllTestUsers(),
  testDataManager
};
