/**
 * 测试应用创建工具
 * 为集成测试提供模拟的Express应用
 */

const express = require('express');
const cors = require('cors');
const { DatabaseManager } = require('../../serverless/utils/database');
const { RedisManager } = require('../../serverless/utils/redis');

// 模拟微信API响应
const mockWechatAPI = {
  code2Session: {
    'test_wechat_code_123': {
      openid: 'test_openid_123',
      session_key: 'test_session_key_123',
      unionid: 'test_unionid_123'
    },
    'test_wechat_code_456': {
      openid: 'test_openid_456',
      session_key: 'test_session_key_456',
      unionid: 'test_unionid_456'
    }
  }
};

/**
 * 创建测试应用
 */
async function createTestApp() {
  const app = express();
  
  // 基础中间件
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
  process.env.WECHAT_APPID = 'test_wechat_appid';
  process.env.WECHAT_SECRET = 'test_wechat_secret';

  // 模拟微信API
  mockWechatAPIRequests();

  // 初始化数据库连接
  await initTestDatabase();

  // 加载路由
  await loadRoutes(app);

  return app;
}

/**
 * 模拟微信API请求
 */
function mockWechatAPIRequests() {
  const nock = require('nock');
  
  // 模拟微信code2session接口
  nock('https://api.weixin.qq.com')
    .persist()
    .get('/sns/jscode2session')
    .query(true)
    .reply((uri, requestBody) => {
      const url = new URL('https://api.weixin.qq.com' + uri);
      const code = url.searchParams.get('js_code');
      
      if (mockWechatAPI.code2Session[code]) {
        return [200, mockWechatAPI.code2Session[code]];
      } else {
        return [400, { errcode: 40013, errmsg: 'invalid code' }];
      }
    });
}

/**
 * 初始化测试数据库
 */
async function initTestDatabase() {
  // 使用内存数据库或测试数据库
  const testDbConfig = {
    host: process.env.TEST_DB_HOST || 'localhost',
    port: process.env.TEST_DB_PORT || 3306,
    user: process.env.TEST_DB_USER || 'test',
    password: process.env.TEST_DB_PASSWORD || 'test',
    database: process.env.TEST_DB_NAME || 'dialect_game_test',
    charset: 'utf8mb4'
  };

  // 初始化数据库管理器
  const dbManager = DatabaseManager.getInstance();
  await dbManager.initialize(testDbConfig);

  // 初始化Redis（使用测试Redis实例）
  const redisManager = RedisManager.getInstance();
  await redisManager.initialize({
    host: process.env.TEST_REDIS_HOST || 'localhost',
    port: process.env.TEST_REDIS_PORT || 6379,
    db: process.env.TEST_REDIS_DB || 1 // 使用不同的数据库
  });

  // 运行数据库迁移
  await runTestMigrations();
}

/**
 * 运行测试数据库迁移
 */
async function runTestMigrations() {
  const dbManager = DatabaseManager.getInstance();
  const pool = dbManager.getPool();

  // 创建测试表
  const migrations = [
    `CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      openid VARCHAR(100) UNIQUE NOT NULL,
      unionid VARCHAR(100),
      nickname VARCHAR(100),
      avatar_url VARCHAR(500),
      gender TINYINT DEFAULT 0,
      country VARCHAR(50),
      province VARCHAR(50),
      city VARCHAR(50),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_openid (openid),
      INDEX idx_unionid (unionid)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,

    `CREATE TABLE IF NOT EXISTS user_sessions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_id INT NOT NULL,
      session_id VARCHAR(100) UNIQUE NOT NULL,
      access_token_hash VARCHAR(255),
      refresh_token_hash VARCHAR(255),
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      INDEX idx_session_id (session_id),
      INDEX idx_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`,

    `CREATE TABLE IF NOT EXISTS token_blacklist (
      id INT AUTO_INCREMENT PRIMARY KEY,
      token_hash VARCHAR(255) UNIQUE NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_token_hash (token_hash),
      INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4`
  ];

  for (const migration of migrations) {
    await pool.execute(migration);
  }
}

/**
 * 加载路由
 */
async function loadRoutes(app) {
  // 加载认证路由
  const authHandler = require('../../serverless/auth/handler');
  app.use('/v1/auth', createRouterFromHandler(authHandler));

  // 加载用户路由
  const userHandler = require('../../serverless/user/handler');
  app.use('/v1/users', createRouterFromHandler(userHandler));

  // 加载游戏路由
  const gameHandler = require('../../serverless/game/handler');
  app.use('/v1/game', createRouterFromHandler(gameHandler));

  // 加载音频路由
  const audioHandler = require('../../serverless/audio/handler');
  app.use('/v1/audio', createRouterFromHandler(audioHandler));

  // 加载排行榜路由
  const leaderboardHandler = require('../../serverless/leaderboard/handler');
  app.use('/v1/leaderboard', createRouterFromHandler(leaderboardHandler));

  // 加载社交路由
  const socialHandler = require('../../serverless/social/handler');
  app.use('/v1/social', createRouterFromHandler(socialHandler));
}

/**
 * 从Serverless handler创建Express路由
 */
function createRouterFromHandler(handler) {
  const router = express.Router();
  
  if (handler.routes) {
    for (const [route, handlerFunc] of Object.entries(handler.routes)) {
      const [method, path] = route.split(' ');
      const expressPath = path.replace(/\{(\w+)\}/g, ':$1');
      
      router[method.toLowerCase()](expressPath, async (req, res) => {
        try {
          // 构造Serverless事件对象
          const event = {
            httpMethod: method,
            path: req.path,
            pathParameters: req.params,
            queryStringParameters: req.query,
            headers: req.headers,
            body: JSON.stringify(req.body),
            requestContext: {
              requestId: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
            }
          };

          // 构造Serverless上下文对象
          const context = {
            requestId: event.requestContext.requestId,
            functionName: 'test-function',
            functionVersion: '1.0.0'
          };

          // 调用handler
          const result = await handlerFunc(event, context);
          
          // 返回响应
          res.status(result.statusCode || 200);
          
          if (result.headers) {
            Object.entries(result.headers).forEach(([key, value]) => {
              res.set(key, value);
            });
          }
          
          if (result.body) {
            const body = typeof result.body === 'string' ? JSON.parse(result.body) : result.body;
            res.json(body);
          } else {
            res.end();
          }
          
        } catch (error) {
          console.error('Handler error:', error);
          res.status(500).json({
            code: 50000,
            message: '服务器内部错误',
            error: error.message
          });
        }
      });
    }
  }
  
  return router;
}

/**
 * 清理测试数据
 */
async function cleanupTestApp() {
  const dbManager = DatabaseManager.getInstance();
  const redisManager = RedisManager.getInstance();
  
  if (dbManager.isInitialized()) {
    await dbManager.close();
  }
  
  if (redisManager.isInitialized()) {
    await redisManager.close();
  }
}

module.exports = {
  createTestApp,
  cleanupTestApp,
  mockWechatAPI
};
