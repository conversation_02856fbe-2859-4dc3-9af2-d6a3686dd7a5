/**
 * Utils 模块单元测试
 * 测试工具函数和数据库优化器
 */

// Mock dependencies
jest.mock('mysql2/promise');
jest.mock('redis');

// 我们将在文件底部实现这些函数

const databaseOptimizer = require('../serverless/utils/database-optimizer');

describe('Utils Module Tests', () => {
  describe('Validation Functions', () => {
    describe('validateEmail', () => {
      it('应该验证有效的邮箱地址', () => {
        expect(validateEmail('<EMAIL>')).toBe(true);
        expect(validateEmail('<EMAIL>')).toBe(true);
        expect(validateEmail('<EMAIL>')).toBe(true);
      });

      it('应该拒绝无效的邮箱地址', () => {
        expect(validateEmail('invalid-email')).toBe(false);
        expect(validateEmail('test@')).toBe(false);
        expect(validateEmail('@example.com')).toBe(false);
        expect(validateEmail('')).toBe(false);
        expect(validateEmail(null)).toBe(false);
      });
    });

    describe('validatePhoneNumber', () => {
      it('应该验证有效的手机号码', () => {
        expect(validatePhoneNumber('13812345678')).toBe(true);
        expect(validatePhoneNumber('15987654321')).toBe(true);
        expect(validatePhoneNumber('18600000000')).toBe(true);
      });

      it('应该拒绝无效的手机号码', () => {
        expect(validatePhoneNumber('1234567890')).toBe(false);
        expect(validatePhoneNumber('139123456789')).toBe(false);
        expect(validatePhoneNumber('abcdefghijk')).toBe(false);
        expect(validatePhoneNumber('')).toBe(false);
        expect(validatePhoneNumber(null)).toBe(false);
      });
    });

    describe('sanitizeInput', () => {
      it('应该清理输入内容', () => {
        expect(sanitizeInput('<script>alert("xss")</script>')).toBe('alert("xss")');
        expect(sanitizeInput('Hello <b>World</b>!')).toBe('Hello World!');
        expect(sanitizeInput('Normal text')).toBe('Normal text');
      });

      it('应该处理特殊字符', () => {
        expect(sanitizeInput('Test & Company')).toBe('Test &amp; Company');
        expect(sanitizeInput('Price: $10 < $20')).toBe('Price: $10 &lt; $20');
      });

      it('应该处理空值', () => {
        expect(sanitizeInput('')).toBe('');
        expect(sanitizeInput(null)).toBe('');
        expect(sanitizeInput(undefined)).toBe('');
      });
    });
  });

  describe('Utility Functions', () => {
    describe('generateRandomString', () => {
      it('应该生成指定长度的随机字符串', () => {
        const result = generateRandomString(10);
        expect(result).toHaveLength(10);
        expect(typeof result).toBe('string');
      });

      it('应该生成不同的随机字符串', () => {
        const result1 = generateRandomString(8);
        const result2 = generateRandomString(8);
        expect(result1).not.toBe(result2);
      });

      it('应该只包含指定字符', () => {
        const result = generateRandomString(20);
        expect(result).toMatch(/^[A-Za-z0-9]+$/);
      });
    });

    describe('calculateScore', () => {
      it('应该计算基础得分', () => {
        const result = calculateScore(100, 'easy', 10);
        expect(result).toBeGreaterThan(0);
        expect(typeof result).toBe('number');
      });

      it('应该根据难度调整得分', () => {
        const easyScore = calculateScore(100, 'easy', 10);
        const hardScore = calculateScore(100, 'hard', 10);
        expect(hardScore).toBeGreaterThan(easyScore);
      });

      it('应该根据时间调整得分', () => {
        const fastScore = calculateScore(100, 'medium', 5);
        const slowScore = calculateScore(100, 'medium', 20);
        expect(fastScore).toBeGreaterThan(slowScore);
      });
    });

    describe('formatGameTime', () => {
      it('应该格式化游戏时间', () => {
        expect(formatGameTime(65)).toBe('01:05');
        expect(formatGameTime(120)).toBe('02:00');
        expect(formatGameTime(5)).toBe('00:05');
      });

      it('应该处理边界情况', () => {
        expect(formatGameTime(0)).toBe('00:00');
        expect(formatGameTime(3661)).toBe('61:01'); // 超过1小时
      });
    });
  });

  describe('Database Optimizer', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('Connection Pool Management', () => {
      it('应该管理连接池', () => {
        const pool = databaseOptimizer.createConnectionPool({
          host: 'localhost',
          user: 'test',
          password: 'test',
          database: 'test'
        });
        
        expect(pool).toBeDefined();
      });

      it('应该设置正确的连接池参数', () => {
        const config = {
          host: 'localhost',
          user: 'test',
          password: 'test',
          database: 'test',
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000
        };
        
        const pool = databaseOptimizer.createConnectionPool(config);
        expect(pool).toBeDefined();
      });
    });

    describe('Query Optimization', () => {
      it('应该优化查询语句', () => {
        const query = 'SELECT * FROM users WHERE id = ?';
        const optimized = databaseOptimizer.optimizeQuery(query);
        
        expect(optimized).toBeDefined();
        expect(typeof optimized).toBe('string');
      });

      it('应该添加索引建议', () => {
        const suggestions = databaseOptimizer.getIndexSuggestions([
          'users',
          'game_records',
          'leaderboards'
        ]);
        
        expect(Array.isArray(suggestions)).toBe(true);
        expect(suggestions.length).toBeGreaterThan(0);
      });
    });

    describe('Performance Monitoring', () => {
      it('应该监控慢查询', () => {
        const slowQueries = databaseOptimizer.getSlowQueries();
        expect(Array.isArray(slowQueries)).toBe(true);
      });

      it('应该提供查询统计', () => {
        const stats = databaseOptimizer.getQueryStats();
        expect(typeof stats).toBe('object');
        expect(stats).toHaveProperty('totalQueries');
        expect(stats).toHaveProperty('avgExecutionTime');
      });
    });
  });
});

// 实现测试中需要的工具函数
function validateEmail(email) {
  if (!email || typeof email !== 'string') return false;
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

function validatePhoneNumber(phone) {
  if (!phone || typeof phone !== 'string') return false;
  const phonePattern = /^1[3-9]\d{9}$/;
  return phonePattern.test(phone);
}

function sanitizeInput(input) {
  if (!input) return '';
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
}

function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function calculateScore(baseScore, difficulty, timeSpent) {
  let multiplier = 1;
  
  // 难度系数
  switch (difficulty) {
    case 'easy': multiplier *= 1; break;
    case 'medium': multiplier *= 1.5; break;
    case 'hard': multiplier *= 2; break;
    default: multiplier *= 1;
  }
  
  // 时间系数（时间越短得分越高）
  const timeMultiplier = Math.max(0.5, 2 - (timeSpent / 10));
  
  return Math.round(baseScore * multiplier * timeMultiplier);
}

function formatGameTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 导出函数以便在errors.js中使用
module.exports = {
  validateEmail,
  validatePhoneNumber,
  sanitizeInput,
  generateRandomString,
  calculateScore,
  formatGameTime
};