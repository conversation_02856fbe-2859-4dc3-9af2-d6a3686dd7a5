/**
 * Models 模块单元测试
 * 测试数据模型的基本功能
 */

// Mock database connection
jest.mock('../serverless/utils/database', () => ({
  getConnection: jest.fn().mockResolvedValue({
    query: jest.fn(),
    execute: jest.fn(),
    beginTransaction: jest.fn(),
    commit: jest.fn(),
    rollback: jest.fn()
  })
}));

const User = require('../serverless/models/User');
const Question = require('../serverless/models/Question');
const GameResult = require('../serverless/models/GameResult');
const GameSession = require('../serverless/models/GameSession');

describe('Models Tests', () => {
  let mockConnection;

  beforeEach(() => {
    jest.clearAllMocks();
    mockConnection = require('../serverless/utils/database').getConnection();
  });

  describe('User Model', () => {
    describe('create', () => {
      it('应该创建新用户', async () => {
        const userData = {
          openid: 'test_openid_123',
          nickname: '测试用户',
          avatar_url: 'https://example.com/avatar.jpg',
          gender: 1,
          province: '广东',
          city: '深圳'
        };

        mockConnection.execute.mockResolvedValue([{ insertId: 123 }]);
        mockConnection.query.mockResolvedValue([[{
          id: 123,
          ...userData,
          total_score: 0,
          games_played: 0,
          status: 1,
          created_at: new Date(),
          updated_at: new Date()
        }]]);

        const user = await User.create(userData);

        expect(user).toBeDefined();
        expect(user.id).toBe(123);
        expect(user.openid).toBe(userData.openid);
        expect(user.nickname).toBe(userData.nickname);
        expect(mockConnection.execute).toHaveBeenCalledWith(
          expect.stringContaining('INSERT INTO users'),
          expect.arrayContaining([userData.openid, userData.nickname])
        );
      });

      it('应该处理重复的openid', async () => {
        const userData = {
          openid: 'duplicate_openid',
          nickname: '重复用户'
        };

        const error = new Error('Duplicate entry');
        error.code = 'ER_DUP_ENTRY';
        mockConnection.execute.mockRejectedValue(error);

        await expect(User.create(userData)).rejects.toThrow('用户已存在');
      });
    });

    describe('findByOpenId', () => {
      it('应该根据openid找到用户', async () => {
        const mockUser = {
          id: 123,
          openid: 'test_openid',
          nickname: '测试用户',
          total_score: 5000,
          games_played: 25
        };

        mockConnection.query.mockResolvedValue([[mockUser]]);

        const user = await User.findByOpenId('test_openid');

        expect(user).toBeDefined();
        expect(user.id).toBe(123);
        expect(user.openid).toBe('test_openid');
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('SELECT * FROM users WHERE openid = ?'),
          ['test_openid']
        );
      });

      it('应该在用户不存在时返回null', async () => {
        mockConnection.query.mockResolvedValue([[]]);

        const user = await User.findByOpenId('nonexistent_openid');

        expect(user).toBeNull();
      });
    });

    describe('findById', () => {
      it('应该根据ID找到用户', async () => {
        const mockUser = {
          id: 123,
          openid: 'test_openid',
          nickname: '测试用户'
        };

        mockConnection.query.mockResolvedValue([[mockUser]]);

        const user = await User.findById(123);

        expect(user).toBeDefined();
        expect(user.id).toBe(123);
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('SELECT * FROM users WHERE id = ?'),
          [123]
        );
      });
    });

    describe('update', () => {
      it('应该更新用户信息', async () => {
        const mockUser = new User({
          id: 123,
          openid: 'test_openid',
          nickname: '旧昵称'
        });

        const updates = {
          nickname: '新昵称',
          avatar_url: 'https://example.com/new-avatar.jpg'
        };

        mockConnection.execute.mockResolvedValue([{ affectedRows: 1 }]);
        mockConnection.query.mockResolvedValue([[{
          id: 123,
          openid: 'test_openid',
          nickname: '新昵称',
          avatar_url: 'https://example.com/new-avatar.jpg'
        }]]);

        const result = await mockUser.update(updates);

        expect(result).toBeDefined();
        expect(result.nickname).toBe('新昵称');
        expect(mockConnection.execute).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE users SET'),
          expect.arrayContaining(['新昵称', 'https://example.com/new-avatar.jpg', 123])
        );
      });
    });

    describe('getUserStats', () => {
      it('应该获取用户统计信息', async () => {
        const mockStats = {
          total_games: 50,
          total_score: 15000,
          average_score: 300,
          best_score: 980,
          correct_rate: 0.75
        };

        mockConnection.query.mockResolvedValue([[mockStats]]);

        const stats = await User.getUserStats(123);

        expect(stats).toBeDefined();
        expect(stats.total_games).toBe(50);
        expect(stats.average_score).toBe(300);
        expect(typeof stats.correct_rate).toBe('number');
      });
    });

    describe('toJSON', () => {
      it('应该返回安全的用户信息', () => {
        const user = new User({
          id: 123,
          openid: 'test_openid',
          nickname: '测试用户',
          avatar_url: 'https://example.com/avatar.jpg',
          total_score: 5000,
          games_played: 25,
          status: 1,
          created_at: new Date(),
          updated_at: new Date()
        });

        const json = user.toJSON();

        expect(json).toHaveProperty('id');
        expect(json).toHaveProperty('nickname');
        expect(json).toHaveProperty('avatar_url');
        expect(json).toHaveProperty('total_score');
        expect(json).not.toHaveProperty('openid'); // 应该被过滤掉
      });

      it('应该在包含私有信息时返回完整信息', () => {
        const user = new User({
          id: 123,
          openid: 'test_openid',
          nickname: '测试用户'
        });

        const json = user.toJSON(true);

        expect(json).toHaveProperty('id');
        expect(json).toHaveProperty('openid'); // 应该包含私有信息
      });
    });
  });

  describe('Question Model', () => {
    describe('findByCategory', () => {
      it('应该根据类别获取题目', async () => {
        const mockQuestions = [
          {
            id: 1,
            category: 'sichuan',
            difficulty: 'easy',
            audio_url: 'https://example.com/audio1.mp3',
            options: JSON.stringify(['选项A', '选项B', '选项C', '选项D']),
            correct_answer: 0
          },
          {
            id: 2,
            category: 'sichuan',
            difficulty: 'medium',
            audio_url: 'https://example.com/audio2.mp3',
            options: JSON.stringify(['选项A', '选项B', '选项C', '选项D']),
            correct_answer: 1
          }
        ];

        mockConnection.query.mockResolvedValue([mockQuestions]);

        const questions = await Question.findByCategory('sichuan', 'easy', 10);

        expect(questions).toHaveLength(2);
        expect(questions[0]).toHaveProperty('audio_url');
        expect(Array.isArray(questions[0].options)).toBe(true);
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('WHERE category = ?'),
          expect.arrayContaining(['sichuan'])
        );
      });
    });

    describe('findById', () => {
      it('应该根据ID获取题目', async () => {
        const mockQuestion = {
          id: 1,
          category: 'sichuan',
          difficulty: 'easy',
          audio_url: 'https://example.com/audio1.mp3',
          options: JSON.stringify(['选项A', '选项B', '选项C', '选项D']),
          correct_answer: 0,
          region: '四川'
        };

        mockConnection.query.mockResolvedValue([[mockQuestion]]);

        const question = await Question.findById(1);

        expect(question).toBeDefined();
        expect(question.id).toBe(1);
        expect(Array.isArray(question.options)).toBe(true);
        expect(question.options).toHaveLength(4);
      });
    });

    describe('getRandomQuestions', () => {
      it('应该获取随机题目', async () => {
        const mockQuestions = [
          { id: 1, category: 'sichuan', difficulty: 'easy' },
          { id: 2, category: 'guangdong', difficulty: 'medium' },
          { id: 3, category: 'shanghai', difficulty: 'hard' }
        ];

        mockConnection.query.mockResolvedValue([mockQuestions]);

        const questions = await Question.getRandomQuestions(3);

        expect(questions).toHaveLength(3);
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('ORDER BY RAND()'),
          [3]
        );
      });
    });

    describe('validateAnswer', () => {
      it('应该验证答案正确性', () => {
        const question = new Question({
          id: 1,
          correct_answer: 2,
          options: ['A', 'B', 'C', 'D']
        });

        expect(question.validateAnswer(2)).toBe(true);
        expect(question.validateAnswer(0)).toBe(false);
        expect(question.validateAnswer(4)).toBe(false); // 超出范围
      });
    });

    describe('calculateScore', () => {
      it('应该计算得分', () => {
        const question = new Question({
          difficulty: 'medium',
          base_score: 100
        });

        const score = question.calculateScore(true, 10);
        expect(typeof score).toBe('number');
        expect(score).toBeGreaterThan(0);
      });

      it('应该为错误答案返回0分', () => {
        const question = new Question({
          difficulty: 'easy',
          base_score: 100
        });

        const score = question.calculateScore(false, 10);
        expect(score).toBe(0);
      });
    });
  });

  describe('GameResult Model', () => {
    describe('create', () => {
      it('应该创建游戏结果记录', async () => {
        const resultData = {
          user_id: 123,
          question_id: 1,
          user_answer: 2,
          is_correct: true,
          score: 150,
          time_spent: 8
        };

        mockConnection.execute.mockResolvedValue([{ insertId: 456 }]);
        mockConnection.query.mockResolvedValue([[{
          id: 456,
          ...resultData,
          created_at: new Date()
        }]]);

        const result = await GameResult.create(resultData);

        expect(result).toBeDefined();
        expect(result.id).toBe(456);
        expect(result.score).toBe(150);
        expect(result.is_correct).toBe(true);
      });
    });

    describe('findByUser', () => {
      it('应该根据用户ID获取游戏结果', async () => {
        const mockResults = [
          {
            id: 1,
            user_id: 123,
            question_id: 1,
            score: 150,
            is_correct: true,
            created_at: new Date()
          },
          {
            id: 2,
            user_id: 123,
            question_id: 2,
            score: 0,
            is_correct: false,
            created_at: new Date()
          }
        ];

        mockConnection.query.mockResolvedValue([mockResults]);

        const results = await GameResult.findByUser(123, 10, 0);

        expect(results).toHaveLength(2);
        expect(results[0].user_id).toBe(123);
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('WHERE user_id = ?'),
          [123, 10, 0]
        );
      });
    });

    describe('getUserStats', () => {
      it('应该获取用户游戏统计', async () => {
        const mockStats = {
          total_games: 25,
          total_score: 5000,
          correct_answers: 18,
          average_time: 12.5
        };

        mockConnection.query.mockResolvedValue([[mockStats]]);

        const stats = await GameResult.getUserStats(123);

        expect(stats).toBeDefined();
        expect(stats.total_games).toBe(25);
        expect(stats.accuracy_rate).toBeCloseTo(0.72);
        expect(typeof stats.average_time).toBe('number');
      });
    });

    describe('getLeaderboard', () => {
      it('应该获取排行榜', async () => {
        const mockLeaderboard = [
          {
            user_id: 123,
            nickname: '玩家A',
            avatar_url: 'https://example.com/avatar1.jpg',
            total_score: 15000,
            games_played: 50,
            rank: 1
          },
          {
            user_id: 124,
            nickname: '玩家B',
            avatar_url: 'https://example.com/avatar2.jpg',
            total_score: 12000,
            games_played: 40,
            rank: 2
          }
        ];

        mockConnection.query.mockResolvedValue([mockLeaderboard]);

        const leaderboard = await GameResult.getLeaderboard('weekly', 20, 0);

        expect(leaderboard).toHaveLength(2);
        expect(leaderboard[0].rank).toBe(1);
        expect(leaderboard[0].total_score).toBe(15000);
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('ORDER BY total_score DESC'),
          [20, 0]
        );
      });
    });
  });

  describe('GameSession Model', () => {
    describe('create', () => {
      it('应该创建游戏会话', async () => {
        const sessionData = {
          user_id: 123,
          session_id: 'session_abc123',
          category: 'sichuan',
          difficulty: 'medium',
          total_questions: 10
        };

        mockConnection.execute.mockResolvedValue([{ insertId: 789 }]);
        mockConnection.query.mockResolvedValue([[{
          id: 789,
          ...sessionData,
          status: 'active',
          current_score: 0,
          answered_questions: 0,
          created_at: new Date()
        }]]);

        const session = await GameSession.create(sessionData);

        expect(session).toBeDefined();
        expect(session.id).toBe(789);
        expect(session.session_id).toBe('session_abc123');
        expect(session.status).toBe('active');
      });
    });

    describe('findBySessionId', () => {
      it('应该根据会话ID找到会话', async () => {
        const mockSession = {
          id: 789,
          user_id: 123,
          session_id: 'session_abc123',
          status: 'active',
          current_score: 500
        };

        mockConnection.query.mockResolvedValue([[mockSession]]);

        const session = await GameSession.findBySessionId('session_abc123');

        expect(session).toBeDefined();
        expect(session.session_id).toBe('session_abc123');
        expect(session.current_score).toBe(500);
      });
    });

    describe('updateScore', () => {
      it('应该更新会话得分', async () => {
        const session = new GameSession({
          id: 789,
          current_score: 500,
          answered_questions: 5
        });

        mockConnection.execute.mockResolvedValue([{ affectedRows: 1 }]);

        await session.updateScore(150);

        expect(mockConnection.execute).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE game_sessions SET'),
          expect.arrayContaining([650, 6, 789]) // 500 + 150, 5 + 1
        );
      });
    });

    describe('complete', () => {
      it('应该完成游戏会话', async () => {
        const session = new GameSession({
          id: 789,
          status: 'active'
        });

        mockConnection.execute.mockResolvedValue([{ affectedRows: 1 }]);

        await session.complete();

        expect(mockConnection.execute).toHaveBeenCalledWith(
          expect.stringContaining('UPDATE game_sessions SET status = ?'),
          expect.arrayContaining(['completed', 789])
        );
      });
    });

    describe('getUserActiveSessions', () => {
      it('应该获取用户活跃会话', async () => {
        const mockSessions = [
          {
            id: 789,
            user_id: 123,
            session_id: 'session_abc123',
            status: 'active',
            current_score: 500
          }
        ];

        mockConnection.query.mockResolvedValue([mockSessions]);

        const sessions = await GameSession.getUserActiveSessions(123);

        expect(sessions).toHaveLength(1);
        expect(sessions[0].status).toBe('active');
        expect(mockConnection.query).toHaveBeenCalledWith(
          expect.stringContaining('WHERE user_id = ? AND status = ?'),
          [123, 'active']
        );
      });
    });
  });

  describe('Model Integration Tests', () => {
    it('应该正确处理JSON字段', () => {
      const question = new Question({
        id: 1,
        options: JSON.stringify(['A', 'B', 'C', 'D'])
      });

      expect(Array.isArray(question.options)).toBe(true);
      expect(question.options).toHaveLength(4);
    });

    it('应该正确格式化日期', () => {
      const user = new User({
        id: 123,
        created_at: new Date('2025-01-01T00:00:00.000Z')
      });

      const json = user.toJSON();
      expect(typeof json.created_at).toBe('string');
      expect(json.created_at).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('应该正确计算统计数据', async () => {
      const mockStats = {
        total_games: 20,
        correct_answers: 15
      };

      mockConnection.query.mockResolvedValue([[mockStats]]);

      const stats = await GameResult.getUserStats(123);
      expect(stats.accuracy_rate).toBeCloseTo(0.75);
    });
  });
});