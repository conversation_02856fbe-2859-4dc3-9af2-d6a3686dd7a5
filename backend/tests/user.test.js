/**
 * User 模块单元测试
 */

const userHandler = require('../serverless/user/handler');
const User = require('../serverless/models/User');
const GameResult = require('../serverless/models/GameResult');

// Mock dependencies
jest.mock('../serverless/models/User');
jest.mock('../serverless/models/GameResult');
jest.mock('../serverless/middleware/auth');
jest.mock('../serverless/middleware/validation');
jest.mock('../serverless/middleware/rateLimit');

describe('User Handler Tests', () => {
  let mockEvent, mockContext;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock event and context
    mockEvent = {
      httpMethod: 'GET',
      path: '/v1/users/user-123',
      pathParameters: { userId: 'user-123' },
      headers: {
        'content-type': 'application/json'
      }
    };

    mockContext = {
      requestId: 'test-request-123',
      functionName: 'user-handler'
    };

    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { authMiddleware, ownershipCheck } = require('../serverless/middleware/auth');
    const { validateRequest } = require('../serverless/middleware/validation');
    
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));
    ownershipCheck.mockReturnValue(() => Promise.resolve({}));
    validateRequest.mockImplementation(() => ({ userId: 'user-123' }));
    
    // Mock event user
    mockEvent.user = { id: 'user-123' };
    mockEvent.scopes = ['user:read'];
  });

  describe('getUserProfile', () => {
    it('应该成功获取用户基本信息', async () => {
      const mockUser = {
        id: 'user-123',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        level: 5,
        experience: 1200,
        totalGames: 50,
        winRate: 0.78,
        bestScore: 950,
        createdAt: '2025-01-01T00:00:00.000Z',
        toJSON: jest.fn().mockReturnValue({
          id: 'user-123',
          nickname: '测试用户',
          avatar: 'https://example.com/avatar.jpg',
          level: 5,
          experience: 1200,
          totalGames: 50,
          winRate: 0.78,
          bestScore: 950,
          createdAt: '2025-01-01T00:00:00.000Z'
        })
      };

      User.findById.mockResolvedValue(mockUser);

      const result = await userHandler.getUserProfile(mockEvent, mockContext);

      expect(result.user.id).toBe('user-123');
      expect(result.user.nickname).toBe('测试用户');
      expect(result.user.level).toBe(5);
      expect(result.user.totalGames).toBe(50);

      expect(User.findById).toHaveBeenCalledWith('user-123');
      expect(mockUser.toJSON).toHaveBeenCalledWith(true); // isOwner = true
    });

    it('应该处理用户不存在的情况', async () => {
      User.findById.mockResolvedValue(null);

      await expect(userHandler.getUserProfile(mockEvent, mockContext))
        .rejects
        .toThrow('用户不存在');
    });

    it('应该处理数据库查询错误', async () => {
      User.findById.mockRejectedValue(new Error('数据库连接失败'));

      await expect(userHandler.getUserProfile(mockEvent, mockContext))
        .rejects
        .toThrow('获取用户信息失败');
    });
  });

  describe('updateUserProfile', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'PUT';
      mockEvent.path = '/v1/users/user-123';
      mockEvent.body = JSON.stringify({
        nickname: '新昵称',
        avatar: 'https://example.com/new-avatar.jpg'
      });
      mockEvent.user = { id: 'user-123' }; // 当前登录用户

      // Mock validation middleware
      const { validateRequest } = require('../serverless/middleware/validation');
      validateRequest.mockImplementation(() => ({
        nickname: '新昵称',
        avatar: 'https://example.com/new-avatar.jpg'
      }));
    });

    it('应该成功更新用户资料', async () => {
      const mockUser = {
        id: 'user-123',
        nickname: '旧昵称',
        avatar: 'https://example.com/old-avatar.jpg'
      };

      const mockUpdatedUser = {
        id: 'user-123',
        nickname: '新昵称',
        avatar: 'https://example.com/new-avatar.jpg',
        updatedAt: '2025-07-31T01:00:00.000Z',
        toJSON: jest.fn().mockReturnValue({
          id: 'user-123',
          nickname: '新昵称',
          avatar: 'https://example.com/new-avatar.jpg',
          updatedAt: '2025-07-31T01:00:00.000Z'
        })
      };

      User.findById.mockResolvedValue(mockUser);
      mockUser.update = jest.fn().mockResolvedValue(true);
      // After update, return updated user when findById is called again
      User.findById.mockResolvedValueOnce(mockUser).mockResolvedValueOnce(mockUpdatedUser);

      const result = await userHandler.updateUserProfile(mockEvent, mockContext);

      expect(result.user.nickname).toBe('新昵称');
      expect(result.user.avatar).toBe('https://example.com/new-avatar.jpg');

      expect(mockUser.update).toHaveBeenCalledWith({
        nickname: '新昵称',
        avatar: 'https://example.com/new-avatar.jpg'
      });
    });

    it('应该拒绝更新其他用户的资料', async () => {
      mockEvent.pathParameters.userId = 'other-user-123';

      await expect(userHandler.updateUserProfile(mockEvent, mockContext))
        .rejects
        .toThrow('无权限修改其他用户资料');
    });

    it('应该处理用户不存在的情况', async () => {
      User.findById.mockResolvedValue(null);

      await expect(userHandler.updateUserProfile(mockEvent, mockContext))
        .rejects
        .toThrow('用户不存在');
    });
  });

  describe('getUserGameStats', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/users/user-123/stats';
      mockEvent.pathParameters = { userId: 'user-123' };
    });

    it('应该成功获取用户游戏统计', async () => {
      const mockStats = {
        totalGames: 100,
        winGames: 75,
        winRate: 0.75,
        averageScore: 820,
        bestScore: 980,
        totalPlayTime: 7200, // 秒
        favoriteDialect: '四川话',
        levelProgress: {
          currentLevel: 8,
          currentExp: 2400,
          nextLevelExp: 3000,
          progress: 0.8
        },
        recentGames: [
          {
            sessionId: 'session-1',
            score: 850,
            dialect: '上海话',
            createdAt: '2025-07-30T10:00:00.000Z'
          },
          {
            sessionId: 'session-2', 
            score: 920,
            dialect: '四川话',
            createdAt: '2025-07-30T09:00:00.000Z'
          }
        ]
      };

      const mockUser = {
        id: 'user-123',
        total_score: 8200,
        total_games: 100,
        win_games: 75,
        max_streak: 12,
        current_level: 8,
        getGameStats: jest.fn().mockResolvedValue(mockStats.categoryStats || {}),
        getLevelInfo: jest.fn().mockReturnValue(mockStats.levelInfo)
      };
      
      User.findById.mockResolvedValue(mockUser);

      const result = await userHandler.getUserGameStats(mockEvent, mockContext);

      expect(result.totalGames).toBe(100);
      expect(result.winRate).toBe(0.75);
      expect(result.levelProgress.currentLevel).toBe(8);
      expect(result.recentGames).toHaveLength(2);

      expect(User.findById).toHaveBeenCalledWith('user-123');
      expect(mockUser.getGameStats).toHaveBeenCalledWith(undefined);
    });

    it('应该处理统计数据获取失败', async () => {
      User.getGameStats.mockRejectedValue(new Error('查询统计失败'));

      await expect(userHandler.getUserGameStats(mockEvent, mockContext))
        .rejects
        .toThrow('获取用户统计失败');
    });
  });

  describe('getUserGameHistory', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/users/user-123/games';
      mockEvent.pathParameters = { userId: 'user-123' };
      mockEvent.queryStringParameters = {
        page: '1',
        pageSize: '20'
      };
    });

    it('应该成功获取用户游戏历史', async () => {
      const mockHistory = {
        games: [
          {
            sessionId: 'session-1',
            score: 850,
            correctAnswers: 8,
            totalQuestions: 10,
            dialect: '上海话',
            difficulty: 2,
            playTime: 120,
            createdAt: '2025-07-30T10:00:00.000Z'
          },
          {
            sessionId: 'session-2',
            score: 920,
            correctAnswers: 9,
            totalQuestions: 10,
            dialect: '四川话',
            difficulty: 3,
            playTime: 95,
            createdAt: '2025-07-30T09:00:00.000Z'
          }
        ],
        pagination: {
          page: 1,
          pageSize: 20,
          total: 85,
          totalPages: 5
        }
      };

      const mockUser = {
        id: 'user-123',
        getGameHistory: jest.fn().mockResolvedValue(mockHistory)
      };
      
      User.findById.mockResolvedValue(mockUser);

      const result = await userHandler.getUserGameHistory(mockEvent, mockContext);

      expect(result.games).toHaveLength(2);
      expect(result.pagination.total).toBe(85);
      expect(result.pagination.totalPages).toBe(5);

      expect(mockUser.getGameHistory).toHaveBeenCalledWith({
        category: undefined,
        page: 1,
        size: 20
      });
    });

    it('应该处理分页参数验证', async () => {
      mockEvent.queryStringParameters = {
        page: '0', // 无效页码
        pageSize: '100' // 超过限制
      };

      const mockUser = {
        id: 'user-123',
        getGameHistory: jest.fn().mockResolvedValue({
          games: [],
          pagination: { page: 1, pageSize: 50, total: 0, totalPages: 0 }
        })
      };
      
      User.findById.mockResolvedValue(mockUser);

      await userHandler.getUserGameHistory(mockEvent, mockContext);

      // 应该修正参数：page=1, pageSize=50(最大限制)
      expect(mockUser.getGameHistory).toHaveBeenCalledWith({
        category: undefined,
        page: 1,
        size: 50
      });
    });
  });

  describe('deleteUser', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'DELETE';
      mockEvent.path = '/v1/users/user-123';
      mockEvent.user = { id: 'user-123' }; // 当前登录用户
    });

    it('应该成功删除用户账号', async () => {
      const mockUser = {
        id: 'user-123',
        nickname: '测试用户'
      };

      mockUser.delete = jest.fn().mockResolvedValue(true);
      User.findById.mockResolvedValue(mockUser);

      const result = await userHandler.deleteUser(mockEvent, mockContext);

      expect(result.message).toBe('用户账号删除成功');
      expect(result.deletedAt).toBeDefined();

      expect(mockUser.delete).toHaveBeenCalled();
    });

    it('应该拒绝删除其他用户账号', async () => {
      mockEvent.pathParameters.userId = 'other-user-123';

      await expect(userHandler.deleteUser(mockEvent, mockContext))
        .rejects
        .toThrow('无权限删除其他用户账号');
    });

    it('应该处理用户不存在的情况', async () => {
      User.findById.mockResolvedValue(null);

      await expect(userHandler.deleteUser(mockEvent, mockContext))
        .rejects
        .toThrow('用户不存在');
    });
  });
});

describe('User Handler Performance Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock middleware
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { authMiddleware } = require('../serverless/middleware/auth');
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));
  });

  it('获取用户信息响应时间应该少于50ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'GET',
      pathParameters: { userId: 'user-123' }
    };

    User.findById.mockResolvedValue({
      id: 'user-123',
      toJSON: jest.fn().mockReturnValue({ id: 'user-123' })
    });

    await userHandler.getUserProfile(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(50);
  });

  it('获取用户统计响应时间应该少于100ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'GET',
      pathParameters: { userId: 'user-123' }
    };

    User.getGameStats.mockResolvedValue({
      totalGames: 50,
      winRate: 0.75,
      averageScore: 800
    });

    await userHandler.getUserGameStats(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(100);
  });
});