/**
 * API Handler 测试
 * 测试主要的 serverless 函数处理器
 */

// Mock dependencies
jest.mock('../serverless/utils/database');
jest.mock('../serverless/utils/redis');
jest.mock('../serverless/services/TokenManager');
jest.mock('../serverless/services/WechatAuthService');
jest.mock('../serverless/services/CosService');
jest.mock('../serverless/models/User');
jest.mock('../serverless/models/Question');
jest.mock('../serverless/models/GameResult');

const request = require('supertest');
const express = require('express');

// Create test app
const app = express();
app.use(express.json());

// Mock successful database connection
const mockDB = {
  query: jest.fn(),
  execute: jest.fn(),
  beginTransaction: jest.fn(),
  commit: jest.fn(),
  rollback: jest.fn()
};

require('../serverless/utils/database').getConnection = jest.fn().mockResolvedValue(mockDB);

describe('API Handlers Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Auth Handler', () => {
    let authHandler;

    beforeAll(() => {
      // Mock auth handler
      authHandler = {
        async login(event, context) {
          const { code, encryptedData, iv } = JSON.parse(event.body || '{}');
          
          if (!code) {
            return {
              statusCode: 400,
              body: JSON.stringify({
                error: 'AUTH_CODE_REQUIRED',
                message: '授权码不能为空'
              })
            };
          }

          if (code === 'invalid_code') {
            return {
              statusCode: 401,
              body: JSON.stringify({
                error: 'AUTH_FAILED',
                message: '授权失败'
              })
            };
          }

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                accessToken: 'mock-access-token',
                refreshToken: 'mock-refresh-token',
                user: {
                  id: 123,
                  openid: 'mock-openid',
                  nickname: '测试用户',
                  avatar_url: 'https://example.com/avatar.jpg'
                },
                expiresIn: 3600
              }
            })
          };
        },

        async refresh(event, context) {
          const { refreshToken } = JSON.parse(event.body || '{}');
          
          if (!refreshToken || refreshToken === 'invalid_token') {
            return {
              statusCode: 401,
              body: JSON.stringify({
                error: 'INVALID_REFRESH_TOKEN',
                message: '刷新令牌无效'
              })
            };
          }

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                accessToken: 'new-access-token',
                expiresIn: 3600
              }
            })
          };
        },

        async logout(event, context) {
          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              message: '退出登录成功'
            })
          };
        }
      };
    });

    describe('POST /auth/login', () => {
      it('应该成功登录', async () => {
        const response = await authHandler.login({
          body: JSON.stringify({
            code: 'valid_code',
            encryptedData: 'encrypted_user_data',
            iv: 'init_vector'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('accessToken');
        expect(result.data).toHaveProperty('user');
      });

      it('应该处理缺少授权码', async () => {
        const response = await authHandler.login({
          body: JSON.stringify({})
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(400);
        expect(result.error).toBe('AUTH_CODE_REQUIRED');
      });

      it('应该处理无效授权码', async () => {
        const response = await authHandler.login({
          body: JSON.stringify({
            code: 'invalid_code'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(401);
        expect(result.error).toBe('AUTH_FAILED');
      });
    });

    describe('POST /auth/refresh', () => {
      it('应该成功刷新令牌', async () => {
        const response = await authHandler.refresh({
          body: JSON.stringify({
            refreshToken: 'valid_refresh_token'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('accessToken');
      });

      it('应该处理无效刷新令牌', async () => {
        const response = await authHandler.refresh({
          body: JSON.stringify({
            refreshToken: 'invalid_token'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(401);
        expect(result.error).toBe('INVALID_REFRESH_TOKEN');
      });
    });

    describe('POST /auth/logout', () => {
      it('应该成功退出登录', async () => {
        const response = await authHandler.logout({}, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Game Handler', () => {
    let gameHandler;

    beforeAll(() => {
      gameHandler = {
        async getQuestions(event, context) {
          const { category, difficulty, limit } = event.queryStringParameters || {};
          
          const questions = [
            {
              id: 1,
              category: category || 'sichuan',
              difficulty: difficulty || 'easy',
              audio_url: 'https://example.com/audio1.mp3',
              options: ['选项A', '选项B', '选项C', '选项D'],
              correct_answer: 0,
              region: '四川'
            },
            {
              id: 2,
              category: category || 'sichuan',
              difficulty: difficulty || 'easy',
              audio_url: 'https://example.com/audio2.mp3',
              options: ['选项A', '选项B', '选项C', '选项D'],
              correct_answer: 1,
              region: '四川'
            }
          ];

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                questions: questions.slice(0, parseInt(limit) || 10),
                total: questions.length
              }
            })
          };
        },

        async submitAnswer(event, context) {
          const { questionId, userAnswer, timeSpent } = JSON.parse(event.body || '{}');
          
          if (!questionId || userAnswer === undefined) {
            return {
              statusCode: 400,
              body: JSON.stringify({
                error: 'INVALID_REQUEST',
                message: '题目ID和答案不能为空'
              })
            };
          }

          const isCorrect = userAnswer === 0; // Mock correct answer
          const score = isCorrect ? 100 : 0;

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                questionId,
                isCorrect,
                score,
                correctAnswer: 0,
                timeSpent
              }
            })
          };
        },

        async getLeaderboard(event, context) {
          const { type, period, offset, limit } = event.queryStringParameters || {};
          
          const leaderboard = [
            {
              rank: 1,
              userId: 123,
              nickname: '玩家A',
              avatar_url: 'https://example.com/avatar1.jpg',
              score: 95000,
              gamesPlayed: 150
            },
            {
              rank: 2,
              userId: 124,
              nickname: '玩家B',
              avatar_url: 'https://example.com/avatar2.jpg',
              score: 89000,
              gamesPlayed: 120
            }
          ];

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                leaderboard: leaderboard.slice(0, parseInt(limit) || 20),
                total: leaderboard.length,
                userRank: 5,
                period: period || 'weekly'
              }
            })
          };
        }
      };
    });

    describe('GET /game/questions', () => {
      it('应该返回题目列表', async () => {
        const response = await gameHandler.getQuestions({
          queryStringParameters: {
            category: 'sichuan',
            difficulty: 'easy',
            limit: '5'
          }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data.questions).toHaveLength(2);
        expect(result.data.questions[0]).toHaveProperty('audio_url');
      });

      it('应该处理空参数', async () => {
        const response = await gameHandler.getQuestions({}, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(Array.isArray(result.data.questions)).toBe(true);
      });
    });

    describe('POST /game/submit', () => {
      it('应该成功提交答案', async () => {
        const response = await gameHandler.submitAnswer({
          body: JSON.stringify({
            questionId: 1,
            userAnswer: 0,
            timeSpent: 10
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('isCorrect');
        expect(result.data).toHaveProperty('score');
      });

      it('应该处理错误答案', async () => {
        const response = await gameHandler.submitAnswer({
          body: JSON.stringify({
            questionId: 1,
            userAnswer: 2,
            timeSpent: 15
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.data.isCorrect).toBe(false);
        expect(result.data.score).toBe(0);
      });

      it('应该处理无效请求', async () => {
        const response = await gameHandler.submitAnswer({
          body: JSON.stringify({})
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(400);
        expect(result.error).toBe('INVALID_REQUEST');
      });
    });

    describe('GET /game/leaderboard', () => {
      it('应该返回排行榜', async () => {
        const response = await gameHandler.getLeaderboard({
          queryStringParameters: {
            type: 'total',
            period: 'weekly',
            limit: '10'
          }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(Array.isArray(result.data.leaderboard)).toBe(true);
        expect(result.data.leaderboard[0]).toHaveProperty('rank');
        expect(result.data.leaderboard[0]).toHaveProperty('score');
      });
    });
  });

  describe('User Handler', () => {
    let userHandler;

    beforeAll(() => {
      userHandler = {
        async getProfile(event, context) {
          const userId = event.pathParameters?.userId || '123';
          
          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                id: parseInt(userId),
                nickname: '测试用户',
                avatar_url: 'https://example.com/avatar.jpg',
                totalScore: 5000,
                gamesPlayed: 25,
                bestScore: 980,
                level: 5,
                achievements: ['新手上路', '连胜达人'],
                createdAt: '2025-01-01T00:00:00.000Z'
              }
            })
          };
        },

        async updateProfile(event, context) {
          const { nickname, avatar_url } = JSON.parse(event.body || '{}');
          
          if (!nickname) {
            return {
              statusCode: 400,
              body: JSON.stringify({
                error: 'NICKNAME_REQUIRED',
                message: '昵称不能为空'
              })
            };
          }

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                id: 123,
                nickname,
                avatar_url: avatar_url || 'https://example.com/default-avatar.jpg',
                updatedAt: new Date().toISOString()
              }
            })
          };
        },

        async getUserStats(event, context) {
          const userId = event.pathParameters?.userId || '123';
          
          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                userId: parseInt(userId),
                totalGames: 50,
                totalScore: 15000,
                averageScore: 300,
                bestScore: 980,
                correctRate: 0.75,
                favoriteCategory: 'sichuan',
                categoryStats: {
                  sichuan: { games: 20, score: 6000, accuracy: 0.8 },
                  guangdong: { games: 15, score: 4500, accuracy: 0.7 },
                  shanghai: { games: 15, score: 4500, accuracy: 0.75 }
                }
              }
            })
          };
        }
      };
    });

    describe('GET /user/profile/:userId', () => {
      it('应该返回用户资料', async () => {
        const response = await userHandler.getProfile({
          pathParameters: { userId: '123' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('id');
        expect(result.data).toHaveProperty('nickname');
        expect(result.data).toHaveProperty('totalScore');
      });
    });

    describe('PUT /user/profile', () => {
      it('应该成功更新用户资料', async () => {
        const response = await userHandler.updateProfile({
          body: JSON.stringify({
            nickname: '新昵称',
            avatar_url: 'https://example.com/new-avatar.jpg'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data.nickname).toBe('新昵称');
      });

      it('应该处理缺少昵称', async () => {
        const response = await userHandler.updateProfile({
          body: JSON.stringify({
            avatar_url: 'https://example.com/new-avatar.jpg'
          })
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(400);
        expect(result.error).toBe('NICKNAME_REQUIRED');
      });
    });

    describe('GET /user/stats/:userId', () => {
      it('应该返回用户统计数据', async () => {
        const response = await userHandler.getUserStats({
          pathParameters: { userId: '123' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('totalGames');
        expect(result.data).toHaveProperty('categoryStats');
        expect(typeof result.data.correctRate).toBe('number');
      });
    });
  });

  describe('Audio Handler', () => {
    let audioHandler;

    beforeAll(() => {
      audioHandler = {
        async getAudioResource(event, context) {
          const { resourceId } = event.pathParameters || {};
          
          if (!resourceId) {
            return {
              statusCode: 400,
              body: JSON.stringify({
                error: 'RESOURCE_ID_REQUIRED',
                message: '资源ID不能为空'
              })
            };
          }

          if (resourceId === 'nonexistent') {
            return {
              statusCode: 404,
              body: JSON.stringify({
                error: 'RESOURCE_NOT_FOUND',
                message: '音频资源不存在'
              })
            };
          }

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                resourceId,
                url: `https://cdn.example.com/audio/${resourceId}.mp3`,
                directUrl: `https://cos.example.com/audio/${resourceId}.mp3`,
                size: 1024000,
                contentType: 'audio/mpeg',
                cacheMaxAge: 604800
              }
            })
          };
        },

        async listAudioResources(event, context) {
          const { category, limit, offset } = event.queryStringParameters || {};
          
          const resources = [
            {
              resourceId: 'sichuan_hello',
              url: 'https://cdn.example.com/audio/sichuan_hello.mp3',
              size: 512000,
              category: 'sichuan',
              lastModified: '2025-01-01T00:00:00.000Z'
            },
            {
              resourceId: 'guangdong_thanks',
              url: 'https://cdn.example.com/audio/guangdong_thanks.mp3',
              size: 768000,
              category: 'guangdong',
              lastModified: '2025-01-02T00:00:00.000Z'
            }
          ];

          const filtered = category 
            ? resources.filter(r => r.category === category)
            : resources;

          return {
            statusCode: 200,
            body: JSON.stringify({
              success: true,
              data: {
                resources: filtered.slice(0, parseInt(limit) || 20),
                total: filtered.length,
                category: category || 'all'
              }
            })
          };
        }
      };
    });

    describe('GET /audio/:resourceId', () => {
      it('应该返回音频资源信息', async () => {
        const response = await audioHandler.getAudioResource({
          pathParameters: { resourceId: 'sichuan_hello' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(result.data).toHaveProperty('url');
        expect(result.data).toHaveProperty('size');
        expect(result.data).toHaveProperty('contentType');
      });

      it('应该处理资源不存在', async () => {
        const response = await audioHandler.getAudioResource({
          pathParameters: { resourceId: 'nonexistent' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(404);
        expect(result.error).toBe('RESOURCE_NOT_FOUND');
      });

      it('应该处理缺少资源ID', async () => {
        const response = await audioHandler.getAudioResource({
          pathParameters: {}
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(400);
        expect(result.error).toBe('RESOURCE_ID_REQUIRED');
      });
    });

    describe('GET /audio', () => {
      it('应该返回音频资源列表', async () => {
        const response = await audioHandler.listAudioResources({
          queryStringParameters: { limit: '10' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.success).toBe(true);
        expect(Array.isArray(result.data.resources)).toBe(true);
        expect(result.data.resources.length).toBeLessThanOrEqual(10);
      });

      it('应该按类别筛选', async () => {
        const response = await audioHandler.listAudioResources({
          queryStringParameters: { category: 'sichuan' }
        }, {});

        const result = JSON.parse(response.body);
        expect(response.statusCode).toBe(200);
        expect(result.data.category).toBe('sichuan');
        expect(result.data.resources.every(r => r.category === 'sichuan')).toBe(true);
      });
    });
  });
});