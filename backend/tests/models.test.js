/**
 * 模型层单元测试
 */

const User = require('../serverless/models/User');
const Question = require('../serverless/models/Question');
const GameSession = require('../serverless/models/GameSession');

// Mock数据库连接
jest.mock('../serverless/utils/dev-database.js');

describe('Model Tests', () => {
  describe('User Model', () => {
    it('应该能创建用户对象', () => {
      const userData = {
        id: 'user-123',
        openid: 'wx-openid-123',
        nickname: '测试用户',
        avatar_url: 'https://example.com/avatar.jpg',
        current_level: 1,
        total_games: 0,
        win_games: 0,
        total_score: 0
      };

      const user = new User(userData);

      expect(user.id).toBe('user-123');
      expect(user.nickname).toBe('测试用户');
      expect(user.current_level).toBe(1);
      expect(user.total_games).toBe(0);
    });

    it('应该正确设置默认值', () => {
      const user = new User({
        id: 'user-123',
        nickname: '测试用户'
      });

      expect(user.total_score).toBe(0);
      expect(user.total_games).toBe(0);
      expect(user.win_games).toBe(0);
      expect(user.current_level).toBe(1);
      expect(user.status).toBe(1);
    });
  });

  describe('Question Model', () => {
    it('应该能创建题目对象', () => {
      const questionData = {
        id: 1,
        text: '这句四川话是什么意思？',
        audioUrl: 'https://example.com/audio.mp3',
        options: ['正确答案', '错误选项1', '错误选项2', '错误选项3'],
        correctAnswer: 0,
        difficulty: 2,
        dialect: '四川话',
        category: 'daily',
        hint: '这是一个提示',
        tags: ['四川话', '日常用语']
      };

      const question = new Question(questionData);

      expect(question.id).toBe(1);
      expect(question.text).toBe('这句四川话是什么意思？');
      expect(question.options).toHaveLength(4);
      expect(question.correctAnswer).toBe(0);
      expect(question.difficulty).toBe(2);
      expect(question.dialect).toBe('四川话');
    });

    it('应该能检查答案是否正确', () => {
      const question = new Question({
        id: 1,
        correctAnswer: 2,
        options: ['A', 'B', 'C', 'D']
      });

      expect(question.checkAnswer(2)).toBe(true);
      expect(question.checkAnswer(0)).toBe(false);
      expect(question.checkAnswer(1)).toBe(false);
      expect(question.checkAnswer(3)).toBe(false);
    });

    it('应该能获取公开的题目信息（不包含正确答案）', () => {
      const question = new Question({
        id: 1,
        text: '测试题目',
        options: ['A', 'B', 'C', 'D'],
        correctAnswer: 2,
        hint: '提示信息'
      });

      const publicData = question.toPublicJSON();
      
      expect(publicData).toHaveProperty('id');
      expect(publicData).toHaveProperty('text');
      expect(publicData).toHaveProperty('options');
      expect(publicData).toHaveProperty('hint');
      expect(publicData).not.toHaveProperty('correctAnswer');
    });

    it('应该验证题目数据', () => {
      expect(() => new Question({})).toThrow(); // 缺少必需字段
      expect(() => new Question({ id: 1, options: ['A', 'B'] })).toThrow(); // 选项不足4个
      expect(() => new Question({ 
        id: 1, 
        options: ['A', 'B', 'C', 'D'], 
        correctAnswer: 5 
      })).toThrow(); // 正确答案索引超出范围
    });
  });

  describe('GameSession Model', () => {
    it('应该能创建游戏会话对象', () => {
      const questions = [
        { id: 1, text: '题目1' },
        { id: 2, text: '题目2' },
        { id: 3, text: '题目3' }
      ];

      const sessionData = {
        sessionId: 'session-123',
        userId: 'user-123',
        questions: questions,
        currentQuestion: 0,
        score: 0,
        correctAnswers: 0,
        status: 'active',
        startTime: new Date(),
        totalTime: 0
      };

      const session = new GameSession(sessionData);

      expect(session.sessionId).toBe('session-123');
      expect(session.userId).toBe('user-123');
      expect(session.questions).toHaveLength(3);
      expect(session.currentQuestion).toBe(0);
      expect(session.status).toBe('active');
    });

    it('应该能获取当前题目', () => {
      const questions = [
        { id: 1, text: '题目1' },
        { id: 2, text: '题目2' },
        { id: 3, text: '题目3' }
      ];

      const session = new GameSession({
        sessionId: 'session-123',
        questions: questions,
        currentQuestion: 1
      });

      const currentQuestion = session.getCurrentQuestion();
      expect(currentQuestion.id).toBe(2);
      expect(currentQuestion.text).toBe('题目2');
    });

    it('应该能计算游戏进度', () => {
      const session = new GameSession({
        sessionId: 'session-123',
        questions: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }],
        currentQuestion: 2
      });

      const progress = session.getProgress();
      expect(progress.current).toBe(2);
      expect(progress.total).toBe(5);
      expect(progress.percentage).toBe(40);
    });

    it('应该能检查游戏是否结束', () => {
      const session = new GameSession({
        sessionId: 'session-123',
        questions: [{ id: 1 }, { id: 2 }, { id: 3 }],
        currentQuestion: 2
      });

      expect(session.isCompleted()).toBe(false);

      session.currentQuestion = 3;
      expect(session.isCompleted()).toBe(true);
    });

    it('应该能计算得分和准确率', () => {
      const session = new GameSession({
        sessionId: 'session-123',
        questions: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }],
        correctAnswers: 4,
        score: 800
      });

      expect(session.getAccuracy()).toBe(0.8); // 4/5 = 0.8
      expect(session.score).toBe(800);
    });

    it('应该验证游戏会话数据', () => {
      expect(() => new GameSession({})).toThrow(); // 缺少必需字段
      expect(() => new GameSession({ 
        sessionId: 'session-123',
        questions: []
      })).toThrow(); // 没有题目
      expect(() => new GameSession({
        sessionId: '',
        questions: [{ id: 1 }]
      })).toThrow(); // 空的会话ID
    });
  });
});

describe('Model Integration Tests', () => {
  it('用户和游戏会话应该能关联', () => {
    const user = new User({
      id: 'user-123',
      nickname: '测试用户',
      totalGames: 10,
      bestScore: 950
    });

    const session = new GameSession({
      sessionId: 'session-123',
      userId: 'user-123',
      questions: [{ id: 1 }],
      score: 980
    });

    expect(session.userId).toBe(user.id);
    expect(session.score).toBeGreaterThan(user.bestScore);
  });

  it('题目和游戏会话应该能关联', () => {
    const questions = [
      new Question({ id: 1, correctAnswer: 0, options: ['A', 'B', 'C', 'D'] }),
      new Question({ id: 2, correctAnswer: 1, options: ['A', 'B', 'C', 'D'] }),
      new Question({ id: 3, correctAnswer: 2, options: ['A', 'B', 'C', 'D'] })
    ];

    const session = new GameSession({
      sessionId: 'session-123',
      userId: 'user-123',
      questions: questions.map(q => q.toPublicJSON()),
      currentQuestion: 0
    });

    expect(session.questions).toHaveLength(3);
    expect(session.getCurrentQuestion().id).toBe(1);
    
    // 检查题目不包含正确答案
    expect(session.getCurrentQuestion()).not.toHaveProperty('correctAnswer');
  });
});

describe('Model Performance Tests', () => {
  it('用户对象创建应该在1ms内完成', () => {
    const startTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      new User({
        id: `user-${i}`,
        nickname: `用户${i}`,
        level: 1
      });
    }
    
    const endTime = Date.now();
    expect(endTime - startTime).toBeLessThan(50); // 1000个对象创建在50ms内
  });

  it('题目对象toPublicJSON应该在1ms内完成', () => {
    const question = new Question({
      id: 1,
      text: '测试题目',
      options: ['A', 'B', 'C', 'D'],
      correctAnswer: 0,
      hint: '提示'
    });

    const startTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      question.toPublicJSON();
    }
    
    const endTime = Date.now();
    expect(endTime - startTime).toBeLessThan(10); // 1000次调用在10ms内
  });
});