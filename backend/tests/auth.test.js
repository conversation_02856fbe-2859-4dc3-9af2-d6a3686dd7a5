/**
 * Auth 模块单元测试
 */

const {
  wechatLogin,
  refreshToken,
  logout,
  getCurrentUser,
  verifyToken
} = require('../serverless/auth/handler');

const wechatAuthService = require('../serverless/services/WechatAuthService');
const { APIError } = require('../serverless/utils/errors');

// Mock dependencies
jest.mock('../serverless/services/WechatAuthService');
jest.mock('../serverless/middleware/auth');
jest.mock('../serverless/middleware/validation');
jest.mock('../serverless/middleware/rateLimit');

describe('Auth Handler Tests', () => {
  let mockEvent, mockContext;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock event and context
    mockEvent = {
      httpMethod: 'POST',
      path: '/v1/auth/wechat/login',
      headers: {
        'content-type': 'application/json',
        'x-device-id': 'test-device-123'
      },
      body: JSON.stringify({
        code: 'test-wechat-code',
        encryptedData: 'test-encrypted-data',
        iv: 'test-iv'
      })
    };

    mockContext = {
      requestId: 'test-request-123',
      functionName: 'auth-handler'
    };

    // Mock validation middleware
    const { validateRequest } = require('../serverless/middleware/validation');
    validateRequest.mockReturnValue(() => ({
      code: 'test-wechat-code',
      encryptedData: 'test-encrypted-data',
      iv: 'test-iv'
    }));

    // Mock rate limit middleware
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
  });

  describe('wechatLogin', () => {
    it('应该成功处理微信登录', async () => {
      // Mock service response
      const mockLoginResult = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 7200,
        user: {
          id: 'user-123',
          nickname: '测试用户',
          avatar: 'https://example.com/avatar.jpg'
        },
        isNewUser: false
      };

      wechatAuthService.login.mockResolvedValue(mockLoginResult);

      const result = await wechatLogin(mockEvent, mockContext);

      expect(result).toEqual({
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 7200,
        tokenType: 'Bearer',
        user: mockLoginResult.user,
        isNewUser: false
      });

      expect(wechatAuthService.login).toHaveBeenCalledWith(
        'test-wechat-code',
        'test-encrypted-data',
        'test-iv',
        'test-device-123'
      );
    });

    it('应该处理无效的微信授权码错误', async () => {
      wechatAuthService.login.mockRejectedValue(
        new Error('无效的code')
      );

      await expect(wechatLogin(mockEvent, mockContext))
        .rejects
        .toThrow('微信授权码无效或已过期');
    });

    it('应该处理微信API频率限制错误', async () => {
      wechatAuthService.login.mockRejectedValue(
        new Error('API调用太频繁')
      );

      await expect(wechatLogin(mockEvent, mockContext))
        .rejects
        .toThrow('微信API调用频率限制');
    });

    it('应该处理通用登录失败错误', async () => {
      wechatAuthService.login.mockRejectedValue(
        new Error('网络连接失败')
      );

      await expect(wechatLogin(mockEvent, mockContext))
        .rejects
        .toThrow('微信登录失败，请重试');
    });
  });

  describe('refreshToken', () => {
    beforeEach(() => {
      mockEvent.body = JSON.stringify({
        refreshToken: 'test-refresh-token'
      });

      const { validateRequest } = require('../serverless/middleware/validation');
      validateRequest.mockReturnValue(() => ({
        refreshToken: 'test-refresh-token'
      }));
    });

    it('应该成功刷新访问令牌', async () => {
      const mockRefreshResult = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 7200,
        user: {
          id: 'user-123',
          nickname: '测试用户'
        }
      };

      wechatAuthService.refreshToken.mockResolvedValue(mockRefreshResult);

      const result = await refreshToken(mockEvent, mockContext);

      expect(result).toEqual({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 7200,
        tokenType: 'Bearer',
        user: mockRefreshResult.user
      });

      expect(wechatAuthService.refreshToken).toHaveBeenCalledWith('test-refresh-token');
    });

    it('应该处理过期的刷新令牌', async () => {
      wechatAuthService.refreshToken.mockRejectedValue(
        new Error('refresh token expired')
      );

      await expect(refreshToken(mockEvent, mockContext))
        .rejects
        .toThrow('刷新令牌已过期，请重新登录');
    });

    it('应该处理无效的刷新令牌', async () => {
      wechatAuthService.refreshToken.mockRejectedValue(
        new Error('invalid refresh token')
      );

      await expect(refreshToken(mockEvent, mockContext))
        .rejects
        .toThrow('无效的刷新令牌');
    });
  });

  describe('getCurrentUser', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/auth/me';
      mockEvent.user = {
        id: 'user-123',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        toJSON: jest.fn().mockReturnValue({
          id: 'user-123',
          nickname: '测试用户',
          avatar: 'https://example.com/avatar.jpg',
          phone: '13812345678'
        })
      };
      mockEvent.scopes = ['user:read'];
      mockEvent.tokenPayload = {
        sessionId: 'session-123'
      };

      // Mock auth middleware
      const { authMiddleware } = require('../serverless/middleware/auth');
      authMiddleware.mockReturnValue(() => Promise.resolve({}));
    });

    it('应该成功获取当前用户信息', async () => {
      const result = await getCurrentUser(mockEvent, mockContext);

      expect(result).toEqual({
        user: {
          id: 'user-123',
          nickname: '测试用户',
          avatar: 'https://example.com/avatar.jpg',
          phone: '13812345678'
        },
        scopes: ['user:read'],
        sessionId: 'session-123'
      });

      expect(mockEvent.user.toJSON).toHaveBeenCalledWith(true);
    });
  });

  describe('verifyToken', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/auth/verify';
      mockEvent.tokenPayload = {
        sub: 'user-123',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
        iat: Math.floor(Date.now() / 1000),
        scope: 'user:read user:write'
      };

      // Mock auth middleware
      const { authMiddleware } = require('../serverless/middleware/auth');
      authMiddleware.mockReturnValue(() => Promise.resolve({}));
    });

    it('应该成功验证Token有效性', async () => {
      const result = await verifyToken(mockEvent, mockContext);

      expect(result.valid).toBe(true);
      expect(result.userId).toBe('user-123');
      expect(result.expiresAt).toBe(mockEvent.tokenPayload.exp);
      expect(result.issuedAt).toBe(mockEvent.tokenPayload.iat);
      expect(result.ttl).toBeGreaterThan(3500); // 应该接近3600秒
      expect(result.scopes).toEqual(['user:read', 'user:write']);
    });
  });

  describe('logout', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'POST';
      mockEvent.path = '/v1/auth/logout';
      mockEvent.user = { id: 'user-123' };

      // Mock auth middleware
      const { authMiddleware } = require('../serverless/middleware/auth');
      authMiddleware.mockReturnValue(() => Promise.resolve({}));
    });

    it('应该成功处理用户退出登录', async () => {
      wechatAuthService.logout.mockResolvedValue();

      const result = await logout(mockEvent, mockContext);

      expect(result).toEqual({
        message: '退出登录成功'
      });

      expect(wechatAuthService.logout).toHaveBeenCalledWith(
        'user-123',
        'test-device-123'
      );
    });

    it('应该处理退出登录失败', async () => {
      wechatAuthService.logout.mockRejectedValue(
        new Error('数据库连接失败')
      );

      await expect(logout(mockEvent, mockContext))
        .rejects
        .toThrow('退出登录失败');
    });
  });
});

describe('Auth Handler Integration Tests', () => {
  describe('性能测试', () => {
    it('微信登录响应时间应该少于100ms', async () => {
      const startTime = Date.now();
      
      const mockEvent = {
        httpMethod: 'POST',
        path: '/v1/auth/wechat/login',
        headers: { 'x-device-id': 'test-device' },
        body: JSON.stringify({
          code: 'test-code',
          encryptedData: 'test-data',
          iv: 'test-iv'
        })
      };

      // Mock 快速响应
      wechatAuthService.login.mockResolvedValue({
        accessToken: 'token',
        refreshToken: 'refresh',
        expiresIn: 7200,
        user: { id: 'user-123' },
        isNewUser: false
      });

      await wechatLogin(mockEvent, {});
      
      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(100);
    });
  });

  describe('错误处理测试', () => {
    it('应该正确处理APIError', async () => {
      const mockEvent = {
        body: JSON.stringify({ refreshToken: 'invalid-token' })
      };

      wechatAuthService.refreshToken.mockRejectedValue(
        new APIError('INVALID_TOKEN', '无效的令牌', null, 400)
      );

      await expect(refreshToken(mockEvent, {}))
        .rejects
        .toThrow(APIError);
    });
  });
});