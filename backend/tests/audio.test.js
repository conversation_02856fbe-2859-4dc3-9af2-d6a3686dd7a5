/**
 * Audio 模块单元测试
 */

const audioHandler = require('../serverless/audio/handler');
const { getCosService } = require('../serverless/services/CosService');
const redisClient = require('../serverless/utils/redis');

// Mock dependencies
jest.mock('../serverless/services/CosService');
jest.mock('../serverless/utils/redis');
jest.mock('../serverless/middleware/auth');
jest.mock('../serverless/middleware/validation');
jest.mock('../serverless/middleware/rateLimit');

describe('Audio Handler Tests', () => {
  let mockEvent, mockContext, mockCosService, mockRedis;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock event and context
    mockEvent = {
      httpMethod: 'GET',
      path: '/v1/audio/test-audio-123',
      pathParameters: { resourceId: 'test-audio-123' },
      headers: {
        'content-type': 'application/json'
      },
      queryStringParameters: {}
    };

    mockContext = {
      requestId: 'test-request-123',
      functionName: 'audio-handler'
    };

    // Mock COS service
    mockCosService = {
      getAudioResource: jest.fn(),
      listAudioResources: jest.fn(),
      getStorageStats: jest.fn(),
      uploadAudio: jest.fn(),
      deleteAudio: jest.fn()
    };
    getCosService.mockReturnValue(mockCosService);

    // Mock Redis
    mockRedis = {
      get: jest.fn(),
      setex: jest.fn(),
      del: jest.fn(),
      connect: jest.fn(),
      disconnect: jest.fn()
    };
    
    // Mock the redis client methods
    redisClient.get = mockRedis.get;
    redisClient.setex = mockRedis.setex;
    redisClient.del = mockRedis.del;

    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { authMiddleware, optionalAuth } = require('../serverless/middleware/auth');
    
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));
    optionalAuth.mockReturnValue(() => Promise.resolve({}));
  });

  describe('getAudioResource', () => {
    it('应该成功获取音频资源（缓存未命中）', async () => {
      const mockResource = {
        id: 'test-audio-123',
        url: 'https://example.com/audio/test.mp3',
        duration: 30,
        size: 1024000,
        format: 'mp3',
        category: 'dialect',
        createdAt: '2025-07-31T01:00:00.000Z'
      };

      mockRedis.get.mockResolvedValue(null); // 缓存未命中
      mockCosService.getAudioResource.mockResolvedValue(mockResource);
      mockRedis.setex.mockResolvedValue('OK');

      const result = await audioHandler.getAudioResource(mockEvent, mockContext);

      expect(result.resource.id).toBe('test-audio-123');
      expect(result.cached).toBe(false);
      expect(mockCosService.getAudioResource).toHaveBeenCalledWith('test-audio-123');
      expect(mockRedis.setex).toHaveBeenCalledWith(
        'audio:resource:test-audio-123',
        24 * 60 * 60,
        expect.any(String)
      );
    });

    it('应该成功获取音频资源（缓存命中）', async () => {
      const cachedResource = {
        id: 'test-audio-123',
        url: 'https://example.com/audio/test.mp3',
        cachedAt: new Date().toISOString() // 刚刚缓存的
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(cachedResource));

      const result = await audioHandler.getAudioResource(mockEvent, mockContext);

      expect(result.resource.id).toBe('test-audio-123');
      expect(result.cached).toBe(true);
      expect(result.cacheAge).toBeLessThan(1); // 小于1小时
      expect(mockCosService.getAudioResource).not.toHaveBeenCalled();
    });

    it('应该处理无效的资源ID', async () => {
      mockEvent.pathParameters.resourceId = 'invalid@id!';

      await expect(audioHandler.getAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('音频资源ID格式无效');
    });

    it('应该处理资源不存在的情况', async () => {
      mockRedis.get.mockResolvedValue(null);
      mockCosService.getAudioResource.mockRejectedValue(new Error('AUDIO_NOT_FOUND'));

      await expect(audioHandler.getAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('音频资源不存在');
    });

    it('应该处理缓存过期的情况', async () => {
      const expiredResource = {
        id: 'test-audio-123',
        cachedAt: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString() // 25小时前
      };

      const freshResource = {
        id: 'test-audio-123',
        url: 'https://example.com/audio/test.mp3'
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(expiredResource));
      mockCosService.getAudioResource.mockResolvedValue(freshResource);

      const result = await audioHandler.getAudioResource(mockEvent, mockContext);

      expect(result.cached).toBe(false);
      expect(mockCosService.getAudioResource).toHaveBeenCalled();
    });
  });

  describe('getAudioResources', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/audio';
      mockEvent.pathParameters = {};
      mockEvent.queryStringParameters = {
        category: 'dialect',
        limit: '20'
      };
    });

    it('应该成功获取音频资源列表', async () => {
      const mockResources = [
        {
          id: 'audio-1',
          url: 'https://example.com/audio1.mp3',
          category: 'dialect'
        },
        {
          id: 'audio-2',
          url: 'https://example.com/audio2.mp3',
          category: 'dialect'
        }
      ];

      mockRedis.get.mockResolvedValue(null); // 缓存未命中
      mockCosService.listAudioResources.mockResolvedValue(mockResources);

      const result = await audioHandler.getAudioResources(mockEvent, mockContext);

      expect(result.resources).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.category).toBe('dialect');
      expect(result.cached).toBe(false);
      expect(mockCosService.listAudioResources).toHaveBeenCalledWith('dialect', 20);
    });

    it('应该限制列表数量在1-100范围内', async () => {
      mockEvent.queryStringParameters = { limit: '200' };
      
      mockRedis.get.mockResolvedValue(null);
      mockCosService.listAudioResources.mockResolvedValue([]);

      await audioHandler.getAudioResources(mockEvent, mockContext);

      // 应该被限制为100
      expect(mockCosService.listAudioResources).toHaveBeenCalledWith(undefined, 100);
    });

    it('应该处理无效的limit参数', async () => {
      mockEvent.queryStringParameters = { limit: 'invalid' };
      
      mockRedis.get.mockResolvedValue(null);
      mockCosService.listAudioResources.mockResolvedValue([]);

      await audioHandler.getAudioResources(mockEvent, mockContext);

      // 应该使用默认值1
      expect(mockCosService.listAudioResources).toHaveBeenCalledWith(undefined, 1);
    });

    it('应该从缓存返回结果', async () => {
      const cachedData = {
        resources: [{ id: 'audio-1' }],
        total: 1,
        cachedAt: new Date().toISOString()
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(cachedData));

      const result = await audioHandler.getAudioResources(mockEvent, mockContext);

      expect(result.cached).toBe(true);
      expect(result.resources).toHaveLength(1);
      expect(mockCosService.listAudioResources).not.toHaveBeenCalled();
    });
  });

  describe('getAudioStats', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'GET';
      mockEvent.path = '/v1/audio/stats';
      mockEvent.user = { id: 'admin-123' };
      mockEvent.scopes = ['admin:read'];
    });

    it('应该成功获取音频统计信息', async () => {
      const mockStats = {
        totalFiles: 1500,
        totalSize: 50 * 1024 * 1024 * 1024, // 50GB
        categories: {
          'dialect': 800,
          'music': 400,
          'sound': 300
        },
        averageFileSize: 33 * 1024 * 1024, // 33MB
        lastUpdated: '2025-07-31T01:00:00.000Z'
      };

      mockRedis.get.mockResolvedValue(null);
      mockCosService.getStorageStats.mockResolvedValue(mockStats);

      const result = await audioHandler.getAudioStats(mockEvent, mockContext);

      expect(result.stats.totalFiles).toBe(1500);
      expect(result.stats.categories.dialect).toBe(800);
      expect(result.cached).toBe(false);
      expect(mockCosService.getStorageStats).toHaveBeenCalled();
    });

    it('应该从缓存返回统计信息', async () => {
      const cachedStats = {
        totalFiles: 1500,
        cachedAt: new Date().toISOString()
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(cachedStats));

      const result = await audioHandler.getAudioStats(mockEvent, mockContext);

      expect(result.cached).toBe(true);
      expect(result.stats.totalFiles).toBe(1500);
      expect(mockCosService.getStorageStats).not.toHaveBeenCalled();
    });

    it('应该处理过期缓存', async () => {
      const expiredStats = {
        totalFiles: 1400,
        cachedAt: new Date(Date.now() - 7 * 60 * 60 * 1000).toISOString() // 7小时前
      };

      const freshStats = {
        totalFiles: 1500
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(expiredStats));
      mockCosService.getStorageStats.mockResolvedValue(freshStats);

      const result = await audioHandler.getAudioStats(mockEvent, mockContext);

      expect(result.cached).toBe(false);
      expect(result.stats.totalFiles).toBe(1500);
      expect(mockCosService.getStorageStats).toHaveBeenCalled();
    });
  });

  describe('uploadAudioResource', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'POST';
      mockEvent.path = '/v1/audio/new-audio-123';
      mockEvent.pathParameters = { resourceId: 'new-audio-123' };
      mockEvent.headers = {
        'content-type': 'audio/mpeg'
      };
      mockEvent.body = Buffer.from('fake audio data').toString('base64');
      mockEvent.isBase64Encoded = true;
      mockEvent.user = { id: 'admin-123' };
      mockEvent.scopes = ['admin:write'];
    });

    it('应该成功上传音频资源', async () => {
      const mockResult = {
        id: 'new-audio-123',
        url: 'https://example.com/audio/new-audio-123.mp3',
        size: 1024
      };

      mockCosService.uploadAudio.mockResolvedValue(mockResult);
      mockRedis.del.mockResolvedValue(1);

      const result = await audioHandler.uploadAudioResource(mockEvent, mockContext);

      expect(result.message).toBe('音频资源上传成功');
      expect(result.resource.id).toBe('new-audio-123');
      expect(mockCosService.uploadAudio).toHaveBeenCalledWith(
        'new-audio-123',
        expect.any(Buffer),
        'audio/mpeg'
      );
      expect(mockRedis.del).toHaveBeenCalledTimes(4); // 清除各种缓存
    });

    it('应该拒绝非音频文件', async () => {
      mockEvent.headers['content-type'] = 'image/jpeg';

      await expect(audioHandler.uploadAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('文件类型必须是音频格式');
    });

    it('应该拒绝过大的文件', async () => {
      // 创建一个超过10MB的文件
      const largeBuffer = Buffer.alloc(11 * 1024 * 1024, 'x');
      mockEvent.body = largeBuffer.toString('base64');

      await expect(audioHandler.uploadAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('音频文件大小不能超过10MB');
    });

    it('应该处理无效的资源ID', async () => {
      mockEvent.pathParameters.resourceId = 'invalid@id!';

      await expect(audioHandler.uploadAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('音频资源ID格式无效');
    });

    it('应该处理二进制上传', async () => {
      mockEvent.isBase64Encoded = false;
      mockEvent.body = Buffer.from('fake audio data').toString('binary');

      const mockResult = {
        id: 'new-audio-123',
        url: 'https://example.com/audio/new-audio-123.mp3'
      };

      mockCosService.uploadAudio.mockResolvedValue(mockResult);

      const result = await audioHandler.uploadAudioResource(mockEvent, mockContext);

      expect(result.message).toBe('音频资源上传成功');
      expect(mockCosService.uploadAudio).toHaveBeenCalled();
    });
  });

  describe('deleteAudioResource', () => {
    beforeEach(() => {
      mockEvent.httpMethod = 'DELETE';
      mockEvent.path = '/v1/audio/delete-audio-123';
      mockEvent.pathParameters = { resourceId: 'delete-audio-123' };
      mockEvent.user = { id: 'admin-123' };
      mockEvent.scopes = ['admin:write'];
    });

    it('应该成功删除音频资源', async () => {
      const mockResult = {
        id: 'delete-audio-123',
        deleted: true
      };

      mockCosService.deleteAudio.mockResolvedValue(mockResult);
      mockRedis.del.mockResolvedValue(1);

      const result = await audioHandler.deleteAudioResource(mockEvent, mockContext);

      expect(result.message).toBe('音频资源删除成功');
      expect(result.resource.deleted).toBe(true);
      expect(mockCosService.deleteAudio).toHaveBeenCalledWith('delete-audio-123');
      expect(mockRedis.del).toHaveBeenCalledTimes(4); // 清除各种缓存
    });

    it('应该处理删除失败', async () => {
      mockCosService.deleteAudio.mockRejectedValue(new Error('COS delete failed'));

      await expect(audioHandler.deleteAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('删除音频资源失败');
    });

    it('应该处理缺少资源ID', async () => {
      mockEvent.pathParameters = {};

      await expect(audioHandler.deleteAudioResource(mockEvent, mockContext))
        .rejects
        .toThrow('音频资源ID不能为空');
    });
  });
});

describe('Audio Handler Performance Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { authMiddleware, optionalAuth } = require('../serverless/middleware/auth');
    
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));
    optionalAuth.mockReturnValue(() => Promise.resolve({}));

    // Mock Redis and COS
    const mockRedis = {
      get: jest.fn().mockResolvedValue(null),
      setex: jest.fn().mockResolvedValue('OK')
    };
    RedisManager.getInstance.mockReturnValue(mockRedis);

    const mockCosService = {
      getAudioResource: jest.fn().mockResolvedValue({ id: 'test' }),
      listAudioResources: jest.fn().mockResolvedValue([])
    };
    getCosService.mockReturnValue(mockCosService);
  });

  it('获取音频资源响应时间应该少于100ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'GET',
      pathParameters: { resourceId: 'test-audio-123' }
    };

    await audioHandler.getAudioResource(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(100);
  });

  it('获取音频列表响应时间应该少于200ms', async () => {
    const startTime = Date.now();
    
    const mockEvent = {
      httpMethod: 'GET',
      queryStringParameters: { limit: '50' }
    };

    await audioHandler.getAudioResources(mockEvent, {});
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(200);
  });
});

describe('Audio Handler Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  it('应该正确处理缓存失效和重新获取', async () => {
    const mockRedis = {
      get: jest.fn(),
      setex: jest.fn().mockResolvedValue('OK')
    };
    RedisManager.getInstance.mockReturnValue(mockRedis);

    const mockCosService = {
      getAudioResource: jest.fn()
    };
    getCosService.mockReturnValue(mockCosService);

    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));

    // 第一次调用 - 缓存未命中
    mockRedis.get.mockResolvedValueOnce(null);
    mockCosService.getAudioResource.mockResolvedValueOnce({
      id: 'test-audio',
      url: 'https://example.com/test.mp3'
    });

    const mockEvent = {
      pathParameters: { resourceId: 'test-audio' }
    };

    const result1 = await audioHandler.getAudioResource(mockEvent, {});
    expect(result1.cached).toBe(false);
    expect(mockCosService.getAudioResource).toHaveBeenCalledTimes(1);

    // 第二次调用 - 缓存命中
    mockRedis.get.mockResolvedValueOnce(JSON.stringify({
      id: 'test-audio',
      url: 'https://example.com/test.mp3',
      cachedAt: new Date().toISOString()
    }));

    const result2 = await audioHandler.getAudioResource(mockEvent, {});
    expect(result2.cached).toBe(true);
    expect(mockCosService.getAudioResource).toHaveBeenCalledTimes(1); // 没有再次调用
  });

  it('应该正确处理批量缓存清除', async () => {
    const mockRedis = {
      del: jest.fn().mockResolvedValue(1)
    };
    RedisManager.getInstance.mockReturnValue(mockRedis);

    const mockCosService = {
      uploadAudio: jest.fn().mockResolvedValue({ id: 'dialect_test_123' })
    };
    getCosService.mockReturnValue(mockCosService);

    // Mock middlewares
    const { rateLimitMiddleware } = require('../serverless/middleware/rateLimit');
    const { authMiddleware } = require('../serverless/middleware/auth');
    
    rateLimitMiddleware.mockReturnValue(() => Promise.resolve({}));
    authMiddleware.mockReturnValue(() => Promise.resolve({}));

    const mockEvent = {
      pathParameters: { resourceId: 'dialect_test_123' },
      headers: { 'content-type': 'audio/mpeg' },
      body: Buffer.from('test').toString('base64'),
      isBase64Encoded: true
    };

    await audioHandler.uploadAudioResource(mockEvent, {});

    // 应该清除多个相关缓存
    expect(mockRedis.del).toHaveBeenCalledWith('audio:resource:dialect_test_123');
    expect(mockRedis.del).toHaveBeenCalledWith('audio:list:dialect:*');
    expect(mockRedis.del).toHaveBeenCalledWith('audio:list:all:*');
    expect(mockRedis.del).toHaveBeenCalledWith('audio:stats:global');
  });
});