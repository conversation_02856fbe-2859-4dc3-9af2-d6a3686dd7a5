openapi: 3.0.0
info:
  title: 家乡话猜猜猜游戏API
  version: 1.0.0
  description: 家乡话猜猜猜游戏后端API文档
  contact:
    name: API Support
    email: <EMAIL>
servers:
  - url: https://api.dialectgame.com/v1
    description: 生产环境
  - url: https://test-api.dialectgame.com/v1
    description: 测试环境
  - url: http://localhost:3001/v1
    description: 开发环境
paths:
  /v1/auth/wechat/login:
    post:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/auth/refresh:
    post:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/auth/logout:
    post:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/auth/logout-all:
    post:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/auth/me:
    get:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/auth/verify:
    get:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/auth/stats:
    get:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/auth/phone:
    post:
      tags:
        - auth
      summary: 认证服务处理器
      description: 处理用户登录、注册、Token刷新等认证相关的API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/users/{userId}:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/{userId}/profile:
    put:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/users/{userId}/stats:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/{userId}/game-history:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/{userId}/ranking:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/{userId}/friends:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
    post:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/users/{userId}/friends/{friendId}:
    delete:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/search:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/users/{userId}/achievements:
    get:
      tags:
        - user
      summary: 用户服务处理器
      description: 处理用户信息管理、游戏统计等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/questions:
    get:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/questions/{questionId}:
    get:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/game-sessions:
    post:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/game-sessions/{sessionId}:
    get:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
    delete:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/game-sessions/{sessionId}/submit:
    post:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
  /v1/game-sessions/{sessionId}/results:
    get:
      tags:
        - game
      summary: 游戏服务处理器
      description: 处理题目管理、游戏会话、游戏结果等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/audio:
    get:
      tags:
        - audio
      summary: 音频资源服务处理器
      description: 处理音频资源的获取、管理等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/audio/stats:
    get:
      tags:
        - audio
      summary: 音频资源服务处理器
      description: 处理音频资源的获取、管理等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
  /v1/audio/{resourceId}:
    get:
      tags:
        - audio
      summary: 音频资源服务处理器
      description: 处理音频资源的获取、管理等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
    post:
      tags:
        - audio
      summary: 音频资源服务处理器
      description: 处理音频资源的获取、管理等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
    delete:
      tags:
        - audio
      summary: 音频资源服务处理器
      description: 处理音频资源的获取、管理等API请求
      parameters: []
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: success
                  data:
                    type: object
                  timestamp:
                    type: string
                    format: date-time
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 资源不存在
        '500':
          description: 服务器内部错误
      security:
        - bearerAuth: []
components:
  schemas:
    GameResult:
      type: object
      properties:
        id:
          type: integer
          description: id字段
          example: null
        userId:
          type: integer
          description: userId字段
          example: data.userId || null
        gameSessionId:
          type: integer
          description: gameSessionId字段
          example: data.gameSessionId || null
        questionId:
          type: integer
          description: questionId字段
          example: data.questionId || null
        userAnswer:
          type: integer
          description: userAnswer字段
          example: data.userAnswer || null
        isCorrect:
          type: integer
          description: isCorrect字段
          example: data.isCorrect || 0
        answerTime:
          type: integer
          description: answerTime字段
          example: data.answerTime || 0
        scoreEarned:
          type: integer
          description: scoreEarned字段
          example: data.scoreEarned || 0
        streakCount:
          type: integer
          description: streakCount字段
          example: data.streakCount || 0
        hintUsed:
          type: integer
          description: hintUsed字段
          example: data.hintUsed || 0
        createdAt:
          type: integer
          description: createdAt字段
          example: data.createdAt || null
      description: GameResult数据模型
    GameSession:
      type: object
      properties:
        id:
          type: integer
          description: id字段
          example: null
        sessionId:
          type: integer
          description: sessionId字段
          example: data.sessionId || null
        userId:
          type: integer
          description: userId字段
          example: data.userId || null
        category:
          type: string
          description: category字段
          example: ''
        difficulty:
          type: string
          description: difficulty字段
          example: 1
        questionCount:
          type: integer
          description: questionCount字段
          example: data.questionCount || 10
        currentQuestion:
          type: integer
          description: currentQuestion字段
          example: data.currentQuestion || 0
        correctCount:
          type: integer
          description: correctCount字段
          example: data.correctCount || 0
        totalScore:
          type: integer
          description: totalScore字段
          example: data.totalScore || 0
        totalTime:
          type: integer
          description: totalTime字段
          example: data.totalTime || 0
        gameMode:
          type: string
          description: gameMode字段
          example: data.gameMode || standard
        status:
          type: string
          description: status字段
          example: 1
        startedAt:
          type: integer
          description: startedAt字段
          example: data.startedAt || null
        finishedAt:
          type: integer
          description: finishedAt字段
          example: data.finishedAt || null
        expiresAt:
          type: integer
          description: expiresAt字段
          example: data.expiresAt || null
        createdAt:
          type: integer
          description: createdAt字段
          example: data.createdAt || null
        updatedAt:
          type: integer
          description: updatedAt字段
          example: data.updatedAt || null
        questions:
          type: array
          description: questions字段
          example: []
        currentQuestionIndex:
          type: integer
          description: currentQuestionIndex字段
          example: 0
        streakCount:
          type: integer
          description: streakCount字段
          example: 0
      description: GameSession数据模型
    Question:
      type: object
      properties:
        id:
          type: integer
          description: id字段
          example: null
        category:
          type: string
          description: category字段
          example: ''
        region:
          type: string
          description: region字段
          example: ''
        questionText:
          type: string
          description: questionText字段
          example: 'data.questionText || '
        questionType:
          type: string
          description: questionType字段
          example: data.questionType || 1
        audioUrl:
          type: integer
          description: audioUrl字段
          example: data.audioUrl || null
        audioDuration:
          type: integer
          description: audioDuration字段
          example: data.audioDuration || 0
        difficultyLevel:
          type: string
          description: difficultyLevel字段
          example: data.difficultyLevel || 1
        standardAnswer:
          type: string
          description: standardAnswer字段
          example: 'data.standardAnswer || '
        answerOptions:
          type: integer
          description: answerOptions字段
          example: data.answerOptions || null
        explanation:
          type: integer
          description: explanation字段
          example: null
        usageCount:
          type: integer
          description: usageCount字段
          example: data.usageCount || 0
        correctRate:
          type: integer
          description: correctRate字段
          example: data.correctRate || 0.0000
        avgAnswerTime:
          type: integer
          description: avgAnswerTime字段
          example: data.avgAnswerTime || 0.0000
        status:
          type: string
          description: status字段
          example: 1
        createdBy:
          type: integer
          description: createdBy字段
          example: data.createdBy || null
        createdAt:
          type: integer
          description: createdAt字段
          example: data.createdAt || null
        updatedAt:
          type: integer
          description: updatedAt字段
          example: data.updatedAt || null
      description: Question数据模型
    User:
      type: object
      properties:
        id:
          type: string
          description: id字段
        openid:
          type: string
          description: openid字段
        unionid:
          type: string
          description: unionid字段
        nickname:
          type: string
          description: nickname字段
        avatar_url:
          type: string
          description: avatar_url字段
        gender:
          type: string
          description: gender字段
        province:
          type: string
          description: province字段
        city:
          type: string
          description: city字段
        country:
          type: string
          description: country字段
        language:
          type: string
          description: language字段
        total_score:
          type: integer
          description: total_score字段
          example: 0
        total_games:
          type: integer
          description: total_games字段
          example: 0
        win_games:
          type: integer
          description: win_games字段
          example: 0
        max_streak:
          type: integer
          description: max_streak字段
          example: 0
        current_level:
          type: string
          description: current_level字段
          example: 1
        status:
          type: string
          description: status字段
          example: 1
        last_login_at:
          type: string
          description: last_login_at字段
        created_at:
          type: string
          description: created_at字段
        updated_at:
          type: string
          description: updated_at字段
      description: User数据模型
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
