# 智能体协作工作流

## 日常开发工作流
* `请所有agents报告昨天的工作进展和今天的计划`
* `刚提交了音频播放模块的代码，请相关agents进行review和测试`
* `请所有agents报告昨天完成情况和今天计划，识别阻碍问题`
* `请所有agents回顾本周工作，总结经验和改进点`
* `请architect-agent设计音频缓存策略，要求离线可用，内存占用<10MB`
* `先让product-manager-agent分析用户需求，然后让ui-designer-agent设计界面，最后让frontend-developer-agent实现`

## 问题解决工作流
* `用户反馈游戏启动慢，请相关agents分析和解决`
* `启动性能优化专项，目标是将游戏启动时间降低到2秒以内，请相关agents协作完成`
* `我们收到了用户反馈的10个bug，请qa-tester-agent整理优先级，然后分配给相应的开发agents修复`

## 新功能开发工作流
* `我们要增加"方言PK挑战"功能，请团队协作完成`
* `开始新的开发冲刺，本周目标是完成核心答题功能，请product-manager-agent制定计划，然后各个开发agents按照计划执行`

## 智能任务分发
* `游戏的音频播放有卡顿问题，需要优化`

## 并行任务执行
* `同时进行：1)优化游戏性能 2)设计新的分享界面 3)准备广东话内容`

## 定期自动化任务
* `每周五下午让data-analyst-agent生成项目数据报告，并让所有相关agents基于数据制定下周计划`
* `请所有agents评估当前协作效率，提出改进建议`

## 当有代码提交时
* `git commit → architect-agent review → qa-tester-agent test`

## 当有新功能需求时  
* `product-manager-agent → ui-designer-agent → frontend-developer-agent`

## 当有性能问题时
* `data-analyst-agent → architect-agent → backend-developer-agent`

## 当需要内容更新时
* `marketing-agent → audio-content-agent → qa-tester-agent`

# 跨角色协作场景

## 场景1：新功能开发

1. product-manager-agent 分析需求和优先级
2. ui-designer-agent 设计界面和交互
3. architect-agent 评估技术方案
4. frontend-developer-agent + backend-developer-agent 并行开发
5. qa-tester-agent 测试验证
6. data-analyst-agent 监控上线效果

## 场景2：性能优化

1. data-analyst-agent 发现性能问题
2. architect-agent 分析瓶颈原因
3. frontend-developer-agent 优化客户端性能
4. backend-developer-agent 优化服务器响应
5. qa-tester-agent 验证优化效果

## 场景3：内容运营

1. marketing-agent 制定内容策略
2. audio-content-agent 制作方言内容
3. backend-developer-agent 部署内容管理
4. qa-tester-agent 验证内容质量
5. data-analyst-agent 监控内容效果

# 🚀 4周冲刺任务分配

## 第1周：核心机制搭建

张技术 + 李前端：答题核心逻辑开发
王后端：基础API和数据库设计
赵设计：主界面UI设计
刘音频：首批100题音频制作

## 第2周：音频系统完善

李前端：音频播放和缓存优化
刘音频：10个方言区内容完成
陈产品：用户测试和反馈收集
周测试：功能测试开始

## 第3周：社交功能开发

王后端：排行榜和分享系统
李前端：分享功能集成
孙运营：KOL对接和内容准备
吴数据：数据埋点和监控

## 第4周：上线准备

全员：最终测试和优化
陈产品：提审和合规检查
孙运营：推广预热活动
张技术：性能最终优化

