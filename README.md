# 家乡话猜猜猜 🏮

> 听懂家乡话，连接你我他

一款基于方言文化的社交学习类微信小游戏，通过有趣的音频问答形式让用户学习和分享中国各地方言，构建以情感共鸣和文化认同为核心的社交娱乐产品。

## 🎯 项目概述

### 产品定位
传承和弘扬中华方言文化，让每个人都能听懂家乡话，让方言文化在数字时代焕发新的生命力。

### 核心功能
- 🎵 **方言音频问答**: 12大方言区域，10000+精选题目
- 🏆 **成就系统**: 方言达人认证，全国通挑战
- 👥 **社交分享**: 基于地域认同的社交网络
- 📚 **学习进度**: 个性化学习路径，智能难度调节
- 🎨 **文化体验**: 34省份差异化视觉设计

### 技术特色
- **超低成本**: Serverless架构，月成本$276支持10万DAU
- **高性能**: API响应<500ms，99.9%可用性
- **文化设计**: 融入传统文化元素的现代界面
- **病毒传播**: K-Factor 1.5+的社交分享机制

## 📁 项目结构

```
hometown-dialect-game/
├── docs/                    # 📋 产品和设计文档中心
│   ├── product/             # 产品管理 (PRD、用户研究、指标)
│   ├── design/              # UI设计 (线框图、视觉稿、设计系统)
│   ├── architecture/        # 系统架构 (技术规范、性能、安全)
│   ├── marketing/           # 营销策略 (获客、留存、变现)
│   ├── testing/             # 质量保证 (测试计划、用例、报告)
│   └── analytics/           # 数据分析 (报告、仪表板、模型)
│
├── frontend/                # 📱 前端开发 (Cocos Creator)
│   ├── cocos-project/       # Cocos Creator项目文件
│   ├── wechat-config/       # 微信小游戏配置
│   └── docs/                # 前端开发文档
│
├── backend/                 # ⚙️ 后端开发 (Serverless)
│   ├── serverless/          # 云函数服务
│   ├── database/            # 数据库设计
│   ├── api/                 # API文档规范
│   └── config/              # 配置管理
│
├── content/                 # 🎵 音频内容管理
│   ├── audio/               # 音频文件资源
│   ├── database/            # 题库数据管理
│   ├── scripts/             # 录制指南规范
│   └── production/          # 制作流程管理
│
└── shared/                  # 🤝 共享资源
    ├── constants/           # 常量配置
    ├── types/               # 类型定义
    └── utils/               # 共享工具
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Cocos Creator 3.8.x
- 微信开发者工具
- 腾讯云账户

### 本地开发
```bash
# 克隆项目
git clone https://github.com/your-org/hometown-dialect-game.git
cd hometown-dialect-game

# 前端开发
cd frontend/cocos-project
# 使用 Cocos Creator 打开项目

# 后端开发
cd backend/serverless
npm install
npm run dev

# 音频内容
cd content
# 查看 scripts/recording-guidelines.md
```

## 📊 项目目标

### 短期目标 (3个月)
- 500K+ 注册用户
- 100K+ DAU
- 7天留存率 >40%

### 中期目标 (6个月)
- 2M+ 注册用户  
- 500K+ DAU
- ARPU >2 RMB/月

### 长期目标 (12个月)
- 10M+ 注册用户
- 2M+ DAU
- 方言学习类目Top1

## 🏗️ 技术架构

### 核心技术栈
- **前端**: Cocos Creator 3.8.x + TypeScript
- **后端**: Serverless Functions (SCF) + Node.js 18.x
- **数据库**: TencentDB Serverless MySQL 8.0
- **缓存**: Redis 内存版
- **存储**: 腾讯云 COS + CDN
- **网关**: 腾讯云 API 网关

### 成本控制
- **月成本**: $276 (10万DAU)
- **每用户成本**: $0.00276/月
- **目标净利润率**: 25-35%

## 🎨 设计系统

### 品牌色彩
- **中国红**: #C8102E (传统文化)
- **暖橙色**: #F4A259 (温暖家乡)
- **34省份专属主题色彩**

### 设计原则  
- 文化传承 + 现代简约
- 直觉交互 (3秒理解)
- 温暖亲和的用户体验
- 地域差异化视觉识别

## 🤝 贡献指南

### 团队协作
每个专业agent负责对应的工作区域：
- **product-manager-agent**: 产品需求和策略
- **architect-agent**: 技术架构和规范  
- **ui-designer-agent**: 界面设计和体验
- **audio-content-agent**: 方言内容制作
- **frontend-developer-agent**: 前端开发实现
- **backend-developer-agent**: 后端服务开发

### 开发流程
1. 需求评审 → 技术设计 → UI设计
2. 前后端并行开发 → 内容制作
3. 联调测试 → 上线发布

## 📈 数据指标

### 核心KPI
- **用户增长**: DAU、MAU、留存率
- **用户参与**: 会话时长、使用频次
- **商业化**: 付费转化率、ARPU、LTV
- **内容质量**: 用户评分、完成率

### 技术指标
- **性能**: API响应时间<500ms
- **可用性**: 99.9%系统可用性
- **成本**: 每用户每月$0.003以内

## 📄 文档链接

- [产品需求文档 (PRD)](./docs/product/requirements/PRD-v1.0.md)
- [技术架构设计](./docs/architecture/serverless-architecture.md)  
- [UI设计规范](./docs/design/design-system/style-guide.md)
- [品牌指南](./docs/design/design-system/brand-guidelines.md)
- [项目结构说明](./structure.md)

## 📜 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

---

**让方言文化在数字时代传承与发展** 🌟