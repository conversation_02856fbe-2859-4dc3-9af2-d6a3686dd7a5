hometown-dialect-game/
├── README.md                           # 项目总体介绍
├── structure.md                        # 项目结构说明
├── oh-my-game.md                       # 项目需求文档
├── daily-report.md                     # 项目每日报告, 只记录当日的报告, 每次更新前需要清空之前的报告
├── daily-issue.md                      # 项目问题记录, 只记录当日的问题, 每次更新前需要清空之前的记录
├── daily-plan.md                       # 项目明日计划, 只记录明日的计划, 每次更新前需要清空之前的计划
├── DEPLOYMENT.md                       # 项目部署文档
│
├── audio-content                       # 项目收集的音频内容
│
├── docs/                              # 📋 产品和设计文档中心
│   ├── product/                       # product-manager-agent 工作区
│   │   ├── requirements/              # 需求文档
│   │   │   ├── PRD-v1.0.md           # 产品需求文档
│   │   │   ├── user-stories.md       # 用户故事
│   │   │   ├── feature-specs/        # 功能规格说明
│   │   │   └── roadmap.md            # 产品路线图
│   │   ├── research/                  # 用户研究
│   │   │   ├── user-interviews.md    # 用户访谈
│   │   │   ├── market-analysis.md    # 市场分析
│   │   │   └── competitor-analysis.md # 竞品分析
│   │   └── metrics/                   # 产品指标
│   │       ├── kpi-definition.md     # KPI定义
│   │       └── success-criteria.md   # 成功标准
│   │
│   ├── design/                        # 🎨 ui-designer-agent 工作区
│   │   ├── wireframes/               # 线框图
│   │   │   ├── main-flow.figma       # 主流程线框图
│   │   │   ├── game-flow.figma       # 游戏流程线框图
│   │   │   └── share-flow.figma      # 分享流程线框图
│   │   ├── mockups/                  # 视觉稿
│   │   │   ├── main-screens/         # 主要界面
│   │   │   ├── game-screens/         # 游戏界面
│   │   │   └── dialects-themes/      # 各方言区主题
│   │   │       ├── sichuan-theme.psd
│   │   │       ├── dongbei-theme.psd
│   │   │       └── guangdong-theme.psd
│   │   ├── assets/                   # 设计资源
│   │   │   ├── icons/               # 图标
│   │   │   ├── illustrations/       # 插画
│   │   │   ├── fonts/              # 字体
│   │   │   └── colors-palette.md   # 色彩规范
│   │   ├── prototypes/              # 交互原型
│   │   │   ├── main-prototype.figma
│   │   │   └── game-prototype.figma
│   │   ├── design-system/           # 设计系统
│   │   │   ├── components.figma     # 组件库
│   │   │   ├── style-guide.md       # 样式指南
│   │   │   └── brand-guidelines.md  # 品牌指南
│   │   └── export/                  # 切图输出
│   │       ├── png/                # PNG格式
│   │       ├── svg/                # SVG格式
│   │       └── webp/               # WebP格式
│   │
│   ├── architecture/                  # 🏗️ architect-agent 工作区
│   │   ├── system-design/            # 系统设计
│   │   │   ├── architecture-overview.md
│   │   │   ├── component-diagram.md
│   │   │   ├── data-flow-diagram.md
│   │   │   └── deployment-diagram.md
│   │   ├── technical-specs/          # 技术规范
│   │   │   ├── frontend-architecture.md
│   │   │   ├── backend-architecture.md
│   │   │   ├── database-design.md
│   │   │   └── api-specifications.md
│   │   ├── performance/              # 性能优化
│   │   │   ├── performance-requirements.md
│   │   │   ├── optimization-strategies.md
│   │   │   └── monitoring-plan.md
│   │   └── security/                 # 安全设计
│   │       ├── security-requirements.md
│   │       ├── data-protection.md
│   │       └── threat-analysis.md
│   │
│   ├── marketing/                     # 📈 marketing-agent 工作区
│   │   ├── strategy/                 # 营销策略
│   │   │   ├── go-to-market.md       # 上市策略
│   │   │   ├── user-acquisition.md   # 获客策略
│   │   │   ├── retention-strategy.md # 留存策略
│   │   │   └── monetization-plan.md  # 变现计划
│   │   ├── campaigns/                # 营销活动
│   │   │   ├── launch-campaign/      # 发布活动
│   │   │   ├── kol-collaboration/    # KOL合作
│   │   │   └── social-media/         # 社媒营销
│   │   ├── content/                  # 营销内容
│   │   │   ├── copy-writing/         # 文案
│   │   │   ├── video-scripts/        # 视频脚本
│   │   │   └── social-posts/         # 社交媒体内容
│   │   └── assets/                   # 营销素材
│   │       ├── posters/              # 海报
│   │       ├── banners/              # 横幅
│   │       ├── videos/               # 视频素材
│   │       └── share-cards/          # 分享卡片
│   │
│   ├── testing/                       # 🔍 qa-tester-agent 工作区
│   │   ├── test-plans/               # 测试计划
│   │   │   ├── test-strategy.md      # 测试策略
│   │   │   ├── functional-tests.md   # 功能测试
│   │   │   ├── performance-tests.md  # 性能测试
│   │   │   └── compatibility-tests.md # 兼容性测试
│   │   ├── test-cases/               # 测试用例
│   │   │   ├── login-flow/           # 登录流程
│   │   │   ├── game-flow/            # 游戏流程
│   │   │   ├── share-flow/           # 分享流程
│   │   │   └── payment-flow/         # 支付流程
│   │   ├── bug-reports/              # Bug报告
│   │   │   ├── critical-bugs.md      # 严重Bug
│   │   │   ├── major-bugs.md         # 主要Bug
│   │   │   └── minor-bugs.md         # 次要Bug
│   │   ├── test-reports/             # 测试报告
│   │   │   ├── daily-reports/        # 日报
│   │   │   ├── weekly-reports/       # 周报
│   │   │   └── release-reports/      # 发布报告
│   │   └── automation/               # 自动化测试
│   │       ├── scripts/              # 测试脚本
│   │       ├── data/                # 测试数据
│   │       └── results/             # 测试结果
│   │
│   └── analytics/                     # 📊 data-analyst-agent 工作区
│       ├── reports/                  # 数据报告
│       │   ├── daily-reports/        # 日报
│       │   ├── weekly-reports/       # 周报
│       │   ├── monthly-reports/      # 月报
│       │   └── ad-hoc-analysis/      # 专项分析
│       ├── dashboards/               # 仪表板
│       │   ├── real-time-dashboard.md # 实时监控
│       │   ├── user-analytics.md     # 用户分析
│       │   ├── game-analytics.md     # 游戏分析
│       │   └── revenue-analytics.md  # 收入分析
│       ├── data-models/              # 数据模型
│       │   ├── user-behavior.sql     # 用户行为
│       │   ├── game-metrics.sql      # 游戏指标
│       │   └── business-metrics.sql  # 业务指标
│       └── monitoring/               # 监控配置
│           ├── alerts-config.yaml    # 告警配置
│           ├── metrics-config.yaml   # 指标配置
│           └── dashboard-config.json # 面板配置
│
├── frontend/                          # 📱 frontend-developer-agent 工作区
│   ├── cocos-project/                # Cocos Creator项目
│   │   ├── assets/                   # 资源文件
│   │   │   ├── scripts/              # 脚本
│   │   │   │   ├── managers/         # 管理器
│   │   │   │   │   ├── GameManager.ts
│   │   │   │   │   ├── AudioManager.ts
│   │   │   │   │   ├── UIManager.ts
│   │   │   │   │   └── NetworkManager.ts
│   │   │   │   ├── components/       # 组件
│   │   │   │   │   ├── QuestionComponent.ts
│   │   │   │   │   ├── AnswerButton.ts
│   │   │   │   │   └── ScoreDisplay.ts
│   │   │   │   ├── utils/           # 工具类
│   │   │   │   │   ├── WechatAPI.ts
│   │   │   │   │   ├── DataStorage.ts
│   │   │   │   │   └── AudioLoader.ts
│   │   │   │   └── data/            # 数据类
│   │   │   │       ├── QuestionData.ts
│   │   │   │       └── UserData.ts
│   │   │   ├── scenes/              # 场景
│   │   │   │   ├── MainScene.scene
│   │   │   │   ├── GameScene.scene
│   │   │   │   └── ResultScene.scene
│   │   │   ├── prefabs/             # 预制体
│   │   │   ├── textures/            # 贴图 (从design/export同步)
│   │   │   └── audio/               # 音频 (从content/audio同步)
│   │   ├── settings/                # 项目设置
│   │   ├── build/                   # 构建输出
│   │   └── project.json             # 项目配置
│   │
│   ├── wechat-config/               # 微信小游戏配置
│   │   ├── game.json               # 游戏配置
│   │   ├── project.config.json     # 项目配置
│   │   └── sitemap.json           # 站点地图
│   │
│   ├── docs/                       # 前端文档
│   │   ├── setup-guide.md          # 环境搭建
│   │   ├── coding-standards.md     # 编码规范
│   │   ├── component-api.md        # 组件API
│   │   └── deployment-guide.md     # 部署指南
│   │
│   └── tools/                      # 开发工具
│       ├── build-scripts/          # 构建脚本
│       ├── asset-optimization/     # 资源优化
│       └── hot-update/            # 热更新
│
├── backend/                           # ⚙️ backend-developer-agent 工作区
│   ├── serverless/                   # Serverless函数
│   │   ├── auth/                     # 认证服务
│   │   │   ├── login/
│   │   │   ├── refresh-token/
│   │   │   └── user-info/
│   │   ├── game/                     # 游戏服务
│   │   │   ├── questions/
│   │   │   ├── submit-result/
│   │   │   └── leaderboard/
│   │   ├── content/                  # 内容服务
│   │   │   ├── dialect-list/
│   │   │   ├── question-list/
│   │   │   └── content-review/
│   │   ├── social/                   # 社交服务
│   │   │   ├── share/
│   │   │   ├── friends/
│   │   │   └── ranking/
│   │   └── analytics/                # 数据分析
│   │       ├── track-event/
│   │       ├── user-behavior/
│   │       └── metrics/
│   │
│   ├── database/                     # 数据库设计
│   │   ├── schemas/                  # 数据模型
│   │   │   ├── user.sql
│   │   │   ├── question.sql
│   │   │   ├── game_result.sql
│   │   │   └── leaderboard.sql
│   │   ├── migrations/               # 数据迁移
│   │   └── seeds/                    # 初始数据
│   │
│   ├── api/                          # API文档
│   │   ├── swagger.yaml              # API规范
│   │   ├── endpoints/                # 接口文档
│   │   └── examples/                 # 请求示例
│   │
│   ├── config/                       # 配置文件
│   │   ├── development.json
│   │   ├── production.json
│   │   └── environment.template
│   │
│   ├── scripts/                      # 脚本工具
│   │   ├── deploy.sh                # 部署脚本
│   │   ├── backup.sh               # 备份脚本
│   │   └── monitoring.sh           # 监控脚本
│   │
│   └── docs/                        # 后端文档
│       ├── api-guide.md            # API使用指南
│       ├── deployment.md           # 部署文档
│       ├── troubleshooting.md      # 故障排除
│       └── security.md             # 安全文档
│
├── content/                          # 🎵 audio-content-agent 工作区
│   ├── audio/                       # 音频文件
│   │   ├── sichuan/                 # 四川话
│   │   │   ├── sc_001.mp3
│   │   │   ├── sc_002.mp3
│   │   │   └── ...
│   │   ├── dongbei/                 # 东北话
│   │   ├── guangdong/               # 广东话
│   │   ├── shanghai/                # 上海话
│   │   └── henan/                   # 河南话
│   │
│   ├── scripts/                     # 录制脚本
│   │   ├── recording-guidelines.md  # 录制指南
│   │   ├── quality-standards.md     # 质量标准
│   │   └── review-checklist.md      # 审核清单
│   │
│   ├── database/                    # 题库数据
│   │   ├── questions.json           # 题目数据
│   │   ├── dialects.json           # 方言信息
│   │   └── cultural-notes.json     # 文化背景
│   │
│   ├── production/                  # 制作流程
│   │   ├── workflow.md             # 制作流程
│   │   ├── tools-setup.md          # 工具配置
│   │   └── batch-processing/       # 批量处理
│   │
│   └── archive/                     # 归档文件
│       ├── raw-recordings/         # 原始录音
│       ├── rejected-content/       # 废弃内容
│       └── backup/                 # 备份文件
│
├── deployment/                       # 🚀 部署配置
│   ├── docker/                      # Docker配置
│   │   ├── Dockerfile.frontend
│   │   ├── Dockerfile.backend
│   │   └── docker-compose.yml
│   │
│   ├── cloud/                       # 云服务配置
│   │   ├── tencent-cloud/          # 腾讯云
│   │   ├── leancloud/              # LeanCloud
│   │   └── cdn/                    # CDN配置
│   │
│   ├── scripts/                     # 部署脚本
│   │   ├── deploy-frontend.sh
│   │   ├── deploy-backend.sh
│   │   └── rollback.sh
│   │
│   └── monitoring/                  # 监控配置
│       ├── prometheus/
│       ├── grafana/
│       └── alertmanager/
│
├── tools/                           # 🔧 开发工具
│   ├── audio-processing/           # 音频处理工具
│   │   ├── batch-converter.py     # 批量格式转换
│   │   ├── quality-checker.py     # 质量检查
│   │   └── metadata-extractor.py  # 元数据提取
│   │
│   ├── data-migration/            # 数据迁移工具
│   │   ├── import-questions.py   # 导入题目
│   │   ├── sync-audio.py         # 同步音频
│   │   └── backup-restore.py     # 备份恢复
│   │
│   ├── automation/                # 自动化工具
│   │   ├── ci-cd/                # CI/CD配置
│   │   ├── testing/              # 自动化测试
│   │   └── deployment/           # 自动化部署
│   │
│   └── analytics/                 # 分析工具
│       ├── log-parser.py         # 日志解析
│       ├── report-generator.py   # 报告生成
│       └── data-export.py        # 数据导出
│
├── shared/                         # 🤝 共享资源
│   ├── constants/                 # 常量定义
│   │   ├── game-config.json      # 游戏配置
│   │   ├── dialects-config.json  # 方言配置
│   │   └── error-codes.json      # 错误代码
│   │
│   ├── types/                     # 类型定义
│   │   ├── user.d.ts             # 用户类型
│   │   ├── game.d.ts             # 游戏类型
│   │   └── api.d.ts              # API类型
│   │
│   └── utils/                     # 共享工具
│       ├── validation.ts         # 验证工具
│       ├── formatting.ts         # 格式化工具
│       └── crypto.ts             # 加密工具
│
└── temp/                          # 🗂️ 临时文件
    ├── builds/                    # 构建临时文件
    ├── logs/                      # 日志文件
    ├── cache/                     # 缓存文件
    └── uploads/                   # 上传临时文件